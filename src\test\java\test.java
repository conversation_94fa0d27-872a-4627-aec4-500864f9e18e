import entities.PetEntity;
import module.pet.PetDao;

/**
 * Created by nara on 2017/11/21.
 */
public class test {
    public static void main(String[] args) {
//        Redis jedis = Redis.getInstance();

    /*     String uid="oML15718QVi9964EzO4725fYH";
        String roleid="2tv15617wjg2545HFp7776OBY";
        ItemDao itemDao=ItemDao.getInstance();
        LoginDao iLogin=LoginDao.getInstance();

        String key = "roledress:" + uid + "#" + 3;
        RoleDressInfo roleDressInfo = iLogin.getRoleDressFromRedis(key);
        List<UtilityInfo> dressList = roleDressInfo.getDressList();
        for (UtilityInfo info:dressList) {
            /// System.out.println(info);
        }
        Map<String, String> map = jedis.hgetAll(key);
        /// System.out.println(map.get("dresslist"));*/

        //Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_FULLINGBALL, 1);

        //long nowStamp = System.currentTimeMillis();//获取系统时间


       /* Map<String,String> map = jedis.hgetAll("role:"+uid);//获取role、
        jedis.hmset("role:"+uid,map);
        /// System.out.println(map);*/

       /* LoginInfo loginInfo=new LoginInfo();
        loginInfo.setLoginType(1);
        loginInfo.setServerId(0);
        loginInfo.setParam("start_1234");
        loginInfo.setPwd("1234");
        loginInfo.setUserId("268");
        LoginJudgeAccount account = iLogin.getLoginRoleInfo(loginInfo.getServerId(), loginInfo.getType(), loginInfo.getParam(),loginInfo.getPwd());

        RoleInfo roleInfo = account.getRoleInfo();
        UserData.Role role = iLogin.roleInfoToUserData(roleInfo);
        /// System.out.println(account);
        /// System.out.println(loginInfo);
        /// System.out.println(role);
*/
       /*/// System.out.println(loginDao.getStationId(uid));
       Map<String, String> map = jedis.hgetAll("rolefriendinstation:" + uid);
       /// System.out.println(map);*/

      /*  StationInfo stion=new StationInfo();
        stion.setUid("zGO15634zGp47953CO4259AXu");
        SuperServerHandler.stationMap.get(1).add(stion);*/

    /*  Map<String,String> bagmap = jedis.hgetAll("roleitem:"+uid+"#0");//背包dress
       //jedis.hget("roleitem:"+uid+"#1",1+"");
       /// System.out.println(bagmap);*/


       /* String key = "roledress:" + uid + "#" + 1;
        RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
        List<UtilityInfo> dressList = roleDressInfo.getDressList();
        /// System.out.println(roleDressInfo);

        for(UtilityInfo clth:dressList){
            /// System.out.println(clth.getId()+"==========="+clth.getOverStamp());
        }*/

       /* Map<String,String> map2=jedis.hgetAll("lotto:"+20+"&"+uid);//获取redis中的六个道具
        /// System.out.println(map2);*/


        /*map.remove("11");
        jedis.hmset("lotto:22&BUA15617mUw25478h22164YyK",map);user:
        /// System.out.println(map);

        /// System.out.println(map2);
        /// System.out.println(map2.size());*/
        /*int id=10;
        StringBuffer sql=new StringBuffer(" FROM DressEntity where uid = '").append(uid).append("' and dressid= ").append(id).append(" ");
        DressEntity dress=(DressEntity)MySql.queryForOne(sql.toString());
        /// System.out.println(dress);*/


        /*/// System.out.println(SuperServerHandler.stationMap);
        /// System.out.println(jedis.hgetAll("stationMap:1"));*/
        /*Map<String, String> stringMap2 = jedis.hgetAll("role:BUA15617mUw25478h22164YyK");
        /// System.out.println(stringMap2);*/
         /*try {
          RoleEntity role=(RoleEntity)MySql.queryForOne(" FROM RoleEntity where uid='zGO15634zGp47953CO4259AXu' ");
            Map<String, String> roleMap = MyUtils.convertBean(role);
            roleMap.put("station","1");
            roleMap.put("status","1");
            jedis.hmset("role:zGO15634zGp47953CO4259AXu",roleMap);
            Map<String, String> stringMap = jedis.hgetAll("role:zGO15634zGp47953CO4259AXu");
            /// System.out.println(stringMap);


            /*List<StationInfo> list = SuperServerHandler.stationMap.get(1);

            /// System.out.println(jedis.hgetAll("stationMap:1"));
        } catch (Exception e) {

        }*/

        /*Factory.up();*/
        /*/// System.out.println(System.currentTimeMillis());

        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MISSION, 60);

        Map<String, String> jummissonMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_JUMPMISSION, 1);
        int exp1 = Integer.parseInt(missionMap.get("exp"));


       /* StringBuffer hql2=new StringBuffer("from UserEntity order by id desc");
        Object object=MySql.queryForOne(hql2.toString(),1);
        if(object!=null){
            /// System.out.println(object);
            UserEntity user=(UserEntity)object;
            /// System.out.println(user.getUserid());
        }*/


        /// String sql = " from RoleEntity as t1, PartEntity as t2 where t1.uid = t2.uid and t2.cupboard = 0 and t1.missionid > 0 AND t1.roleid = t2.roleid ORDER BY t1.headballmissionid DESC";
      /*  List<Object> list = MySql.queryForList(hql.toString());
        /// System.out.println(list);*/
       /*  StringBuffer hql2=new StringBuffer("select id from UserEntity where id=1");
        StringBuffer hql2=new StringBuffer("from UserEntity where id=1");
        Object object=MySql.queryForOne(hql2.toString());
        /// System.out.println(list);
       if(object!=null){
            /// System.out.println(object);
        }*/

//        StringBuffer hql3=new StringBuffer("update RoleEntity set name='断剑重铸之日' where userid='2tv15617wjg2545HFp7776OBY'");
//        MySql.mustUpdateSomes(hql3.toString());



    }
}