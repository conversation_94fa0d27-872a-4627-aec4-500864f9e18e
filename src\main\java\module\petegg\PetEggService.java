package module.petegg;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.PetEggEntity;
import entities.PetEntity;
import manager.MySql;
import module.pet.PetDao;
import protocol.PetEggData;

public class PetEggService {
    private static PetEggService inst = null;

    public static PetEggService getInstance() {
        if (inst == null) {
            inst = new PetEggService();
        }
        return inst;
    }


    public byte[] RequestPetEggExp(byte[] inBytes, String uid) {
        PetEggData.ResponsePetEggExp.Builder builder = PetEggData.ResponsePetEggExp.newBuilder();
        PetEggData.RequestPetEggExp data = null;
        try {
            data = PetEggData.RequestPetEggExp.parseFrom(inBytes);
            PetEggEntity entity = PetEggDao.getInstance().Get(uid, data.getPetUid());

            builder.setPetUid(entity.getPetUid());
            builder.setPetExp(entity.getExp());
            builder.setPetLv(entity.getLv());
        } catch (InvalidProtocolBufferException e) {

        }
        return builder.build().toByteArray();
    }

    public byte[] RequestAddPetEggExp(byte[] inBytes, String uid) {
        PetEggData.ResponsePetEggExp.Builder builder = PetEggData.ResponsePetEggExp.newBuilder();
        PetEggData.RequestAddPetEggExp data = null;
        try {
            data = PetEggData.RequestAddPetEggExp.parseFrom(inBytes);
            PetEggEntity entity = PetEggDao.getInstance().Update(uid, data);

            builder.setPetUid(entity.getPetUid());
            builder.setPetExp(entity.getExp());
            builder.setPetLv(entity.getLv());
        } catch (InvalidProtocolBufferException e) {

        }
        return builder.build().toByteArray();
    }

    public void PetEggFastHatch(byte[] inBytes, String uid) {
        try {
            PetEggData.ResponsePetEggFastHatch data = PetEggData.ResponsePetEggFastHatch.parseFrom(inBytes);//反序列化;
            PetEntity entity = PetDao.getInstance().queryPet(uid, data.getPetUid());
            entity.setMode(2);
            entity.setGrowing(true);
            MySql.update(entity);
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }
}
