package module.Job;

import entities.ItemEntity;
import entities.RoleEntity;
import manager.MySql;
import module.item.ItemDao;
import module.role.RoleDao;
import protocol.ItemData;
import protocol.RoleData;

import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

public class XiuGaiTime {
    public static void main(String[] args) {
//        TimerTask timerTask = new TimerTask() {
//
//            @Override
//            public void run() {
//
//                RoleDao roleDao = new RoleDao();
//                List<Object> list = roleDao.queryUid();
//                String uid = null;
//                for (int i = 0; i < list.size(); i++) {
//                    Object o = list.get(i);
//                    uid = (String) o;
//                    RoleEntity roleEntity = roleDao.queryRole(uid);
//                    RoleData.PlainRole role = RoleEntity.entityToPb(roleEntity);
//                    int LV = role.getRoleCurrentLV();
//                    int stamina = 0;
//                    for (int k = 0; k <= 30; k++) {
//                        stamina = LV + 30;
//                    }
//                    ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, 3);
//                    if (itemEntity == null){
//                        itemEntity = new ItemEntity();
//                        itemEntity.setItemid(3);
//                        itemEntity.setItemnum(stamina);
//                        MySql.insert(itemEntity);
//                    }else {
//                        double itemnum = itemEntity.getItemnum();
//                        itemnum += 30;
//                        if (itemnum > (double) stamina || itemnum < 0) {
//                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//                            itemEntity.setItemnum((double) stamina);
//                            MySql.update(itemEntity);
//                            builder.setId(itemEntity.getItemid());
//                            builder.setNum(itemEntity.getItemnum());
//                            reportBuilder.addItem(builder);
//                        } else {
//                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//                            itemEntity.setItemnum(itemnum);
//                            MySql.update(itemEntity);
//                            builder.setId(itemEntity.getItemid());
//                            builder.setNum(itemEntity.getItemnum());
//                            reportBuilder.addItem(builder);
//                        }
//                    }
//                }
//            }
//        };
//        Timer timer = new Timer();
//        timer.schedule(timerTask, 1000, 3000*10*10);
//    }
}}
