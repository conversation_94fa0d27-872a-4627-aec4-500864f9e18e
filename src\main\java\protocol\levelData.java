// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: level.proto

package protocol;

public final class levelData {
  private levelData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestLevelNumOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 page_number = 1;
    /**
     * <code>required int32 page_number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    boolean hasPageNumber();
    /**
     * <code>required int32 page_number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    int getPageNumber();

    // required int32 leveLnum = 2;
    /**
     * <code>required int32 leveLnum = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    boolean hasLeveLnum();
    /**
     * <code>required int32 leveLnum = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    int getLeveLnum();
  }
  /**
   * Protobuf type {@code protocol.RequestLevelNum}
   *
   * <pre>
   *1308
   * </pre>
   */
  public static final class RequestLevelNum extends
      com.google.protobuf.GeneratedMessage
      implements RequestLevelNumOrBuilder {
    // Use RequestLevelNum.newBuilder() to construct.
    private RequestLevelNum(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestLevelNum(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestLevelNum defaultInstance;
    public static RequestLevelNum getDefaultInstance() {
      return defaultInstance;
    }

    public RequestLevelNum getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestLevelNum(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              pageNumber_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              leveLnum_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.levelData.internal_static_protocol_RequestLevelNum_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.levelData.internal_static_protocol_RequestLevelNum_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.levelData.RequestLevelNum.class, protocol.levelData.RequestLevelNum.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestLevelNum> PARSER =
        new com.google.protobuf.AbstractParser<RequestLevelNum>() {
      public RequestLevelNum parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestLevelNum(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestLevelNum> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 page_number = 1;
    public static final int PAGE_NUMBER_FIELD_NUMBER = 1;
    private int pageNumber_;
    /**
     * <code>required int32 page_number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    public boolean hasPageNumber() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 page_number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    public int getPageNumber() {
      return pageNumber_;
    }

    // required int32 leveLnum = 2;
    public static final int LEVELNUM_FIELD_NUMBER = 2;
    private int leveLnum_;
    /**
     * <code>required int32 leveLnum = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    public boolean hasLeveLnum() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 leveLnum = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    public int getLeveLnum() {
      return leveLnum_;
    }

    private void initFields() {
      pageNumber_ = 0;
      leveLnum_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPageNumber()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLeveLnum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, pageNumber_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, leveLnum_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, pageNumber_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, leveLnum_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.levelData.RequestLevelNum parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelNum parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.levelData.RequestLevelNum parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.RequestLevelNum parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.levelData.RequestLevelNum prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestLevelNum}
     *
     * <pre>
     *1308
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.levelData.RequestLevelNumOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.levelData.internal_static_protocol_RequestLevelNum_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.levelData.internal_static_protocol_RequestLevelNum_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.levelData.RequestLevelNum.class, protocol.levelData.RequestLevelNum.Builder.class);
      }

      // Construct using protocol.levelData.RequestLevelNum.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        pageNumber_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        leveLnum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.levelData.internal_static_protocol_RequestLevelNum_descriptor;
      }

      public protocol.levelData.RequestLevelNum getDefaultInstanceForType() {
        return protocol.levelData.RequestLevelNum.getDefaultInstance();
      }

      public protocol.levelData.RequestLevelNum build() {
        protocol.levelData.RequestLevelNum result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.levelData.RequestLevelNum buildPartial() {
        protocol.levelData.RequestLevelNum result = new protocol.levelData.RequestLevelNum(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.pageNumber_ = pageNumber_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.leveLnum_ = leveLnum_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.levelData.RequestLevelNum) {
          return mergeFrom((protocol.levelData.RequestLevelNum)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.levelData.RequestLevelNum other) {
        if (other == protocol.levelData.RequestLevelNum.getDefaultInstance()) return this;
        if (other.hasPageNumber()) {
          setPageNumber(other.getPageNumber());
        }
        if (other.hasLeveLnum()) {
          setLeveLnum(other.getLeveLnum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPageNumber()) {
          
          return false;
        }
        if (!hasLeveLnum()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.levelData.RequestLevelNum parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.levelData.RequestLevelNum) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 page_number = 1;
      private int pageNumber_ ;
      /**
       * <code>required int32 page_number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public boolean hasPageNumber() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 page_number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public int getPageNumber() {
        return pageNumber_;
      }
      /**
       * <code>required int32 page_number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public Builder setPageNumber(int value) {
        bitField0_ |= 0x00000001;
        pageNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 page_number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public Builder clearPageNumber() {
        bitField0_ = (bitField0_ & ~0x00000001);
        pageNumber_ = 0;
        onChanged();
        return this;
      }

      // required int32 leveLnum = 2;
      private int leveLnum_ ;
      /**
       * <code>required int32 leveLnum = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public boolean hasLeveLnum() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 leveLnum = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public int getLeveLnum() {
        return leveLnum_;
      }
      /**
       * <code>required int32 leveLnum = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public Builder setLeveLnum(int value) {
        bitField0_ |= 0x00000002;
        leveLnum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 leveLnum = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public Builder clearLeveLnum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        leveLnum_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestLevelNum)
    }

    static {
      defaultInstance = new RequestLevelNum(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestLevelNum)
  }

  public interface ResponseLevelPageOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.ResponseLevelPage}
   *
   * <pre>
   *2308
   * </pre>
   */
  public static final class ResponseLevelPage extends
      com.google.protobuf.GeneratedMessage
      implements ResponseLevelPageOrBuilder {
    // Use ResponseLevelPage.newBuilder() to construct.
    private ResponseLevelPage(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseLevelPage(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseLevelPage defaultInstance;
    public static ResponseLevelPage getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseLevelPage getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseLevelPage(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.levelData.internal_static_protocol_ResponseLevelPage_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.levelData.internal_static_protocol_ResponseLevelPage_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.levelData.ResponseLevelPage.class, protocol.levelData.ResponseLevelPage.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseLevelPage> PARSER =
        new com.google.protobuf.AbstractParser<ResponseLevelPage>() {
      public ResponseLevelPage parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseLevelPage(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseLevelPage> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.levelData.ResponseLevelPage parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelPage parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.levelData.ResponseLevelPage parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.ResponseLevelPage parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.levelData.ResponseLevelPage prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseLevelPage}
     *
     * <pre>
     *2308
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.levelData.ResponseLevelPageOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.levelData.internal_static_protocol_ResponseLevelPage_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.levelData.internal_static_protocol_ResponseLevelPage_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.levelData.ResponseLevelPage.class, protocol.levelData.ResponseLevelPage.Builder.class);
      }

      // Construct using protocol.levelData.ResponseLevelPage.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.levelData.internal_static_protocol_ResponseLevelPage_descriptor;
      }

      public protocol.levelData.ResponseLevelPage getDefaultInstanceForType() {
        return protocol.levelData.ResponseLevelPage.getDefaultInstance();
      }

      public protocol.levelData.ResponseLevelPage build() {
        protocol.levelData.ResponseLevelPage result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.levelData.ResponseLevelPage buildPartial() {
        protocol.levelData.ResponseLevelPage result = new protocol.levelData.ResponseLevelPage(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.levelData.ResponseLevelPage) {
          return mergeFrom((protocol.levelData.ResponseLevelPage)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.levelData.ResponseLevelPage other) {
        if (other == protocol.levelData.ResponseLevelPage.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.levelData.ResponseLevelPage parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.levelData.ResponseLevelPage) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseLevelPage)
    }

    static {
      defaultInstance = new ResponseLevelPage(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseLevelPage)
  }

  public interface RequestLevelDOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestLevelD}
   *
   * <pre>
   *1309
   * </pre>
   */
  public static final class RequestLevelD extends
      com.google.protobuf.GeneratedMessage
      implements RequestLevelDOrBuilder {
    // Use RequestLevelD.newBuilder() to construct.
    private RequestLevelD(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestLevelD(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestLevelD defaultInstance;
    public static RequestLevelD getDefaultInstance() {
      return defaultInstance;
    }

    public RequestLevelD getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestLevelD(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.levelData.internal_static_protocol_RequestLevelD_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.levelData.internal_static_protocol_RequestLevelD_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.levelData.RequestLevelD.class, protocol.levelData.RequestLevelD.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestLevelD> PARSER =
        new com.google.protobuf.AbstractParser<RequestLevelD>() {
      public RequestLevelD parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestLevelD(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestLevelD> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.levelData.RequestLevelD parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.RequestLevelD parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelD parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.RequestLevelD parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelD parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.RequestLevelD parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelD parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.levelData.RequestLevelD parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.levelData.RequestLevelD parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.RequestLevelD parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.levelData.RequestLevelD prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestLevelD}
     *
     * <pre>
     *1309
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.levelData.RequestLevelDOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.levelData.internal_static_protocol_RequestLevelD_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.levelData.internal_static_protocol_RequestLevelD_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.levelData.RequestLevelD.class, protocol.levelData.RequestLevelD.Builder.class);
      }

      // Construct using protocol.levelData.RequestLevelD.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.levelData.internal_static_protocol_RequestLevelD_descriptor;
      }

      public protocol.levelData.RequestLevelD getDefaultInstanceForType() {
        return protocol.levelData.RequestLevelD.getDefaultInstance();
      }

      public protocol.levelData.RequestLevelD build() {
        protocol.levelData.RequestLevelD result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.levelData.RequestLevelD buildPartial() {
        protocol.levelData.RequestLevelD result = new protocol.levelData.RequestLevelD(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.levelData.RequestLevelD) {
          return mergeFrom((protocol.levelData.RequestLevelD)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.levelData.RequestLevelD other) {
        if (other == protocol.levelData.RequestLevelD.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.levelData.RequestLevelD parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.levelData.RequestLevelD) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestLevelD)
    }

    static {
      defaultInstance = new RequestLevelD(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestLevelD)
  }

  public interface ResponseLevelDOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 number = 1;
    /**
     * <code>required int32 number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    boolean hasNumber();
    /**
     * <code>required int32 number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    int getNumber();

    // required int32 road = 2;
    /**
     * <code>required int32 road = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    boolean hasRoad();
    /**
     * <code>required int32 road = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    int getRoad();
  }
  /**
   * Protobuf type {@code protocol.ResponseLevelD}
   *
   * <pre>
   *2309
   * </pre>
   */
  public static final class ResponseLevelD extends
      com.google.protobuf.GeneratedMessage
      implements ResponseLevelDOrBuilder {
    // Use ResponseLevelD.newBuilder() to construct.
    private ResponseLevelD(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseLevelD(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseLevelD defaultInstance;
    public static ResponseLevelD getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseLevelD getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseLevelD(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              number_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              road_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.levelData.internal_static_protocol_ResponseLevelD_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.levelData.internal_static_protocol_ResponseLevelD_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.levelData.ResponseLevelD.class, protocol.levelData.ResponseLevelD.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseLevelD> PARSER =
        new com.google.protobuf.AbstractParser<ResponseLevelD>() {
      public ResponseLevelD parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseLevelD(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseLevelD> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 number = 1;
    public static final int NUMBER_FIELD_NUMBER = 1;
    private int number_;
    /**
     * <code>required int32 number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    public boolean hasNumber() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 number = 1;</code>
     *
     * <pre>
     *当前层数
     * </pre>
     */
    public int getNumber() {
      return number_;
    }

    // required int32 road = 2;
    public static final int ROAD_FIELD_NUMBER = 2;
    private int road_;
    /**
     * <code>required int32 road = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    public boolean hasRoad() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 road = 2;</code>
     *
     * <pre>
     *当前关卡
     * </pre>
     */
    public int getRoad() {
      return road_;
    }

    private void initFields() {
      number_ = 0;
      road_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNumber()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRoad()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, number_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, road_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, number_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, road_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.levelData.ResponseLevelD parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelD parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.levelData.ResponseLevelD parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.levelData.ResponseLevelD parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.levelData.ResponseLevelD prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseLevelD}
     *
     * <pre>
     *2309
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.levelData.ResponseLevelDOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.levelData.internal_static_protocol_ResponseLevelD_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.levelData.internal_static_protocol_ResponseLevelD_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.levelData.ResponseLevelD.class, protocol.levelData.ResponseLevelD.Builder.class);
      }

      // Construct using protocol.levelData.ResponseLevelD.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        number_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        road_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.levelData.internal_static_protocol_ResponseLevelD_descriptor;
      }

      public protocol.levelData.ResponseLevelD getDefaultInstanceForType() {
        return protocol.levelData.ResponseLevelD.getDefaultInstance();
      }

      public protocol.levelData.ResponseLevelD build() {
        protocol.levelData.ResponseLevelD result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.levelData.ResponseLevelD buildPartial() {
        protocol.levelData.ResponseLevelD result = new protocol.levelData.ResponseLevelD(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.number_ = number_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.road_ = road_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.levelData.ResponseLevelD) {
          return mergeFrom((protocol.levelData.ResponseLevelD)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.levelData.ResponseLevelD other) {
        if (other == protocol.levelData.ResponseLevelD.getDefaultInstance()) return this;
        if (other.hasNumber()) {
          setNumber(other.getNumber());
        }
        if (other.hasRoad()) {
          setRoad(other.getRoad());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNumber()) {
          
          return false;
        }
        if (!hasRoad()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.levelData.ResponseLevelD parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.levelData.ResponseLevelD) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 number = 1;
      private int number_ ;
      /**
       * <code>required int32 number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public boolean hasNumber() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public int getNumber() {
        return number_;
      }
      /**
       * <code>required int32 number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public Builder setNumber(int value) {
        bitField0_ |= 0x00000001;
        number_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 number = 1;</code>
       *
       * <pre>
       *当前层数
       * </pre>
       */
      public Builder clearNumber() {
        bitField0_ = (bitField0_ & ~0x00000001);
        number_ = 0;
        onChanged();
        return this;
      }

      // required int32 road = 2;
      private int road_ ;
      /**
       * <code>required int32 road = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public boolean hasRoad() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 road = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public int getRoad() {
        return road_;
      }
      /**
       * <code>required int32 road = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public Builder setRoad(int value) {
        bitField0_ |= 0x00000002;
        road_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 road = 2;</code>
       *
       * <pre>
       *当前关卡
       * </pre>
       */
      public Builder clearRoad() {
        bitField0_ = (bitField0_ & ~0x00000002);
        road_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseLevelD)
    }

    static {
      defaultInstance = new ResponseLevelD(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseLevelD)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestLevelNum_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestLevelNum_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseLevelPage_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseLevelPage_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestLevelD_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestLevelD_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseLevelD_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseLevelD_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013level.proto\022\010protocol\032\013proto.proto\"8\n\017" +
      "RequestLevelNum\022\023\n\013page_number\030\001 \002(\005\022\020\n\010" +
      "leveLnum\030\002 \002(\005\"\023\n\021ResponseLevelPage\"\017\n\rR" +
      "equestLevelD\".\n\016ResponseLevelD\022\016\n\006number" +
      "\030\001 \002(\005\022\014\n\004road\030\002 \002(\005B\013B\tlevelData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestLevelNum_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestLevelNum_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestLevelNum_descriptor,
              new java.lang.String[] { "PageNumber", "LeveLnum", });
          internal_static_protocol_ResponseLevelPage_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseLevelPage_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseLevelPage_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_RequestLevelD_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestLevelD_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestLevelD_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseLevelD_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseLevelD_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseLevelD_descriptor,
              new java.lang.String[] { "Number", "Road", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
