package table.worldbossreward;

import table.LineKey;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
public class worldbossrewardLine implements  LineKey {
    public int percentage;
    public String reward;

    public List<List<Integer>> rewards;

    public void Parse() {
        rewards = new ArrayList<>();
        String[] groupInfo = reward.split("[|]");
        for (int i = 0; i < groupInfo.length; i++) {
            rewards.add(new ArrayList<>());
            String[] cellInfo = groupInfo[i].split("[,]");
            for (int j = 0; j < cellInfo.length; j++) {
                rewards.get(i).add(Integer.parseInt(cellInfo[j]));
            }
        }
    }
    @Override
    public int Key() {
        return percentage;
    }
}
