package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "activities", schema = "", catalog = "super_star_fruit")
public class ActivitiesEntity {
    private int id;
    private int type;
    private String uid;
    private int activitiesId;
    private Integer num;
    private Integer status;
    private long timestamp;
    private int overdue;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "activitiesId")
    public int getActivitiesId() {
        return activitiesId;
    }

    public void setActivitiesId(int activitiesId) {
        this.activitiesId = activitiesId;
    }

    @Basic
    @Column(name = "num")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "timestamp")
    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Basic
    @Column(name = "overdue")
    public int getOverdue() {
        return overdue;
    }

    public void setOverdue(int overdue) {
        this.overdue = overdue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ActivitiesEntity that = (ActivitiesEntity) o;
        return getId() == that.getId() &&
                getType() == that.getType() &&
                getActivitiesId() == that.getActivitiesId() &&
                getOverdue() == that.getOverdue() &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getNum(), that.getNum()) &&
                Objects.equals(getStatus(), that.getStatus()) &&
                Objects.equals(getTimestamp(), that.getTimestamp());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getType(), getUid(), getActivitiesId(), getNum(), getStatus(), getTimestamp(), getOverdue());
    }

    @Override
    public String toString() {
        return "ActivitiesEntity{" +
                "id=" + id +
                ", type=" + type +
                ", uid='" + uid + '\'' +
                ", activitiesId=" + activitiesId +
                ", num=" + num +
                ", status=" + status +
                ", timestamp=" + timestamp +
                ", overdue=" + overdue +
                '}';
    }
}
