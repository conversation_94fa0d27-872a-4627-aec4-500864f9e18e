package manager;

import server.SuperClient;

/**
 * Created by nara on 2018/10/6.
 */
public class MissionHeartHandler implements Runnable {
    public void run() {
        while (true){
            try {
                Thread.sleep(5000);

                if (!SuperClient.channel.isActive()){
                    SuperClient.reConnect();
                }
                String string = "";
                SuperClient.sendMsg(0,string.getBytes());

            }catch (Exception e){
                e.printStackTrace();
                SuperClient.reConnect();
            }
        }
    }
}
