package module.pvp;

import entities.PVPBaseDataEntity;
import entities.PVPPetsEntity;
import entities.RoleEntity;
import manager.MySql;
import module.mail.MailService;
import protocol.PVPData;

import java.util.*;

// 维护PVP的排名
public class PVPRank {
    private static PVPRank inst = null;
    private PVPRank() {
    }
    public static PVPRank getInstance() {
        if (inst == null) {
            inst = new PVPRank();
            inst.InitData();
        }
        return inst;
    }

    // 全部的PVP用户的基础数据
    List<PVPBaseDataEntity> pvpPlayerBaseDataArrayList = new ArrayList<>();
    public List<PVPBaseDataEntity> GetPVPPlayerBaseDataArrayList(){
        return pvpPlayerBaseDataArrayList;
    }
    // 快速查找
    HashMap<String, PVPBaseDataEntity> pvpPlayerBaseDataHashMap = new HashMap<>();
    public HashMap<String, PVPBaseDataEntity> GetPvpPlayerBaseDataHashMap(){
        return pvpPlayerBaseDataHashMap;
    }

    private void InitData(){
        pvpPlayerBaseDataArrayList = PVPDao.getInstance().GetPVPBaseDataAll();
        if (pvpPlayerBaseDataArrayList.size() < 100){
            InitGeneratePVPRobot();
            pvpPlayerBaseDataArrayList = PVPDao.getInstance().GetPVPBaseDataAll();
        }
        for (PVPBaseDataEntity data:
                pvpPlayerBaseDataArrayList) {
            pvpPlayerBaseDataHashMap.put(data.getRoleUid(), data);
        }

        PVPBaseDataSort();
    }
    // 生成机器人，格式: #名字
    private void InitGeneratePVPRobot(){
        PVPPetData pvpPetData = new PVPPetData();
        for (int i = 0; i < 100; i++) {
            String uid = "#" + String.valueOf(i);

            PVPBaseDataEntity pvpBaseDataEntity = new PVPBaseDataEntity();
            pvpBaseDataEntity.setRoleUid(uid);
            pvpBaseDataEntity.setRank(0);
            pvpBaseDataEntity.setScore(0);
            pvpBaseDataEntity.setVictory(0);
            pvpBaseDataEntity.setFail(0);
            MySql.insert(pvpBaseDataEntity);

            protocol.PVPData.PVPTeam.Builder robotTeam = PVPRobot.getInstance().GetPVPRobot();
            String petStr1 = pvpPetData.SetInfo(robotTeam.getPet1()).toJson();
            String petStr2 = pvpPetData.SetInfo(robotTeam.getPet2()).toJson();
            String petStr3 = pvpPetData.SetInfo(robotTeam.getPet3()).toJson();
            String petStr4 = pvpPetData.SetInfo(robotTeam.getPet4()).toJson();
            String petStr5 = pvpPetData.SetInfo(robotTeam.getPet5()).toJson();

            PVPPetsEntity pvpPetsEntity = PVPDao.getInstance().GetPVPPets(uid);
            pvpPetsEntity.setPet1(petStr1);
            pvpPetsEntity.setPet2(petStr2);
            pvpPetsEntity.setPet3(petStr3);
            pvpPetsEntity.setPet4(petStr4);
            pvpPetsEntity.setPet5(petStr5);
            MySql.update(pvpPetsEntity);
        }
    }

    // 赛季结算
    public void Settlement(){
        for (int i = 0; i < pvpPlayerBaseDataArrayList.size(); i++) {
            PVPBaseDataEntity data = pvpPlayerBaseDataArrayList.get(i);
            data.setRank(i + 1);
            data.setScore(0);
            data.setVictory(0);
            data.setFail(0);
            MySql.update(data);
            MailService.getInstance().SentRankReward(i+1, data.getRoleUid());
        }
    }

    // 战斗结果，记分
    public void PVPPlayerBattleResult(String uid, boolean victory){
        PVPBaseDataEntity pvpPetsEntity = pvpPlayerBaseDataHashMap.get(uid);
        if (victory){
            pvpPetsEntity.setScore(pvpPetsEntity.getScore() + 1);
            pvpPetsEntity.setVictory(pvpPetsEntity.getVictory() + 1);
        }else{
            pvpPetsEntity.setScore(pvpPetsEntity.getScore() - 1);
            if(pvpPetsEntity.getScore()<=0)
            {
                pvpPetsEntity.setScore(0);
            }
            pvpPetsEntity.setFail(pvpPetsEntity.getFail() + 1);
        }

        PVPBaseDataSort();
    }
    // 基本信息获取
    public PVPBaseDataEntity GetPVPPlayerBaseData(String uid){
        PVPBaseDataEntity pvpPetsEntity = pvpPlayerBaseDataHashMap.get(uid);
        if (pvpPetsEntity == null){
            pvpPetsEntity = PVPDao.getInstance().GetPVPBaseData(uid, pvpPlayerBaseDataArrayList.size() + 1);
            pvpPlayerBaseDataHashMap.put(uid, pvpPetsEntity);
            pvpPlayerBaseDataArrayList.add(pvpPetsEntity);
        }
        return pvpPetsEntity;
    }
    // 战斗对象获取，前后5名
    public String GetBattlePlayerPets(String uid, PVPData.ResponsePVPBattle.Builder builder) {
        int index = pvpPlayerBaseDataHashMap.get(uid).getRank();
        List<PVPBaseDataEntity> petsRange = new ArrayList<>();
        int startIndex = index - 5 < 0 ? 0: index - 5;
        for (int i = startIndex; i < pvpPlayerBaseDataArrayList.size() && i < startIndex + 11; i++) {
            if (pvpPlayerBaseDataArrayList.get(i).getRoleUid().equals(uid)){
                continue;
            }
            petsRange.add(pvpPlayerBaseDataArrayList.get(i));
        }


        // 前后 5 名成员添加进去
        for (PVPBaseDataEntity data :
                petsRange) {
            PVPData.PVPBattlePlayer.Builder player = PVPData.PVPBattlePlayer.newBuilder();

            if (data.getRoleUid().charAt(0) == '#'){
                player.setName(data.getRoleUid().split("#")[1]);
                player.setSex(1);
                player.setHead(1);
            }else {
                StringBuffer sql=new  StringBuffer("from RoleEntity where uid='").append(data.getRoleUid()).append("'");
                RoleEntity roleEntity =(RoleEntity)MySql.queryForOne(sql.toString());

                player.setName(roleEntity.getName());
                player.setSex(roleEntity.getSex());
                player.setHead(roleEntity.getHead());
            }

            builder.addPlayer(player);
        }


        return petsRange.get(new Random().nextInt(petsRange.size())).getRoleUid();
    }

    // 排行榜排名计算
    private void PVPBaseDataSort(){
        pvpPlayerBaseDataArrayList.sort((a, b)->b.getScore() - a.getScore());
        for (int i = 0; i < pvpPlayerBaseDataArrayList.size(); i++) {
            PVPBaseDataEntity data = pvpPlayerBaseDataArrayList.get(i);
            data.setRank(i + 1);
//            StringBuffer hql = new StringBuffer("update PVPBaseDataEntity set rank = '").append(data.getRank())
//                    .append("' where uid = '").append(data.getRoleUid()).append("'");
//            MySql.update(data);
        }
    }
}
