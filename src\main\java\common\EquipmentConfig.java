package common;

import java.util.List;

@ExcelConfigObject(key = "equipment")
public class EquipmentConfig {
    @ExcelColumn(name = "ID")
    private int equipType;
    @ExcelColumn(name = "name")
    private int name;
    @ExcelColumn(name = "desc")
    private int desc;
    @ExcelColumn(name = "sort")
    private int sort;
    @ExcelColumn(name = "level_required")
    private int level_required;
    @ExcelColumn(name = "extra")
    private int extra;
    @ExcelColumn(name = "hp", isList = true, isFloat = true)
    private List<Float> hp;
    @ExcelColumn(name = "physical_attack", isList = true, isFloat = true)
    private List<Float> physical_attack;
    @ExcelColumn(name = "magical_attack", isList = true, isFloat = true)
    private List<Float> magical_attack;
    @ExcelColumn(name = "physical_defence", isList = true, isFloat = true)
    private List<Float> physical_defence;
    @ExcelColumn(name = "magical_defence", isList = true, isFloat = true)
    private List<Float> magical_defence;
    @ExcelColumn(name = "speed", isList = true, isFloat = true)
    private List<Float> speed;

    public int getEquipType() {
        return equipType;
    }

    public void setEquipType(int equipType) {
        this.equipType = equipType;
    }

    public int getName() {
        return name;
    }

    public void setName(int name) {
        this.name = name;
    }

    public int getDesc() {
        return desc;
    }

    public void setDesc(int desc) {
        this.desc = desc;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getLevel_required() {
        return level_required;
    }

    public void setLevel_required(int level_required) {
        this.level_required = level_required;
    }

    public int getExtra() {
        return extra;
    }

    public void setExtra(int extra) {
        this.extra = extra;
    }

    public List<Float> getHp() {
        return hp;
    }

    public void setHp(List<Float> hp) {
        this.hp = hp;
    }

    public List<Float> getPhysical_attack() {
        return physical_attack;
    }

    public void setPhysical_attack(List<Float> physical_attack) {
        this.physical_attack = physical_attack;
    }

    public List<Float> getMagical_attack() {
        return magical_attack;
    }

    public void setMagical_attack(List<Float> magical_attack) {
        this.magical_attack = magical_attack;
    }

    public List<Float> getPhysical_defence() {
        return physical_defence;
    }

    public void setPhysical_defence(List<Float> physical_defence) {
        this.physical_defence = physical_defence;
    }

    public List<Float> getMagical_defence() {
        return magical_defence;
    }

    public void setMagical_defence(List<Float> magical_defence) {
        this.magical_defence = magical_defence;
    }

    public List<Float> getSpeed() {
        return speed;
    }

    public void setSpeed(List<Float> speed) {
        this.speed = speed;
    }


}
