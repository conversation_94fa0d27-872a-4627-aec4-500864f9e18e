package module.activity.RechargeRebate;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.PayDiamondEntity;
import io.netty.util.CharsetUtil;
import manager.ReportManager;
import module.mail.mail_tool.MailGetRewards;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.LimitedTimeRewardData;
import protocol.MailData;
import protocol.ProtoData;
import protocol.RechargeRebateData;
import table.RechargeRebate.RechargeRebateLine;
import table.RechargeRebate.RechargeRebateTable;
import table.equitment_attribute.EquipmentAttributeLine;
import table.equitment_attribute.EquipmentAttributeTable;
import table.limitedtime_reward.LimitedTimeRewardLine;
import table.limitedtime_reward.LimitedTimeRewardTable;
import table.new_shop_item.new_shop_itemTable;

import java.util.ArrayList;
import java.util.List;

public class RechargeRebateService {
    private static RechargeRebateService inst = null;
    public static RechargeRebateService getInstance() {
        if (inst == null) {
            inst = new RechargeRebateService();
        }
        return inst;
    }
    private static Logger log = LoggerFactory.getLogger(RechargeRebateService.class);

    public void ChargeDiamond(String uid, int payId) {
        PayDiamondEntity entity = PayDiamondDao.getInstance().PlayerPayDiamond(uid,
                Long.valueOf(new_shop_itemTable.getInstance().GetItem(payId).diamond_num));

        // 发送充值的钻石信息
        RechargeRebateData.ResponsePayDiamond.Builder builder = RechargeRebateData.ResponsePayDiamond.newBuilder();
        builder.setDiamondNum(entity.getDiamond_num());
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPAYDIAMOND_VALUE, builder.build().toByteArray());
    }

    public byte[] requestRechargeRebateReward(byte[] inBytes, String uid) {
        RechargeRebateData.RequestRechargeRebateReward reward = null;
        RechargeRebateData.ResponseRechargeRebateReward.Builder builder = RechargeRebateData.ResponseRechargeRebateReward.newBuilder();
        try {
            reward = RechargeRebateData.RequestRechargeRebateReward.parseFrom(inBytes);
            int nextId = reward.getCurRewardId() + 1;
            builder.setRewardId(nextId);
            // 存数据库
            RechargeRebateRewardDao.getInstance().ChangePlayerRechargeRebateReward(uid, nextId);
            GetItem(uid,reward.getCurRewardId());
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        return builder.build().toByteArray();
    }

    private void GetItem(String uid, int curRewardId) {
        RechargeRebateLine targetLine = RechargeRebateTable.getInstance().GetItem(curRewardId);
        if(targetLine == null) return;
        //这是奖励物品的一组信息，第一个元素代表类型，第二个元素代表id，第三个元素代表数量
        for (List<Integer> itemInfo : targetLine.rewards) {
            MailData.Attachment.Builder attacment = MailData.Attachment.newBuilder();
            attacment.setType(itemInfo.get(0));
            switch(itemInfo.get(0))
            {
                //type1表示获取的奖励是物品
                case 1:{
                    protocol.ItemData.Item.Builder item = protocol.ItemData.Item.newBuilder();
                    item.setId(itemInfo.get(1));
                    item.setNum(itemInfo.get(2));
                    attacment.addItem(item);
                    break;
                }
                //2表示装备
                case 2:{
                    EquipmentAttributeLine equipmentAttributeLine = EquipmentAttributeTable.getInstance().GetItem(itemInfo.get(1));
                    attacment.addEquip(EquipmentAttributeTable.getInstance().GenerateEquip(equipmentAttributeLine.type,equipmentAttributeLine.level_required));
                    break;
                }
                //3表示宠物
                case 3:{
                    protocol.MailData.DefaultPet.Builder pet = protocol.MailData.DefaultPet.newBuilder();
                    pet.setPetId(itemInfo.get(1));
                    pet.setIsEgg(0);
                    attacment.addDefaultPet(pet);
                    break;
                }
                default:
                    throw new RuntimeException("当前奖励类型错误");
            }
            MailGetRewards.getInstance().GetRewards(uid, attacment.build());
        }
    }


}
