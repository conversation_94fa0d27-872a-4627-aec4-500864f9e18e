package module.pay;

import java.util.List;
import protocol.ItemData;
import protocol.PayData;

/**
 * Created by nara on 2019/3/21.
 */
public interface IPay {
    PayData.ResponseChoosePay.Builder requestChoosePay(String uid,int payId,int platform);
    PayData.ResponseSubmitPayBack.Builder requestSubmitPayBack(String uid,String transactionID,String receipt);
    List<ItemData.Item.Builder> paySuccess(String uid,int payId,String product_id,String transaction_id,int platform);

    PayData.ResponseGooglePay.Builder requestGooglePay(String uid,String packageName,String productId, String purchaseToken, String orderId, String cp_order);
    PayData.ResponseConfirmHuaWeiPurchase.Builder requestConfirmHuaWeiPurchase(String uid,String key,String transactionID,String purchaseToken,String huaWeiOrderID);
    PayData.ResponseYSDKBalance.Builder requestYSDKBalance(String uid,String key, String openid,String openkey,String ts,String pf,String pfkey, String transactionID  );
    //PayData.ResponseWeChatPay.Builder requestWeChatPay(String uid,String inappPurchaseData,String inappDataSignature, String productId);

}