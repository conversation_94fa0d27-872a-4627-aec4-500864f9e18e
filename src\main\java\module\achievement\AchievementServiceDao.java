package module.achievement;

import entities.AchievementEntity;
import manager.MySql;
import org.hibernate.Session;
import java.util.List;

public class AchievementServiceDao {
    private static AchievementServiceDao inst = null;
    public static AchievementServiceDao getInstance() {
        if (inst == null) {
            inst = new AchievementServiceDao();
        }
        return inst;
    }

    public void insert(AchievementEntity entity){
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public void update(AchievementEntity entity) {
        Session session = MySql.getSession();
        session.update(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public AchievementEntity GetOne(String uid, String dailyType) {
        StringBuffer stringBuffer = new StringBuffer("from AchievementEntity where uid='").append(uid).append("'")
                .append(" and type='").append(dailyType).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (AchievementEntity) entity;
    }

    public List<Object> GetAll(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from AchievementEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());
        return list;
    }
}
