package server;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.MessageToByteEncoder;
import protocol.ProtoData;

/**
 * Created by nara on 2017/11/15.
 */
/**
 * <pre>
 * 编码
 * </pre>
 */
public class SuperEncoder extends MessageToByteEncoder<SuperProtocol> {
    @Override
    protected void encode(ChannelHandlerContext tcx, SuperProtocol msg,
                          ByteBuf out) throws Exception {
        out.writeByte(msg.getHead_data());
        out.writeInt(msg.getMsgId());
        out.writeInt(msg.getContentLength());
        out.writeBytes(msg.getContent());

//        if (msg.getMsgId() == ProtoData.MToC.RESPONSECONNECTFIGHT_VALUE){
//            /// System.out.println("======start out======");
////            for (int i = 0 ; i<out.readableBytes() ; i++){
////                int a = out.getByte(i)&0xff;
////                /// System.out.print(a + " ");
////            }
////            /// System.out.println();
////            /// System.out.println("============");
//            for (int i = 0 ; i<out.readableBytes() ; i++){
//                String a = Integer.toHexString(out.getByte(i)&0xff);
//                /// System.out.print(a + " ");
////            /// System.out.print(Integer.toHexString(buffer.getByte(i)) + " ");
//            }
//            /// System.out.println();
//            /// System.out.println("======end out======");
//        }
    }
}
