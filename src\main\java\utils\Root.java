package utils;

import common.EquipmentConfig;
import common.PetConfig;
import common.SuperConfig;
import entities.*;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import model.ItemInfo;
import model.RoleDressInfo;
import model.SqlCallBackInfo;
import model.UtilityInfo;
import module.equip.EquipUtils;
import module.item.ItemDao;
import module.item.ItemUtils;
import module.login.LoginDao;
import module.pet.PetUtils;
import module.robot.ranName;
import org.hibernate.Filter;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.query.Query;
import protocol.EquipData;
import protocol.ItemData;
import protocol.PetData;
import protocol.ProtoData;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;

public class Root {
    public static final String prefix = "##\\t"; //GM指令前缀
    public static final String record1 = "mission";
    public static final String record2 = "fall";
    public static List<String> recordList = new ArrayList<String>();

    static {
        Class<Root> root = Root.class;
        Field[] fields = root.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(true);
            String fieldName = field.getName();
            Object fieldSring = null;
            try {
                fieldSring = field.get(Root.class);
            } catch (IllegalAccessException e) {
                e.printStackTrace();
            }
            if (fieldName.startsWith("record") && fieldSring instanceof String) {
                recordList.add(fieldSring.toString());
            }
        }

    }

    public static void root(String content, String uid) {

        Redis jedis = Redis.getInstance();
        ItemDao itemdao = ItemDao.getInstance();
        StringBuffer hql = new StringBuffer("");
        String case1 = content.substring(2, 6);
        String command = content.split("@")[0].substring(2);
        /// System.err.println(content+"command"+command);
        if ("addPet".equals(command) || "deletePet".equals(command) || "auth".equals(command) || "item".equals(command)) {
            try {
                Method method = Root.class.getDeclaredMethod(command, String.class, String.class);
                method.setAccessible(true);
                method.invoke(null, uid, content.split("@")[1]);
                return;
            } catch (NoSuchMethodException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if ("allI".equals(case1)) {
            StringBuffer sql = new StringBuffer("from ItemEntity where uid='").append(uid).append("'");
            List<Object> itemInfoList = MySql.queryForList(sql.toString());
            Map<Integer, Double> hasItemMap = new HashMap<Integer, Double>();
            for (int i = 0; i < itemInfoList.size(); i++) {
                ItemEntity itemEntity = (ItemEntity) itemInfoList.get(i);
                hasItemMap.put(itemEntity.getItemid(), itemEntity.getItemnum());
            }
            Set<String> itemConfigSet = Redis.keys("itemconfig*");
            Iterator<String> iterator = itemConfigSet.iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                Map<String, String> itemInfoMap = jedis.hgetAll(key);
                double numMax = Double.parseDouble(itemInfoMap.get("group_max"));
                int itemId = Integer.parseInt(itemInfoMap.get("ID"));
                Double nowNums = hasItemMap.get(itemId);

                if (nowNums == null) {
                    itemdao.updateItemInfo(uid, itemId, Math.round(numMax));
                } else if (numMax - nowNums > 0) {
                    itemdao.updateItemInfo(uid, itemId, Math.round(numMax - nowNums));
                }
            }
            return;
        } else if ("lvlv".equals(case1)) {
            StringBuffer sql = new StringBuffer("update RoleEntity set gamecopy1=0 and gamecopy2=0 where uid= '").append(uid).append("'");
            MySql.updateSomes(sql.toString());
            jedis.hset("role:" + uid, "gamecopy1", "0");
            jedis.hset("role:" + uid, "gamecopy2", "0");
            return;
        } else if ("okok".equals(case1)) {
            //   /// System.err.println("okok");
            StringBuffer sql = new StringBuffer("from MissionEntity where uid='").append(uid).append("' and type=1");
            List<Object> list = MySql.queryForList(sql.toString());
            List missionList = new ArrayList<Integer>();
            for (int i = 0; i < list.size(); i++) {
                MissionEntity entity = (MissionEntity) list.get(i);
                missionList.add(entity.getMissionid());
            }
            int totalMissionNum = Redis.keys(SuperConfig.REDIS_EXCEL_MISSION + "*").size();
            String key = "rolemission:" + uid + "#1";
            Redis Jedis = Redis.getInstance();
            for (int i = 0; i < totalMissionNum; i++) {
                if (!missionList.contains(Integer.valueOf(i))) {
                    MissionEntity missionEntity = new MissionEntity();
                    missionEntity.setType(1);
                    missionEntity.setNums(10);
                    missionEntity.setMissionid(i);
                    missionEntity.setUid(uid);
                    missionEntity.setVersion(0);
                    MySql.insert(missionEntity);
                    jedis.hset(key, i + "", "10");

                }
            }
            return;
        }
        String num = content.substring(6, content.length());
        String case2 = null;
        if (content.length() < 7) {
            case2 = content.substring(2, 7);
        } else {
            case2 = content.substring(2, content.length());
            //  /// System.err.println(case2);
        }
        if ("dress".equals(case2)) {
            Connection connection = null;
            Statement statement = null;
            try {
                connection = JdbcUtils.getConnection();
                statement = connection.createStatement();
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            String dresses = content.substring(7);
            String[] batchUpdate = dresses.split(",");
            long overstamp = 0;
            String roleId = LoginDao.getInstance().getRoleIdFromUid(uid);
            String key = "roledress:" + uid + "#" + roleId;
            RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
            for (int i = 0; i < batchUpdate.length; i++) {
                Map dressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_DRESS, Integer.parseInt(batchUpdate[i]));
                int dressRoleId = Integer.parseInt((String) dressMap.get("mod"));
                /* jedis.hset("roledress:" + uid + "#" + roleId, "dresslist", MyUtils.objectToJson(dressList));*/
                StringBuffer sql = new StringBuffer("insert into dress (overstamp, roleid, dressid,uid,version) values(").append(overstamp).append(",").
                        append(Integer.parseInt(roleId)).append(",").append(Integer.parseInt(batchUpdate[i])).append(",'").append(uid).append("',").append(0).append(")");
                try {
                    List<UtilityInfo> dressList = roleDressInfo.getDressList();
                    UtilityInfo utilityInfo = new UtilityInfo();
                    utilityInfo.setId(Integer.parseInt(batchUpdate[i]));
                    utilityInfo.setOverStamp("0");
                    dressList.add(utilityInfo);
                    jedis.hset("roledress:" + uid + "#" + roleId, "dresslist", MyUtils.objectToJson(dressList));
                    statement.executeUpdate(sql.toString());
                } catch (SQLException e) {
                    e.printStackTrace();
                    /// System.out.println("sql语句错误");
                }
                if (i == batchUpdate.length) {
                    /// System.out.println("导入成功");
                    try {
                        statement.close();
                        connection.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                }
            }


        } else if ("allclothes".equals(case2)) {
            Set set = jedis.keys(SuperConfig.REDIS_EXCEL_DRESS + "*");
            Iterator<String> iterator = set.iterator();
            int dreessnums = 0;
            while (iterator.hasNext()) {
                String dressIds = iterator.next();
                String[] ids = dressIds.split(":");
                int dressId = Integer.parseInt(ids[1]);
                StringBuffer sql = new StringBuffer("from DressEntity where uid='").append(uid).append("' and dressid=").append(dressId);
                Object entity = MySql.queryForOne(sql.toString());
                if (entity != null) {
                    continue;
                } else {
                    dreessnums++;
                    Map<String, String> dressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_DRESS, dressId);
                    String role = dressMap.get("mod");
                    DressEntity dressEntity = new DressEntity();
                    dressEntity.setDressid(dressId);
                    dressEntity.setRoleid(Integer.parseInt(role));
                    dressEntity.setType(1);
                    dressEntity.setUid(uid);
                    dressEntity.setOverstamp("0");
                    MySql.insert(dressEntity);
                 /*  String key = "roledress:" + uid + "#" + Integer.parseInt(role);
                   RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
                   List<UtilityInfo> dressList=roleDressInfo.getDressList();
                   UtilityInfo  utilityInfo = new UtilityInfo();
                   utilityInfo.setId(dressId);
                   utilityInfo.setOverStamp("0");
                   dressList.add(utilityInfo);
                   jedis.hset("roledress:" + uid + "#" + Integer.parseInt(role), "dresslist", MyUtils.objectToJson(dressList));*/
                }
            }
            StringBuffer sql = new StringBuffer("update RoleEntity set dressnums=dressnums+").append(dreessnums).append(" where uid='").append(uid).append("'");
            MySql.updateSomes(sql.toString());
            Redis.del("role:" + uid);
            return;
        }
        if (case1.equals("jump")) {
            Map<String, String> map = jedis.hgetAll("headball:" + uid);
            map.put("jump_missionid", num + "");
            jedis.hmset("headball:" + uid, map);
            hql.append(" update RoleEntity set headballmissionid= '").append(num).append("' where uid= '").append(uid).append("' ");
            HeadBallEntity headball = new HeadBallEntity();
            headball.setIs_first(1);
            headball.setBattle_num(2);
            headball.setJump_num(3);
            headball.setJump_missionid(Integer.parseInt(num));
            headball.setIs_threestar(2);
            headball.setUser_id(uid);
            MySql.mustInsert(headball);
        } else if (case1.equals("miss")) {
            hql.append(" update RoleEntity set missionid= '").append(num).append("' , missionstamp= '")
                    .append(ranName.time()).append("' where uid= '").append(uid).append("' ");
        } else if (case1.equals("gold")) {
            hql.append(" update ItemEntity set itemnum=itemnum+ '").append(num).append("' where uid= '")
                    .append(uid).append("' and itemid= 1 ");
        } else if (case1.equals("star")) {
            hql.append(" update ItemEntity set itemnum=itemnum+ '").append(num).append("' where uid= '")
                    .append(uid).append("' and itemid= 2 ");
        } else if (case1.equals("item")) {
            StringBuffer stringBuffer = new StringBuffer(" from ItemEntity where uid='").append(uid).append("' and itemId=").append(num.split(",")[0]);
            ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuffer.toString());
            long addNum = 0;
            Integer nowNeed = Integer.valueOf(num.split(",")[1]);
            if (itemEntity == null) {
                addNum = nowNeed;
            } else {
                addNum = (long) (nowNeed - itemEntity.getItemnum());
            }
            itemdao.updateItemInfo(uid, Integer.valueOf(num.split(",")[0]), addNum);
        } else if (case1.equals("fall")) {
            hql.append("update RoleEntity set headballadvance=").append(num)
                    .append("where uid='").append(uid).append("'");
            jedis.hset("role:" + uid, "headballadvance", num);

        } else if (case1.equals("clea")) {
            hql.append("delete ItemEntity where uid='").append(uid).append("' and type<> 0");
            for (int i = 1; i <= 3; i++)
                Redis.getInstance().del("roleitem:" + uid + "#" + i);
        } else if ("reborn".equals(case2.substring(0, case2.length() - 2))) {
            Redis redis = Redis.getInstance();
            // /// System.out.println(case2);
            String advance = case2.split(",")[1];
            redis.hset("role:" + uid, "advance", advance);
            hql = new StringBuffer("update RoleEntity set advance = '").append(advance).append("' where uid = '").append(uid).append("'");
        }

        if (!"item".equals(case1) && !"dress".equals(case2)) {
            MySql.mustUpdateSomes(hql.toString());
        }
    }

    public static void GmOrder(String content, String uid) {
        for (String record : recordList) {
            if (content.startsWith(record)) {

                break;
            }
        }
    }

    public static void mission(String uid, String num) throws IllegalArgumentException {
        try {
            Integer.parseInt(num);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException();
        }
        Redis jedis = Redis.getInstance();
        StringBuffer hql = new StringBuffer("");
        hql.append("update RoleEntity set headballadvance=").append(num)
                .append("where uid='").append(uid).append("'");
        jedis.hset("role:" + uid, "headballadvance", num);
        MySql.updateSomes(hql.toString());
    }


    public static void fall(String uid, String num) throws IllegalArgumentException {
        try {
            Integer.parseInt(num);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException();
        }

        StringBuffer hql = new StringBuffer("");
        hql.append(" update RoleEntity set missionid= '").append(num).append("' , missionstamp= '")
                .append(ranName.time()).append("' where uid= '").append(uid).append("' ");
        MySql.updateSomes(hql.toString());
    }

//    public static void addEquip(String uid,String content){
//        EquipEntity equipEntity=EquipUtils.createEquip(uid,Integer.parseInt(content));
//        EquipData.ResponseGetEquip.Builder equipBuilder=EquipData.ResponseGetEquip.newBuilder();
//        equipBuilder.setErrorId(0);
//        equipBuilder.addEquip(EquipEntity.entityToPb(equipEntity));
//        ReportManager.reportInfo(uid,ProtoData.SToC.RESPONSEGETEQUIP_VALUE,equipBuilder.build().toByteArray());
//    }
    public static void addPet(String uid, String content) {
        try {
            PetEntity petEntity = PetUtils.createPet(uid, Integer.parseInt(content), PetConfig.petFromGM);
            PetData.ResponseOperatePet.Builder petbuilder = PetData.ResponseOperatePet.newBuilder();
            petbuilder.setErrorId(0);
            petbuilder.setType(1);
            petbuilder.addPet(PetEntity.entityToPb(petEntity));
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petbuilder.build().toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void deletePet(String uid, String content) {
        try {
            PetData.ResponseOperatePet.Builder petbuilder = PetData.ResponseOperatePet.newBuilder();
            if ("all".equals(content)) {
                StringBuilder queryPets = new StringBuilder("from PetEntity where friendId='").append(uid).append("'");
                List<Object> list = MySql.queryForList(queryPets.toString());
                petbuilder.setErrorId(0);
                petbuilder.setType(3);
                for (int i = 0; i < list.size(); i++) {
                    PetEntity entity = (PetEntity) list.get(i);
                    petbuilder.addPet(PetEntity.entityToPb(entity));
                }
                StringBuilder stringBuilder = new StringBuilder("delete PetEntity where friendId='").append(uid).append("'");
                MySql.updateSomes(stringBuilder.toString());
            } else {
                int petId = Integer.parseInt(content);
                StringBuilder queryPets = new StringBuilder("from PetEntity where friendId='").append(uid).append("' and petUId=").append(petId);
                PetEntity pet = (PetEntity) MySql.queryForOne(queryPets.toString());
                petbuilder.addPet(PetEntity.entityToPb(pet));
                MySql.delete(pet);
            }
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petbuilder.build().toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void auth(String uid, String content) {
        try {
            if ("1".equals(content)) {
                StringBuilder sql = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(sql.toString());
                StringBuilder sql1 = new StringBuilder("update UserEntity set  authentication=1  where userid='").append(roleEntity.getUserid()).append("'");
                MySql.updateSomes(sql1.toString());
            } else if ("0".equals(content)) {
                StringBuilder sql = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(sql.toString());
                StringBuilder sql1 = new StringBuilder("update UserEntity set  authentication=0  where userid='").append(roleEntity.getUserid()).append("'");
                MySql.updateSomes(sql1.toString());

            }
        } catch (Exception e) {
            e.printStackTrace();

        }


    }

    public static void item(String uid, String content) {
        try {
            ItemInfo itemInfo = ItemUtils.getItemInfo(content);
            StringBuffer stringBuffer = new StringBuffer(" from ItemEntity where uid='").append(uid).append("' and itemId=").append(itemInfo.getId());
            ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuffer.toString());
            double num = itemInfo.getNum();
            ItemDao itemdao = ItemDao.getInstance();
            if (itemEntity == null) {
                /// System.err.println("itemEntity1"+itemEntity);
                itemdao.updateItemInfo(uid, itemInfo.getId(), (long) num);
            } else {
                /// System.err.println("itemEntity2"+itemEntity);
                itemdao.updateItemInfo(uid, itemInfo.getId(), (long) (num - itemEntity.getItemnum()));
            }
            ItemData.ReportItem.Builder builder = ItemData.ReportItem.newBuilder();
            builder.addItem(ItemUtils.getItemData(itemInfo.getId(), itemInfo.getNum()));
            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, builder.build().toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
