package module.Dispatch;

import Json.ExperienceJson;
import manager.Redis;
import utils.MyUtils;

import java.util.Map;

public class DispatchDao {
    private static DispatchDao inst = null;

    public static DispatchDao getInstance() {
        if (inst == null) {
            inst = new DispatchDao();
        }
        return inst;
    }

    public DispatchEntity getAllExperience(String uid) {
        DispatchEntity dispatchEntity = null;
        Redis redis = Redis.getInstance();
        String key = Dispatch.getKey(uid);
        Map<String, String> data = redis.hgetAll(key);
        //新增
        if (data == null || data.size() == 0) {
            ///         /// System.err.println("新增");
            dispatchEntity = Dispatch.init();
            data = Dispatch.objectToMap(dispatchEntity);
            redis.hmset(key, data);
        }
        dispatchEntity = Dispatch.datatoObject(data);
        return dispatchEntity;
    }

    public static boolean updateDispatch(String uid, DispatchEntity entity) {
        if (entity == null) {
            return false;
        }
        try {
            Redis redis = Redis.getInstance();

            Map<String, String> data = Dispatch.objectToMap(entity);
            redis.hmset(Dispatch.getKey(uid), data);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static boolean updateExperience(String uid, ExperienceJson experienceJson) {
        if (experienceJson == null) {
            return false;
        }
        try {
            Redis redis = Redis.getInstance();
            redis.hset(Dispatch.getKey(uid), experienceJson.getKey() + "", MyUtils.objectToJson(experienceJson));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static ExperienceJson getExperience(String uid, String field) {
        ExperienceJson experienceJson = null;
        try {
            Redis redis = Redis.getInstance();
            String result = redis.hget(Dispatch.getKey(uid), field);
            Object object = MyUtils.jsonToBean(result, ExperienceJson.class);
            experienceJson = (ExperienceJson) object;

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        return experienceJson;
    }

    public static void removeExperience(String uid, String filed) {
        long result = Redis.hdel(Dispatch.getKey(uid), filed);
        ///    /// System.err.println(result+"result");


    }

    public static void removeDispatchEntity(String uid) {
        long result = Redis.del(Dispatch.getKey(uid));
        /// /// System.err.println(result+"result");


    }
}
