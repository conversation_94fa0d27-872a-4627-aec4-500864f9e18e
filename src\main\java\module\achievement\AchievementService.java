package module.achievement;

import entities.AchievementEntity;
import entities.CompleteAchievementEntity;
import entities.CompleteDailyTaskEntity;
import entities.DailyTaskEntity;
import module.activity.LimitedTime.LimitedTimeRewardService;
import module.daily_task.CompleteDailtTaskDao;
import module.daily_task.DailtTaskDao;
import module.daily_task.DailyTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.TaskData;

import java.util.Date;
import java.util.List;

public class AchievementService {
    private static Logger log = LoggerFactory.getLogger(DailyTaskService.class);
    private static AchievementService inst = null;
    public static AchievementService getInstance() {
        if (inst == null) {
            inst = new AchievementService();
        }
        return inst;
    }

    // 每日成就的任务数量 ++
    public byte[] NumberIncreaseAchievement(String uid,byte[] bytes){
        try {
            TaskData.RequestAchievementType requestData  = TaskData.RequestAchievementType.parseFrom(bytes);
            if (requestData != null){
                AchievementEntity dailyTaskEntity = AchievementServiceDao.getInstance().GetOne(
                        uid,
                        String.valueOf(requestData.getType())
                );
                if (dailyTaskEntity != null){
                    // 数据库里面有相同类型的每日任务
                    dailyTaskEntity.setNumber(dailyTaskEntity.getNumber() + requestData.getNum());
                    AchievementServiceDao.getInstance().update(dailyTaskEntity);
                }else{
                    // 没有此任务类型，插入一个
                    AchievementEntity entity = new AchievementEntity();
                    entity.setUid(uid);
                    entity.setTime(new Date(System.currentTimeMillis()));
                    entity.setType(requestData.getType());
                    entity.setNumber(requestData.getNum());
                    AchievementServiceDao.getInstance().insert(entity);
                }

                // 限时任务
                //LimitedTimeRewardService.getInstance().TaskNumberIncrease(uid, requestData.getType(), requestData.getNum());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 返回奖励

        // 不需要返回值
        return null;
    }

    // 修改任务状态
    public byte[] FinishAchievement(String uid,byte[] bytes){
        try {
            TaskData.RequestAchievementIs requestData  = TaskData.RequestAchievementIs.parseFrom(bytes);
//            System.err.println("id = " + requestData.getId()+"\n"+
//            "isComplete = " + requestData.getIsComplete());
            if (requestData !=  null){
                CompleteAchievementEntity dailyTaskEntity = CompleteAchievementServiceDao.getInstance().GetOne(
                        uid,
                        String.valueOf(requestData.getId())
                );
                if (dailyTaskEntity != null){
//                    System.err.println("测试成功\n" + dailyTaskEntity.isComplete() + dailyTaskEntity.getTime());
                    dailyTaskEntity.setComplete(requestData.getIsComplete());
                    CompleteAchievementServiceDao.getInstance().update(dailyTaskEntity);
                }else{
//                    System.err.println("测试失败");
                    CompleteAchievementEntity savaData = new CompleteAchievementEntity();
                    savaData.setUid(uid);
                    savaData.setTime(new Date(System.currentTimeMillis()));
                    savaData.setAchievementId(requestData.getId());
                    savaData.setComplete(requestData.getIsComplete());
                    CompleteAchievementServiceDao.getInstance().insert(savaData);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        // 返回奖励

        // 不需要返回值
        return null;
    }

    // 获取全部的成就
    public byte[] GetAllAchievement(String uid, byte[] bytes){
//        System.err.println("成就 获取全部的");
        TaskData.ResponseAchievement.Builder builder = TaskData.ResponseAchievement.newBuilder();
        try {
            List<Object> dailyTaskEntity = AchievementServiceDao.getInstance().GetAll(uid);
            List<Object> completeDailyTaskEntity = CompleteAchievementServiceDao.getInstance().GetAll(uid);

            for (Object item :
                    dailyTaskEntity) {
                AchievementEntity data = (AchievementEntity)item;
                TaskData.Dailytansks.Builder dataBuilder = TaskData.Dailytansks.newBuilder();
                dataBuilder.setNum(data.getNumber());
                dataBuilder.setType(data.getType());

                builder.addDailytansks(dataBuilder);
            }

            for (Object item :
                    completeDailyTaskEntity) {
                CompleteAchievementEntity data = (CompleteAchievementEntity)item;
                TaskData.Completedailytasks.Builder dataBuilder = TaskData.Completedailytasks.newBuilder();
                dataBuilder.setId(data.getAchievementId());
                dataBuilder.setIsComplete(data.isComplete());

                builder.addCompletedailytasks(dataBuilder);
            }

            builder.setErrorId(1);
        }catch (Exception e){
            builder.setErrorId(0);
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    // 没写协议，自动领取成就
    public byte[] AutoFinishAchievement(String uid, byte[] bytes){
        return  null;
    }
}
