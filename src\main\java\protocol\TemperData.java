// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: temper.proto

package protocol;

public final class TemperData {
  private TemperData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface ResponseTemperOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional bool get_item = 1;
    /**
     * <code>optional bool get_item = 1;</code>
     */
    boolean hasGetItem();
    /**
     * <code>optional bool get_item = 1;</code>
     */
    boolean getGetItem();

    // optional bool delete_item = 2;
    /**
     * <code>optional bool delete_item = 2;</code>
     */
    boolean hasDeleteItem();
    /**
     * <code>optional bool delete_item = 2;</code>
     */
    boolean getDeleteItem();
  }
  /**
   * Protobuf type {@code protocol.ResponseTemper}
   *
   * <pre>
   *2315
   * </pre>
   */
  public static final class ResponseTemper extends
      com.google.protobuf.GeneratedMessage
      implements ResponseTemperOrBuilder {
    // Use ResponseTemper.newBuilder() to construct.
    private ResponseTemper(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseTemper(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseTemper defaultInstance;
    public static ResponseTemper getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseTemper getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseTemper(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              getItem_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              deleteItem_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.TemperData.internal_static_protocol_ResponseTemper_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.TemperData.internal_static_protocol_ResponseTemper_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.TemperData.ResponseTemper.class, protocol.TemperData.ResponseTemper.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseTemper> PARSER =
        new com.google.protobuf.AbstractParser<ResponseTemper>() {
      public ResponseTemper parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseTemper(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseTemper> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional bool get_item = 1;
    public static final int GET_ITEM_FIELD_NUMBER = 1;
    private boolean getItem_;
    /**
     * <code>optional bool get_item = 1;</code>
     */
    public boolean hasGetItem() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional bool get_item = 1;</code>
     */
    public boolean getGetItem() {
      return getItem_;
    }

    // optional bool delete_item = 2;
    public static final int DELETE_ITEM_FIELD_NUMBER = 2;
    private boolean deleteItem_;
    /**
     * <code>optional bool delete_item = 2;</code>
     */
    public boolean hasDeleteItem() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional bool delete_item = 2;</code>
     */
    public boolean getDeleteItem() {
      return deleteItem_;
    }

    private void initFields() {
      getItem_ = false;
      deleteItem_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, getItem_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBool(2, deleteItem_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, getItem_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, deleteItem_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.TemperData.ResponseTemper parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.TemperData.ResponseTemper parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.TemperData.ResponseTemper parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.TemperData.ResponseTemper parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.TemperData.ResponseTemper prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseTemper}
     *
     * <pre>
     *2315
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.TemperData.ResponseTemperOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.TemperData.internal_static_protocol_ResponseTemper_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.TemperData.internal_static_protocol_ResponseTemper_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.TemperData.ResponseTemper.class, protocol.TemperData.ResponseTemper.Builder.class);
      }

      // Construct using protocol.TemperData.ResponseTemper.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        getItem_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        deleteItem_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.TemperData.internal_static_protocol_ResponseTemper_descriptor;
      }

      public protocol.TemperData.ResponseTemper getDefaultInstanceForType() {
        return protocol.TemperData.ResponseTemper.getDefaultInstance();
      }

      public protocol.TemperData.ResponseTemper build() {
        protocol.TemperData.ResponseTemper result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.TemperData.ResponseTemper buildPartial() {
        protocol.TemperData.ResponseTemper result = new protocol.TemperData.ResponseTemper(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.getItem_ = getItem_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.deleteItem_ = deleteItem_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.TemperData.ResponseTemper) {
          return mergeFrom((protocol.TemperData.ResponseTemper)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.TemperData.ResponseTemper other) {
        if (other == protocol.TemperData.ResponseTemper.getDefaultInstance()) return this;
        if (other.hasGetItem()) {
          setGetItem(other.getGetItem());
        }
        if (other.hasDeleteItem()) {
          setDeleteItem(other.getDeleteItem());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.TemperData.ResponseTemper parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.TemperData.ResponseTemper) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional bool get_item = 1;
      private boolean getItem_ ;
      /**
       * <code>optional bool get_item = 1;</code>
       */
      public boolean hasGetItem() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional bool get_item = 1;</code>
       */
      public boolean getGetItem() {
        return getItem_;
      }
      /**
       * <code>optional bool get_item = 1;</code>
       */
      public Builder setGetItem(boolean value) {
        bitField0_ |= 0x00000001;
        getItem_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool get_item = 1;</code>
       */
      public Builder clearGetItem() {
        bitField0_ = (bitField0_ & ~0x00000001);
        getItem_ = false;
        onChanged();
        return this;
      }

      // optional bool delete_item = 2;
      private boolean deleteItem_ ;
      /**
       * <code>optional bool delete_item = 2;</code>
       */
      public boolean hasDeleteItem() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional bool delete_item = 2;</code>
       */
      public boolean getDeleteItem() {
        return deleteItem_;
      }
      /**
       * <code>optional bool delete_item = 2;</code>
       */
      public Builder setDeleteItem(boolean value) {
        bitField0_ |= 0x00000002;
        deleteItem_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool delete_item = 2;</code>
       */
      public Builder clearDeleteItem() {
        bitField0_ = (bitField0_ & ~0x00000002);
        deleteItem_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseTemper)
    }

    static {
      defaultInstance = new ResponseTemper(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseTemper)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseTemper_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseTemper_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014temper.proto\022\010protocol\032\013proto.proto\"7\n" +
      "\016ResponseTemper\022\020\n\010get_item\030\001 \001(\010\022\023\n\013del" +
      "ete_item\030\002 \001(\010B\014B\nTemperData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_ResponseTemper_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_ResponseTemper_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseTemper_descriptor,
              new java.lang.String[] { "GetItem", "DeleteItem", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
