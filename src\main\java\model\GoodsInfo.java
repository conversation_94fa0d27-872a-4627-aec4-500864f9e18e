package model;

import java.util.Objects;

//交易所商品对象
public class GoodsInfo {
    private int id;
    private int nowPrice;
    private int fluctuate;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getNowPrice() {
        return nowPrice;
    }

    public void setNowPrice(int nowPrice) {
        this.nowPrice = nowPrice;
    }

    public int getFluctuate() {
        return fluctuate;
    }

    public void setFluctuate(int fluctuate) {
        this.fluctuate = fluctuate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        GoodsInfo goodsInfo = (GoodsInfo) o;
        return getId() == goodsInfo.getId() &&
                getNowPrice() == goodsInfo.getNowPrice() &&
                getFluctuate() == goodsInfo.getFluctuate();
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getNowPrice(), getFluctuate());
    }
}
