package module.ranking;


import protocol.ProtoData;
import protocol.RankingData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ProtoData;
import protocol.UserData;
public class RankingService {
//    private static Logger log = LoggerFactory.getLogger(RankingService.class);
//    private static RankingService inst = null;
//    public static RankingService getInstance() {
//        if (inst == null) {
//            inst = new RankingService();
//        }
//        return inst;
//    }
//    public byte[] getRank(String uid){
//        RankingData.ResponseGetRanking.Builder builder = RankingData.ResponseGetRanking.newBuilder();
//        if (uid == null){
//            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
//        }else {
//            IRanking iRank = RankingDao.getInstance();
//            builder = iRank.getRankList(uid);
//        }
//        return builder.build().toByteArray();
//    }
}
