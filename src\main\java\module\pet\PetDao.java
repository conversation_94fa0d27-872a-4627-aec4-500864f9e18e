package module.pet;

import entities.PetEntity;
import manager.MySql;

import java.util.List;

public class PetDao {
    private static PetDao inst = null;

    public static PetDao getInstance() {
        if (inst == null) {
            inst = new PetDao();
        }
        return inst;
    }

    public List<Object> queryPet(String uid) {
        StringBuilder stringBuilder = new StringBuilder(" from PetEntity where friendId='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuilder.toString());
        return list;
    }

    public PetEntity queryPets(String uid) {
        StringBuilder stringBuilder = new StringBuilder(" from PetEntity where friendId='").append(uid).append("'");
        Object object = MySql.queryForOne(stringBuilder.toString());
        return (PetEntity)object;
    }

    public PetEntity queryPet(String uid, int petUid) {
        StringBuilder stringBuilder = new StringBuilder(" from PetEntity where friendId='").append(uid).append("' and petUId=").append(petUid);
        Object entity = MySql.queryForOne(stringBuilder.toString());
        return (PetEntity) entity;
    }

    public PetEntity queryPet(int petUid) {
        StringBuilder stringBuilder = new StringBuilder(" from PetEntity where petUId='").append(petUid).append("'");
        Object entity = MySql.queryForOne(stringBuilder.toString());
        return (PetEntity) entity;
    }


    public void DeleteAllPets(String uid) {
        StringBuffer stringBuffer = new StringBuffer("Delete from PetEntity where friendId='").append(uid).append("'");
        MySql.updateSomes(stringBuffer.toString());
    }
}
