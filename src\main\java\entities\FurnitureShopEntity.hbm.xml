<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.FurnitureShopEntity" table="furnitureshop" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="uid" column="uid"/>
        <property name="furnitureId" column="furnitureId"/>
        <property name="nums" column="nums" />
    </class>
</hibernate-mapping>