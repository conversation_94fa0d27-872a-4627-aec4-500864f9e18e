package module.temperature_item;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.TemItemEntity;
import protocol.TemItemData;

public class TemItemService {
    private static TemItemService inst = null;

    public static TemItemService getInstance() {
        if (inst == null) {
            inst = new TemItemService();
        }
        return inst;
    }

    public byte[] RequestTemItem(byte[] inBytes, String uid) {
        TemItemData.RequestTemItem data = null;
        TemItemData.ResponseTemItem.Builder res = TemItemData.ResponseTemItem.newBuilder();
        try {
            data = TemItemData.RequestTemItem.parseFrom(inBytes);
            if (data != null){
                TemItemEntity itemEntity = TemItemDao.getInstance().Update(uid, data);
                res.setBattle(itemEntity.getBattle());
                res.setLeaf(itemEntity.getLeaf());
                res.setPuzzle(itemEntity.getPuzzle());
                res.setRun(itemEntity.getRun());
                res.setSoil(itemEntity.getSoil());
                res.setWater(itemEntity.getWater());
                res.setWood(itemEntity.getWood());

            }
            else
            {
                System.out.println("TemItemData.RequestTemItem parseFrom error");
            }
        } catch (Exception e){
            e.printStackTrace();
        }
        finally {
            return res.build().toByteArray();
        }
    }
}
