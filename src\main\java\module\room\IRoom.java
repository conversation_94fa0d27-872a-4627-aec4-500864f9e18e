package module.room;

import protocol.MissionData;
import protocol.UserData;

import java.util.List;

/**
 * Created by nara on 2018/6/1.
 */
public interface IRoom {
    MissionData.ResponseJoinRoom.Builder joinRoom(String uid,int type,int id,String pwd);
    MissionData.ResponseCreateRoom.Builder createRoom(String uid,int type,String name,String pwd);
    MissionData.ResponseLeaveRoom.Builder leaveRoom(String uid);
    MissionData.ResponseUpdateRoomInfo.Builder updateRoomInfo(String uid,int type,String name,String pwd);
    MissionData.ResponseUpdateRoomRole.Builder updateRoomRole(String uid,int status,int queue,int position);
    MissionData.ResponseJoinFightHall.Builder joinFightHall(String uid,int type,int hall);
    void moveInRoom(String uid, UserData.PointDouble point, UserData.PointDouble direction,double speed);
    int startInRoom(String uid);
    MissionData.SResponseOverMission.Builder overMission(int type,int hall,int roomId,List<String> uids);
}
