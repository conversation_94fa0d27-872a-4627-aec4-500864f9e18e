// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: notice.proto

package protocol;

public final class NoticeData {
  private NoticeData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface NoticeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 nid = 1;
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告id
     * </pre>
     */
    boolean hasNid();
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告id
     * </pre>
     */
    int getNid();

    // optional string sender = 2;
    /**
     * <code>optional string sender = 2;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    boolean hasSender();
    /**
     * <code>optional string sender = 2;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    String getSender();
    /**
     * <code>optional string sender = 2;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    com.google.protobuf.ByteString
        getSenderBytes();

    // optional string owner = 3;
    /**
     * <code>optional string owner = 3;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    boolean hasOwner();
    /**
     * <code>optional string owner = 3;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    String getOwner();
    /**
     * <code>optional string owner = 3;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    com.google.protobuf.ByteString
        getOwnerBytes();

    // optional string title = 4;
    /**
     * <code>optional string title = 4;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    boolean hasTitle();
    /**
     * <code>optional string title = 4;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    String getTitle();
    /**
     * <code>optional string title = 4;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    // required string content = 5;
    /**
     * <code>required string content = 5;</code>
     *
     * <pre>
     *内容 
     * </pre>
     */
    boolean hasContent();
    /**
     * <code>required string content = 5;</code>
     *
     * <pre>
     *内容 
     * </pre>
     */
    String getContent();
    /**
     * <code>required string content = 5;</code>
     *
     * <pre>
     *内容 
     * </pre>
     */
    com.google.protobuf.ByteString
        getContentBytes();

    // optional int64 timeStamp = 6;
    /**
     * <code>optional int64 timeStamp = 6;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    boolean hasTimeStamp();
    /**
     * <code>optional int64 timeStamp = 6;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    long getTimeStamp();
  }
  /**
   * Protobuf type {@code protocol.Notice}
   *
   * <pre>
   *公告1111
   * </pre>
   */
  public static final class Notice extends
      com.google.protobuf.GeneratedMessage
      implements NoticeOrBuilder {
    // Use Notice.newBuilder() to construct.
    private Notice(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Notice(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Notice defaultInstance;
    public static Notice getDefaultInstance() {
      return defaultInstance;
    }

    public Notice getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Notice(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              nid_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              sender_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              owner_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              title_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              content_ = input.readBytes();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              timeStamp_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return NoticeData.internal_static_protocol_Notice_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return NoticeData.internal_static_protocol_Notice_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Notice.class, Builder.class);
    }

    public static com.google.protobuf.Parser<Notice> PARSER =
        new com.google.protobuf.AbstractParser<Notice>() {
      public Notice parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Notice(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<Notice> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 nid = 1;
    public static final int NID_FIELD_NUMBER = 1;
    private int nid_;
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告id
     * </pre>
     */
    public boolean hasNid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告id
     * </pre>
     */
    public int getNid() {
      return nid_;
    }

    // optional string sender = 2;
    public static final int SENDER_FIELD_NUMBER = 2;
    private Object sender_;
    /**
     * <code>optional string sender = 2;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    public boolean hasSender() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional string sender = 2;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    public String getSender() {
      Object ref = sender_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sender_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string sender = 2;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSenderBytes() {
      Object ref = sender_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sender_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string owner = 3;
    public static final int OWNER_FIELD_NUMBER = 3;
    private Object owner_;
    /**
     * <code>optional string owner = 3;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    public boolean hasOwner() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string owner = 3;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    public String getOwner() {
      Object ref = owner_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          owner_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string owner = 3;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    public com.google.protobuf.ByteString
        getOwnerBytes() {
      Object ref = owner_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        owner_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string title = 4;
    public static final int TITLE_FIELD_NUMBER = 4;
    private Object title_;
    /**
     * <code>optional string title = 4;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string title = 4;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    public String getTitle() {
      Object ref = title_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          title_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string title = 4;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string content = 5;
    public static final int CONTENT_FIELD_NUMBER = 5;
    private Object content_;
    /**
     * <code>required string content = 5;</code>
     *
     * <pre>
     *内容 
     * </pre>
     */
    public boolean hasContent() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string content = 5;</code>
     *
     * <pre>
     *内容 
     * </pre>
     */
    public String getContent() {
      Object ref = content_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          content_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string content = 5;</code>
     *
     * <pre>
     *内容 
     * </pre>
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      Object ref = content_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int64 timeStamp = 6;
    public static final int TIMESTAMP_FIELD_NUMBER = 6;
    private long timeStamp_;
    /**
     * <code>optional int64 timeStamp = 6;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    public boolean hasTimeStamp() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int64 timeStamp = 6;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    public long getTimeStamp() {
      return timeStamp_;
    }

    private void initFields() {
      nid_ = 0;
      sender_ = "";
      owner_ = "";
      title_ = "";
      content_ = "";
      timeStamp_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasContent()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, nid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getSenderBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getOwnerBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getTitleBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getContentBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt64(6, timeStamp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, nid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getSenderBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getOwnerBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getTitleBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getContentBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, timeStamp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static Notice parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Notice parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Notice parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Notice parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Notice parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static Notice parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static Notice parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static Notice parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static Notice parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static Notice parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(Notice prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Notice}
     *
     * <pre>
     *公告1111
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements NoticeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return NoticeData.internal_static_protocol_Notice_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return NoticeData.internal_static_protocol_Notice_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Notice.class, Builder.class);
      }

      // Construct using protocol.NoticeData.Notice.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        nid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        sender_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        owner_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        title_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        content_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        timeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return NoticeData.internal_static_protocol_Notice_descriptor;
      }

      public Notice getDefaultInstanceForType() {
        return Notice.getDefaultInstance();
      }

      public Notice build() {
        Notice result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public Notice buildPartial() {
        Notice result = new Notice(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nid_ = nid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.sender_ = sender_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.owner_ = owner_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.title_ = title_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.content_ = content_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.timeStamp_ = timeStamp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Notice) {
          return mergeFrom((Notice)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Notice other) {
        if (other == Notice.getDefaultInstance()) return this;
        if (other.hasNid()) {
          setNid(other.getNid());
        }
        if (other.hasSender()) {
          bitField0_ |= 0x00000002;
          sender_ = other.sender_;
          onChanged();
        }
        if (other.hasOwner()) {
          bitField0_ |= 0x00000004;
          owner_ = other.owner_;
          onChanged();
        }
        if (other.hasTitle()) {
          bitField0_ |= 0x00000008;
          title_ = other.title_;
          onChanged();
        }
        if (other.hasContent()) {
          bitField0_ |= 0x00000010;
          content_ = other.content_;
          onChanged();
        }
        if (other.hasTimeStamp()) {
          setTimeStamp(other.getTimeStamp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNid()) {
          
          return false;
        }
        if (!hasContent()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        Notice parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (Notice) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 nid = 1;
      private int nid_ ;
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告id
       * </pre>
       */
      public boolean hasNid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告id
       * </pre>
       */
      public int getNid() {
        return nid_;
      }
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告id
       * </pre>
       */
      public Builder setNid(int value) {
        bitField0_ |= 0x00000001;
        nid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告id
       * </pre>
       */
      public Builder clearNid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nid_ = 0;
        onChanged();
        return this;
      }

      // optional string sender = 2;
      private Object sender_ = "";
      /**
       * <code>optional string sender = 2;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public boolean hasSender() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional string sender = 2;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public String getSender() {
        Object ref = sender_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          sender_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>optional string sender = 2;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSenderBytes() {
        Object ref = sender_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          sender_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sender = 2;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public Builder setSender(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        sender_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sender = 2;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public Builder clearSender() {
        bitField0_ = (bitField0_ & ~0x00000002);
        sender_ = getDefaultInstance().getSender();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sender = 2;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public Builder setSenderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        sender_ = value;
        onChanged();
        return this;
      }

      // optional string owner = 3;
      private Object owner_ = "";
      /**
       * <code>optional string owner = 3;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public boolean hasOwner() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string owner = 3;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public String getOwner() {
        Object ref = owner_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          owner_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>optional string owner = 3;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public com.google.protobuf.ByteString
          getOwnerBytes() {
        Object ref = owner_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          owner_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string owner = 3;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public Builder setOwner(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        owner_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string owner = 3;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public Builder clearOwner() {
        bitField0_ = (bitField0_ & ~0x00000004);
        owner_ = getDefaultInstance().getOwner();
        onChanged();
        return this;
      }
      /**
       * <code>optional string owner = 3;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public Builder setOwnerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        owner_ = value;
        onChanged();
        return this;
      }

      // optional string title = 4;
      private Object title_ = "";
      /**
       * <code>optional string title = 4;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string title = 4;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public String getTitle() {
        Object ref = title_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>optional string title = 4;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string title = 4;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public Builder setTitle(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 4;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000008);
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 4;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        title_ = value;
        onChanged();
        return this;
      }

      // required string content = 5;
      private Object content_ = "";
      /**
       * <code>required string content = 5;</code>
       *
       * <pre>
       *内容 
       * </pre>
       */
      public boolean hasContent() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string content = 5;</code>
       *
       * <pre>
       *内容 
       * </pre>
       */
      public String getContent() {
        Object ref = content_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>required string content = 5;</code>
       *
       * <pre>
       *内容 
       * </pre>
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string content = 5;</code>
       *
       * <pre>
       *内容 
       * </pre>
       */
      public Builder setContent(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string content = 5;</code>
       *
       * <pre>
       *内容 
       * </pre>
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x00000010);
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <code>required string content = 5;</code>
       *
       * <pre>
       *内容 
       * </pre>
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        content_ = value;
        onChanged();
        return this;
      }

      // optional int64 timeStamp = 6;
      private long timeStamp_ ;
      /**
       * <code>optional int64 timeStamp = 6;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public boolean hasTimeStamp() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int64 timeStamp = 6;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public long getTimeStamp() {
        return timeStamp_;
      }
      /**
       * <code>optional int64 timeStamp = 6;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public Builder setTimeStamp(long value) {
        bitField0_ |= 0x00000020;
        timeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 timeStamp = 6;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public Builder clearTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        timeStamp_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Notice)
    }

    static {
      defaultInstance = new Notice(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Notice)
  }

  public interface RequestOperateNoticeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 nid = 1;
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告唯一id		
     * </pre>
     */
    boolean hasNid();
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告唯一id		
     * </pre>
     */
    int getNid();
  }
  /**
   * Protobuf type {@code protocol.RequestOperateNotice}
   */
  public static final class RequestOperateNotice extends
      com.google.protobuf.GeneratedMessage
      implements RequestOperateNoticeOrBuilder {
    // Use RequestOperateNotice.newBuilder() to construct.
    private RequestOperateNotice(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestOperateNotice(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestOperateNotice defaultInstance;
    public static RequestOperateNotice getDefaultInstance() {
      return defaultInstance;
    }

    public RequestOperateNotice getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestOperateNotice(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              nid_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return NoticeData.internal_static_protocol_RequestOperateNotice_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return NoticeData.internal_static_protocol_RequestOperateNotice_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RequestOperateNotice.class, Builder.class);
    }

    public static com.google.protobuf.Parser<RequestOperateNotice> PARSER =
        new com.google.protobuf.AbstractParser<RequestOperateNotice>() {
      public RequestOperateNotice parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestOperateNotice(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<RequestOperateNotice> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 nid = 1;
    public static final int NID_FIELD_NUMBER = 1;
    private int nid_;
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告唯一id		
     * </pre>
     */
    public boolean hasNid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 nid = 1;</code>
     *
     * <pre>
     *公告唯一id		
     * </pre>
     */
    public int getNid() {
      return nid_;
    }

    private void initFields() {
      nid_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, nid_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, nid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static RequestOperateNotice parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestOperateNotice parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestOperateNotice parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestOperateNotice parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestOperateNotice parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestOperateNotice parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static RequestOperateNotice parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static RequestOperateNotice parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static RequestOperateNotice parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestOperateNotice parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(RequestOperateNotice prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestOperateNotice}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements RequestOperateNoticeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return NoticeData.internal_static_protocol_RequestOperateNotice_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return NoticeData.internal_static_protocol_RequestOperateNotice_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RequestOperateNotice.class, Builder.class);
      }

      // Construct using protocol.NoticeData.RequestOperateNotice.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        nid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return NoticeData.internal_static_protocol_RequestOperateNotice_descriptor;
      }

      public RequestOperateNotice getDefaultInstanceForType() {
        return RequestOperateNotice.getDefaultInstance();
      }

      public RequestOperateNotice build() {
        RequestOperateNotice result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RequestOperateNotice buildPartial() {
        RequestOperateNotice result = new RequestOperateNotice(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nid_ = nid_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RequestOperateNotice) {
          return mergeFrom((RequestOperateNotice)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RequestOperateNotice other) {
        if (other == RequestOperateNotice.getDefaultInstance()) return this;
        if (other.hasNid()) {
          setNid(other.getNid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RequestOperateNotice parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RequestOperateNotice) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 nid = 1;
      private int nid_ ;
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告唯一id		
       * </pre>
       */
      public boolean hasNid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告唯一id		
       * </pre>
       */
      public int getNid() {
        return nid_;
      }
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告唯一id		
       * </pre>
       */
      public Builder setNid(int value) {
        bitField0_ |= 0x00000001;
        nid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 nid = 1;</code>
       *
       * <pre>
       *公告唯一id		
       * </pre>
       */
      public Builder clearNid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nid_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestOperateNotice)
    }

    static {
      defaultInstance = new RequestOperateNotice(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestOperateNotice)
  }

  public interface ReportNewNoticeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.Notice newNotice = 1;
    /**
     * <code>required .protocol.Notice newNotice = 1;</code>
     */
    boolean hasNewNotice();
    /**
     * <code>required .protocol.Notice newNotice = 1;</code>
     */
    Notice getNewNotice();
    /**
     * <code>required .protocol.Notice newNotice = 1;</code>
     */
    NoticeOrBuilder getNewNoticeOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ReportNewNotice}
   *
   * <pre>
   *通知新公告2222
   * </pre>
   */
  public static final class ReportNewNotice extends
      com.google.protobuf.GeneratedMessage
      implements ReportNewNoticeOrBuilder {
    // Use ReportNewNotice.newBuilder() to construct.
    private ReportNewNotice(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ReportNewNotice(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ReportNewNotice defaultInstance;
    public static ReportNewNotice getDefaultInstance() {
      return defaultInstance;
    }

    public ReportNewNotice getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ReportNewNotice(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              Notice.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = newNotice_.toBuilder();
              }
              newNotice_ = input.readMessage(Notice.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newNotice_);
                newNotice_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return NoticeData.internal_static_protocol_ReportNewNotice_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return NoticeData.internal_static_protocol_ReportNewNotice_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ReportNewNotice.class, Builder.class);
    }

    public static com.google.protobuf.Parser<ReportNewNotice> PARSER =
        new com.google.protobuf.AbstractParser<ReportNewNotice>() {
      public ReportNewNotice parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReportNewNotice(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<ReportNewNotice> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.Notice newNotice = 1;
    public static final int NEWNOTICE_FIELD_NUMBER = 1;
    private Notice newNotice_;
    /**
     * <code>required .protocol.Notice newNotice = 1;</code>
     */
    public boolean hasNewNotice() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.Notice newNotice = 1;</code>
     */
    public Notice getNewNotice() {
      return newNotice_;
    }
    /**
     * <code>required .protocol.Notice newNotice = 1;</code>
     */
    public NoticeOrBuilder getNewNoticeOrBuilder() {
      return newNotice_;
    }

    private void initFields() {
      newNotice_ = Notice.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNewNotice()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getNewNotice().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, newNotice_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, newNotice_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ReportNewNotice parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReportNewNotice parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReportNewNotice parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReportNewNotice parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReportNewNotice parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ReportNewNotice parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ReportNewNotice parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ReportNewNotice parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ReportNewNotice parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ReportNewNotice parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ReportNewNotice prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ReportNewNotice}
     *
     * <pre>
     *通知新公告2222
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ReportNewNoticeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return NoticeData.internal_static_protocol_ReportNewNotice_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return NoticeData.internal_static_protocol_ReportNewNotice_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ReportNewNotice.class, Builder.class);
      }

      // Construct using protocol.NoticeData.ReportNewNotice.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getNewNoticeFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (newNoticeBuilder_ == null) {
          newNotice_ = Notice.getDefaultInstance();
        } else {
          newNoticeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return NoticeData.internal_static_protocol_ReportNewNotice_descriptor;
      }

      public ReportNewNotice getDefaultInstanceForType() {
        return ReportNewNotice.getDefaultInstance();
      }

      public ReportNewNotice build() {
        ReportNewNotice result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ReportNewNotice buildPartial() {
        ReportNewNotice result = new ReportNewNotice(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (newNoticeBuilder_ == null) {
          result.newNotice_ = newNotice_;
        } else {
          result.newNotice_ = newNoticeBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ReportNewNotice) {
          return mergeFrom((ReportNewNotice)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ReportNewNotice other) {
        if (other == ReportNewNotice.getDefaultInstance()) return this;
        if (other.hasNewNotice()) {
          mergeNewNotice(other.getNewNotice());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNewNotice()) {
          
          return false;
        }
        if (!getNewNotice().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ReportNewNotice parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ReportNewNotice) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.Notice newNotice = 1;
      private Notice newNotice_ = Notice.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          Notice, Notice.Builder, NoticeOrBuilder> newNoticeBuilder_;
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public boolean hasNewNotice() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public Notice getNewNotice() {
        if (newNoticeBuilder_ == null) {
          return newNotice_;
        } else {
          return newNoticeBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public Builder setNewNotice(Notice value) {
        if (newNoticeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newNotice_ = value;
          onChanged();
        } else {
          newNoticeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public Builder setNewNotice(
          Notice.Builder builderForValue) {
        if (newNoticeBuilder_ == null) {
          newNotice_ = builderForValue.build();
          onChanged();
        } else {
          newNoticeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public Builder mergeNewNotice(Notice value) {
        if (newNoticeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              newNotice_ != Notice.getDefaultInstance()) {
            newNotice_ =
              Notice.newBuilder(newNotice_).mergeFrom(value).buildPartial();
          } else {
            newNotice_ = value;
          }
          onChanged();
        } else {
          newNoticeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public Builder clearNewNotice() {
        if (newNoticeBuilder_ == null) {
          newNotice_ = Notice.getDefaultInstance();
          onChanged();
        } else {
          newNoticeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public Notice.Builder getNewNoticeBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNewNoticeFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      public NoticeOrBuilder getNewNoticeOrBuilder() {
        if (newNoticeBuilder_ != null) {
          return newNoticeBuilder_.getMessageOrBuilder();
        } else {
          return newNotice_;
        }
      }
      /**
       * <code>required .protocol.Notice newNotice = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          Notice, Notice.Builder, NoticeOrBuilder>
          getNewNoticeFieldBuilder() {
        if (newNoticeBuilder_ == null) {
          newNoticeBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              Notice, Notice.Builder, NoticeOrBuilder>(
                  newNotice_,
                  getParentForChildren(),
                  isClean());
          newNotice_ = null;
        }
        return newNoticeBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ReportNewNotice)
    }

    static {
      defaultInstance = new ReportNewNotice(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ReportNewNotice)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Notice_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Notice_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestOperateNotice_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestOperateNotice_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ReportNewNotice_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ReportNewNotice_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\014notice.proto\022\010protocol\"g\n\006Notice\022\013\n\003ni" +
      "d\030\001 \002(\005\022\016\n\006sender\030\002 \001(\t\022\r\n\005owner\030\003 \001(\t\022\r" +
      "\n\005title\030\004 \001(\t\022\017\n\007content\030\005 \002(\t\022\021\n\ttimeSt" +
      "amp\030\006 \001(\003\"#\n\024RequestOperateNotice\022\013\n\003nid" +
      "\030\001 \002(\005\"6\n\017ReportNewNotice\022#\n\tnewNotice\030\001" +
      " \002(\0132\020.protocol.NoticeB\014B\nNoticeData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_Notice_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_Notice_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Notice_descriptor,
              new String[] { "Nid", "Sender", "Owner", "Title", "Content", "TimeStamp", });
          internal_static_protocol_RequestOperateNotice_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestOperateNotice_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestOperateNotice_descriptor,
              new String[] { "Nid", });
          internal_static_protocol_ReportNewNotice_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ReportNewNotice_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ReportNewNotice_descriptor,
              new String[] { "NewNotice", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
