package common;

import java.util.Objects;

@ExcelConfigObject(key = "characterXp")
public class RoleExpConfig {
    @ExcelColumn(name = "masterlv")
    private int masterLv;
    @ExcelColumn(name = "tolevelneedexp")
    private int toLevelNeedExp;
    @ExcelColumn(name = "actionlimt")
    private int actionLimte;
    @ExcelColumn(name = "costlimt")
    private int costLimte;

    public int getMasterLv() {
        return masterLv;
    }

    public void setMasterLv(int masterLv) {
        this.masterLv = masterLv;
    }

    public int getToLevelNeedExp() {
        return toLevelNeedExp;
    }

    public void setToLevelNeedExp(int toLevelNeedExp) {
        this.toLevelNeedExp = toLevelNeedExp;
    }

    public int getActionLimte() {
        return actionLimte;
    }

    public void setActionLimte(int actionLimte) {
        this.actionLimte = actionLimte;
    }

    public int getCostLimte() {
        return costLimte;
    }

    public void setCostLimte(int costLimte) {
        this.costLimte = costLimte;
    }

    @Override
    public String toString() {
        return "RoleExpConfig{" +
                "masterLv=" + masterLv +
                ", toLevelNeedExp=" + toLevelNeedExp +
                ", actionLimte=" + actionLimte +
                ", costLimte=" + costLimte +
                '}';
        }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RoleExpConfig that = (RoleExpConfig) o;
        return getMasterLv() == that.getMasterLv() &&
                getToLevelNeedExp() == that.getToLevelNeedExp() &&
                getActionLimte() == that.getActionLimte() &&
                getCostLimte() == that.getCostLimte();
    }

    @Override
    public int hashCode() {

        return Objects.hash(getMasterLv(), getToLevelNeedExp(), getActionLimte(), getCostLimte());
    }
}
