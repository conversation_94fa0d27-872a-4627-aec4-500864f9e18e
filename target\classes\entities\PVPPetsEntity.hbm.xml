<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.PVPPetsEntity" table="productrecord" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <id name="roleUid" column="roleUid"/>
        <id name="pet1" column="pet1"/>
        <id name="pet2" column="pet2"/>
        <id name="pet3" column="pet3"/>
        <id name="pet4" column="pet4"/>
        <id name="pet5" column="pet5"/>
    </class>
</hibernate-mapping>