package entities;

import protocol.PetData;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Objects;


@Entity
@Table(name = "pvp_pets", schema = "", catalog = "super_star_fruit")
public class PVPPetsEntity {
    private int id;
    private String roleUid;
    private String pet1;
    private String pet2;
    private String pet3;
    private String pet4;
    private String pet5;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "roleUid")
    public String getRoleUid() {
        return roleUid;
    }

    public void setRoleUid(String roleUid) {
        this.roleUid = roleUid;
    }

    @Basic
    @Column(name = "pet1")
    public String getPet1() {
        return pet1;
    }

    public void setPet1(String pet1) {
        this.pet1 = pet1;
    }

    @Basic
    @Column(name = "pet2")
    public String getPet2() {
        return pet2;
    }

    public void setPet2(String pet2) {
        this.pet2 = pet2;
    }

    @Basic
    @Column(name = "pet3")
    public String getPet3() {
        return pet3;
    }

    public void setPet3(String pet3) {
        this.pet3 = pet3;
    }

    @Basic
    @Column(name = "pet4")
    public String getPet4() {
        return pet4;
    }

    public void setPet4(String pet4) {
        this.pet4 = pet4;
    }

    @Basic
    @Column(name = "pet5")
    public String getPet5() {
        return pet5;
    }

    public void setPet5(String pet5) {
        this.pet5 = pet5;
    }

    @Override
    public String toString() {
        return "PVPPetsEntity{" +
                "id=" + id +
                ", userUid=" + roleUid +
                ", pet1='" + pet1 + '\'' +
                ", pet2='" + pet2 + '\'' +
                ", pet3='" + pet3 + '\'' +
                ", pet4='" + pet4 + '\'' +
                ", pet5='" + pet5 + '\'' +
                '}';
    }
}
