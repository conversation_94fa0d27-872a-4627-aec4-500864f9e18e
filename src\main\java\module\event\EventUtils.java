package module.event;

import entities.EventEntity;
import manager.Redis;
import manager.TimerHandler;
import module.robot.ranName;
import protocol.EventData;

import java.util.*;
import java.util.List;

public class EventUtils {
    private static List<String> eventPool;
    private static Map<Integer, Integer> idMap;

    static {
        eventPool = new ArrayList<String>();
        idMap = new HashMap<Integer, Integer>();
        Set<String> eventConfigSet = Redis.keys("mapEventconfig*");
        Iterator<String> iterator = eventConfigSet.iterator();
        Redis jedis = Redis.getInstance();
        while (iterator.hasNext()) {
            Map<String, String> configMap = jedis.hgetAll(iterator.next());
            int mapId = Integer.parseInt(configMap.get("id"));
            idMap.put(eventPool.size(), mapId);
            eventPool.add("1");
        }
    }

    public static EventData.EventInfo objectToPb(EventEntity entity) {
        EventData.EventInfo.Builder builder = EventData.EventInfo.newBuilder();
        builder.setEventType(entity.getType());
        builder.setIsFinished(entity.isFinished());
        builder.setMapId(entity.getMapId());
        if (builder.getIsFinished()) {
            int countdown = (int) ((entity.getCountdown() - TimerHandler.nowTimeStamp) / 1000);
            builder.setCountdown(countdown < 0 ? 0 : countdown);
        } else {
            builder.setEventAdvance(entity.getAdvance());
        }
        return builder.build();
    }

    public static EventEntity createEvent(String uid, boolean isFirst) {
        int index = ranName.ranGift(eventPool);
        int mapId = idMap.get(index);
        Map<String, String> mapIdConfig = Redis.getExcelMap("mapEventconfig", mapId);
        int mapType = createMapType(mapIdConfig.get("trigger"), mapIdConfig.get("probability"), isFirst);
        EventEntity eventEntity = null;
        if (mapType != -1) {
            eventEntity = new EventEntity();
            eventEntity.setAdvance(0);
            eventEntity.setFinished(false);
            eventEntity.setMapId(Integer.parseInt(mapIdConfig.get("map_id")));
            eventEntity.setCountdown(-1);
            eventEntity.setUid(uid);
            eventEntity.setEventId(UUID.randomUUID().hashCode());
            eventEntity.setType(mapType);

        }
        return eventEntity;
    }

    private static int createMapType(String trigger, String probability, boolean isFirst) {
        int mapType = 0;
        int totalProbability = 100;
        List<String> probabilityPool = new ArrayList<String>();
        String[] probabilityArray = probability.split(",");
        for (int i = 0; i < probabilityArray.length; i++) {
            probabilityPool.add(probabilityArray[i]);
            totalProbability -= Integer.parseInt(probabilityArray[i]);
        }

        if (!isFirst) {
            probabilityPool.add(totalProbability + "");
        }
        int index = ranName.ranGift(probabilityPool);
        String[] typeArray = trigger.split(",");
        ///     /// System.err.println(index+"index"+trigger+"trigger");
        mapType = Integer.parseInt(typeArray[mapType]);

        return mapType;
    }
}
