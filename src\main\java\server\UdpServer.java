package server;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.CharsetUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MissionData;

/**
 * Created by nara on 2018/5/23.
 */
public class UdpServer {
    private static Logger logger = LoggerFactory.getLogger(UdpServer.class);
    private static int port = 8567;

    /**
     * udp服务端，接受客户端发送的广播
     */
    public static void initServer() {
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(group)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .handler(new UdpServerHandler());
            Channel channel = bootstrap.bind(port).sync().channel();
            channel.closeFuture().await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } finally {
            group.shutdownGracefully();
        }
    }

    private static class UdpServerHandler extends SimpleChannelInboundHandler<DatagramPacket> {
        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket msg) throws Exception {
            // 因为Netty对UDP进行了封装，所以接收到的是DatagramPacket对象。
//            String req = msg.content().toString(CharsetUtil.UTF_8);
//            /// System.out.println(req);

//            if ("hello!!!".equals(req)) {
//                ctx.writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer(
//                        "结果：", CharsetUtil.UTF_8), msg.sender()));
//            }

            SuperProtocol superProtocol = decoder(msg.content());
            MissionData.SRequestEnterMission sRequestEnterMission = MissionData.SRequestEnterMission.parseFrom(superProtocol.getContent());
            /// System.out.println(superProtocol.getMsgId());

            ctx.writeAndFlush(new DatagramPacket(Unpooled.copiedBuffer("结果：", CharsetUtil.UTF_8), msg.sender()));
        }

        private SuperProtocol decoder(ByteBuf buffer){
            SuperProtocol protocol  = null;
            int BASE_LENGTH = 9;
            if (buffer.readableBytes() >= BASE_LENGTH) {
                if (buffer.readableBytes() > 2048) {
                    buffer.skipBytes(buffer.readableBytes());
                }
                int beginReader;
                while (true) {
                    beginReader = buffer.readerIndex();
                    buffer.markReaderIndex();
                    if (buffer.readByte() == 0X76) {
                        break;
                    }
                    buffer.resetReaderIndex();
                    buffer.readByte();
                    if (buffer.readableBytes() < BASE_LENGTH) {
                        return null;
                    }
                }
                int msgId = buffer.readInt();
                int length = buffer.readInt();
                if (buffer.readableBytes() < length) {
                    buffer.readerIndex(beginReader);
                    return null;
                }
                byte[] data = new byte[length];
                buffer.readBytes(data);
                protocol = new SuperProtocol(msgId,data.length, data);
            }
            return protocol;
        }
    }
}
