package model;

import java.util.List;

/**
 * Created by nara on 2017/12/22.
 */
public class RoleDressInfo {
    private String uid;
    private int id;
    private int cupboardNum;
    private List<UtilityInfo> dressList;
    private CupboardInfo lastDress;
    private List<CupboardInfo> cupboardList;

    public int getCupboardNum() {
        return cupboardNum;
    }

    public void setCupboardNum(int cupboardNum) {
        this.cupboardNum = cupboardNum;
    }

    public List<CupboardInfo> getCupboardList() {
        return cupboardList;
    }

    public void setCupboardList(List<CupboardInfo> cupboardList) {
        this.cupboardList = cupboardList;
    }

    public List<UtilityInfo> getDressList() {
        return dressList;
    }

    public CupboardInfo getLastDress() {
        return lastDress;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setDressList(List<UtilityInfo> dresList) {
        this.dressList = dresList;
    }

    public void setLastDress(CupboardInfo lastDress) {
        this.lastDress = lastDress;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}