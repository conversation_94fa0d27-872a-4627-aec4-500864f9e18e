#log4j.rootLogger=CONSOLE,FILE
log4j.rootLogger=DEBUG,CONSOLE,FILE,A1
log4j.addivity.org.apache=true
log4j.logger.org.hibernate=INFO;

#�Զ���
log4j.logger.syncLog=info,syncLog
log4j.appender.syncLog=org.apache.log4j.FileAppender
log4j.appender.syncLog.File=/home/<USER>/logs/sync.log
log4j.appender.syncLog.Append=true
log4j.appender.syncLog.Threshold=info
log4j.appender.syncLog.layout=org.apache.log4j.PatternLayout
log4j.appender.syncLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.syncLog = false

log4j.logger.sqlLog=info,sqlLog
log4j.appender.sqlLog=org.apache.log4j.FileAppender
log4j.appender.sqlLog.File=/home/<USER>/logs/sql.log
log4j.appender.sqlLog.Append=true
log4j.appender.sqlLog.Threshold=info
log4j.appender.sqlLog.layout=org.apache.log4j.PatternLayout
log4j.appender.sqlLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.sqlLog = false

log4j.logger.taskLog=info,taskLog
log4j.appender.taskLog=org.apache.log4j.FileAppender
log4j.appender.taskLog.File=/home/<USER>/logs/task.log
log4j.appender.taskLog.Append=true
log4j.appender.taskLog.Threshold=info
log4j.appender.taskLog.layout=org.apache.log4j.PatternLayout
log4j.appender.taskLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.taskLog = false

log4j.logger.roleInfoLog=info,roleInfoLog
log4j.appender.roleInfoLog=org.apache.log4j.FileAppender
log4j.appender.roleInfoLog.File=/home/<USER>/logs/roleInfo.log
log4j.appender.roleInfoLog.Append=true
log4j.appender.roleInfoLog.Threshold=info
log4j.appender.roleInfoLog.layout=org.apache.log4j.PatternLayout
log4j.appender.roleInfoLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.userInfoLog = false

log4j.logger.itemLog=info,itemLog
log4j.appender.itemLog=org.apache.log4j.FileAppender
log4j.appender.itemLog.File=/home/<USER>/logs/item.log
log4j.appender.itemLog.Append=true
log4j.appender.itemLog.Threshold=info
log4j.appender.itemLog.layout=org.apache.log4j.PatternLayout
log4j.appender.itemLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.pveFightLog = false

log4j.logger.friendLog=info,friendLog
log4j.appender.friendLog=org.apache.log4j.FileAppender
log4j.appender.friendLog.File=/home/<USER>/logs/friend.log
log4j.appender.friendLog.Append=true
log4j.appender.friendLog.Threshold=info
log4j.appender.friendLog.layout=org.apache.log4j.PatternLayout
log4j.appender.friendLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.pvpFightLog = false

log4j.logger.missionLog=info,missionLog
log4j.appender.missionLog=org.apache.log4j.FileAppender
log4j.appender.missionLog.File=/home/<USER>/logs/mission.log
log4j.appender.missionLog.Append=true
log4j.appender.missionLog.Threshold=info
log4j.appender.missionLog.layout=org.apache.log4j.PatternLayout
log4j.appender.missionLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.teamLog = false

log4j.logger.mailLog=info,mailLog
log4j.appender.mailLog=org.apache.log4j.FileAppender
log4j.appender.mailLog.File=/home/<USER>/logs/mail.log
log4j.appender.mailLog.Append=true
log4j.appender.mailLog.Threshold=info
log4j.appender.mailLog.layout=org.apache.log4j.PatternLayout
log4j.appender.mailLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.mailLog = false

log4j.logger.payLog=info,payLog
log4j.appender.payLog=org.apache.log4j.FileAppender
log4j.appender.payLog.File=/home/<USER>/logs/pay.log
log4j.appender.payLog.Append=true
log4j.appender.payLog.Threshold=info
log4j.appender.payLog.layout=org.apache.log4j.PatternLayout
log4j.appender.payLog.layout.ConversionPattern==%d [%t] %p [%c] - %m%n
log4j.additivity.weiXinPayLog = false


# ÿ���½����а���־
#log4j.logger.rankLog=info,rankLog
#log4j.appender.rankLog=org.apache.log4j.DailyRollingFileAppender
#log4j.appender.rankLog.File=C:/Java/log4j/superstar/rank/rank.log
#log4j.appender.rankLog.Encoding=GBK
#log4j.appender.rankLog.Threshold=DEBUG
#log4j.appender.rankLog.DatePattern='.'yyyy-MM-dd
#log4j.appender.rankLog.layout=org.apache.log4j.PatternLayout
#log4j.appender.rankLog.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}:%L : %m%n


# Ӧ���ڿ���̨
log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender
log4j.appender.CONSOLE.Threshold=INFO
log4j.appender.CONSOLE.Target=System.out
log4j.appender.CONSOLE.Encoding=GBK
log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout
log4j.appender.CONSOLE.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n

# ÿ���½���־
log4j.appender.A1=org.apache.log4j.DailyRollingFileAppender
log4j.appender.A1.File=/home/<USER>/logs/daily.log
log4j.appender.A1.Encoding=GBK
log4j.appender.A1.Threshold=DEBUG
log4j.appender.A1.DatePattern='.'yyyy-MM-dd
log4j.appender.A1.layout=org.apache.log4j.PatternLayout
log4j.appender.A1.layout.ConversionPattern=%d{ABSOLUTE} %5p %c{1}:%L : %m%n

#Ӧ�����ļ�
log4j.appender.FILE=org.apache.log4j.FileAppender
log4j.appender.FILE.File=/home/<USER>/logs/superstar.log
log4j.appender.FILE.Append=true
log4j.appender.FILE.Encoding=GBK
log4j.appender.FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.FILE.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n

# Ӧ�����ļ��ع�
log4j.appender.ROLLING_FILE=org.apache.log4j.RollingFileAppender
log4j.appender.ROLLING_FILE.Threshold=ERROR
log4j.appender.ROLLING_FILE.File=rolling.log
log4j.appender.ROLLING_FILE.Append=true
log4j.appender.CONSOLE_FILE.Encoding=GBK
log4j.appender.ROLLING_FILE.MaxFileSize=10KB
log4j.appender.ROLLING_FILE.MaxBackupIndex=1
log4j.appender.ROLLING_FILE.layout=org.apache.log4j.PatternLayout
log4j.appender.ROLLING_FILE.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n

#�Զ���Appender
log4j.appender.im = net.cybercorlin.util.logger.appender.IMAppender
log4j.appender.im.host = mail.cybercorlin.net
log4j.appender.im.username = username
log4j.appender.im.password = password
log4j.appender.im.recipient = <EMAIL>
log4j.appender.im.layout=org.apache.log4j.PatternLayout
log4j.appender.im.layout.ConversionPattern =[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n

#Ӧ����socket
log4j.appender.SOCKET=org.apache.log4j.RollingFileAppender
log4j.appender.SOCKET.RemoteHost=localhost
log4j.appender.SOCKET.Port=5001
log4j.appender.SOCKET.LocationInfo=true
# Set up for Log Facter 5
log4j.appender.SOCKET.layout=org.apache.log4j.PatternLayout
log4j.appender.SOCET.layout.ConversionPattern=[start]%d{DATE}[DATE]%n%p[PRIORITY]%n%x[NDC]%n%t[THREAD]%n%c[CATEGORY]%n%m[MESSAGE]%n%n
# Log Factor 5 Appender
log4j.appender.LF5_APPENDER=org.apache.log4j.lf5.LF5Appender
log4j.appender.LF5_APPENDER.MaxNumberOfRecords=2000

# ������־���ʼ�
#log4j.appender.MAIL=org.apache.log4j.net.SMTPAppender
#log4j.appender.MAIL.Threshold=FATAL
#log4j.appender.MAIL.BufferSize=10
#log4j.appender.MAIL.From=<EMAIL>
#log4j.appender.MAIL.SMTPHost=www.wusetu.com
#log4j.appender.MAIL.Subject=Log4J Message
#log4j.appender.MAIL.To=<EMAIL>
#log4j.appender.MAIL.layout=org.apache.log4j.PatternLayout
#log4j.appender.MAIL.layout.ConversionPattern=[framework] %d - %c -%-4r [%t] %-5p %c %x - %m%n