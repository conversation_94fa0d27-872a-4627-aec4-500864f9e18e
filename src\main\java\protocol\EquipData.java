// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: equip.proto

package protocol;

public final class EquipData {
  private EquipData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface EquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 equipEid = 1;
    /**
     * <code>required int32 equipEid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    boolean hasEquipEid();
    /**
     * <code>required int32 equipEid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    int getEquipEid();

    // required int32 equipType = 2;
    /**
     * <code>required int32 equipType = 2;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    boolean hasEquipType();
    /**
     * <code>required int32 equipType = 2;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    int getEquipType();

    // required int32 currentLevel = 3;
    /**
     * <code>required int32 currentLevel = 3;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    boolean hasCurrentLevel();
    /**
     * <code>required int32 currentLevel = 3;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    int getCurrentLevel();

    // required int32 currentExp = 4;
    /**
     * <code>required int32 currentExp = 4;</code>
     *
     * <pre>
     *当前经验
     * </pre>
     */
    boolean hasCurrentExp();
    /**
     * <code>required int32 currentExp = 4;</code>
     *
     * <pre>
     *当前经验
     * </pre>
     */
    int getCurrentExp();

    // required float adFactor = 5;
    /**
     * <code>required float adFactor = 5;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    boolean hasAdFactor();
    /**
     * <code>required float adFactor = 5;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    float getAdFactor();

    // required float apFactor = 6;
    /**
     * <code>required float apFactor = 6;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    boolean hasApFactor();
    /**
     * <code>required float apFactor = 6;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    float getApFactor();

    // required float armFactor = 7;
    /**
     * <code>required float armFactor = 7;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    boolean hasArmFactor();
    /**
     * <code>required float armFactor = 7;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    float getArmFactor();

    // required float mdfFactor = 8;
    /**
     * <code>required float mdfFactor = 8;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    boolean hasMdfFactor();
    /**
     * <code>required float mdfFactor = 8;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    float getMdfFactor();

    // required float speedFactor = 9;
    /**
     * <code>required float speedFactor = 9;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    boolean hasSpeedFactor();
    /**
     * <code>required float speedFactor = 9;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    float getSpeedFactor();

    // required float hpFactor = 10;
    /**
     * <code>required float hpFactor = 10;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    boolean hasHpFactor();
    /**
     * <code>required float hpFactor = 10;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    float getHpFactor();

    // required string name = 11;
    /**
     * <code>required string name = 11;</code>
     *
     * <pre>
     *名称
     * </pre>
     */
    boolean hasName();
    /**
     * <code>required string name = 11;</code>
     *
     * <pre>
     *名称
     * </pre>
     */
    java.lang.String getName();
    /**
     * <code>required string name = 11;</code>
     *
     * <pre>
     *名称
     * </pre>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    // required int32 sort = 12;
    /**
     * <code>required int32 sort = 12;</code>
     */
    boolean hasSort();
    /**
     * <code>required int32 sort = 12;</code>
     */
    int getSort();
  }
  /**
   * Protobuf type {@code protocol.Equip}
   *
   * <pre>
   *装备
   * </pre>
   */
  public static final class Equip extends
      com.google.protobuf.GeneratedMessage
      implements EquipOrBuilder {
    // Use Equip.newBuilder() to construct.
    private Equip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Equip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Equip defaultInstance;
    public static Equip getDefaultInstance() {
      return defaultInstance;
    }

    public Equip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Equip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              equipEid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              equipType_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              currentLevel_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              currentExp_ = input.readInt32();
              break;
            }
            case 45: {
              bitField0_ |= 0x00000010;
              adFactor_ = input.readFloat();
              break;
            }
            case 53: {
              bitField0_ |= 0x00000020;
              apFactor_ = input.readFloat();
              break;
            }
            case 61: {
              bitField0_ |= 0x00000040;
              armFactor_ = input.readFloat();
              break;
            }
            case 69: {
              bitField0_ |= 0x00000080;
              mdfFactor_ = input.readFloat();
              break;
            }
            case 77: {
              bitField0_ |= 0x00000100;
              speedFactor_ = input.readFloat();
              break;
            }
            case 85: {
              bitField0_ |= 0x00000200;
              hpFactor_ = input.readFloat();
              break;
            }
            case 90: {
              bitField0_ |= 0x00000400;
              name_ = input.readBytes();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              sort_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_Equip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_Equip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.Equip.class, protocol.EquipData.Equip.Builder.class);
    }

    public static com.google.protobuf.Parser<Equip> PARSER =
        new com.google.protobuf.AbstractParser<Equip>() {
      public Equip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Equip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Equip> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 equipEid = 1;
    public static final int EQUIPEID_FIELD_NUMBER = 1;
    private int equipEid_;
    /**
     * <code>required int32 equipEid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    public boolean hasEquipEid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 equipEid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    public int getEquipEid() {
      return equipEid_;
    }

    // required int32 equipType = 2;
    public static final int EQUIPTYPE_FIELD_NUMBER = 2;
    private int equipType_;
    /**
     * <code>required int32 equipType = 2;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    public boolean hasEquipType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 equipType = 2;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    public int getEquipType() {
      return equipType_;
    }

    // required int32 currentLevel = 3;
    public static final int CURRENTLEVEL_FIELD_NUMBER = 3;
    private int currentLevel_;
    /**
     * <code>required int32 currentLevel = 3;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    public boolean hasCurrentLevel() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 currentLevel = 3;</code>
     *
     * <pre>
     *当前等级
     * </pre>
     */
    public int getCurrentLevel() {
      return currentLevel_;
    }

    // required int32 currentExp = 4;
    public static final int CURRENTEXP_FIELD_NUMBER = 4;
    private int currentExp_;
    /**
     * <code>required int32 currentExp = 4;</code>
     *
     * <pre>
     *当前经验
     * </pre>
     */
    public boolean hasCurrentExp() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 currentExp = 4;</code>
     *
     * <pre>
     *当前经验
     * </pre>
     */
    public int getCurrentExp() {
      return currentExp_;
    }

    // required float adFactor = 5;
    public static final int ADFACTOR_FIELD_NUMBER = 5;
    private float adFactor_;
    /**
     * <code>required float adFactor = 5;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    public boolean hasAdFactor() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required float adFactor = 5;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    public float getAdFactor() {
      return adFactor_;
    }

    // required float apFactor = 6;
    public static final int APFACTOR_FIELD_NUMBER = 6;
    private float apFactor_;
    /**
     * <code>required float apFactor = 6;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    public boolean hasApFactor() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required float apFactor = 6;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    public float getApFactor() {
      return apFactor_;
    }

    // required float armFactor = 7;
    public static final int ARMFACTOR_FIELD_NUMBER = 7;
    private float armFactor_;
    /**
     * <code>required float armFactor = 7;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    public boolean hasArmFactor() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required float armFactor = 7;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    public float getArmFactor() {
      return armFactor_;
    }

    // required float mdfFactor = 8;
    public static final int MDFFACTOR_FIELD_NUMBER = 8;
    private float mdfFactor_;
    /**
     * <code>required float mdfFactor = 8;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    public boolean hasMdfFactor() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>required float mdfFactor = 8;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    public float getMdfFactor() {
      return mdfFactor_;
    }

    // required float speedFactor = 9;
    public static final int SPEEDFACTOR_FIELD_NUMBER = 9;
    private float speedFactor_;
    /**
     * <code>required float speedFactor = 9;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    public boolean hasSpeedFactor() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>required float speedFactor = 9;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    public float getSpeedFactor() {
      return speedFactor_;
    }

    // required float hpFactor = 10;
    public static final int HPFACTOR_FIELD_NUMBER = 10;
    private float hpFactor_;
    /**
     * <code>required float hpFactor = 10;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    public boolean hasHpFactor() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>required float hpFactor = 10;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    public float getHpFactor() {
      return hpFactor_;
    }

    // required string name = 11;
    public static final int NAME_FIELD_NUMBER = 11;
    private java.lang.Object name_;
    /**
     * <code>required string name = 11;</code>
     *
     * <pre>
     *名称
     * </pre>
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>required string name = 11;</code>
     *
     * <pre>
     *名称
     * </pre>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string name = 11;</code>
     *
     * <pre>
     *名称
     * </pre>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 sort = 12;
    public static final int SORT_FIELD_NUMBER = 12;
    private int sort_;
    /**
     * <code>required int32 sort = 12;</code>
     */
    public boolean hasSort() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>required int32 sort = 12;</code>
     */
    public int getSort() {
      return sort_;
    }

    private void initFields() {
      equipEid_ = 0;
      equipType_ = 0;
      currentLevel_ = 0;
      currentExp_ = 0;
      adFactor_ = 0F;
      apFactor_ = 0F;
      armFactor_ = 0F;
      mdfFactor_ = 0F;
      speedFactor_ = 0F;
      hpFactor_ = 0F;
      name_ = "";
      sort_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEquipEid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasEquipType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCurrentLevel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCurrentExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAdFactor()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasApFactor()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasArmFactor()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMdfFactor()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSpeedFactor()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHpFactor()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSort()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, equipEid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, equipType_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, currentLevel_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, currentExp_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeFloat(5, adFactor_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeFloat(6, apFactor_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeFloat(7, armFactor_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeFloat(8, mdfFactor_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeFloat(9, speedFactor_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeFloat(10, hpFactor_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeBytes(11, getNameBytes());
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeInt32(12, sort_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, equipEid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, equipType_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, currentLevel_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, currentExp_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(5, adFactor_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(6, apFactor_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, armFactor_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(8, mdfFactor_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, speedFactor_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(10, hpFactor_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(11, getNameBytes());
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, sort_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.Equip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.Equip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.Equip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.Equip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.Equip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.Equip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.Equip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.Equip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.Equip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.Equip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.Equip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Equip}
     *
     * <pre>
     *装备
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.EquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_Equip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_Equip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.Equip.class, protocol.EquipData.Equip.Builder.class);
      }

      // Construct using protocol.EquipData.Equip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        equipEid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        equipType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        currentLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        currentExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        adFactor_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000010);
        apFactor_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000020);
        armFactor_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000040);
        mdfFactor_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000080);
        speedFactor_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000100);
        hpFactor_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000200);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        sort_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_Equip_descriptor;
      }

      public protocol.EquipData.Equip getDefaultInstanceForType() {
        return protocol.EquipData.Equip.getDefaultInstance();
      }

      public protocol.EquipData.Equip build() {
        protocol.EquipData.Equip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.Equip buildPartial() {
        protocol.EquipData.Equip result = new protocol.EquipData.Equip(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.equipEid_ = equipEid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.equipType_ = equipType_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.currentLevel_ = currentLevel_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.currentExp_ = currentExp_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.adFactor_ = adFactor_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.apFactor_ = apFactor_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.armFactor_ = armFactor_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.mdfFactor_ = mdfFactor_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.speedFactor_ = speedFactor_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.hpFactor_ = hpFactor_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.sort_ = sort_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.Equip) {
          return mergeFrom((protocol.EquipData.Equip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.Equip other) {
        if (other == protocol.EquipData.Equip.getDefaultInstance()) return this;
        if (other.hasEquipEid()) {
          setEquipEid(other.getEquipEid());
        }
        if (other.hasEquipType()) {
          setEquipType(other.getEquipType());
        }
        if (other.hasCurrentLevel()) {
          setCurrentLevel(other.getCurrentLevel());
        }
        if (other.hasCurrentExp()) {
          setCurrentExp(other.getCurrentExp());
        }
        if (other.hasAdFactor()) {
          setAdFactor(other.getAdFactor());
        }
        if (other.hasApFactor()) {
          setApFactor(other.getApFactor());
        }
        if (other.hasArmFactor()) {
          setArmFactor(other.getArmFactor());
        }
        if (other.hasMdfFactor()) {
          setMdfFactor(other.getMdfFactor());
        }
        if (other.hasSpeedFactor()) {
          setSpeedFactor(other.getSpeedFactor());
        }
        if (other.hasHpFactor()) {
          setHpFactor(other.getHpFactor());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000400;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasSort()) {
          setSort(other.getSort());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEquipEid()) {
          
          return false;
        }
        if (!hasEquipType()) {
          
          return false;
        }
        if (!hasCurrentLevel()) {
          
          return false;
        }
        if (!hasCurrentExp()) {
          
          return false;
        }
        if (!hasAdFactor()) {
          
          return false;
        }
        if (!hasApFactor()) {
          
          return false;
        }
        if (!hasArmFactor()) {
          
          return false;
        }
        if (!hasMdfFactor()) {
          
          return false;
        }
        if (!hasSpeedFactor()) {
          
          return false;
        }
        if (!hasHpFactor()) {
          
          return false;
        }
        if (!hasName()) {
          
          return false;
        }
        if (!hasSort()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.Equip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.Equip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 equipEid = 1;
      private int equipEid_ ;
      /**
       * <code>required int32 equipEid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public boolean hasEquipEid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 equipEid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public int getEquipEid() {
        return equipEid_;
      }
      /**
       * <code>required int32 equipEid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public Builder setEquipEid(int value) {
        bitField0_ |= 0x00000001;
        equipEid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 equipEid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public Builder clearEquipEid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        equipEid_ = 0;
        onChanged();
        return this;
      }

      // required int32 equipType = 2;
      private int equipType_ ;
      /**
       * <code>required int32 equipType = 2;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public boolean hasEquipType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 equipType = 2;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public int getEquipType() {
        return equipType_;
      }
      /**
       * <code>required int32 equipType = 2;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public Builder setEquipType(int value) {
        bitField0_ |= 0x00000002;
        equipType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 equipType = 2;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public Builder clearEquipType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        equipType_ = 0;
        onChanged();
        return this;
      }

      // required int32 currentLevel = 3;
      private int currentLevel_ ;
      /**
       * <code>required int32 currentLevel = 3;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public boolean hasCurrentLevel() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 currentLevel = 3;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public int getCurrentLevel() {
        return currentLevel_;
      }
      /**
       * <code>required int32 currentLevel = 3;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public Builder setCurrentLevel(int value) {
        bitField0_ |= 0x00000004;
        currentLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 currentLevel = 3;</code>
       *
       * <pre>
       *当前等级
       * </pre>
       */
      public Builder clearCurrentLevel() {
        bitField0_ = (bitField0_ & ~0x00000004);
        currentLevel_ = 0;
        onChanged();
        return this;
      }

      // required int32 currentExp = 4;
      private int currentExp_ ;
      /**
       * <code>required int32 currentExp = 4;</code>
       *
       * <pre>
       *当前经验
       * </pre>
       */
      public boolean hasCurrentExp() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 currentExp = 4;</code>
       *
       * <pre>
       *当前经验
       * </pre>
       */
      public int getCurrentExp() {
        return currentExp_;
      }
      /**
       * <code>required int32 currentExp = 4;</code>
       *
       * <pre>
       *当前经验
       * </pre>
       */
      public Builder setCurrentExp(int value) {
        bitField0_ |= 0x00000008;
        currentExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 currentExp = 4;</code>
       *
       * <pre>
       *当前经验
       * </pre>
       */
      public Builder clearCurrentExp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        currentExp_ = 0;
        onChanged();
        return this;
      }

      // required float adFactor = 5;
      private float adFactor_ ;
      /**
       * <code>required float adFactor = 5;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public boolean hasAdFactor() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required float adFactor = 5;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public float getAdFactor() {
        return adFactor_;
      }
      /**
       * <code>required float adFactor = 5;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public Builder setAdFactor(float value) {
        bitField0_ |= 0x00000010;
        adFactor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float adFactor = 5;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public Builder clearAdFactor() {
        bitField0_ = (bitField0_ & ~0x00000010);
        adFactor_ = 0F;
        onChanged();
        return this;
      }

      // required float apFactor = 6;
      private float apFactor_ ;
      /**
       * <code>required float apFactor = 6;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public boolean hasApFactor() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required float apFactor = 6;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public float getApFactor() {
        return apFactor_;
      }
      /**
       * <code>required float apFactor = 6;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public Builder setApFactor(float value) {
        bitField0_ |= 0x00000020;
        apFactor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float apFactor = 6;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public Builder clearApFactor() {
        bitField0_ = (bitField0_ & ~0x00000020);
        apFactor_ = 0F;
        onChanged();
        return this;
      }

      // required float armFactor = 7;
      private float armFactor_ ;
      /**
       * <code>required float armFactor = 7;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public boolean hasArmFactor() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required float armFactor = 7;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public float getArmFactor() {
        return armFactor_;
      }
      /**
       * <code>required float armFactor = 7;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public Builder setArmFactor(float value) {
        bitField0_ |= 0x00000040;
        armFactor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float armFactor = 7;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public Builder clearArmFactor() {
        bitField0_ = (bitField0_ & ~0x00000040);
        armFactor_ = 0F;
        onChanged();
        return this;
      }

      // required float mdfFactor = 8;
      private float mdfFactor_ ;
      /**
       * <code>required float mdfFactor = 8;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public boolean hasMdfFactor() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>required float mdfFactor = 8;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public float getMdfFactor() {
        return mdfFactor_;
      }
      /**
       * <code>required float mdfFactor = 8;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public Builder setMdfFactor(float value) {
        bitField0_ |= 0x00000080;
        mdfFactor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float mdfFactor = 8;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public Builder clearMdfFactor() {
        bitField0_ = (bitField0_ & ~0x00000080);
        mdfFactor_ = 0F;
        onChanged();
        return this;
      }

      // required float speedFactor = 9;
      private float speedFactor_ ;
      /**
       * <code>required float speedFactor = 9;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public boolean hasSpeedFactor() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>required float speedFactor = 9;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public float getSpeedFactor() {
        return speedFactor_;
      }
      /**
       * <code>required float speedFactor = 9;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public Builder setSpeedFactor(float value) {
        bitField0_ |= 0x00000100;
        speedFactor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float speedFactor = 9;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public Builder clearSpeedFactor() {
        bitField0_ = (bitField0_ & ~0x00000100);
        speedFactor_ = 0F;
        onChanged();
        return this;
      }

      // required float hpFactor = 10;
      private float hpFactor_ ;
      /**
       * <code>required float hpFactor = 10;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public boolean hasHpFactor() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>required float hpFactor = 10;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public float getHpFactor() {
        return hpFactor_;
      }
      /**
       * <code>required float hpFactor = 10;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public Builder setHpFactor(float value) {
        bitField0_ |= 0x00000200;
        hpFactor_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float hpFactor = 10;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public Builder clearHpFactor() {
        bitField0_ = (bitField0_ & ~0x00000200);
        hpFactor_ = 0F;
        onChanged();
        return this;
      }

      // required string name = 11;
      private java.lang.Object name_ = "";
      /**
       * <code>required string name = 11;</code>
       *
       * <pre>
       *名称
       * </pre>
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>required string name = 11;</code>
       *
       * <pre>
       *名称
       * </pre>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string name = 11;</code>
       *
       * <pre>
       *名称
       * </pre>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string name = 11;</code>
       *
       * <pre>
       *名称
       * </pre>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 11;</code>
       *
       * <pre>
       *名称
       * </pre>
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000400);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 11;</code>
       *
       * <pre>
       *名称
       * </pre>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        name_ = value;
        onChanged();
        return this;
      }

      // required int32 sort = 12;
      private int sort_ ;
      /**
       * <code>required int32 sort = 12;</code>
       */
      public boolean hasSort() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>required int32 sort = 12;</code>
       */
      public int getSort() {
        return sort_;
      }
      /**
       * <code>required int32 sort = 12;</code>
       */
      public Builder setSort(int value) {
        bitField0_ |= 0x00000800;
        sort_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sort = 12;</code>
       */
      public Builder clearSort() {
        bitField0_ = (bitField0_ & ~0x00000800);
        sort_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Equip)
    }

    static {
      defaultInstance = new Equip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Equip)
  }

  public interface RequestGetEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetEquip}
   *
   * <pre>
   *1215
   * </pre>
   */
  public static final class RequestGetEquip extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetEquipOrBuilder {
    // Use RequestGetEquip.newBuilder() to construct.
    private RequestGetEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetEquip defaultInstance;
    public static RequestGetEquip getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_RequestGetEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_RequestGetEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.RequestGetEquip.class, protocol.EquipData.RequestGetEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetEquip> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetEquip>() {
      public RequestGetEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetEquip> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.RequestGetEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.RequestGetEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.RequestGetEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.RequestGetEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.RequestGetEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetEquip}
     *
     * <pre>
     *1215
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.RequestGetEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_RequestGetEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_RequestGetEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.RequestGetEquip.class, protocol.EquipData.RequestGetEquip.Builder.class);
      }

      // Construct using protocol.EquipData.RequestGetEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_RequestGetEquip_descriptor;
      }

      public protocol.EquipData.RequestGetEquip getDefaultInstanceForType() {
        return protocol.EquipData.RequestGetEquip.getDefaultInstance();
      }

      public protocol.EquipData.RequestGetEquip build() {
        protocol.EquipData.RequestGetEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.RequestGetEquip buildPartial() {
        protocol.EquipData.RequestGetEquip result = new protocol.EquipData.RequestGetEquip(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.RequestGetEquip) {
          return mergeFrom((protocol.EquipData.RequestGetEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.RequestGetEquip other) {
        if (other == protocol.EquipData.RequestGetEquip.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.RequestGetEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.RequestGetEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetEquip)
    }

    static {
      defaultInstance = new RequestGetEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetEquip)
  }

  public interface ResponseGetEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // repeated .protocol.Equip equip = 2;
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    java.util.List<protocol.EquipData.Equip> 
        getEquipList();
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    protocol.EquipData.Equip getEquip(int index);
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    int getEquipCount();
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    java.util.List<? extends protocol.EquipData.EquipOrBuilder> 
        getEquipOrBuilderList();
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    protocol.EquipData.EquipOrBuilder getEquipOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseGetEquip}
   *
   * <pre>
   *2215
   * </pre>
   */
  public static final class ResponseGetEquip extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetEquipOrBuilder {
    // Use ResponseGetEquip.newBuilder() to construct.
    private ResponseGetEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetEquip defaultInstance;
    public static ResponseGetEquip getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                equip_ = new java.util.ArrayList<protocol.EquipData.Equip>();
                mutable_bitField0_ |= 0x00000002;
              }
              equip_.add(input.readMessage(protocol.EquipData.Equip.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          equip_ = java.util.Collections.unmodifiableList(equip_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_ResponseGetEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_ResponseGetEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.ResponseGetEquip.class, protocol.EquipData.ResponseGetEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetEquip> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetEquip>() {
      public ResponseGetEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetEquip> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // repeated .protocol.Equip equip = 2;
    public static final int EQUIP_FIELD_NUMBER = 2;
    private java.util.List<protocol.EquipData.Equip> equip_;
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    public java.util.List<protocol.EquipData.Equip> getEquipList() {
      return equip_;
    }
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    public java.util.List<? extends protocol.EquipData.EquipOrBuilder> 
        getEquipOrBuilderList() {
      return equip_;
    }
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    public int getEquipCount() {
      return equip_.size();
    }
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    public protocol.EquipData.Equip getEquip(int index) {
      return equip_.get(index);
    }
    /**
     * <code>repeated .protocol.Equip equip = 2;</code>
     */
    public protocol.EquipData.EquipOrBuilder getEquipOrBuilder(
        int index) {
      return equip_.get(index);
    }

    private void initFields() {
      errorId_ = 0;
      equip_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getEquipCount(); i++) {
        if (!getEquip(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      for (int i = 0; i < equip_.size(); i++) {
        output.writeMessage(2, equip_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      for (int i = 0; i < equip_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, equip_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.ResponseGetEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.ResponseGetEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.ResponseGetEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.ResponseGetEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.ResponseGetEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetEquip}
     *
     * <pre>
     *2215
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.ResponseGetEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_ResponseGetEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_ResponseGetEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.ResponseGetEquip.class, protocol.EquipData.ResponseGetEquip.Builder.class);
      }

      // Construct using protocol.EquipData.ResponseGetEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEquipFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (equipBuilder_ == null) {
          equip_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          equipBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_ResponseGetEquip_descriptor;
      }

      public protocol.EquipData.ResponseGetEquip getDefaultInstanceForType() {
        return protocol.EquipData.ResponseGetEquip.getDefaultInstance();
      }

      public protocol.EquipData.ResponseGetEquip build() {
        protocol.EquipData.ResponseGetEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.ResponseGetEquip buildPartial() {
        protocol.EquipData.ResponseGetEquip result = new protocol.EquipData.ResponseGetEquip(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (equipBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            equip_ = java.util.Collections.unmodifiableList(equip_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.equip_ = equip_;
        } else {
          result.equip_ = equipBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.ResponseGetEquip) {
          return mergeFrom((protocol.EquipData.ResponseGetEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.ResponseGetEquip other) {
        if (other == protocol.EquipData.ResponseGetEquip.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (equipBuilder_ == null) {
          if (!other.equip_.isEmpty()) {
            if (equip_.isEmpty()) {
              equip_ = other.equip_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureEquipIsMutable();
              equip_.addAll(other.equip_);
            }
            onChanged();
          }
        } else {
          if (!other.equip_.isEmpty()) {
            if (equipBuilder_.isEmpty()) {
              equipBuilder_.dispose();
              equipBuilder_ = null;
              equip_ = other.equip_;
              bitField0_ = (bitField0_ & ~0x00000002);
              equipBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getEquipFieldBuilder() : null;
            } else {
              equipBuilder_.addAllMessages(other.equip_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getEquipCount(); i++) {
          if (!getEquip(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.ResponseGetEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.ResponseGetEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Equip equip = 2;
      private java.util.List<protocol.EquipData.Equip> equip_ =
        java.util.Collections.emptyList();
      private void ensureEquipIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          equip_ = new java.util.ArrayList<protocol.EquipData.Equip>(equip_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> equipBuilder_;

      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public java.util.List<protocol.EquipData.Equip> getEquipList() {
        if (equipBuilder_ == null) {
          return java.util.Collections.unmodifiableList(equip_);
        } else {
          return equipBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public int getEquipCount() {
        if (equipBuilder_ == null) {
          return equip_.size();
        } else {
          return equipBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public protocol.EquipData.Equip getEquip(int index) {
        if (equipBuilder_ == null) {
          return equip_.get(index);
        } else {
          return equipBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder setEquip(
          int index, protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipIsMutable();
          equip_.set(index, value);
          onChanged();
        } else {
          equipBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder setEquip(
          int index, protocol.EquipData.Equip.Builder builderForValue) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.set(index, builderForValue.build());
          onChanged();
        } else {
          equipBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder addEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipIsMutable();
          equip_.add(value);
          onChanged();
        } else {
          equipBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder addEquip(
          int index, protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipIsMutable();
          equip_.add(index, value);
          onChanged();
        } else {
          equipBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder addEquip(
          protocol.EquipData.Equip.Builder builderForValue) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.add(builderForValue.build());
          onChanged();
        } else {
          equipBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder addEquip(
          int index, protocol.EquipData.Equip.Builder builderForValue) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.add(index, builderForValue.build());
          onChanged();
        } else {
          equipBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder addAllEquip(
          java.lang.Iterable<? extends protocol.EquipData.Equip> values) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          super.addAll(values, equip_);
          onChanged();
        } else {
          equipBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder clearEquip() {
        if (equipBuilder_ == null) {
          equip_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          equipBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public Builder removeEquip(int index) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.remove(index);
          onChanged();
        } else {
          equipBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public protocol.EquipData.Equip.Builder getEquipBuilder(
          int index) {
        return getEquipFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public protocol.EquipData.EquipOrBuilder getEquipOrBuilder(
          int index) {
        if (equipBuilder_ == null) {
          return equip_.get(index);  } else {
          return equipBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public java.util.List<? extends protocol.EquipData.EquipOrBuilder> 
           getEquipOrBuilderList() {
        if (equipBuilder_ != null) {
          return equipBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(equip_);
        }
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public protocol.EquipData.Equip.Builder addEquipBuilder() {
        return getEquipFieldBuilder().addBuilder(
            protocol.EquipData.Equip.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public protocol.EquipData.Equip.Builder addEquipBuilder(
          int index) {
        return getEquipFieldBuilder().addBuilder(
            index, protocol.EquipData.Equip.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Equip equip = 2;</code>
       */
      public java.util.List<protocol.EquipData.Equip.Builder> 
           getEquipBuilderList() {
        return getEquipFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> 
          getEquipFieldBuilder() {
        if (equipBuilder_ == null) {
          equipBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder>(
                  equip_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          equip_ = null;
        }
        return equipBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetEquip)
    }

    static {
      defaultInstance = new ResponseGetEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetEquip)
  }

  public interface RequestOperateEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 创建      
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 创建      
     * </pre>
     */
    int getType();
  }
  /**
   * Protobuf type {@code protocol.RequestOperateEquip}
   *
   * <pre>
   *1216
   * </pre>
   */
  public static final class RequestOperateEquip extends
      com.google.protobuf.GeneratedMessage
      implements RequestOperateEquipOrBuilder {
    // Use RequestOperateEquip.newBuilder() to construct.
    private RequestOperateEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestOperateEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestOperateEquip defaultInstance;
    public static RequestOperateEquip getDefaultInstance() {
      return defaultInstance;
    }

    public RequestOperateEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestOperateEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_RequestOperateEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_RequestOperateEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.RequestOperateEquip.class, protocol.EquipData.RequestOperateEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestOperateEquip> PARSER =
        new com.google.protobuf.AbstractParser<RequestOperateEquip>() {
      public RequestOperateEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestOperateEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestOperateEquip> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 创建      
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 创建      
     * </pre>
     */
    public int getType() {
      return type_;
    }

    private void initFields() {
      type_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.RequestOperateEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.RequestOperateEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.RequestOperateEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.RequestOperateEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.RequestOperateEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestOperateEquip}
     *
     * <pre>
     *1216
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.RequestOperateEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_RequestOperateEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_RequestOperateEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.RequestOperateEquip.class, protocol.EquipData.RequestOperateEquip.Builder.class);
      }

      // Construct using protocol.EquipData.RequestOperateEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_RequestOperateEquip_descriptor;
      }

      public protocol.EquipData.RequestOperateEquip getDefaultInstanceForType() {
        return protocol.EquipData.RequestOperateEquip.getDefaultInstance();
      }

      public protocol.EquipData.RequestOperateEquip build() {
        protocol.EquipData.RequestOperateEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.RequestOperateEquip buildPartial() {
        protocol.EquipData.RequestOperateEquip result = new protocol.EquipData.RequestOperateEquip(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.RequestOperateEquip) {
          return mergeFrom((protocol.EquipData.RequestOperateEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.RequestOperateEquip other) {
        if (other == protocol.EquipData.RequestOperateEquip.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.RequestOperateEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.RequestOperateEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 创建      
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 创建      
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 创建      
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 创建      
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestOperateEquip)
    }

    static {
      defaultInstance = new RequestOperateEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestOperateEquip)
  }

  public interface ResponseOperateEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // required .protocol.Equip equip = 2;
    /**
     * <code>required .protocol.Equip equip = 2;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    boolean hasEquip();
    /**
     * <code>required .protocol.Equip equip = 2;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    protocol.EquipData.Equip getEquip();
    /**
     * <code>required .protocol.Equip equip = 2;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    protocol.EquipData.EquipOrBuilder getEquipOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseOperateEquip}
   *
   * <pre>
   *2216
   * </pre>
   */
  public static final class ResponseOperateEquip extends
      com.google.protobuf.GeneratedMessage
      implements ResponseOperateEquipOrBuilder {
    // Use ResponseOperateEquip.newBuilder() to construct.
    private ResponseOperateEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseOperateEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseOperateEquip defaultInstance;
    public static ResponseOperateEquip getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseOperateEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseOperateEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              protocol.EquipData.Equip.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = equip_.toBuilder();
              }
              equip_ = input.readMessage(protocol.EquipData.Equip.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(equip_);
                equip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_ResponseOperateEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_ResponseOperateEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.ResponseOperateEquip.class, protocol.EquipData.ResponseOperateEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseOperateEquip> PARSER =
        new com.google.protobuf.AbstractParser<ResponseOperateEquip>() {
      public ResponseOperateEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseOperateEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseOperateEquip> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required .protocol.Equip equip = 2;
    public static final int EQUIP_FIELD_NUMBER = 2;
    private protocol.EquipData.Equip equip_;
    /**
     * <code>required .protocol.Equip equip = 2;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public boolean hasEquip() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required .protocol.Equip equip = 2;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public protocol.EquipData.Equip getEquip() {
      return equip_;
    }
    /**
     * <code>required .protocol.Equip equip = 2;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public protocol.EquipData.EquipOrBuilder getEquipOrBuilder() {
      return equip_;
    }

    private void initFields() {
      errorId_ = 0;
      equip_ = protocol.EquipData.Equip.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasEquip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getEquip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, equip_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, equip_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.ResponseOperateEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.ResponseOperateEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.ResponseOperateEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.ResponseOperateEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.ResponseOperateEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseOperateEquip}
     *
     * <pre>
     *2216
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.ResponseOperateEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_ResponseOperateEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_ResponseOperateEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.ResponseOperateEquip.class, protocol.EquipData.ResponseOperateEquip.Builder.class);
      }

      // Construct using protocol.EquipData.ResponseOperateEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEquipFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (equipBuilder_ == null) {
          equip_ = protocol.EquipData.Equip.getDefaultInstance();
        } else {
          equipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_ResponseOperateEquip_descriptor;
      }

      public protocol.EquipData.ResponseOperateEquip getDefaultInstanceForType() {
        return protocol.EquipData.ResponseOperateEquip.getDefaultInstance();
      }

      public protocol.EquipData.ResponseOperateEquip build() {
        protocol.EquipData.ResponseOperateEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.ResponseOperateEquip buildPartial() {
        protocol.EquipData.ResponseOperateEquip result = new protocol.EquipData.ResponseOperateEquip(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (equipBuilder_ == null) {
          result.equip_ = equip_;
        } else {
          result.equip_ = equipBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.ResponseOperateEquip) {
          return mergeFrom((protocol.EquipData.ResponseOperateEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.ResponseOperateEquip other) {
        if (other == protocol.EquipData.ResponseOperateEquip.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasEquip()) {
          mergeEquip(other.getEquip());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasEquip()) {
          
          return false;
        }
        if (!getEquip().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.ResponseOperateEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.ResponseOperateEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required .protocol.Equip equip = 2;
      private protocol.EquipData.Equip equip_ = protocol.EquipData.Equip.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> equipBuilder_;
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public boolean hasEquip() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipData.Equip getEquip() {
        if (equipBuilder_ == null) {
          return equip_;
        } else {
          return equipBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder setEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          equip_ = value;
          onChanged();
        } else {
          equipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder setEquip(
          protocol.EquipData.Equip.Builder builderForValue) {
        if (equipBuilder_ == null) {
          equip_ = builderForValue.build();
          onChanged();
        } else {
          equipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder mergeEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              equip_ != protocol.EquipData.Equip.getDefaultInstance()) {
            equip_ =
              protocol.EquipData.Equip.newBuilder(equip_).mergeFrom(value).buildPartial();
          } else {
            equip_ = value;
          }
          onChanged();
        } else {
          equipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder clearEquip() {
        if (equipBuilder_ == null) {
          equip_ = protocol.EquipData.Equip.getDefaultInstance();
          onChanged();
        } else {
          equipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipData.Equip.Builder getEquipBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getEquipFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipData.EquipOrBuilder getEquipOrBuilder() {
        if (equipBuilder_ != null) {
          return equipBuilder_.getMessageOrBuilder();
        } else {
          return equip_;
        }
      }
      /**
       * <code>required .protocol.Equip equip = 2;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> 
          getEquipFieldBuilder() {
        if (equipBuilder_ == null) {
          equipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder>(
                  equip_,
                  getParentForChildren(),
                  isClean());
          equip_ = null;
        }
        return equipBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseOperateEquip)
    }

    static {
      defaultInstance = new ResponseOperateEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseOperateEquip)
  }

  public interface RequestChoiceEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.Equip equip = 1;
    /**
     * <code>required .protocol.Equip equip = 1;</code>
     */
    boolean hasEquip();
    /**
     * <code>required .protocol.Equip equip = 1;</code>
     */
    protocol.EquipData.Equip getEquip();
    /**
     * <code>required .protocol.Equip equip = 1;</code>
     */
    protocol.EquipData.EquipOrBuilder getEquipOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.RequestChoiceEquip}
   *
   * <pre>
   *1218
   * </pre>
   */
  public static final class RequestChoiceEquip extends
      com.google.protobuf.GeneratedMessage
      implements RequestChoiceEquipOrBuilder {
    // Use RequestChoiceEquip.newBuilder() to construct.
    private RequestChoiceEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestChoiceEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestChoiceEquip defaultInstance;
    public static RequestChoiceEquip getDefaultInstance() {
      return defaultInstance;
    }

    public RequestChoiceEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestChoiceEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              protocol.EquipData.Equip.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = equip_.toBuilder();
              }
              equip_ = input.readMessage(protocol.EquipData.Equip.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(equip_);
                equip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_RequestChoiceEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_RequestChoiceEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.RequestChoiceEquip.class, protocol.EquipData.RequestChoiceEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestChoiceEquip> PARSER =
        new com.google.protobuf.AbstractParser<RequestChoiceEquip>() {
      public RequestChoiceEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestChoiceEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestChoiceEquip> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.Equip equip = 1;
    public static final int EQUIP_FIELD_NUMBER = 1;
    private protocol.EquipData.Equip equip_;
    /**
     * <code>required .protocol.Equip equip = 1;</code>
     */
    public boolean hasEquip() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.Equip equip = 1;</code>
     */
    public protocol.EquipData.Equip getEquip() {
      return equip_;
    }
    /**
     * <code>required .protocol.Equip equip = 1;</code>
     */
    public protocol.EquipData.EquipOrBuilder getEquipOrBuilder() {
      return equip_;
    }

    private void initFields() {
      equip_ = protocol.EquipData.Equip.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEquip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getEquip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, equip_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, equip_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.RequestChoiceEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.RequestChoiceEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.RequestChoiceEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.RequestChoiceEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.RequestChoiceEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestChoiceEquip}
     *
     * <pre>
     *1218
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.RequestChoiceEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_RequestChoiceEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_RequestChoiceEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.RequestChoiceEquip.class, protocol.EquipData.RequestChoiceEquip.Builder.class);
      }

      // Construct using protocol.EquipData.RequestChoiceEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEquipFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (equipBuilder_ == null) {
          equip_ = protocol.EquipData.Equip.getDefaultInstance();
        } else {
          equipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_RequestChoiceEquip_descriptor;
      }

      public protocol.EquipData.RequestChoiceEquip getDefaultInstanceForType() {
        return protocol.EquipData.RequestChoiceEquip.getDefaultInstance();
      }

      public protocol.EquipData.RequestChoiceEquip build() {
        protocol.EquipData.RequestChoiceEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.RequestChoiceEquip buildPartial() {
        protocol.EquipData.RequestChoiceEquip result = new protocol.EquipData.RequestChoiceEquip(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (equipBuilder_ == null) {
          result.equip_ = equip_;
        } else {
          result.equip_ = equipBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.RequestChoiceEquip) {
          return mergeFrom((protocol.EquipData.RequestChoiceEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.RequestChoiceEquip other) {
        if (other == protocol.EquipData.RequestChoiceEquip.getDefaultInstance()) return this;
        if (other.hasEquip()) {
          mergeEquip(other.getEquip());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEquip()) {
          
          return false;
        }
        if (!getEquip().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.RequestChoiceEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.RequestChoiceEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.Equip equip = 1;
      private protocol.EquipData.Equip equip_ = protocol.EquipData.Equip.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> equipBuilder_;
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public boolean hasEquip() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public protocol.EquipData.Equip getEquip() {
        if (equipBuilder_ == null) {
          return equip_;
        } else {
          return equipBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public Builder setEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          equip_ = value;
          onChanged();
        } else {
          equipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public Builder setEquip(
          protocol.EquipData.Equip.Builder builderForValue) {
        if (equipBuilder_ == null) {
          equip_ = builderForValue.build();
          onChanged();
        } else {
          equipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public Builder mergeEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              equip_ != protocol.EquipData.Equip.getDefaultInstance()) {
            equip_ =
              protocol.EquipData.Equip.newBuilder(equip_).mergeFrom(value).buildPartial();
          } else {
            equip_ = value;
          }
          onChanged();
        } else {
          equipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public Builder clearEquip() {
        if (equipBuilder_ == null) {
          equip_ = protocol.EquipData.Equip.getDefaultInstance();
          onChanged();
        } else {
          equipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public protocol.EquipData.Equip.Builder getEquipBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getEquipFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      public protocol.EquipData.EquipOrBuilder getEquipOrBuilder() {
        if (equipBuilder_ != null) {
          return equipBuilder_.getMessageOrBuilder();
        } else {
          return equip_;
        }
      }
      /**
       * <code>required .protocol.Equip equip = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> 
          getEquipFieldBuilder() {
        if (equipBuilder_ == null) {
          equipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder>(
                  equip_,
                  getParentForChildren(),
                  isClean());
          equip_ = null;
        }
        return equipBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestChoiceEquip)
    }

    static {
      defaultInstance = new RequestChoiceEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestChoiceEquip)
  }

  public interface ResponseChoiceEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool status = 1;
    /**
     * <code>required bool status = 1;</code>
     */
    boolean hasStatus();
    /**
     * <code>required bool status = 1;</code>
     */
    boolean getStatus();

    // required int32 errorId = 2;
    /**
     * <code>required int32 errorId = 2;</code>
     *
     * <pre>
     *错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 2;</code>
     *
     * <pre>
     *错误id
     * </pre>
     */
    int getErrorId();

    // required .protocol.Equip equip = 3;
    /**
     * <code>required .protocol.Equip equip = 3;</code>
     */
    boolean hasEquip();
    /**
     * <code>required .protocol.Equip equip = 3;</code>
     */
    protocol.EquipData.Equip getEquip();
    /**
     * <code>required .protocol.Equip equip = 3;</code>
     */
    protocol.EquipData.EquipOrBuilder getEquipOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseChoiceEquip}
   *
   * <pre>
   *2218
   * </pre>
   */
  public static final class ResponseChoiceEquip extends
      com.google.protobuf.GeneratedMessage
      implements ResponseChoiceEquipOrBuilder {
    // Use ResponseChoiceEquip.newBuilder() to construct.
    private ResponseChoiceEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseChoiceEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseChoiceEquip defaultInstance;
    public static ResponseChoiceEquip getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseChoiceEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseChoiceEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              status_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              errorId_ = input.readInt32();
              break;
            }
            case 26: {
              protocol.EquipData.Equip.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = equip_.toBuilder();
              }
              equip_ = input.readMessage(protocol.EquipData.Equip.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(equip_);
                equip_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipData.internal_static_protocol_ResponseChoiceEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipData.internal_static_protocol_ResponseChoiceEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipData.ResponseChoiceEquip.class, protocol.EquipData.ResponseChoiceEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseChoiceEquip> PARSER =
        new com.google.protobuf.AbstractParser<ResponseChoiceEquip>() {
      public ResponseChoiceEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseChoiceEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseChoiceEquip> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool status = 1;
    public static final int STATUS_FIELD_NUMBER = 1;
    private boolean status_;
    /**
     * <code>required bool status = 1;</code>
     */
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool status = 1;</code>
     */
    public boolean getStatus() {
      return status_;
    }

    // required int32 errorId = 2;
    public static final int ERRORID_FIELD_NUMBER = 2;
    private int errorId_;
    /**
     * <code>required int32 errorId = 2;</code>
     *
     * <pre>
     *错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 errorId = 2;</code>
     *
     * <pre>
     *错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required .protocol.Equip equip = 3;
    public static final int EQUIP_FIELD_NUMBER = 3;
    private protocol.EquipData.Equip equip_;
    /**
     * <code>required .protocol.Equip equip = 3;</code>
     */
    public boolean hasEquip() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required .protocol.Equip equip = 3;</code>
     */
    public protocol.EquipData.Equip getEquip() {
      return equip_;
    }
    /**
     * <code>required .protocol.Equip equip = 3;</code>
     */
    public protocol.EquipData.EquipOrBuilder getEquipOrBuilder() {
      return equip_;
    }

    private void initFields() {
      status_ = false;
      errorId_ = 0;
      equip_ = protocol.EquipData.Equip.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasEquip()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getEquip().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, status_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, errorId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(3, equip_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, status_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, errorId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, equip_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipData.ResponseChoiceEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipData.ResponseChoiceEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipData.ResponseChoiceEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseChoiceEquip}
     *
     * <pre>
     *2218
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipData.ResponseChoiceEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipData.internal_static_protocol_ResponseChoiceEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipData.internal_static_protocol_ResponseChoiceEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipData.ResponseChoiceEquip.class, protocol.EquipData.ResponseChoiceEquip.Builder.class);
      }

      // Construct using protocol.EquipData.ResponseChoiceEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEquipFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        status_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (equipBuilder_ == null) {
          equip_ = protocol.EquipData.Equip.getDefaultInstance();
        } else {
          equipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipData.internal_static_protocol_ResponseChoiceEquip_descriptor;
      }

      public protocol.EquipData.ResponseChoiceEquip getDefaultInstanceForType() {
        return protocol.EquipData.ResponseChoiceEquip.getDefaultInstance();
      }

      public protocol.EquipData.ResponseChoiceEquip build() {
        protocol.EquipData.ResponseChoiceEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipData.ResponseChoiceEquip buildPartial() {
        protocol.EquipData.ResponseChoiceEquip result = new protocol.EquipData.ResponseChoiceEquip(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.status_ = status_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        if (equipBuilder_ == null) {
          result.equip_ = equip_;
        } else {
          result.equip_ = equipBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipData.ResponseChoiceEquip) {
          return mergeFrom((protocol.EquipData.ResponseChoiceEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipData.ResponseChoiceEquip other) {
        if (other == protocol.EquipData.ResponseChoiceEquip.getDefaultInstance()) return this;
        if (other.hasStatus()) {
          setStatus(other.getStatus());
        }
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasEquip()) {
          mergeEquip(other.getEquip());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasStatus()) {
          
          return false;
        }
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasEquip()) {
          
          return false;
        }
        if (!getEquip().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipData.ResponseChoiceEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipData.ResponseChoiceEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool status = 1;
      private boolean status_ ;
      /**
       * <code>required bool status = 1;</code>
       */
      public boolean hasStatus() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool status = 1;</code>
       */
      public boolean getStatus() {
        return status_;
      }
      /**
       * <code>required bool status = 1;</code>
       */
      public Builder setStatus(boolean value) {
        bitField0_ |= 0x00000001;
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool status = 1;</code>
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000001);
        status_ = false;
        onChanged();
        return this;
      }

      // required int32 errorId = 2;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 2;</code>
       *
       * <pre>
       *错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 errorId = 2;</code>
       *
       * <pre>
       *错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 2;</code>
       *
       * <pre>
       *错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000002;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 2;</code>
       *
       * <pre>
       *错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required .protocol.Equip equip = 3;
      private protocol.EquipData.Equip equip_ = protocol.EquipData.Equip.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> equipBuilder_;
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public boolean hasEquip() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public protocol.EquipData.Equip getEquip() {
        if (equipBuilder_ == null) {
          return equip_;
        } else {
          return equipBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public Builder setEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          equip_ = value;
          onChanged();
        } else {
          equipBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public Builder setEquip(
          protocol.EquipData.Equip.Builder builderForValue) {
        if (equipBuilder_ == null) {
          equip_ = builderForValue.build();
          onChanged();
        } else {
          equipBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public Builder mergeEquip(protocol.EquipData.Equip value) {
        if (equipBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004) &&
              equip_ != protocol.EquipData.Equip.getDefaultInstance()) {
            equip_ =
              protocol.EquipData.Equip.newBuilder(equip_).mergeFrom(value).buildPartial();
          } else {
            equip_ = value;
          }
          onChanged();
        } else {
          equipBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public Builder clearEquip() {
        if (equipBuilder_ == null) {
          equip_ = protocol.EquipData.Equip.getDefaultInstance();
          onChanged();
        } else {
          equipBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public protocol.EquipData.Equip.Builder getEquipBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getEquipFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      public protocol.EquipData.EquipOrBuilder getEquipOrBuilder() {
        if (equipBuilder_ != null) {
          return equipBuilder_.getMessageOrBuilder();
        } else {
          return equip_;
        }
      }
      /**
       * <code>required .protocol.Equip equip = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder> 
          getEquipFieldBuilder() {
        if (equipBuilder_ == null) {
          equipBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.EquipData.Equip, protocol.EquipData.Equip.Builder, protocol.EquipData.EquipOrBuilder>(
                  equip_,
                  getParentForChildren(),
                  isClean());
          equip_ = null;
        }
        return equipBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseChoiceEquip)
    }

    static {
      defaultInstance = new ResponseChoiceEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseChoiceEquip)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Equip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Equip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetEquip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetEquip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestOperateEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestOperateEquip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseOperateEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseOperateEquip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestChoiceEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestChoiceEquip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseChoiceEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseChoiceEquip_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013equip.proto\022\010protocol\032\nitem.proto\"\343\001\n\005" +
      "Equip\022\020\n\010equipEid\030\001 \002(\005\022\021\n\tequipType\030\002 \002" +
      "(\005\022\024\n\014currentLevel\030\003 \002(\005\022\022\n\ncurrentExp\030\004" +
      " \002(\005\022\020\n\010adFactor\030\005 \002(\002\022\020\n\010apFactor\030\006 \002(\002" +
      "\022\021\n\tarmFactor\030\007 \002(\002\022\021\n\tmdfFactor\030\010 \002(\002\022\023" +
      "\n\013speedFactor\030\t \002(\002\022\020\n\010hpFactor\030\n \002(\002\022\014\n" +
      "\004name\030\013 \002(\t\022\014\n\004sort\030\014 \002(\005\"\021\n\017RequestGetE" +
      "quip\"C\n\020ResponseGetEquip\022\017\n\007errorId\030\001 \002(" +
      "\005\022\036\n\005equip\030\002 \003(\0132\017.protocol.Equip\"#\n\023Req" +
      "uestOperateEquip\022\014\n\004type\030\001 \002(\005\"G\n\024Respon",
      "seOperateEquip\022\017\n\007errorId\030\001 \002(\005\022\036\n\005equip" +
      "\030\002 \002(\0132\017.protocol.Equip\"4\n\022RequestChoice" +
      "Equip\022\036\n\005equip\030\001 \002(\0132\017.protocol.Equip\"V\n" +
      "\023ResponseChoiceEquip\022\016\n\006status\030\001 \002(\010\022\017\n\007" +
      "errorId\030\002 \002(\005\022\036\n\005equip\030\003 \002(\0132\017.protocol." +
      "EquipB\013B\tEquipData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_Equip_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_Equip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Equip_descriptor,
              new java.lang.String[] { "EquipEid", "EquipType", "CurrentLevel", "CurrentExp", "AdFactor", "ApFactor", "ArmFactor", "MdfFactor", "SpeedFactor", "HpFactor", "Name", "Sort", });
          internal_static_protocol_RequestGetEquip_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestGetEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetEquip_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetEquip_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ResponseGetEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetEquip_descriptor,
              new java.lang.String[] { "ErrorId", "Equip", });
          internal_static_protocol_RequestOperateEquip_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestOperateEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestOperateEquip_descriptor,
              new java.lang.String[] { "Type", });
          internal_static_protocol_ResponseOperateEquip_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponseOperateEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseOperateEquip_descriptor,
              new java.lang.String[] { "ErrorId", "Equip", });
          internal_static_protocol_RequestChoiceEquip_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_RequestChoiceEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestChoiceEquip_descriptor,
              new java.lang.String[] { "Equip", });
          internal_static_protocol_ResponseChoiceEquip_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_protocol_ResponseChoiceEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseChoiceEquip_descriptor,
              new java.lang.String[] { "Status", "ErrorId", "Equip", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
