package model;

/**
 * Created by nara on 2018/2/26.
 * des:邮件信息
 */
public class MailInfo {
    private int mailId;
    private String type;
    private String mid;
    private String title;
    private String content;
    private int roleId;
    private long publishTime;
    private int status;//0为发送 1已读

    public MailInfo() {
        this.status = 0;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getPublishTime() {
        return publishTime;
    }

    public void setPublishTime(long publishTime) {
        this.publishTime = publishTime;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getMailId() {
        return mailId;
    }

    public void setMailId(int mailId) {
        this.mailId = mailId;
    }

    @Override
    public String toString() {
        return "MailInfo{" +
                "mailId=" + mailId +
                ", type='" + type + '\'' +
                ", mid='" + mid + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", roleId=" + roleId +
                ", publishTime=" + publishTime +
                ", status=" + status +
                '}';
    }
}
