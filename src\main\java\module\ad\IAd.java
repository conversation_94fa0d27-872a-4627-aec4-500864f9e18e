package module.ad;

import entities.AdLogEntity;
import entities.ClickNumEntity;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-03 19:24
 */
public interface IAd {
    ClickNumEntity getClickNumEntity(String user_id);

    List<Object> getClickNumList(String event_id);

    void saveClickNum(ClickNumEntity clickNumEntity);

    void saveAdLog(AdLogEntity adLogEntity);

    void updateClickNum(int num, String user_id, Date date);
}
