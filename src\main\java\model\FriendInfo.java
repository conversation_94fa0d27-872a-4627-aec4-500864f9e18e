package model;

/**
 * Created by nara on 2018/2/1.
 */
public class FriendInfo {
    private int id;
    private String uid;
    private int type;//1为好友
    private int lv;
    private int head;
    private String name;
    private String signature;
    private int value;
    private int station;
    private String roomInfo;
    private int status;
    private int dailyScript;
    private int dailyItem;
    private String scriptStamp;
    private String itemStamp;
    private long totalEndless;

    public FriendInfo() {
        this.dailyItem = 0;
        this.dailyScript = 0;
        this.scriptStamp = "0";
        this.itemStamp = "0";
    }

    public long getTotalEndless() {
        return totalEndless;
    }

    public void setTotalEndless(long totalEndless) {
        this.totalEndless = totalEndless;
    }

    public String getRoomInfo() {
        return roomInfo;
    }

    public void setRoomInfo(String roomInfo) {
        this.roomInfo = roomInfo;
    }

    public String getItemStamp() {
        return itemStamp;
    }

    public void setItemStamp(String itemStamp) {
        this.itemStamp = itemStamp;
    }

    public String getScriptStamp() {
        return scriptStamp;
    }

    public void setScriptStamp(String scriptStamp) {
        this.scriptStamp = scriptStamp;
    }

    public int getStation() {
        return station;
    }

    public void setStation(int station) {
        this.station = station;
    }

    public int getDailyItem() {
        return dailyItem;
    }

    public void setDailyItem(int dailyItem) {
        this.dailyItem = dailyItem;
    }

    public int getDailyScript() {
        return dailyScript;
    }

    public void setDailyScript(int dailyScript) {
        this.dailyScript = dailyScript;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getHead() {
        return head;
    }

    public void setHead(int head) {
        this.head = head;
    }

    public int getLv() {
        return lv;
    }

    public void setLv(int lv) {
        this.lv = lv;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
