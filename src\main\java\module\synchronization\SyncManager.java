package module.synchronization;

import common.SuperConfig;
import io.netty.channel.ChannelHandlerContext;
import manager.Redis;
import manager.ReportManager;
import model.CupboardInfo;
import model.PointInfo;
import module.item.IItem;
import module.item.ItemDao;
import module.login.ILogin;
import module.login.LoginDao;
import module.mission.MissionDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ItemData;
import protocol.MissionData;
import protocol.ProtoData;
import protocol.UserData;
import server.SuperProtocol;

import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by nara on 2018/6/12.
 */
public class SyncManager {
    private static Logger log = LoggerFactory.getLogger(SyncManager.class);

    private static ChannelHandlerContext gameServerCtx = null;
    /**
     * 多人战斗房间数据
     */
    private static Map<String, GameInfo> syncProgressList = new HashMap<String, GameInfo>();

    /**
     * 玩家对应房间
     */
    private static Map<ChannelHandlerContext, String> playerRoomMap = new HashMap<ChannelHandlerContext, String>();

    public static Map<String, GameInfo> getSyncProgressList() {
        return syncProgressList;
    }

    public static void setGameServerCtx(ChannelHandlerContext ctx) {
        gameServerCtx = ctx;
    }


    public static byte[] receiveHeart(byte[] bytes) {
        MissionData.RequestHeart requestHeart = null;
        MissionData.ResponseHeart.Builder builder = MissionData.ResponseHeart.newBuilder();
        try {
            requestHeart = MissionData.RequestHeart.parseFrom(bytes);
        } catch (Exception e) {
            return null;
        }
        builder.setId(requestHeart.getId());
        builder.setTimeStamp(System.currentTimeMillis() + "");
        return builder.build().toByteArray();
    }

    //游戏服通知开启一场战斗
    public static byte[] receiveEnterMission(ChannelHandlerContext ctx, byte[] bytes) {
        MissionData.SRequestEnterMission sRequestEnterMission = null;
        MissionData.MResponseEnterMission.Builder builder = MissionData.MResponseEnterMission.newBuilder();
        builder.setErrorId(0);
        try {
            sRequestEnterMission = MissionData.SRequestEnterMission.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            return builder.build().toByteArray();
        }
        setGameServerCtx(ctx);

        String key = sRequestEnterMission.getKey();
        setFightFromRoom(key, sRequestEnterMission.getType(), sRequestEnterMission.getHall(), sRequestEnterMission.getRoomId(), sRequestEnterMission.getPlayersList());
        builder.setKey(key);
        builder.setHall(sRequestEnterMission.getHall());
        builder.setRoomId(sRequestEnterMission.getRoomId());
        builder.setType(sRequestEnterMission.getType());

        CompleteHandler completeHandler = new CompleteHandler();
        completeHandler.setKey(sRequestEnterMission.getKey());
        Thread thread = new Thread(completeHandler);
        thread.start();

        return builder.build().toByteArray();
    }

    public static void setFightFromRoom(String key, int type, int hall, int roomId, List<MissionData.TransferPlayerInfo> playerIds) {
        GameInfo gameInfo = new GameInfo();
        gameInfo.setType(type);
        gameInfo.setHall(hall);
        gameInfo.setRoomId(roomId);
        Map<Integer, Integer> playerOnBall = new HashMap<Integer, Integer>();
        for (int i = 0; i < playerIds.size(); i++) {
            MissionData.TransferPlayerInfo transferPlayerInfo = playerIds.get(i);
            FightPlayerInfo fightPlayerInfo = new FightPlayerInfo();
            fightPlayerInfo.setUid(transferPlayerInfo.getUid());
            fightPlayerInfo.setQueue(transferPlayerInfo.getQueue());
            gameInfo.getFightPlayers().put(transferPlayerInfo.getId(), fightPlayerInfo);
            gameInfo.getScoreMap().put(transferPlayerInfo.getId(), 0);
            gameInfo.getComboMap().put(transferPlayerInfo.getId(), 0);
            gameInfo.getNowComboMap().put(transferPlayerInfo.getId(), 0);
            gameInfo.getNowBaseScore().put(transferPlayerInfo.getId(), 0);

            playerOnBall.put(transferPlayerInfo.getId(), -1);
        }
        //初始化地图信息
        List<BallInfo> ballInfoList = null;
        if (type == 1) {
            ballInfoList = initFightMap();
        } else if (type == 2) {
            ballInfoList = initSpeedFightMap();
        }

        gameInfo.setMapInfo(new MapInfo());
        gameInfo.getMapInfo().setBallList(ballInfoList);
        gameInfo.getMapInfo().setBallNum(ballInfoList.size());
        gameInfo.getMapInfo().setPlayerOnBall(playerOnBall);
        syncProgressList.put(key, gameInfo);
        //

    }

    //接收连接战斗服请求
    public static byte[] receiveConnectFight(ChannelHandlerContext ctx, byte[] bytes) {
        /// System.out.println("============================receiveConnectFight==========================");
        MissionData.RequestConnectFight requestConnectFight = null;
        MissionData.ResponseConnectFight.Builder builder = MissionData.ResponseConnectFight.newBuilder();
        builder.setErrorId(0);
        try {
            requestConnectFight = MissionData.RequestConnectFight.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            return builder.build().toByteArray();
        }
        builder.setKey(requestConnectFight.getKey());
        String key = requestConnectFight.getKey();
        GameInfo gameInfo = syncProgressList.get(key);
        if (gameInfo == null) {
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            return builder.build().toByteArray();
        }
        int id = requestConnectFight.getId();
        /// System.out.println("id>>>>>>>>>>>>>>>>>>>>>>>>"+id);
        FightPlayerInfo fightPlayerInfo = gameInfo.getFightPlayers().get(id);
//        fightPlayerInfo.setStatus(1);
        fightPlayerInfo.setCtx(ctx);
        List<BallInfo> ballInfoList = gameInfo.getMapInfo().getBallList();
        for (int i = 0; i < ballInfoList.size(); i++) {
            BallInfo ballInfo = ballInfoList.get(i);
            MissionData.PvpBall.Builder pvpBall = MissionData.PvpBall.newBuilder();
            MissionData.BigBall bigBall = BallInfo.setBigBall(ballInfo);
            pvpBall.setSpeed(ballInfo.getSpeed());
            pvpBall.setBigBall(bigBall);
            pvpBall.setId(ballInfo.getIndex());
            builder.addBigBall(pvpBall);
        }
        ILogin iLogin = LoginDao.getInstance();
        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            String uid = entry.getValue().getUid();
            CupboardInfo cupboardInfo = iLogin.getCurrentDressOnLine(uid);
            MissionData.FightRole.Builder fightRoleBu = MissionData.FightRole.newBuilder();
            fightRoleBu.setRoleId(cupboardInfo.getRoleId());
            fightRoleBu.setPlayerId(entry.getKey());
            fightRoleBu.setQueue(entry.getValue().getQueue());
            UserData.Part part = CupboardInfo.setCupboardToPartData(cupboardInfo);
            fightRoleBu.setPart(part);
            String name = iLogin.getNameFromUid(uid);
            fightRoleBu.setName(name);
            int head = iLogin.getHeadFromUid(uid);
            fightRoleBu.setHead(head);
            int lv = iLogin.getLvFromUid(uid);
            fightRoleBu.setLv(lv);
            builder.addRoleList(fightRoleBu);
            builder.setErrorId(entry.getKey());
        }
        playerRoomMap.put(ctx, key);
        return builder.build().toByteArray();
    }

    //接收加载完成
    public static byte[] receiveLoadFinish(ChannelHandlerContext ctx, byte[] bytes) {
        MissionData.RequestLoadFinish requestLoadFinish = null;
        MissionData.ResponseLoadFinish.Builder builder = MissionData.ResponseLoadFinish.newBuilder();
        builder.setErrorId(0);
        try {
            requestLoadFinish = MissionData.RequestLoadFinish.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            return builder.build().toByteArray();
        }
        String key = requestLoadFinish.getKey();
        GameInfo gameInfo = syncProgressList.get(key);
        if (gameInfo == null) {
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            return builder.build().toByteArray();
        }
        int playerId = judgePlayerId(gameInfo, ctx);
        FightPlayerInfo fightPlayerInfo = gameInfo.getFightPlayers().get(playerId);
        fightPlayerInfo.setStatus(1);
        return builder.build().toByteArray();
    }

    private static int judgePlayerId(GameInfo gameInfo, ChannelHandlerContext ctx) {
        int playerId = 0;
        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            FightPlayerInfo fightPlayerInfo = entry.getValue();
            if (fightPlayerInfo.getCtx() == ctx) {
                playerId = entry.getKey();
                break;
            }
        }
        return playerId;
    }


    //同步战斗
    public static byte[] receiveSyncMission(ChannelHandlerContext ctx, byte[] bytes) {
        MissionData.RequestSyncMission requestSyncMission = null;
        MissionData.ResponseSyncMission.Builder builder = MissionData.ResponseSyncMission.newBuilder();
        builder.setTimeStamp(System.currentTimeMillis() + "");
        builder.setStatus(true);
        try {
            requestSyncMission = MissionData.RequestSyncMission.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            builder.setStatus(false);
            return builder.build().toByteArray();
        }
        GameInfo gameInfo = syncProgressList.get(requestSyncMission.getKey());
        if (gameInfo == null) {
            builder.setStatus(false);
            return builder.build().toByteArray();
        }
        int playerId = judgePlayerId(gameInfo, ctx);
        if (playerId == 0) {
            builder.setStatus(false);
            return builder.build().toByteArray();
        }
        int ballIndex = requestSyncMission.getBallIndex();
        MissionData.ReportBroadcastMission.Builder broadcastBu = MissionData.ReportBroadcastMission.newBuilder();
        broadcastBu.setId(playerId);
        broadcastBu.setType(requestSyncMission.getType());
        broadcastBu.setSpeed(requestSyncMission.getSpeed());
        broadcastBu.setEndPos(requestSyncMission.getEndPos());
        broadcastBu.setStartPos(requestSyncMission.getStartPos());
        broadcastBu.setBallIndex(ballIndex);
        broadcastBu.setTimeStamp(requestSyncMission.getTimeStamp());
        if (requestSyncMission.getDistance() != -1) {
            broadcastBu.setDistance(requestSyncMission.getDistance());
        }

        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            if (gameInfo.getType() == 2 || (gameInfo.getType() == 1 && entry.getKey() != playerId)) {
                /// System.out.println("receiveSyncMission>>>>>>>>>>>>>>>>>>>>>>>"+entry.getKey());
                FightPlayerInfo fightPlayerInfo = entry.getValue();
                ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTBROADCASTSYNC_VALUE, broadcastBu.build().toByteArray());
            }
        }
        if (requestSyncMission.getType() != 5) {//设置球的速度
            gameInfo.getMapInfo().getPlayerOnBall().put(playerId, ballIndex);
            /// System.out.println("receiveSyncMission location>>>>>>>>>>>>>>>>>playerId:"+playerId+",ballIndex:"+ballIndex);

            if (ballIndex >= 0) {//跳上球
                /// System.out.println("jump to the ball>>>>>>>>>>>>>>>>>playerId:"+playerId+",ballIndex:"+ballIndex);
                MapInfo mapInfo = gameInfo.getMapInfo();
                if (ballIndex != 9999) {
                    BallInfo ballInfo = null;
                    for (int i = 0; i < mapInfo.getBallList().size(); i++) {
                        BallInfo tmp = mapInfo.getBallList().get(i);
                        if (tmp != null && tmp.getIndex() == ballIndex) {
                            ballInfo = tmp;
                            break;
                        }
                    }
                    if (ballInfo != null) {
                        if (gameInfo.getType() == 2 || (gameInfo.getType() == 1 && ballInfo.getIntegral() != 0)) {
                            int nowCombo = gameInfo.getNowComboMap().get(playerId) + 1;
                            gameInfo.getNowComboMap().put(playerId, nowCombo);
                            int maxCombo = gameInfo.getComboMap().get(playerId);
                            if (nowCombo > maxCombo) {
                                maxCombo = nowCombo;
                                gameInfo.getComboMap().put(playerId, maxCombo);
                            }

                            if (gameInfo.getType() == 1) {
                                int playerScore = gameInfo.getScoreMap().get(playerId) + ballInfo.getIntegral();
                                gameInfo.getScoreMap().put(playerId, playerScore);
                                int score = gameInfo.getTotalScore() + ballInfo.getIntegral();
                                gameInfo.setTotalScore(score);
                                ballInfo.setIntegral(0);

                                MissionData.ReportChangeScore.Builder scoreBu = MissionData.ReportChangeScore.newBuilder();
                                scoreBu.setId(-1);
                                scoreBu.setScore(score);
                                for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
                                    FightPlayerInfo fightPlayerInfo = entry.getValue();
                                    ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTCHANGESCORE_VALUE, scoreBu.build().toByteArray());
                                    /// System.out.println("reportChangeScore>>>>>>>>>>>>>>>>>>>>>>>playerId:" + playerId + ",nowScore:" + score);
                                }

                                //增加一个球
                                MissionData.ReportMissionProgress.Builder progressBu = MissionData.ReportMissionProgress.newBuilder();
                                BallInfo newBall = setFightBall(gameInfo.getMapInfo().getBallNum());
                                MissionData.PvpBall.Builder pvpBall = MissionData.PvpBall.newBuilder();
                                MissionData.BigBall bigBall = BallInfo.setBigBall(ballInfo);
                                pvpBall.setSpeed(ballInfo.getSpeed());
                                pvpBall.setBigBall(bigBall);
                                pvpBall.setId(mapInfo.getBallNum());
                                progressBu.setBigBall(pvpBall);
                                mapInfo.getBallList().add(newBall);
                                for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
//                    if (entry.getKey() != playerId){
                                    FightPlayerInfo fightPlayerInfo = entry.getValue();
                                    ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTMISSIONPROGRESS_VALUE, progressBu.build().toByteArray());
//                    }
                                }
                                mapInfo.setBallNum(mapInfo.getBallNum() + 1);

                                //判断踢人下球
                                judgeKickPlayerOnBall(gameInfo, mapInfo, playerId, ballIndex);

                            } else if (gameInfo.getType() == 2) {
                                int add = nowCombo > 10 ? 10 : nowCombo;
                                int score = gameInfo.getScoreMap().get(playerId) + add * 10;

                                gameInfo.getScoreMap().put(playerId, score);
//                                ballInfo.setIntegral(0);

                                MissionData.ReportChangeScore.Builder scoreBu = MissionData.ReportChangeScore.newBuilder();
                                scoreBu.setId(playerId);
                                scoreBu.setScore(score);
                                for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
                                    FightPlayerInfo fightPlayerInfo = entry.getValue();
                                    ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTCHANGESCORE_VALUE, scoreBu.build().toByteArray());
                                    /// System.out.println("reportChangeScore>>>>>>>>>>>>>>>>>>>>>>>playerId:" + playerId + ",score:" + score);
                                }
                            }
                        } else if (gameInfo.getType() == 1) {
                            //判断踢人下球
                            judgeKickPlayerOnBall(gameInfo, mapInfo, playerId, ballIndex);
                        }
                    }
                } else {//在基地上
                    int baseVal = gameInfo.getNowBaseScore().get(playerId) + 1;
                    gameInfo.getNowBaseScore().put(playerId, baseVal);
                    int playerScore = gameInfo.getScoreMap().get(playerId) + baseVal;
                    gameInfo.getScoreMap().put(playerId, playerScore);
                    int score = gameInfo.getTotalScore() + baseVal;
                    gameInfo.setTotalScore(score);

                    MissionData.ReportChangeScore.Builder scoreBu = MissionData.ReportChangeScore.newBuilder();
                    scoreBu.setId(-1);
                    scoreBu.setScore(score);
                    for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
                        FightPlayerInfo fightPlayerInfo = entry.getValue();
                        ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTCHANGESCORE_VALUE, scoreBu.build().toByteArray());
                        /// System.out.println("reportChangeScore>>>>>>>>>>>>>>>>>>>>>>>playerId:" + playerId + ",score:" + score);
                    }

                    //判断踢人下球
                    judgeKickPlayerOnBall(gameInfo, mapInfo, playerId, ballIndex);
                }
            } else {
                if (gameInfo.getType() == 1 || (gameInfo.getType() == 2 && requestSyncMission.getType() != 2)) {
                    gameInfo.getNowComboMap().put(playerId, 0);
                }
            }
        }
        return builder.build().toByteArray();
    }

    private static void judgeKickPlayerOnBall(GameInfo gameInfo, MapInfo mapInfo, int playerId, int ballIndex) {
        //判断踢人下球
        for (Map.Entry<Integer, Integer> entry : mapInfo.getPlayerOnBall().entrySet()) {
            if (entry.getKey() != playerId && entry.getValue() == ballIndex) {
                /// System.out.println("old the ball>>>>>>>>>>>>>>>>>playerId:"+entry.getKey()+",ballIndex:"+entry.getValue());
                /// System.out.println("kick player>>>>>>>>>>>>>>>>>playerId:"+playerId+",ballIndex:"+ballIndex);
                MissionData.ReportKick.Builder kickBu = MissionData.ReportKick.newBuilder();
                kickBu.setInitiative(playerId);
                kickBu.setPassive(entry.getKey());

                for (Map.Entry<Integer, FightPlayerInfo> entry2 : gameInfo.getFightPlayers().entrySet()) {
                    FightPlayerInfo fightPlayerInfo = entry2.getValue();
                    ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTKICK_VALUE, kickBu.build().toByteArray());
                }

                mapInfo.getPlayerOnBall().put(entry.getKey(), -2);
                break;
            }
        }
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    public static void getConnectInfo(String key, int playerId, ChannelHandlerContext ctx) {
        GameInfo gameInfo = syncProgressList.get(key);
        FightPlayerInfo fightPlayerInfo = gameInfo.getFightPlayers().get(playerId);
        if (fightPlayerInfo != null) {
            fightPlayerInfo.setStatus(1);
        } else {
            //error
        }
    }

    public static void startFight(final String key) {
        GameInfo gameInfo = syncProgressList.get(key);
        if (gameInfo.getType() == 1) {
            ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
            gameInfo.setService(service);
            SyncMissionHandler syncMission = new SyncMissionHandler(key);
            //采集客户端上传操作并按固定频率广播已接收到的操作数据
            service.scheduleAtFixedRate(syncMission, 50, 50, TimeUnit.MILLISECONDS);
        }

        /// System.out.println("========================startFight=======================");

        //广播开始战斗
        MissionData.ReportStartFight.Builder builder = MissionData.ReportStartFight.newBuilder();
        builder.setTimeStamp(System.currentTimeMillis() + "");

        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            FightPlayerInfo fightPlayerInfo = entry.getValue();
            ///
            ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTSTARTFIGHT_VALUE, builder.build().toByteArray());
        }
        //开启定时器两分钟后结束游戏
        Timer nTimer = new Timer();
        int time = 2 * 60 * 1000;
        if (gameInfo.getType() == 2) {
            time = 2 * 60 * 1000;
        }
        nTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                overFight(key, null);
            }
        }, time);//毫秒
    }

    private static MissionData.ReportOverFight.Builder setPkExitData(GameInfo gameInfo, ChannelHandlerContext ctx) {
        MissionData.ReportOverFight.Builder builder = MissionData.ReportOverFight.newBuilder();
        int goOutId = 0;
        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            FightPlayerInfo fightPlayerInfo = entry.getValue();
            if (fightPlayerInfo.getCtx() == ctx) {
                goOutId = entry.getKey();
                break;
            }
        }
        for (Map.Entry<Integer, Integer> entry : gameInfo.getScoreMap().entrySet()) {
            int id = entry.getKey();
            if (id == goOutId) {
                MissionData.PvpOverData.Builder pvpBu = MissionData.PvpOverData.newBuilder();
                pvpBu.setId(id);
                pvpBu.setIntegral(0);
                pvpBu.setCombo(0);
                String uid = gameInfo.getFightPlayers().get(id).getUid();
                List<ItemData.Item> itemList = getPkReward(uid, gameInfo.getType(), 0, 0);
                pvpBu.addAllItem(itemList);
                pvpBu.setIsWin(0);
                builder.addPlayerDatas(pvpBu);
            } else {
                gameInfo.setWinId(id);
                MissionData.PvpOverData.Builder pvpBu = MissionData.PvpOverData.newBuilder();
                pvpBu.setId(id);
                pvpBu.setIntegral(entry.getValue());
                int combo = gameInfo.getComboMap().get(id);
                pvpBu.setCombo(combo);
                String uid = gameInfo.getFightPlayers().get(id).getUid();
                List<ItemData.Item> itemList = getPkReward(uid, gameInfo.getType(), 1, combo);
                pvpBu.addAllItem(itemList);
                pvpBu.setIsWin(1);
                builder.addPlayerDatas(pvpBu);
            }
        }
        return builder;
    }

    private static MissionData.ReportOverFight.Builder setPkData(GameInfo gameInfo) {
        MissionData.ReportOverFight.Builder builder = MissionData.ReportOverFight.newBuilder();
        int maxId = 0;
        int maxNum = -1;
        for (Map.Entry<Integer, Integer> entry : gameInfo.getScoreMap().entrySet()) {
            if (entry.getValue() > maxNum) {
                maxId = entry.getKey();
                maxNum = entry.getValue();
            } else if (entry.getValue() == maxNum) {
                maxId = -1;
            }
        }
        gameInfo.setWinId(maxId);
        for (Map.Entry<Integer, Integer> entry : gameInfo.getScoreMap().entrySet()) {
            int id = entry.getKey();
            MissionData.PvpOverData.Builder pvpBu = MissionData.PvpOverData.newBuilder();
            pvpBu.setId(id);
            pvpBu.setIntegral(entry.getValue());
            int combo = gameInfo.getComboMap().get(id);
            pvpBu.setCombo(combo);
            int isWin = 0;
            if (maxId == -1) {
                isWin = 2;
            } else {
                isWin = id == maxId ? 1 : 0;
            }
            String uid = gameInfo.getFightPlayers().get(id).getUid();
            List<ItemData.Item> itemList = getPkReward(uid, gameInfo.getType(), isWin, combo);
            pvpBu.addAllItem(itemList);
            pvpBu.setIsWin(isWin);
            builder.addPlayerDatas(pvpBu);
        }
        return builder;
    }

    private static MissionData.ReportOverFight.Builder setPartnerData(GameInfo gameInfo) {
        MissionData.ReportOverFight.Builder builder = MissionData.ReportOverFight.newBuilder();
        int totalScore = gameInfo.getTotalScore();
        Map<String, String> rewardMap = null;
        for (int i = 1; i <= 6; i++) {
            Map<String, String> tmp = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_PARTNERREWARD, i);
            int integral = Integer.parseInt(tmp.get("integral"));
            if (totalScore <= integral) {
                rewardMap = tmp;
                break;
            }
        }
        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            int id = entry.getKey();
            MissionData.PvpOverData.Builder pvpBu = MissionData.PvpOverData.newBuilder();
            pvpBu.setId(id);
            pvpBu.setIntegral(gameInfo.getScoreMap().get(id));
            pvpBu.setCombo(-1);
            String uid = gameInfo.getFightPlayers().get(id).getUid();
            List<ItemData.Item> itemList = getPartnerReward(uid, rewardMap);
            pvpBu.addAllItem(itemList);
            pvpBu.setIsWin(1);
            builder.addPlayerDatas(pvpBu);
        }

        return builder;
    }

    public static void overFight(String key, ChannelHandlerContext ctx) {
        GameInfo gameInfo = syncProgressList.remove(key);
        if (gameInfo == null) {
            return;
        }

        /// System.out.println("========================overFight=======================");

        if (gameInfo.getType() == 1) {
            ScheduledExecutorService service = gameInfo.getService();
            service.shutdown();
        }

        //告诉玩家战斗结束结果
        MissionData.ReportOverFight.Builder builder = null;
        if (ctx != null) {
            if (gameInfo.getType() == 1) {
                builder = setPartnerData(gameInfo);
            } else if (gameInfo.getType() == 2) {
                builder = setPkExitData(gameInfo, ctx);
            }

        } else {
            if (gameInfo.getType() == 1) {
                builder = setPartnerData(gameInfo);
            } else if (gameInfo.getType() == 2) {
                builder = setPkData(gameInfo);
            }
        }

        MissionData.MRequestOverMission.Builder mBuilder = MissionData.MRequestOverMission.newBuilder();
        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {

            FightPlayerInfo fightPlayerInfo = entry.getValue();
            ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTOVERFIGHT_VALUE, builder.build().toByteArray());
            if (gameInfo.getType() == 2) {
                if (gameInfo.getWinId() == -1 || gameInfo.getWinId() == entry.getKey()) {
                    mBuilder.addUids(fightPlayerInfo.getUid());
                }
            }
        }
        //告诉游戏服战斗结束，修改房间状态
        mBuilder.setHall(gameInfo.getHall());
        mBuilder.setRoomId(gameInfo.getRoomId());
        mBuilder.setType(gameInfo.getType());
        //
        byte[] sendBytes = mBuilder.build().toByteArray();
        SuperProtocol response = new SuperProtocol(ProtoData.MToS.REQUESTOVERMISSION_VALUE, sendBytes.length, sendBytes);
        gameServerCtx.writeAndFlush(response);

        //移除玩家加入战斗数据
        for (Map.Entry<Integer, FightPlayerInfo> entry : gameInfo.getFightPlayers().entrySet()) {
            FightPlayerInfo fightPlayerInfo = entry.getValue();
            playerRoomMap.remove(fightPlayerInfo.getCtx());
        }
    }

    private static List<ItemData.Item> getPkReward(String uid, int type, int isWin, int combo) {
        List<ItemData.Item> list = new ArrayList<ItemData.Item>();
        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_PVPREWARD, type);
        int comboNeed = Integer.parseInt(map.get("combo"));
        int comboGive = Integer.parseInt(map.get("combo_item"));
        String[] expStr = map.get("exp").split("\\|");
        String[] coinStr = map.get("coin").split("\\|");
        int other = combo / comboNeed * comboGive;
        int itemId = 0;
        int itemNum = 0;
        int exp = 0;
        int coin = 0;
        String[] strings = null;
        if (isWin == 1) {
            itemId = Integer.parseInt(map.get("win_item"));
            strings = map.get("win_num").split("\\|");
            exp = Integer.parseInt(expStr[0]);
            coin = Integer.parseInt(coinStr[0]);
        } else if (isWin == 0) {
            itemId = Integer.parseInt(map.get("lose_item"));
            strings = map.get("lose_num").split("\\|");
            exp = Integer.parseInt(expStr[1]);
            coin = Integer.parseInt(coinStr[1]);
        } else if (isWin == 2) {//平局
            itemId = Integer.parseInt(map.get("draw_item"));
            strings = map.get("draw_num").split("\\|");
            exp = Integer.parseInt(expStr[2]);
            coin = Integer.parseInt(coinStr[2]);
        }
        itemNum = (int) (Integer.parseInt(strings[0]) + Math.random() * (Integer.parseInt(strings[1]) - Integer.parseInt(strings[0]) + 1));
        itemNum += other;
        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
        itemBu.setId(itemId);
        itemBu.setNum(itemNum);

        ItemData.Item.Builder coinBu = ItemData.Item.newBuilder();
        coinBu.setId(1);
        coinBu.setNum(coin);

        ItemData.Item.Builder expBu = ItemData.Item.newBuilder();
        expBu.setId(25);
        expBu.setNum(exp);

        IItem iItem = ItemDao.getInstance();
        iItem.updateItemInfo(uid, itemId, itemNum);
        iItem.updateItemInfo(uid, 1, coin);
        ILogin iLogin = LoginDao.getInstance();
        //  iLogin.updateRoleExp(uid, exp);

        list.add(itemBu.build());
        list.add(coinBu.build());
        list.add(expBu.build());
        return list;
    }

    private static List<ItemData.Item> getPartnerReward(String uid, Map<String, String> map) {
        List<ItemData.Item> list = new ArrayList<ItemData.Item>();
        int exp = Integer.parseInt(map.get("exp"));
        ItemData.Item.Builder expBu = ItemData.Item.newBuilder();
        expBu.setId(25);
        expBu.setNum(exp);
        ILogin iLogin = LoginDao.getInstance();
        iLogin.updateRoleExp(uid, exp);
        list.add(expBu.build());

        int coin = Integer.parseInt(map.get("coin"));
        ItemData.Item.Builder coinBu = ItemData.Item.newBuilder();
        coinBu.setId(1);
        coinBu.setNum(coin);
        IItem iItem = ItemDao.getInstance();
        iItem.updateItemInfo(uid, 1, coin);
        list.add(coinBu.build());

        String[] items = map.get("item").split("\\|");
        for (int i = 0; i < items.length; i++) {
            int itemId = Integer.parseInt(items[i].split(",")[0]);
            int itemNum = Integer.parseInt(items[i].split(",")[1]);
            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setId(itemId);
            itemBu.setNum(itemNum);
            iItem.updateItemInfo(uid, itemId, itemNum);
            list.add(itemBu.build());
        }
        return list;
    }

    public static List<BallInfo> initFightMap() {
        List<BallInfo> ballList = new ArrayList<BallInfo>();
        for (int i = 0; i < 2; i++) {
            BallInfo ballInfo = setFightBall(i);
            int val = ballInfo.getType() == 1 ? -1 : 1;
            int add = i * 500 * val;
            ballInfo.getPoint().setX(ballInfo.getPoint().getX() + add);
            ballList.add(ballInfo);
        }
//        BallInfo baseBall = BallInfo.getBaseBall();
//        ballList.add(baseBall);
//        BallInfo ballInfo1 = setFightBall(1);
//        ballList.add(ballInfo1);
//        BallInfo ballInfo2 = setFightBall(1);
//        ballInfo2.getPoint().setX(ballInfo2.getPoint().getX()+300);
//        ballList.add(ballInfo2);
//
//        BallInfo ballInfo3 = setFightBall(2);
//        ballList.add(ballInfo3);
//        BallInfo ballInfo4 = setFightBall(2);
//        ballInfo4.getPoint().setX(ballInfo4.getPoint().getX()+300);
//        ballList.add(ballInfo4);
        return ballList;
    }

    public static List<BallInfo> initSpeedFightMap() {
        List<BallInfo> ballList = new ArrayList<BallInfo>();
        int initX = 0;
        int val = 0;
        for (int i = 0; i < 85; i++) {
            BallInfo ballInfo = setSpeedFightBall(i);
            int randX = 0;
            if (i == 0) {
                randX = (int) (300 + Math.random() * (600 - 300 + 1));
            } else {
                randX = (int) (100 + Math.random() * (550 - 100 + 1));
            }
            if (randX <= 200) {
                val += 1;
            } else {
                val = 0;
            }
            if (val > 2) {
                randX = (int) (100 + Math.random() * (650 - 250 + 1));
                val = 0;
            }
            initX += randX;
            ballInfo.getPoint().setX(initX);
            ballList.add(ballInfo);
        }
        return ballList;
    }

    public static BallInfo setFightBall(int ballIndex) {
        //球id策划配置
//        int ballList[] = {10001,10002,10003,10028,10029,10030,10031};

        BallInfo ballInfo = new BallInfo();
        ballInfo.setIndex(ballIndex);
//        int index = (int)(1 + Math.random() * (ballList.length - 1 + 1));
//        int ballId = ballList[index-1];
        int ballId = 20002;
        ballInfo.setBallId(ballId);
        ballInfo.setIntegral(1);
        int type = (int) (1 + Math.random() * (2 - 1 + 1));
//        int type = 2;
        ballInfo.setType(type);
        PointInfo pointInfo = new PointInfo();
        int val = type == 1 ? -1 : 1;
        int initX = type == 1 ? -100 : 1380;
        int randX = (int) (1 + Math.random() * (300 - 1 + 1));
        randX *= val;
        randX += initX;
        int randY = (int) (350 + Math.random() * (550 - 350 + 1));
        pointInfo.setX(randX);
        pointInfo.setY(randY);
        ballInfo.setPoint(pointInfo);

        int speed = (int) (20 + Math.random() * (35 - 20 + 1));
        float f = (float) speed / 10;
        ballInfo.setSpeed(f);
        return ballInfo;
    }

    public static BallInfo setSpeedFightBall(int ballIndex) {
        //球id策划配置
        int ballList[] = {10001, 10002, 10003, 10028, 10029, 10030, 10031};

        BallInfo ballInfo = new BallInfo();
        ballInfo.setIndex(ballIndex);
        int index = (int) (1 + Math.random() * (ballList.length - 1 + 1));
        int ballId = ballList[index - 1];
//        int ballId = 10001;
        ballInfo.setBallId(ballId);
        ballInfo.setIntegral(0);
        ballInfo.setType(1);
        PointInfo pointInfo = new PointInfo();
//        int randX = (int)(1 + Math.random() * (300 - 1 + 1));
//        int randY = (int)(400 + Math.random() * (600 - 400 + 1));
        pointInfo.setX(0);
        pointInfo.setY(225);
        ballInfo.setPoint(pointInfo);
        ballInfo.setSpeed(-1);
        return ballInfo;
    }

    public static void playGoOutMission(ChannelHandlerContext ctx) {
        String key = playerRoomMap.get(ctx);
        if (key != null) {
            overFight(key, ctx);
        }
    }
}