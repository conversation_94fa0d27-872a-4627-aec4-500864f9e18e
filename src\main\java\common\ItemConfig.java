package common;

@ExcelConfigObject(key = "item")
public class ItemConfig {
    public static final int PetUpExpItemType = 1;//合成的宠物
    @ExcelColumn(name = "ID")
    private int id;
    @ExcelColumn(name = "group_max")//单格上限
    private int cellMax;
    @ExcelColumn(name = "max")//数量上限
    private int totalMax;
    @ExcelColumn(name = "bag")//在背包内的位置
    private int bag;
    @ExcelColumn(name = "sort")//排序
    private int sort;
    @ExcelColumn(name = "use")//是否可使用
    private int useAble;
    @ExcelColumn(name = "get_pet")//获得宠物
    private int GetPet;
    @ExcelColumn(name = "get_id")//使用后获得
    private int getId;
    @ExcelColumn(name = "use_other")//需要其他物品
    private int useOther;
    @ExcelColumn(name = "notice")//获得是否公告
    private int isNotice;
    @ExcelColumn(name = "donate")//赠送物品
    private int isDonate;
    @ExcelColumn(name = "num_item")
    private int effectValue;
    @ExcelColumn(name = "player_or_pet")
    private int type;
    @ExcelColumn(name = "function_item")
    private int effectType;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getCellMax() {
        return cellMax;
    }

    public void setCellMax(int cellMax) {
        this.cellMax = cellMax;
    }

    public int getTotalMax() {
        return totalMax;
    }

    public void setTotalMax(int totalMax) {
        this.totalMax = totalMax;
    }

    public int getBag() {
        return bag;
    }

    public void setBag(int bag) {
        this.bag = bag;
    }

    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }

    public int getUseAble() {
        return useAble;
    }

    public void setUseAble(int useAble) {
        this.useAble = useAble;
    }

    public int getGetPet() {
        return GetPet;
    }

    public void setGetPet(int getPet) {
        GetPet = getPet;
    }

    public int getGetId() {
        return getId;
    }

    public void setGetId(int getId) {
        this.getId = getId;
    }

    public int getUseOther() {
        return useOther;
    }

    public void setUseOther(int useOther) {
        this.useOther = useOther;
    }

    public int getIsNotice() {
        return isNotice;
    }

    public void setIsNotice(int isNotice) {
        this.isNotice = isNotice;
    }

    public int getIsDonate() {
        return isDonate;
    }

    public void setIsDonate(int isDonate) {
        this.isDonate = isDonate;
    }

    public int getEffectValue() {
        return effectValue;
    }

    public void setEffectValue(int effectValue) {
        this.effectValue = effectValue;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getEffectType() {
        return effectType;
    }

    public void setEffectType(int effectType) {
        this.effectType = effectType;
    }

    @Override
    public String toString() {
        return "ItemConfig{" +
                "id=" + id +
                ", cellMax=" + cellMax +
                ", totalMax=" + totalMax +
                ", bag=" + bag +
                ", sort=" + sort +
                ", useAble=" + useAble +
                ", GetPet=" + GetPet +
                ", getId=" + getId +
                ", useOther=" + useOther +
                ", isNotice=" + isNotice +
                ", isDonate=" + isDonate +
                ", effectValue=" + effectValue +
                ", type=" + type +
                ", effectType=" + effectType +
                '}';
    }
}
