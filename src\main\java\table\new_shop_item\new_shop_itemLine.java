package table.new_shop_item;

import table.LineKey;

public class new_shop_itemLine implements LineKey {
    public int id;
    public String Product_ID;
    public String type;
    public String item;
    public String design;
    public String item_num;
    public String coid;
    public String coid_num;
    public String icon;
    public String diamond_num;
    @Override
    public String toString() {
        return "new_shop_itemLine{" +
                "id=" + id +
                ", Product_ID='" + Product_ID + '\'' +
                ", type='" + type + '\'' +
                ", item='" + item + '\'' +
                ", design='" + design + '\'' +
                ", item_num='" + item_num + '\'' +
                ", coid='" + coid + '\'' +
                ", coid_num='" + coid_num + '\'' +
                ", icon='" + icon + '\'' +
                ", diamond_num='" + diamond_num + '\'' +
                '}';
    }

    @Override
    public int Key() {
        return id;
    }
}
