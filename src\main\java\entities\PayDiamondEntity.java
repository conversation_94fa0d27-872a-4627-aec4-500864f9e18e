package entities;

import javax.persistence.*;

@Entity
@Table(name = "paydiamond", schema = "", catalog = "super_star_fruit")
public class PayDiamondEntity {
    private int id;
    private String uid;
    private Long diamond_num;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "diamond_num")
    public Long getDiamond_num() {
        return diamond_num;
    }

    public void setDiamond_num(Long diamond_num) {
        this.diamond_num = diamond_num;
    }
}
