package entities;


import protocol.EquipAData;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "equida", schema = "", catalog = "super_star_fruit")
public class EquipAEntity {
    private int id;
    private int eid;
    private String uid;
    private int hp;//体力
    private int ad;//物攻
    private int ap;//法攻
    private int arm;//物防
    private int mdf;//法防
    private int sp;//速度
    private int Ehp;//体力
    private int Ead;//物攻
    private int Eap;//法攻
    private int Earm;//物防
    private int Emdf;//法防
    private int Esp;//速度
    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "eid")
    public int getEid() {
        return eid;
    }

    public void setEid(int eid) {
        this.eid = eid;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "hp")
    public int getHp() {
        return hp;
    }

    public void setHp(int hp) {
        this.hp = hp;
    }

    @Basic
    @Column(name = "ad")
    public int getAd() {
        return ad;
    }

    public void setAd(int ad) {
        this.ad = ad;
    }

    @Basic
    @Column(name = "ap")
    public int getAp() {
        return ap;
    }

    public void setAp(int ap) {
        this.ap = ap;
    }

    @Basic
    @Column(name = "arm")
    public int getArm() {
        return arm;
    }

    public void setArm(int arm) {
        this.arm = arm;
    }

    @Basic
    @Column(name = "mdf")
    public int getMdf() {
        return mdf;
    }

    public void setMdf(int mdf) {
        this.mdf = mdf;
    }

    @Basic
    @Column(name = "sp")
    public int getSp() {
        return sp;
    }

    public void setSp(int sp) {
        this.sp = sp;
    }

    @Basic
    @Column(name = "ehp")
    public int getEhp() {
        return Ehp;
    }

    public void setEhp(int ehp) {
        Ehp = ehp;
    }

    @Basic
    @Column(name = "ead")
    public int getEad() {
        return Ead;
    }

    public void setEad(int ead) {
        Ead = ead;
    }

    @Basic
    @Column(name = "eap")
    public int getEap() {
        return Eap;
    }

    public void setEap(int eap) {
        Eap = eap;
    }

    @Basic
    @Column(name = "earm")
    public int getEarm() {
        return Earm;
    }

    public void setEarm(int earm) {
        Earm = earm;
    }

    @Basic
    @Column(name = "emdf")
    public int getEmdf() {
        return Emdf;
    }

    public void setEmdf(int emdf) {
        Emdf = emdf;
    }

    @Basic
    @Column(name = "esp")
    public int getEsp() {
        return Esp;
    }

    public void setEsp(int esp) {
        Esp = esp;
    }



    @Override
    public String toString() {
        return "EquipAEntity{" +
                "id=" + id +
                ", eid=" + eid +
                ", uid='" + uid + '\'' +
                ", hp=" + hp +
                ", ad=" + ad +
                ", ap=" + ap +
                ", arm=" + arm +
                ", mdf=" + mdf +
                ", sp=" + sp +
                ", Ehp=" + Ehp +
                ", Ead=" + Ead +
                ", Eap=" + Eap +
                ", Earm=" + Earm +
                ", Emdf=" + Emdf +
                ", Esp=" + Esp +
                '}';
    }
}
