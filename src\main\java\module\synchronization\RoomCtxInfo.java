package module.synchronization;

import io.netty.channel.ChannelHandlerContext;

/**
 * Created by nara on 2018/6/4.
 */
public class RoomCtxInfo {
    private ChannelHandlerContext ctx;
    private int roomId;
    private int hall;//1初级 2高级
    private int type;//1合作 2竞速模式

    public RoomCtxInfo(ChannelHandlerContext ctx){
        this.ctx = ctx;
        roomId = 0;
        type = 0;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getHall() {
        return hall;
    }

    public void setHall(int hall) {
        this.hall = hall;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }
}
