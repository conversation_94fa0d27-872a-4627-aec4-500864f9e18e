package module.robot;

import common.SuperConfig;
import entities.PartEntity;
import manager.Redis;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class ranAccount {
    private static Redis jedis = Redis.getInstance();
    private static int isOne;
    private static String id;
    private static List<String> one=new ArrayList<String>();
    private static List<String> two=new ArrayList<String>();
    private static List<String> three=new ArrayList<String>();
    private static List<String> four=new ArrayList<String>();
    private static List<String> five=new ArrayList<String>();
    private static List<String> six=new ArrayList<String>();
    private static List<String> seven=new ArrayList<String>();
    private static List<String> eight=new ArrayList<String>();
    private static List<String> nine=new ArrayList<String>();

    public static PartEntity dress(PartEntity partEntity){
        one.clear();two.clear();three.clear();four.clear();five.clear();six.clear();seven.clear();eight.clear();nine.clear();
        Set<String> tasksize = jedis.keys(SuperConfig.REDIS_EXCEL_DRESS+"*");
        int num=ranName.ranNum(1,3);
       // /// System.out.println(tasksize.size());
        for(int row=1;row<tasksize.size();row++){
            if(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, row+"", "type")!=null){
                int getType=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, row + "", "get_choose"));
                if(getType==4){
                    continue;
                }
                isOne=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, row + "", "type"));
                id=Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, row + "", "id");
            }
            if(1==isOne){
                one.add(id);
            }else if(2==isOne){
                two.add(id);
            }else if(3==isOne){
                three.add(id);
            }else if(4==isOne){
                four.add(id);
            }else if(5==isOne){
                five.add(id);
            }else if(6==isOne){
                six.add(id);
            }else if(7==isOne){
                seven.add(id);
            }else if(8==isOne){
                eight.add(id);
            }else if(9==isOne){
                nine.add(id);
            }
        }

        switch (num){
            case 1:
                partEntity.setRoleid(1);
                partEntity.setPart1(0);
                partEntity.setPart2(Integer.parseInt(ranName.ranList(two)));
                partEntity.setPart3(Integer.parseInt(ranName.ranList(three)));
                partEntity.setPart4(Integer.parseInt(ranName.ranList(four)));
                partEntity.setPart5(Integer.parseInt(ranName.ranList(five)));
                partEntity.setPart6(Integer.parseInt(ranName.ranList(six)));
                partEntity.setPart7(Integer.parseInt(ranName.ranList(seven)));
                partEntity.setPart8(Integer.parseInt(ranName.ranList(eight)));
                partEntity.setPart9(Integer.parseInt(ranName.ranList(nine)));
                break;
            case 2:
                partEntity.setRoleid(2);
                partEntity.setPart1(Integer.parseInt(ranName.ranList(one)));
                partEntity.setPart2(Integer.parseInt(ranName.ranList(two)));
                partEntity.setPart3(0);
                partEntity.setPart4(0);
                partEntity.setPart5(0);
                partEntity.setPart6(Integer.parseInt(ranName.ranList(six)));
                partEntity.setPart7(Integer.parseInt(ranName.ranList(seven)));
                partEntity.setPart8(Integer.parseInt(ranName.ranList(eight)));
                partEntity.setPart9(Integer.parseInt(ranName.ranList(nine)));
                break;
            case 3:
                partEntity.setRoleid(3);
                partEntity.setPart1(0);
                partEntity.setPart2(Integer.parseInt(ranName.ranList(two)));
                partEntity.setPart3(0);
                partEntity.setPart4(0);
                partEntity.setPart5(0);
                partEntity.setPart6(Integer.parseInt(ranName.ranList(six)));
                partEntity.setPart7(Integer.parseInt(ranName.ranList(seven)));
                partEntity.setPart8(Integer.parseInt(ranName.ranList(eight)));
                partEntity.setPart9(Integer.parseInt(ranName.ranList(nine)));
                break;
        }
        return partEntity;
    }
}
