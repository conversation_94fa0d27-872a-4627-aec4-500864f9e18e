package module.role;


import com.google.protobuf.InvalidProtocolBufferException;
import entities.ItemEntity;
import entities.RoleEntity;
import manager.MySql;
import manager.ReportManager;
import model.LoginInfo;
import module.item.ItemDao;
import module.item.ItemUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.*;

import java.util.List;

public class RoleService {
    private static Logger log = LoggerFactory.getLogger(RoleService.class);
    private static RoleService inst = null;

    public static RoleService getInstance() {
        if (inst == null) {
            inst = new RoleService();
        }
        return inst;
    }

    public byte[] OperateRoleExp(byte[] bytes, String uid) {
        RoleData.RequestOperateRole requestRoleExp = null;
        RoleData.ResponseOperateRole.Builder builder = RoleData.ResponseOperateRole.newBuilder();
        try {
            requestRoleExp = RoleData.RequestOperateRole.parseFrom(bytes);
            RoleDao roleDao = new RoleDao();
            RoleEntity roleEntity = roleDao.queryRole(uid);
            int roleExp;
            roleExp = requestRoleExp.getRoleExp() + roleEntity.getExp();
            roleEntity = RoleUtils.addRoleExp(roleEntity, requestRoleExp.getRoleExp());
//            builder.addRole(RoleEntity.entityToPb(roleEntity));
            RoleData.PlainRole role = RoleEntity.entityToPb(roleEntity);
            Integer lv = role.getRoleCurrentLV();
            int stamina = 0;
            for (int i = lv; i <= 20; i++) {
                stamina += lv + 20;
            }
            System.out.println(stamina);
            //=======================================================

            //=======================================================

        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] getRoleExp(byte[] bytes, String uid) {
        RoleData.ResponseGetRoleExp.Builder builder = RoleData.ResponseGetRoleExp.newBuilder();
        builder.setErrorId(0);
        try {
            RoleDao roleDao = RoleDao.getInstance();
            List<Object> list = roleDao.query(uid);
            for (int i = 0; i < list.size(); i++) {
                RoleEntity entity = (RoleEntity) list.get(i);
                builder.addRole(RoleEntity.entityToPb(entity));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestRoleSex(byte[] bytes, String uid) {
        SexData.RequestRoleSex requestRoleSex = null;
        SexData.ResponseRoleSex.Builder builder = SexData.ResponseRoleSex.newBuilder();
        SexData.RoleSex.Builder sex = SexData.RoleSex.newBuilder();
        try {
            requestRoleSex = SexData.RequestRoleSex.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestRoleSex != null) {
            builder.setErrorId(1);
            sex.setSex(requestRoleSex.getSex());
            builder.setSex(sex);
            List<Object> role = RoleDao.getInstance().queryOne(uid);
            int sex1 = sex.getSex();
            System.out.println(role);
            StringBuilder append = new StringBuilder("update RoleEntity set ")
                    .append("sex ='")
                    .append(sex1)
                    .append("'where uid ='")
                    .append(uid).append("'");
            MySql.updateSomes(append.toString());

//           StringBuilder s =    new StringBuilder("update role set sex = <new_sex_value> where uid =").append(uid);
//            MySql.updateSomes(s.toString());
        } else {
            log.error(":[RequestRoleSex] error");
            builder.setErrorId(0);
            sex.setSex(-1);
            builder.setSex(sex);

        }

        return builder.build().toByteArray();
    }


    public byte[] RequestGetSex(byte[] bytes, String uid) {
        SexData.RequestGetSex requestGetSex = null;
        SexData.ResponseGetSex.Builder builder = SexData.ResponseGetSex.newBuilder();
        try {
            builder.setErrorId(-1);
//            RoleDao roleDao = RoleDao.getInstance();
            List<Object> role = RoleDao.getInstance().queryOne(uid);
            for (int i = 0; i < role.size(); i++) {
                RoleEntity roleEntity = (RoleEntity) role.get(i);
                builder.setSex(RoleEntity.entity(roleEntity));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }
}
