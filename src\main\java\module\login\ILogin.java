package module.login;

import entities.RoleEntity;
import io.netty.channel.ChannelHandlerContext;
import model.*;
import module.callback.CallBack;
import net.sf.json.JSONObject;
import protocol.EquipData;
import protocol.RankData;
import protocol.TaskData;
import protocol.UserData;

import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2017/11/17.
 */
public interface ILogin {
    String getAnnouncement();
    List<UserData.Server> getServerList(int type);
    int register(String account,String pwd,String phone,String idfa);
    int  visitorRegister(String account,String pwd,String phone,String idfa);
    LoginJudgeAccount getLoginRoleInfo(int serverId,int type,String param,String pwd);
    LoginJudgeAccount getRoleAllInfoFromDB(int serverId,int type,String param,String pwd);
    UserData.Role roleInfoToUserData(RoleInfo roleInfo);
    RoleInfo setInitRoleInfo(String userId,String name);
    void setRoleToUser(RoleInfo roleInfo);
    UserData.ResponseSetInfo.Builder setRoleBaseInfo(String uid,int type,String info);
    List<Integer> updateRoleExp(String uid,int addExp);
    void updateAction(String uid,int addAction,String actionStamp);
    UserData.ResponseChooseRole.Builder chooseRole(String uid,int type,int roleId,int payChoice);
    int changeDress(String uid, UserData.Part part);
//    int changeEquip(String uid, EquipData.Equip equip);
    List<String> useAction(String uid,int useNum);
    void operateView(String uid,String views);
    RoleEntity getRoleEntityFromRedis(String uid);
    void getRoleEntityFromDB(String uid,CallBack callBack,int type);
    void getRoleEntityFromDBById(int id,CallBack callBack,int type);
    void itemsToRedis(String uid,Map<Integer,List<ItemInfo>> itemMap);
    int getRoleOnlineStatus(String uid);
    List<FriendInfo> getFriendInfoFromRedis(String uid);
    int judgeName(String name);
    UserData.ResponseBuyClothing.Builder buyClothing(String uid,int type,List<UserData.Clothing> clothingList);
    UserData.ResponseChangeCupboard.Builder changeCupboard(String uid,int id,int type,UserData.Part part);
//    EquipData.ResponseChangeEquip.Builder changeEquip(String uid, int id, int type, EquipData.Equip equip);
    int cupboardToBody(String uid,int cupboardId);
    UserData.ResponseStarbiToMoney.Builder starbiToMoney(String uid);
    void stationLocation(ChannelHandlerContext ctx,String uid, UserData.PointDouble point, UserData.PointDouble direction,double speed);
    int getIdFromUid(String uid);
    int getStationFromUid(String uid);
    String getNameFromUid(String uid);
    int getHeadFromUid(String uid);
    int getLvFromUid(String uid);
    String getRoleIdFromUid(String uid);
    List<TaskInfo> getTaskInfoFromRedis(String uid,int type);
    RoleDressInfo getRoleDressFromRedis(String key);
    CupboardInfo getCupboardInfo(List<CupboardInfo> cupboardList,int id);
    CupboardInfo getCurrentDressOnLine(String uid);
    List<RoleDressInfo> getUserAllDressFromRedis(String uid);
    boolean updateNewUser(String uid,int advance);
    Map<String,FriendInStationInfo> getStationFriends(String uid,int stationId,List<FriendInfo> friendInfoList);
    int getStationId(String uid);
    void setPlayerType(JSONObject jsonObject,ChannelHandlerContext ctx);
    int getFirstRecharge(String uid);
    void setFirstRecharge(String uid);
//   UserData.ResponseApproval.Builder approval(String uid,int recognized,int type);
   UserData.ResponseGetLuckyItem.Builder getLuckyItem(String uid,int id);
   TaskData.ResponseFinishActivities.Builder finishActivities(String uid, int id);
    UserData.ResponseMarketInformation.Builder getMarketInformation(String uid);
   UserData.ResponseGoodsOperate.Builder goodsOperate(int type,int goodsId,int nums,String uid);
    UserData.ResponseTickteExchange.Builder tickteExchange(int id,int nums,String uid);
    UserData.ResponseQueryRoleInformation.Builder queryRoleInformation(int roleId);
    UserData.ResponseWaitItem.Builder waitItem(String uid);
    RankData.ResponseRank.Builder rank(String uid, int mold);
}