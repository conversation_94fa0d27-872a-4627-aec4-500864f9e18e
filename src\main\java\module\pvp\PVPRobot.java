package module.pvp;

import entities.PVPPetsEntity;
import protocol.PVPData;
import table.pvp_robot.*;

public class PVPRobot {
    private static PVPRobot inst = null;
    private PVPRobot() {
    }
    public static PVPRobot getInstance() {
        if (inst == null) {
            inst = new PVPRobot();
        }
        return inst;
    }

    // 根据 roleUid 获取PVP编队
    public protocol.PVPData.PVPTeam.Builder GetPVPRobot(){
        PVPData.PetData.Builder[] petDatas = GetPets();

        protocol.PVPData.PVPTeam.Builder value = PVPData.PVPTeam.newBuilder();
        value.setPet1(petDatas[0]);
        value.setPet2(petDatas[1]);
        value.setPet3(petDatas[2]);
        value.setPet4(petDatas[3]);
        value.setPet5(petDatas[4]);
        return value;
    }
    private PVPData.PetData.Builder[] GetPets() {
        pvp_robotLine robotLine = pvp_robotTable.getInstance().GetRandom();
        PVPData.PetData.Builder[] petDatas = new PVPData.PetData.Builder[5];
        petDatas[0] = GetPet(robotLine.petIds[0], Integer.parseInt(robotLine.currentLevel));
        petDatas[1] = GetPet(robotLine.petIds[1], Integer.parseInt(robotLine.currentLevel));
        petDatas[2] = GetPet(robotLine.petIds[2], Integer.parseInt(robotLine.currentLevel));
        petDatas[3] = GetPet(robotLine.petIds[3], Integer.parseInt(robotLine.currentLevel));
        petDatas[4] = GetPet(robotLine.petIds[4], Integer.parseInt(robotLine.currentLevel));
        return petDatas;
    }

    private PVPData.PetData.Builder GetPet(int petIds, int currentLevel) {
        protocol.PVPData.PetData.Builder value = PVPData.PetData.newBuilder();
        value.setUid(0);
        value.setId(petIds);
        value.setNameItem("测试");
        value.setCurrentLevel(currentLevel);

        value.setHp(currentLevel*10);
        value.setSp(100);
        value.setAttack(currentLevel/10);
        value.setDefense(currentLevel/10);
        value.setSpecialAttack(currentLevel/10);
        value.setSpecialDefense(currentLevel/10);
        value.setSpeed(currentLevel/2);

        value.setAvoid(5);
        value.setHid(5);
        value.setCrt(5);

        value.setTypeone(1);
        return value;
    }

    private PVPData.PetData.Builder GetPet() {
        protocol.PVPData.PetData.Builder value = PVPData.PetData.newBuilder();
        value.setUid(0);
        value.setId(74);
        value.setNameItem("测试");
        value.setCurrentLevel(1);

        value.setHp(10);
        value.setSp(10);
        value.setAttack(1);
        value.setDefense(2);
        value.setSpecialAttack(2);
        value.setSpecialDefense(1);
        value.setSpeed(3);

        value.setAvoid(5);
        value.setHid(5);
        value.setCrt(5);

        value.setTypeone(1);
        return value;
    }

}
