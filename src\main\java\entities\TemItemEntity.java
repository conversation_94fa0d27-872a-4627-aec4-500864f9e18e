package entities;

import javax.persistence.*;

@Entity
@Table(name = "tem_item", schema = "", catalog = "super_star_fruit")
public class TemItemEntity {
    private int id;
    private String uid;

    private int wood;
    private int leaf;
    private int water;
    private int soil;

    private int battle;
    private int run;
    private int puzzle;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
    @Basic
    @Column(name = "wood")
    public int getWood() {
        return wood;
    }

    public void setWood(int wood) {
        this.wood = wood;
    }
    @Basic
    @Column(name = "leaf")
    public int getLeaf() {
        return leaf;
    }

    public void setLeaf(int leaf) {
        this.leaf = leaf;
    }
    @Basic
    @Column(name = "water")
    public int getWater() {
        return water;
    }

    public void setWater(int water) {
        this.water = water;
    }
    @Basic
    @Column(name = "soil")
    public int getSoil() {
        return soil;
    }

    public void setSoil(int soil) {
        this.soil = soil;
    }
    @Basic
    @Column(name = "battle")
    public int getBattle() {
        return battle;
    }

    public void setBattle(int battle) {
        this.battle = battle;
    }
    @Basic
    @Column(name = "run")
    public int getRun() {
        return run;
    }

    public void setRun(int run) {
        this.run = run;
    }
    @Basic
    @Column(name = "puzzle")
    public int getPuzzle() {
        return puzzle;
    }

    public void setPuzzle(int puzzle) {
        this.puzzle = puzzle;
    }
}
