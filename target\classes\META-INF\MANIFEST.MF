Manifest-Version: 1.0
Main-Class: superStar
Class-Path: jackson-core-2.10.1.jar javassist-3.20.0-GA.jar json-lib-2
 .4-jdk15.jar slf4j-log4j12-1.7.12.jar commons-codec-1.10.jar jdom-1.1
 .3.jar jackson-databind-2.10.1.jar spring-aop-5.2.2.RELEASE.jar bcpro
 v-jdk15on-1.62.jar spring-expression-5.2.2.RELEASE.jar geronimo-jta_1
 .1_spec-1.1.1.jar hibernate-commons-annotations-5.0.1.Final.jar hiber
 nate-jpa-2.1-api-1.0.0.Final.jar c3p0-*******.jar el-api-2.2.jar http
 client-4.5.5.jar commons-lang-2.5.jar mchange-commons-java-0.2.15.jar
  jackson-annotations-2.10.1.jar xstream-1.4.10.jar ezmorph-1.0.6.jar 
 spring-core-5.2.3.RELEASE.jar antlr-2.7.7.jar alipay-sdk-java-4.9.9.j
 ar httpmime-4.5.5.jar spring-jcl-5.2.3.RELEASE.jar jboss-logging-3.3.
 0.Final.jar log4j-1.2.17.jar commons-pool2-2.4.2.jar slf4j-api-1.7.12
 .jar netty-all-4.1.17.Final.jar commons-collections-3.2.1.jar hiberna
 te-core-5.2.6.Final.jar cdi-api-1.1.jar httpcore-4.4.1.jar fastjson-1
 .2.50.jar classmate-1.3.0.jar spring-web-5.2.3.RELEASE.jar spring-bea
 ns-5.2.3.RELEASE.jar commons-beanutils-1.8.0.jar xmlpull-*******.jar 
 gson-2.6.2.jar commons-logging-1.1.1.jar protobuf-java-format-1.2.jar
  protobuf-java-2.5.0.jar jsr250-api-1.0.jar javax.inject-1.jar dom4j-
 1.6.1.jar hibernate-c3p0-5.4.10.Final.jar jjwt-0.9.0.jar jboss-interc
 eptors-api_1.1_spec-1.0.0.Beta1.jar mysql-connector-java-5.1.6.jar sp
 ring-context-5.2.2.RELEASE.jar xpp3_min-1.1.4c.jar jandex-2.0.3.Final
 .jar jedis-2.9.0.jar
Main-Class: superStar

