package module.playerstatus;

import entities.ItemEntity;
import entities.RoleEntity;
import manager.ReportManager;
import module.item.ItemDao;
import module.role.RoleDao;
import org.hibernate.NonUniqueResultException;
import protocol.ItemData;
import protocol.ProtoData;
import protocol.TemperData;
import server.SuperServerHandler;
import table.character_xp.character_xpTable;

public class PlayerOnlineTimer {
    private static PlayerOnlineTimer inst = null;
    public static PlayerOnlineTimer getInstance() {
        if (inst == null) {
            inst = new PlayerOnlineTimer();
        }
        return inst;
    }
    public void PerSecondRun(){
        TemperatureGetItem();
        TemperatureDeleteItem();
        try {
            BatteryRefresh();
        } catch(NonUniqueResultException ee){
            System.err.println("非单条!!!!!!!!!!!!");
        } catch (Exception e){
            System.err.println("温度层刷新报错");
            e.printStackTrace();
        }

    }
    // 每多少秒刷新
    public final long BatteryRefreshMaxNum = 3600;
    private long BatteryRefreshCurNum = 0;
    // 道具id
    private final int BatteryId = 3;
    // 每次刷新领取数量
    public final int BatteryIncreaseNum = 1;
    private void BatteryRefresh(){
        BatteryRefreshCurNum++;
        if (BatteryRefreshCurNum < BatteryRefreshMaxNum){
            return;
        }
        BatteryRefreshCurNum = 0;

        //ItemData.Item.Builder builder=ItemData.Item.newBuilder();
        for (String uid :
                SuperServerHandler.linkMap.values()) {
            System.out.print("每六十分钟体力添加");
            AddBattery(uid, BatteryIncreaseNum);

        }
    }


    public void AddBattery(String uid, long addNum){
        System.err.println("AddBattery!!!!!!!!!!!!!!!!!!");
        ItemData.Item.Builder builder=ItemData.Item.newBuilder();

        // 当前等级的最大体力数ItemDao.getInstance().updateItemInfo(uid, BatteryId, maxBattery);rint
        long maxBattery = 0;
        // 当前体力
        long curBattery = 0;
        RoleEntity player = RoleDao.getInstance().queryRole(uid);
           System.out.println("Player lv:" + player.getLv());
        maxBattery = character_xpTable.getInstance().GetItem(player.getLv()).actionlimt;

//            System.err.println("玩家：" + uid);
        ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, BatteryId);
        if (itemEntity != null){
                System.err.println("Battery num:" + itemEntity.getItemnum());
            curBattery = (int) itemEntity.getItemnum();

            if (curBattery == maxBattery){
                System.out.println("Battery Full");
                return;
            }

            if (curBattery + addNum <= maxBattery){
                ItemDao.getInstance().updateItemInfo(uid, BatteryId, addNum);
                curBattery += addNum;
                System.out.println("Add Battery");
            }else {
                itemEntity.setItemnum(maxBattery);
                ItemDao.getInstance().update(itemEntity);
                curBattery = maxBattery;
            }
        }else {
                System.err.println("No Battery");
                if(ItemDao.getInstance().queryItemGildCoins(uid, BatteryId)==null)
                {
                    ItemDao.getInstance().updateItemInfo(uid, BatteryId, maxBattery);
                    curBattery = maxBattery;
                }
        }
        System.err.println("Do you have battery?");
        ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
        builder.setId(BatteryId);
        builder.setNum(curBattery);
        reportBuilder.addItem(builder);
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
    }

    // 240
    private final long TemperatureGetItemMaxNum = 240;
    private long TemperatureGetItemCurNum = 0;
    // 温度层活得
    private void TemperatureGetItem(){
        TemperatureGetItemCurNum++;
        if (TemperatureGetItemCurNum < TemperatureGetItemMaxNum){
            return;
        }
        TemperatureGetItemCurNum = 0;

//        System.err.println("每分钟刷新温度层");
        for (String uid :
                SuperServerHandler.linkMap.values()) {
//            System.err.println("发送消息：" + uid);
            TemperData.ResponseTemper.Builder attachment = TemperData.ResponseTemper.newBuilder();
            attachment.setGetItem(true);
            ReportManager.reportInfo(uid,ProtoData.SToC.RESPONSETemper_VALUE,attachment.build().toByteArray());
        }
    }

    // 3600
    public final long TemperatureDeleteItemMaxNum = 3600;
    private long TemperatureDeleteItemCurNum = 0;
    // 温度层刪除
    private void TemperatureDeleteItem(){
        TemperatureDeleteItemCurNum++;
        if (TemperatureDeleteItemCurNum < TemperatureDeleteItemMaxNum){
            return;
        }
        TemperatureDeleteItemCurNum = 0;

//        System.err.println("每分钟刷新温度层");
        for (String uid :
                SuperServerHandler.linkMap.values()) {
//            System.err.println("发送消息：" + uid);
            TemperData.ResponseTemper.Builder attachment = TemperData.ResponseTemper.newBuilder();
            attachment.setDeleteItem(true);
            ReportManager.reportInfo(uid,ProtoData.SToC.RESPONSETemper_VALUE,attachment.build().toByteArray());
        }
    }
}
