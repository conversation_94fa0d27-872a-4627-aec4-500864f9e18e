package entities;

import javax.persistence.*;
import java.util.Date;


@Entity
@Table(name = "complete_achievement", schema = "", catalog = "super_star_fruit")
public class CompleteAchievementEntity {
    private int id;
    private String uid;
    private Date time;
    private int achievementId;
    private boolean isComplete;
    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
    @Basic
    @Column(name = "time")
    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }
    @Basic
    @Column(name = "achievement_id")
    public int getAchievementId() {
        return achievementId;
    }

    public void setAchievementId(int dailyTaskId) {
        this.achievementId = dailyTaskId;
    }
    @Basic
    @Column(name = "is_complete")
    public boolean isComplete() {
        return isComplete;
    }

    public void setComplete(boolean complete) {
        isComplete = complete;
    }

    @Override
    public String toString() {
        return "CompleteAchievementEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", time=" + time +
                ", dailyTaskId=" + achievementId +
                ", isComplete=" + isComplete +
                '}';
    }
}
