package module.room;

import com.googlecode.protobuf.format.JsonFormat;
import common.SuperConfig;
import entities.PartEntity;
import entities.RoleEntity;
import manager.*;
import model.CupboardInfo;
import model.PointDoubleInfo;
import model.RoleDressInfo;
import module.login.ILogin;
import module.login.LoginDao;
import module.mission.IMission;
import module.mission.MissionDao;
import module.robot.ranName;
import module.synchronization.RoomInfo;
import module.synchronization.RoomRoleInfo;
import module.synchronization.RoomManager;
import module.synchronization.match.RoomShowInfo;
import module.task.ITask;
import module.task.TaskDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MissionData;
import protocol.ProtoData;
import protocol.UserData;
import redis.clients.jedis.Pipeline;
import utils.MyUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by nara on 2018/6/1.
 */
public class RoomDao implements IRoom {
    private static Logger log = LoggerFactory.getLogger(RoomDao.class);
    private static RoomDao inst = null;
    public static RoomDao getInstance() {
        if (inst == null) {
            inst = new RoomDao();
        }
        return inst;
    }

    public MissionData.ResponseJoinRoom.Builder joinRoom(String uid,int type,int id,String pwd){
        MissionData.ResponseJoinRoom.Builder builder = MissionData.ResponseJoinRoom.newBuilder();
        builder.setId(id);
        int nowRoomId = RoomManager.getRoleRoomId(uid);
        if(id >= 0){
            if (nowRoomId != 0){
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid+":[joinRoom] error 1>>>nowRoomId:"+nowRoomId);
                return builder;
            }
            int hall = RoomManager.getRoleHall(uid);
            int roomId = id;
            if (id > 0){
                int val = RoomManager.judgePassword(type,hall, roomId, pwd);
                if (val == 1){
                    builder.setErrorId(ProtoData.ErrorCode.ROOMNOTEXIT_VALUE);
                    log.error(uid+":[joinRoom] error 2>>>id:"+id);
                    return builder;
                }else if (val == 2){
                    builder.setErrorId(ProtoData.ErrorCode.ROOMPWDERROR_VALUE);
                    log.error(uid+":[joinRoom] error 3>>>id:"+id);
                    return builder;
                }
            }if (id == 0){//快速加入
                roomId = RoomManager.randRoomId(type,hall);
            }
            if (roomId == 0){
                builder.setErrorId(ProtoData.ErrorCode.ROOMFULL_VALUE);
                log.error(uid+":[joinRoom] error 4");
                return builder;
            }
            RoomRoleInfo roomRoleInfo = setRoomRoleInfo(uid,2);
            RoomInfo roomInfo = RoomManager.joinRoom(uid, type,roomId, roomRoleInfo);
            if (roomInfo == null){
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid+":[joinRoom] error 5>>>id:"+id);
            }else {
                MissionData.Room.Builder roomBu = MissionData.Room.newBuilder();
                roomBu.setId(roomId);
                for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
                    RoomRoleInfo info = roomInfo.getRoomRoleList().get(i);
                    MissionData.RoomRole roomRole = RoomRoleInfo.toRoomRoleData(info);
                    roomBu.addRoomRoleList(roomRole);
                }
                builder.setRoom(roomBu);
            }
        }else {
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[joinRoom] error 6>>>id:"+id);
        }
        return builder;
    }

    private RoomRoleInfo setRoomRoleInfo(String uid,int type){
        RoomRoleInfo roomRoleInfo = new RoomRoleInfo();
        roomRoleInfo.setUid(uid);
        roomRoleInfo.setType(type);
        roomRoleInfo.setStatus(0);
        ILogin iLogin = LoginDao.getInstance();
        RoleEntity roleEntity = iLogin.getRoleEntityFromRedis(uid);
        roomRoleInfo.setPlayerId(roleEntity.getId());
        roomRoleInfo.setName(roleEntity.getName());
        roomRoleInfo.setLv(roleEntity.getLv());
        roomRoleInfo.setHead(roleEntity.getHead());
        PointDoubleInfo pointDoubleInfo = new PointDoubleInfo();
        pointDoubleInfo.setX(0);
        pointDoubleInfo.setY(0);
        roomRoleInfo.setPoint(pointDoubleInfo);
        return roomRoleInfo;
    }

    public MissionData.ResponseCreateRoom.Builder createRoom(String uid,int type,String name,String pwd){
        MissionData.ResponseCreateRoom.Builder builder = MissionData.ResponseCreateRoom.newBuilder();
        int nowRoomId = RoomManager.getRoleRoomId(uid);
        if (nowRoomId > 0){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid + ":[createRoom] error 1");
            return builder;
        }

        RoomRoleInfo roomRoleInfo = setRoomRoleInfo(uid,1);
        roomRoleInfo.setQueue(1);
        roomRoleInfo.setPosition(1);
        roomRoleInfo.setStatus(1);
        if (name.length() > 8){
            name = name.substring(0,8);
        }
        int val = RoomManager.addRoom(uid, roomRoleInfo, name, pwd,type);
        if (val == -1){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid + ":[createRoom] error 2");
        }else if (val == -2){
            builder.setErrorId(ProtoData.ErrorCode.NOTINHALL_VALUE);
            log.error(uid + ":[createRoom] error 3");
        }else {
            builder.setName(name);
            if (pwd == null || pwd.equals("")){
                builder.setIsSecret(0);
            }else {
                builder.setIsSecret(1);
            }
            MissionData.Room.Builder roomBu = MissionData.Room.newBuilder();
            roomBu.setId(val);
            MissionData.RoomRole roomRole = RoomRoleInfo.toRoomRoleData(roomRoleInfo);
            roomBu.addRoomRoleList(roomRole);
            builder.setRoom(roomBu);
        }

        return builder;
    }

    public static void createVirtualRoom(){
        Map<Integer,Map<Integer,RoomInfo>> roomInfoMap =RoomManager.roomInfoMap2;
        for(int i=0;i<20;i++){
            int roomId = RoomManager.getRoomId(2);
            RoomInfo roomInfo = new RoomInfo();
            roomInfo.setType(2);
            roomInfo.setName(HandlerRobotMatch.roomNamePool.get((int)(Math.random()*HandlerRobotMatch.roomNamePool.size())));
            roomInfo.setPwd("");
           roomInfo.setIsPlaying(Math.random()<=0.8?false:true);
            roomInfo.setRobot(true);

            List<RoomRoleInfo> roomRoleList=new ArrayList<RoomRoleInfo>();
            //RoomRoleInfo roomRoleInfo=new RoomRoleInfo();
            RoomRoleInfo roomRoleInfo=new HandlerRobotMatch().setRoomRobotRoleInfo(1);
            roomRoleInfo.setType(1);
            roomRoleInfo.setPosition(1);
            roomRoleInfo.setStatus(1);
            roomRoleInfo.setQueue(1);
           roomRoleList.add(roomRoleInfo);
         if(roomInfo.isPlaying()){
             RoomRoleInfo roomRoleInfo2=new RoomRoleInfo();
             roomRoleInfo2.setRobot(true);
             roomRoleInfo2.setPosition(1);
             roomRoleInfo2.setStatus(1);
             roomRoleInfo2.setQueue(2);
             roomRoleList.add(roomRoleInfo2);

             HandlerRobotMatch.isPalyingRoomNums++;
         }else{
             HandlerRobotMatch.accessableRoomNums++;
         }
            roomInfo.setRoomRoleList(roomRoleList);
           HandlerRobotMatch.roomIdList.add(roomId);
            roomInfoMap.get(1).put(roomId,roomInfo);
        }

    }
    public static void startRobotMatch(){
        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
        try {
            service.scheduleAtFixedRate(new HandlerRobotMatch(), 5000, 1000, TimeUnit.MILLISECONDS);
        }catch ( Exception e){
            e.printStackTrace();
        }
    }

    public MissionData.ResponseLeaveRoom.Builder leaveRoom(String uid){
        MissionData.ResponseLeaveRoom.Builder builder = MissionData.ResponseLeaveRoom.newBuilder();
        int val = 0;
        int nowRoomId = RoomManager.getRoleRoomId(uid);
        if (nowRoomId > 0){
            RoomManager.leaveRoom(uid, nowRoomId);

//            int hall = RoomManager.getRoleHall(uid);
//            if (hall > 0){
//                List<RoomShowInfo> roomList = RoomManager.getRoomList(hall);
//                for (int i = 0 ; i<roomList.size() ; i++){
//                    RoomShowInfo roomShowInfo = roomList.get(i);
//                    MissionData.RoomShow.Builder showBu = MissionData.RoomShow.newBuilder();
//                    showBu.setId(roomShowInfo.getId());
//                    showBu.setName(roomShowInfo.getName());
//                    showBu.setIsSecret(roomShowInfo.getIsSecret());
//                    showBu.setPlayerNum(roomShowInfo.getPlayerNum());
//                    showBu.setIsPlaying(roomShowInfo.isPlaying());
//                    showBu.setType(roomShowInfo.getType());
//                    List<RoomRoleInfo> roomRoleList = RoomManager.getRoomInfoById(hall,roomShowInfo.getId()).getRoomRoleList();
//                    for (int j = 0 ; j < roomRoleList.size() ; j++){
//                        RoomRoleInfo roomRoleInfo = roomRoleList.get(j);
//                        MissionData.RoomShowOne.Builder oneBu = MissionData.RoomShowOne.newBuilder();
//                        oneBu.setStatus(roomRoleInfo.getStatus());
//                        oneBu.setQueue(roomRoleInfo.getQueue());
//                        oneBu.setPosition(roomRoleInfo.getPosition());
//                        showBu.addList(oneBu);
//                    }
//                    builder.addRoomList(showBu);
//                }
//            }
        }else {
            if (nowRoomId == 0){
                val = ProtoData.ErrorCode.NOTINROOM_VALUE;
                log.error(uid+":[leaveRoom] error 1");
            }else if (nowRoomId == -1){
                val = ProtoData.ErrorCode.NOTINHALL_VALUE;
                log.error(uid+":[leaveRoom] error 2");
            }else {
                val = ProtoData.ErrorCode.UNKNOWERROR_VALUE;
                log.error(uid+":[leaveRoom] error 3");
            }
        }
        builder.setErrorId(val);
        return builder;
    }

    public MissionData.ResponseUpdateRoomInfo.Builder updateRoomInfo(String uid,int type,String name,String pwd){
        MissionData.ResponseUpdateRoomInfo.Builder builder = MissionData.ResponseUpdateRoomInfo.newBuilder();
        builder.setErrorId(0);
        builder.setType(type);
        int nowRoomId = RoomManager.getRoleRoomId(uid);
        if (nowRoomId <= 0){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[updateRoomInfo] error 1>>>nowRoomId:"+nowRoomId);
            return builder;
        }
        int hall = RoomManager.getRoleHall(uid);
        int roomType = RoomManager.getRoleRoomType(uid);
        RoomInfo roomInfo = RoomManager.getRoomInfoById(roomType,hall, nowRoomId);
        if (roomInfo == null){
            RoomManager.leaveRoom(uid, nowRoomId);
            builder.setErrorId(ProtoData.ErrorCode.ROOMNOTEXIT_VALUE);
            log.error(uid+":[updateRoomInfo] error 2>>>nowRoomId:"+nowRoomId);
            return builder;
        }
        RoomRoleInfo roomRoleInfo = null;
        for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
            if (roomInfo.getRoomRoleList().get(i).getUid().equals(uid)){
                roomRoleInfo = roomInfo.getRoomRoleList().get(i);
                break;
            }
        }
        if (roomRoleInfo == null){
            RoomManager.leaveRoom(uid, nowRoomId);
            builder.setErrorId(ProtoData.ErrorCode.NOTINROOM_VALUE);
            log.error(uid+":[updateRoomInfo] error 3>>>nowRoomId:"+nowRoomId);
            return builder;
        }

        if (roomRoleInfo.getType() != 1){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[updateRoomInfo] error 4>>>is not Owner nowRoomId:"+nowRoomId);
            return builder;
        }

        if (type == 1){
            if (roomRoleInfo.getType() != 1){
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid+":[updateRoomInfo] error 5>>>nowRoomId:"+nowRoomId);
                return builder;
            }
            roomInfo.setName(name);
            builder.setName(name);
            RoomManager.updateRoomName(roomInfo.getRoomRoleList(), roomRoleInfo.getPlayerId(), name);
        }else if (type == 2){
            int val = pwd.equals("") ? 0 : 1;
            String oldPwd = roomInfo.getPwd();
            roomInfo.setPwd(pwd);
            builder.setIsSecret(val);
            RoomManager.updateRoomPwd(roomInfo.getRoomRoleList(), roomRoleInfo.getPlayerId(), pwd, oldPwd);
        }else if (type == 0){
            if (roomRoleInfo.getType() != 1){
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid+":[updateRoomInfo] error 6>>>nowRoomId:"+nowRoomId);
                return builder;
            }
            roomInfo.setName(name);
            builder.setName(name);

            int val = pwd.equals("") ? 0 : 1;
            String oldPwd = roomInfo.getPwd();
            roomInfo.setPwd(pwd);
            builder.setIsSecret(val);
            RoomManager.updateRoomNameAndPwd(roomInfo.getRoomRoleList(), roomRoleInfo.getPlayerId(), name,pwd, oldPwd);
        }else {
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[updateRoomInfo] error 7>>>nowRoomId:"+nowRoomId);
        }
        return builder;
    }

    public MissionData.ResponseUpdateRoomRole.Builder updateRoomRole(String uid,int status,int queue,int position){
        MissionData.ResponseUpdateRoomRole.Builder builder = MissionData.ResponseUpdateRoomRole.newBuilder();
        builder.setErrorId(0);
        int nowRoomId = RoomManager.getRoleRoomId(uid);
        if (nowRoomId <= 0){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[updateRoomRole] error 1>>>nowRoomId:"+nowRoomId);
            return builder;
        }
        int hall = RoomManager.getRoleHall(uid);
        int type = RoomManager.getRoleRoomType(uid);
        RoomInfo roomInfo = RoomManager.getRoomInfoById(type,hall, nowRoomId);
        if (roomInfo == null){
            RoomManager.leaveRoom(uid, nowRoomId);
            builder.setErrorId(ProtoData.ErrorCode.ROOMNOTEXIT_VALUE);
            log.error(uid+":[updateRoomRole] error 2>>>nowRoomId:"+nowRoomId);
            return builder;
        }
        RoomRoleInfo roomRoleInfo = null;
        for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
            if (roomInfo.getRoomRoleList().get(i).getUid().equals(uid)){
                roomRoleInfo = roomInfo.getRoomRoleList().get(i);
                break;
            }
        }
        if (roomRoleInfo == null){
            RoomManager.leaveRoom(uid, nowRoomId);
            builder.setErrorId(ProtoData.ErrorCode.NOTINROOM_VALUE);
            log.error(uid+":[updateRoomRole] error 3>>>nowRoomId:"+nowRoomId);
            return builder;
        }
        if (status != -1){
            roomRoleInfo.setStatus(status);
            builder.setStatus(status);
            RoomManager.updateRoomRoleStatus(roomInfo.getRoomRoleList(), roomRoleInfo.getPlayerId(), status);
            RoomManager.boardcastRoom(type,hall,nowRoomId,roomInfo.getRoomRoleList().size(),false,null,-1);
        }
        if (queue != -1 && position != -1){
            boolean bo = judgePosition(roomInfo.getRoomRoleList(),queue,position);
            if (bo == false){
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid+":[updateRoomRole] error 4>>>nowRoomId:"+nowRoomId+",queue:"+queue+",position:"+position);
                return builder;
            }
            roomRoleInfo.setQueue(queue);
            builder.setQueue(queue);
            roomRoleInfo.setPosition(position);
            builder.setPosition(position);
            RoomManager.updateRoomRoleQueue(roomInfo.getRoomRoleList(), roomRoleInfo.getPlayerId(), queue, position);
        }else if (position != -1){
            boolean bo = judgePosition(roomInfo.getRoomRoleList(),roomRoleInfo.getQueue(),position);
            if (bo == false){
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid+":[updateRoomRole] error 5>>>nowRoomId:"+nowRoomId+",queue:"+roomRoleInfo.getQueue()+",position:"+position);
                return builder;
            }
            roomRoleInfo.setPosition(position);
            builder.setPosition(position);
            RoomManager.updateRoomRolePosition(roomInfo.getRoomRoleList(), roomRoleInfo.getPlayerId(), queue);
        }
        if(status==1&&roomInfo.isRobot()){
            int addTime=(int)((Math.random()+2)*1000);
             long startGameTime=TimerHandler.nowTimeStamp+addTime;
             synchronized (HandlerRobotMatch.robotGameHandleMap) {
                 if (HandlerRobotMatch.robotGameHandleMap.get(uid) == null) {
                     HandlerRobotMatch.robotGameHandleMap.put(uid, startGameTime);
                 }
             }
           // /// System.out.println(addTime+"addTime"+startGameTime+"startGameTime");
        }
        return builder;
    }
    private boolean judgePosition(List<RoomRoleInfo> roomRoleInfoList,int queue,int pos){
        for (int i = 0 ; i<roomRoleInfoList.size() ; i++){
            RoomRoleInfo roomRoleInfo = roomRoleInfoList.get(i);
            if (roomRoleInfo.getQueue() == queue && roomRoleInfo.getPosition() == pos){
                return false;
            }
        }
        return true;
    }

    public MissionData.ResponseJoinFightHall.Builder joinFightHall(String uid,int type,int hall){
        MissionData.ResponseJoinFightHall.Builder builder = MissionData.ResponseJoinFightHall.newBuilder();
        builder.setErrorId(0);
        boolean bo = RoomManager.updateRoomRoleMap(type,hall, uid);
        if (bo == false){
            builder.setErrorId(ProtoData.ErrorCode.FIGHTHALLFULL_VALUE);
            log.error(uid+":[joinFightHall] error 1>>>type:"+type);
        }else {
            if (type == 1){
                if (hall == 2){
                    IMission iMission = MissionDao.getInstance();
                    int missionMax = iMission.getMissionMax(uid);
                    if (missionMax < SuperConfig.FIGHTHALLMISSIONID){
                        builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                        log.error(uid+":[joinFightHall] error 2>>>missionMax:"+missionMax);
                    }
                }else {
                    //目前的双人模式
                    Redis jedis=Redis.getInstance();
                    jedis.hset(uid+":station","joinHall","1");
                    Redis.expire(uid+":station",60*60*5);
                    List<RoomShowInfo> roomList = RoomManager.getRoomList(hall);
                    for (int i = 0 ; i<roomList.size() ; i++){
                        RoomShowInfo roomShowInfo = roomList.get(i);
                        MissionData.RoomShow.Builder showBu = MissionData.RoomShow.newBuilder();
                        showBu.setId(roomShowInfo.getId());
                        showBu.setName(roomShowInfo.getName());
                        showBu.setIsSecret(roomShowInfo.getIsSecret());
                        showBu.setPlayerNum(roomShowInfo.getPlayerNum());
                        showBu.setIsPlaying(roomShowInfo.isPlaying());
                        showBu.setType(roomShowInfo.getType());
                        if(roomShowInfo.isRobotRoom()){
                        showBu.setIsRobotRoom(true);
                        }

                        // 虚拟房间不需要角色信息
                        List<RoomRoleInfo> roomRoleList=null;
       /*         if(roomShowInfo.isRobotRoom()==true){
                    showBu.setIsRobotRoom(true);
                    showBu.setPlayerNum(2);
                    roomRoleList=new ArrayList<RoomRoleInfo>();
                    RoomRoleInfo roomRoleInfo=new RoomRoleInfo();
                    roomRoleInfo.setPosition(1);
                    roomRoleInfo.setStatus(1);
                   roomRoleInfo.setQueue(1);
                    roomRoleList.add(roomRoleInfo);
                    RoomRoleInfo roomRoleInfo2=new RoomRoleInfo();
                    roomRoleInfo2.setPosition(1);
                    if(showBu.getIsPlaying()){
                        roomRoleInfo2.setStatus(1);
                    }else{
                        roomRoleInfo2.setStatus(0);
                    }
                    roomRoleInfo2.setQueue(2);
                    roomRoleList.add(roomRoleInfo2);
                }else {*/
                    roomRoleList = RoomManager.getRoomInfoById(roomShowInfo.getType(),hall,roomShowInfo.getId()).getRoomRoleList();
             //   }
                for (int j = 0 ; j < roomRoleList.size() ; j++){
                            RoomRoleInfo roomRoleInfo = roomRoleList.get(j);
                            MissionData.RoomShowOne.Builder oneBu = MissionData.RoomShowOne.newBuilder();
                            oneBu.setStatus(roomRoleInfo.getStatus());
                            oneBu.setQueue(roomRoleInfo.getQueue());
                            oneBu.setPosition(roomRoleInfo.getPosition());
                            showBu.addList(oneBu);
                           // /// System.err.println(roomRoleInfo.getType()+"~~~~"+roomShowInfo.getId()+roomRoleInfo);
                        }
                        builder.addRoomList(showBu);
                    }
                }
            }
        }
       // /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder;
    }

    public void moveInRoom(String uid, UserData.PointDouble point, UserData.PointDouble direction,double speed){
        int hall = RoomManager.getRoleHall(uid);
        int roomId = RoomManager.getRoleRoomId(uid);
        int type = RoomManager.getRoleRoomType(uid);
        if (roomId != -1){
            RoomInfo roomInfo = RoomManager.getRoomInfoById(type,hall,roomId);
            ILogin iLogin = LoginDao.getInstance();
            int id = iLogin.getIdFromUid(uid);
            UserData.ReportStationLocation.Builder stationBu = UserData.ReportStationLocation.newBuilder();
            stationBu.setPassengerId(id);
            stationBu.setDirection(direction);
            stationBu.setSpeed(speed);
            stationBu.setPoint(point);
            for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
                RoomRoleInfo roomRoleInfo = roomInfo.getRoomRoleList().get(i);
                if (roomRoleInfo.getUid() != uid){
                    ReportManager.reportInfo(roomRoleInfo.getUid(), ProtoData.SToC.REPORTSTATIONLOCATION_VALUE, stationBu.build().toByteArray());
                }else {
                    PointDoubleInfo pointDoubleInfo = PointDoubleInfo.pointDoubleDataToInfo(point);
                    roomRoleInfo.setPoint(pointDoubleInfo);
                }
            }
        }
    }

    public int startInRoom(String uid){
        synchronized (uid) {
            int nowRoomId = RoomManager.getRoleRoomId(uid);
            if (nowRoomId <= 0) {
                log.error(uid + ":[startInRoom] error 1>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            int hall = RoomManager.getRoleHall(uid);
            int type = RoomManager.getRoleRoomType(uid);
            RoomInfo roomInfo = RoomManager.getRoomInfoById(type, hall, nowRoomId);
            if (roomInfo == null) {
                RoomManager.leaveRoom(uid, nowRoomId);
                log.error(uid + ":[startInRoom] error 2>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.ROOMNOTEXIT_VALUE;
            }
            if (roomInfo.getRoomRoleList().size() < 2) {
                log.error(uid + ":[startInRoom] error 3>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            if(roomInfo.isPlaying()){
                log.error(uid + ":[startInRoom] error 4>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            RoomRoleInfo roomRoleInfo = null;
            for (int i = 0; i < roomInfo.getRoomRoleList().size(); i++) {
                RoomRoleInfo tmp = roomInfo.getRoomRoleList().get(i);
                if (tmp.getType() != 1 && tmp.getStatus() != 1 && (!roomInfo.isRobot())) {
                    log.error(uid + ":[startInRoom] error 4>>>roleId:" + tmp.getPlayerId() + ",not in ready!");
                    return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
                }
                if (tmp.getUid().equals(uid)) {
                    roomRoleInfo = tmp;
                }
            }
            if (roomRoleInfo == null) {
                RoomManager.leaveRoom(uid, nowRoomId);
                log.error(uid + ":[startInRoom] error 5>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.NOTINROOM_VALUE;
            }
            if (roomRoleInfo.getType() != 1 && (!roomInfo.isRobot())) {
                log.error(uid + ":[startInRoom] error 6>>>roleType:" + roomRoleInfo.getType());
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            RoomManager.comeToFight(hall, nowRoomId, roomInfo);
            return 0;
        }
    }

    public static  int startInRobotRoom(String uid){
        synchronized (uid) {
            int nowRoomId = RoomManager.getRoleRoomId(uid);
            if (nowRoomId <= 0) {
                log.error(uid + ":[startInRoom] error 1>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            int hall = RoomManager.getRoleHall(uid);
            int type = RoomManager.getRoleRoomType(uid);
            RoomInfo roomInfo = RoomManager.getRoomInfoById(type, hall, nowRoomId);
            if (roomInfo == null) {
                RoomManager.leaveRoom(uid, nowRoomId);
                log.error(uid + ":[startInRoom] error 2>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.ROOMNOTEXIT_VALUE;
            }
            if (roomInfo.getRoomRoleList().size() < 2) {
                log.error(uid + ":[startInRoom] error 3>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            if(roomInfo.isPlaying()){
                log.error(uid + ":[startInRoom] error 4>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            RoomRoleInfo roomRoleInfo = null;
            for (int i = 0; i < roomInfo.getRoomRoleList().size(); i++) {
                RoomRoleInfo tmp = roomInfo.getRoomRoleList().get(i);
                if (tmp.getType() != 1 && tmp.getStatus() != 1 && (!roomInfo.isRobot())) {
                    log.error(uid + ":[startInRoom] error 4>>>roleId:" + tmp.getPlayerId() + ",not in ready!");
                    return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
                }
                if (tmp.getUid().equals(uid)) {
                    roomRoleInfo = tmp;
                }
            }
            if (roomRoleInfo == null) {
                RoomManager.leaveRoom(uid, nowRoomId);
                log.error(uid + ":[startInRoom] error 5>>>nowRoomId:" + nowRoomId);
                return ProtoData.ErrorCode.NOTINROOM_VALUE;
            }
            if (roomRoleInfo.getType() != 1 && (!roomInfo.isRobot())) {
                log.error(uid + ":[startInRoom] error 6>>>roleType:" + roomRoleInfo.getType());
                return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
            }
            RoomManager.comeToFight(hall, nowRoomId, roomInfo);
            return 0;
        }
    }
    public MissionData.SResponseOverMission.Builder overMission(int type,int hall,int roomId,List<String> uids){
        MissionData.SResponseOverMission.Builder builder = MissionData.SResponseOverMission.newBuilder();
        builder.setErrorId(0);
        builder.setHall(hall);
        builder.setRoomId(roomId);
        builder.setType(type);
        RoomManager.comeBackFight(type,hall, roomId);
        Redis jedis = Redis.getInstance();
        for (int i = 0 ; i < uids.size() ; i++){
            String uid = uids.get(i);
            int dailyPkNum = Integer.parseInt(jedis.hget("role:"+uid, "dailypknum"))+1;
            jedis.hset("role:"+uid, "dailypknum",dailyPkNum+"");
            StringBuffer hql = new StringBuffer("update RoleEntity set dailypknum = ").append(dailyPkNum)
                    .append(" where uid = '").append(uid).append("'");
            MySql.updateSomes(hql.toString());
            ITask taskDao =TaskDao.getInstance();
            taskDao.updateOneTask(uid,10016,1);
        }
        return builder;
    }


    public static class HandlerRobotMatch implements Runnable {
        private static List<RoleEntity> robotRolePool = new ArrayList<RoleEntity>();
        private static List<PartEntity> robotPartPool = new ArrayList<PartEntity>();
        private static List<String> roomNamePool = new ArrayList<String>();
        private  static  long count;
        private static List<Integer>  roomIdList=new Vector<Integer>();
        public static int isPalyingRoomNums=0;
        public static int accessableRoomNums=0;
         public   static  Map<String,Long> robotGameHandleMap=new ConcurrentHashMap<String, Long>();
       //private static List<Integer[]>  roomIdList=new Vector<Integer[]>();
        static {
            getRoomNameFormLanguageExcel();
            StringBuffer sql =new StringBuffer("from RoleEntity ");
            List<Object> robotGroup=MySql.queryForList(sql.toString(),100);
            for(int i=0;i<robotGroup.size();i++){
                RoleEntity entity=(RoleEntity)robotGroup.get(i);
                robotRolePool.add(entity);
            }

            StringBuffer querySql =new StringBuffer("from PartEntity ");
            List<Object> robotPartGroup=MySql.queryForList(querySql.toString(),100);
            Redis jedis=Redis.getInstance();
            String key="robotPartPool";
            for(int i=0;i<robotPartGroup.size();i++){
                PartEntity entity=(PartEntity)robotPartGroup.get(i);
                robotPartPool.add(entity);
                jedis.hset(key,i+"",MyUtils.objectToJson(entity));
            }
            String  roomNameKey="roomNamePool";
            Map<String,String> roomNameMap=jedis.hgetAll(roomNameKey);
            for(Map.Entry<String,String> entry:roomNameMap.entrySet()){
                roomNamePool.add(entry.getKey());
            }
        }
        public void removeRobotRoom() {
            //銷毀一個房間
            if(roomIdList.size()==0){
                addRoom(0);
                return;
            }
            int roomId= roomIdList.get((int)(roomIdList.size()*Math.random()));
            int type=2;
            int hall=1;
            Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? RoomManager.roomInfoMap1 : RoomManager.roomInfoMap2;

           try{
               RoomInfo roomInfo=roomInfoMap.get(hall).get(roomId);
           if(roomInfo==null) {
               //||roomInfo.getRoomRoleList().size()>=2
               return;
           }
           if(roomInfo.isPlaying()){
               List<RoomRoleInfo> roomRoleList=roomInfo.getRoomRoleList();
               boolean isRobotRoom=true;
               for(int i=0;i<roomRoleList.size();i++){
                   RoomRoleInfo roomRoleInfo=roomRoleList.get(i);
                   if(!roomRoleInfo.isRobot()){
                       isRobotRoom=false;
                   }
               }
               if(!isRobotRoom){
                   return;
               }
           }
               roomInfoMap.get(hall).remove(roomId);
              if(roomInfo.isPlaying()){
                  HandlerRobotMatch.isPalyingRoomNums--;

           }else{
                  HandlerRobotMatch.accessableRoomNums--;
              }
            List<RoomRoleInfo> roomRoleList=roomInfo.getRoomRoleList();
            for(int i=0;i<roomRoleList.size();i++){
                RoomRoleInfo roomRoleInfo=roomRoleList.get(0);
                if(roomRoleInfo.isRobot()){
                    Redis.del(roomRoleInfo.getUid());
                }
            }
           }catch (Exception e){
               /// System.err.println(roomId+"roomId");
               e.printStackTrace();
           }
            List<Boolean> roomPool = type == 1 ? RoomManager.roomPool1 : RoomManager.roomPool2;
            int roomid1=roomId-1;
            roomPool.set(roomid1,false);
            MissionData.ReportRoom.Builder builder = MissionData.ReportRoom.newBuilder();
            MissionData.RoomShow.Builder showBu = MissionData.RoomShow.newBuilder();
            showBu.setId(roomId);
            showBu.setPlayerNum(-1);
            showBu.setIsPlaying(false);
            showBu.setType(type);
            builder.setRoom(showBu);
            roomIdList.remove(new Integer(roomId));
           // /// System.err.println("刷新房间");
            ReportManager.boardcastInfo(0,hall,ProtoData.SToC.REPORTROOM_VALUE,builder.build().toByteArray());

        }

        public void addRoom(int type){ //type==0代表生成可用房间
            try{

            Map<Integer,Map<Integer,RoomInfo>> roomInfoMap =RoomManager.roomInfoMap2;
                int roomId = RoomManager.getRoomId(2);
                RoomInfo roomInfo = new RoomInfo();
                roomInfo.setType(2);
                String name=HandlerRobotMatch.roomNamePool.get((int)(Math.random()*HandlerRobotMatch.roomNamePool.size()));
                if(name.length()>8){
                 name=name.substring(0,8);
                }
                roomInfo.setName(name);
                roomInfo.setPwd("");

                roomInfo.setIsPlaying(Math.random()<=0.8?false:true);
                if(type==0){
                    roomInfo.setIsPlaying(false);
                }
                roomInfo.setRobot(true);
                List<RoomRoleInfo> roomRoleList=new ArrayList<RoomRoleInfo>();
                RoomRoleInfo roomRoleInfo=new HandlerRobotMatch().setRoomRobotRoleInfo(1);
                roomRoleInfo.setPosition(1);
                roomRoleInfo.setStatus(1);
                roomRoleInfo.setQueue(1);
                roomRoleList.add(roomRoleInfo);
                MissionData.RoomShowOne.Builder oneBu = MissionData.RoomShowOne.newBuilder();
                MissionData.RoomShow.Builder showBu = MissionData.RoomShow.newBuilder();
                if(roomInfo.isPlaying()){
                    RoomRoleInfo roomRoleInfo2=new RoomRoleInfo();
                    roomRoleInfo2.setRobot(true);
                    roomRoleInfo2.setPosition(1);
                    roomRoleInfo2.setStatus(1);
                    roomRoleInfo2.setQueue(2);
                    roomRoleList.add(roomRoleInfo2);
                    MissionData.RoomShowOne.Builder oneBu2 = MissionData.RoomShowOne.newBuilder();
                    oneBu2.setStatus(roomRoleInfo2.getStatus());
                    oneBu2.setQueue(roomRoleInfo2.getQueue());
                    oneBu2.setPosition(roomRoleInfo2.getPosition());
                    showBu.addList(oneBu2);
                    isPalyingRoomNums++;
                }else{
                    accessableRoomNums++;
                }
                roomInfo.setRoomRoleList(roomRoleList);
                roomInfoMap.get(1).put(roomId,roomInfo);
               roomIdList.add(roomId);

            MissionData.ReportRoom.Builder builder = MissionData.ReportRoom.newBuilder();

            showBu.setId(roomId);
            showBu.setPlayerNum(2);
            showBu.setIsPlaying(roomInfo.isPlaying());
            showBu.setType(roomInfo.getType());
            showBu.setIsRobotRoom(true);
            showBu.setName(name);

            oneBu.setStatus(roomRoleInfo.getStatus());
            oneBu.setQueue(roomRoleInfo.getQueue());
            oneBu.setPosition(roomRoleInfo.getPosition());
            showBu.addList(oneBu);
            builder.setRoom(showBu);
            int hall=1;
            ReportManager.boardcastInfo(0,hall,ProtoData.SToC.REPORTROOM_VALUE,builder.build().toByteArray());

            }
            catch(Exception e){
       e.printStackTrace();
                }
        }

        public void run() {
            try {
                count += 1000;
                long flushTime=1000 * 20;
                if (count >= flushTime) {
                    count = 0;
                    double random = Math.random();
                    if (0.2 >= random) {
                        if (isPalyingRoomNums> 5) {
                            removeRobotRoom();
                        }

                    } else {
                        if (accessableRoomNums<10) {
                         /*   int needNums=10-accessableRoomNums;
                            count=flushTime-flushTime/(needNums*2);*/
                                addRoom(0);
                        }
                    }

                }

                ///// System.err.println(count);
                long now = TimerHandler.nowTimeStamp;
                Map<Integer, RoomInfo> roomInfoMap = RoomManager.roomInfoMap2.get(1);
                //  /// System.err.println("roomInfoMap"+roomInfoMap.size());

                for (Map.Entry<Integer, RoomInfo> entry : roomInfoMap.entrySet()) {
                    RoomInfo roomInfo = entry.getValue();
                    if (!roomInfo.isRobot()&&(!roomInfo.isPlaying() && roomInfo.getRoomRoleList().size() == 1) && (roomInfo.getPwd() == null || ("".equals(roomInfo.getPwd())))) {
                        long createRoomTime = roomInfo.getCreateRoomTime();
                        if (now - createRoomTime>=0) {
                         //   /// System.err.println("开始机器人");
                            RoomRoleInfo roomRoleInfo = setRoomRobotRoleInfo(1);
                            roomInfo = joinRobotRoom(2, entry.getKey(), roomRoleInfo);
                        }
                    }
                }

                //玩家进入机器人房间后处理开始游戏程序

                for(Map.Entry<String,Long> entry:robotGameHandleMap.entrySet()){
                    String uid=entry.getKey();
                    long time=entry.getValue();
                    if(time<=now){
                        startInRobotRoom(uid);
                        robotGameHandleMap.remove(uid);
                    }
                }
            }catch (Exception e){
                e.printStackTrace();
            }
            }



        public RoomRoleInfo setRoomRobotRoleInfo(int type) {
            RoomRoleInfo roomRoleInfo = new RoomRoleInfo();
            roomRoleInfo.setRobot(true);
            roomRoleInfo.setType(2);
            roomRoleInfo.setStatus(1);
            int roleIndex = (int) (Math.random() * robotRolePool.size());
            RoleEntity roleEntity = robotRolePool.get(roleIndex);
           String uuid=("robot"+TimerHandler.nowTimeStamp+Math.random()).substring(0,30);
            int index = (int) (Math.random() * robotPartPool.size());
            roomRoleInfo.setUid(uuid);
            roomRoleInfo.setPlayerId(-999);
            roomRoleInfo.setName(ranName.name());
            roomRoleInfo.setLv(roleEntity.getLv());
            roomRoleInfo.setHead(roleEntity.getHead());
            Redis jedis=Redis.getInstance();
           Map robotInfoMap =new HashMap<String,String>();
           robotInfoMap.put("index",index+"");
            robotInfoMap.put("name",roomRoleInfo.getName());
            robotInfoMap.put("lv",roomRoleInfo.getLv()+"");
            robotInfoMap.put("head",roomRoleInfo.getHead()+"");
             jedis.hmset(uuid,robotInfoMap);
             Redis.expire(uuid,3600*5);
            PartEntity partEntity = robotPartPool.get(index);
            CupboardInfo cupboard = new CupboardInfo();
            cupboard.setRoleId(partEntity.getRoleid());
            cupboard.setPart1(partEntity.getPart1());
            cupboard.setPart2(partEntity.getPart2());
            cupboard.setPart3(partEntity.getPart3());
            cupboard.setPart4(partEntity.getPart4());
            cupboard.setPart5(partEntity.getPart5());
            cupboard.setPart6(partEntity.getPart6());
            cupboard.setPart7(partEntity.getPart7());
            cupboard.setPart8(partEntity.getPart8());
            cupboard.setPart9(partEntity.getPart9());
            roomRoleInfo.setCupboard(cupboard);
            PointDoubleInfo pointDoubleInfo = new PointDoubleInfo();
            pointDoubleInfo.setX(0);
            pointDoubleInfo.setY(0);
            roomRoleInfo.setPoint(pointDoubleInfo);
           // /// System.err.println("设置装扮");
            return roomRoleInfo;
        }

        private static RoomInfo joinRobotRoom(int type, int roomId, RoomRoleInfo roomRoleInfo) {
            int hall = 1;
            String key = hall + "&" + roomId;
            synchronized (key) {
                Map<Integer, Map<Integer, RoomInfo>> roomInfoMap = type == 1 ? RoomManager.roomInfoMap1 : RoomManager.roomInfoMap2;
                RoomInfo roomInfo = roomInfoMap.get(hall).get(roomId);
                if (roomInfo == null) {
                    return null;
                }
                if (roomInfo.getRoomRoleList().size() >= 2) {
                    return null;
                }
                //更新房间玩家
                MissionData.ReportAddRoomRole.Builder builder = MissionData.ReportAddRoomRole.newBuilder();
                int p1 = 0;
                int p2 = 0;
                int val = roomInfo.getRoomRoleList().get(0).getQueue() == 1 ? 1 : -1;
                for (int i = 0; i < roomInfo.getRoomRoleList().size(); i++) {
                    RoomRoleInfo info = roomInfo.getRoomRoleList().get(i);
                    if (info.getQueue() == 1) {
                        p1 += info.getPosition();
                    } else {
                        p2 += info.getPosition();
                    }
                    val *= -1;
                }

                if (val == 1) {
                    roomRoleInfo.setQueue(1);
                    int pos = 3 - p1 == 3 ? 1 : (3 - p1);
                    roomRoleInfo.setPosition(pos);
                } else {
                    roomRoleInfo.setQueue(2);
                    int pos = 3 - p2 == 3 ? 1 : (3 - p2);
                    roomRoleInfo.setPosition(pos);
                }
                MissionData.RoomRole roomRole = RoomRoleInfo.toRoomRoleData(roomRoleInfo);
                builder.setRoomRole(roomRole);
                for (int i = 0; i < roomInfo.getRoomRoleList().size(); i++) {
                    RoomRoleInfo info = roomInfo.getRoomRoleList().get(i);
                    String pUid = info.getUid();
                    ReportManager.reportInfo(pUid, ProtoData.SToC.REPORTADDROOMROLE_VALUE, builder.build().toByteArray());
                }

                roomInfo.getRoomRoleList().add(roomRoleInfo);
                //广播房间
                RoomManager.boardcastRoom(type, hall, roomId, roomInfo.getRoomRoleList().size(), false, null, -1);
                roomInfo.setRobot(true);
                return roomInfo;

            }
        }
    }

    private static void getRoomNameFormLanguageExcel(){
        Redis jedis=Redis.getInstance();
        Iterator<String> iterator=Redis.keys("roomnameconfig:*").iterator();
        ///// System.err.println(Redis.keys("roomnameconfig:*").size()+"size");
        List<String> list=new ArrayList<String>();
        while (iterator.hasNext()){
            String key=iterator.next();
            Map<String,String>  roomNameMap=jedis.hgetAll(key);
            list.add(roomNameMap.get(("name")));
        }
        Iterator<String> iterator1=Redis.keys("languageRoomname*").iterator();
      // /// System.err.println(Redis.keys("languageRoomname*").size()+"Redis.keys(languageRoomname）");
        Map<String,String> roomNameMap=new HashMap<String,String>();
        int i=1;
        while (iterator1.hasNext()){
            String key=iterator1.next();
            Map<String,String> map=jedis.hgetAll(key);
            String roomNameKey=map.get("id");
            if(list.contains(roomNameKey)){
                roomNameMap.put(map.get("chinese"),(i++)+"");
            }
        }
        Redis.del("roomNamePool");
       jedis.hmset("roomNamePool",roomNameMap);
       /* Iterator<String> iter = jedis.keys("languageRoomname*").iterator();
        Pipeline pdel = jedis.pipelined();
        while (iter.hasNext()) {
            String key = iter.next();
            pdel.del(key);
        }
        pdel.sync();*/

    }
}