package module.pet;

import common.*;
import entities.PetEntity;
import entities.RoleAdditionalEntity;
import manager.MySql;
import manager.Redis;
import manager.TimerHandler;
import module.mail.NoticeDao;
import module.robot.ranName;
import protocol.NoticeData;
import protocol.PetData;
import protocol.SerializeObject;

import java.util.*;

public class PetUtils {
    private static int petCharacterNums = 25;

    private static void initPetCharacterNums() {
        Set configSet = Redis.scan("disposition*");
        petCharacterNums = configSet.size();
    }

    //宠物等级对照表
    //  private static Map<Integer,Integer> petExpMap=new HashMap<Integer, Integer>();
 /*   private static void initPetExpMap(){

    }*/
    private static Map<Integer, Integer> indexMappingPetType = new HashMap<Integer, Integer>();
    private static List<String> probabilitysList = new ArrayList<String>();
    private static List<String> rarityProbabilitysList = new ArrayList<String>();
    private static Map<Integer, Integer> indexMappingrarity = new HashMap<Integer, Integer>();
    private static List<Integer> breedPetTypePool = new ArrayList<Integer>();
    private static Map<Integer, Integer> indexMappingStar = new HashMap<Integer, Integer>();
    private static List<String> petStarProbabilitysPool = new ArrayList<String>();

    static {
        try {
            /// System.out.println("SuperConfig.petComposeConfig="+SuperConfig.petComposeConfig);
            /// System.out.println("SuperConfig.getCongifMap(SuperConfig.petComposeConfig)="+SuperConfig.getCongifMap(SuperConfig.petComposeConfig));
            Map<Integer, Object> composeConfigMap = SuperConfig.getCongifMap(SuperConfig.petComposeConfig);
            int index = 0;
            for (Map.Entry<Integer, Object> entry : composeConfigMap.entrySet()) {
                ComposeConfig composeConfig = (ComposeConfig) entry.getValue();
                indexMappingPetType.put(index, composeConfig.getComposeId());
                /// System.out.println("composeConfig.getDimprobability()="+composeConfig.getDimprobability());
                probabilitysList.add(composeConfig.getDimprobability() + "");
                index++;
            }


        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            Map<Integer, Object> composeConfigMap = SuperConfig.getCongifMap(SuperConfig.petCultivateConfig);
            int index = 0;
            for (Map.Entry<Integer, Object> entry : composeConfigMap.entrySet()) {
                PetCultivateConfig petCultivateConfig = (PetCultivateConfig) entry.getValue();
                indexMappingrarity.put(index, petCultivateConfig.getUr());
                rarityProbabilitysList.add(petCultivateConfig.getProbability() + "");
                index++;
            }
        } catch (Exception e) {

            e.printStackTrace();
        }

        try {
            Map<Integer, Object> petConfigMap = SuperConfig.getCongifMap(SuperConfig.petConfig);
            for (Map.Entry<Integer, Object> entry : petConfigMap.entrySet()) {
                PetConfig petConfig = (PetConfig) entry.getValue();
                if (petConfig.getPetKind() == 1) {
                    breedPetTypePool.add(petConfig.getPetType());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {

            Map<Integer, Object> petStarConfigMap = SuperConfig.getCongifMap(SuperConfig.petStarConfig);
            int index = 0;
            for (Map.Entry<Integer, Object> entry : petStarConfigMap.entrySet()) {
                PetStarConfig petStarConfig = (PetStarConfig) entry.getValue();
                petStarProbabilitysPool.add(petStarConfig.getPetStarProbability() + "");
                indexMappingStar.put(index, petStarConfig.getId());
                index++;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    // 普通宠物的创建
    public static PetEntity createPet(String uid, int petType, int access) {
//        System.out.println("非卡池宠物");
        PetEntity petEntity = new PetEntity();
        petEntity.setPetCharacter(createPetCharacter());
        petEntity.setCurrentExp(0);
        petEntity.setCurrentLevel(1);
        petEntity.setFriendId(uid);
        petEntity.setPetType(petType);
        petEntity.setStrongLevel(0);
        petEntity.setSpSkillLv(1);
        petEntity.setNormalSkillLv(1);
        petEntity.setLockStatus(0);
        petEntity.setBreakLV(0);
        petEntity.setAccessType(access);
        petEntity.setGrowing(false);
        petEntity.setName("");
        petEntity.setMode(1);
        petEntity.setGetTime(TimerHandler.nowTimeStamp);
        //获取宠物的稀有度/星级
        int rarity = getPetRarity();
        /// System.out.println("rarity="+rarity);
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("petconfig:*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> mailMap = jedis.hgetAll(key);
            int keys = Integer.parseInt(mailMap.get("id"));
            if (keys == petEntity.getPetType()) {
                petEntity.setRarity(Integer.parseInt(String.valueOf(mailMap.get("rare"))));
            }
        }
        try {
            //星级 决定宠物属性六维的系数
            // int starLV=getPetStar();
            petEntity.setStarLevel(rarity);
            PetAttributeConfig petAttributeConfig = (PetAttributeConfig) SuperConfig.getCongifObject(SuperConfig.petAttributeConfig, rarity);
            //hp
            List<Float> hpFactorList = petAttributeConfig.getHpCoefficient();
            int hpFactorindex = (int) (Math.random() * hpFactorList.size());
            float hpFactor = hpFactorList.get(hpFactorindex);
            petEntity.setHpFactor(hpFactor);
            //atk
            List<Float> AtkFactorList = petAttributeConfig.getAtkcoefficient();
            int AtkFactorIndex = (int) (Math.random() * AtkFactorList.size());
            float AtkFactor = AtkFactorList.get(AtkFactorIndex);
            petEntity.setAtkFactor(AtkFactor);
            //def
            List<Float> defFactorList = petAttributeConfig.getDefCoefficient();
            int defFactorIndex = (int) (Math.random() * defFactorList.size());
            float defFactor = defFactorList.get(defFactorIndex);
            petEntity.setDefFactor(defFactor);
            //satk
            List<Float> satkFactorList = petAttributeConfig.getSatkCoefficient();
            int satkFactorIndex = (int) (Math.random() * satkFactorList.size());
            float satkFactor = satkFactorList.get(satkFactorIndex);
            petEntity.setSatkFactor(satkFactor);
            //sdef
            List<Float> sdefFactorList = petAttributeConfig.getSdefCoefficient();
            int sdefFactorIndex = (int) (Math.random() * sdefFactorList.size());
            float sdefFactor = sdefFactorList.get(sdefFactorIndex);
            petEntity.setSdefFactor(sdefFactor);
            //speed
            List<Float> speedFactorList = petAttributeConfig.getSpeedCoefficient();
            int speedFactorIndex = (int) (Math.random() * speedFactorList.size());
            float speedFactor = speedFactorList.get(speedFactorIndex);
            petEntity.setSpeedFactor(speedFactor);
            PetConfig petConfig = (PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig, petType);
            petEntity.setMainAttribute(petConfig.getTypeone());
            petEntity.setSubAttribute(petConfig.getTypetwo());
            /*petEntity.setHp(petConfig.getHpgrow());
            petEntity.setAtk(petConfig.getAtkgrow());
            petEntity.setDef(petConfig.getDefgrow());
            petEntity.setSatk(petConfig.getSatkgrow());
            petEntity.setSdef(petConfig.getSdefgrow());
            petEntity.setSpeed(petConfig.getSpeedgrow());*/
            petEntity.setNormalSkillId(petConfig.getNormalskillid());
            petEntity.setSpSkillId(petConfig.getSpskillid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        int petId = createPetId(petEntity.getPetCharacter(), uid, petType);
        petEntity.setPetUId(petId);
        /// System.err.println("新添加的宠物="+petEntity.toString());
        MySql.insert(petEntity);

        // ！！！！！！！！！！创建蛋的数据
        return petEntity;
    }

    //創建盲盒卡池裡的準備進化的寵物
    public static PetEntity createEvlutionPet(String uid, int petType, int access) {
//        System.out.println("卡池宠物");
        PetEntity petEntity = new PetEntity();
        petEntity.setPetCharacter(createPetCharacter());
        petEntity.setCurrentExp(0);
        petEntity.setCurrentLevel(1);
        petEntity.setFriendId(uid);
        petEntity.setPetType(petType);
        petEntity.setStrongLevel(0);
        petEntity.setSpSkillLv(1);
        petEntity.setNormalSkillLv(1);
        petEntity.setLockStatus(0);
        petEntity.setBreakLV(0);
        petEntity.setAccessType(access);
        petEntity.setGrowing(false);
        petEntity.setMode(1);
        petEntity.setGetTime(TimerHandler.nowTimeStamp);
        //獲取寵物的稀有度
        int rarity = getPetRarity();
        petEntity.setRarity(rarity);

        if (access == PetConfig.petFromRafflePool) {
            SerializeObject.GrowingPetInfo.Builder petGrowInfo = SerializeObject.GrowingPetInfo.newBuilder();
            petGrowInfo.setAge(0);
            petGrowInfo.setGrowth(0);
            petGrowInfo.setHappayTime(0);
            petGrowInfo.setMood(1);
            petEntity.setGrowInfo(petGrowInfo.build().toByteArray());
            petEntity.setName("");
        }
        try {
            //星级 決定宠物属性六维的系数
            int starLV = 1;
            petEntity.setStarLevel(rarity);
            PetAttributeConfig petAttributeConfig = (PetAttributeConfig) SuperConfig.getCongifObject(SuperConfig.petAttributeConfig, starLV);
            //hp
            List<Float> hpFactorList = petAttributeConfig.getHpCoefficient();
            int hpFactorindex = (int) (Math.random() * hpFactorList.size());
            float hpFactor = hpFactorList.get(hpFactorindex);
            petEntity.setHpFactor(hpFactor);
            //atk
            List<Float> AtkFactorList = petAttributeConfig.getAtkcoefficient();
            int AtkFactorIndex = (int) (Math.random() * AtkFactorList.size());
            float AtkFactor = AtkFactorList.get(AtkFactorIndex);
            petEntity.setAtkFactor(AtkFactor);
            //def
            List<Float> defFactorList = petAttributeConfig.getDefCoefficient();
            int defFactorIndex = (int) (Math.random() * defFactorList.size());
            float defFactor = defFactorList.get(defFactorIndex);
            petEntity.setDefFactor(defFactor);
            //satk
            List<Float> satkFactorList = petAttributeConfig.getSatkCoefficient();
            int satkFactorIndex = (int) (Math.random() * satkFactorList.size());
            float satkFactor = satkFactorList.get(satkFactorIndex);
            petEntity.setSatkFactor(satkFactor);

            //sdef
            List<Float> sdefFactorList = petAttributeConfig.getSdefCoefficient();
            int sdefFactorIndex = (int) (Math.random() * sdefFactorList.size());
            float sdefFactor = sdefFactorList.get(sdefFactorIndex);
            petEntity.setSdefFactor(sdefFactor);
            //speed
            List<Float> speedFactorList = petAttributeConfig.getSpeedCoefficient();
            int speedFactorIndex = (int) (Math.random() * speedFactorList.size());
            float speedFactor = speedFactorList.get(speedFactorIndex);
            petEntity.setSpeedFactor(speedFactor);
            PetConfig petConfig = (PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig, petType);
            petEntity.setMainAttribute(petConfig.getTypeone());
            petEntity.setSubAttribute(petConfig.getTypetwo());
            petEntity.setNormalSkillId(petConfig.getNormalskillid());
            petEntity.setSpSkillId(petConfig.getSpskillid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        int petId = createPetId(petEntity.getPetCharacter(), uid, petType);
        petEntity.setPetUId(petId);
        MySql.insert(petEntity);
        /// System.err.println(petEntity);
        return petEntity;
    }

    //創建孵化出来的宠物
    public static PetEntity createBreedPet(String uid, int petType, int rarity, int access) {
        PetEntity petEntity = new PetEntity();
        petEntity.setPetCharacter(createPetCharacter());
        petEntity.setCurrentExp(0);
        petEntity.setCurrentLevel(1);
        petEntity.setFriendId(uid);
        petEntity.setPetType(petType);
        petEntity.setStrongLevel(0);
        petEntity.setSpSkillLv(1);
        petEntity.setNormalSkillLv(1);
        petEntity.setLockStatus(1 << PetConfig.eggFormLockIndex);
        petEntity.setBreakLV(0);
        petEntity.setAccessType(access);
        petEntity.setGrowing(false);
        petEntity.setRarity(rarity);
        petEntity.setName("");
        petEntity.setMode(1);
        petEntity.setGetTime(TimerHandler.nowTimeStamp);
        try {

            //星级 決定宠物属性六维的系数
            int starLV = 1;
            petEntity.setStarLevel(rarity);
            PetAttributeConfig petAttributeConfig = (PetAttributeConfig) SuperConfig.getCongifObject(SuperConfig.petAttributeConfig, starLV);
            //hp
            List<Float> hpFactorList = petAttributeConfig.getHpCoefficient();
            int hpFactorindex = (int) (Math.random() * hpFactorList.size());
            float hpFactor = hpFactorList.get(hpFactorindex);
            petEntity.setHpFactor(hpFactor);
            //atk
            List<Float> AtkFactorList = petAttributeConfig.getAtkcoefficient();
            int AtkFactorIndex = (int) (Math.random() * AtkFactorList.size());
            float AtkFactor = AtkFactorList.get(AtkFactorIndex);
            petEntity.setAtkFactor(AtkFactor);
            //def
            List<Float> defFactorList = petAttributeConfig.getDefCoefficient();
            int defFactorIndex = (int) (Math.random() * defFactorList.size());
            float defFactor = defFactorList.get(defFactorIndex);
            petEntity.setDefFactor(defFactor);
            //satk
            List<Float> satkFactorList = petAttributeConfig.getSatkCoefficient();
            int satkFactorIndex = (int) (Math.random() * satkFactorList.size());
            float satkFactor = satkFactorList.get(satkFactorIndex);
            petEntity.setSatkFactor(satkFactor);

            //sdef
            List<Float> sdefFactorList = petAttributeConfig.getSdefCoefficient();
            int sdefFactorIndex = (int) (Math.random() * sdefFactorList.size());
            float sdefFactor = sdefFactorList.get(sdefFactorIndex);
            petEntity.setSdefFactor(sdefFactor);
            //speed
            List<Float> speedFactorList = petAttributeConfig.getSpeedCoefficient();
            int speedFactorIndex = (int) (Math.random() * speedFactorList.size());
            float speedFactor = speedFactorList.get(speedFactorIndex);
            petEntity.setSpeedFactor(speedFactor);
            PetConfig petConfig = (PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig, petType);
            petEntity.setMainAttribute(petConfig.getTypeone());
            petEntity.setSubAttribute(petConfig.getTypetwo());
         /*   petEntity.setHp(petConfig.getHpgrow());
            petEntity.setAtk(petConfig.getAtkgrow());
            petEntity.setDef(petConfig.getDefgrow());
            petEntity.setSatk(petConfig.getSatkgrow());
            petEntity.setSdef(petConfig.getSdefgrow());
            petEntity.setSpeed(petConfig.getSpeedgrow());*/
            petEntity.setNormalSkillId(petConfig.getNormalskillid());
            petEntity.setSpSkillId(petConfig.getSpskillid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        int petId = createPetId(petEntity.getPetCharacter(), uid, petType);
        petEntity.setPetUId(petId);
        MySql.insert(petEntity);
        /// System.err.println(petEntity);
        return petEntity;
    }

    public static int createPetId(int character, String freiendId, int petType) {
        int petId = 0;
        petId = ((int) (Math.random() * 128)) << 16 + ((int) TimerHandler.nowTimeStamp) >> 16 << 8 + petType % 64 << 2 + character % 4;
        petId = ((int) (Math.random() * Integer.MAX_VALUE));
        return petId;
    }

    // 生成宠物性格
    private static int createPetCharacter() {
    /*    if(petCharacterNums==0){
            initPetCharacterNums();
        }*/
        //   /// System.err.println(petCharacterNums);
        //   return  (int)(Math.random()*petCharacterNums)+1;
        return 1;
    }

    // 宠物增加经验
    public static PetEntity addPetExp(PetEntity pet, int addExp) {
        // 等级上限
        int nowlvLimit = nowLvLimit(pet);
        int nowLv = pet.getCurrentLevel();
        int nowExp = pet.getCurrentExp();
        if (nowLv >= nowlvLimit) {
            /// System.err.println(pet+"nowPet");
            return pet;
        }
        try {
//            PetExpConfig petExpConfig = (PetExpConfig) SuperConfig.getCongifObject(SuperConfig.petExpConfig, nowLv);
            // 当前宠物的等级
            int curLv = nowLv;
            // 当前宠物的全部经验
            int curTotalExp = nowExp + addExp;
            // 当前等级需要的全部经验
            int curLvNeedTotalExp = ((PetExpConfig) SuperConfig.getCongifObject(SuperConfig.petExpConfig, curLv)).getTotalExp();

//            System.err.println("宠物增加经验\n"+
//            "当前等级");

            for (int i = curLv; i < nowlvLimit; i++){
                // 可以去下一个等级
                if (curTotalExp >= curLvNeedTotalExp){
                    curLv++;
                    curLvNeedTotalExp = ((PetExpConfig) SuperConfig.getCongifObject(SuperConfig.petExpConfig, curLv)).getTotalExp();
                }else{
                    break;
                }
            }
            // 防止经验总值溢出
            curTotalExp = curTotalExp < ((PetExpConfig) SuperConfig.getCongifObject(SuperConfig.petExpConfig, nowlvLimit)).getTotalExp()
                    ? curTotalExp : ((PetExpConfig) SuperConfig.getCongifObject(SuperConfig.petExpConfig, nowlvLimit)).getTotalExp();
            pet.setCurrentExp(curTotalExp);
            pet.setCurrentLevel(curLv);
//            int lvUpNeedExp = petExpConfig.getExp();
//            nowExp = addExp + nowExp;
//            /// System.err.println(petExpConfig+"*****nowLv"+nowLv+"--0-"+nowExp+"nowExp"+"~~~"+lvUpNeedExp+"lvUpNeedExp"+nowlvLimit);
//            int uplv = 0;
//            while (nowExp >= lvUpNeedExp) {
//                /// System.err.println(nowExp+"nowExp"+"~~~"+nowLv+"nowLv");
//                if (nowLv >= nowlvLimit) {
//                    //达到突破的等级限制
//                    /// System.err.println("达到突破的等级限制");
//                    nowExp = 0;
//                    break;
//                }
//                nowExp -= lvUpNeedExp;
//                nowLv++;
//                uplv++;
//                petExpConfig = (PetExpConfig) SuperConfig.getCongifObject(SuperConfig.petExpConfig, nowLv);
//                if (petExpConfig == null) {
//                    nowExp = 0;
//                    break;//达到最高级
//                }
//                lvUpNeedExp = petExpConfig.getExp();
//                if (nowLv == nowlvLimit) {
//                    nowExp = 0;
//                }
//                //  宠物升级
//
//                //     /// System.err.println(nowExp+"nowExp"+"~~~"+nowLv+"nowLv");
//
//            }
//            /// System.err.println(nowExp+"nowLv"+nowLv);
//            pet.setCurrentExp(nowExp);
//            pet.setCurrentLevel(nowLv);
//            if (uplv != 0) {
//                petUpLv(pet, uplv);
//            } else {
//                MySql.update(pet);
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        /// System.err.println(pet+"~~");
        MySql.update(pet);
        return pet;
    }

    private static void petUpLv(PetEntity pet, int upLv) {
        int character = pet.getPetCharacter();
        try {
            PetCharaterConfig petCharaterConfig = (PetCharaterConfig) SuperConfig.getCongifObject(SuperConfig.petCharaterConfig, character);
            PetConfig petConfig = (PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig, pet.getPetType());
           /*  int hp=pet.getHp()+upLv*petConfig.getHpgrow();
             int atk=(int)(pet.getAtk()+upLv*petConfig.getAtkgrow()*petCharaterConfig.getAtkFactor());
             int def=(int)(pet.getDef()+upLv*petConfig.getDefgrow()*petCharaterConfig.getDefFactor());
             int satk=(int)(pet.getSatk()+upLv*petConfig.getSatkgrow()*petCharaterConfig.getSatkFactor());
             int sdef=(int)(pet.getSdef()+upLv*petConfig.getSdefgrow()*petCharaterConfig.getSdefFactor());
             int speed=(int)(pet.getSpeed()+upLv*petConfig.getSpeedgrow()*petCharaterConfig.getSpeedFactor());*/
    /*         pet.setHp(hp);
             pet.setAtk(atk);
             pet.setDef(def);
             pet.setSatk(satk);
             pet.setSdef(sdef);
             pet.setSpeed(speed);*/
            /// System.out.println("petUpLv"+pet);
            MySql.update(pet);
        } catch (Exception e) {
            e.printStackTrace();
        }


    }

    // 获取当前星级下的等级限制
    public static int nowLvLimit(PetEntity pet) {
        int limit = 0;
        int breakLV = pet.getBreakLV();
        try {
            PetBreakConfig petBreakConfig = (PetBreakConfig) SuperConfig.getCongifObject(SuperConfig.petBreakConfig, breakLV);
            limit = petBreakConfig.getLevelLimit();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return limit;

    }


    public static PetEntity petUpStar(PetEntity petEntity, int star) {
        petEntity.setStarLevel(petEntity.getStarLevel() + star);
        MySql.update(petEntity);
        return petEntity;
    }

    public static PetEntity petBreak(PetEntity petEntity, int LV) {
        petEntity.setBreakLV(petEntity.getBreakLV() + 1);
        MySql.update(petEntity);
        return petEntity;
    }

    public static PetEntity petChangeCharacter(PetEntity petEntity) {
        int character = petEntity.getPetCharacter();
        int newCharacter = character;
        while (character == newCharacter) {
            newCharacter = createPetCharacter();
            // /// System.err.println(newCharacter+"111newCharacter"+character);
        }
        try {
            int upLv = petEntity.getCurrentLevel();
            PetCharaterConfig petCharaterConfig = (PetCharaterConfig) SuperConfig.getCongifObject(SuperConfig.petCharaterConfig, newCharacter);
            PetConfig petConfig = (PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig, petEntity.getPetType());
/*            int hp=petEntity.getHp()+upLv*petConfig.getHpgrow();
            int atk=(int)(upLv*petConfig.getAtkgrow()*petCharaterConfig.getAtkFactor());
            int def=(int)(upLv*petConfig.getDefgrow()*petCharaterConfig.getDefFactor());
            int satk=(int)(upLv*petConfig.getSatkgrow()*petCharaterConfig.getSatkFactor());
            int sdef=(int)(upLv*petConfig.getSdefgrow()*petCharaterConfig.getSdefFactor());
            int speed=(int)(upLv*petConfig.getSpeedgrow()*petCharaterConfig.getSpeedFactor());*/
       /*     petEntity.setHp(hp);
            petEntity.setAtk(atk);
            petEntity.setDef(def);
            petEntity.setSatk(satk);
            petEntity.setSdef(sdef);
            petEntity.setSpeed(speed);*/
        } catch (Exception e) {
            e.printStackTrace();
        }

        petEntity.setPetCharacter(newCharacter);
        MySql.update(petEntity);
        return petEntity;
    }

    //技能升星
    public static boolean upPetSkill(String uid, PetEntity mainPet, PetEntity subPet) {
        if (mainPet.getPetType() != subPet.getPetType()) {
            return false;
        }
        //  int normalSkillLV=mainPet.getNormalSkillLv();
        int spSkillLv = mainPet.getSpSkillLv();
        //   int  normalSkillMaxLv=getPetSkillMaxLv(mainPet.getNormalSkillId());
        int spSkillMaxLv = getPetSkillMaxLv(mainPet.getSpSkillId());
        if (spSkillLv >= spSkillMaxLv) {
            return false;
        } else {
            mainPet.setSpSkillLv(mainPet.getSpSkillLv() + 1);
        }
        MySql.delete(subPet);
        return true;
    }

    private static int getPetSkillMaxLv(int skillType) {
        int skillMaxlv = 0;
        try {
            PetSkillConfig petSkillConfig = (PetSkillConfig) SuperConfig.getCongifObject(SuperConfig.petSkillConfig, skillType);
            skillMaxlv = petSkillConfig.getSpmaxlv();
        } catch (Exception e) {

            e.printStackTrace();
        }
        return skillMaxlv;
    }

    // 改名字
    public static PetEntity changeName(PetEntity petEntity, String newName) {
        petEntity.setName(newName);
        MySql.update(petEntity);
        return petEntity;
    }

    public static PetEntity powerUp(PetEntity petEntity) {
        petEntity.setStrongLevel(petEntity.getStrongLevel() + 1);
        MySql.update(petEntity);
        return petEntity;
    }

    public static PetEntity lockPet(PetEntity petEntity) {
        MySql.update(petEntity);
        return petEntity;
    }


    public static int getComposePetType() {
        int index = ranName.ranGift(probabilitysList);
        return indexMappingPetType.get(index);
    }

    //
    public static int getPetRarity() {
        int index = ranName.ranGift(rarityProbabilitysList);
        /// System.out.println("index="+index);
        return indexMappingrarity.get(index);
    }

    public static int getBreedPetType() {
        int index = (int) (Math.random() * breedPetTypePool.size());
        return breedPetTypePool.get(index);
    }

    public static PetData.ResponseGetPetBreedInfo.Builder PetBreedInfoToResponseGetPetBreedInfo(SerializeObject.PetBreedInfo petBreedInfo) {
        PetData.ResponseGetPetBreedInfo.Builder builder = PetData.ResponseGetPetBreedInfo.newBuilder();
        builder.setErrorId(0);
        long nowTime = TimerHandler.nowTimeStamp;
        int breedCDPetCount = petBreedInfo.getBreedCDPetCount();
        if (breedCDPetCount != 0) {
            List<SerializeObject.PetBreedCD> breedCDPetList = petBreedInfo.getBreedCDPetList();
            for (SerializeObject.PetBreedCD petBreedCD : breedCDPetList) {
                long breedCD = petBreedCD.getTime() - nowTime;
                if (breedCD > 0) {
                    PetData.PlainPet.Builder plainPet = PetData.PlainPet.newBuilder();
                    plainPet.setPetId(petBreedCD.getPetId());
                    plainPet.setBreedCD((int) (breedCD / 1000));
                    builder.addPet(plainPet);
                }

            }
        }
        int status = petBreedInfo.getPetBreedStatus();
        builder.setPetBreedStatus(status);
        if (status == 2) {
            long breedFinishTime = petBreedInfo.getBreedfinishTime();
            int breedCd = (int) ((breedFinishTime - nowTime) / 1000);
            builder.setBreedfinishCD(breedCd);
            if (petBreedInfo.hasUseItem()) {
                builder.setUseItem(petBreedInfo.getUseItem());
            }
            builder.addAllBreedPetId(petBreedInfo.getBreedPetIdList());
        }

        return builder;
    }


    private static int getPetStar() {
        int index = ranName.ranGift(petStarProbabilitysPool);
        return indexMappingStar.get(index);
    }

    public static List<PetData.PetFormation> getPetFormation(String uid) {
        StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
        RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
        byte[] petFormation = entity.getPetFormation();
        List<PetData.PetFormation> formationList = null;
        try {
            PetData.RequestPetFormation formnation = PetData.RequestPetFormation.parseFrom(petFormation);
            formationList = formnation.getPetFormationList();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return formationList;
    }

    public static PetEntity changeMode(PetEntity petEntity) {
        MySql.update(petEntity);
        return petEntity;
    }

    // 官方赠送宠物的创建
   /* public static PetEntity createPet(String uid,int petType,int access,int lv,int rarity){
        PetEntity petEntity   = new PetEntity();
        petEntity.setPetCharacter(createPetCharacter());
        petEntity.setCurrentExp(0);
        petEntity.setCurrentLevel(lv);
        petEntity.setFriendId(uid);
        petEntity.setPetType(petType);
        petEntity.setStrongLevel(0);
        petEntity.setSpSkillLv(1);
        petEntity.setNormalSkillLv(1);
        petEntity.setLockStatus(1<<PetConfig.eggFormLockIndex);
        petEntity.setBreakLV(0);
        petEntity.setAccessType(access);
        petEntity.setGrowing(false);
        petEntity.setName("");
        petEntity.setMode(2);
        petEntity.setGetTime(TimerHandler.nowTimeStamp);
        //獲取寵物的稀有度

        petEntity.setRarity(rarity);
        try{
            //星级 決定宠物属性六维的系数
            // int starLV=getPetStar();
            petEntity.setStarLevel(rarity);
            PetAttributeConfig petAttributeConfig=(PetAttributeConfig)SuperConfig.getCongifObject(SuperConfig.petAttributeConfig,rarity);
            //hp
            List<Float>  hpFactorList=petAttributeConfig.getHpCoefficient();
            int hpFactorindex=(int)(Math.random()*hpFactorList.size());
            float hpFactor=hpFactorList.get(hpFactorindex);
            petEntity.setHpFactor(hpFactor);
            //atk
            List<Float>  AtkFactorList=petAttributeConfig.getAtkcoefficient();
            int AtkFactorIndex=(int)(Math.random()*AtkFactorList.size());
            float AtkFactor=AtkFactorList.get(AtkFactorIndex);
            petEntity.setAtkFactor(AtkFactor);
            //def
            List<Float>  defFactorList=petAttributeConfig.getDefCoefficient();
            int defFactorIndex=(int)(Math.random()*defFactorList.size());
            float defFactor=defFactorList.get(defFactorIndex);
            petEntity.setDefFactor(defFactor);
            //satk
            List<Float>  satkFactorList=petAttributeConfig.getSatkCoefficient();
            int satkFactorIndex=(int)(Math.random()*satkFactorList.size());
            float satkFactor=satkFactorList.get(satkFactorIndex);
            petEntity.setSatkFactor(satkFactor);

            //sdef
            List<Float>  sdefFactorList=petAttributeConfig.getSdefCoefficient();
            int sdefFactorIndex=(int)(Math.random()*sdefFactorList.size());
            float sdefFactor=sdefFactorList.get(sdefFactorIndex);
            petEntity.setSdefFactor(sdefFactor);
            //speed
            List<Float>  speedFactorList=petAttributeConfig.getSpeedCoefficient();
            int speedFactorIndex=(int)(Math.random()*speedFactorList.size());
            float speedFactor=speedFactorList.get(speedFactorIndex);
            petEntity.setSpeedFactor(speedFactor);
            PetConfig petConfig  =(PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig,petType);
            petEntity.setMainAttribute(petConfig.getTypeone());
            petEntity.setSubAttribute(petConfig.getTypetwo());
            *//*petEntity.setHp(petConfig.getHpgrow());
            petEntity.setAtk(petConfig.getAtkgrow());
            petEntity.setDef(petConfig.getDefgrow());
            petEntity.setSatk(petConfig.getSatkgrow());
            petEntity.setSdef(petConfig.getSdefgrow());
            petEntity.setSpeed(petConfig.getSpeedgrow());*//*
            petEntity.setNormalSkillId(petConfig.getNormalskillid());
            petEntity.setSpSkillId(petConfig.getSpskillid());
        } catch ( Exception e){
            e.printStackTrace();
        }
        int petId=createPetId(petEntity.getPetCharacter(),uid,petType);
        petEntity.setPetUId(petId);
        MySql.insert(petEntity);
        /// System.err.println(petEntity);
        return petEntity;
    }*/


}
