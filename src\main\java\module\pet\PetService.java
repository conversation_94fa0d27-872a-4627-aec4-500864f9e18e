package module.pet;

import com.google.protobuf.InvalidProtocolBufferException;
import common.*;
import entities.ItemEntity;
import entities.PetEntity;
import entities.RoleAdditionalEntity;
import manager.MySql;
import manager.ReportManager;
import manager.TimerHandler;
import model.CommonInfo;
import module.item.ItemDao;
import module.item.ItemUtils;
import module.mail.mail_tool.MailGetRewards;
import protocol.*;
import utils.MyUtils;

import java.text.SimpleDateFormat;
import java.util.*;

public class PetService {
    private static PetService inst = null;

    public static PetService getInstance() {
        if (inst == null) {
            inst = new PetService();
        }
        return inst;
    }

    public byte[] getPetsInfo(byte[] bytes, String uid) {
        PetData.RequestGetPetsInfo petsInfo = null;
        PetData.ResponseGetPetsInfo.Builder builder = PetData.ResponseGetPetsInfo.newBuilder();
        builder.setErrorId(0);
        try {
            PetDao petDao = PetDao.getInstance();
            List<Object> petList = petDao.queryPet(uid);
            for (int i = 0; i < petList.size(); i++) {
                PetEntity pet = (PetEntity) petList.get(i);
                builder.addPetInfo(PetEntity.entityToPb(pet));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        //  /// System.err.println(JsonFormat.printToString(builder.build())+"寵物獲取");
        return builder.build().toByteArray();
    }

    public byte[] operatePet(byte[] bytes, String uid) {
        PetData.RequestOperatePet requestOperatePet = null;
        PetData.ResponseOperatePet.Builder builder = PetData.ResponseOperatePet.newBuilder();
        try {
            requestOperatePet = PetData.RequestOperatePet.parseFrom(bytes);
            /// System.out.println();
            /// System.err.println(JsonFormat.printToString(requestOperatePet));
            int operateType = requestOperatePet.getType();
            builder.setErrorId(0);
            builder.setType(operateType);
            PetData.PlainPet pet = requestOperatePet.getPet();
            PetEntity petEntity = null;
            int petId = pet.getPetId();
            PetDao petDao = PetDao.getInstance();
            petEntity = petDao.queryPet(uid, petId);
            if (petEntity == null) {
                builder.setErrorId(1);
                return builder.build().toByteArray();
            }

            //  /// System.err.println(JsonFormat.printToString(requestOperatePet));

            switch (operateType) {
                case 1:
//                        PetEntity   petEntity=PetUtils.createPet(uid,pet.getPetType());
//                        builder.addPet(PetEntity.entityToPb(petEntity));
                    break;
                //升级
                case 3:
                    List<ItemData.Item> itemList = requestOperatePet.getItemList();
                    int addEXP = 0;
                    for (ItemData.Item item : itemList) {
                        ItemConfig itemConfig = (ItemConfig) SuperConfig.getCongifObject(SuperConfig.itemConfig, item.getId());
                        int effectType = itemConfig.getEffectType();
                        int petExp = itemConfig.getEffectValue();
                        if (effectType != ItemConfig.PetUpExpItemType) {
                            builder.setErrorId(2);
                            return builder.build().toByteArray();
                        } else {
                            int num = (int) item.getNum();
                            addEXP += (num * petExp);
                        }
                    }
                    //   /// System.err.println(addEXP+"addEXP");
                    petEntity = PetUtils.addPetExp(petEntity, addEXP);
                    builder.addPet(PetEntity.entityToPb(petEntity));
                    builder.addAllItem(requestOperatePet.getItemList());
                    ItemUtils.updatePlayerItems(uid, requestOperatePet.getItemList(), false);
                    break;

                //升星
                case 4:
                    int petStar = petEntity.getStarLevel();
                    CommonInfo commonInfo = needupStarItem(petStar);
                    List<ItemData.Item> upStarItemList = requestOperatePet.getItemList();
                    if (upStarItemList.size() == 1) {
                        ItemData.Item itemInfo = upStarItemList.get(0);
                        /// System.err.println(itemInfo + "~~" + commonInfo);
              /*      if(itemInfo.getNum()==commonInfo.getValue()&&itemInfo.getId()==commonInfo.getKey()){
                        builder.addPet(PetEntity.entityToPb(PetUtils.petUpStar(petEntity,1)));
                    }*/
                        builder.addPet(PetEntity.entityToPb(PetUtils.petUpStar(petEntity, 1)));
                    }
                    builder.addAllItem(requestOperatePet.getItemList());
                    ItemUtils.updatePlayerItems(uid, requestOperatePet.getItemList(), false);
                    break;

                //改性格
                case 5:
                    List<ItemData.Item> changeCharacterItemList = requestOperatePet.getItemList();
                    if (changeCharacterItemList.size() == 1) {
                        ItemData.Item itemInfo = changeCharacterItemList.get(0);
                        if (itemInfo.getNum() == 1 && itemInfo.getId() == 9) {
                            builder.addPet(PetEntity.entityToPb(PetUtils.petChangeCharacter(petEntity)));
                        }
                    }
                    builder.addAllItem(requestOperatePet.getItemList());
                    ItemUtils.updatePlayerItems(uid, requestOperatePet.getItemList(), false);
                    break;
                //改昵称
                case 6:
                    String newName = pet.getName();
                    PetUtils.changeName(petEntity, newName);
                    builder.addPet(PetEntity.entityToPb(PetUtils.petChangeCharacter(petEntity)));
                    break;
                //升级强化等级
                case 7:
                    int strongLv = petEntity.getStrongLevel();
                    PowerupConfig powerupConfig = (PowerupConfig) SuperConfig.getCongifObject(SuperConfig.powerUpConfig, strongLv + 1);
                    CommonInfo itemInfo = powerupConfig.getCostItem();
                    ItemUtils.updatePlayerItems(uid, itemInfo.getKey(), itemInfo.getValue(), false);
                    int costItem = powerupConfig.getCostGold();
                    ItemUtils.updatePlayerItems(uid, 1, costItem, false);
                    builder.addAllItem(requestOperatePet.getItemList());
                    builder.addPet(PetEntity.entityToPb(PetUtils.powerUp(petEntity)));
                    break;
                case 8:
                    int commonLockStatus = petEntity.getLockStatus() & (1 << PetConfig.commonLockIndex);

                    int changeValue = commonLockStatus == 0 ? (1 << PetConfig.commonLockIndex) : -(1 << PetConfig.commonLockIndex);
                    petEntity.setLockStatus(petEntity.getLockStatus() + changeValue);

                    builder.addPet(PetEntity.entityToPb(PetUtils.lockPet(petEntity)));
                    break;
                case 9:
                    if (!petEntity.isGrowing()) {
                        builder.setErrorId(2);
                        return builder.build().toByteArray();
                    }
                    PetData.PlainPet plainPet = requestOperatePet.getPet();
                    petEntity.setMode(plainPet.getMode());
                    builder.addPet(PetEntity.entityToPb(PetUtils.changeMode(petEntity)));
                    break;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    //获取宠物经验
    public byte[] getPetExp(byte[] bytes, String uid) {
        UserData.RequestPetExp requestPetExp = null;
        PetData.ResponseOperatePet.Builder builder = PetData.ResponseOperatePet.newBuilder();
        try {
            requestPetExp = UserData.RequestPetExp.parseFrom(bytes);
            builder.setErrorId(0);
            builder.setType(3);
            int petuid = requestPetExp.getPetUid();
            PetEntity nowPetEntity = PetDao.getInstance().queryPet(uid, petuid);
//            MapConfig mapConfig = (MapConfig) SuperConfig.getCongifObject(SuperConfig.mapConfig, requestPetExp.getPetUid());
//            int petexp;
//            petexp = nowPetEntity.getCurrentExp() + requestPetExp.getPetexp();
            nowPetEntity = PetUtils.addPetExp(nowPetEntity, requestPetExp.getPetexp());
//            PetData.PlainPet.Builder plainPetBu = PetData.PlainPet.newBuilder();
//            plainPetBu.setPetId(nowPetEntity.getPetUId());
            builder.addPet(PetEntity.entityToPb(nowPetEntity));
        } catch (Exception e) {
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, builder.build().toByteArray());
        return builder.build().toByteArray();
    }

    //获取需要升星的物品信息
    private static CommonInfo needupStarItem(int nowStar) {
        CommonConfig commonConfigObject = (CommonConfig) SuperConfig.configMap.get(SuperConfig.commonConfig).get(60);
        CommonInfo commonInfo = null;
        switch (nowStar) {
            case 1:
                commonInfo = commonConfigObject.getStar2Item();
                break;
            case 2:
                commonInfo = commonConfigObject.getStar3Item();
                break;
            case 3:
                commonInfo = commonConfigObject.getStar4Item();
                break;
            case 4:
                commonInfo = commonConfigObject.getStar5Item();
                break;
        }
        return commonInfo;
    }


    //强化宠物技能等级

    public byte[] upPetSkill(byte[] bytes, String uid) {
        PetData.ResponseUpPetSkill.Builder builder = PetData.ResponseUpPetSkill.newBuilder();
        PetData.RequestUpPetSkill requestUpPetSkill = null;
        ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
        builder.setErrorId(0);
        try {
            requestUpPetSkill = PetData.RequestUpPetSkill.parseFrom(bytes);
            /// System.out.println(JsonFormat.printToString(requestUpPetSkill));
            int mainPetId = requestUpPetSkill.getMainPetId();
            List<Integer> subPetList = requestUpPetSkill.getSubPetIdList();
            PetEntity mainPetEntity = PetDao.getInstance().queryPet(uid, mainPetId);
            if (mainPetEntity == null) {
                builder.setErrorId(1);
                return builder.build().toByteArray();
            }
            PetData.ResponseOperatePet.Builder deletePetbuild = PetData.ResponseOperatePet.newBuilder();

            for (Integer subPetId : subPetList) {
                PetEntity subPetEntity = PetDao.getInstance().queryPet(uid, subPetId);
                if (subPetEntity == null) {
                    builder.setErrorId(1);
                    return builder.build().toByteArray();
                }
                if (mainPetEntity.getPetType() != subPetEntity.getPetType()) {
                    builder.setErrorId(2);
                    return builder.build().toByteArray();
                }
                if (PetUtils.upPetSkill(uid, mainPetEntity, subPetEntity)) {
                    builder.setMainPet(PetEntity.entityToPb(mainPetEntity));
                    deletePetbuild.addPet(PetEntity.entityToPb(subPetEntity));
                } else {
                    builder.setErrorId(3);
                    builder.setMainPet(PetEntity.entityToPb(mainPetEntity));
                    return builder.build().toByteArray();
                }
            }
            //  /// System.err.println(mainPetEntity);
            MySql.update(mainPetEntity);
            PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
            updatePetbuild.addPet(PetEntity.entityToPb(mainPetEntity));
            updatePetbuild.setType(2);
            updatePetbuild.setErrorId(0);
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());

            deletePetbuild.setType(-1);
            deletePetbuild.setErrorId(0);
            ItemUtils.updatePlayerItems(uid, 1, 80000, false);
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, deletePetbuild.build().toByteArray());
            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
            // /// System.err.println(JsonFormat.printToString(deletePetbuild.build()));
        } catch (Exception e) {

            e.printStackTrace();
        }
        /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    /*点击开始游戏获取人物编队*/
    public byte[] getPetFormation(byte[] bytes, String uid) {
        PetData.ResponseGetPetFormation.Builder builder = PetData.ResponseGetPetFormation.newBuilder();
        builder.setErrorId(0);
        try {
            byte[] petFormation = null;
            StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
            RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
            if (entity == null || entity.getPetFormation() == null) {
                builder.setErrorId(1);
                return builder.build().toByteArray();
            }
            petFormation = entity.getPetFormation();
            PetData.RequestPetFormation formnation = PetData.RequestPetFormation.parseFrom(petFormation);
            for (PetData.PetFormation pet : formnation.getPetFormationList()) {
                PetData.PetFormation.Builder petFormationBu = PetData.PetFormation.newBuilder();
                petFormationBu.setIndex(pet.getIndex());
                petFormationBu.setPetId(pet.getPetId());
                builder.addPetFormation(petFormationBu);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }


    public byte[] petFormation(byte[] bytes, String uid) {
        PetData.ResponsePetFormation.Builder builder = PetData.ResponsePetFormation.newBuilder();
        PetData.RequestPetFormation requestPetFormation = null;
        builder.setErrorId(0);
        List<PetEntity> list = new ArrayList<PetEntity>();
        try {
            requestPetFormation = PetData.RequestPetFormation.parseFrom(bytes);//反序列化
//            /// System.err.println(requestPetFormation);
            List<PetData.PetFormation> petFormationList = requestPetFormation.getPetFormationList();
            for (PetData.PetFormation petFormation : petFormationList) {
                builder.addPetFormation(petFormation);
                int petId = petFormation.getPetId();
                PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                if (petEntity == null) {
                    builder.setErrorId(1);
                    return builder.build().toByteArray();
                } else {
                    list.add(petEntity);
                }
            }


            StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity  where uid='").append(uid).append("'");
            RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
            if (entity.getPetFormation() == null) {
                entity.setUid(uid);
                entity.setPetFormation(bytes);
                MySql.update(entity);
            } else {
                PetData.RequestPetFormation preFormation = PetData.RequestPetFormation.parseFrom(entity.getPetFormation());
                if (preFormation.getPetFormationList().size() != 0) {
                    PetData.ResponseOperatePet.Builder petRemoveLockbuild = PetData.ResponseOperatePet.newBuilder();
                    petRemoveLockbuild.setErrorId(0);
                    petRemoveLockbuild.setType(2);
                    for (PetData.PetFormation prePetFormation : preFormation.getPetFormationList()) {
                        int prePetId = prePetFormation.getPetId();
                        PetEntity petEntity = PetDao.getInstance().queryPet(uid, prePetId);
                        if (list.contains(petEntity)) {
                            list.remove(petEntity);
                            continue;
                        }
                        int lockStatus = petEntity.getLockStatus();
                        if ((lockStatus & (1 << PetConfig.formationLockIndex)) > 1) {//getLockStatus锁定状态
                            petEntity.setLockStatus(petEntity.getLockStatus() - (1 << PetConfig.formationLockIndex));
                            petRemoveLockbuild.addPet(PetEntity.entityToPb(petEntity));
                        }
                        MySql.update(petEntity);

                        ///
                    }
                    /// System.err.println("remove" + JsonFormat.printToString(petRemoveLockbuild.build()));
                    ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petRemoveLockbuild.build().toByteArray());
                }
                entity.setPetFormation(bytes);
                MySql.update(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (list.size() > 0) {
            PetData.ResponseOperatePet.Builder petAddLockbuild = PetData.ResponseOperatePet.newBuilder();
            for (PetEntity updateEntity : list) {
                int lockStatus = updateEntity.getLockStatus();
                /// System.err.println(lockStatus);
                /// System.err.println("++++++++++++++++++++++++++++++++++++++++++++");
                /// System.err.println("lockStatus & (1 << PetConfig.formationLockIndex)");
                /// System.err.println(lockStatus & (1 << PetConfig.formationLockIndex));
                if ((lockStatus & (1 << PetConfig.formationLockIndex)) == 0) {

                    updateEntity.setLockStatus(lockStatus + (1 << PetConfig.formationLockIndex));
                    /// System.err.println(updateEntity.getLockStatus());
                    petAddLockbuild.addPet(PetEntity.entityToPb(updateEntity));
                }
                MySql.update(updateEntity);
            }
            petAddLockbuild.setType(2);
            petAddLockbuild.setErrorId(0);
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petAddLockbuild.build().toByteArray());
//            /// System.err.println("add" + JsonFormat.printToString(petAddLockbuild.build()));
        }


//        /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    //请求宠物合成
    public byte[] petCompose(byte[] bytes, String uid) {
        CombineData.RequestPetCombine requestPetCompose = null;
        PetData.ResponsePetCompose.Builder builder = PetData.ResponsePetCompose.newBuilder();
        builder.setErrorId(0);
        List<PetEntity> petEntitiesList = new ArrayList<PetEntity>();
        try {
            requestPetCompose =  CombineData.RequestPetCombine.parseFrom(bytes);
            /// System.err.println(requestPetCompose);
            List<Integer> petList = requestPetCompose.getPetIdList();
            if (petList == null || petList.size() != 4) {
                builder.setErrorId(1);
                /// System.err.println(JsonFormat.printToString(builder.build()));
                return builder.build().toByteArray();
            } else {
                for (Integer petId : petList) {
                    PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                    if (petEntity == null) {
                        builder.setErrorId(2);
                        /// System.err.println(JsonFormat.printToString(builder.build()));
                        return builder.build().toByteArray();
                    } else {
                        int lockStatus = petEntity.getLockStatus();
                        if ((lockStatus & PetConfig.formationLockIndex) == 1 || (lockStatus & PetConfig.commonLockIndex) == 1) {
                            builder.setErrorId(3);
                            /// System.err.println(JsonFormat.printToString(builder.build()));
                            return builder.build().toByteArray();
                        }
                        petEntitiesList.add(petEntity);
                    }
                }
            }
            PetData.ResponseOperatePet.Builder deletePetbuild = PetData.ResponseOperatePet.newBuilder();
            deletePetbuild.setType(-1);
            deletePetbuild.setErrorId(0);
            for (PetEntity entity : petEntitiesList) {
                deletePetbuild.addPet(PetEntity.entityToPb(entity));
                MySql.delete(entity);
            }
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, deletePetbuild.build().toByteArray());

            // 生成新的宠物
            int petType = PetUtils.getComposePetType();
            PetData.Pet pet = PetEntity.entityToPb(PetUtils.createPet(uid, petType, PetConfig.PetFromCompose));
            builder.setNewPet(pet);
            PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
            updatePetbuild.setType(1);
            updatePetbuild.setErrorId(0);
            updatePetbuild.addPet(pet);

            if (!requestPetCompose.getFree()){
                ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                ItemUtils.updatePlayerItems(uid, 1, requestPetCompose.getGold(), false);
                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
            }

            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    public byte[] getPetBreedInfo(byte[] bytes, String uid) {
        PetData.ResponseGetPetBreedInfo.Builder builder = PetData.ResponseGetPetBreedInfo.newBuilder();
        builder.setErrorId(0);
        StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity  where uid='").append(uid).append("'");
        RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
        byte[] petBreedInfo = entity.getPetBreed();
        if (petBreedInfo == null) {
            builder.setPetBreedStatus(0);
            SerializeObject.PetBreedInfo.Builder petBreedBu = SerializeObject.PetBreedInfo.newBuilder();
            petBreedBu.setPetBreedStatus(0);//初始状态
            entity.setPetBreed(petBreedBu.build().toByteArray());
            MySql.update(entity);
            /// System.out.println(JsonFormat.printToString(builder.build()));
            return builder.build().toByteArray();
        } else {
            try {
                SerializeObject.PetBreedInfo petBreed = SerializeObject.PetBreedInfo.parseFrom(petBreedInfo);

                long nowTime = TimerHandler.nowTimeStamp;
                int breedCDPetCount = petBreed.getBreedCDPetCount();
                if (breedCDPetCount != 0) {
                    List<SerializeObject.PetBreedCD> breedCDPetList = petBreed.getBreedCDPetList();
                    for (SerializeObject.PetBreedCD petBreedCD : breedCDPetList) {
                        long breedCD = petBreedCD.getTime() - nowTime;
                        if (breedCD > 0) {
                            PetData.PlainPet.Builder plainPet = PetData.PlainPet.newBuilder();
                            plainPet.setPetId(petBreedCD.getPetId());
                            plainPet.setBreedCD((int) (breedCD / 1000));
                            builder.addPet(plainPet);
                        }

                    }
                }
                int status = petBreed.getPetBreedStatus();
                builder.setPetBreedStatus(status);
                if (status == 2) {
                    builder.addAllBreedPetId(petBreed.getBreedPetIdList());
                    long breedFinishTime = petBreed.getBreedfinishTime();
                    int breedCd = (int) ((breedFinishTime - nowTime) / 1000);
                    builder.setBreedfinishCD(breedCd);
                    if (petBreed.hasUseItem()) {
                        builder.setUseItem(petBreed.getUseItem());
                    }

                }
            } catch (
                    Exception e) {
                e.printStackTrace();
            }
        }
        /// System.out.println("JsonFormat.printToString(builder.build())=" + JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }


    public byte[] operatePetBreed(byte[] bytes, String uid) {
        PetData.ResponseOperatePetBreed.Builder builder = PetData.ResponseOperatePetBreed.newBuilder();
        PetData.RequestOperatePetBreed requestOperatePetBreed = null;
        try {
            //获取之前的孵化情况
            StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity  where uid='").append(uid).append("'");
            RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
            byte[] petBreedInfo = entity.getPetBreed();
            SerializeObject.PetBreedInfo lastPetBreed = SerializeObject.PetBreedInfo.parseFrom(petBreedInfo);

            // 解析客户端请求对象
            requestOperatePetBreed = PetData.RequestOperatePetBreed.parseFrom(bytes);
            int operateType = requestOperatePetBreed.getType();
            builder.setErrorId(0);
            builder.setType(operateType);
            int petBreedStatus = lastPetBreed.getPetBreedStatus();
            //创建最新的孵化对象
            SerializeObject.PetBreedInfo.Builder curBreedInfo = SerializeObject.PetBreedInfo.newBuilder();
            long nowTime = TimerHandler.nowTimeStamp;
            if (petBreedStatus != 0) { //初始状态一定没有宠物cd
                //刷新孵化cd宠物链表
                int breedCDPetCount = lastPetBreed.getBreedCDPetCount();
                if (breedCDPetCount != 0) {
                    List<SerializeObject.PetBreedCD> breedCDPetList = lastPetBreed.getBreedCDPetList();
                    for (SerializeObject.PetBreedCD petBreedCD : breedCDPetList) {
                        long breedCD = petBreedCD.getTime() - nowTime;
                        if (breedCD > 0) {
                     /*    PetData.PlainPet.Builder plainPet= PetData.PlainPet.newBuilder();
                         plainPet.setPetId(petBreedCD.getPetId());
                        plainPet.setBreedCD((int)(breedCD/1000));*/
                            SerializeObject.PetBreedCD.Builder petBreedCDBu = SerializeObject.PetBreedCD.newBuilder();
                            petBreedCDBu.setPetId(petBreedCD.getPetId());
                            petBreedCDBu.setTime(petBreedCD.getTime());
                            curBreedInfo.addBreedCDPet(petBreedCDBu);
                        }
                    }
                }
            }

            switch (operateType) {
                case 1:
                    //状态验证
                    if (petBreedStatus == 2) {

                    }
                    // 客户端请求验证
                    List<Integer> petIdList = requestOperatePetBreed.getPetIdList();
                    ArrayList<PetEntity> petList = new ArrayList<PetEntity>();
                    int maxRarity = 0;
                    for (Integer petId : petIdList) {
                        PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                        if (petEntity == null) {
                        } else {
                            //宠物要求限制的验证
                            int petLockStatus = petEntity.getLockStatus();
                            if ((petBreedStatus & (1 << PetConfig.breedLockIndex)) == 1) {

                            }
                            int petRarity = petEntity.getRarity();
                            PetCultivateConfig petCultivateConfig = (PetCultivateConfig) SuperConfig.getCongifObject(SuperConfig.petCultivateConfig, petRarity);
                            int minLVLimit = petCultivateConfig.getHatchLevelLimit();
                            if (minLVLimit > petEntity.getCurrentLevel()) {

                            }
                            if (petRarity >= maxRarity) {
                                maxRarity = petRarity;
                            }
                            petList.add(petEntity);

                        }
                    }
                    //设置正在孵化状态
                    curBreedInfo.setPetBreedStatus(2);
                    //根据最大稀有度计算孵化完成时间
                    PetCultivateConfig petCultivateConfig = (PetCultivateConfig) SuperConfig.getCongifObject(SuperConfig.petCultivateConfig, maxRarity);
                    int breedNeedTime = petCultivateConfig.getHatchTime();
                    curBreedInfo.setBreedfinishTime(breedNeedTime * 60 * 1000);
                    // 判断孵化添加了额外增大成功率的物品
                    if (requestOperatePetBreed.hasItem()) {
                        ItemData.Item item = requestOperatePetBreed.getItem();
                        curBreedInfo.setUseItem(item);
                    }
                    //更新宠物孵化锁状态
                    for (PetEntity petEntity : petList) {
                        curBreedInfo.addBreedPetId(petEntity.getPetUId());
                        petEntity.setLockStatus(petEntity.getLockStatus() + (1 << PetConfig.breedLockIndex));
                        MySql.update(petEntity);
                    }
                    break;
                case 2:
                    if (petBreedStatus != 2) {

                    }
                    curBreedInfo.setPetBreedStatus(1);
                    //解除宠物孵化锁状态
                    List<Integer> breedPetIdList = lastPetBreed.getBreedPetIdList();
                    for (Integer petId : breedPetIdList) {
                        PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                        petEntity.setLockStatus(petEntity.getLockStatus() - (1 << PetConfig.breedLockIndex));
                        MySql.update(petEntity);
                    }
                    break;
                case 3:
                    if (petBreedStatus == 1) {

                    }
                    if (nowTime < lastPetBreed.getBreedfinishTime()) {

                    }
                    //获取孵化的基本概率
                    CommonConfig commonConfigObject = (CommonConfig) SuperConfig.configMap.get(SuperConfig.commonConfig).get(60);
                    int breedSuccessProbability = commonConfigObject.getBreedSuccessProbability();
                    //获取额外物品的增加几率
                    if (lastPetBreed.hasUseItem()) {
                        ItemData.Item useItem = lastPetBreed.getUseItem();
                        ItemConfig itemConfig = (ItemConfig) SuperConfig.getCongifObject(SuperConfig.itemConfig, useItem.getId());
                        breedSuccessProbability += (itemConfig.getEffectValue() * useItem.getNum());
                    }
                    //
                    boolean isSuccessBreed = MyUtils.isSuccessRandom(breedSuccessProbability);
                    builder.setBreedIsSuccess(isSuccessBreed);
                    List<Integer> breedPetList = lastPetBreed.getBreedPetIdList();
                    List<PetEntity> petEntities = new ArrayList<PetEntity>();
                    for (Integer petId : breedPetList) {
                        PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                        petEntity.setLockStatus(petEntity.getLockStatus() - (1 << PetConfig.breedLockIndex));
                        MySql.update(petEntity);
                        //孵化完宠物进入cd
                        int rarity = petEntity.getRarity();
                        PetCultivateConfig petBreedCdConfig = (PetCultivateConfig) SuperConfig.getCongifObject(SuperConfig.petCultivateConfig, rarity);
                        SerializeObject.PetBreedCD.Builder petBreedCdBu = SerializeObject.PetBreedCD.newBuilder();
                        petBreedCdBu.setTime(nowTime + petBreedCdConfig.getBreedColdDown() * 60 * 1000);
                        petBreedCdBu.setPetId(petId);

                        curBreedInfo.addBreedCDPet(petBreedCdBu);
                        petEntities.add(petEntity);
                    }
                    curBreedInfo.setPetBreedStatus(1);
                    if (isSuccessBreed) {
                        int pet1Rarity = petEntities.get(0).getRarity();
                        int pet2Rarity = petEntities.get(1).getRarity();
                        BreedPetRarityMappingConfig breedPetRarityMappingConfig = (BreedPetRarityMappingConfig) SuperConfig.getCongifObject(SuperConfig.breedPetRarityMappingConfig, pet1Rarity);
                        List<Integer> probabilityList = null;
                        switch (pet2Rarity) {
                            case 1:
                                probabilityList = breedPetRarityMappingConfig.getRarity1();
                                break;
                            case 2:
                                probabilityList = breedPetRarityMappingConfig.getRarity2();
                                break;
                            case 3:
                                probabilityList = breedPetRarityMappingConfig.getRarity3();
                                break;
                            case 4:
                                probabilityList = breedPetRarityMappingConfig.getRarity4();
                                break;
                            case 5:
                                probabilityList = breedPetRarityMappingConfig.getRarity5();
                                break;
                        }
                        int rarity = MyUtils.ranGift(probabilityList) + 1;
                        int petType = PetUtils.getBreedPetType();
                        PetEntity newPet = PetUtils.createBreedPet(uid, petType, rarity, PetConfig.PetFromBreed);
                        builder.setNewPet(PetEntity.entityToPb(newPet));
                        PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
                        updatePetbuild.setType(1);
                        updatePetbuild.setErrorId(0);
                        updatePetbuild.addPet(PetEntity.entityToPb(newPet));
                        ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());
                    }
            }
            //刷新孵化
            SerializeObject.PetBreedInfo PetBreedInfo = curBreedInfo.build();
            entity.setPetBreed(PetBreedInfo.toByteArray());
            MySql.update(entity);
            PetData.ResponseGetPetBreedInfo.Builder responseGetPetBreedBuilder = PetUtils.PetBreedInfoToResponseGetPetBreedInfo(PetBreedInfo);
            /// System.err.println(JsonFormat.printToString(responseGetPetBreedBuilder.build()) + "更新孵化状态");
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEGETPETBREEDINFO_VALUE, responseGetPetBreedBuilder.build().toByteArray());
            /// System.err.println(JsonFormat.printToString(builder.build()));
        } catch (Exception e) {
            e.printStackTrace();
        }


        return builder.build().toByteArray();
    }

    public byte[] getPetGrowInfo(byte[] bytes, String uid) {
        PetData.ResponseGetPetGrowInfo.Builder builder = PetData.ResponseGetPetGrowInfo.newBuilder();
        try {
            StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity  where uid='").append(uid).append("'");
            RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
            byte[] petEvolutionInfo = entity.getPetEvolution();
            if (petEvolutionInfo == null) {
                builder.setErrorId(0);
            } else {
                //  SerializeObject.PetEvolution petEvolution=SerializeObject.PetEvolution.parseFrom(petEvolutionInfo);
                //   List<Integer>  petIdList=petEvolution.getPetIdList();
                //  builder.addAllPetId(petIdList);
            }
        } catch (Exception e) {
            e.printStackTrace();


        }
        return builder.build().toByteArray();
    }

    public byte[] PetEvolution(byte[] bytes, String uid) {
        PetData.ResponsePetEvolution.Builder builder = PetData.ResponsePetEvolution.newBuilder();
        try {
            PetData.RequestPetEvolution requestPetEvolution = PetData.RequestPetEvolution.parseFrom(bytes);
            int operateType = requestPetEvolution.getOperateType();
            if (operateType == 1) {
                List<Integer> petIdList = requestPetEvolution.getPetIdList();
                for (Integer petId : petIdList) {
                    PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                    if (petEntity == null) {

                    } else {
                        int accessType = petEntity.getAccessType();
                        if (accessType != PetConfig.petFromRafflePool) {

                        } else {
                            // byte[] growInfo=petEntity.getGrowInfo();
                            // SerializeObject.GrowingPetInfo growingPetInfo =SerializeObject.GrowingPetInfo.parseFrom(growInfo);
                            if (!petEntity.isGrowing()) {
                            } else {
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
        }
        return null;
    }

    public byte[] petBreakThrough(byte[] bytes, String uid) {
        PetData.ResponsePetBreakThrough.Builder builder = PetData.ResponsePetBreakThrough.newBuilder();
        builder.setErrorId(0);
        PetData.RequestPetBreakThrough requestPetBreakThrough = null;
        try {
            requestPetBreakThrough = PetData.RequestPetBreakThrough.parseFrom(bytes);
            PetEntity mainPetEntity = PetDao.getInstance().queryPet(uid, requestPetBreakThrough.getMainPetId());
            System.err.println("======Selected PetUID:"+requestPetBreakThrough.getSubPetId());
            if(requestPetBreakThrough.getSubPetId()==997||requestPetBreakThrough.getSubPetId()==998||requestPetBreakThrough.getSubPetId()==999)
            {
                if(mainPetEntity == null)
                {
                    builder.setErrorId(1);
                }
                else
                {
                    int breakLV = mainPetEntity.getBreakLV();
                    PetBreakConfig petBreakConfig = (PetBreakConfig) SuperConfig.getCongifObject(SuperConfig.petBreakConfig, breakLV + 1);
                    CommonInfo gold = petBreakConfig.getCostGold();
                    CommonInfo itemCost = petBreakConfig.getCostItem();
                    ItemUtils.updatePlayerItems(uid, gold.getKey(), gold.getValue(), false);
                    ItemUtils.updatePlayerItems(uid, itemCost.getKey(), itemCost.getValue(), false);
                    int itemid=0;
                    switch (requestPetBreakThrough.getSubPetId())
                    {
                        case 997:
                            itemid=30;
                            break;
                        case 998:
                            itemid=31;
                            break;
                        case 999:
                            itemid=32;
                            break;
                    }
                    ItemUtils.updatePlayerItems(uid, itemid, 1, false);
                    /// System.err.println("1:" + mainPetEntity);
                    mainPetEntity = PetUtils.petBreak(mainPetEntity, 1);
                    PetData.PlainPet.Builder plainPetBu = PetData.PlainPet.newBuilder();
                    plainPetBu.setPetId(mainPetEntity.getPetUId());
                    /// System.err.println("2:" + mainPetEntity);
                    plainPetBu.setBreakThroughLv(mainPetEntity.getBreakLV());
                    builder.setPet(plainPetBu);
                    PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
                    updatePetbuild.addPet(PetEntity.entityToPb(mainPetEntity));
                    updatePetbuild.setType(2);
                    updatePetbuild.setErrorId(0);
                    ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());//序列化
                }
            }
            else
            {
                PetEntity subPet = PetDao.getInstance().queryPet(uid, requestPetBreakThrough.getSubPetId());
                if (mainPetEntity == null || subPet == null) {
                    builder.setErrorId(1);
                } else {
                    int breakLV = mainPetEntity.getBreakLV();
                    PetBreakConfig petBreakConfig = (PetBreakConfig) SuperConfig.getCongifObject(SuperConfig.petBreakConfig, breakLV + 1);
                    CommonInfo gold = petBreakConfig.getCostGold();
                    CommonInfo itemCost = petBreakConfig.getCostItem();
                    ItemUtils.updatePlayerItems(uid, gold.getKey(), gold.getValue(), false);
                    ItemUtils.updatePlayerItems(uid, itemCost.getKey(), itemCost.getValue(), false);
                    /// System.err.println("1:" + mainPetEntity);

                    mainPetEntity = PetUtils.petBreak(mainPetEntity, 1);

                    PetData.PlainPet.Builder plainPetBu = PetData.PlainPet.newBuilder();
                    plainPetBu.setPetId(mainPetEntity.getPetUId());
                    /// System.err.println("2:" + mainPetEntity);
                    plainPetBu.setBreakThroughLv(mainPetEntity.getBreakLV());
                    builder.setPet(plainPetBu);
                    PetData.ResponseOperatePet.Builder deletePetbuild = PetData.ResponseOperatePet.newBuilder();
                    deletePetbuild.setType(-1);
                    deletePetbuild.setErrorId(0);
                    deletePetbuild.addPet(PetEntity.entityToPb(subPet));
                    MySql.delete(subPet);
                    ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, deletePetbuild.build().toByteArray());
                    PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
                    updatePetbuild.addPet(PetEntity.entityToPb(mainPetEntity));
                    updatePetbuild.setType(2);
                    updatePetbuild.setErrorId(0);
                    ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());//序列化
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] getPetBreedInofrmation(byte[] inBytes, String uid) throws Exception {
        //数据存放
        PetData.PetBreed.Builder buder = null;
        //返回前端
        PetData.ResponsePetBreedInofrmation.Builder responsePetList;
        PetData.ResponsePetBreedInofrmation.Builder responsePetLists;
        //反序列化
        PetData.RequestPetBreedInofrmation requestPetCompose = null;
        requestPetCompose = PetData.RequestPetBreedInofrmation.parseFrom(inBytes);
        RoleAdditionalEntity roleAdditionalEntity1 = (RoleAdditionalEntity) RoleAdditionalEntityDao.getInstance().getROleBlackMarket(uid);
        responsePetList = new PetData.ResponsePetBreedInofrmation.Builder();
        responsePetLists = new PetData.ResponsePetBreedInofrmation.Builder();
//        double goldCoins = 0;
//        for (int i = 0; i < requestPetCompose.getRequestPetDataList().size(); i++) {
//            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//            if (requestPetCompose.getRequestPetDataList().get(i).getPetId() != 0) {
//                PetEntity petEntity = PetDao.getInstance().queryPet(requestPetCompose.getRequestPetDataList().get(i).getPetId());
//                buder = new PetData.PetBreed.Builder();
//                buder.setPetState(requestPetCompose.getRequestPetDataList().get(i).getPetState());
//                buder.setPetUid(requestPetCompose.getRequestPetDataList().get(i).getPetId());
//                if (petEntity.getStarLevel() == 1) {
//                    goldCoins = 500;
//                } else if (petEntity.getStarLevel() == 2) {
//                    goldCoins = 1000;
//                } else if (petEntity.getStarLevel() == 3) {
//                    goldCoins = 2000;
//                } else if (petEntity.getStarLevel() == 4) {
//                    goldCoins = 4000;
//                } else if (petEntity.getStarLevel() == 5) {
//                    goldCoins = 8000;
//                }
//            }
//            ItemEntity itemEntity = new ItemEntity();
//            itemEntity.setItemnum(itemEntity.getItemnum() - goldCoins);
//            MySql.update(itemEntity);
//            ItemUtils.updatePlayerItems(uid,1, (int) goldCoins,false);
//            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//        }
        boolean isNull = false;
        for (int i = 0; i < requestPetCompose.getRequestPetDataList().size(); i++) {
            if (requestPetCompose.getRequestPetDataList().get(i).getPetId() == 0) {
                isNull = true;
            } else {
                isNull = false;
                break;
            }
        }
        if (isNull) {
            buder = new PetData.PetBreed.Builder();
            buder.setPetState(0);
            buder.setIndex(0);
            buder.setPetUid(0);
            buder.setPetBreedStatus(0);
            buder.setBreedfinishCD(0);
            roleAdditionalEntity1.setPetBreed(buder.build().toByteArray());
            MySql.update(roleAdditionalEntity1);
        }
        for (int i = 0; i < requestPetCompose.getRequestPetDataList().size(); i++) {
            if (requestPetCompose.getRequestPetDataList().get(i).getPetId() != 0) {
                PetEntity petEntity = PetDao.getInstance().queryPet(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                if (!petEntity.isGrowing()) {
                    RoleAdditionalEntity roleAdditionalEntity = (RoleAdditionalEntity) RoleAdditionalEntityDao.getInstance().getROleBlackMarket(petEntity.getFriendId());
                    if (roleAdditionalEntity != null) {
                        //数据库中查出的数据不为空
                        boolean isUid = false;
                        if (roleAdditionalEntity.getPetBreed() == null) {
                            //数据库查出的数据为空
                            petEntity = PetDao.getInstance().queryPet(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                            //存入数据库
                            buder = new PetData.PetBreed.Builder();
                            Date date = new Date();
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            long current = Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date))) / 1000;
                            long endTime = current;
                            buder.setPetUid(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                            buder.setIndex(i + 1);
                            buder.setPetState(requestPetCompose.getRequestPetDataList().get(i).getPetState());
                            if (petEntity.getRarity() == 1) {
                                buder.setPetBreedStatus(60);
                                endTime = endTime + 60;
                            } else if (petEntity.getRarity() == 2) {
                                buder.setPetBreedStatus(120);
                                endTime = endTime + 120;
                            } else if (petEntity.getRarity() == 3) {
                                buder.setPetBreedStatus(180);
                                endTime = endTime + 180;
                            } else if (petEntity.getRarity() == 4) {
                                buder.setPetBreedStatus(240);
                                endTime = endTime + 240;
                            } else if (petEntity.getRarity() == 5) {
                                buder.setPetBreedStatus(300);
                                endTime = endTime + 300;
                            }
                            buder.setBreedfinishCD(endTime);
                            responsePetList.addPetBreed(buder);
                            buder.setBreedfinishCD((endTime - current));
                            responsePetLists.addPetBreed(buder);
                            /// System.out.println("/// System.out.println((1 << PetConfig.formationLockIndex));" + (1 << PetConfig.formationLockIndex));
                            if ((petEntity.getLockStatus() & (1 << PetConfig.formationLockIndex)) == 0) {
                                petEntity.setLockStatus(petEntity.getLockStatus() + (1 << PetConfig.formationLockIndex));
                            }
                            MySql.update(petEntity);
                            /// System.out.println("数据库中的数据为空");
                        } else {
                            PetData.ResponsePetBreedInofrmation responsePetBreedInofrmation = PetData.ResponsePetBreedInofrmation.parseFrom(roleAdditionalEntity.getPetBreed());
                            //前端传回来的uid
                            petEntity = PetDao.getInstance().queryPet(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                            int index = 0;
                            for (int j = 0; j < responsePetBreedInofrmation.getPetBreedList().size(); j++) {
                                /// System.err.println(responsePetBreedInofrmation.getPetBreedList().get(j));
                                if (petEntity.getPetUId() == responsePetBreedInofrmation.getPetBreedList().get(j).getPetUid()) {
                                    index = j;
                                    isUid = true;
                                    break;
                                } else {
                                    index = j;
                                    isUid = false;
                                }
                            }
                            buder = new PetData.PetBreed.Builder();
                            roleAdditionalEntity1 = new RoleAdditionalEntity();
                            if (isUid) {
                                Date date = new Date();
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                long current = Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date))) / 1000;
                                buder.setBreedfinishCD(responsePetBreedInofrmation.getPetBreed(index).getBreedfinishCD());
                                buder.setPetUid(responsePetBreedInofrmation.getPetBreedList().get(index).getPetUid());
                                buder.setPetBreedStatus(responsePetBreedInofrmation.getPetBreedList().get(index).getPetBreedStatus());
                                buder.setIndex(i + 1);
                                buder.setPetState(requestPetCompose.getRequestPetDataList().get(i).getPetState());
                                responsePetList.addPetBreed(buder);
                                buder.setBreedfinishCD((buder.getBreedfinishCD() - current));
                                responsePetLists.addPetBreed(buder);
                                roleAdditionalEntity1.setPetEvolution(roleAdditionalEntity.getPetEvolution());
                                roleAdditionalEntity1.setPetFormation(roleAdditionalEntity.getPetFormation());
                                roleAdditionalEntity1.setMarketFlushNums(roleAdditionalEntity.getMarketFlushNums());
                                roleAdditionalEntity1.setBlackMarket(roleAdditionalEntity.getBlackMarket());
                                roleAdditionalEntity1.setPetBreed(responsePetList.build().toByteArray());
                                roleAdditionalEntity1.setUid(petEntity.getFriendId());
                                roleAdditionalEntity1.setId(roleAdditionalEntity.getId());
                            } else {
                                Date date = new Date();
                                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                long current = Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date))) / 1000;
                                long endTime = current;
                                buder.setPetUid(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                                buder.setIndex(i + 1);
                                buder.setPetState(requestPetCompose.getRequestPetDataList().get(i).getPetState());
                                if (petEntity.getRarity() == 1) {
                                    buder.setPetBreedStatus(60);
                                    endTime = endTime + 60;
                                } else if (petEntity.getRarity() == 2) {
                                    buder.setPetBreedStatus(120);
                                    endTime = endTime + 120;
                                } else if (petEntity.getRarity() == 3) {
                                    buder.setPetBreedStatus(180);
                                    endTime = endTime + 180;
                                } else if (petEntity.getRarity() == 4) {
                                    buder.setPetBreedStatus(240);
                                    endTime = endTime + 240;
                                } else if (petEntity.getRarity() == 5) {
                                    buder.setPetBreedStatus(300);
                                    endTime = endTime + 300;
                                }
                                buder.setBreedfinishCD(endTime);
                                responsePetList.addPetBreed(buder);
                                buder.setBreedfinishCD((endTime - current));
                                responsePetLists.addPetBreed(buder);
                                roleAdditionalEntity1.setPetEvolution(roleAdditionalEntity.getPetEvolution());
                                roleAdditionalEntity1.setPetFormation(roleAdditionalEntity.getPetFormation());
                                roleAdditionalEntity1.setMarketFlushNums(roleAdditionalEntity.getMarketFlushNums());
                                roleAdditionalEntity1.setBlackMarket(roleAdditionalEntity.getBlackMarket());
                                roleAdditionalEntity1.setPetBreed(responsePetList.build().toByteArray());
                                roleAdditionalEntity1.setUid(petEntity.getFriendId());
                                roleAdditionalEntity1.setId(roleAdditionalEntity.getId());
                                if ((petEntity.getLockStatus() & (1 << PetConfig.formationLockIndex)) == 0) {
                                    petEntity.setLockStatus(petEntity.getLockStatus() + (1 << PetConfig.formationLockIndex));
                                }
                                MySql.update(petEntity);
                                /// System.out.println(petEntity.getLockStatus());
                                /// System.out.println("数据库中的数据不为空");
                            }
                        }
                        if (i == (requestPetCompose.getRequestPetDataList().size() - 1)) {
                            MySql.update(roleAdditionalEntity1);
                            return responsePetLists.build().toByteArray();
                        }
                    } else {
                        //数据库查出的数据为空
                        petEntity = PetDao.getInstance().queryPet(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                        //存入数据库
                        responsePetList = new PetData.ResponsePetBreedInofrmation.Builder();
                        buder = new PetData.PetBreed.Builder();
                        Date date = new Date();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        long current = Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date))) / 1000;
                        long endTime = current;
                        buder.setPetUid(requestPetCompose.getRequestPetDataList().get(i).getPetId());
                        buder.setIndex(i + 1);
                        buder.setPetState(requestPetCompose.getRequestPetDataList().get(i).getPetState());
                        if (petEntity.getRarity() == 1) {
                            buder.setPetBreedStatus(60);
                            endTime = endTime + 60;
                        } else if (petEntity.getRarity() == 2) {
                            buder.setPetBreedStatus(120);
                            endTime = endTime + 120;
                        } else if (petEntity.getRarity() == 3) {
                            buder.setPetBreedStatus(180);
                            endTime = endTime + 180;
                        } else if (petEntity.getRarity() == 4) {
                            buder.setPetBreedStatus(240);
                            endTime = endTime + 240;
                        } else if (petEntity.getRarity() == 5) {
                            buder.setPetBreedStatus(300);
                            endTime = endTime + 300;
                        }
                        buder.setBreedfinishCD(endTime);
                        responsePetList.addPetBreed(buder);
                        buder.setBreedfinishCD((endTime - current));
                        responsePetLists.addPetBreed(buder);
                        if ((petEntity.getLockStatus() & (1 << PetConfig.formationLockIndex)) == 0) {
                            petEntity.setLockStatus(petEntity.getLockStatus() + (1 << PetConfig.formationLockIndex));
                        }
                        MySql.update(petEntity);
                        /// System.out.println("数据库中没有这条数据");
                        if (i == (requestPetCompose.getRequestPetDataList().size() - 1)) {
                            roleAdditionalEntity1 = new RoleAdditionalEntity();
                            roleAdditionalEntity1.setPetBreed(responsePetList.build().toByteArray());
                            roleAdditionalEntity1.setUid(petEntity.getFriendId());
                            MySql.insert(roleAdditionalEntity1);
                            MySql.update(petEntity);
                            return responsePetLists.build().toByteArray();
                        }
                    }
                }
            }
        }
        roleAdditionalEntity1.setPetBreed(responsePetList.build().toByteArray());
        MySql.update(roleAdditionalEntity1);
        return responsePetLists.build().toByteArray();

    }

    public synchronized byte[] getPetBreedOperation(byte[] inBytes, String uid) throws Exception {//有问题,数据返回有误
        PetData.RequestPetBreedOperation requestPetUid = PetData.RequestPetBreedOperation.parseFrom(inBytes);//反序列化;
        //通过uid查询出宠物
        PetEntity petEntity = PetDao.getInstance().queryPet(requestPetUid.getPetId());
        RoleAdditionalEntity roleAdditionalEntity = (RoleAdditionalEntity) RoleAdditionalEntityDao.getInstance().getROleBlackMarket(petEntity.getFriendId());
        /// System.out.println(roleAdditionalEntity.toString());
        PetData.ResponsePetBreedInofrmation responsePetBreed = null;
        try {
            responsePetBreed = PetData.ResponsePetBreedInofrmation.parseFrom(roleAdditionalEntity.getPetBreed());
        } catch (NullPointerException e) {
            /// System.err.println("空指针" + e.getMessage());
        }
        PetData.PetBreed.Builder builder = new PetData.PetBreed.Builder();
        PetData.ResponsePetBreedInofrmation.Builder responsePetBreedInofrmationBuilder = new PetData.ResponsePetBreedInofrmation.Builder();
        PetData.ResponsePetBreedInofrmation.Builder mysqlResponsePetBreedInofrmationBuilder = new PetData.ResponsePetBreedInofrmation.Builder();

        //获取当前时间
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long current = Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date))) / 1000;
        //获取数据库中的uid看是否被孵化

        switch (requestPetUid.getType()) {
            case 1:
                break;
            //将type为2 3的宠物从redis中删除
            case 2:
                //删除id为 requestPetUid.getPetId()
                break;
            case 3:
                for (int i = 0; i < responsePetBreed.getPetBreedList().size(); i++) {
                    //responsePetBreed.getPetBreedList().get(i).getBreedfinishCD() <= current
                    if (true) {
                        petEntity.setGrowing(true);
                        petEntity.setMode(2);//修改人物形态
                        if ((petEntity.getLockStatus() & (1 << PetConfig.formationLockIndex)) > 1) {//getLockStatus锁定状态
                            petEntity.setLockStatus(petEntity.getLockStatus() - (1 << PetConfig.formationLockIndex));
                        }
                        MySql.update(petEntity);
                    }
                    PetEntity petEntity1 = PetDao.getInstance().queryPet(uid, responsePetBreed.getPetBreedList().get(i).getPetUid());
                    if (!petEntity1.isGrowing()) {
                        if (responsePetBreed.getPetBreedList().get(i).getPetUid() != requestPetUid.getPetId()) {
                            builder.setIndex(responsePetBreed.getPetBreedList().get(i).getIndex());
                            builder.setPetState(responsePetBreed.getPetBreedList().get(i).getPetState());
                            builder.setPetUid(responsePetBreed.getPetBreedList().get(i).getPetUid());
                            builder.setPetBreedStatus(responsePetBreed.getPetBreedList().get(i).getPetBreedStatus());
                            builder.setBreedfinishCD(responsePetBreed.getPetBreedList().get(i).getBreedfinishCD());
                            mysqlResponsePetBreedInofrmationBuilder.addPetBreed(builder);
                            builder.setBreedfinishCD((responsePetBreed.getPetBreedList().get(i).getBreedfinishCD() - current) * 100);
                            responsePetBreedInofrmationBuilder.addPetBreed(builder);
                        }
                    }
                }
                for (int i = 0; i < responsePetBreedInofrmationBuilder.getPetBreedList().size(); i++) {
                    /// System.out.println(responsePetBreedInofrmationBuilder.getPetBreedList().get(i));
                    /// System.out.println(mysqlResponsePetBreedInofrmationBuilder.getPetBreedList().get(i));
                }
                roleAdditionalEntity.setPetBreed(mysqlResponsePetBreedInofrmationBuilder.build().toByteArray());
                MySql.update(roleAdditionalEntity);
//                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPETBREEDINOFRMATION_VALUE, responsePetBreedInofrmationBuilder.build().toByteArray());
        }
        return inBytes;
    }

    public byte[] RequestPetBreed(byte[] inBytes, String uid) throws Exception {
        /// System.err.println(uid);
        PetData.PetBreed.Builder petBuilder = new PetData.PetBreed.Builder();
        RoleAdditionalEntity roleAdditionalEntity = (RoleAdditionalEntity) RoleAdditionalEntityDao.getInstance().getROleBlackMarket(uid);
        PetData.ResponsePetBreed.Builder builder = new PetData.ResponsePetBreed.Builder();
        PetData.ResponsePetBreedInofrmation responsePetBreed = null;
        try {
            responsePetBreed = PetData.ResponsePetBreedInofrmation.parseFrom(roleAdditionalEntity.getPetBreed());
            if (responsePetBreed.getPetBreedList().size() <= 0) {
                return builder.build().toByteArray();
            } else {
                for (int i = 0; i < responsePetBreed.getPetBreedList().size(); i++) {
                    PetEntity petEntity = PetDao.getInstance().queryPet(uid, responsePetBreed.getPetBreedList().get(i).getPetUid());
                    if (!petEntity.isGrowing()) {
                        /// System.err.println(responsePetBreed.getPetBreedList().get(i));
                        Date date = new Date();
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        long current = Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date))) / 1000;
                        petBuilder.setPetUid(responsePetBreed.getPetBreedList().get(i).getPetUid());
                        petBuilder.setIndex(responsePetBreed.getPetBreedList().get(i).getIndex());
                        petBuilder.setPetState(responsePetBreed.getPetBreedList().get(i).getPetState());
                        petBuilder.setPetBreedStatus(responsePetBreed.getPetBreedList().get(i).getPetBreedStatus());
                        petBuilder.setBreedfinishCD((responsePetBreed.getPetBreedList().get(i).getBreedfinishCD() - current));
                        builder.addPetBreed(petBuilder);
                    }
                }
            }
        } catch (NullPointerException e) {
            /// System.err.println("空指针" + e.getMessage());
        }
        return builder.build().toByteArray();
    }

    //回收宠物
    public byte[] RequestRemovePet(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
        ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
        PetData.ResponseRemovePet.Builder builder = new PetData.ResponseRemovePet.Builder();
        PetData.RequestRemovePet requestRemovePet = PetData.RequestRemovePet.parseFrom(inBytes);
        List<PetEntity> petEntities = new ArrayList<>();
        double goldCoins = 0;
        int bagCost=0;
        List<ItemData.Item> stoneItem = new ArrayList<>();
        for (int i = 0; i < requestRemovePet.getPetUidList().size(); i++) {
            PetEntity petEntity = PetDao.getInstance().queryPet(uid, requestRemovePet.getPetUidList().get(i).getPetId());
            petEntities.add(petEntity);
            if (petEntity.getRarity() == 1) {
                goldCoins += 1500;
            } else if (petEntity.getRarity() == 2) {
                goldCoins += 3000;
            } else if (petEntity.getRarity() == 3) {
                goldCoins += 6000;
            } else if (petEntity.getRarity() == 4) {
                goldCoins += 12000;
            } else if (petEntity.getRarity() == 5) {
                goldCoins += 24000;
            }
            bagCost+=30;
            int itemId=0;
            switch (petEntity.getMainAttribute())
            {
                case 1:
                    itemId=25;
                    break;
                case 2:
                    itemId=26;
                    break;
                case 3:
                    itemId=27;
                    break;
                case 4:
                    itemId=28;
                    break;
                case 5:
                    itemId=29;
                    break;
            }
            int getChance=0;
            int getNumber=0;
            switch (petEntity.getRarity())
            {
                case 1:
                    getChance=10;
                    getNumber=1;
                    break;
                case 2:
                    getChance=15;
                    getNumber=1;
                    break;
                case 3:
                    getChance=25;
                    getNumber=1;
                    break;
                case 4:
                    getChance=35;
                    getNumber=2;
                    break;
                case 5:
                    getChance=55;
                    getNumber=2;
                    break;
            }
            System.err.println("Reclye itemId+"+petEntity.getMainAttribute());
            System.err.println("Reclye getChance+"+getChance);
            System.err.println("Reclye getNumber+"+getNumber);
            if((int)(Math.random()*100)<getChance)
            {
                if(itemId!=0 && getChance!=0 && getNumber!=0)
                {
                    ItemData.Item.Builder item = ItemData.Item.newBuilder();
                    item.setNum(getNumber);
                    item.setId(itemId);
                    if(stoneItem.contains(item.getId()))
                    {
                        stoneItem.set(item.getId(),stoneItem.get(item.getId()+(int)item.getNum()));
                    }
                    else
                    {
                        stoneItem.add(item.build());
                    }
                }
            }
        }
        ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, 1);
        itemEntity.setItemnum(goldCoins + itemEntity.getItemnum());
        /// System.err.println(itemEntity.toString());
        MySql.update(itemEntity);
        builder.setGoldCoins(itemEntity.getItemnum());
        reportBuilder.addItem(ItemUtils.getItemData(1, itemEntity.getItemnum()));
        ItemUtils.updatePlayerItems(uid, 21, bagCost, false);
        if(!stoneItem.isEmpty())
        {
            ItemUtils.updatePlayerItems(uid, stoneItem, true);
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
        for (int i = 0; i < petEntities.size(); i++) {
            MySql.delete(petEntities.get(i));
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestPetHatch( String uid,byte[] inBytes) throws InvalidProtocolBufferException {
        ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
        ConsumeData.ResponsePetHatch.Builder builder =ConsumeData.ResponsePetHatch.newBuilder();
        ConsumeData.RequestPetHatch requestPetHatch =ConsumeData.RequestPetHatch.parseFrom(inBytes);
        ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, 1);
        itemEntity.setItemnum( itemEntity.getItemnum());
        MySql.update(itemEntity);
        ItemUtils.updatePlayerItems(uid,1, requestPetHatch.getNum(),false);
//        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
        return builder.build().toByteArray();
    }

    public byte[] DeleteAllPets(byte[] inBytes, String uid) {

        RecycleData.ResponseDeleteAllPets.Builder builder = RecycleData.ResponseDeleteAllPets.newBuilder();
        RecycleData.RequestDeleteAllPets data = null;
        try {
            data = RecycleData.RequestDeleteAllPets.parseFrom(inBytes);

            PetDao.getInstance().DeleteAllPets(uid);

            builder.setSuccess(true);
        } catch (InvalidProtocolBufferException e) {
            builder.setSuccess(false);
        }
        return builder.build().toByteArray();
    }

    public void RequestGuidePet(byte[] inBytes, String uid){
        PetData.RequestGuidePetExtraction data = null;
        MailData.Attachment.Builder builder = MailData.Attachment.newBuilder();
        try{
            data = PetData.RequestGuidePetExtraction.parseFrom(inBytes);
            builder.setType(3);//3表示是宠物
            MailData.DefaultPet.Builder pet = MailData.DefaultPet.newBuilder();
            pet.setPetId(data.getGuidePetID());
            pet.setIsEgg(0);
            builder.addDefaultPet(pet);
            MailGetRewards.getInstance().GetRewards(uid, builder.build());
        }catch (Exception e){
            System.err.println("guide pet err");
            e.printStackTrace();
        }
    }
}
