package entities;

import javax.persistence.*;

@Entity
@Table(name = "room", schema = "", catalog = "super_star_fruit")
public class RoomEntity {
    private int id;
    private String uid;
    private int type;
    private int wallID;
    private int floorID;
    private byte[] FurnitureInfo;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "wallID")
    public int getWallID() {
        return wallID;
    }

    public void setWallID(int wallID) {
        this.wallID = wallID;
    }

    @Basic
    @Column(name = "floorID")
    public int getFloorID() {
        return floorID;
    }

    public void setFloorID(int floorID) {
        this.floorID = floorID;
    }

    @Basic
    @Column(name = "FurnitureInfo")

    public byte[] getFurnitureInfo() {
        return FurnitureInfo;
    }

    public void setFurnitureInfo(byte[] furnitureInfo) {
        FurnitureInfo = furnitureInfo;
    }
}
