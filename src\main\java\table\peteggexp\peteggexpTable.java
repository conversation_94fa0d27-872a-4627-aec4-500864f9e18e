package table.peteggexp;

import table.TableManager;

public class peteggexpTable extends TableManager<peteggexpLine> {
    private static peteggexpTable instance;
    public static peteggexpTable getInstance(){
        if (instance != null){
            return instance;
        }
        instance = new peteggexpTable();
        return instance;
    }
    private peteggexpTable(){

    }


    @Override
    public String TableName() {
        return "peteggexp";
    }

    @Override
    public String LinePath() {
        return "table.peteggexp.peteggexpLine";
    }
}
