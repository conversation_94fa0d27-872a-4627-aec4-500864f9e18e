package entities;

import javax.persistence.*;

@Entity
@Table(name = "gamerecord", schema = "", catalog = "super_star_fruit")
public class GameRecordEntity {
    private int id;
    private String uid;
    private int gamepattern;
    private int missionid;
    private int type;
    private String reward;


    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "gamepattern")
    public int getGamepattern() {
        return gamepattern;
    }

    public void setGamepattern(int gamepattern) {
        this.gamepattern = gamepattern;
    }

    @Basic
    @Column(name = "missionid")
    public int getMissionid() {
        return missionid;
    }

    public void setMissionid(int missionid) {
        this.missionid = missionid;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "reward")
    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    @Override
    public String toString() {
        return "GameRecordEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", gamepattern=" + gamepattern +
                ", missionid=" + missionid +
                ", type=" + type +
                ", reward='" + reward + '\'' +
                '}';
    }
}
