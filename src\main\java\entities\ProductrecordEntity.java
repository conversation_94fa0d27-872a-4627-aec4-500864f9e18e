package entities;

import javax.persistence.*;

@Entity
@Table(name = "productrecord", schema = "", catalog = "super_star_fruit")
public class ProductrecordEntity {


    private int id;
    private String uid;
    private int itemid;
    private String getitem;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "itemid")
    public int getItemid() {
        return itemid;
    }

    public void setItemid(int itemid) {
        this.itemid = itemid;
    }

    @Basic
    @Column(name = "getitem")
    public String getGetitem() {
        return getitem;
    }

    public void setGetitem(String getitem) {
        this.getitem = getitem;
    }
}
