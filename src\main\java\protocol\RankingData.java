// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ranking.proto

package protocol;

public final class RankingData {
  private RankingData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  /**
   * Protobuf enum {@code protocol.RankType}
   */
  public enum RankType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>LV = 1;</code>
     *
     * <pre>
     *等级
     * </pre>
     */
    LV(0, 1),
    /**
     * <code>SCORE = 2;</code>
     *
     * <pre>
     *分数
     * </pre>
     */
    SCORE(1, 2),
    /**
     * <code>PETNUM = 3;</code>
     *
     * <pre>
     *数量
     * </pre>
     */
    PETNUM(2, 3),
    ;

    /**
     * <code>LV = 1;</code>
     *
     * <pre>
     *等级
     * </pre>
     */
    public static final int LV_VALUE = 1;
    /**
     * <code>SCORE = 2;</code>
     *
     * <pre>
     *分数
     * </pre>
     */
    public static final int SCORE_VALUE = 2;
    /**
     * <code>PETNUM = 3;</code>
     *
     * <pre>
     *数量
     * </pre>
     */
    public static final int PETNUM_VALUE = 3;


    public final int getNumber() { return value; }

    public static RankType valueOf(int value) {
      switch (value) {
        case 1: return LV;
        case 2: return SCORE;
        case 3: return PETNUM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<RankType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<RankType>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<RankType>() {
            public RankType findValueByNumber(int number) {
              return RankType.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.RankingData.getDescriptor().getEnumTypes().get(0);
    }

    private static final RankType[] VALUES = values();

    public static RankType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private RankType(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.RankType)
  }

  public interface RankPlayerOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 id = 1;
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *排名
     * </pre>
     */
    boolean hasId();
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *排名
     * </pre>
     */
    int getId();

    // required .protocol.Player player = 2;
    /**
     * <code>required .protocol.Player player = 2;</code>
     */
    boolean hasPlayer();
    /**
     * <code>required .protocol.Player player = 2;</code>
     */
    protocol.FriendData.Player getPlayer();
    /**
     * <code>required .protocol.Player player = 2;</code>
     */
    protocol.FriendData.PlayerOrBuilder getPlayerOrBuilder();

    // required int32 roleId = 3;
    /**
     * <code>required int32 roleId = 3;</code>
     */
    boolean hasRoleId();
    /**
     * <code>required int32 roleId = 3;</code>
     */
    int getRoleId();

    // required int32 score = 4;
    /**
     * <code>required int32 score = 4;</code>
     *
     * <pre>
     *积分
     * </pre>
     */
    boolean hasScore();
    /**
     * <code>required int32 score = 4;</code>
     *
     * <pre>
     *积分
     * </pre>
     */
    int getScore();

    // required int32 number = 5;
    /**
     * <code>required int32 number = 5;</code>
     *
     * <pre>
     *数
     * </pre>
     */
    boolean hasNumber();
    /**
     * <code>required int32 number = 5;</code>
     *
     * <pre>
     *数
     * </pre>
     */
    int getNumber();
  }
  /**
   * Protobuf type {@code protocol.RankPlayer}
   */
  public static final class RankPlayer extends
      com.google.protobuf.GeneratedMessage
      implements RankPlayerOrBuilder {
    // Use RankPlayer.newBuilder() to construct.
    private RankPlayer(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RankPlayer(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RankPlayer defaultInstance;
    public static RankPlayer getDefaultInstance() {
      return defaultInstance;
    }

    public RankPlayer getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RankPlayer(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 18: {
              protocol.FriendData.Player.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = player_.toBuilder();
              }
              player_ = input.readMessage(protocol.FriendData.Player.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(player_);
                player_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              roleId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              score_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              number_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankingData.internal_static_protocol_RankPlayer_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankingData.internal_static_protocol_RankPlayer_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankingData.RankPlayer.class, protocol.RankingData.RankPlayer.Builder.class);
    }

    public static com.google.protobuf.Parser<RankPlayer> PARSER =
        new com.google.protobuf.AbstractParser<RankPlayer>() {
      public RankPlayer parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RankPlayer(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RankPlayer> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *排名
     * </pre>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *排名
     * </pre>
     */
    public int getId() {
      return id_;
    }

    // required .protocol.Player player = 2;
    public static final int PLAYER_FIELD_NUMBER = 2;
    private protocol.FriendData.Player player_;
    /**
     * <code>required .protocol.Player player = 2;</code>
     */
    public boolean hasPlayer() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required .protocol.Player player = 2;</code>
     */
    public protocol.FriendData.Player getPlayer() {
      return player_;
    }
    /**
     * <code>required .protocol.Player player = 2;</code>
     */
    public protocol.FriendData.PlayerOrBuilder getPlayerOrBuilder() {
      return player_;
    }

    // required int32 roleId = 3;
    public static final int ROLEID_FIELD_NUMBER = 3;
    private int roleId_;
    /**
     * <code>required int32 roleId = 3;</code>
     */
    public boolean hasRoleId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 roleId = 3;</code>
     */
    public int getRoleId() {
      return roleId_;
    }

    // required int32 score = 4;
    public static final int SCORE_FIELD_NUMBER = 4;
    private int score_;
    /**
     * <code>required int32 score = 4;</code>
     *
     * <pre>
     *积分
     * </pre>
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 score = 4;</code>
     *
     * <pre>
     *积分
     * </pre>
     */
    public int getScore() {
      return score_;
    }

    // required int32 number = 5;
    public static final int NUMBER_FIELD_NUMBER = 5;
    private int number_;
    /**
     * <code>required int32 number = 5;</code>
     *
     * <pre>
     *数
     * </pre>
     */
    public boolean hasNumber() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required int32 number = 5;</code>
     *
     * <pre>
     *数
     * </pre>
     */
    public int getNumber() {
      return number_;
    }

    private void initFields() {
      id_ = 0;
      player_ = protocol.FriendData.Player.getDefaultInstance();
      roleId_ = 0;
      score_ = 0;
      number_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPlayer()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRoleId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasScore()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasNumber()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getPlayer().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, player_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, roleId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, score_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, number_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, player_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, roleId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, score_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, number_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankingData.RankPlayer parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.RankPlayer parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.RankPlayer parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.RankPlayer parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.RankPlayer parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.RankPlayer parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.RankPlayer parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankingData.RankPlayer parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.RankPlayer parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.RankPlayer parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankingData.RankPlayer prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RankPlayer}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankingData.RankPlayerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankingData.internal_static_protocol_RankPlayer_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankingData.internal_static_protocol_RankPlayer_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankingData.RankPlayer.class, protocol.RankingData.RankPlayer.Builder.class);
      }

      // Construct using protocol.RankingData.RankPlayer.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPlayerFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (playerBuilder_ == null) {
          player_ = protocol.FriendData.Player.getDefaultInstance();
        } else {
          playerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        roleId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        score_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        number_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankingData.internal_static_protocol_RankPlayer_descriptor;
      }

      public protocol.RankingData.RankPlayer getDefaultInstanceForType() {
        return protocol.RankingData.RankPlayer.getDefaultInstance();
      }

      public protocol.RankingData.RankPlayer build() {
        protocol.RankingData.RankPlayer result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankingData.RankPlayer buildPartial() {
        protocol.RankingData.RankPlayer result = new protocol.RankingData.RankPlayer(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (playerBuilder_ == null) {
          result.player_ = player_;
        } else {
          result.player_ = playerBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.roleId_ = roleId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.score_ = score_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.number_ = number_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankingData.RankPlayer) {
          return mergeFrom((protocol.RankingData.RankPlayer)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankingData.RankPlayer other) {
        if (other == protocol.RankingData.RankPlayer.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasPlayer()) {
          mergePlayer(other.getPlayer());
        }
        if (other.hasRoleId()) {
          setRoleId(other.getRoleId());
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        if (other.hasNumber()) {
          setNumber(other.getNumber());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasId()) {
          
          return false;
        }
        if (!hasPlayer()) {
          
          return false;
        }
        if (!hasRoleId()) {
          
          return false;
        }
        if (!hasScore()) {
          
          return false;
        }
        if (!hasNumber()) {
          
          return false;
        }
        if (!getPlayer().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankingData.RankPlayer parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankingData.RankPlayer) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 id = 1;
      private int id_ ;
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *排名
       * </pre>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *排名
       * </pre>
       */
      public int getId() {
        return id_;
      }
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *排名
       * </pre>
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *排名
       * </pre>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      // required .protocol.Player player = 2;
      private protocol.FriendData.Player player_ = protocol.FriendData.Player.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.FriendData.Player, protocol.FriendData.Player.Builder, protocol.FriendData.PlayerOrBuilder> playerBuilder_;
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public boolean hasPlayer() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public protocol.FriendData.Player getPlayer() {
        if (playerBuilder_ == null) {
          return player_;
        } else {
          return playerBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public Builder setPlayer(protocol.FriendData.Player value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          player_ = value;
          onChanged();
        } else {
          playerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public Builder setPlayer(
          protocol.FriendData.Player.Builder builderForValue) {
        if (playerBuilder_ == null) {
          player_ = builderForValue.build();
          onChanged();
        } else {
          playerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public Builder mergePlayer(protocol.FriendData.Player value) {
        if (playerBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              player_ != protocol.FriendData.Player.getDefaultInstance()) {
            player_ =
              protocol.FriendData.Player.newBuilder(player_).mergeFrom(value).buildPartial();
          } else {
            player_ = value;
          }
          onChanged();
        } else {
          playerBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = protocol.FriendData.Player.getDefaultInstance();
          onChanged();
        } else {
          playerBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public protocol.FriendData.Player.Builder getPlayerBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPlayerFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      public protocol.FriendData.PlayerOrBuilder getPlayerOrBuilder() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilder();
        } else {
          return player_;
        }
      }
      /**
       * <code>required .protocol.Player player = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.FriendData.Player, protocol.FriendData.Player.Builder, protocol.FriendData.PlayerOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.FriendData.Player, protocol.FriendData.Player.Builder, protocol.FriendData.PlayerOrBuilder>(
                  player_,
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }

      // required int32 roleId = 3;
      private int roleId_ ;
      /**
       * <code>required int32 roleId = 3;</code>
       */
      public boolean hasRoleId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 roleId = 3;</code>
       */
      public int getRoleId() {
        return roleId_;
      }
      /**
       * <code>required int32 roleId = 3;</code>
       */
      public Builder setRoleId(int value) {
        bitField0_ |= 0x00000004;
        roleId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roleId = 3;</code>
       */
      public Builder clearRoleId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        roleId_ = 0;
        onChanged();
        return this;
      }

      // required int32 score = 4;
      private int score_ ;
      /**
       * <code>required int32 score = 4;</code>
       *
       * <pre>
       *积分
       * </pre>
       */
      public boolean hasScore() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 score = 4;</code>
       *
       * <pre>
       *积分
       * </pre>
       */
      public int getScore() {
        return score_;
      }
      /**
       * <code>required int32 score = 4;</code>
       *
       * <pre>
       *积分
       * </pre>
       */
      public Builder setScore(int value) {
        bitField0_ |= 0x00000008;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 score = 4;</code>
       *
       * <pre>
       *积分
       * </pre>
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000008);
        score_ = 0;
        onChanged();
        return this;
      }

      // required int32 number = 5;
      private int number_ ;
      /**
       * <code>required int32 number = 5;</code>
       *
       * <pre>
       *数
       * </pre>
       */
      public boolean hasNumber() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required int32 number = 5;</code>
       *
       * <pre>
       *数
       * </pre>
       */
      public int getNumber() {
        return number_;
      }
      /**
       * <code>required int32 number = 5;</code>
       *
       * <pre>
       *数
       * </pre>
       */
      public Builder setNumber(int value) {
        bitField0_ |= 0x00000010;
        number_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 number = 5;</code>
       *
       * <pre>
       *数
       * </pre>
       */
      public Builder clearNumber() {
        bitField0_ = (bitField0_ & ~0x00000010);
        number_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RankPlayer)
    }

    static {
      defaultInstance = new RankPlayer(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RankPlayer)
  }

  public interface RankOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *RankType
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *RankType
     * </pre>
     */
    int getType();

    // repeated .protocol.RankPlayer rankPlayers = 2;
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    java.util.List<protocol.RankingData.RankPlayer> 
        getRankPlayersList();
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    protocol.RankingData.RankPlayer getRankPlayers(int index);
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    int getRankPlayersCount();
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    java.util.List<? extends protocol.RankingData.RankPlayerOrBuilder> 
        getRankPlayersOrBuilderList();
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    protocol.RankingData.RankPlayerOrBuilder getRankPlayersOrBuilder(
        int index);

    // required int32 score = 3;
    /**
     * <code>required int32 score = 3;</code>
     *
     * <pre>
     *个人积分
     * </pre>
     */
    boolean hasScore();
    /**
     * <code>required int32 score = 3;</code>
     *
     * <pre>
     *个人积分
     * </pre>
     */
    int getScore();
  }
  /**
   * Protobuf type {@code protocol.Rank}
   */
  public static final class Rank extends
      com.google.protobuf.GeneratedMessage
      implements RankOrBuilder {
    // Use Rank.newBuilder() to construct.
    private Rank(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Rank(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Rank defaultInstance;
    public static Rank getDefaultInstance() {
      return defaultInstance;
    }

    public Rank getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Rank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                rankPlayers_ = new java.util.ArrayList<protocol.RankingData.RankPlayer>();
                mutable_bitField0_ |= 0x00000002;
              }
              rankPlayers_.add(input.readMessage(protocol.RankingData.RankPlayer.PARSER, extensionRegistry));
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              score_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          rankPlayers_ = java.util.Collections.unmodifiableList(rankPlayers_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankingData.internal_static_protocol_Rank_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankingData.internal_static_protocol_Rank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankingData.Rank.class, protocol.RankingData.Rank.Builder.class);
    }

    public static com.google.protobuf.Parser<Rank> PARSER =
        new com.google.protobuf.AbstractParser<Rank>() {
      public Rank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Rank(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Rank> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *RankType
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *RankType
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // repeated .protocol.RankPlayer rankPlayers = 2;
    public static final int RANKPLAYERS_FIELD_NUMBER = 2;
    private java.util.List<protocol.RankingData.RankPlayer> rankPlayers_;
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    public java.util.List<protocol.RankingData.RankPlayer> getRankPlayersList() {
      return rankPlayers_;
    }
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    public java.util.List<? extends protocol.RankingData.RankPlayerOrBuilder> 
        getRankPlayersOrBuilderList() {
      return rankPlayers_;
    }
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    public int getRankPlayersCount() {
      return rankPlayers_.size();
    }
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    public protocol.RankingData.RankPlayer getRankPlayers(int index) {
      return rankPlayers_.get(index);
    }
    /**
     * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
     */
    public protocol.RankingData.RankPlayerOrBuilder getRankPlayersOrBuilder(
        int index) {
      return rankPlayers_.get(index);
    }

    // required int32 score = 3;
    public static final int SCORE_FIELD_NUMBER = 3;
    private int score_;
    /**
     * <code>required int32 score = 3;</code>
     *
     * <pre>
     *个人积分
     * </pre>
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 score = 3;</code>
     *
     * <pre>
     *个人积分
     * </pre>
     */
    public int getScore() {
      return score_;
    }

    private void initFields() {
      type_ = 0;
      rankPlayers_ = java.util.Collections.emptyList();
      score_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasScore()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getRankPlayersCount(); i++) {
        if (!getRankPlayers(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      for (int i = 0; i < rankPlayers_.size(); i++) {
        output.writeMessage(2, rankPlayers_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(3, score_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      for (int i = 0; i < rankPlayers_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, rankPlayers_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, score_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankingData.Rank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.Rank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.Rank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.Rank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.Rank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.Rank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.Rank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankingData.Rank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.Rank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.Rank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankingData.Rank prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Rank}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankingData.RankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankingData.internal_static_protocol_Rank_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankingData.internal_static_protocol_Rank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankingData.Rank.class, protocol.RankingData.Rank.Builder.class);
      }

      // Construct using protocol.RankingData.Rank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRankPlayersFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (rankPlayersBuilder_ == null) {
          rankPlayers_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          rankPlayersBuilder_.clear();
        }
        score_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankingData.internal_static_protocol_Rank_descriptor;
      }

      public protocol.RankingData.Rank getDefaultInstanceForType() {
        return protocol.RankingData.Rank.getDefaultInstance();
      }

      public protocol.RankingData.Rank build() {
        protocol.RankingData.Rank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankingData.Rank buildPartial() {
        protocol.RankingData.Rank result = new protocol.RankingData.Rank(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (rankPlayersBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            rankPlayers_ = java.util.Collections.unmodifiableList(rankPlayers_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.rankPlayers_ = rankPlayers_;
        } else {
          result.rankPlayers_ = rankPlayersBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000002;
        }
        result.score_ = score_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankingData.Rank) {
          return mergeFrom((protocol.RankingData.Rank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankingData.Rank other) {
        if (other == protocol.RankingData.Rank.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (rankPlayersBuilder_ == null) {
          if (!other.rankPlayers_.isEmpty()) {
            if (rankPlayers_.isEmpty()) {
              rankPlayers_ = other.rankPlayers_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureRankPlayersIsMutable();
              rankPlayers_.addAll(other.rankPlayers_);
            }
            onChanged();
          }
        } else {
          if (!other.rankPlayers_.isEmpty()) {
            if (rankPlayersBuilder_.isEmpty()) {
              rankPlayersBuilder_.dispose();
              rankPlayersBuilder_ = null;
              rankPlayers_ = other.rankPlayers_;
              bitField0_ = (bitField0_ & ~0x00000002);
              rankPlayersBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRankPlayersFieldBuilder() : null;
            } else {
              rankPlayersBuilder_.addAllMessages(other.rankPlayers_);
            }
          }
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (!hasScore()) {
          
          return false;
        }
        for (int i = 0; i < getRankPlayersCount(); i++) {
          if (!getRankPlayers(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankingData.Rank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankingData.Rank) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *RankType
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *RankType
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *RankType
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *RankType
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.RankPlayer rankPlayers = 2;
      private java.util.List<protocol.RankingData.RankPlayer> rankPlayers_ =
        java.util.Collections.emptyList();
      private void ensureRankPlayersIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          rankPlayers_ = new java.util.ArrayList<protocol.RankingData.RankPlayer>(rankPlayers_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RankingData.RankPlayer, protocol.RankingData.RankPlayer.Builder, protocol.RankingData.RankPlayerOrBuilder> rankPlayersBuilder_;

      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public java.util.List<protocol.RankingData.RankPlayer> getRankPlayersList() {
        if (rankPlayersBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankPlayers_);
        } else {
          return rankPlayersBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public int getRankPlayersCount() {
        if (rankPlayersBuilder_ == null) {
          return rankPlayers_.size();
        } else {
          return rankPlayersBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public protocol.RankingData.RankPlayer getRankPlayers(int index) {
        if (rankPlayersBuilder_ == null) {
          return rankPlayers_.get(index);
        } else {
          return rankPlayersBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder setRankPlayers(
          int index, protocol.RankingData.RankPlayer value) {
        if (rankPlayersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankPlayersIsMutable();
          rankPlayers_.set(index, value);
          onChanged();
        } else {
          rankPlayersBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder setRankPlayers(
          int index, protocol.RankingData.RankPlayer.Builder builderForValue) {
        if (rankPlayersBuilder_ == null) {
          ensureRankPlayersIsMutable();
          rankPlayers_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankPlayersBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder addRankPlayers(protocol.RankingData.RankPlayer value) {
        if (rankPlayersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankPlayersIsMutable();
          rankPlayers_.add(value);
          onChanged();
        } else {
          rankPlayersBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder addRankPlayers(
          int index, protocol.RankingData.RankPlayer value) {
        if (rankPlayersBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankPlayersIsMutable();
          rankPlayers_.add(index, value);
          onChanged();
        } else {
          rankPlayersBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder addRankPlayers(
          protocol.RankingData.RankPlayer.Builder builderForValue) {
        if (rankPlayersBuilder_ == null) {
          ensureRankPlayersIsMutable();
          rankPlayers_.add(builderForValue.build());
          onChanged();
        } else {
          rankPlayersBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder addRankPlayers(
          int index, protocol.RankingData.RankPlayer.Builder builderForValue) {
        if (rankPlayersBuilder_ == null) {
          ensureRankPlayersIsMutable();
          rankPlayers_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankPlayersBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder addAllRankPlayers(
          java.lang.Iterable<? extends protocol.RankingData.RankPlayer> values) {
        if (rankPlayersBuilder_ == null) {
          ensureRankPlayersIsMutable();
          super.addAll(values, rankPlayers_);
          onChanged();
        } else {
          rankPlayersBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder clearRankPlayers() {
        if (rankPlayersBuilder_ == null) {
          rankPlayers_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          rankPlayersBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public Builder removeRankPlayers(int index) {
        if (rankPlayersBuilder_ == null) {
          ensureRankPlayersIsMutable();
          rankPlayers_.remove(index);
          onChanged();
        } else {
          rankPlayersBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public protocol.RankingData.RankPlayer.Builder getRankPlayersBuilder(
          int index) {
        return getRankPlayersFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public protocol.RankingData.RankPlayerOrBuilder getRankPlayersOrBuilder(
          int index) {
        if (rankPlayersBuilder_ == null) {
          return rankPlayers_.get(index);  } else {
          return rankPlayersBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public java.util.List<? extends protocol.RankingData.RankPlayerOrBuilder> 
           getRankPlayersOrBuilderList() {
        if (rankPlayersBuilder_ != null) {
          return rankPlayersBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankPlayers_);
        }
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public protocol.RankingData.RankPlayer.Builder addRankPlayersBuilder() {
        return getRankPlayersFieldBuilder().addBuilder(
            protocol.RankingData.RankPlayer.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public protocol.RankingData.RankPlayer.Builder addRankPlayersBuilder(
          int index) {
        return getRankPlayersFieldBuilder().addBuilder(
            index, protocol.RankingData.RankPlayer.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.RankPlayer rankPlayers = 2;</code>
       */
      public java.util.List<protocol.RankingData.RankPlayer.Builder> 
           getRankPlayersBuilderList() {
        return getRankPlayersFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RankingData.RankPlayer, protocol.RankingData.RankPlayer.Builder, protocol.RankingData.RankPlayerOrBuilder> 
          getRankPlayersFieldBuilder() {
        if (rankPlayersBuilder_ == null) {
          rankPlayersBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.RankingData.RankPlayer, protocol.RankingData.RankPlayer.Builder, protocol.RankingData.RankPlayerOrBuilder>(
                  rankPlayers_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          rankPlayers_ = null;
        }
        return rankPlayersBuilder_;
      }

      // required int32 score = 3;
      private int score_ ;
      /**
       * <code>required int32 score = 3;</code>
       *
       * <pre>
       *个人积分
       * </pre>
       */
      public boolean hasScore() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 score = 3;</code>
       *
       * <pre>
       *个人积分
       * </pre>
       */
      public int getScore() {
        return score_;
      }
      /**
       * <code>required int32 score = 3;</code>
       *
       * <pre>
       *个人积分
       * </pre>
       */
      public Builder setScore(int value) {
        bitField0_ |= 0x00000004;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 score = 3;</code>
       *
       * <pre>
       *个人积分
       * </pre>
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000004);
        score_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Rank)
    }

    static {
      defaultInstance = new Rank(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Rank)
  }

  public interface RequestGetRankingOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 roleUid = 1;
    /**
     * <code>required int32 roleUid = 1;</code>
     *
     * <pre>
     *uid
     * </pre>
     */
    boolean hasRoleUid();
    /**
     * <code>required int32 roleUid = 1;</code>
     *
     * <pre>
     *uid
     * </pre>
     */
    int getRoleUid();

    // required int32 type = 2;
    /**
     * <code>required int32 type = 2;</code>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 2;</code>
     */
    int getType();
  }
  /**
   * Protobuf type {@code protocol.RequestGetRanking}
   */
  public static final class RequestGetRanking extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetRankingOrBuilder {
    // Use RequestGetRanking.newBuilder() to construct.
    private RequestGetRanking(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetRanking(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetRanking defaultInstance;
    public static RequestGetRanking getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetRanking getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetRanking(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              roleUid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              type_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankingData.internal_static_protocol_RequestGetRanking_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankingData.internal_static_protocol_RequestGetRanking_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankingData.RequestGetRanking.class, protocol.RankingData.RequestGetRanking.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetRanking> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetRanking>() {
      public RequestGetRanking parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetRanking(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetRanking> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 roleUid = 1;
    public static final int ROLEUID_FIELD_NUMBER = 1;
    private int roleUid_;
    /**
     * <code>required int32 roleUid = 1;</code>
     *
     * <pre>
     *uid
     * </pre>
     */
    public boolean hasRoleUid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 roleUid = 1;</code>
     *
     * <pre>
     *uid
     * </pre>
     */
    public int getRoleUid() {
      return roleUid_;
    }

    // required int32 type = 2;
    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>required int32 type = 2;</code>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 type = 2;</code>
     */
    public int getType() {
      return type_;
    }

    private void initFields() {
      roleUid_ = 0;
      type_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRoleUid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, roleUid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, type_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, roleUid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankingData.RequestGetRanking parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.RequestGetRanking parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankingData.RequestGetRanking parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.RequestGetRanking parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankingData.RequestGetRanking prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetRanking}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankingData.RequestGetRankingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankingData.internal_static_protocol_RequestGetRanking_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankingData.internal_static_protocol_RequestGetRanking_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankingData.RequestGetRanking.class, protocol.RankingData.RequestGetRanking.Builder.class);
      }

      // Construct using protocol.RankingData.RequestGetRanking.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        roleUid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankingData.internal_static_protocol_RequestGetRanking_descriptor;
      }

      public protocol.RankingData.RequestGetRanking getDefaultInstanceForType() {
        return protocol.RankingData.RequestGetRanking.getDefaultInstance();
      }

      public protocol.RankingData.RequestGetRanking build() {
        protocol.RankingData.RequestGetRanking result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankingData.RequestGetRanking buildPartial() {
        protocol.RankingData.RequestGetRanking result = new protocol.RankingData.RequestGetRanking(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.roleUid_ = roleUid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankingData.RequestGetRanking) {
          return mergeFrom((protocol.RankingData.RequestGetRanking)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankingData.RequestGetRanking other) {
        if (other == protocol.RankingData.RequestGetRanking.getDefaultInstance()) return this;
        if (other.hasRoleUid()) {
          setRoleUid(other.getRoleUid());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRoleUid()) {
          
          return false;
        }
        if (!hasType()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankingData.RequestGetRanking parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankingData.RequestGetRanking) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 roleUid = 1;
      private int roleUid_ ;
      /**
       * <code>required int32 roleUid = 1;</code>
       *
       * <pre>
       *uid
       * </pre>
       */
      public boolean hasRoleUid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 roleUid = 1;</code>
       *
       * <pre>
       *uid
       * </pre>
       */
      public int getRoleUid() {
        return roleUid_;
      }
      /**
       * <code>required int32 roleUid = 1;</code>
       *
       * <pre>
       *uid
       * </pre>
       */
      public Builder setRoleUid(int value) {
        bitField0_ |= 0x00000001;
        roleUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roleUid = 1;</code>
       *
       * <pre>
       *uid
       * </pre>
       */
      public Builder clearRoleUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        roleUid_ = 0;
        onChanged();
        return this;
      }

      // required int32 type = 2;
      private int type_ ;
      /**
       * <code>required int32 type = 2;</code>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 type = 2;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 2;</code>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000002;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 2;</code>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetRanking)
    }

    static {
      defaultInstance = new RequestGetRanking(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetRanking)
  }

  public interface ResponseGetRankingOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.Rank rankList = 1;
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    java.util.List<protocol.RankingData.Rank> 
        getRankListList();
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    protocol.RankingData.Rank getRankList(int index);
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    int getRankListCount();
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    java.util.List<? extends protocol.RankingData.RankOrBuilder> 
        getRankListOrBuilderList();
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    protocol.RankingData.RankOrBuilder getRankListOrBuilder(
        int index);

    // optional int32 errorId = 2;
    /**
     * <code>optional int32 errorId = 2;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>optional int32 errorId = 2;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();
  }
  /**
   * Protobuf type {@code protocol.ResponseGetRanking}
   */
  public static final class ResponseGetRanking extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetRankingOrBuilder {
    // Use ResponseGetRanking.newBuilder() to construct.
    private ResponseGetRanking(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetRanking(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetRanking defaultInstance;
    public static ResponseGetRanking getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetRanking getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetRanking(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                rankList_ = new java.util.ArrayList<protocol.RankingData.Rank>();
                mutable_bitField0_ |= 0x00000001;
              }
              rankList_.add(input.readMessage(protocol.RankingData.Rank.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          rankList_ = java.util.Collections.unmodifiableList(rankList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankingData.internal_static_protocol_ResponseGetRanking_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankingData.internal_static_protocol_ResponseGetRanking_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankingData.ResponseGetRanking.class, protocol.RankingData.ResponseGetRanking.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetRanking> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetRanking>() {
      public ResponseGetRanking parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetRanking(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetRanking> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // repeated .protocol.Rank rankList = 1;
    public static final int RANKLIST_FIELD_NUMBER = 1;
    private java.util.List<protocol.RankingData.Rank> rankList_;
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    public java.util.List<protocol.RankingData.Rank> getRankListList() {
      return rankList_;
    }
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    public java.util.List<? extends protocol.RankingData.RankOrBuilder> 
        getRankListOrBuilderList() {
      return rankList_;
    }
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    public int getRankListCount() {
      return rankList_.size();
    }
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    public protocol.RankingData.Rank getRankList(int index) {
      return rankList_.get(index);
    }
    /**
     * <code>repeated .protocol.Rank rankList = 1;</code>
     */
    public protocol.RankingData.RankOrBuilder getRankListOrBuilder(
        int index) {
      return rankList_.get(index);
    }

    // optional int32 errorId = 2;
    public static final int ERRORID_FIELD_NUMBER = 2;
    private int errorId_;
    /**
     * <code>optional int32 errorId = 2;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 errorId = 2;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    private void initFields() {
      rankList_ = java.util.Collections.emptyList();
      errorId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getRankListCount(); i++) {
        if (!getRankList(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < rankList_.size(); i++) {
        output.writeMessage(1, rankList_.get(i));
      }
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(2, errorId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rankList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rankList_.get(i));
      }
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, errorId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankingData.ResponseGetRanking parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.ResponseGetRanking parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankingData.ResponseGetRanking parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankingData.ResponseGetRanking parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankingData.ResponseGetRanking prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetRanking}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankingData.ResponseGetRankingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankingData.internal_static_protocol_ResponseGetRanking_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankingData.internal_static_protocol_ResponseGetRanking_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankingData.ResponseGetRanking.class, protocol.RankingData.ResponseGetRanking.Builder.class);
      }

      // Construct using protocol.RankingData.ResponseGetRanking.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRankListFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          rankListBuilder_.clear();
        }
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankingData.internal_static_protocol_ResponseGetRanking_descriptor;
      }

      public protocol.RankingData.ResponseGetRanking getDefaultInstanceForType() {
        return protocol.RankingData.ResponseGetRanking.getDefaultInstance();
      }

      public protocol.RankingData.ResponseGetRanking build() {
        protocol.RankingData.ResponseGetRanking result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankingData.ResponseGetRanking buildPartial() {
        protocol.RankingData.ResponseGetRanking result = new protocol.RankingData.ResponseGetRanking(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (rankListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            rankList_ = java.util.Collections.unmodifiableList(rankList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rankList_ = rankList_;
        } else {
          result.rankList_ = rankListBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankingData.ResponseGetRanking) {
          return mergeFrom((protocol.RankingData.ResponseGetRanking)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankingData.ResponseGetRanking other) {
        if (other == protocol.RankingData.ResponseGetRanking.getDefaultInstance()) return this;
        if (rankListBuilder_ == null) {
          if (!other.rankList_.isEmpty()) {
            if (rankList_.isEmpty()) {
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankListIsMutable();
              rankList_.addAll(other.rankList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankList_.isEmpty()) {
            if (rankListBuilder_.isEmpty()) {
              rankListBuilder_.dispose();
              rankListBuilder_ = null;
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankListBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRankListFieldBuilder() : null;
            } else {
              rankListBuilder_.addAllMessages(other.rankList_);
            }
          }
        }
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getRankListCount(); i++) {
          if (!getRankList(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankingData.ResponseGetRanking parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankingData.ResponseGetRanking) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.Rank rankList = 1;
      private java.util.List<protocol.RankingData.Rank> rankList_ =
        java.util.Collections.emptyList();
      private void ensureRankListIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          rankList_ = new java.util.ArrayList<protocol.RankingData.Rank>(rankList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RankingData.Rank, protocol.RankingData.Rank.Builder, protocol.RankingData.RankOrBuilder> rankListBuilder_;

      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public java.util.List<protocol.RankingData.Rank> getRankListList() {
        if (rankListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankList_);
        } else {
          return rankListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public int getRankListCount() {
        if (rankListBuilder_ == null) {
          return rankList_.size();
        } else {
          return rankListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public protocol.RankingData.Rank getRankList(int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);
        } else {
          return rankListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder setRankList(
          int index, protocol.RankingData.Rank value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.set(index, value);
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder setRankList(
          int index, protocol.RankingData.Rank.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder addRankList(protocol.RankingData.Rank value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder addRankList(
          int index, protocol.RankingData.Rank value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(index, value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder addRankList(
          protocol.RankingData.Rank.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder addRankList(
          int index, protocol.RankingData.Rank.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder addAllRankList(
          java.lang.Iterable<? extends protocol.RankingData.Rank> values) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          super.addAll(values, rankList_);
          onChanged();
        } else {
          rankListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder clearRankList() {
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public Builder removeRankList(int index) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.remove(index);
          onChanged();
        } else {
          rankListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public protocol.RankingData.Rank.Builder getRankListBuilder(
          int index) {
        return getRankListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public protocol.RankingData.RankOrBuilder getRankListOrBuilder(
          int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);  } else {
          return rankListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public java.util.List<? extends protocol.RankingData.RankOrBuilder> 
           getRankListOrBuilderList() {
        if (rankListBuilder_ != null) {
          return rankListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankList_);
        }
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public protocol.RankingData.Rank.Builder addRankListBuilder() {
        return getRankListFieldBuilder().addBuilder(
            protocol.RankingData.Rank.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public protocol.RankingData.Rank.Builder addRankListBuilder(
          int index) {
        return getRankListFieldBuilder().addBuilder(
            index, protocol.RankingData.Rank.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Rank rankList = 1;</code>
       */
      public java.util.List<protocol.RankingData.Rank.Builder> 
           getRankListBuilderList() {
        return getRankListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RankingData.Rank, protocol.RankingData.Rank.Builder, protocol.RankingData.RankOrBuilder> 
          getRankListFieldBuilder() {
        if (rankListBuilder_ == null) {
          rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.RankingData.Rank, protocol.RankingData.Rank.Builder, protocol.RankingData.RankOrBuilder>(
                  rankList_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          rankList_ = null;
        }
        return rankListBuilder_;
      }

      // optional int32 errorId = 2;
      private int errorId_ ;
      /**
       * <code>optional int32 errorId = 2;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 errorId = 2;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>optional int32 errorId = 2;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000002;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 errorId = 2;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetRanking)
    }

    static {
      defaultInstance = new ResponseGetRanking(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetRanking)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RankPlayer_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RankPlayer_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Rank_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Rank_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetRanking_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetRanking_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetRanking_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetRanking_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rranking.proto\022\010protocol\032\013proto.proto\032\n" +
      "item.proto\032\ntask.proto\032\014friend.proto\032\014co" +
      "mmon.proto\032\nmail.proto\"i\n\nRankPlayer\022\n\n\002" +
      "id\030\001 \002(\005\022 \n\006player\030\002 \002(\0132\020.protocol.Play" +
      "er\022\016\n\006roleId\030\003 \002(\005\022\r\n\005score\030\004 \002(\005\022\016\n\006num" +
      "ber\030\005 \002(\005\"N\n\004Rank\022\014\n\004type\030\001 \002(\005\022)\n\013rankP" +
      "layers\030\002 \003(\0132\024.protocol.RankPlayer\022\r\n\005sc" +
      "ore\030\003 \002(\005\"2\n\021RequestGetRanking\022\017\n\007roleUi" +
      "d\030\001 \002(\005\022\014\n\004type\030\002 \002(\005\"G\n\022ResponseGetRank" +
      "ing\022 \n\010rankList\030\001 \003(\0132\016.protocol.Rank\022\017\n",
      "\007errorId\030\002 \001(\005*)\n\010RankType\022\006\n\002LV\020\001\022\t\n\005SC" +
      "ORE\020\002\022\n\n\006PETNUM\020\003B\rB\013RankingData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RankPlayer_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RankPlayer_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RankPlayer_descriptor,
              new java.lang.String[] { "Id", "Player", "RoleId", "Score", "Number", });
          internal_static_protocol_Rank_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_Rank_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Rank_descriptor,
              new java.lang.String[] { "Type", "RankPlayers", "Score", });
          internal_static_protocol_RequestGetRanking_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestGetRanking_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetRanking_descriptor,
              new java.lang.String[] { "RoleUid", "Type", });
          internal_static_protocol_ResponseGetRanking_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseGetRanking_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetRanking_descriptor,
              new java.lang.String[] { "RankList", "ErrorId", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
          protocol.ItemData.getDescriptor(),
          protocol.TaskData.getDescriptor(),
          protocol.FriendData.getDescriptor(),
          protocol.CommonData.getDescriptor(),
          protocol.MailData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
