package module.temper;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.MailEntity;
import manager.ReportManager;
import module.mail.MailDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MailData;
import protocol.ProtoData;
import protocol.TemperData;
import server.SuperServerHandler;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class temperService {
    private static Logger log = LoggerFactory.getLogger(temperService.class);
    private static temperService inst = null;

    public static temperService getInstance() {
        if (inst == null) {
            inst = new temperService();
        }
        return inst;
    }

//    public byte[] RequestTemper(String uid) {
//        TemperData.ResponseTemper responseTemper = null;
//        TemperData.ResponseTemper.Builder builder = TemperData.ResponseTemper.newBuilder();
//        MailEntity entity = new MailEntity();
//        Runnable runnable = new Runnable() {
//            @Override
//            public void run() {
//                if (SuperServerHandler.getCtxFromUid(uid) != null) {
//                    MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
//                    entity.setAttchment(attachment.build().toByteArray());
//                    MailData.ReportNewMail.Builder reportNewMail = MailData.ReportNewMail.newBuilder();
//                    reportNewMail.setNewMail(MailDao.getInstance().entityToPb(entity));
//                    ReportManager.reportInfo(uid,ProtoData.SToC.RESPONSETemper_VALUE,reportNewMail.build().toByteArray());
//                }
//            }
//
//        };
//
//        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
//        service.scheduleAtFixedRate(runnable, 1, 5, TimeUnit.MINUTES);
//        return builder.build().toByteArray();
//    }
}
