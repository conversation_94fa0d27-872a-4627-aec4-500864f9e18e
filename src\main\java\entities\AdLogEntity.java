package entities;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2019-06-03 19:33
 */
@Entity
@Table(name = "adlog", schema = "", catalog = "super_star_fruit")
public class AdLogEntity {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String item_type;
    private Double pre_num;
    private Double after_num;
    private String user_id;
    private String event_id;
    private Date operate_time;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getItem_type() {
        return item_type;
    }

    public void setItem_type(String item_type) {
        this.item_type = item_type;
    }

    public Double getPre_num() {
        return pre_num;
    }

    public void setPre_num(Double pre_num) {
        this.pre_num = pre_num;
    }

    public Double getAfter_num() {
        return after_num;
    }

    public void setAfter_num(Double after_num) {
        this.after_num = after_num;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getEvent_id() {
        return event_id;
    }

    public void setEvent_id(String event_id) {
        this.event_id = event_id;
    }

    public Date getOperate_time() {
        return operate_time;
    }

    public void setOperate_time(Date operate_time) {
        this.operate_time = operate_time;
    }
}
