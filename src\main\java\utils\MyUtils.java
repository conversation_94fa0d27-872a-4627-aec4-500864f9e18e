package utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.*;

import entities.UserdataEntity;
import manager.MySql;
import manager.Redis;
import manager.TimerHandler;

import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Base64;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.*;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * Created by nara on 2017/11/22.
 */
public class MyUtils {
    private static Gson gson = new Gson();

    private static Logger log = LoggerFactory.getLogger(MyUtils.class);

    /**
     * ��jsonת����bean����
     */
    public static Object jsonToBean(String jsonStr, Class<?> cl) {
        Object obj = null;
        if (gson != null) {
            try{
                obj = gson.fromJson(jsonStr, cl);
            }catch (Exception e){
               System.err.println(cl.toString()+"内容："+jsonStr);
                e.printStackTrace();
            }

        }
        return obj;
    }

    /**
     * ������ת����json��ʽ
     */
    public static String objectToJson(Object ts) {
        String jsonStr = null;
        if (gson != null) {
            jsonStr = gson.toJson(ts);
        }
        return jsonStr;
    }
    public static String objectToJson(List ts) {
        String jsonStr = null;
        if (gson != null) {
            jsonStr = gson.toJson(ts);
        }
        return jsonStr;
    }
    public static<T> List<T> JsonToObjectList(String json,Class<T> tClass) {
        JsonArray jsonArray =new JsonParser().parse(json).getAsJsonArray();
        List objectList=new ArrayList<T>();
        if (gson != null) {
            for(JsonElement jsonElement:jsonArray){
               T t=gson.fromJson(jsonElement,tClass);
                objectList.add(t);
            }
        }
        return  objectList;
    }
    /**
     * 设置随机字符串
     */
    public static String setRandom() {
        String rand = System.currentTimeMillis() + "";//13
        StringBuffer str = new StringBuffer(getRandomString(3)).append(rand.substring(0, 5)).append(getRandomString(3)).append(rand.substring(5, 9))
                .append(getRandomString(3)).append(rand.substring(9)).append(getRandomString(3));
        return str.toString();
    }
    public static String GenerateID() {
        return getOrder();
    }
    public static String getOrder() {
        long timeStamp = System.currentTimeMillis();

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        Date date = new Date(timeStamp);
        String stringDate = simpleDateFormat.format(date);

        String rand = System.currentTimeMillis() + "";//13
        StringBuffer str = new StringBuffer(stringDate).append(getRandomString(3)).append(rand.substring(9)).append(getRandomString(4));
        return str.toString();
    }    
    private static String getRandomString(int length) {
        String base = "abcdefghijklmnopqrstuvwxyz0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    public static String getRandomName(int length) {
        String base = "abcdefghijklmnopqrstuvwxyz";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 日期转时间戳
     */
    public static String dateToStampCompletes(String s){
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
            Date date = simpleDateFormat.parse(s);
            long ts = date.getTime();
            String res = String.valueOf(ts);
            return res;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 日期转时间戳
     */
    public static String dateToStampComplete(String s){
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = simpleDateFormat.parse(s);
            long ts = date.getTime();
            String res = String.valueOf(ts);
            return res;
        }catch (Exception e){
            e.printStackTrace();
            return null;
        }
    }
    public static long dateStringToStamp(String s){
        long ts;
        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date date = simpleDateFormat.parse(s);
             ts = date.getTime();
        }catch (Exception e){
            e.printStackTrace();
           return 0;
        }
        return ts;
    }
    /**
     * 时间戳转日期
     */
    public static String stampToDate(long s){
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date(s);
        res = simpleDateFormat.format(date);
        return res;
    }

    public static String GetTimestamp()
    {
        String timeStamp = System.currentTimeMillis()+"";
        return timeStamp.substring(0, 10);
    }
    public static String stampToDate2(long s){
        String res;
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(s);
        res = simpleDateFormat.format(date);
        return res;
    }

    /**
     * 判断是否是同一天
     */
    private static boolean isOneDay(String s1,String s2){
        int month1 = Integer.parseInt(s1.split("-")[1]);
        int month2 = Integer.parseInt(s2.split("-")[1]);
        int day1 = Integer.parseInt(s1.split("-")[2].split(" ")[0]);
        int day2 = Integer.parseInt(s2.split("-")[2].split(" ")[0]);
        if (month1 == month2 && day1 == day2){
        //    /// System.out.println(s1+"~~~"+s2);
            return true;
        }else {
            return false;
        }
    }
    public static boolean isOneDay(long t1,long t2){
        String s1 = stampToDate(t1);
        String s2 = stampToDate(t2);
        return isOneDay(s1,s2);
    }
    public static boolean isOneMonth(long t1,long t2){
        String s1 = stampToDate(t1);
        String s2 = stampToDate(t2);
        int month1 = Integer.parseInt(s1.split("-")[1]);
        int month2 = Integer.parseInt(s2.split("-")[1]);
        if (month1 == month2){
            //    /// System.out.println(s1+"~~~"+s2);
            return true;
        }else {
            return false;
        }
    }

    public static int getStampHour(long t){
        String s = stampToDate(t);
        int hour = Integer.parseInt(s.split(" ")[1].split(":")[0]);
        return hour;
    }

    /**
     * 将时间转换为时间戳
     */
    public static long dateToStamp(String s) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        Date date = simpleDateFormat.parse(s);
        long ts = date.getTime();
        return ts;
    }

    /**
     * 相距天数
     */
    public static int getApartDays(long t1,long t2){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(t1);
        String s1 = simpleDateFormat.format(date);
        Date date2 = new Date(t2);
        String s2 = simpleDateFormat.format(date2);
        try {
            long ts1 = dateToStamp(s1);
            long ts2 = dateToStamp(s2);
            int val = (int)Math.floor((ts2 - ts1)/(60*60*24*1000.0));
            return val;
        }catch (Exception e){
            return  -999;
        }
    }

    /**
     * 相距小时
     * @param t1 big
     * @param t2 small
     */
    public static int getApartHours(long t1,long t2){
        int hour1 = getStampHour(t1);
        int hour2 = getStampHour(t2);
        int apartDays = getApartDays(t1,t2);
        int apart = hour1-hour2+apartDays*24;
        return apart;
    }

    /**
     * 字符串转换成日期
     * @param str
     * @return date
     */
    public static Date strToDate(String str) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * md5加密
     */
    public static String  stringToMD5(String str){
        try {
            //生成实现指定摘要算法的 MessageDigest 对象。
            MessageDigest md = MessageDigest.getInstance("MD5");
            //使用指定的字节数组更新摘要。
            md.update(str.getBytes("utf-8"));
            //通过执行诸如填充之类的最终操作完成哈希计算。
            byte b[] = md.digest();
            //生成具体的md5密码到buf数组
            int i;
            StringBuffer buf = new StringBuffer("");
            for (int offset = 0; offset < b.length; offset++) {
                i = b[offset];
                if (i < 0)
                    i += 256;
                if (i < 16)
                    buf.append("0");
                buf.append(Integer.toHexString(i));
            }
    //        /// System.out.println("32位: " + buf.toString());// 32位的加密
//            /// System.out.println("16位: " + buf.toString().substring(8, 24));// 16位的加密，其实就是32位加密后的截取
            return buf.toString();
        }
        catch (Exception e) {
            /// System.out.println("MD5 ERROE!");
            return null;
        }
    }

    /**
     * md5解密
     */
    public static String  MD5Encode(String str,String charsetname){
        String resultString = null;
        try {
            resultString = new String(str);
            MessageDigest md = MessageDigest.getInstance("MD5");
            if (charsetname == null || "".equals(charsetname))
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes()));
            else
                resultString = byteArrayToHexString(md.digest(resultString
                        .getBytes(charsetname)));
        } catch (Exception exception) {
        }
        return resultString;
    }
    private static String byteArrayToHexString(byte b[]) {
        StringBuffer resultSb = new StringBuffer();
        for (int i = 0; i < b.length; i++) {
            resultSb.append(byteToHexString(b[i]));
        }
        return resultSb.toString();
    }
    private static final String hexDigits[] = { "0", "1", "2", "3", "4", "5",
            "6", "7", "8", "9", "a", "b", "c", "d", "e", "f" };
    private static String byteToHexString(byte b) {
        int n = b;
        if (n < 0)
            n += 256;
        int d1 = n / 16;
        int d2 = n % 16;
        return hexDigits[d1] + hexDigits[d2];
    }

    /**
     * SHA1加密
     */
    public static String stringToSHA1(String str){
        try {
            MessageDigest digest = MessageDigest
                    .getInstance("SHA-1");
            digest.update(str.getBytes());
            byte messageDigest[] = digest.digest();
            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            // 字节数组转换为 十六进制 数
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 使用 HMAC-SHA1 签名方法对对encryptText进行签名
     * @param encryptText 被签名的字符串
     * @param encryptKey  密钥
     * @return
     * @throws Exception
     */
    public static String HmacSHA1Encrypt(String encryptText, String encryptKey)
    {
        try {
            byte[] data=encryptKey.getBytes("UTF-8");
            //根据给定的字节数组构造一个密钥,第二参数指定一个密钥算法的名称
            SecretKey secretKey = new SecretKeySpec(data, "HmacSHA1");
            //生成一个指定 Mac 算法 的 Mac 对象
            Mac mac = Mac.getInstance("HmacSHA1");
            //用给定密钥初始化 Mac 对象
            mac.init(secretKey);

            byte[] text = encryptText.getBytes("UTF-8");
            //完成 Mac 操作
            byte[] val = mac.doFinal(text);
            return Base64.getEncoder().encodeToString(val);
        }catch (UnsupportedEncodingException | IllegalStateException | InvalidKeyException | NoSuchAlgorithmException e){
            return null;
        }
    }

    /**
     * 将一个 JavaBean 对象转化为一个 Map<string,string>
     *
     * @param bean
     *            要转化的JavaBean 对象
     * @return 转化出来的 Map 对象
     * @throws IntrospectionException
     *             如果分析类属性失败
     * @throws IllegalAccessException
     *             如果实例化 JavaBean 失败
     * @throws InvocationTargetException
     *             如果调用属性的 setter 方法失败
     */
    public static Map<String,String> convertBean(Object bean) throws IntrospectionException,
            IllegalAccessException, InvocationTargetException {
        Class<?> type = bean.getClass();
        Map<String,String> returnMap = new HashMap<String,String>();
        BeanInfo beanInfo = Introspector.getBeanInfo(type);

        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        for (PropertyDescriptor descriptor : propertyDescriptors) 
        {
            String propertyName = descriptor.getName();
            if (!propertyName.equals("class")) 
            {
                Method readMethod = descriptor.getReadMethod();
                Object result = readMethod.invoke(bean, new Object[0]);
                if (result != null) {
                    returnMap.put(propertyName, result.toString());
                } else {
                    returnMap.put(propertyName, "");
                }
            }
        }
        return returnMap;
    }

    /**
     * 将一个 Map 对象转化为一个 JavaBean
     *
     * @param type
     *            要转化的类型
     * @param map
     *            包含属性值的 map
     * @return 转化出来的 JavaBean 对象
     * @throws IntrospectionException
     *             如果分析类属性失败
     * @throws IllegalAccessException
     *             如果实例化 JavaBean 失败
     * @throws InstantiationException
     *             如果实例化 JavaBean 失败
     * @throws InvocationTargetException
     *             如果调用属性的 setter 方法失败
     */
    public static Object convertMap(Class type, Map map)
            throws IntrospectionException, IllegalAccessException,
            InstantiationException, InvocationTargetException {
        BeanInfo beanInfo = Introspector.getBeanInfo(type); // 获取类属性
        Object obj = type.newInstance(); // 创建 JavaBean 对象

        // 给 JavaBean 对象的属性赋值
        PropertyDescriptor[] propertyDescriptors = beanInfo
                .getPropertyDescriptors();
        for (int i = 0; i < propertyDescriptors.length; i++) {
            PropertyDescriptor descriptor = propertyDescriptors[i];
            String propertyName = descriptor.getName();

            if (map.containsKey(propertyName)) {
                // 下面一句可以 try 起来，这样当一个属性赋值失败的时候就不会影响其他属性赋值。
                Object value = map.get(propertyName);

                Object[] args = new Object[1];
                args[0] = value;

                descriptor.getWriteMethod().invoke(obj, args);
            }
        }
        return obj;
    }

    public static void clearAllUserCache(){
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("user*").iterator();
        while (iterator.hasNext()){
            String key = iterator.next();
            jedis.del(key);
        }
        Iterator<String> iterator2 = jedis.keys("role*").iterator();
        while (iterator2.hasNext()){
            String key = iterator2.next();
            jedis.del(key);
        }
        Iterator<String> iterator3 = jedis.keys("nowmission*").iterator();
        while (iterator3.hasNext()){
            String key = iterator3.next();
            jedis.del(key);
        }
        Iterator<String> iterator4 = jedis.keys("missioninfo:*").iterator();
        while (iterator4.hasNext()){
            String key = iterator4.next();
            jedis.del(key);
        }
    }


    public static Map<String, String> URLRequest(String URL) {
        Map<String, String> mapRequest = new HashMap<String, String>();

        String[] arrSplit = null;

        String strUrlParam = TruncateUrlPage(URL);
        if (strUrlParam == null) {
            return mapRequest;
        }
        // 每个键值为一组 www.2cto.com
        arrSplit = strUrlParam.split("[&]");
        for (String strSplit : arrSplit) {
            String[] arrSplitEqual = null;
            arrSplitEqual = strSplit.split("[=]");

            // 解析出键值
            if (arrSplitEqual.length > 1) {
                // 正确解析
                mapRequest.put(arrSplitEqual[0], arrSplitEqual[1]);

            } else {
                if (arrSplitEqual[0] != "") {
                    // 只有参数没有值，不加入
                    mapRequest.put(arrSplitEqual[0], "");
                }
            }
        }
        return mapRequest;
    }

    /**
     * 去掉url中的路径，留下请求参数部分
     * @param strURL url地址
     * @return url请求参数部分
     */
    private static String TruncateUrlPage(String strURL) {
        String strAllParam = null;
        String[] arrSplit = null;
        if(strURL.indexOf('?') == -1) return strURL;

        arrSplit = strURL.split("[?]");
        if (strURL.length() > 1) {
            if (arrSplit.length > 1) {
                if (arrSplit[1] != null) {
                    strAllParam = arrSplit[1];
                }
            }
        }

        return strAllParam;
    }

    public static void sendEmailNew(String uid){


    }

    public static boolean isSameDay(Date date1, Date date2) {
        if(date1 != null && date2 != null) {
            Calendar cal1 = Calendar.getInstance();
            cal1.setTime(date1);
            Calendar cal2 = Calendar.getInstance();
            cal2.setTime(date2);
            return isSameDay(cal1, cal2);
        } else {
            throw new IllegalArgumentException("The date must not be null");
        }
    }
    public static boolean isSameDay(Calendar cal1, Calendar cal2) {
        if(cal1 != null && cal2 != null) {
            return cal1.get(0) == cal2.get(0) && cal1.get(1) == cal2.get(1) && cal1.get(6) == cal2.get(6);
        } else {
            throw new IllegalArgumentException("The date must not be null");
        }
    }
    public  static UserdataEntity getUserdata(long LoginTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(LoginTime);
        String today = simpleDateFormat.format(date);
        StringBuffer hql = new StringBuffer("from UserdataEntity where date='").append(today).append("'");
            Object entity = MySql.queryForOne(hql.toString());
            if (entity == null) {
                UserdataEntity userdata = new UserdataEntity();
                userdata.setDate(today);
                Session session1 = MySql.getSession();
                session1.save(userdata);
                session1.beginTransaction().commit();
                session1.close();
                Session session2 = MySql.getSession();
                Object object = session2.createQuery(new StringBuffer("select id from UserdataEntity where date='").append(today).append("'").toString()).uniqueResult();
                session2.close();
                userdata.setId(Integer.parseInt(object.toString()));
                return  userdata ;
            } else {
                return (UserdataEntity) entity;
            }


    }

    public static int getDayInterval(long t1,long t2){
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date(t1);
        String s1 = simpleDateFormat.format(date);
        Date date2 = new Date(t2);
        String s2 = simpleDateFormat.format(date2);
        try {
            int val = (int)Math.floor((t2 - t1)/(60*60*24*1000.0));
            if(val==0&&!s1.equals(s2)){
                val=1;
            }
            return val;
        }catch (Exception e){
            return  -999;
        }
    }

    public static byte[]  objectToBytes(Object object) throws IOException {
        ByteArrayOutputStream byt =new ByteArrayOutputStream();
        ObjectOutputStream obs=new ObjectOutputStream(byt);
        obs.writeObject(object);
        return  byt.toByteArray();
    }

    public static Object  bytesToObject(byte[] bytes,Object object) throws Exception {
        ByteArrayInputStream byi =new ByteArrayInputStream(bytes);
        ObjectInputStream ois=new ObjectInputStream(byi);
        object=ois.readObject();
        return  object;
    }



    public static boolean checkIDCard(String code){
        Pattern pattern=Pattern.compile("\\d{17}[\\d|X]");
        Matcher matcher =pattern.matcher(code);
        if(!matcher.matches()){
            /// System.out.println("1");
            return false;
        }
        String addressCode=code.substring(0,6);

        String birthday=code.substring(6,14);
     /*   //大于1900年
        if(){
            return false;
        }*/
        int sum=0;
        int Ai=0;
        String AiValue=null;
        int  Wi=0;
        for(int i=0;i<code.length()-1;i++){
            AiValue=code.substring(i,i+1);
            Ai=Integer.parseInt(AiValue);
            //7 9 10 5 8 4 2 1 6 3 7 9 10 5 8 4 2
            switch (i){
                case 0:
                    Wi=7;
                    break;
                case 1:
                    Wi=9;
                    break;
                case 2:
                    Wi=10;
                    break;
                case 3:
                    Wi=5;
                    break;
                case 4:
                    Wi=8;
                    break;
                case 5:
                    Wi=4;
                    break;
                case 6:
                    Wi=2;
                    break;
                case 7:
                    Wi=1;
                    break;
                case 8:
                    Wi=6;
                    break;
                case 9:
                    Wi=3;
                    break;
                case 10:
                    Wi=7;
                    break;
                case 11:
                    Wi=9;
                    break;
                case 12:
                    Wi=10;
                    break;
                case 13:
                    Wi=5;
                    break;
                case 14:
                    Wi=8;
                    break;
                case 15:
                    Wi=4;
                    break;
                case 16:
                    Wi=2;
                    break;
                default:
                    break;
            }
            sum+=(Ai*Wi);
        }
        //Y = mod(S, 11)
        int y=sum%11;
        AiValue=code.substring(17,18);
        /// System.out.println("~~~~~~~~~~~~~~~"+y);

        int lastCheckCode=0;
        if("X".equals(AiValue)){
            lastCheckCode=10;
        }else{
            lastCheckCode=Integer.parseInt(AiValue);
        }
        if(!checksum(y,lastCheckCode)){
            /// System.out.println("2");
            return  false;
        }

        //最后去库里匹配
      /*  if(StringUtils.isEmpty("")){
            return false;
        }*/
        return  true;
    }

    private static int getchecksum(int y,int code){

        return   0;
    }

    /*      Y:     0 1 2 3 4 5 6 7 8 9 10
　　校验码:     1 0 X 9 8 7 6 5 4 3 2*/
    private static boolean checksum(int y,int code){
        int result=0;
        switch (y){
            case 0:
                result=1;
                break;
            case 1:
                result=0;
                break;
            case 2:
                result=10;
                break;
            case 3:
                result=9;
                break;
            case 4:
                result=8;
                break;
            case 5:
                result=7;
                break;
            case 6:
                result=6;
                break;
            case 7:
                result=5;
                break;
            case 8:
                result=4;
                break;
            case 9:
                result=3;
                break;
            case 10:
                result=2;
                break;
        }
        return  result==code;
    }
    public  static  boolean  isOverdue(String start,String end) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date openTime=null;
        Date endTime=null;
        try {
            openTime  =simpleDateFormat.parse(start);
            endTime =simpleDateFormat.parse(end);
        }catch (ParseException e ){
            e.printStackTrace();
            /// System.err.println(start+"~~~~"+end);
        }
        /// System.err.println(openTime.getTime()+"~~~~"+endTime.getTime()+"~~~~~~~~~~~~"+TimerHandler.nowTimeStamp);
        return  !(TimerHandler.nowTimeStamp>=openTime.getTime()&&TimerHandler.nowTimeStamp<=endTime.getTime());
    }
    //随机时，非正即负的场景
    public static boolean isSuccessRandom(int factor){
        int total=100;
        return Math.random()*total<=factor;
    }

    public static  int ranGift(List<Integer> list){
        if(list.size() == 0) return 0;
        Integer[] shuzu=new Integer[list.size()];
        Integer num=0;
        for(int i=0;i<list.size();i++){
            num+=list.get(i);
            shuzu[i]=num;
        }
        int ran=ranNum(1,shuzu[shuzu.length-1]);
        for(int i=0;i<list.size();i++){
            if(ran<=shuzu[i]){
                return i;
            }
        }
        return 0;
    }
    public static int  ranNum(int min,int max){
        /* return (int)(min+Math.random()*(max-min+min));*/
        int bound = max-min+1;
        if(bound < 1 )
            bound = 1;
        return new Random().nextInt(bound)+min;

    }
//
  public  static  int getCountdown(long timestamp){
        long cd=(timestamp-TimerHandler.nowTimeStamp)/1000;
        return cd>0?(int)cd:0;
  }
}
