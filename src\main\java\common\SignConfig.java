package common;

@ExcelConfigObject(key = "sign")
public class SignConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "item")
    private int awardItemId;
    @ExcelColumn(name = "num")
    private int awardItemNums;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAwardItemId() {
        return awardItemId;
    }

    public void setAwardItemId(int awardItemId) {
        this.awardItemId = awardItemId;
    }

    public int getAwardItemNums() {
        return awardItemNums;
    }

    public void setAwardItemNums(int awardItemNums) {
        this.awardItemNums = awardItemNums;
    }

    @Override
    public String toString() {
        return "SignConfig{" +
                "id=" + id +
                ", awardItemId=" + awardItemId +
                ", awardItemNums=" + awardItemNums +
                '}';
    }
}
