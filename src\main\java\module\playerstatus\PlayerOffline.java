package module.playerstatus;

import module.role_offline_time.RoleOfflineTimeDao;

public class PlayerOffline {
    private static PlayerOffline inst = null;
    public static PlayerOffline getInstance() {
        if (inst == null) {
            inst = new PlayerOffline();
        }
        return inst;
    }
    // 下线处理
    public void SendMessage(String uid) {
        RoleOfflineTimeDao.getInstance().Update(uid);
    }
}
