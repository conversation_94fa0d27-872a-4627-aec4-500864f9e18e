package module.activity.LimitedTime;

import entities.LimitedTimeRewardCompleteTaskEntity;
import manager.MySql;
import org.hibernate.Session;

import java.util.List;

public class LimitedTimeRewardCompleteTaskDao {
    private static LimitedTimeRewardCompleteTaskDao inst = null;
    public static LimitedTimeRewardCompleteTaskDao getInstance() {
        if (inst == null) {
            inst = new LimitedTimeRewardCompleteTaskDao();
        }
        return inst;
    }

    public void insert(LimitedTimeRewardCompleteTaskEntity entity){
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public void update(LimitedTimeRewardCompleteTaskEntity entity) {
        Session session = MySql.getSession();
        session.update(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public LimitedTimeRewardCompleteTaskEntity GetCompleteDailyTaskEntity(String uid, String dailyId) {
        StringBuffer stringBuffer = new StringBuffer("from LimitedTimeRewardCompleteTaskEntity where uid='").append(uid).append("'")
                .append(" and daily_task_id='").append(dailyId).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (LimitedTimeRewardCompleteTaskEntity) entity;
    }

    public List<Object> GetAll(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from LimitedTimeRewardCompleteTaskEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());
        return list;
    }

    public void Delete() {
        StringBuffer stringBuffer = new StringBuffer("Delete from LimitedTimeRewardCompleteTaskEntity");
        MySql.updateSomes(stringBuffer.toString());
    }
}
