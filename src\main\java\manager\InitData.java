package manager;

import common.SuperConfig;
import module.item.ItemService;
import module.mission.MissionDao;
import module.robot.Factory;
import module.room.RoomDao;
import module.synchronization.RoomManager;
import redis.clients.jedis.Jedis;
import utils.MyUtils;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.Vector;

/**
 * Created by nara on 2018/1/22.
 */
public class InitData {
    public static void initGameConfig(){
        Redis jedis = Redis.getInstance();


//        MissionDao.endlessList = new ArrayList<Integer>();
//        Iterator<String> assortIter = jedis.keys(SuperConfig.REDIS_EXCEL_ASSORT+"*").iterator();
//        while (assortIter.hasNext()){
//            String key = assortIter.next();
//            int endless = Integer.parseInt(jedis.hget(key,"endless"));
//            if (endless == 1){
//                int id = Integer.parseInt(key.split(":")[1]);
//                MissionDao.endlessList.add(id);
//            }
//        }

        long timeStamp = System.currentTimeMillis();
        String string = MyUtils.stampToDate(timeStamp);
        String newStr = string.split(" ")[0]+ " 03:00:00";
        String threeStamp = MyUtils.dateToStampComplete(newStr);
        if (threeStamp != null){
            int val = (int)(timeStamp - Long.parseLong(threeStamp))/1000;
            TimerHandler.dailyThreeHour = val;
        }
        newStr = string.split(":")[0]+ ":00:00";
        String oneStamp = MyUtils.dateToStampComplete(newStr);
        if (oneStamp != null){
            int val = (int)(timeStamp - Long.parseLong(oneStamp))/1000;
            TimerHandler.oneHourRank = val;
        }
        newStr = string.split(" ")[0]+ " 00:00:00";
        String oneHourStamp = MyUtils.dateToStampComplete(newStr);
        if (oneHourStamp != null){
            int val = (int)(timeStamp - Long.parseLong(oneHourStamp))/1000;
            TimerHandler.dailyOneHour = val;
            TimerHandler.todayStr = MyUtils.stampToDate2(timeStamp);
        }
       String TimeStamp12th = string.split(" ")[0]+ " 12:00:00";
        long LuckyItemStamp12th = Long.parseLong(MyUtils.dateToStampComplete(TimeStamp12th));
        String TimeStamp18th = string.split(" ")[0]+ " 18:00:00";
        long LuckyItemStamp18th = Long.parseLong(MyUtils.dateToStampComplete(TimeStamp18th));
       if(timeStamp>LuckyItemStamp12th){
           TimerHandler.daily12th=(int)(timeStamp -LuckyItemStamp12th)/1000;

       }else{
           TimerHandler.daily12th=60*60*24-(int)(LuckyItemStamp12th-timeStamp)/1000;
       }

        if(timeStamp>LuckyItemStamp18th){
            TimerHandler.daily18th=(int)(timeStamp -LuckyItemStamp18th)/1000;

        }else{
            TimerHandler.daily18th=60*60*24-(int)(LuckyItemStamp18th-timeStamp)/1000;
        }
 /*      Jedis redis =Redis.getJedis(-1);
        String luckyItemVersion=redis.get("luckItemVersion");
       if(luckyItemVersion==null){
              redis.set("luckItemVersion","0");
       }else {
           TimerHandler.luckItemVersion = Integer.parseInt(luckyItemVersion);
       }
       redis.close();
*/
    /*    char minuteLeft=string.charAt(14);
        int nowMinuteLeft=  Integer.parseInt(String.valueOf(minuteLeft));
        int  nowMinute=Integer.parseInt(string.substring(14,16));
        int nowSecond=Integer.parseInt(string.substring(17,19));
        if(nowMinuteLeft>=3){
            TimerHandler.halfHour=60*30-(60*60-(nowMinute*60+nowSecond));
        }else{
            TimerHandler.halfHour=60*30-(30*60-(nowMinute*60+nowSecond));
        }*/
       // /// System.out.print(TimerHandler.halfHour);
    }
}
