package manager;

import common.SuperConfig;
import entities.MapEntity;
import entities.RoleEntity;
import entities.UserEntity;
import model.LoginInfo;
import model.RoleInfo;
import model.SqlCallBackInfo;
import module.callback.CallBack;
import org.hibernate.HibernateException;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.cfg.Configuration;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by nara on 2017/11/21.
 */
public class MySql {
    private static Logger log = LoggerFactory.getLogger(MySql.class);
    private static SessionFactory sessionFactory;
    private static final ThreadLocal<Session> threadLocal = new ThreadLocal<Session>();

    // public final static int maxSize = 12;
    public final static int maxSize = 600;
    private static int nowSize = 0;
    //public static List<SqlCallBackInfo> sqlList = new ArrayList<SqlCallBackInfo>();
    public static List<SqlCallBackInfo> sqlList = new ArrayList<SqlCallBackInfo>();
    private static Thread updateThread = null;

    public final static int loginMaxSize = 6;
    private static int loginNowSize = 0;
    public static List<LoginInfo> loginList = new ArrayList<LoginInfo>();
    public static List<RoleInfo> RoleList = new ArrayList<RoleInfo>();
    private static Thread loginThread = null;

    public synchronized static void addNowSize() {
        nowSize++;
    }

    public synchronized static void reduceNowSize() {
        nowSize--;
    }

    public synchronized static void addLoginNowSize() {
        loginNowSize++;
    }

    public synchronized static void reduceLoginNowSize() {
        loginNowSize--;
    }

    public static int getLoginNowSize() {
        return loginNowSize;
    }

    public static int getNowSize() {
        return nowSize;
    }

    public static void getInstance() {
        try {
            Configuration configuration = new Configuration().configure();//创建配置对象
            sessionFactory = configuration.buildSessionFactory();//创建会话工厂
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        startUpdateHandler();
        startLoginHandler();
    }

    public static Session getSession() throws HibernateException {
        if (sessionFactory == null || sessionFactory.isClosed()) {
            try {
                sessionFactory = null;
                Configuration configuration = new Configuration().configure();//创建配置对象
                sessionFactory = configuration.buildSessionFactory();//创建会话工厂
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        Session session = sessionFactory.openSession();
//        Session session = threadLocal.get();
//        if (session == null || !session.isOpen()) {
//            if (sessionFactory == null || sessionFactory.isClosed()) {
//                try {
//                    sessionFactory = null;
//                    Configuration configuration = new Configuration().configure();//创建配置对象
//                    sessionFactory = configuration.buildSessionFactory();//创建会话工厂
//                }catch (Exception e){
//                    log.error(e.getMessage(),e);
//                }
//            }
//            session = (sessionFactory != null) ? sessionFactory.openSession() : null;
//            threadLocal.set(session);
//        }
        return session;
    }

    public static void closeSession() throws HibernateException {
        Session session = threadLocal.get();
        threadLocal.set(null);
        if (session != null) {
            session.close();
        }
//        sessionFactory.close();
    }

    private static Thread getUpdateInstance() {
        if (updateThread == null) {
            UpdateHandler updateHandler = UpdateHandler.getInstance();
            updateThread = new Thread(updateHandler);
        }
        return updateThread;
    }

    private static void startUpdateHandler() {
        Thread thread = getUpdateInstance();
        thread.start();
    }

    private static Thread getLoginInstance() {
        if (loginThread == null) {
            LoginHandler loginHandler = LoginHandler.getInstance();
            loginThread = new Thread(loginHandler, "superStarLoginThread");
        }
        return loginThread;
    }

    private static void startLoginHandler() {
        Thread thread = getLoginInstance();
        thread.start();
    }

    public static Object queryForOne(String sql) {
        Object obj = null;
        try {
            Session session = MySql.getSession();
            obj = session.createQuery(sql).uniqueResult();
            session.close();
        } catch (Exception e) {
//            /// System.err.println(sql);
            e.printStackTrace();
        } finally {
            return obj;
        }

    }

    public static Object queryForOne(String sql, int limit) {
        Session session = MySql.getSession();
        Query query = session.createQuery(sql);
        query.setMaxResults(limit);
        Object obj = query.uniqueResult();
        session.close();
        return obj;
    }


    public static List<Object> queryForList(String sql, int limit) {
        Session session = MySql.getSession();
        Query query = session.createQuery(sql);
        query.setMaxResults(limit);
        List<Object> objList = query.list();
        session.close();
        return objList;
    }

    public static List<Object> queryForList(String sql) {
        Session session = MySql.getSession();
        List<Object> objList = session.createQuery(sql).list();
        session.close();
        return objList;
    }


    public static int insertForCreateUser(Object entity) {
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
        Session session2 = MySql.getSession();
        UserEntity userEntity = (UserEntity) entity;
        Object object = session2.createQuery("select id from UserEntity where userid='" + userEntity.getUserid() + "'").uniqueResult();
        session2.close();
        return Integer.parseInt(object.toString());
    }

    public static int insertForCreateRole(Object entity) {
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
        Session session2 = MySql.getSession();
        RoleEntity roleEntity = (RoleEntity) entity;
        Object object = session2.createQuery("select id from RoleEntity where uid='" + roleEntity.getUid() + "'").uniqueResult();
        session2.close();
        return Integer.parseInt(object.toString());
    }


    public static void insert(Object entity) {
        SqlCallBackInfo sqlCallBackInfo = new SqlCallBackInfo();
        sqlCallBackInfo.setMold(SuperConfig.DB_INSERT);
        // /// System.out.println(entity.toString());
        sqlCallBackInfo.setObject(entity);
        MySql.sqlList.add(sqlCallBackInfo);
//        MySql.sqlList.add(entity);
    }

    public static void mustInsert(Object entity) {
        MysqlHandler mysqlHandler = new MysqlHandler();
        mysqlHandler.setType(SuperConfig.DB_INSERT);
        mysqlHandler.setEntity(entity);

        Thread thread = new Thread(mysqlHandler);
        thread.start();
    }

    public static void update(Object entity) {
        MysqlHandler mysqlHandler = new MysqlHandler();
        mysqlHandler.setType(SuperConfig.DB_UPDATE);
        mysqlHandler.setEntity(entity);
        Thread thread = new Thread(mysqlHandler, "update");
        thread.start();
    }

    public static void updateSomes(String hql) {
        try {
            SqlCallBackInfo sqlCallBackInfo = new SqlCallBackInfo();
            sqlCallBackInfo.setMold(SuperConfig.DB_UPDATESOMES);
            sqlCallBackInfo.setHql(hql);
            MySql.sqlList.add(sqlCallBackInfo);
        } catch (Exception e) {
            e.printStackTrace();
///            /// System.err.println(hql);
        }

        //  MySql.sqlList.add(hql);
    }

    public static void addSexToUid(String uid ,int sex) {

    }

    public static void mustUpdateSomes(String hql) {
        MysqlHandler mysqlHandler = new MysqlHandler();
        mysqlHandler.setType(SuperConfig.DB_UPDATESOMES);
        mysqlHandler.setHql(hql);
        Thread thread = new Thread(mysqlHandler, "mustUpdateSomes");
        thread.start();
    }

    public static void delete(Object entity) {
        MysqlHandler mysqlHandler = new MysqlHandler();
        mysqlHandler.setType(SuperConfig.DB_DELETE);
        mysqlHandler.setEntity(entity);
        Thread thread = new Thread(mysqlHandler, "delete");
        thread.start();
    }

    public static void queryInSql(String hql, CallBack callBack, int mold) {
        SqlCallBackInfo sqlCallBackInfo = new SqlCallBackInfo();
        sqlCallBackInfo.setHql(hql);
        //sqlCallBackInfo.setType(type);
        sqlCallBackInfo.setMold(mold);
        sqlCallBackInfo.setCallBack(callBack);
        MySql.sqlList.add(sqlCallBackInfo);
    }

    public static void queryInSql(SqlCallBackInfo sqlCallBackInfo) {
        MysqlHandler mysqlHandler = new MysqlHandler();
        mysqlHandler.setType(sqlCallBackInfo.getMold());
        mysqlHandler.setHql(sqlCallBackInfo.getHql());
        mysqlHandler.setCallBack(sqlCallBackInfo.getCallBack());
        Thread thread = new Thread(mysqlHandler, "queryInSql");
        thread.start();
    }

    public static int fastUpdate(Object entity) {
        Session session = MySql.getSession();
        try {
            session.update(entity);
            session.beginTransaction().commit();
        } catch (Exception e) {
            e.printStackTrace();
            ///           /// System.err.println(entity+"exception");
            return -1;
        } finally {
            session.close();
        }
        return 0;
    }


}
