package module.equip;

import com.google.protobuf.InvalidProtocolBufferException;
import com.googlecode.protobuf.format.JsonFormat;
import common.EquipmentConfig;
import entities.EquipEntity;
import manager.Redis;
import manager.ReportManager;
import module.item.ItemService;
import module.login.ILogin;
import module.login.LoginDao;
import module.robot.ranName;
import protocol.EquipData;
import protocol.PetData;
import protocol.ProtoData;


import java.util.*;

public class EquipService {
    private static EquipService inst = null;

    public static EquipService getInstance() {
        if (inst == null) {
            inst = new EquipService();
        }
        return inst;
    }

    public byte[] getEquip(byte[] bytes, String uid) {
        EquipData.RequestGetEquip equip = null;
        EquipData.ResponseGetEquip.Builder builder = EquipData.ResponseGetEquip.newBuilder();
        builder.setErrorId(0);
        try {
            EquipDao equipDao = EquipDao.getInstance();
            List<Object> equipList = equipDao.queryEquip(uid);
            for (int i = 0; i < equipList.size(); i++) {
                EquipEntity entity = (EquipEntity) equipList.get(i);
                builder.addEquip(EquipEntity.entityToPb(entity));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
//        System.err.println(JsonFormat.printToString(builder.build()) + "");
        return builder.build().toByteArray();
    }

    /**
     * 操作装备
     * type=1 创建装备
     *
     * @param bytes
     * @param uid
     * @return
     */
    public byte[] operateEquip(byte[] bytes, String uid) {
        EquipData.RequestOperateEquip requestOperateEquip = null;
        EquipData.ResponseOperateEquip.Builder builder = EquipData.ResponseOperateEquip.newBuilder();
        try {
            requestOperateEquip = EquipData.RequestOperateEquip.parseFrom(bytes);
            int type = requestOperateEquip.getType();
            builder.setErrorId(0);
            EquipEntity equipEntity = new EquipEntity();
            EquipDao equipDao = EquipDao.getInstance();
            int equipId = equipEntity.getEquipEid();
            equipEntity = equipDao.queryEquip(uid, equipId);
            switch (type) {
                case 1:
                    Redis jedis = Redis.getInstance();
                    Iterator<String> iterator = jedis.keys("equipmentconfig:*").iterator();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        Map<String, String> mailMap = jedis.hgetAll(key);
                        int equipType = new Random().nextInt(Integer.parseInt(mailMap.get("ID")));
                        EquipData.Equip equip = EquipEntity.entityToPb(EquipUtils.createEquip(uid, equipType));
                        builder.setEquip(equip);
                        break;

                    }
            }
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

//    public byte[] changeEquip(byte[] bytes, String uid) {
//        EquipData.RequestChangeEquip requestChangeEquip = null;
//        EquipData.ResponseChangeEquip.Builder builder = EquipData.ResponseChangeEquip.newBuilder();
//        if (uid == null) {
//            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
//        } else {
//            try {
//                requestChangeEquip = EquipData.RequestChangeEquip.parseFrom(bytes);
//            } catch (InvalidProtocolBufferException e) {
//                e.printStackTrace();
//            }
//            if (requestChangeEquip == null) {
//                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
//            } else {
//                ILogin iLogin = LoginDao.getInstance();
//                builder = iLogin.changeEquip(uid, requestChangeEquip.getId(), requestChangeEquip.getType(), requestChangeEquip.getEquip());
//            }
//        }
//        return builder.build().toByteArray();
//    }

    /**
     * 选择装备
     *
     * @param bytes
     * @param uid
     * @return
     */
    public byte[] choiceEquip(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        EquipData.RequestChoiceEquip requestChoiceEquip = null;
        EquipData.ResponseChoiceEquip.Builder builder = EquipData.ResponseChoiceEquip.newBuilder();
//        if (uid == null) {
//            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
//        } else {
//            try {
//                requestChoiceEquip = EquipData.RequestChoiceEquip.parseFrom(bytes);
//            } catch (Exception e) {
//               e.printStackTrace();
//            }
//            if (requestChoiceEquip == null) {
//                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
//            } else {
//                ILogin iLogin = LoginDao.getInstance();
//                int n = iLogin.changeEquip(uid,requestChoiceEquip.getEquip());
//                if (n == 1){
//                    builder.setStatus(true);
//                }else{
//                    builder.setStatus(false);
//                    if (n > 1){
//                        builder.setErrorId(n);
//                    }
//                }
//                builder.setEquip(requestChoiceEquip.getEquip());
//            }
//        }
        return builder.build().toByteArray();
//        requestChoiceEquip = EquipData.RequestChoiceEquip.parseFrom(bytes);
//        int type = requestChoiceEquip.getType();
//        EquipDao equipDao = new EquipDao();
//        List<Object> equipList = equipDao.queryEquip(uid);
//        switch (type){
//            case 1:
//                List<Object> choice = new ArrayList<>();
//                if (equipList.size() >= 4) {
//                    choice.add(equipList.get(0));
//                    choice.add(equipList.get(1));
//                    choice.add(equipList.get(2));
//                    choice.add(equipList.get(3));
//                }
//                EquipData.Equip equip = EquipEntity.entityToPb((EquipEntity) choice);
//                equip.getEquipEid();
//                builder.setEquip(equip);
//                break;
//            case 2:
//                choice= Collections.singletonList(builder.getEquip());
//                choice.clear();
//        }
//        builder.getEquip();
//        return builder.build().toByteArray();
    }
}
