package model;

import module.callback.CallBack;

/**
 * Created by nara on 2018/5/8.
 */
public class SqlCallBackInfo {
    private int mold;
    private String hql;
    private CallBack callBack;
    private Object object;

    public SqlCallBackInfo() {
        this.hql = null;
        this.callBack = null;
        this.object = null;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(Object object) {
        this.object = object;
    }

    public CallBack getCallBack() {
        return callBack;
    }

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    public String getHql() {
        return hql;
    }

    public void setHql(String hql) {
        this.hql = hql;
    }

    public int getMold() {
        return mold;
    }

    public void setMold(int mold) {
        this.mold = mold;
    }
}
