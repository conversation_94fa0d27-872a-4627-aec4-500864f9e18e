package common;

import java.util.List;


@ExcelConfigObject(key = "starupCoefficientSet")
public class PetAttributeConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "star")
    private int starLv;
    @ExcelColumn(name = "hp_coefficient", isList = true, isFloat = true)
    private List<Float> hpCoefficient;
    @ExcelColumn(name = "atk_coefficient", isList = true, isFloat = true)
    private List<Float> atkcoefficient;
    @ExcelColumn(name = "def_coefficient", isList = true, isFloat = true)
    private List<Float> defCoefficient;
    @ExcelColumn(name = "satk_coefficient", isList = true, isFloat = true)
    private List<Float> satkCoefficient;
    @ExcelColumn(name = "sdef_coefficient", isList = true, isFloat = true)
    private List<Float> sdefCoefficient;
    @ExcelColumn(name = "speed_coefficient", isList = true, isFloat = true)
    private List<Float> speedCoefficient;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getStarLv() {
        return starLv;
    }

    public void setStarLv(int starLv) {
        this.starLv = starLv;
    }

    public List<Float> getHpCoefficient() {
        return hpCoefficient;
    }

    public void setHpCoefficient(List<Float> hpCoefficient) {
        this.hpCoefficient = hpCoefficient;
    }

    public List<Float> getAtkcoefficient() {
        return atkcoefficient;
    }

    public void setAtkcoefficient(List<Float> atkcoefficient) {
        this.atkcoefficient = atkcoefficient;
    }

    public List<Float> getDefCoefficient() {
        return defCoefficient;
    }

    public void setDefCoefficient(List<Float> defCoefficient) {
        this.defCoefficient = defCoefficient;
    }

    public List<Float> getSatkCoefficient() {
        return satkCoefficient;
    }

    public void setSatkCoefficient(List<Float> satkCoefficient) {
        this.satkCoefficient = satkCoefficient;
    }

    public List<Float> getSdefCoefficient() {
        return sdefCoefficient;
    }

    public void setSdefCoefficient(List<Float> sdefCoefficient) {
        this.sdefCoefficient = sdefCoefficient;
    }

    public List<Float> getSpeedCoefficient() {
        return speedCoefficient;
    }

    public void setSpeedCoefficient(List<Float> speedCoefficient) {
        this.speedCoefficient = speedCoefficient;
    }
}
