package common;

import java.util.List;

@ExcelConfigObject(key = "monster")
public class MonsterConfig {
//    @ExcelColumn(name = "id")
//    private int id;
//    @ExcelColumn(name = "name")
//    private int name;
//    @ExcelColumn(name = "hp_default")
//    private int hp;
//    @ExcelColumn(name = "atk_default")
//    private int atk;
//    @ExcelColumn(name = "def_default")
//    private int def;
//    @ExcelColumn(name = "spatk_default")
//    private int spatk;
//    @ExcelColumn(name = "spdef_default")
//    private int spdef;
//    @ExcelColumn(name = "speed_default")
//    private int speed;
//    @ExcelColumn(name = "typeone")
//    private int typeone;
//    @ExcelColumn(name = "normalskillid")
//    private int normalskillid;
//    @ExcelColumn(name = "spskillid")
//    private int spskillid;
//    @ExcelColumn(name = "havegold")
//    private int havegold;
//    @ExcelColumn(name = "haveexp")
//    private int haveexp;
//    @ExcelColumn(name = "monsterlv")
//    private int monsterlv;
//    @ExcelColumn(name = "powerlv")
//    private int powerlv;
//    @ExcelColumn(name = "splv")
//    private int splv;
//
//    public int getName() {
//        return name;
//    }
//
//    public void setName(int name) {
//        this.name = name;
//    }
//
//    public int getHp() {
//        return hp;
//    }
//
//    public void setHp(int hp) {
//        this.hp = hp;
//    }
//
//    public int getAtk() {
//        return atk;
//    }
//
//    public void setAtk(int atk) {
//        this.atk = atk;
//    }
//
//    public int getDef() {
//        return def;
//    }
//
//    public void setDef(int def) {
//        this.def = def;
//    }
//
//    public int getSpatk() {
//        return spatk;
//    }
//
//    public void setSpatk(int spatk) {
//        this.spatk = spatk;
//    }
//
//    public int getSpdef() {
//        return spdef;
//    }
//
//    public void setSpdef(int spdef) {
//        this.spdef = spdef;
//    }
//
//    public int getSpeed() {
//        return speed;
//    }
//
//    public void setSpeed(int speed) {
//        this.speed = speed;
//    }
//
//    public int getTypeone() {
//        return typeone;
//    }
//
//    public void setTypeone(int typeone) {
//        this.typeone = typeone;
//    }
//
//    public int getNormalskillid() {
//        return normalskillid;
//    }
//
//    public void setNormalskillid(int normalskillid) {
//        this.normalskillid = normalskillid;
//    }
//
//    public int getSpskillid() {
//        return spskillid;
//    }
//
//    public void setSpskillid(int spskillid) {
//        this.spskillid = spskillid;
//    }
//
//    public int getHavegold() {
//        return havegold;
//    }
//
//    public void setHavegold(int havegold) {
//        this.havegold = havegold;
//    }
//
//    public int getHaveexp() {
//        return haveexp;
//    }
//
//    public void setHaveexp(int haveexp) {
//        this.haveexp = haveexp;
//    }
//
//    public int getMonsterlv() {
//        return monsterlv;
//    }
//
//    public void setMonsterlv(int monsterlv) {
//        this.monsterlv = monsterlv;
//    }
//
//    public int getPowerlv() {
//        return powerlv;
//    }
//
//    public void setPowerlv(int powerlv) {
//        this.powerlv = powerlv;
//    }
//
//    public int getSplv() {
//        return splv;
//    }
//
//    public void setSplv(int splv) {
//        this.splv = splv;
//    }
//
//    public int getId() {
//
//        return id;
//    }
//
//    public void setId(int id) {
//        this.id = id;
//    }
//
//    @Override
//    public String toString() {
//        return "MonsterConfig{" +
//                "id=" + id +
//                ", name=" + name +
//                ", hp=" + hp +
//                ", atk=" + atk +
//                ", def=" + def +
//                ", spatk=" + spatk +
//                ", spdef=" + spdef +
//                ", speed=" + speed +
//                ", typeone=" + typeone +
//                ", normalskillid=" + normalskillid +
//                ", spskillid=" + spskillid +
//                ", havegold=" + havegold +
//                ", haveexp=" + haveexp +
//                ", monsterlv=" + monsterlv +
//                ", powerlv=" + powerlv +
//                ", splv=" + splv +
//                '}';
//    }
}
