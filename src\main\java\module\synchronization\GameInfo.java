package module.synchronization;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

/**
 * Created by nara on 2018/6/11.
 */
public class GameInfo {
    private int type;//1抢球 2竞速
    private int hall;
    private int roomId;
    private ScheduledExecutorService service;
    private int totalScore = 0;//游戏总分
    private Map<Integer,Integer> scoreMap = new HashMap<Integer, Integer>();//玩家总分
    private Map<Integer,Integer> comboMap = new HashMap<Integer, Integer>();//玩家最大combo
    private Map<Integer,Integer> nowComboMap = new HashMap<Integer, Integer>();//玩家当前combo
    private Map<Integer,Integer> nowBaseScore = new HashMap<Integer, Integer>();//玩家当前基地分数
    private Map<Integer,MapInfo> map = new HashMap<Integer, MapInfo>();
    private MapInfo mapInfo = null;
    private Map<Integer,FightPlayerInfo> fightPlayers = new HashMap<Integer, FightPlayerInfo>();
    private int winId;

    public int getWinId() {
        return winId;
    }

    public void setWinId(int winId) {
        this.winId = winId;
    }

    public int getTotalScore() {
        return totalScore;
    }

    public void setTotalScore(int totalScore) {
        this.totalScore = totalScore;
    }

    public Map<Integer, Integer> getNowBaseScore() {
        return nowBaseScore;
    }

    public void setNowBaseScore(Map<Integer, Integer> nowBaseScore) {
        this.nowBaseScore = nowBaseScore;
    }

    public Map<Integer, Integer> getNowComboMap() {
        return nowComboMap;
    }

    public void setNowComboMap(Map<Integer, Integer> nowComboMap) {
        this.nowComboMap = nowComboMap;
    }

    public Map<Integer, Integer> getComboMap() {
        return comboMap;
    }

    public void setComboMap(Map<Integer, Integer> comboMap) {
        this.comboMap = comboMap;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public ScheduledExecutorService getService() {
        return service;
    }

    public void setService(ScheduledExecutorService service) {
        this.service = service;
    }

    public Map<Integer, FightPlayerInfo> getFightPlayers() {
        return fightPlayers;
    }

    public void setFightPlayers(Map<Integer, FightPlayerInfo> fightPlayers) {
        this.fightPlayers = fightPlayers;
    }

    public int getRoomId() {
        return roomId;
    }

    public void setRoomId(int roomId) {
        this.roomId = roomId;
    }

    public int getHall() {
        return hall;
    }

    public void setHall(int hall) {
        this.hall = hall;
    }

    public MapInfo getMapInfo() {
        return mapInfo;
    }

    public void setMapInfo(MapInfo mapInfo) {
        this.mapInfo = mapInfo;
    }

    public Map<Integer, Integer> getScoreMap() {
        return scoreMap;
    }

    public void setScoreMap(Map<Integer, Integer> scoreMap) {
        this.scoreMap = scoreMap;
    }

    public Map<Integer, MapInfo> getMap() {
        return map;
    }

    public void setMap(Map<Integer, MapInfo> map) {
        this.map = map;
    }
}