// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ad.proto

package protocol;

public final class AdData {
  private AdData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface ReportAdRewardOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.Item item = 1;
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ReportAdReward}
   *
   * <pre>
   *广告
   * </pre>
   */
  public static final class ReportAdReward extends
      com.google.protobuf.GeneratedMessage
      implements ReportAdRewardOrBuilder {
    // Use ReportAdReward.newBuilder() to construct.
    private ReportAdReward(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ReportAdReward(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ReportAdReward defaultInstance;
    public static ReportAdReward getDefaultInstance() {
      return defaultInstance;
    }

    public ReportAdReward getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ReportAdReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000001;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.AdData.internal_static_protocol_ReportAdReward_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.AdData.internal_static_protocol_ReportAdReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.AdData.ReportAdReward.class, protocol.AdData.ReportAdReward.Builder.class);
    }

    public static com.google.protobuf.Parser<ReportAdReward> PARSER =
        new com.google.protobuf.AbstractParser<ReportAdReward>() {
      public ReportAdReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReportAdReward(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ReportAdReward> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.Item item = 1;
    public static final int ITEM_FIELD_NUMBER = 1;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    private void initFields() {
      item_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(1, item_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, item_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.AdData.ReportAdReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.ReportAdReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.ReportAdReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.ReportAdReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.ReportAdReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.ReportAdReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.AdData.ReportAdReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.AdData.ReportAdReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.AdData.ReportAdReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.ReportAdReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.AdData.ReportAdReward prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ReportAdReward}
     *
     * <pre>
     *广告
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.AdData.ReportAdRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.AdData.internal_static_protocol_ReportAdReward_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.AdData.internal_static_protocol_ReportAdReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.AdData.ReportAdReward.class, protocol.AdData.ReportAdReward.Builder.class);
      }

      // Construct using protocol.AdData.ReportAdReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          itemBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.AdData.internal_static_protocol_ReportAdReward_descriptor;
      }

      public protocol.AdData.ReportAdReward getDefaultInstanceForType() {
        return protocol.AdData.ReportAdReward.getDefaultInstance();
      }

      public protocol.AdData.ReportAdReward build() {
        protocol.AdData.ReportAdReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.AdData.ReportAdReward buildPartial() {
        protocol.AdData.ReportAdReward result = new protocol.AdData.ReportAdReward(this);
        int from_bitField0_ = bitField0_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.AdData.ReportAdReward) {
          return mergeFrom((protocol.AdData.ReportAdReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.AdData.ReportAdReward other) {
        if (other == protocol.AdData.ReportAdReward.getDefaultInstance()) return this;
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000001);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.AdData.ReportAdReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.AdData.ReportAdReward) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.Item item = 1;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ReportAdReward)
    }

    static {
      defaultInstance = new ReportAdReward(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ReportAdReward)
  }

  public interface RequestADItemOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 ad_id = 1;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    boolean hasAdId();
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    int getAdId();
  }
  /**
   * Protobuf type {@code protocol.RequestADItem}
   *
   * <pre>
   * 1341 广告完成领取奖励
   * </pre>
   */
  public static final class RequestADItem extends
      com.google.protobuf.GeneratedMessage
      implements RequestADItemOrBuilder {
    // Use RequestADItem.newBuilder() to construct.
    private RequestADItem(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestADItem(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestADItem defaultInstance;
    public static RequestADItem getDefaultInstance() {
      return defaultInstance;
    }

    public RequestADItem getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestADItem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              adId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.AdData.internal_static_protocol_RequestADItem_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.AdData.internal_static_protocol_RequestADItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.AdData.RequestADItem.class, protocol.AdData.RequestADItem.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestADItem> PARSER =
        new com.google.protobuf.AbstractParser<RequestADItem>() {
      public RequestADItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestADItem(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestADItem> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 ad_id = 1;
    public static final int AD_ID_FIELD_NUMBER = 1;
    private int adId_;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public boolean hasAdId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public int getAdId() {
      return adId_;
    }

    private void initFields() {
      adId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAdId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, adId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, adId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.AdData.RequestADItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.RequestADItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.RequestADItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.RequestADItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.RequestADItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.RequestADItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.AdData.RequestADItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.AdData.RequestADItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.AdData.RequestADItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.RequestADItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.AdData.RequestADItem prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestADItem}
     *
     * <pre>
     * 1341 广告完成领取奖励
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.AdData.RequestADItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.AdData.internal_static_protocol_RequestADItem_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.AdData.internal_static_protocol_RequestADItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.AdData.RequestADItem.class, protocol.AdData.RequestADItem.Builder.class);
      }

      // Construct using protocol.AdData.RequestADItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        adId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.AdData.internal_static_protocol_RequestADItem_descriptor;
      }

      public protocol.AdData.RequestADItem getDefaultInstanceForType() {
        return protocol.AdData.RequestADItem.getDefaultInstance();
      }

      public protocol.AdData.RequestADItem build() {
        protocol.AdData.RequestADItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.AdData.RequestADItem buildPartial() {
        protocol.AdData.RequestADItem result = new protocol.AdData.RequestADItem(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.adId_ = adId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.AdData.RequestADItem) {
          return mergeFrom((protocol.AdData.RequestADItem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.AdData.RequestADItem other) {
        if (other == protocol.AdData.RequestADItem.getDefaultInstance()) return this;
        if (other.hasAdId()) {
          setAdId(other.getAdId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAdId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.AdData.RequestADItem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.AdData.RequestADItem) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 ad_id = 1;
      private int adId_ ;
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public boolean hasAdId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public int getAdId() {
        return adId_;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder setAdId(int value) {
        bitField0_ |= 0x00000001;
        adId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder clearAdId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        adId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestADItem)
    }

    static {
      defaultInstance = new RequestADItem(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestADItem)
  }

  public interface RequestAdPlayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 ad_id = 1;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    boolean hasAdId();
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    int getAdId();
  }
  /**
   * Protobuf type {@code protocol.RequestAdPlay}
   *
   * <pre>
   * 1602 请求广告
   * </pre>
   */
  public static final class RequestAdPlay extends
      com.google.protobuf.GeneratedMessage
      implements RequestAdPlayOrBuilder {
    // Use RequestAdPlay.newBuilder() to construct.
    private RequestAdPlay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestAdPlay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestAdPlay defaultInstance;
    public static RequestAdPlay getDefaultInstance() {
      return defaultInstance;
    }

    public RequestAdPlay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestAdPlay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              adId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.AdData.internal_static_protocol_RequestAdPlay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.AdData.internal_static_protocol_RequestAdPlay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.AdData.RequestAdPlay.class, protocol.AdData.RequestAdPlay.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestAdPlay> PARSER =
        new com.google.protobuf.AbstractParser<RequestAdPlay>() {
      public RequestAdPlay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestAdPlay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestAdPlay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 ad_id = 1;
    public static final int AD_ID_FIELD_NUMBER = 1;
    private int adId_;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public boolean hasAdId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public int getAdId() {
      return adId_;
    }

    private void initFields() {
      adId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAdId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, adId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, adId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.AdData.RequestAdPlay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.AdData.RequestAdPlay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.AdData.RequestAdPlay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.RequestAdPlay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.AdData.RequestAdPlay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestAdPlay}
     *
     * <pre>
     * 1602 请求广告
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.AdData.RequestAdPlayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.AdData.internal_static_protocol_RequestAdPlay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.AdData.internal_static_protocol_RequestAdPlay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.AdData.RequestAdPlay.class, protocol.AdData.RequestAdPlay.Builder.class);
      }

      // Construct using protocol.AdData.RequestAdPlay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        adId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.AdData.internal_static_protocol_RequestAdPlay_descriptor;
      }

      public protocol.AdData.RequestAdPlay getDefaultInstanceForType() {
        return protocol.AdData.RequestAdPlay.getDefaultInstance();
      }

      public protocol.AdData.RequestAdPlay build() {
        protocol.AdData.RequestAdPlay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.AdData.RequestAdPlay buildPartial() {
        protocol.AdData.RequestAdPlay result = new protocol.AdData.RequestAdPlay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.adId_ = adId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.AdData.RequestAdPlay) {
          return mergeFrom((protocol.AdData.RequestAdPlay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.AdData.RequestAdPlay other) {
        if (other == protocol.AdData.RequestAdPlay.getDefaultInstance()) return this;
        if (other.hasAdId()) {
          setAdId(other.getAdId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAdId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.AdData.RequestAdPlay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.AdData.RequestAdPlay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 ad_id = 1;
      private int adId_ ;
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public boolean hasAdId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public int getAdId() {
        return adId_;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder setAdId(int value) {
        bitField0_ |= 0x00000001;
        adId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder clearAdId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        adId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestAdPlay)
    }

    static {
      defaultInstance = new RequestAdPlay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestAdPlay)
  }

  public interface ResponseAdPlayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // required int32 ad_id = 2;
    /**
     * <code>required int32 ad_id = 2;</code>
     */
    boolean hasAdId();
    /**
     * <code>required int32 ad_id = 2;</code>
     */
    int getAdId();
  }
  /**
   * Protobuf type {@code protocol.ResponseAdPlay}
   *
   * <pre>
   * 2602 是否领取成功
   * </pre>
   */
  public static final class ResponseAdPlay extends
      com.google.protobuf.GeneratedMessage
      implements ResponseAdPlayOrBuilder {
    // Use ResponseAdPlay.newBuilder() to construct.
    private ResponseAdPlay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseAdPlay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseAdPlay defaultInstance;
    public static ResponseAdPlay getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseAdPlay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseAdPlay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              adId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.AdData.internal_static_protocol_ResponseAdPlay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.AdData.internal_static_protocol_ResponseAdPlay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.AdData.ResponseAdPlay.class, protocol.AdData.ResponseAdPlay.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseAdPlay> PARSER =
        new com.google.protobuf.AbstractParser<ResponseAdPlay>() {
      public ResponseAdPlay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseAdPlay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseAdPlay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 ad_id = 2;
    public static final int AD_ID_FIELD_NUMBER = 2;
    private int adId_;
    /**
     * <code>required int32 ad_id = 2;</code>
     */
    public boolean hasAdId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 ad_id = 2;</code>
     */
    public int getAdId() {
      return adId_;
    }

    private void initFields() {
      errorId_ = 0;
      adId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAdId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, adId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, adId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.AdData.ResponseAdPlay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdPlay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.AdData.ResponseAdPlay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.ResponseAdPlay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.AdData.ResponseAdPlay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseAdPlay}
     *
     * <pre>
     * 2602 是否领取成功
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.AdData.ResponseAdPlayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.AdData.internal_static_protocol_ResponseAdPlay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.AdData.internal_static_protocol_ResponseAdPlay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.AdData.ResponseAdPlay.class, protocol.AdData.ResponseAdPlay.Builder.class);
      }

      // Construct using protocol.AdData.ResponseAdPlay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        adId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.AdData.internal_static_protocol_ResponseAdPlay_descriptor;
      }

      public protocol.AdData.ResponseAdPlay getDefaultInstanceForType() {
        return protocol.AdData.ResponseAdPlay.getDefaultInstance();
      }

      public protocol.AdData.ResponseAdPlay build() {
        protocol.AdData.ResponseAdPlay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.AdData.ResponseAdPlay buildPartial() {
        protocol.AdData.ResponseAdPlay result = new protocol.AdData.ResponseAdPlay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.adId_ = adId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.AdData.ResponseAdPlay) {
          return mergeFrom((protocol.AdData.ResponseAdPlay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.AdData.ResponseAdPlay other) {
        if (other == protocol.AdData.ResponseAdPlay.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasAdId()) {
          setAdId(other.getAdId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasAdId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.AdData.ResponseAdPlay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.AdData.ResponseAdPlay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 ad_id = 2;
      private int adId_ ;
      /**
       * <code>required int32 ad_id = 2;</code>
       */
      public boolean hasAdId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 ad_id = 2;</code>
       */
      public int getAdId() {
        return adId_;
      }
      /**
       * <code>required int32 ad_id = 2;</code>
       */
      public Builder setAdId(int value) {
        bitField0_ |= 0x00000002;
        adId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 ad_id = 2;</code>
       */
      public Builder clearAdId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        adId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseAdPlay)
    }

    static {
      defaultInstance = new ResponseAdPlay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseAdPlay)
  }

  public interface RequestAdNumOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 ad_id = 1;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    boolean hasAdId();
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    int getAdId();
  }
  /**
   * Protobuf type {@code protocol.RequestAdNum}
   *
   * <pre>
   * 1603 广告次数
   * </pre>
   */
  public static final class RequestAdNum extends
      com.google.protobuf.GeneratedMessage
      implements RequestAdNumOrBuilder {
    // Use RequestAdNum.newBuilder() to construct.
    private RequestAdNum(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestAdNum(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestAdNum defaultInstance;
    public static RequestAdNum getDefaultInstance() {
      return defaultInstance;
    }

    public RequestAdNum getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestAdNum(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              adId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.AdData.internal_static_protocol_RequestAdNum_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.AdData.internal_static_protocol_RequestAdNum_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.AdData.RequestAdNum.class, protocol.AdData.RequestAdNum.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestAdNum> PARSER =
        new com.google.protobuf.AbstractParser<RequestAdNum>() {
      public RequestAdNum parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestAdNum(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestAdNum> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 ad_id = 1;
    public static final int AD_ID_FIELD_NUMBER = 1;
    private int adId_;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public boolean hasAdId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public int getAdId() {
      return adId_;
    }

    private void initFields() {
      adId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAdId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, adId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, adId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.AdData.RequestAdNum parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.RequestAdNum parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.RequestAdNum parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.RequestAdNum parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.RequestAdNum parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.RequestAdNum parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.AdData.RequestAdNum parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.AdData.RequestAdNum parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.AdData.RequestAdNum parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.RequestAdNum parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.AdData.RequestAdNum prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestAdNum}
     *
     * <pre>
     * 1603 广告次数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.AdData.RequestAdNumOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.AdData.internal_static_protocol_RequestAdNum_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.AdData.internal_static_protocol_RequestAdNum_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.AdData.RequestAdNum.class, protocol.AdData.RequestAdNum.Builder.class);
      }

      // Construct using protocol.AdData.RequestAdNum.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        adId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.AdData.internal_static_protocol_RequestAdNum_descriptor;
      }

      public protocol.AdData.RequestAdNum getDefaultInstanceForType() {
        return protocol.AdData.RequestAdNum.getDefaultInstance();
      }

      public protocol.AdData.RequestAdNum build() {
        protocol.AdData.RequestAdNum result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.AdData.RequestAdNum buildPartial() {
        protocol.AdData.RequestAdNum result = new protocol.AdData.RequestAdNum(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.adId_ = adId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.AdData.RequestAdNum) {
          return mergeFrom((protocol.AdData.RequestAdNum)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.AdData.RequestAdNum other) {
        if (other == protocol.AdData.RequestAdNum.getDefaultInstance()) return this;
        if (other.hasAdId()) {
          setAdId(other.getAdId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAdId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.AdData.RequestAdNum parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.AdData.RequestAdNum) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 ad_id = 1;
      private int adId_ ;
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public boolean hasAdId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public int getAdId() {
        return adId_;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder setAdId(int value) {
        bitField0_ |= 0x00000001;
        adId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder clearAdId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        adId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestAdNum)
    }

    static {
      defaultInstance = new RequestAdNum(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestAdNum)
  }

  public interface ResponseAdNumOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 ad_id = 1;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    boolean hasAdId();
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    int getAdId();

    // required int32 num = 2;
    /**
     * <code>required int32 num = 2;</code>
     */
    boolean hasNum();
    /**
     * <code>required int32 num = 2;</code>
     */
    int getNum();

    // required int32 maxNum = 3;
    /**
     * <code>required int32 maxNum = 3;</code>
     */
    boolean hasMaxNum();
    /**
     * <code>required int32 maxNum = 3;</code>
     */
    int getMaxNum();
  }
  /**
   * Protobuf type {@code protocol.ResponseAdNum}
   *
   * <pre>
   * 2603 
   * </pre>
   */
  public static final class ResponseAdNum extends
      com.google.protobuf.GeneratedMessage
      implements ResponseAdNumOrBuilder {
    // Use ResponseAdNum.newBuilder() to construct.
    private ResponseAdNum(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseAdNum(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseAdNum defaultInstance;
    public static ResponseAdNum getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseAdNum getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseAdNum(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              adId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              maxNum_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.AdData.internal_static_protocol_ResponseAdNum_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.AdData.internal_static_protocol_ResponseAdNum_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.AdData.ResponseAdNum.class, protocol.AdData.ResponseAdNum.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseAdNum> PARSER =
        new com.google.protobuf.AbstractParser<ResponseAdNum>() {
      public ResponseAdNum parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseAdNum(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseAdNum> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 ad_id = 1;
    public static final int AD_ID_FIELD_NUMBER = 1;
    private int adId_;
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public boolean hasAdId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 ad_id = 1;</code>
     */
    public int getAdId() {
      return adId_;
    }

    // required int32 num = 2;
    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>required int32 num = 2;</code>
     */
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 num = 2;</code>
     */
    public int getNum() {
      return num_;
    }

    // required int32 maxNum = 3;
    public static final int MAXNUM_FIELD_NUMBER = 3;
    private int maxNum_;
    /**
     * <code>required int32 maxNum = 3;</code>
     */
    public boolean hasMaxNum() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 maxNum = 3;</code>
     */
    public int getMaxNum() {
      return maxNum_;
    }

    private void initFields() {
      adId_ = 0;
      num_ = 0;
      maxNum_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasAdId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasNum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMaxNum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, adId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, num_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, maxNum_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, adId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, maxNum_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.AdData.ResponseAdNum parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdNum parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.AdData.ResponseAdNum parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.AdData.ResponseAdNum parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.AdData.ResponseAdNum prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseAdNum}
     *
     * <pre>
     * 2603 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.AdData.ResponseAdNumOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.AdData.internal_static_protocol_ResponseAdNum_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.AdData.internal_static_protocol_ResponseAdNum_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.AdData.ResponseAdNum.class, protocol.AdData.ResponseAdNum.Builder.class);
      }

      // Construct using protocol.AdData.ResponseAdNum.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        adId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        maxNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.AdData.internal_static_protocol_ResponseAdNum_descriptor;
      }

      public protocol.AdData.ResponseAdNum getDefaultInstanceForType() {
        return protocol.AdData.ResponseAdNum.getDefaultInstance();
      }

      public protocol.AdData.ResponseAdNum build() {
        protocol.AdData.ResponseAdNum result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.AdData.ResponseAdNum buildPartial() {
        protocol.AdData.ResponseAdNum result = new protocol.AdData.ResponseAdNum(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.adId_ = adId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.num_ = num_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.maxNum_ = maxNum_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.AdData.ResponseAdNum) {
          return mergeFrom((protocol.AdData.ResponseAdNum)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.AdData.ResponseAdNum other) {
        if (other == protocol.AdData.ResponseAdNum.getDefaultInstance()) return this;
        if (other.hasAdId()) {
          setAdId(other.getAdId());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasMaxNum()) {
          setMaxNum(other.getMaxNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasAdId()) {
          
          return false;
        }
        if (!hasNum()) {
          
          return false;
        }
        if (!hasMaxNum()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.AdData.ResponseAdNum parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.AdData.ResponseAdNum) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 ad_id = 1;
      private int adId_ ;
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public boolean hasAdId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public int getAdId() {
        return adId_;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder setAdId(int value) {
        bitField0_ |= 0x00000001;
        adId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 ad_id = 1;</code>
       */
      public Builder clearAdId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        adId_ = 0;
        onChanged();
        return this;
      }

      // required int32 num = 2;
      private int num_ ;
      /**
       * <code>required int32 num = 2;</code>
       */
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 num = 2;</code>
       */
      public int getNum() {
        return num_;
      }
      /**
       * <code>required int32 num = 2;</code>
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 num = 2;</code>
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }

      // required int32 maxNum = 3;
      private int maxNum_ ;
      /**
       * <code>required int32 maxNum = 3;</code>
       */
      public boolean hasMaxNum() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 maxNum = 3;</code>
       */
      public int getMaxNum() {
        return maxNum_;
      }
      /**
       * <code>required int32 maxNum = 3;</code>
       */
      public Builder setMaxNum(int value) {
        bitField0_ |= 0x00000004;
        maxNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 maxNum = 3;</code>
       */
      public Builder clearMaxNum() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxNum_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseAdNum)
    }

    static {
      defaultInstance = new ResponseAdNum(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseAdNum)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ReportAdReward_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ReportAdReward_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestADItem_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestADItem_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestAdPlay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestAdPlay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseAdPlay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseAdPlay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestAdNum_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestAdNum_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseAdNum_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseAdNum_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\010ad.proto\022\010protocol\032\nitem.proto\".\n\016Repo" +
      "rtAdReward\022\034\n\004item\030\001 \003(\0132\016.protocol.Item" +
      "\"\036\n\rRequestADItem\022\r\n\005ad_id\030\001 \002(\005\"\036\n\rRequ" +
      "estAdPlay\022\r\n\005ad_id\030\001 \002(\005\"0\n\016ResponseAdPl" +
      "ay\022\017\n\007errorId\030\001 \002(\005\022\r\n\005ad_id\030\002 \002(\005\"\035\n\014Re" +
      "questAdNum\022\r\n\005ad_id\030\001 \002(\005\";\n\rResponseAdN" +
      "um\022\r\n\005ad_id\030\001 \002(\005\022\013\n\003num\030\002 \002(\005\022\016\n\006maxNum" +
      "\030\003 \002(\005B\010B\006AdData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_ReportAdReward_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_ReportAdReward_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ReportAdReward_descriptor,
              new java.lang.String[] { "Item", });
          internal_static_protocol_RequestADItem_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestADItem_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestADItem_descriptor,
              new java.lang.String[] { "AdId", });
          internal_static_protocol_RequestAdPlay_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestAdPlay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestAdPlay_descriptor,
              new java.lang.String[] { "AdId", });
          internal_static_protocol_ResponseAdPlay_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseAdPlay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseAdPlay_descriptor,
              new java.lang.String[] { "ErrorId", "AdId", });
          internal_static_protocol_RequestAdNum_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_RequestAdNum_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestAdNum_descriptor,
              new java.lang.String[] { "AdId", });
          internal_static_protocol_ResponseAdNum_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_ResponseAdNum_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseAdNum_descriptor,
              new java.lang.String[] { "AdId", "Num", "MaxNum", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
