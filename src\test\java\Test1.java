import entities.FriendApplicationEntity;
import entities.RoleEntity;
import manager.MySql;

import java.util.List;

public class Test1 {
    public static void main(String[] args) throws InterruptedException {
        /*StringBuilder stringBuilder=new StringBuilder("from RoleEntity ORDER BY RAND()");
        stringBuilder=new StringBuilder("from FriendApplicationEntity");
        List<Object> rolelist=MySql.queryForList(stringBuilder.toString(),10);
        for (int i=0;i<rolelist.size();i++){
            FriendApplicationEntity roleEntity= (FriendApplicationEntity) rolelist.get(i);
            System.out.println(roleEntity.toString());
        }*/
        int i = 9;
        System.out.println(i&(-i));
    }
}
