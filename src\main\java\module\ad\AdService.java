package module.ad;

import common.SuperConfig;
import entities.AdLogEntity;
import entities.ClickNumEntity;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import module.item.ItemDao;
import module.login.LoginService;
import protocol.AdData;
import protocol.ItemData;
import protocol.ProtoData;
import protocol.AdData.RequestAdPlay;
import protocol.ProtoData.ErrorCode;
import server.SuperProtocol;
import server.SuperServerHandler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.MyUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2019-06-03 19:23
 */
public class AdService {
    private static Logger log = LoggerFactory.getLogger(LoginService.class);

    private static AdService inst = null;

    public static AdService getInstance() {
        if (inst == null) {
            inst = new AdService();
        }
        return inst;
    }


    //通过user_id获取ClickNumEntity
    public ClickNumEntity getClickNumEntity(String user_id) {
        IAd iAd = IAdDao.getInstance();
        ClickNumEntity clickNumEntity = iAd.getClickNumEntity(user_id);
        return clickNumEntity;
    }

    public List<Object> getClickNumList(String event_id) {
        IAd iAd = IAdDao.getInstance();
        List<Object> list = iAd.getClickNumList(event_id);
        return list;
    }

    //第一次点击广告
    public void firstClick(String item_type, String user_id,  Integer ad_id) {

        IAd iAd = IAdDao.getInstance();
        ItemDao itemDao = ItemDao.getInstance();
        Redis jedis = Redis.getInstance();
        int itemId = getItemId(item_type);
        String type = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag");//0
        String key = "roleitem:" + user_id + "#" + type;
        int itemNum = getItemNum(item_type);
        //保存clicknum
        ClickNumEntity clickNumEntity = new ClickNumEntity();
        clickNumEntity.setClick_num(1);
        clickNumEntity.setClick_time(new Date());
        clickNumEntity.setItem_type(item_type);
        clickNumEntity.setUser_id(user_id);
        iAd.saveClickNum(clickNumEntity);
        /// System.out.println(user_id+"##############################################");
        Map<String, String> map = new HashMap<String, String>();
        map.put(user_id, MyUtils.objectToJson(clickNumEntity));
        jedis.hmset("clicknum:" + user_id + "adid:"+ ad_id, map);

        //插入adlog
        //int type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag"));
        AdLogEntity adLogEntity = new AdLogEntity();
        adLogEntity.setEvent_id("");
        adLogEntity.setOperate_time(new Date());
        adLogEntity.setItem_type(item_type);
        adLogEntity.setUser_id(user_id);
        //获得itemnum
        //String itemNumStr = jedis.hget("roleitem:" + user_id + "#" + type, itemId + "");
        //看他原本有不有砖石
        String jewelNum = jedis.hget(key, itemId + "");
        if (jewelNum == null || jewelNum == "") {
            adLogEntity.setPre_num(0.0);
            adLogEntity.setAfter_num(0.0 + itemNum);
        } else {
            adLogEntity.setPre_num(Double.parseDouble(jewelNum));
            adLogEntity.setAfter_num(Double.parseDouble(jewelNum) + itemNum);
        }

        iAd.saveAdLog(adLogEntity);

        //保存或更新item表
        double total = itemDao.updateItemInfo(user_id, itemId, itemNum);

        AdData.ReportAdReward.Builder builder = AdData.ReportAdReward.newBuilder();
        ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
        addItemBu.setId(itemId);
        addItemBu.setNum(total);
        builder.addItem(addItemBu);

        byte[] bytes = builder.build().toByteArray();
        SuperProtocol response = new SuperProtocol(ProtoData.SToC.RREPORTADREWARD_VALUE, bytes.length, bytes);
        ChannelHandlerContext uidCtx = SuperServerHandler.getCtxFromUid(user_id);
        if (uidCtx != null) {
            uidCtx.writeAndFlush(response);
        }
    }

    private int getItemId(String item_type) {
        String[] split = item_type.split("[,]");
        return Integer.parseInt(split[0]);
    }

    private int getItemNum(String item_type) {
        String[] split = item_type.split(",");
        return Integer.parseInt(split[1]);
    }

    //同一天的操作
    public void sameDayOperation(String item_type, String user_id,  Integer ad_id, Date date) {
        ItemDao itemDao = ItemDao.getInstance();
        IAd iAd = IAdDao.getInstance();
        Redis jedis = Redis.getInstance();
        int itemId = getItemId(item_type);//2
        int itemNum = getItemNum(item_type);//5
        String type = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag");//0
        String key = "roleitem:" + user_id + "#" + type;
        Map<String, String> stringMap = jedis.hgetAll("clicknum:" + user_id+ "adid:" + ad_id);
        String jsonStr = stringMap.get(user_id);
        ClickNumEntity clickNumEntity = (ClickNumEntity) MyUtils.jsonToBean(jsonStr, ClickNumEntity.class);
        Integer click_num = clickNumEntity.getClick_num();
        int num = click_num + 1;
        //更新clicknum表
        iAd.updateClickNum(num, user_id, date);
        clickNumEntity.setClick_num(num);
        clickNumEntity.setClick_time(date);
        Map<String, String> map = new HashMap<String, String>();
        map.put(user_id, MyUtils.objectToJson(clickNumEntity));
        jedis.hmset("clicknum:" + user_id+ "adid:" + ad_id, map);

        //插入adlog
        String itemNumStr = jedis.hget(key, itemId + "");
        if (itemNumStr == null || itemNumStr == "") {
            itemNumStr = "0";
        }
        double item_num = Double.parseDouble(itemNumStr);
        AdLogEntity adLogEntity = new AdLogEntity();
        adLogEntity.setUser_id(user_id);
        adLogEntity.setItem_type(item_type);
        adLogEntity.setOperate_time(new Date());
        adLogEntity.setEvent_id("");
        adLogEntity.setPre_num(item_num);
        adLogEntity.setAfter_num(item_num + itemNum);
        MySql.insert(adLogEntity);

        //更新item
        double total = itemDao.updateItemInfo(user_id, itemId, itemNum);

        AdData.ReportAdReward.Builder builder = AdData.ReportAdReward.newBuilder();
        ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
        addItemBu.setId(itemId);
        addItemBu.setNum(total);
        builder.addItem(addItemBu);

        byte[] bytes = builder.build().toByteArray();
        SuperProtocol response = new SuperProtocol(ProtoData.SToC.RREPORTADREWARD_VALUE, bytes.length, bytes);
        ChannelHandlerContext uidCtx = SuperServerHandler.getCtxFromUid(user_id);
        if (uidCtx != null)
            uidCtx.writeAndFlush(response);

    }

    //不是同一天
    public void otherDayOperation(String item_type, String user_id,  Integer ad_id, Date date) {
        IAd iAd = new IAdDao();
        ItemDao itemDao = ItemDao.getInstance();
        Redis jedis = Redis.getInstance();
        int itemId = getItemId(item_type);
        int itemNum = getItemNum(item_type);
        String type = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag");
        String key = "roleitem:" + user_id + "#" + type;
        Map<String, String> stringMap = jedis.hgetAll("clicknum:" + user_id + "adid:" + ad_id);
        String jsonStr = stringMap.get(user_id);
        ClickNumEntity clickNumEntity = (ClickNumEntity) MyUtils.jsonToBean(jsonStr, ClickNumEntity.class);
        int num = 1;
        //更新clicknum表
        iAd.updateClickNum(num, user_id, date);
        clickNumEntity.setClick_time(date);
        clickNumEntity.setClick_num(num);
        Map<String, String> map = new HashMap<String, String>();
        map.put(user_id, MyUtils.objectToJson(clickNumEntity));
        jedis.hmset("clicknum:" + user_id + "adid:"+ ad_id, map);

        //插入adlog
        String itemNumStr = jedis.hget(key, itemId + "");
        if (itemNumStr == null || itemNumStr == "") {
            itemNumStr = "0";
        }
        double item_num = Double.parseDouble(itemNumStr);
        AdLogEntity adLogEntity = new AdLogEntity();
        adLogEntity.setUser_id(user_id);
        adLogEntity.setItem_type(item_type);
        adLogEntity.setOperate_time(new Date());
        adLogEntity.setEvent_id("");
        adLogEntity.setPre_num(item_num);
        adLogEntity.setAfter_num(item_num + itemNum);
        MySql.insert(adLogEntity);

        //更新item
        double total = itemDao.updateItemInfo(user_id, itemId, itemNum);

        AdData.ReportAdReward.Builder builder = AdData.ReportAdReward.newBuilder();
        ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
        addItemBu.setId(itemId);
        addItemBu.setNum(total);
        builder.addItem(addItemBu);

        byte[] bytes = builder.build().toByteArray();
        SuperProtocol response = new SuperProtocol(ProtoData.SToC.RREPORTADREWARD_VALUE, bytes.length, bytes);
        ChannelHandlerContext uidCtx = SuperServerHandler.getCtxFromUid(user_id);
        if (uidCtx != null)
            uidCtx.writeAndFlush(response);
    }

    //播放广告的请求
    public byte[] requestAdPlay(byte[] bytes, String user_id) {
        AdData.RequestAdPlay requestAdPlay = null;
        AdData.ResponseAdPlay.Builder builder = AdData.ResponseAdPlay.newBuilder();
        builder.setErrorId(0);

        try
        {
            requestAdPlay  = AdData.RequestAdPlay.parseFrom(bytes);
            builder.setAdId(requestAdPlay.getAdId());
        }
        catch( Exception e )
        {
            builder.setAdId(-1);
            e.printStackTrace();
            log.error(e.getMessage(), e);
            return builder == null ? null : builder.build().toByteArray();
        }

        Integer ad_id = requestAdPlay.getAdId();

        Redis jedis = Redis.getInstance();
        Map<String, String> clickNumMap = jedis.hgetAll("clicknum:" + user_id + "adid:" + ad_id);
        String jsonStr = clickNumMap.get(user_id);
        
        //从redis中拿clickNumMap
        if (clickNumMap == null || clickNumMap.size() == 0) {
            System.err.println("First AD click");
            IAdDao ida = IAdDao.getInstance();
            String item_type = jedis.hget("awardAdconfig:" + ad_id, "item");//从award_ad.xlsx表格获得奖励的item表格里的id
            Map<String, String> tmpClickNumMap = new HashMap<>();
            ClickNumEntity clickNumEntity = new ClickNumEntity();
            clickNumEntity.setId(ad_id);
            clickNumEntity.setClick_num(1);
            clickNumEntity.setClick_time(new Date());
            clickNumEntity.setUser_id(user_id);
            clickNumEntity.setItem_type(item_type);
            ida.saveClickNum(clickNumEntity);
            tmpClickNumMap.put(user_id,MyUtils.objectToJson(clickNumEntity));
            jedis.hmset("clicknum:" + user_id + "adid:" + ad_id,tmpClickNumMap);
            //第一次点击广告
            builder.setErrorId(0);
        } else {
            //判断点击次数是否大于10次
            ClickNumEntity clickNumEntity = (ClickNumEntity) MyUtils.jsonToBean(jsonStr, ClickNumEntity.class);
            Integer click_num = clickNumEntity.getClick_num();
            System.err.println("Click Count:" + click_num);
            //这里获取不到max
//            int max = Integer.parseInt(jedis.hget("awardAdconfig:" + ad_id, "max"));
            int max = 10;
            Date click_time = clickNumEntity.getClick_time();
            //判断是不是同一天
            boolean isSameDate = MyUtils.isSameDay(new Date(), click_time);

            if (isSameDate) {
                //是同一天
                if (click_num < max) {
                    builder.setErrorId(0);
                    clickNumEntity.setClick_num(click_num + 1);
                    clickNumEntity.setClick_time(new Date());

                    IAdDao ida = IAdDao.getInstance();
                    ida.saveClickNum(clickNumEntity);

                    Map<String, String> tmpClickNumMap = new HashMap<>();
                    tmpClickNumMap.put(user_id,MyUtils.objectToJson(clickNumEntity));
                    jedis.hmset("clicknum:" + user_id + "adid:" + ad_id,tmpClickNumMap);
                } else {
                    builder.setErrorId(ErrorCode.ADMAX_VALUE);
                }
            } else {
                builder.setErrorId(0);
                clickNumEntity.setClick_num(0);
                clickNumEntity.setClick_time(new Date());

                IAdDao ida = IAdDao.getInstance();
                ida.saveClickNum(clickNumEntity);

                Map<String, String> tmpClickNumMap = new HashMap<>();
                tmpClickNumMap.put(user_id,MyUtils.objectToJson(clickNumEntity));
                jedis.hmset("clicknum:" + user_id + "adid:" + ad_id,tmpClickNumMap);
            }
        }

        return builder == null ? null : builder.build().toByteArray();

    }

    public void requestADItem(byte[] bytes, String user_id )
    {
        Integer ad_id = 0;
        AdData.RequestADItem requestADItem = null;
        try
        {
            requestADItem = AdData.RequestADItem.parseFrom(bytes);
            ad_id = requestADItem.getAdId();
        }
        catch( Exception e )
        {
            e.printStackTrace();
            log.error(e.getMessage(), e);
            return;
        }

        Redis jedis = Redis.getInstance();

        String item_type = jedis.hget("awardAdconfig:" + ad_id, "item");//从award_ad.xlsx表格获得奖励的item表格里的id

        // 先从redis中查询clicknum中有不有数据
        Map<String, String> clickNumMap = jedis.hgetAll("clicknum:" + user_id + "adid:"+ad_id);
        String jsonStr = clickNumMap.get(user_id);

        // 从redis中拿clickNumMap
        if (clickNumMap == null || clickNumMap.size() == 0) {
            // 第一次点击广告
            firstClick(item_type, user_id, ad_id );
        } else {
            // 判断点击次数是否大于10次
            ClickNumEntity clickNumEntity = (ClickNumEntity) MyUtils.jsonToBean(jsonStr, ClickNumEntity.class);
            Integer click_num = clickNumEntity.getClick_num();
//            int max = Integer.parseInt(jedis.hget("awardAdconfig:" + ad_id, "max"));
            int max = 10;
            // 不是第一次点击广告
            Date click_time = clickNumEntity.getClick_time();
            // 判断是不是同一天
            boolean isSameDate = MyUtils.isSameDay(new Date(), click_time);
            if (isSameDate) {
                // 是同一天
                if (click_num < max) {
                    Date date = new Date();
                    sameDayOperation(item_type, user_id, ad_id, date);
                } else {
                    return;
                }
            } else {
                // 不是同一天
                Date date = new Date();
                otherDayOperation(item_type, user_id, ad_id, date);
            }

        }

        return;    
    }

    //获取广告点击次数的请求处理
    public byte[] requestAdNum(byte[] bytes, String user_id) {
        AdData.RequestAdNum requestAdNum = null;
        AdData.ResponseAdNum.Builder builder = AdData.ResponseAdNum.newBuilder();
        builder.setNum(-1);
        try
        {
            requestAdNum  = AdData.RequestAdNum.parseFrom(bytes);

            // 初始化数据
            builder.setAdId(requestAdNum.getAdId());
            builder.setMaxNum(10);
        }
        catch( Exception e )
        {
            e.printStackTrace();
            log.error(e.getMessage(), e);
            return builder == null ? null : builder.build().toByteArray();
        }


        Integer ad_id = requestAdNum.getAdId();

        Redis jedis = Redis.getInstance();
        Map<String, String> clickNumMap = jedis.hgetAll("clicknum:" + user_id + "adid:" + ad_id);
        String jsonStr = clickNumMap.get(user_id);

        //从redis中拿clickNumMap
        if (clickNumMap == null || clickNumMap.size() == 0) {
            log.error("first Click ad");
            //第一次点击广告
            builder.setNum(0);
        } else {
            //判断点击次数是否大于10次
            ClickNumEntity clickNumEntity = (ClickNumEntity) MyUtils.jsonToBean(jsonStr, ClickNumEntity.class);
            Integer click_num = clickNumEntity.getClick_num();
//            int max = Integer.parseInt(jedis.hget("awardAdconfig:" + ad_id, "max"));

            Date click_time = clickNumEntity.getClick_time();
//            判断是不是同一天
            boolean isSameDate = MyUtils.isSameDay(new Date(), click_time);

            if(isSameDate)
                builder.setNum(click_num);
            else
                builder.setNum(0);

//            if (isSameDate) {
//                //是同一天
//                if (click_num < max) {
//                } else {
//                    builder.setNum(ErrorCode.ADMAX_VALUE);
//                }
//            } else {
//                builder.setErrorId(0);
//            }
        }



        return builder.build().toByteArray();
    }
}
