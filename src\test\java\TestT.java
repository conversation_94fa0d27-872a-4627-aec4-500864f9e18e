import com.google.protobuf.InvalidProtocolBufferException;
import entities.InformationEntity;
import entities.ItemEntity;
import entities.RoleEntity;
import manager.MySql;
import manager.ReportManager;
import org.junit.Test;
import protocol.FriendData;
import protocol.ProtoData;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestT {

    public static void main(String[] args) {
        StringBuilder stringBuilder=new StringBuilder("from ItemEntity where uid='20200209123350ZV30685sYev'");
        List<Object> itemlist=MySql.queryForList(stringBuilder.toString());
        Map<String,Map<Integer,Double>> map=new HashMap<>();
        for (int i=0;i<itemlist.size();i++){
            ItemEntity itemEntity= (ItemEntity) itemlist.get(i);
            System.err.println(itemEntity.toString());
            Map<Integer,Double> maps=new HashMap<>();
            maps.put(itemEntity.getItemid(),itemEntity.getItemnum());
            map.put(itemEntity.getUid(),maps);
        }
        for (String str:map.keySet()){
            Map<Integer,Double> maps=map.get(str);
            System.out.println(str);
            for (Integer key:maps.keySet()){
                System.out.println(key);
                System.out.println(maps.get(key));
            }
            System.out.println("————————————————————————————————————————————————");
        }
    }
    @Test
    public void testss() throws InvalidProtocolBufferException {
        FriendData.RequestSendOutInformation.Builder builder=FriendData.RequestSendOutInformation.newBuilder();
        builder.setContent("123");
        builder.setId(3628);
        tests("20220919100058bFL8043I4vP",builder.build().toByteArray());
    }
    public void tests(String uid,byte[] inBytes) throws InvalidProtocolBufferException {
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        FriendData.RequestSendOutInformation requestSendOutInformation = FriendData.RequestSendOutInformation.parseFrom(inBytes);
        FriendData.ResponseSendOutInformation.Builder builder=FriendData.ResponseSendOutInformation.newBuilder();
        FriendData.Information.Builder information=FriendData.Information.newBuilder();
        //通过id查出role
        StringBuilder stringBuilder=new StringBuilder("from RoleEntity where id=").append(requestSendOutInformation.getId());
        RoleEntity roleEntity= (RoleEntity) MySql.queryForOne(stringBuilder.toString());
        builder.setUid(roleEntity.getId());
        //首先将数据存在消息表内
        InformationEntity informationEntity=new InformationEntity();
        informationEntity.setUid1(uid);
        informationEntity.setUid2(roleEntity.getUid());
        informationEntity.setContent(requestSendOutInformation.getContent());
        informationEntity.setDate(simpleDateFormat.format(date).trim());
        MySql.insert(informationEntity);
        /*//查询出聊天记录并返回
        stringBuilder=new StringBuilder("from InformationEntity where uid1='").append(uid).append("' and uid2='").append(roleEntity.getUid()).append("' or uid1='").append(roleEntity.getUid()).append("' and uid2='").append(uid).append("'");
        List<Object> objectList=MySql.queryForList(stringBuilder.toString());
        for (int i=0;i<objectList.size();i++){
            informationEntity= (InformationEntity) objectList.get(i);
            information.setInformationId(informationEntity.getId());
            information.setContent(informationEntity.getContent());
            information.setDate(informationEntity.getDate());
            builder.addInformation(information);
        }
        for (int i=0;i<builder.getInformationList().size();i++){
            System.out.println("消息内容:="+builder.getInformationList().get(i).getContent());
            System.out.println("发送日期:="+builder.getInformationList().get(i).getDate());
            System.out.println("消息id:="+builder.getInformationList().get(i).getInformationId());
        }*/
    }
}
