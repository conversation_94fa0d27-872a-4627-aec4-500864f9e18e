package module.event;

import entities.EventEntity;
import manager.MySql;

import java.util.List;

public class EventDao {
    private static EventDao inst = null;

    public static EventDao getInstance() {
        if (inst == null) {
            inst = new EventDao();
        }
        return inst;
    }

    public List<Object> getEventInfos(String uid) {
        StringBuilder sql = new StringBuilder("from EventEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(sql.toString());
        return list;
    }

    public EventEntity getEventInfo(String uid, int eventId) {
        StringBuilder sql = new StringBuilder("from EventEntity where uid='").append(uid).append("' and eventId=").append(eventId);
        EventEntity eventEntity = (EventEntity) MySql.queryForOne(sql.toString());
        return eventEntity;
    }

}
