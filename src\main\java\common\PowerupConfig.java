package common;

import model.CommonInfo;

@ExcelConfigObject(key = "powerup")
public class PowerupConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "addgrow")
    private int addgrow;
    @ExcelColumn(name = "item", isCommonInfo = true)
    private CommonInfo costItem;
    @ExcelColumn(name = "gold")
    private int costGold;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getAddgrow() {
        return addgrow;
    }

    public void setAddgrow(int addgrow) {
        this.addgrow = addgrow;
    }

    public CommonInfo getCostItem() {
        return costItem;
    }

    public void setCostItem(CommonInfo costItem) {
        this.costItem = costItem;
    }

    public int getCostGold() {
        return costGold;
    }

    public void setCostGold(int costGold) {
        this.costGold = costGold;
    }

    @Override
    public String toString() {
        return "PowerupConfig{" +
                "id=" + id +
                ", addgrow=" + addgrow +
                ", costItem=" + costItem +
                ", costGold=" + costGold +
                '}';
    }
}
