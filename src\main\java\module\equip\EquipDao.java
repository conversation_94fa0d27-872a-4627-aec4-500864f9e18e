package module.equip;

import entities.EquipEntity;
import manager.MySql;

import java.util.List;

public class EquipDao {
    private static EquipDao inst = null;

    public static EquipDao getInstance() {
        if (inst == null) {
            inst = new EquipDao();
        }
        return inst;
    }

    public List<Object> queryEquip(String uid) {
        StringBuilder stringBuilder = new StringBuilder(" from EquipEntity where friendId='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuilder.toString());
        return list;
    }

    public EquipEntity queryEquip(String uid, int equipEid) {
        StringBuilder stringBuilder = new StringBuilder(" from EquipEntity where friendId='").append(uid).append("' and equipEid=").append(equipEid);
        Object entity = MySql.queryForOne(stringBuilder.toString());
        return (EquipEntity) entity;
    }
}
