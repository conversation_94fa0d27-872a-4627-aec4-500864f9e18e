import entities.RoleEntity;
import manager.MySql;

import java.util.List;

public class WeiYunSuanTest {
    public static void main(String[] args) {
        StringBuilder hql  =new StringBuilder("from RoleEntity");
        List<Object> obj = MySql.queryForList(hql.toString());
        for (int i=0;i<obj.size();i++){
            RoleEntity roleEntity=(RoleEntity)obj.get(i);
//            /// System.out.println(roleEntity.toString());
        }
    }
}
