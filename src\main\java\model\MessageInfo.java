package model;

/**
 * Created by nara on 2018/2/12.
 */
public class MessageInfo {
    private int id;
    private String mid;
    private int type;
    private String content;
    private String timeStamp;
    private int effective;
    private int status;//0未读 1已读 2已领取奖励  -1已删除但未过期

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getEffective() {
        return effective;
    }

    public void setEffective(int effective) {
        this.effective = effective;
    }

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "MessageInfo{" +
                "id=" + id +
                ", mid='" + mid + '\'' +
                ", type=" + type +
                ", content='" + content + '\'' +
                ", timeStamp='" + timeStamp + '\'' +
                ", effective=" + effective +
                ", status=" + status +
                '}';
    }
}
