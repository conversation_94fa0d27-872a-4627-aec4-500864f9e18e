package entities;

import java.util.Date;
import javax.persistence.*;

@Entity
@Table(name = "limited_time_reward_task", schema = "", catalog = "super_star_fruit")
public class LimitedTimeRewardTaskEntity {
    private int id;
    private String uid;
    private Date time;
    private int type;
    private int number;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
    @Basic
    @Column(name = "time")
    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }
    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
    @Basic
    @Column(name = "number")
    public int getNumber() {
        return number;
    }

    public void setNumber(int number) {
        this.number = number;
    }

    @Override
    public String toString() {
        return "LimitedTimeRewardTaskEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", time=" + time +
                ", type=" + type +
                ", number=" + number +
                '}';
    }
}
