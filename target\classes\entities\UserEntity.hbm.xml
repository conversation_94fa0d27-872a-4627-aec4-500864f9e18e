<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.UserEntity" table="user" schema="" catalog="super_star_fruit">
        <id name="id" column="id">
            <generator class="native"/>
        </id>
        <property name="version" column="version"/>
        <property name="serverid" column="serverid"/>
        <property name="userid" column="userid"/>
        <property name="openid" column="openid"/>
        <property name="pwd" column="pwd"/>
        <property name="roleuid" column="roleuid"/>
        <property name="type" column="type"/>
        <property name="phone" column="phone"/>
        <property name="registertime" column="registertime"/>
        <property name="prelogin" column="prelogin"/>
        <property name="lastlogin" column="lastlogin"/>
        <property name="weekflag" column="weekflag"/>
        <property name="visitor" column="visitor"/>
        <property name="name" column="name"/>
        <property name="idcard" column="idcard"/>
        <property name="gametime" column="gametime"/>
        <property name="authentication" column="authentication"/>
        <property name="idfa" column="idfa"/>
        <property name="uuid" column="uuid"/>
    </class>
</hibernate-mapping>