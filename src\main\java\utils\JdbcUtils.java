package utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.sql.Statement;

public class JdbcUtils {
    public static Connection getConnection() throws ClassNotFoundException,SQLException {
             String url="***************************************************************";
             String user="superStar";
             String password="<EMAIL>";
             Class.forName("com.mysql.jdbc.Driver");
          Connection conn = DriverManager.getConnection(url,user,password);
          return  conn;
    }
    public static void close(){

    }
}
