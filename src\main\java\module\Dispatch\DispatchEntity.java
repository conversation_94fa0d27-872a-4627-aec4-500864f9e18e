package module.Dispatch;

import Json.ExperienceJson;

import java.util.List;

public class DispatchEntity {
    private int flushNums;
    private List<ExperienceJson> experienceJsons;

    public int getFlushNums() {
        return flushNums;
    }

    public void setFlushNums(int flushNums) {
        this.flushNums = flushNums;
    }

    public List<ExperienceJson> getExperienceJsons() {
        return experienceJsons;
    }

    public void setExperienceJsons(List<ExperienceJson> experienceJsons) {
        this.experienceJsons = experienceJsons;
    }

    @Override
    public String toString() {
        return "DispatchEntity{" +
                "flushNums=" + flushNums +
                ", experienceJsons=" + experienceJsons +
                '}';
    }
}
