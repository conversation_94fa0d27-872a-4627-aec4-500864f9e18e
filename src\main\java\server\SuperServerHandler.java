package server;

import common.SuperConfig;
import entities.FriendApplicationEntity;
import entities.RelativeshipEntity;
import entities.RoleEntity;
import entities.UserEntity;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.ReferenceCountUtil;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import model.PointDoubleInfo;
import model.StationInfo;
import module.Dispatch.DispatchService;
import module.achievement.AchievementService;
import module.activity.LimitedTime.LimitedTimeRewardService;
import module.activity.RechargeRebate.RechargeRebateService;
import module.ad.AdService;
import module.battle.BattleService;
import module.boss.BossService;
import module.daily_task.DailyTaskService;
import module.equip.EquipService;
import module.equipA.EquipAService;
import module.event.EventService;
import module.friend.FriendService;
import module.item.ItemService;
import module.level.LevelService;
import module.login.ILogin;
import module.login.LoginDao;
import module.login.LoginService;
import module.mail.MailDao;
import module.mail.MailService;
import module.map.MapService;
import module.mission.MissionService;
import module.pay.PayService;
import module.pet.PetService;
import module.petegg.PetEggService;
import module.playerstatus.PlayerOffline;
import module.playerstatus.PlayerOnline;
import module.plot.plotService;
import module.pvp.PVPService;
import module.rank.RankService;
import module.robot.Factory;
import module.role.RoleService;
import module.room.RoomService;
import module.synchronization.RoomManager;
import module.synchronization.SyncManager;
import module.task.TaskService;
import module.temperature_item.TemItemService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by nara on 2017/11/15.
 */
public class SuperServerHandler extends ChannelInboundHandlerAdapter {
    private static Logger log = LoggerFactory.getLogger(SuperServerHandler.class);
    //public static  Map<String,ChannelHandlerContext> rolectx=new ConcurrentHashMap<String,ChannelHandlerContext>();
    public static Map<String, ChannelHandlerContext> rolectx = new HashMap<String, ChannelHandlerContext>();
    public static Map<Integer, Integer> serverMap = new ConcurrentHashMap<Integer, Integer>();

    /**
     * 所有其他服服链接
     * <uid,serveripid>
     */
    public static Map<String, Integer> otherlinkMap = new ConcurrentHashMap<String, Integer>();
    /**
     * 所有当前服务器下（不一定同一区）链接
     */
    public static Map<ChannelHandlerContext, String> linkMap = new ConcurrentHashMap<ChannelHandlerContext, String>();
//    /**
//     * 在线角色玩家
//     */
//    public static Map<String, ChannelHandlerContext> onLineRoleMap = new HashMap<String, ChannelHandlerContext>();
    /**
     * 所有区服在线角色玩家(当前服务器下所有区服的玩家)!!!!!
     */
    public static Map<Integer, Map<String, ChannelHandlerContext>> serverRoleMap = new ConcurrentHashMap<Integer, Map<String, ChannelHandlerContext>>();
    /**
     * 新用户
     */
    public static Map<ChannelHandlerContext, String> newUserMap = new ConcurrentHashMap<ChannelHandlerContext, String>();
    /**
     * 同屏对战玩家
     */
//    public static Map<Integer, List<SyncChapterInfo>> roomMap = new HashMap<Integer, List<SyncChapterInfo>>();

    /**
     * 同站玩家
     */
    public static int stationVal = 1;
    public static Map<Integer, List<StationInfo>> stationMap = new ConcurrentHashMap<Integer, List<StationInfo>>();
    private int counter = 0;

    // 用于获取客户端发送的信息
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg)
            throws Exception {
//        System.err.println("接受协议");

        SuperProtocol body = (SuperProtocol) msg;
        //     /// System.err.println(ctx.channel().remoteAddress().toString());
      /*  if(body.getMsgId()!=0){
            /// System.out.println("~~~~~~~~~~~~~~~~~~~收到請求："+body);
        }*/
        try {

            if ((SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION_PLOTTER) && body.getMsgId() == ProtoData.CToS.REQUESTRECONNECTION_VALUE) {
                ctx.close();
            } else {
                //0为心跳包
                if (body.getMsgId() != 0) {
                    System.out.println("接受消息：" + body.getMsgId());
                    byte[] bytes = judgeFunction(body.getMsgId(), body.getContent(), ctx);
                    if (bytes != null && bytes.length > 0) {
                        // 写数据给客户端
                        int resId = getResponseId(body.getMsgId());
//                        System.out.println("！！！！！！！！！！发送请求："+resId);
//                SuperProtocol response = new SuperProtocol(resId,str.getBytes("ISO-8859-1").length,
//                        str.getBytes("ISO-8859-1"));
                        SuperProtocol response = new SuperProtocol(resId, bytes.length, bytes);
                        ctx.writeAndFlush(response);

                        System.out.println("发送消息：" + resId);
                        // 当服务端完成写操作后，关闭与客户端的连接
                        /* ctx.writeAndFlush(response);*/
                        // .addListener(ChannelFutureListener.CLOSE);
                    }
                } else {
                    byte[] bytes = LoginService.getInstance().getTimeStamp();
                    SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSESTAMP_VALUE, bytes.length, bytes);
                    ctx.writeAndFlush(response);
                }
                counter = 0;

            }
            // 当有写操作时，不需要手动释放msg的引用
            // 当只有读操作时，才需要手动释放msg的引用
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            ReferenceCountUtil.release(body);
        }

    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            // 空闲6s之后触发 (心跳包丢失)
            if (counter >= 6) {
                // 连续丢失3个心跳包 (断开连接)
                ctx.channel().close().sync();
                removeCtxFromMap(ctx);
                //  /// System.out.println("已与Client断开连接");
            } else {
                counter++;
                //  /// System.out.println("丢失了第 " + counter + " 个心跳包");
            }
        }
    }

    private String getRoleUid(ChannelHandlerContext ctx) {
        String uid = null;
        //    /// System.err.println("1111111111111111111");
        // synchronized (linkMap){uid= linkMap.get(ctx);}
        uid = linkMap.get(ctx);
        if (uid != null) {
            //  ChannelHandlerContext tmp = getServerIdCtxFromUid(uid);
    /*        if (tmp == null){
                removeCtxFromMap(ctx);
                ctx.close();
                /// System.err.println("~~~~~~~~~~~~---------------");
                return null;
            }*/
        }
        if (uid == null) {

        }
        //   /// System.err.println("22222222222222");
        return uid;
    }

    private byte[] judgeFunction(int msgId, byte[] inBytes, ChannelHandlerContext ctx) {
        byte[] bytes = null;
        String uid = null;
        try {
            if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_TEST || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MAIN || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_PLOTTER) {
                switch (msgId) {
                    case ProtoData.CToS.REQUESTSERVER_VALUE:
                        bytes = LoginService.getInstance().getServerInfo();
                        break;
                    case ProtoData.CToS.REQUESTLOGIN_VALUE:
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginUserInfo(ctx, inBytes);
                        }
                        break;
                    case ProtoData.CToS.REQUESTWECHATLOGIN_VALUE:
                        /// System.out.println("judgeFunction wechat login");
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginWeChatUserInfo(ctx, inBytes);
                        }
                        break;

                    case ProtoData.CToS.REQUESTAPPLELOGIN_VALUE:
                        /// System.out.println("judgeFunction Apple login");
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginAppleUserInfo(ctx, inBytes);
                        }
                        break;
                    case ProtoData.CToS.REQUESTQQLOGIN_VALUE:
                        /// System.out.println("judgeFunction QQ login");
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginQQUserInfo(ctx, inBytes);
                        }
                        break;
                    case ProtoData.CToS.REQUESTYSDKLOGIN_VALUE:
                        /// System.out.println("judgeFunction YSDK login");
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginYSDKUserInfo(ctx, inBytes);
                        }
                        break;
                    case ProtoData.CToS.REQUESTHUAWEILOGIN_VALUE:
                        /// System.out.println("judgeFunction huawei login");
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginHuaWeiUserInfo(ctx, inBytes);
                        }
                        break;
                    case ProtoData.CToS.REQUESTGOOGLELOGIN_VALUE:
                        /// System.out.println("judgeFunction huawei login");
                        if (linkMap.size() < 2000) {
                            bytes = LoginService.getInstance().getLoginGoogleInfo(ctx, inBytes);
                        }
                        break;
                    case ProtoData.CToS.REQUESTREGISTER_VALUE:
                        bytes = LoginService.getInstance().register(ctx, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCREATE_VALUE:
                        String userId = newUserMap.get(ctx);
                        bytes = LoginService.getInstance().createRole(ctx, userId, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTROLESEX_VALUE:
                        uid = getRoleUid(ctx);
                        String userId1 = newUserMap.get(ctx);
                        bytes = RoleService.getInstance().RequestRoleSex(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETSEX_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoleService.getInstance().RequestGetSex(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTRECONNECTION_VALUE:
                        //  /// System.out.println("==================reconnection=================");
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().reconnection(ctx, uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTSETINFO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().setRoleBaseInfo(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCHOOSEROLE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().chooseRole(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCHANGEDRESS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().changeDress(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTBUYITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().buyItem(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTUSEITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().useItem(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTOPENBAGCELL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().openBagCell(uid);
                        break;
                    case ProtoData.CToS.REQUESTCOMPOSE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().compose(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTBEGINMISSION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().beginMission(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBEGINGOLD_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().beginGold(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBEGINDOWN_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().beginDown(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTLOTTO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().countLotto(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTMISSION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countMission(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBEGINHEADBALL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().beginHeadBallMission(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTHEADBALL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countHeadBallMission(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTGOLD_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countGold(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTDOWN_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countDown(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBEGINENDLESS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().beginEndless(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTENDLESS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countEndless(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTSENDCHAT_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().sendChat(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTFINISHTASK_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = TaskService.getInstance().finishTask(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEVIEW_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().operateView(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEFRIEND_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().operateFriend(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEFRIEND1_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestOperateFriend1(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTPLAYERRECOMMEND_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestPlayerRecommend(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTFRIENDSAPPLY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestFriendsApply(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTSENDOUTINFORMATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestSendOutInformation(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCHATRECORD_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestChatRecord(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTUPDATETYPE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestUpdateType(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEMESSAGE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().operateMessage(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTPETHATCH_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().RequestPetHatch(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTBUYCLOTHING_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().buyClothing(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCHANGECUPBOARD_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().changeCupboard(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCUPBOARDTOBODY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().cupboardToBody(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTSTARBITOMONEY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().starbiToMoney(uid);
                        break;
                    case ProtoData.CToS.REQUESTSTATIONLOCATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().stationLocation(uid, inBytes, ctx);
                        break;
                    case ProtoData.CToS.REQUESTGIVEPRESENT_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().givePresent(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTACCUSATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().accusation(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTRANK_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RankService.getInstance().getRank(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTLEVEL_VALUE:
                        uid = getRoleUid(ctx);
                       bytes =LevelService.getInstance().RequestLevelid(inBytes,uid);
                        break;

                    case  ProtoData.CToS.REQUESTLEVELD_VALUE:
                        uid=getRoleUid(ctx);
                         bytes= LevelService.getInstance().SelectLevelid(uid);
                        break;
                    case  ProtoData.CToS.REQUESTPlont_VALUE:
                        uid =getRoleUid(ctx);
                        bytes= plotService.getInstance().Requestplotid(inBytes,uid);
                         break;
                    case ProtoData.CToS.REQUESTGetEquipA_VALUE:
                        uid =getRoleUid(ctx);
                        bytes=EquipAService.getInstance().RequestEquipA(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTDDDEquip_VALUE:
                        uid =getRoleUid(ctx);
                        bytes=EquipAService.getInstance().RequestEquipAll(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTbossTiem_VALUE:
                        uid=getRoleUid(ctx);
//                        bytes=BossService.getInstance().RequestBossStart1(inBytes,uid);
                        bytes=BossService.getInstance().RequestWorldBossState(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTbossdear_VALUE:
                        uid=getRoleUid(ctx);
//                        bytes=BossService.getInstance().RequestBossAll(inBytes,uid);
                        bytes=BossService.getInstance().RequestWorldBossInjureNumber(inBytes,uid);
                        break;

                    case ProtoData.CToS.REQUESTEquipF_VALUE:
                        uid =getRoleUid(ctx);
                        EquipAService.getInstance().RequestEquipB(inBytes,uid);
                        break;



                    case  ProtoData.CToS.REQUESTChap_VALUE:
                        uid =getRoleUid(ctx);
                        bytes= plotService.getInstance().Selectplotid(uid);
                        break;
//                    case ProtoData.CToS.REQUESTGETRANKING_VALUE:
//                        uid = getRoleUid(ctx);
//                        bytes = RankingService.getInstance().getRank(uid);
//                        break;
                    case ProtoData.CToS.REQUESTRECOMMENDFRIEND_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().recommendFriend(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTUPDATENEWUSER_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().updateNewuser(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTGETTOTALENDLESS_VALUE:
                        uid = getRoleUid(ctx);
                        //   bytes = ItemService.getInstance().getTotalEndless(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTJOINROOM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().joinRoom(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCREATEROOM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().createRoom(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTLEAVEROOM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().leaveRoom(uid);
                        break;
                    case ProtoData.CToS.REQUESTUPDATEROOMINFO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().updateRoomInfo(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTUPDATEROOMROLE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().updateRoomRole(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTJOINFIGHTHALL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().joinFightHall(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTSTARTINROOM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoomService.getInstance().startInRoom(uid);
                        break;
                    case ProtoData.CToM.REQUESTCONNECTFIGHT_VALUE:
                        bytes = SyncManager.receiveConnectFight(ctx, inBytes);
                        break;
                    case ProtoData.MToS.REQUESTOVERMISSION_VALUE:
                        bytes = RoomService.getInstance().overMission(inBytes);
                        break;

                    case ProtoData.CToS.REQUESTCHOOSEPAY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PayService.getInstance().requestChoosePay(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTSUBMITPAYBACK_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PayService.getInstance().requestSubmitPayBack(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGOOGLEPAY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PayService.getInstance().requestGooglePayBack(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCONFIRMHUAWEIPURCHASE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PayService.getInstance().requestConfirmHuaWeiPurchase(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTYSDKBALANCE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PayService.getInstance().requestYSDKBalance(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTADPLAY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = AdService.getInstance().requestAdPlay(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTADNUN_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = AdService.getInstance().requestAdNum(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTADITEM_VALUE:
                        uid = getRoleUid(ctx);
                        AdService.getInstance().requestADItem(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTRECHAREGREBATEREWARD_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RechargeRebateService.getInstance().requestRechargeRebateReward(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTSOURCESMACHINE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().sourceMachine(ctx, uid);
                        break;
                    case ProtoData.CToS.REQURESTMAKEITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().makeItem(inBytes, uid);

                        break;
                    case ProtoData.CToS.REQUESTPRODUCT_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().getProduct(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTVISITOROPERATION_VALUE:
                        bytes = LoginService.getInstance().visitorRegister(ctx, inBytes);
                        break;
//                    case ProtoData.CToS.REQUESTAPPROVAL_VALUE:
//                        uid = getRoleUid(ctx);
//                        bytes = LoginService.getInstance().approval(inBytes, uid);
//                        break;
                    case ProtoData.CToS.REQUESTAUTHENTICATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().authentication(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTQUERYROLEINFORMATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().queryRoleInformation(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTSTARTGAMECOPY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().startGameCopy(inBytes, uid);
                        break; //开始副本模式

                    case ProtoData.CToS.REQUESTCOUNTGAMECOPY_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countGameCopy(inBytes, uid);
                        break; //结算副本模式
                    case ProtoData.CToS.REQUESTBEGINSTACK_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().beginStack(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTSTACK_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MissionService.getInstance().countStack(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETLUCKYITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().getLuckyItem(inBytes, uid);
                        break; //获得幸运物品奖励
                    case ProtoData.CToS.REQUESTFINISHACTIVITIES_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().finishActivities(inBytes, uid);
                        break;
                    case OnlineConfrontationData.CToS.REQUESTGAMEMATCH_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().gameMatch(inBytes, uid);//对战匹配
                        break;
                    case ProtoData.CToS.REQUESTMARKETINFORMATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().getMarketInformation(uid);
                        break;
                    case ProtoData.CToS.REQUESTGOODSOPERATE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().goodsOperate(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTTICKTEEXCHANGE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().tickteExchange(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTWAITITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().waitItem(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTUPWAITITEMLEVEL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().upWaitItemLevel(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETWAITITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().getWaitItem(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTHOUSE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().house(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTHOUSEPART_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().housePart(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBUYFURNITURE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().buyFurniture(inBytes, uid);
                        break;
                    //工具自定义邮件消息
                    case 6888:
                        bytes = FriendService.getInstance().publishMail(inBytes);
                        break;
                    case 6890:
                        bytes = LoginService.getInstance().setPlayerType(inBytes, ctx);
                        break;
                    case 6892:
                        bytes = LoginService.getInstance().getGameData(inBytes);
                        break;
                    case 6894:
                        bytes = LoginService.getInstance().getOnlinePlayers(inBytes);
                        break;
                    case 6896:
                        bytes = FriendService.getInstance().publishNotice(inBytes);
                        break;

                    //邮件全新版本REQUESTOPERATEMAIL
                    case ProtoData.CToS.REQUESTOPERATEMAIL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MailDao.getInstance().operateMail(inBytes, uid);
                    break;

                    case ProtoData.CToS.REQUESTGETPETSINFO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetsInfo(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETEQUIP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = EquipService.getInstance().getEquip(inBytes,uid);
                                break;
                    case ProtoData.CToS.REQUESTGETMAP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MapService.getInstance().countMap(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTUPDMAP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = MapService.getInstance().UpdMap(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEPET_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().operatePet(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCHOICEEQUIP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes =EquipService.getInstance().choiceEquip(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEEQUIP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = EquipService.getInstance().operateEquip(inBytes, uid);
                        break;
//                    case ProtoData.CToS.REQUESTCHANGEEQUIP_VALUE:
//                        uid = getRoleUid(ctx);
//                        bytes = EquipService.getInstance().changeEquip(inBytes, uid);
//                        break;
                    case ProtoData.CToS.REQUESTBINDIDCARD_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = LoginService.getInstance().bindIDCard(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTRAFFLEPOOL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().getRafflePool(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETEVENTINFO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = EventService.getInstance().getEventInfo(inBytes, uid);
                        break;

                    case ProtoData.CToS.REQUESTFLUSHEVENT_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = EventService.getInstance().flushEvent(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCOUNTBATTLE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = BattleService.getInstance().countBattle(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPLAYERITEMS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().getPlayerItems(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTUPPETSKILL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().upPetSkill(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETEXP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetExp(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEROLE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoleService.getInstance().OperateRoleExp(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBUYBALCKMARKETITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().buyBlackMarketItem(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTBALCKMARKET_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().blackMarket(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETPETFORMATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetFormation(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETFORMATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().petFormation(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETCOMPOSE_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().petCompose(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETPETBREEDINFO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetBreedInfo(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETBREEDINOFRMATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetBreedInofrmation(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETBREEDOPERATION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetBreedOperation(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETBREED_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().RequestPetBreed(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEPETBREED_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().operatePetBreed(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETPETGROWINFO_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().getPetGrowInfo(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETEVOLUTION_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().PetEvolution(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETBREAKTHROUGH_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().petBreakThrough(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGETEXPERIENCES_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = DispatchService.getInstance().getExperiences(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTFLUSHEXPERIENCES_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = DispatchService.getInstance().flushExperiences(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTOPERATEEXPERIENCES_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = DispatchService.getInstance().operateExperience(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTDAILYTANSKS_VALUE:
                        uid = getRoleUid(ctx);
//                        bytes = DispatchService.getInstance().requestdailytansks(inBytes, uid);
                        bytes = DailyTaskService.getInstance().NumberIncreaseDailyTask(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTCOMPLETEDAILYTASKS_VALUE:
                        uid = getRoleUid(ctx);
//                        bytes = DispatchService.getInstance().requestcompletedailytasks(inBytes, uid);
                        bytes = DailyTaskService.getInstance().FinishDailyTask(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTTASK_VALUE:
                        uid = getRoleUid(ctx);
                        // bytes = DispatchService.getInstance().requestTask(inBytes, uid);
                        bytes = DailyTaskService.getInstance().GetAllDailyTask(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTACHIEVEMENTTYPE_VALUE:
                        uid = getRoleUid(ctx);
//                        bytes = DispatchService.getInstance().RequestAchievementType(inBytes, uid);
                        bytes = AchievementService.getInstance().NumberIncreaseAchievement(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTACHIEVEMENTIS_VALUE:
                        uid = getRoleUid(ctx);
//                        bytes = DispatchService.getInstance().RequestAchievementIs(inBytes, uid);
                        bytes = AchievementService.getInstance().FinishAchievement(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTACHIEVEMENT_VALUE:
                        uid = getRoleUid(ctx);
//                        bytes = DispatchService.getInstance().RequestAchievement(inBytes, uid);
                        bytes = AchievementService.getInstance().GetAllAchievement(uid, inBytes);
                        break;
                    case ProtoData.CToS.REQUESTADDITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().RequestAddItem(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTSYNTHESIS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().RequestSynthesis(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTREMOVEPET_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetService.getInstance().RequestRemovePet(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTOPERATIONFRIENDS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = FriendService.getInstance().RequestOperationFriends(inBytes, uid);
                        break;

                    case ProtoData.CToS.REQUESTPHYSICAL_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = ItemService.getInstance().RequestPhysical(uid,inBytes);
                        break;
                    case ProtoData.CToS.REQUESTGETROLEEXP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = RoleService.getInstance().getRoleExp(inBytes,uid);
                        break;
                    case ProtoData.CToS.REQUESTITEMCOMPOSE_VALUE:
                        uid = getRoleUid(ctx);
                        //   bytes =宠物合成的方法
                        bytes = ItemService.getInstance().itemCompose(inBytes, uid);
                        break;

                    case ProtoData.CToS.REQUESTPVPPetOperate_VALUE:
                        uid = getRoleUid(ctx);
                        //   PVP宠物存储
                        bytes = PVPService.getInstance().RequestPVPPetOperate(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPVPBaseData_VALUE:
                        uid = getRoleUid(ctx);
                        //   PVP个人信息
                        bytes = PVPService.getInstance().RequestPVPBaseData(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPVPBattle_VALUE:
                        uid = getRoleUid(ctx);
                        //   PVP战斗
                        bytes = PVPService.getInstance().RequestPVPBattle(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPVPBattleResult_VALUE:
                        uid = getRoleUid(ctx);
                        //   PVP战斗 输赢
                        PVPService.getInstance().RequestPVPBattleResult(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPVPBATTLEREMAINTIME_VALUE:
                        uid = getRoleUid(ctx);
                        //   PVP战斗 输赢
                        bytes = PVPService.getInstance().RequestPVPBattleRemainTime(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTNewMailTest_VALUE:
                        uid = getRoleUid(ctx);
                        //   获取一个新邮件
                        MailService.getInstance().RequestNewMailTest(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTCREATENEWMAIL_VALUE:
                        uid = getRoleUid(ctx);
                        //   获取一个新邮件
                        MailService.getInstance().RequestCreateNewMail(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTLIMITEDTIMEREWARD_VALUE:
                        uid = getRoleUid(ctx);
                        // 限时返利修改信息
                        bytes = LimitedTimeRewardService.getInstance().RequestLimitedTimeReward(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTPETEGGEXP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetEggService.getInstance().RequestPetEggExp(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTADDPETEGGEXP_VALUE:
                        uid = getRoleUid(ctx);
                        bytes = PetEggService.getInstance().RequestAddPetEggExp(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTLIMITEDTIMEREWARDTYPE_VALUE:
                        uid = getRoleUid(ctx);
                        LimitedTimeRewardService.getInstance().RequestLimitedTimeRewardType(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTLIMITEDTIMEREWARDFINISH_VALUE:
                        uid = getRoleUid(ctx);
                        LimitedTimeRewardService.getInstance().RequestLimitedTimeRewardFinish(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTREMAINTIME_VALUE:
                        uid = getRoleUid(ctx);
                        bytes =  LimitedTimeRewardService.getInstance().RequestLimitedTimeRewardGetRemainTime(inBytes, uid);
                        break;
                    case ProtoData.CToS.RESPONSEPETEGGFASTHATCH_VALUE:
                        uid = getRoleUid(ctx);
                        PetEggService.getInstance().PetEggFastHatch(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTTEMITEM_VALUE:
                        uid = getRoleUid(ctx);
                        bytes =  TemItemService.getInstance().RequestTemItem(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTDELETEALLPETS_VALUE:
                        uid = getRoleUid(ctx);
                        bytes =  PetService.getInstance().DeleteAllPets(inBytes, uid);
                        break;
                    case ProtoData.CToS.REQUESTGUIDEPETEXTRACTION_VALUE:
                        uid = getRoleUid(ctx);
                        PetService.getInstance().RequestGuidePet(inBytes, uid);
                        break;
                }
            } else if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION_PLOTTER) {
                switch (msgId) {
                    case ProtoData.SToM.REQUESTENTERMISSION_VALUE:
                        bytes = SyncManager.receiveEnterMission(ctx, inBytes);
                        break;
                    case ProtoData.CToM.REQUESTCONNECTFIGHT_VALUE:
                        bytes = SyncManager.receiveConnectFight(ctx, inBytes);
                        break;
                    case ProtoData.CToM.REQUESTLOADFINISH_VALUE:
                        bytes = SyncManager.receiveLoadFinish(ctx, inBytes);
                        break;
                    case ProtoData.CToM.REQUESTSYNC_VALUE:
                        bytes = SyncManager.receiveSyncMission(ctx, inBytes);
                        break;
                    case ProtoData.CToM.REQUESTHEART_VALUE:
                        bytes = SyncManager.receiveHeart(inBytes);
                        break;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
//            return "error".getBytes();
        } finally {

        }
        return bytes;
    }

    private int getResponseId(int msgId) {
        int resId = 0;
        switch (msgId) {
            case ProtoData.CToS.REQUESTSERVER_VALUE:
                resId = ProtoData.SToC.RESPONSESERVER_VALUE;
                break;
            case ProtoData.CToS.REQUESTLOGIN_VALUE:
                resId = ProtoData.SToC.RESPONSELOGIN_VALUE;
                break;
            case ProtoData.CToS.REQUESTREGISTER_VALUE:
                resId = ProtoData.SToC.RESPONSEREGISTER_VALUE;
                break;
            case ProtoData.CToS.REQUESTCREATE_VALUE:
                resId = ProtoData.SToC.RESPONSECREATE_VALUE;
                break;
            case ProtoData.CToS.REQUESTROLESEX_VALUE:
                resId = ProtoData.SToC.RESPONSEROLESEX_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETSEX_VALUE:
                resId = ProtoData.SToC.RESPONSEGETSEX_VALUE;
                break;
            case ProtoData.CToS.REQUESTRECONNECTION_VALUE:
                resId = ProtoData.SToC.RESPONSERECONNECTION_VALUE;
                break;
            case ProtoData.CToS.REQUESTSETINFO_VALUE:
                resId = ProtoData.SToC.RESPONSESETINFO_VALUE;
                break;
            case ProtoData.CToS.REQUESTCHOOSEROLE_VALUE:
                resId = ProtoData.SToC.RESPONSECHOOSEROLE_VALUE;
                break;
            case ProtoData.CToS.REQUESTCHANGEDRESS_VALUE:
                resId = ProtoData.SToC.RESPONSECHANGEDRESS_VALUE;
                break;
            case ProtoData.CToS.REQUESTBUYITEM_VALUE:
                resId = ProtoData.SToC.RESPONSEBUYITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTUSEITEM_VALUE:
                resId = ProtoData.SToC.RESPONSEUSEITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPENBAGCELL_VALUE:
                resId = ProtoData.SToC.RESPONSEOPENBAGCELL_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOMPOSE_VALUE:
                resId = ProtoData.SToC.RESPONSECOMPOSE_VALUE;
                break;
            case ProtoData.CToS.REQUESTBEGINMISSION_VALUE:
                resId = ProtoData.SToC.RESPONSEBEGINMISSION_VALUE;
                break;
            case ProtoData.CToS.REQUESTBEGINGOLD_VALUE:
                resId = ProtoData.SToC.RESPONSEBEGINGOLD_VALUE;
                break;
            case ProtoData.CToS.REQUESTBEGINDOWN_VALUE:
                resId = ProtoData.SToC.RESPONSEBEGINDOWN_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTLOTTO_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTLOTTO_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTMISSION_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTMISSION_VALUE;
                break;
            case ProtoData.CToS.REQUESTBEGINENDLESS_VALUE:
                resId = ProtoData.SToC.RESPONSEBEGINENDLESS_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTENDLESS_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTENDLESS_VALUE;
                break;
            case ProtoData.CToS.REQUESTSENDCHAT_VALUE:
                resId = ProtoData.SToC.RESPONSESENDCHAT_VALUE;
                break;
            case ProtoData.CToS.REQUESTFINISHTASK_VALUE:
                resId = ProtoData.SToC.RESPONSEFINISHTASK_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEFRIEND_VALUE:
                resId = ProtoData.SToC.RESPONSEOPERATEFRIEND_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEFRIEND1_VALUE:
                resId = ProtoData.SToC.RESPONSEOPERATEFRIEND1_VALUE;
                break;
            case ProtoData.CToS.REQUESTPLAYERRECOMMEND_VALUE:
                resId = ProtoData.SToC.RESPONSEPLAYERRECOMMEND_VALUE;
                break;
            case ProtoData.CToS.REQUESTFRIENDSAPPLY_VALUE:
                resId = ProtoData.SToC.RESPONSEFRIENDSAPPLY_VALUE;
                break;
            case ProtoData.CToS.REQUESTSENDOUTINFORMATION_VALUE:
                resId = ProtoData.SToC.RESPONSESENDOUTINFORMATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTCHATRECORD_VALUE:
                resId = ProtoData.SToC.RESPONSECHATRECORD_VALUE;
                break;
            case ProtoData.CToS.REQUESTUPDATETYPE_VALUE:
                resId = ProtoData.SToC.RESPONSEUPDATETYPE_VALUE;
                break;
            case ProtoData.CToS.REQUESTRANK_VALUE:
                resId = ProtoData.SToC.RESPONSERANK_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEMESSAGE_VALUE:
                resId = ProtoData.SToC.RESPONSEOPERATEMESSAGE_VALUE;
                break;
            case ProtoData.CToS.REQUESTBUYCLOTHING_VALUE:
                resId = ProtoData.SToC.RESPONSEBUYCLOTHING_VALUE;
                break;
            case ProtoData.CToS.REQUESTCHANGECUPBOARD_VALUE:
                resId = ProtoData.SToC.RESPONSECHANGECUPBOARD_VALUE;
                break;
            case ProtoData.CToS.REQUESTCUPBOARDTOBODY_VALUE:
                resId = ProtoData.SToC.RESPONSECUPBOARDTOBODY_VALUE;
                break;
            case ProtoData.CToS.REQUESTSTARBITOMONEY_VALUE:
                resId = ProtoData.SToC.RESPONSESTARBITOMONEY_VALUE;
                break;
            case ProtoData.CToS.REQUESTGIVEPRESENT_VALUE:
                resId = ProtoData.SToC.RESPONSEGIVEPRESENT_VALUE;
                break;
            case ProtoData.CToS.REQUESTACCUSATION_VALUE:
                resId = ProtoData.SToC.RESPONSEACCUSATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETRANK_VALUE:
                resId = ProtoData.SToC.RESPONSEGETRANK_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETRANKING_VALUE:
                resId = ProtoData.SToC.RESPONSEGETRANKING_VALUE;
                break;
            case ProtoData.CToS.REQUESTJOINROOM_VALUE:
                resId = ProtoData.SToC.RESPONSEJOINROOM_VALUE;
                break;
            case ProtoData.CToS.REQUESTCREATEROOM_VALUE:
                resId = ProtoData.SToC.RESPONSECREATEROOM_VALUE;
                break;
            case ProtoData.CToS.REQUESTLEAVEROOM_VALUE:
                resId = ProtoData.SToC.RESPONSELEAVEROOM_VALUE;
                break;
            case ProtoData.CToS.REQUESTUPDATEROOMINFO_VALUE:
                resId = ProtoData.SToC.RESPONSEUPDATEROOMINFO_VALUE;
                break;
            case ProtoData.CToS.REQUESTUPDATEROOMROLE_VALUE:
                resId = ProtoData.SToC.RESPONSEUPDATEROOMROLE_VALUE;
                break;
            case ProtoData.CToS.REQUESTJOINFIGHTHALL_VALUE:
                resId = ProtoData.SToC.RESPONSEJOINFIGHTHALL_VALUE;
                break;
            case ProtoData.CToS.REQUESTSTARTINROOM_VALUE:
                resId = ProtoData.SToC.RESPONSESTARTINROOM_VALUE;
                break;
            case ProtoData.CToS.REQUESTRECOMMENDFRIEND_VALUE:
                resId = ProtoData.SToC.RESPONSERECOMMENDFRIEND_VALUE;
                break;
            case ProtoData.CToS.REQUESTUPDATENEWUSER_VALUE:
                resId = ProtoData.SToC.RESPONSEUPDATENEWUSER_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETTOTALENDLESS_VALUE:
                resId = ProtoData.SToC.RESPONSEGETTOTALENDLESS_VALUE;
                break;
            case ProtoData.CToS.REQUESTBEGINHEADBALL_VALUE:
                resId = ProtoData.SToC.RESPONSEBEGINHEADBALL_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTHEADBALL_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTHEADBALL_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTGOLD_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTGOLD_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTDOWN_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTDOWN_VALUE;
                break;
            case ProtoData.CToM.REQUESTCONNECTFIGHT_VALUE:
                resId = ProtoData.MToC.RESPONSECONNECTFIGHT_VALUE;
                /// System.out.println("============================responseConnectFight==========================");
                break;
            case ProtoData.CToM.REQUESTLOADFINISH_VALUE:
                resId = ProtoData.MToC.RESPONSELOADFINISH_VALUE;
                break;
            case ProtoData.CToM.REQUESTSYNC_VALUE:
                resId = ProtoData.MToC.RESPONSESYNC_VALUE;
                break;
            case ProtoData.CToM.REQUESTHEART_VALUE:
                resId = ProtoData.MToC.RESPONSEHEART_VALUE;
                break;


            //游戏服战斗服
            case ProtoData.SToM.REQUESTENTERMISSION_VALUE:
                resId = ProtoData.MToS.RESPONSEENTERMISSION_VALUE;
                break;
            case ProtoData.MToS.REQUESTOVERMISSION_VALUE:
                resId = ProtoData.SToM.RESPONSEOVERMISSION_VALUE;
                break;

            case ProtoData.CToS.REQUESTCHOOSEPAY_VALUE:
                resId = ProtoData.SToC.RESPONSECHOOSEPAY_VALUE;
                break;
            case ProtoData.CToS.REQUESTSUBMITPAYBACK_VALUE:
                resId = ProtoData.SToC.RESPONSESUBMITPAYBACK_VALUE;
                break;
            case ProtoData.CToS.REQUESTWECHATPAY_VALUE:
                resId = ProtoData.SToC.RESPONSEWECHATPAY_VALUE;
                break;
            case ProtoData.CToS.REQUESTALIPAY_VALUE:
                resId = ProtoData.SToC.RESPONSEALIPAY_VALUE;
                break;
            case ProtoData.CToS.REQUESTGOOGLEPAY_VALUE:
                resId = ProtoData.SToC.RESPONSEGOOGLEPAY_VALUE;
                break;
            case ProtoData.CToS.REQUESTYSDKBALANCE_VALUE:
                resId = ProtoData.SToC.RESPONSEYSDKBALANCE_VALUE;
                break;
            case ProtoData.CToS.REQUESTADPLAY_VALUE:
                resId = ProtoData.SToC.RESPONSEADPLAY_VALUE;
                break;
            case ProtoData.CToS.REQUESTADNUN_VALUE:
                resId = ProtoData.SToC.RESPONSEADNUM_VALUE;
                break;
            case ProtoData.CToS.REQUESTSOURCESMACHINE_VALUE:
                resId = ProtoData.SToC.RESPONSESOURCESMACHINE_VALUE;
                break;
            case ProtoData.CToS.REQURESTMAKEITEM_VALUE:
                resId = ProtoData.SToC.RESPONSEMAKEITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTPRODUCT_VALUE:
                resId = ProtoData.SToC.RESPONSEPRODUCT_VALUE;
                break;
            case ProtoData.CToS.REQUESTVISITOROPERATION_VALUE:
                resId = ProtoData.SToC.RESPONSEVISITOROPERATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTAPPROVAL_VALUE:
                resId = ProtoData.SToC.RESPONSEAPPROVAL_VALUE;
                break;
            case ProtoData.CToS.REQUESTAUTHENTICATION_VALUE:
                resId = ProtoData.SToC.RESPONSAUTHENTICATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETAPPROVAL_VALUE:
                resId = ProtoData.SToC.RESPONSEGETAPPROVAL_VALUE;
                break;
            case ProtoData.CToS.REQUESTQUERYROLEINFORMATION_VALUE:
                resId = ProtoData.SToC.RESPOSEQUERYROLEINFORMATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTSTARTGAMECOPY_VALUE:
                resId = ProtoData.SToC.RESPOSESTARTGAMECOPY_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTGAMECOPY_VALUE:
                resId = ProtoData.SToC.REAPOSECOUNTGAMECOPY_VALUE;
                break;
            case ProtoData.CToS.REQUESTBEGINSTACK_VALUE:
                resId = ProtoData.SToC.RESPONSEBEGINSTACK_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOUNTSTACK_VALUE:
                resId = ProtoData.SToC.RESPONSECOUNTSTACK_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETLUCKYITEM_VALUE:
                resId = ProtoData.SToC.RESPOSEGETLUCKYITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTFINISHACTIVITIES_VALUE:
                resId = ProtoData.SToC.RESPOSEFINISHACTIVITIES_VALUE;
                break;
            case ProtoData.CToS.REQUESTCONFIRMHUAWEIPURCHASE_VALUE:
                resId = ProtoData.SToC.RESPONSECONFIRMHUAWEIPURCHASE_VALUE;
                break;
            case ProtoData.CToS.REQUESTMARKETINFORMATION_VALUE:
                resId = ProtoData.SToC.RESPOSEMARKETINFORMATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTGOODSOPERATE_VALUE:
                resId = ProtoData.SToC.RESPOSEGOODSOPERATE_VALUE;
                break;
            case ProtoData.CToS.REQUESTTICKTEEXCHANGE_VALUE:
                resId = ProtoData.SToC.RESPOSETICKTEEXCHANGE_VALUE;
                break;
            case ProtoData.CToS.REQUESTWAITITEM_VALUE:
                resId = ProtoData.SToC.RESPOSEWAITITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTUPWAITITEMLEVEL_VALUE:
                resId = ProtoData.SToC.RESPOSEUPWAITITEMLEVEL_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETWAITITEM_VALUE:
                resId = ProtoData.SToC.RESPOSEGETWAITITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTHOUSE_VALUE:
                resId = ProtoData.SToC.RESPOSEHOUSE_VALUE;
                break;
            case ProtoData.CToS.REQUESTHOUSEPART_VALUE:
                resId = ProtoData.SToC.RESPOSEHOUSEPART_VALUE;
                break;
            case ProtoData.CToS.REQUESTBUYFURNITURE_VALUE:
                resId = ProtoData.SToC.RESPOSEBUYFURNITURE_VALUE;
                break;
            case ProtoData.CToS.REQUESTITEMCOMPOSE_VALUE:
                resId = ProtoData.SToC.RESPOSEITEMCOMPOSE_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATIONFRIENDS_VALUE:
                resId = ProtoData.SToC.RESPONSEOPERATIONFRIENDS_VALUE;
                break;
            case ProtoData.CToS.REQUESTPHYSICAL_VALUE:
                resId = ProtoData.SToC.RESPONSEPHYSICAL_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETROLEEXP_VALUE:
                resId = ProtoData.SToC.RESPONSEGETROLEEXP_VALUE;
                break;
            case ProtoData.CToS.REQUESTREMOVEPET_VALUE:
                resId = ProtoData.SToC.RESPONSEREMOVEPET_VALUE;
                break;
            case ProtoData.CToS.REQUESTDAILYTANSKS_VALUE:
                resId = ProtoData.SToC.RESPONSEDAILYTANSKS_VALUE;
                break;
            case ProtoData.CToS.REQUESTCOMPLETEDAILYTASKS_VALUE:
                resId = ProtoData.SToC.RESPONSECOMPLETEDAILYTASKS_VALUE;
                break;
            case ProtoData.CToS.REQUESTACHIEVEMENTTYPE_VALUE:
                resId = ProtoData.SToC.RESPONSEACHIEVEMENTTYPE_VALUE;
                break;
            case ProtoData.CToS.REQUESTACHIEVEMENTIS_VALUE:
                resId = ProtoData.SToC.RESPONSEACHIEVEMENTIS_VALUE;
                break;
            case ProtoData.CToS.REQUESTACHIEVEMENT_VALUE:
                resId = ProtoData.SToC.RESPONSEACHIEVEMENT_VALUE;
                break;
            case ProtoData.CToS.REQUESTADDITEM_VALUE:
                resId = ProtoData.SToC.REPORTITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTSYNTHESIS_VALUE:
                resId = ProtoData.SToC.RESPONSESYNTHESIS_VALUE;
                break;
            case ProtoData.CToS.REQUESTTASK_VALUE:
                resId = ProtoData.SToC.RESPONSETASK_VALUE;
                break;
            case 6888:
                resId = 6889;
                break;
            case 6890:
                resId = 6891;
                break;
            case 6892:
                resId = 6893;
                break;
            case 6894:
                resId = 6895;
                break;
            case 6896:
                resId = 6897;
                break;
            case ProtoData.CToS.REQUESTOPERATEMAIL_VALUE:
                resId = ProtoData.SToC.RESPOSEOPERATEMAIL_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATENOTICE_VALUE:
                resId=ProtoData.SToC.REPORTNEWNOTICE_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEPET_VALUE:
                resId = ProtoData.SToC.RESPOSEOPERATEPET_VALUE;
                break;
            case ProtoData.CToS.REQUESTCHOICEEQUIP_VALUE:
                resId = ProtoData.SToC.RESPONSECHOICEEQUIP_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEEQUIP_VALUE:
                resId = ProtoData.SToC.RESPONSEOPERATEEQUIP_VALUE;
                break;
            case ProtoData.CToS. REQUESTLEVELD_VALUE:
                resId =ProtoData.SToC.RESPPNSELEVELD_VALUE;
                break;
            case ProtoData.CToS. REQUESTChap_VALUE:
                resId =ProtoData.SToC.RESPONSEChap_VALUE;
                break;
            case ProtoData.CToS. REQUESTPlont_VALUE:
                resId =ProtoData.SToC.RESPONSEPlont_VALUE;
                break;
            case ProtoData.CToS.REQUESTGetEquipA_VALUE:
                resId =ProtoData.SToC.RESPONSEGetEquipA_VALUE;
                 break;
            case ProtoData.CToS.REQUESTEquipF_VALUE:
                resId =ProtoData.SToC.RESPONSEGetEquipA_VALUE;
                break;
            case ProtoData.CToS.REQUESTDDDEquip_VALUE:
                resId =ProtoData.SToC.RESPONSEDDDEquip_VALUE;
                break;
            case ProtoData.CToS.REQUESTbossTiem_VALUE:
                resId =ProtoData.SToC.RESPONSEbosstime_VALUE;
                break;
            case ProtoData.CToS.REQUESTbossdear_VALUE:
                resId =ProtoData.SToC.RESPONSEbossdown_VALUE;
                break;


            case ProtoData.CToS.REQUESTCHANGEEQUIP_VALUE:
                resId = ProtoData.SToC.RESPONSECHANGEEQUIP_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETPETSINFO_VALUE:
                resId = ProtoData.SToC.RESPOSEGETPETSINFO_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETEQUIP_VALUE:
                resId = ProtoData.SToC.RESPONSEGETEQUIP_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETMAP_VALUE:
                resId = ProtoData.SToC.RESPONSEGETMAP_VALUE;
                break;
            case ProtoData.CToS.REQUESTUPDMAP_VALUE:
                resId = ProtoData.SToC.RESPONSEUPDMAP_VALUE;
                break;
            case ProtoData.CToS.REQUESTBINDIDCARD_VALUE:
                resId = ProtoData.SToC.RESPOSEBINDIDCARD_VALUE;
                break;

            case ProtoData.CToS.REQUESTRAFFLEPOOL_VALUE:
                resId = ProtoData.SToC.RESPOSERAFFLEPOOL_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETEVENTINFO_VALUE:
                resId = ProtoData.SToC.RESPOSEGETEVENTINFO_VALUE;
                break;

            case ProtoData.CToS.REQUESTFLUSHEVENT_VALUE:
                resId = ProtoData.SToC.RESPOSEFLUSHEVENT_VALUE;
                break;

            case ProtoData.CToS.REQUESTCOUNTBATTLE_VALUE:
                resId = ProtoData.SToC.RESPOSECOUNTBATTLE_VALUE;
                break;
            case ProtoData.CToS.REQUESTPLAYERITEMS_VALUE:
                resId = ProtoData.SToC.RESPOSEPLAYERITEMS_VALUE;
                break;

            case ProtoData.CToS.REQUESTUPPETSKILL_VALUE:
                resId = ProtoData.SToC.RESPOSEUPPETSKILL_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETEXP_VALUE:
                resId = ProtoData.SToC.RESPONSEPETEXP_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEROLE_VALUE:
                resId = ProtoData.SToC.RESPONSEOPERATEROLE_VALUE;
                break;
            case ProtoData.CToS.REQUESTBUYBALCKMARKETITEM_VALUE:
                resId = ProtoData.SToC.RESPOSEBUYBALCKMARKETITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTBALCKMARKET_VALUE:
                resId = ProtoData.SToC.RESPOSEBALCKMARKET_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETPETFORMATION_VALUE:
                resId = ProtoData.SToC.RESPOSEGETPETFORMATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETFORMATION_VALUE:
                resId = ProtoData.SToC.RESPOSEPETFORMATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETCOMPOSE_VALUE:
                resId = ProtoData.SToC.RESPOSEPETCOMPOSE_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETBREEDINOFRMATION_VALUE:
                resId = ProtoData.SToC.RESPONSEPETBREEDINOFRMATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETPETBREEDINFO_VALUE:
                resId = ProtoData.SToC.RESPOSEGETPETBREEDINFO_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETBREEDOPERATION_VALUE:
                resId = ProtoData.SToC.RESPONSEPETBREEDOPERATION_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETBREED_VALUE:
                resId = ProtoData.SToC.RESPONSEPETBREED_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEPETBREED_VALUE:
                resId = ProtoData.SToC.RESPOSEOPERATEPETBREED_VALUE;
                break;
            case ProtoData.CToS.REQUESTGETPETGROWINFO_VALUE:
                resId = ProtoData.SToC.RESPOSEGETPETGROWINFO_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETEVOLUTION_VALUE:
                resId = ProtoData.SToC.RESPOSEPETEVOLUTION_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETBREAKTHROUGH_VALUE:
                resId = ProtoData.SToC.RESPOSEPETBREAKTHROUGH_VALUE;
                break;

            case ProtoData.CToS.REQUESTGETEXPERIENCES_VALUE:
                resId = ProtoData.SToC.RESPOSEGETEXPERIENCES_VALUE;
                break;
            case ProtoData.CToS.REQUESTFLUSHEXPERIENCES_VALUE:
                resId = ProtoData.SToC.RESPOSEFLUSHEXPERIENCES_VALUE;
                break;
            case ProtoData.CToS.REQUESTOPERATEEXPERIENCES_VALUE:
                resId = ProtoData.SToC.RESPOSEOPERATEEXPERIENCES_VALUE;
                break;

            case ProtoData.CToS.REQUESTPVPPetOperate_VALUE:
                resId = ProtoData.SToC.RESPONSEPVPPetOperate_VALUE;
                break;
            case ProtoData.CToS.REQUESTPVPBaseData_VALUE:
                resId = ProtoData.SToC.RESPONSEPVPBaseData_VALUE;
                break;
            case ProtoData.CToS.REQUESTPVPBattle_VALUE:
                resId = ProtoData.SToC.RESPONSEPVPBattle_VALUE;
                break;
            case ProtoData.CToS.REQUESTPVPBATTLEREMAINTIME_VALUE:
                resId = ProtoData.SToC.RESPONSEPVPBATTLEREMAINTIME_VALUE;
                break;
            case ProtoData.CToS.REQUESTRECHAREGREBATEREWARD_VALUE:
                resId = ProtoData.SToC.RESPONSERECHAREGREBATEREWARD_VALUE;
                break;
            case ProtoData.CToS.REQUESTLIMITEDTIMEREWARD_VALUE:
                resId = ProtoData.SToC.RESPONSELIMITEDTIMEREWARD_VALUE;
                break;
            case ProtoData.CToS.REQUESTREMAINTIME_VALUE:
                resId = ProtoData.SToC.RESPONSEREMAINTIME_VALUE;
                break;
            case ProtoData.CToS.REQUESTPETEGGEXP_VALUE:
                resId = ProtoData.SToC.RESPONSEPETEGGEXP_VALUE;
                break;
            case ProtoData.CToS.REQUESTADDPETEGGEXP_VALUE:
                resId = ProtoData.SToC.RESPONSEPETEGGEXP_VALUE;
                break;
            case ProtoData.CToS.REQUESTTEMITEM_VALUE:
                resId = ProtoData.SToC.RESPONSETEMITEM_VALUE;
                break;
            case ProtoData.CToS.REQUESTDELETEALLPETS_VALUE:
                resId = ProtoData.SToC.RESPONSEDELETEALLPETS_VALUE;
                break;
        }
        return resId;
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause)
            throws Exception {
        cause.printStackTrace();
        ctx.close();
        removeCtxFromMap(ctx);
        String role = linkMap.get(ctx);
        if (role != null) {
            rolectx.remove(role);
        }
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        String role = linkMap.get(ctx);
        if (role != null) {
            StringBuffer sql = new StringBuffer("from RoleEntity where uid='").append(role).append("'");
            RoleEntity roleinfo = (RoleEntity) MySql.queryForOne(sql.toString());
            StringBuffer hql = new StringBuffer("from UserEntity where userid='").append(roleinfo.getUserid()).append("'");
            UserEntity user = (UserEntity) MySql.queryForOne(hql.toString());
            long addtime = System.currentTimeMillis() - user.getLastlogin();
            if (addtime < 0) {
                addtime = 0;
            }
            user.setGametime(addtime + user.getGametime());
            roleinfo.setDailyGameTime(roleinfo.getDailyGameTime() + addtime);
            MySql.update(user);
            Redis jedis = Redis.getInstance();
            jedis.hset("role:" + roleinfo.getUid(), "dailyGameTime", roleinfo.getDailyGameTime() + "");
            MySql.update(roleinfo);
        }
        removeCtxFromMap(ctx);
        if (role != null) {
            rolectx.remove(role);
        }
    }

    private static void removeCtxFromMap(ChannelHandlerContext ctx) {
        if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MAIN || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_PLOTTER) {//游戏服
            String uid = null;
            synchronized (linkMap) {
                uid = linkMap.get(ctx);
            }
            if (uid != null) {
                Redis.setRoleOffLine(uid);
                Redis.setDelTimeForRoleAllInfoInRedis(uid);
                removeCtxFromRoleMap(ctx, uid);
            }
            if (newUserMap.containsKey(ctx)) {
                Redis.setDelTimeForUserInfoInRedis(newUserMap.get(ctx));
                newUserMap.remove(ctx);
            }
        } else if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION_PLOTTER) {//战斗服
            SyncManager.playGoOutMission(ctx);
        }
    }

    public static void removeCtxFromRoleMap(ChannelHandlerContext ctx, String uid) {
        synchronized (linkMap) {
            System.err.println("玩家下线=" + linkMap.get(ctx)+"\n"+ctx);
            FriendData.ResponsePlayerOfflineOrNot.Builder responsePlayerOfflineOrNot = FriendData.ResponsePlayerOfflineOrNot.newBuilder();
            responsePlayerOfflineOrNot.setType(0);
            StringBuilder str = new StringBuilder("from RoleEntity where uid='").append(linkMap.get(ctx)).append("'");
            RoleEntity roleEntityId = (RoleEntity) MySql.queryForOne(str.toString());
            responsePlayerOfflineOrNot.setId(roleEntityId.getId());
            str = new StringBuilder("from RelativeshipEntity where roleuid1='").append(linkMap.get(ctx)).append("' or roleuid2='").append(linkMap.get(ctx)).append("'");
            List<Object> relativeships = MySql.queryForList(str.toString());
            for (int i = 0; i < relativeships.size(); i++) {
                RelativeshipEntity relativeshipEntity = (RelativeshipEntity) relativeships.get(i);
                if (relativeshipEntity.getRoleuid1().equals(linkMap.get(ctx))) {
                    ReportManager.reportInfo(relativeshipEntity.getRoleuid2(), ProtoData.SToC.RESPONSEPLAYEROFFLINEORNOT_VALUE, responsePlayerOfflineOrNot.build().toByteArray());
                } else {
                    ReportManager.reportInfo(relativeshipEntity.getRoleuid1(), ProtoData.SToC.RESPONSEPLAYEROFFLINEORNOT_VALUE, responsePlayerOfflineOrNot.build().toByteArray());
                }
            }
            linkMap.remove(ctx);
        }
        int server = Redis.getRoleServer(uid);
        Map<String, ChannelHandlerContext> map = serverRoleMap.get(server);
        if (map == null || map.get(uid) != ctx) {
            return;
        }
        if (map != null) {
            map.remove(uid);
        }
        removeFromStation(uid, ctx);
        RoomManager.leaveFightHall(uid);

        PlayerOffline.getInstance().SendMessage(uid);
    }
//~~~~~

    private static void removeFromStation(String uid, ChannelHandlerContext ctx, int station) {
        ILogin iLogin = LoginDao.getInstance();
        List<StationInfo> list = stationMap.get(station);
        if (list != null) {
            UserData.ReportInAndOutStation.Builder stationBu = null;
            for (int i = 0; i < list.size(); i++) {
                StationInfo tmp = list.get(i);
                if (tmp.getCtx() == ctx) {
                    list.remove(i);
                    stationBu = UserData.ReportInAndOutStation.newBuilder();
                    stationBu.setType(2);
                    int roleId = iLogin.getIdFromUid(uid);
                    stationBu.setPassengerId(roleId);
                    //               ReportManager.reportStationOutChange(uid, station, stationBu.build().toByteArray());
                    break;
                }
            }
            if (stationBu != null) {
                for (int i = 0; i < list.size(); i++) {
                    StationInfo tmp = list.get(i);
                    ReportManager.reportInfo(tmp.getCtx(), ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, stationBu.build().toByteArray());
                }
            }

            Redis jedis = Redis.getInstance();
            jedis.hset("role:" + uid, "station", "-1");
        }
    }

    public static void removeFromStation(String uid, ChannelHandlerContext ctx) {
        ILogin iLogin = LoginDao.getInstance();
        int station = iLogin.getStationFromUid(uid);
        removeFromStation(uid, ctx, station);
    }

    public static void removeFromStation(String uid) {
        ChannelHandlerContext ctx = getServerIdCtxFromUid(uid);
        ILogin iLogin = LoginDao.getInstance();
        int station = iLogin.getStationFromUid(uid);
        removeFromStation(uid, ctx, station);
    }

    public static int addToStation(String uid, ChannelHandlerContext ctx) {
        ILogin iLogin = LoginDao.getInstance();
        int station = iLogin.getStationFromUid(uid);
        if (station != -1) {
            List<StationInfo> list = stationMap.get(station);
            for (int i = 0; i < list.size(); i++) {
                StationInfo stationInfo = list.get(i);
                if (stationInfo.getCtx() == ctx) {
                    return -1;
                }
            }
        }
        if (station == -1) {
            station = iLogin.getStationId(uid);
        }
        List<StationInfo> list = stationMap.get(station);
        if (list == null) {
            list = new ArrayList<StationInfo>();
            stationMap.put(station, list);
        }

        StationInfo stationInfo = new StationInfo();
        stationInfo.setUid(uid);
        stationInfo.setCtx(ctx);
        PointDoubleInfo point = new PointDoubleInfo();
        point.setX(0);
        point.setY(2);
        stationInfo.setPoint(point);
        list.add(stationInfo);
        return station;
    }


    private static void boardcastOwnStation(int station, ChannelHandlerContext ctx) {
        List<StationInfo> list = stationMap.get(station);
        //增加机器人
        Redis jedis = Redis.getInstance();
        String uid = linkMap.get(ctx);
        String status = jedis.hget(uid + ":station", "joinHall");
        if ((list == null || list.size() <= 2) && "1".equals(status)) {
            Redis.del(uid + ":station");
            if (station <= 0) {
                station = (int) (Math.random() * Factory.robotStationNums) + 1;
            }
            int currentPassengers = (list == null ? 0 : list.size());
            List<UserData.Passenger> passengerRobotList = LoginDao.getInstance().getStationRobotPassengers(uid, station, currentPassengers);
            if (passengerRobotList != null) {
                for (int i = 0; i < passengerRobotList.size(); i++) {
                    UserData.ReportInAndOutStation.Builder stationBu2 = UserData.ReportInAndOutStation.newBuilder();
                    stationBu2.setType(1);
                    stationBu2.setPassenger(passengerRobotList.get(i));
                    ReportManager.reportInfo(ctx, ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, stationBu2.build().toByteArray());
                }

            }

        }

           /* UserData.ReportInAndOutStation.Builder stationBu2 = UserData.ReportInAndOutStation.newBuilder();
            stationBu2.setType(1);
            try {
                Redis jedis=Redis.getInstance();
                String uid=linkMap.get(ctx);
                String status=jedis.hget(uid+":station","joinHall");
                if("1".equals(status)){
                List<UserData.Passenger> passengerRobotList = LoginDao.getInstance().getStationRobotPassengers(station);
                stationBu2.setPassenger(passengerRobotList.get(0));
                ReportManager.reportInfo(ctx, ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, stationBu2.build().toByteArray());
                 Redis.del(uid+":station");
                }
            }catch (Exception e){
                e.printStackTrace();
            }*/


        if (list != null) {
            ILogin iLogin = LoginDao.getInstance();
            for (int i = 0; i < list.size(); i++) {
                StationInfo info = list.get(i);
                if (ctx != info.getCtx()) {
                    UserData.ReportInAndOutStation.Builder stationBu = UserData.ReportInAndOutStation.newBuilder();
                    stationBu.setType(1);
                    String stationUid = linkMap.get(info.getCtx());
                    if (stationUid != null) {
                        RoleEntity roleEntity = iLogin.getRoleEntityFromRedis(stationUid);
                        if (roleEntity == null) {
                            removeCtxFromMap(info.getCtx());
                            continue;
                        }


                    }
                }
            }

        }
    }

    public static void comeBackToStation(String uid) {
        ChannelHandlerContext ctx = getServerIdCtxFromUid(uid);
        int nowStation = addToStation(uid, ctx);
        boardcastOwnStation(nowStation, ctx);

    }

    public static void changeStation(String uid, ChannelHandlerContext ctx, int old) {
        removeFromStation(uid, ctx, old);
        int nowStation = addToStation(uid, ctx);
    }

    public static void addCtxToRoleMap(ChannelHandlerContext ctx, String uid) {
        if (linkMap == null) {
            linkMap = new HashMap<ChannelHandlerContext, String>();
        }
        linkMap.put(ctx, uid);
        System.err.println("玩家上线=" + linkMap.get(ctx)+"\n"+ctx);
        FriendData.ResponsePlayerOfflineOrNot.Builder responsePlayerOfflineOrNot = FriendData.ResponsePlayerOfflineOrNot.newBuilder();
        responsePlayerOfflineOrNot.setType(1);
        StringBuilder str = new StringBuilder("from RoleEntity where uid='").append(linkMap.get(ctx)).append("'");
        RoleEntity roleEntityId = (RoleEntity) MySql.queryForOne(str.toString());
        responsePlayerOfflineOrNot.setId(roleEntityId.getId());
        str = new StringBuilder("from RelativeshipEntity where roleuid1='").append(linkMap.get(ctx)).append("' or roleuid2='").append(linkMap.get(ctx)).append("'");
        List<Object> relativeships = MySql.queryForList(str.toString());
        for (int i = 0; i < relativeships.size(); i++) {
            RelativeshipEntity relativeshipEntity = (RelativeshipEntity) relativeships.get(i);
            if (relativeshipEntity.getRoleuid1().equals(linkMap.get(ctx))) {
                ReportManager.reportInfo(relativeshipEntity.getRoleuid2(), ProtoData.SToC.RESPONSEPLAYEROFFLINEORNOT_VALUE, responsePlayerOfflineOrNot.build().toByteArray());
            } else {
                ReportManager.reportInfo(relativeshipEntity.getRoleuid1(), ProtoData.SToC.RESPONSEPLAYEROFFLINEORNOT_VALUE, responsePlayerOfflineOrNot.build().toByteArray());
            }
        }
        //玩家上线
        FriendData.ResponsePlayeronlineOrNot.Builder responsePlayeronlineOrNot = FriendData.ResponsePlayeronlineOrNot.newBuilder();
        FriendData.RoleUser.Builder roleUser = FriendData.RoleUser.newBuilder();
        StringBuilder stringBuilder = new StringBuilder("from RelativeshipEntity where roleuid1='").append(uid).append("' or roleuid2='").append(uid).append("'");
        List<Object> relativeshipEntityList = MySql.queryForList(stringBuilder.toString());
        for (int i = 0; i < relativeshipEntityList.size(); i++) {
            RelativeshipEntity relativeshipEntity = (RelativeshipEntity) relativeshipEntityList.get(i);
            if (relativeshipEntity.getRoleuid1().equals(uid)) {
                stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid2()).append("'");
                RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                roleUser.setId(roleEntity.getId());
                roleUser.setName(roleEntity.getName());
                roleUser.setLv(roleEntity.getLv());
                roleUser.setHead(roleEntity.getHead());
                roleUser.setStatus(0);
                for (ChannelHandlerContext key : linkMap.keySet()) {
                    if (linkMap.get(key).equals(relativeshipEntity.getRoleuid2())) {
                        roleUser.setStatus(1);
                    }
                }
                responsePlayeronlineOrNot.addRoleUser(roleUser);
            } else {
                stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid1()).append("'");
                RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                roleUser.setId(roleEntity.getId());
                roleUser.setName(roleEntity.getName());
                roleUser.setLv(roleEntity.getLv());
                roleUser.setHead(roleEntity.getHead());
                roleUser.setStatus(0);
                for (ChannelHandlerContext key : linkMap.keySet()) {
                    if (linkMap.get(key).equals(relativeshipEntity.getRoleuid1())) {
                        roleUser.setStatus(1);
                    }
                }
                responsePlayeronlineOrNot.addRoleUser(roleUser);
            }
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPLAYERONLINEORNOT_VALUE, responsePlayeronlineOrNot.build().toByteArray());
        //获取好友的申请
        FriendData.ResponseFriendsApply.Builder builder = FriendData.ResponseFriendsApply.newBuilder();
        FriendData.FriendsApply.Builder friendsApply = FriendData.FriendsApply.newBuilder();
        stringBuilder = new StringBuilder("from FriendApplicationEntity where roleuid2='").append(linkMap.get(ctx)).append("'");
        List<Object> friendApplicationList = MySql.queryForList(stringBuilder.toString());
        for (int i = 0; i < friendApplicationList.size(); i++) {
            FriendApplicationEntity friendApplicationEntity = (FriendApplicationEntity) friendApplicationList.get(i);
            stringBuilder = new StringBuilder("from RoleEntity where uid='").append(friendApplicationEntity.getRoleuid1()).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            friendsApply.setId(roleEntity.getId());
            if (roleEntity.getSignaTure()==null){
                friendsApply.setSignature("");
            }else{
                friendsApply.setSignature(roleEntity.getSignaTure());
            }
            friendsApply.setHead(roleEntity.getHead());
            friendsApply.setLv(roleEntity.getLv());
            friendsApply.setName(roleEntity.getName());
            friendsApply.setStatus(1);
            friendsApply.setType(friendApplicationEntity.getApply());
            if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                friendsApply.setStatus(0);
            }
            builder.addFriendsApply(friendsApply);
        }
        ReportManager.reportInfo(linkMap.get(ctx), ProtoData.SToC.RESPONSEFRIENDSAPPLY_VALUE, builder.build().toByteArray());
        int server = Redis.getRoleServer(uid);
        Map<String, ChannelHandlerContext> nowMap = serverRoleMap.get(server);
        if (nowMap == null) {
            nowMap = new HashMap<String, ChannelHandlerContext>();
            serverRoleMap.put(server, nowMap);
        }
        nowMap.put(uid, ctx);
        addToStation(uid, ctx);


        // 上线发送消息
        PlayerOnline.getInstance().SendMessage(uid);
    }

    //不确定是否在线
    public static ChannelHandlerContext getCtxFromUid(String uid) {
        ChannelHandlerContext ctx = null;
        for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
            if (uid.equals(entry.getValue())) {
                ctx = entry.getKey();
                break;
            }
        }
        return ctx;
    }

    //确定在线
    public static ChannelHandlerContext getServerIdCtxFromUid(String uid) {
        int server = Redis.getRoleServer(uid);
        return server == -1 ? null : getServerIdCtxFromUid(server, uid);
    }

    public static ChannelHandlerContext getServerIdCtxFromUid(int server, String uid) {
        Map<String, ChannelHandlerContext> roleMap = SuperServerHandler.serverRoleMap.get(server);
        return roleMap != null ? roleMap.get(uid) : null;
    }

    public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
        /// System.err.println(ctx.channel().remoteAddress());
    }

}