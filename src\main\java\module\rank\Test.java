package module.rank;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import entities.ItemEntity;
import entities.RankEntity;
import entities.RoleEntity;
import module.item.ItemDao;
import module.role.RoleDao;
import protocol.ItemData;

import javax.xml.transform.Result;
import java.lang.reflect.Field;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

public class Test {
//        List<RankEntity> rankEntities=(List<RankEntity>)(List) role;
    public static void main(String[] args) {
//        RankEntity rankEntity = new RankEntity();
//        RankDao rankDao = new RankDao();
//       List<Object> list= rankDao.getPetNumRankSqlToRedis("20230203110135Kra50083I1o");
//       RoleEntity rankEntity1=new ObjectMapper().convertValue(list,RoleEntity.class);
//       ObjectMapper objectMapper= new ObjectMapper();
//        RankEntity roleEntity= JSON.parseObject(JSON.toJSONString(list),RankEntity.class);
//        ItemEntity itemEntity = new ItemEntity();
//        ItemData.Item.Builder builder=ItemData.Item.newBuilder();
//        double i=builder.getNum();
//        System.out.println(i);
        RoleEntity roleEntity = (RoleEntity) RoleDao.getInstance().query("20230221160324vdK43216PNp");
        roleEntity.setLv(roleEntity.getLv());
        roleEntity.setExp(roleEntity.getExp());
        System.out.println(roleEntity);
//        System.out.println(rankEntity.toString());
//        System.out.println(rankEntity1);
//       for (Object l:list){
//           System.out.println(l.toString());
//       }
//        System.out.println(list);
//        ArrayList<Object> objects = new ArrayList<>();
//        for (Object exe:list){
//            RoleEntity roleEntity = new RoleEntity();
//            Map map= (Map) exe;
//            roleEntity.setName(map.get("name").toString());
//            roleEntity.setLv(Integer.parseInt((String) map.get("lv")));
//            roleEntity.setHead(Integer.parseInt((String) map.get("head")));
//            roleEntity.setRanknumber(Integer.parseInt((String) map.get("head")));
//            objects.add(roleEntity);
//            System.out.println(roleEntity);
//        }

//        System.out.println(rankDao.getPetNumRankSqlToRedis("20230203110135Kra50083I1o"));
//        System.out.println();
//        EquipDao equipDao = new EquipDao();
//        List<Object> list=equipDao.queryEquip("20220816153814vh64503FAue");
//        System.out.println(list);
    }

}
