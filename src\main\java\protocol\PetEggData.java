// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: petegg.proto

package protocol;

public final class PetEggData {
  private PetEggData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestPetEggExpOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 pet_uid = 1;
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    boolean hasPetUid();
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    int getPetUid();
  }
  /**
   * Protobuf type {@code protocol.RequestPetEggExp}
   *
   * <pre>
   * 1360 查询蛋的经验值和等级
   * </pre>
   */
  public static final class RequestPetEggExp extends
      com.google.protobuf.GeneratedMessage
      implements RequestPetEggExpOrBuilder {
    // Use RequestPetEggExp.newBuilder() to construct.
    private RequestPetEggExp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPetEggExp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPetEggExp defaultInstance;
    public static RequestPetEggExp getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPetEggExp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPetEggExp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petUid_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PetEggData.internal_static_protocol_RequestPetEggExp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PetEggData.internal_static_protocol_RequestPetEggExp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PetEggData.RequestPetEggExp.class, protocol.PetEggData.RequestPetEggExp.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPetEggExp> PARSER =
        new com.google.protobuf.AbstractParser<RequestPetEggExp>() {
      public RequestPetEggExp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPetEggExp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPetEggExp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 pet_uid = 1;
    public static final int PET_UID_FIELD_NUMBER = 1;
    private int petUid_;
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    public boolean hasPetUid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    public int getPetUid() {
      return petUid_;
    }

    private void initFields() {
      petUid_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetUid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petUid_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petUid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PetEggData.RequestPetEggExp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.RequestPetEggExp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PetEggData.RequestPetEggExp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.RequestPetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PetEggData.RequestPetEggExp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPetEggExp}
     *
     * <pre>
     * 1360 查询蛋的经验值和等级
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PetEggData.RequestPetEggExpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PetEggData.internal_static_protocol_RequestPetEggExp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PetEggData.internal_static_protocol_RequestPetEggExp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PetEggData.RequestPetEggExp.class, protocol.PetEggData.RequestPetEggExp.Builder.class);
      }

      // Construct using protocol.PetEggData.RequestPetEggExp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petUid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PetEggData.internal_static_protocol_RequestPetEggExp_descriptor;
      }

      public protocol.PetEggData.RequestPetEggExp getDefaultInstanceForType() {
        return protocol.PetEggData.RequestPetEggExp.getDefaultInstance();
      }

      public protocol.PetEggData.RequestPetEggExp build() {
        protocol.PetEggData.RequestPetEggExp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PetEggData.RequestPetEggExp buildPartial() {
        protocol.PetEggData.RequestPetEggExp result = new protocol.PetEggData.RequestPetEggExp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petUid_ = petUid_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PetEggData.RequestPetEggExp) {
          return mergeFrom((protocol.PetEggData.RequestPetEggExp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PetEggData.RequestPetEggExp other) {
        if (other == protocol.PetEggData.RequestPetEggExp.getDefaultInstance()) return this;
        if (other.hasPetUid()) {
          setPetUid(other.getPetUid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetUid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PetEggData.RequestPetEggExp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PetEggData.RequestPetEggExp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 pet_uid = 1;
      private int petUid_ ;
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public boolean hasPetUid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public int getPetUid() {
        return petUid_;
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public Builder setPetUid(int value) {
        bitField0_ |= 0x00000001;
        petUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public Builder clearPetUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petUid_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPetEggExp)
    }

    static {
      defaultInstance = new RequestPetEggExp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPetEggExp)
  }

  public interface RequestAddPetEggExpOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 pet_uid = 1;
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    boolean hasPetUid();
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    int getPetUid();

    // required int32 add_exp = 2;
    /**
     * <code>required int32 add_exp = 2;</code>
     */
    boolean hasAddExp();
    /**
     * <code>required int32 add_exp = 2;</code>
     */
    int getAddExp();
  }
  /**
   * Protobuf type {@code protocol.RequestAddPetEggExp}
   *
   * <pre>
   * 1361 经验值增加
   * </pre>
   */
  public static final class RequestAddPetEggExp extends
      com.google.protobuf.GeneratedMessage
      implements RequestAddPetEggExpOrBuilder {
    // Use RequestAddPetEggExp.newBuilder() to construct.
    private RequestAddPetEggExp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestAddPetEggExp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestAddPetEggExp defaultInstance;
    public static RequestAddPetEggExp getDefaultInstance() {
      return defaultInstance;
    }

    public RequestAddPetEggExp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestAddPetEggExp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petUid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              addExp_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PetEggData.internal_static_protocol_RequestAddPetEggExp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PetEggData.internal_static_protocol_RequestAddPetEggExp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PetEggData.RequestAddPetEggExp.class, protocol.PetEggData.RequestAddPetEggExp.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestAddPetEggExp> PARSER =
        new com.google.protobuf.AbstractParser<RequestAddPetEggExp>() {
      public RequestAddPetEggExp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestAddPetEggExp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestAddPetEggExp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 pet_uid = 1;
    public static final int PET_UID_FIELD_NUMBER = 1;
    private int petUid_;
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    public boolean hasPetUid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    public int getPetUid() {
      return petUid_;
    }

    // required int32 add_exp = 2;
    public static final int ADD_EXP_FIELD_NUMBER = 2;
    private int addExp_;
    /**
     * <code>required int32 add_exp = 2;</code>
     */
    public boolean hasAddExp() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 add_exp = 2;</code>
     */
    public int getAddExp() {
      return addExp_;
    }

    private void initFields() {
      petUid_ = 0;
      addExp_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetUid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAddExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petUid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, addExp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petUid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, addExp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.RequestAddPetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PetEggData.RequestAddPetEggExp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestAddPetEggExp}
     *
     * <pre>
     * 1361 经验值增加
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PetEggData.RequestAddPetEggExpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PetEggData.internal_static_protocol_RequestAddPetEggExp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PetEggData.internal_static_protocol_RequestAddPetEggExp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PetEggData.RequestAddPetEggExp.class, protocol.PetEggData.RequestAddPetEggExp.Builder.class);
      }

      // Construct using protocol.PetEggData.RequestAddPetEggExp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petUid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        addExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PetEggData.internal_static_protocol_RequestAddPetEggExp_descriptor;
      }

      public protocol.PetEggData.RequestAddPetEggExp getDefaultInstanceForType() {
        return protocol.PetEggData.RequestAddPetEggExp.getDefaultInstance();
      }

      public protocol.PetEggData.RequestAddPetEggExp build() {
        protocol.PetEggData.RequestAddPetEggExp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PetEggData.RequestAddPetEggExp buildPartial() {
        protocol.PetEggData.RequestAddPetEggExp result = new protocol.PetEggData.RequestAddPetEggExp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petUid_ = petUid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.addExp_ = addExp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PetEggData.RequestAddPetEggExp) {
          return mergeFrom((protocol.PetEggData.RequestAddPetEggExp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PetEggData.RequestAddPetEggExp other) {
        if (other == protocol.PetEggData.RequestAddPetEggExp.getDefaultInstance()) return this;
        if (other.hasPetUid()) {
          setPetUid(other.getPetUid());
        }
        if (other.hasAddExp()) {
          setAddExp(other.getAddExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetUid()) {
          
          return false;
        }
        if (!hasAddExp()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PetEggData.RequestAddPetEggExp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PetEggData.RequestAddPetEggExp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 pet_uid = 1;
      private int petUid_ ;
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public boolean hasPetUid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public int getPetUid() {
        return petUid_;
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public Builder setPetUid(int value) {
        bitField0_ |= 0x00000001;
        petUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public Builder clearPetUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petUid_ = 0;
        onChanged();
        return this;
      }

      // required int32 add_exp = 2;
      private int addExp_ ;
      /**
       * <code>required int32 add_exp = 2;</code>
       */
      public boolean hasAddExp() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 add_exp = 2;</code>
       */
      public int getAddExp() {
        return addExp_;
      }
      /**
       * <code>required int32 add_exp = 2;</code>
       */
      public Builder setAddExp(int value) {
        bitField0_ |= 0x00000002;
        addExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 add_exp = 2;</code>
       */
      public Builder clearAddExp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        addExp_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestAddPetEggExp)
    }

    static {
      defaultInstance = new RequestAddPetEggExp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestAddPetEggExp)
  }

  public interface ResponsePetEggExpOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 pet_uid = 1;
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    boolean hasPetUid();
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    int getPetUid();

    // required int32 pet_lv = 2;
    /**
     * <code>required int32 pet_lv = 2;</code>
     */
    boolean hasPetLv();
    /**
     * <code>required int32 pet_lv = 2;</code>
     */
    int getPetLv();

    // required int32 pet_exp = 3;
    /**
     * <code>required int32 pet_exp = 3;</code>
     */
    boolean hasPetExp();
    /**
     * <code>required int32 pet_exp = 3;</code>
     */
    int getPetExp();
  }
  /**
   * Protobuf type {@code protocol.ResponsePetEggExp}
   *
   * <pre>
   * 2360
   * </pre>
   */
  public static final class ResponsePetEggExp extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePetEggExpOrBuilder {
    // Use ResponsePetEggExp.newBuilder() to construct.
    private ResponsePetEggExp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePetEggExp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePetEggExp defaultInstance;
    public static ResponsePetEggExp getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePetEggExp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePetEggExp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petUid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              petLv_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              petExp_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PetEggData.internal_static_protocol_ResponsePetEggExp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PetEggData.internal_static_protocol_ResponsePetEggExp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PetEggData.ResponsePetEggExp.class, protocol.PetEggData.ResponsePetEggExp.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePetEggExp> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePetEggExp>() {
      public ResponsePetEggExp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePetEggExp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePetEggExp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 pet_uid = 1;
    public static final int PET_UID_FIELD_NUMBER = 1;
    private int petUid_;
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    public boolean hasPetUid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 pet_uid = 1;</code>
     */
    public int getPetUid() {
      return petUid_;
    }

    // required int32 pet_lv = 2;
    public static final int PET_LV_FIELD_NUMBER = 2;
    private int petLv_;
    /**
     * <code>required int32 pet_lv = 2;</code>
     */
    public boolean hasPetLv() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 pet_lv = 2;</code>
     */
    public int getPetLv() {
      return petLv_;
    }

    // required int32 pet_exp = 3;
    public static final int PET_EXP_FIELD_NUMBER = 3;
    private int petExp_;
    /**
     * <code>required int32 pet_exp = 3;</code>
     */
    public boolean hasPetExp() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 pet_exp = 3;</code>
     */
    public int getPetExp() {
      return petExp_;
    }

    private void initFields() {
      petUid_ = 0;
      petLv_ = 0;
      petExp_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetUid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPetLv()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPetExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petUid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, petLv_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, petExp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petUid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, petLv_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, petExp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PetEggData.ResponsePetEggExp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.ResponsePetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PetEggData.ResponsePetEggExp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePetEggExp}
     *
     * <pre>
     * 2360
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PetEggData.ResponsePetEggExpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PetEggData.internal_static_protocol_ResponsePetEggExp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PetEggData.internal_static_protocol_ResponsePetEggExp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PetEggData.ResponsePetEggExp.class, protocol.PetEggData.ResponsePetEggExp.Builder.class);
      }

      // Construct using protocol.PetEggData.ResponsePetEggExp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petUid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        petLv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        petExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PetEggData.internal_static_protocol_ResponsePetEggExp_descriptor;
      }

      public protocol.PetEggData.ResponsePetEggExp getDefaultInstanceForType() {
        return protocol.PetEggData.ResponsePetEggExp.getDefaultInstance();
      }

      public protocol.PetEggData.ResponsePetEggExp build() {
        protocol.PetEggData.ResponsePetEggExp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PetEggData.ResponsePetEggExp buildPartial() {
        protocol.PetEggData.ResponsePetEggExp result = new protocol.PetEggData.ResponsePetEggExp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petUid_ = petUid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.petLv_ = petLv_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.petExp_ = petExp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PetEggData.ResponsePetEggExp) {
          return mergeFrom((protocol.PetEggData.ResponsePetEggExp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PetEggData.ResponsePetEggExp other) {
        if (other == protocol.PetEggData.ResponsePetEggExp.getDefaultInstance()) return this;
        if (other.hasPetUid()) {
          setPetUid(other.getPetUid());
        }
        if (other.hasPetLv()) {
          setPetLv(other.getPetLv());
        }
        if (other.hasPetExp()) {
          setPetExp(other.getPetExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetUid()) {
          
          return false;
        }
        if (!hasPetLv()) {
          
          return false;
        }
        if (!hasPetExp()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PetEggData.ResponsePetEggExp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PetEggData.ResponsePetEggExp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 pet_uid = 1;
      private int petUid_ ;
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public boolean hasPetUid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public int getPetUid() {
        return petUid_;
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public Builder setPetUid(int value) {
        bitField0_ |= 0x00000001;
        petUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 pet_uid = 1;</code>
       */
      public Builder clearPetUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petUid_ = 0;
        onChanged();
        return this;
      }

      // required int32 pet_lv = 2;
      private int petLv_ ;
      /**
       * <code>required int32 pet_lv = 2;</code>
       */
      public boolean hasPetLv() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 pet_lv = 2;</code>
       */
      public int getPetLv() {
        return petLv_;
      }
      /**
       * <code>required int32 pet_lv = 2;</code>
       */
      public Builder setPetLv(int value) {
        bitField0_ |= 0x00000002;
        petLv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 pet_lv = 2;</code>
       */
      public Builder clearPetLv() {
        bitField0_ = (bitField0_ & ~0x00000002);
        petLv_ = 0;
        onChanged();
        return this;
      }

      // required int32 pet_exp = 3;
      private int petExp_ ;
      /**
       * <code>required int32 pet_exp = 3;</code>
       */
      public boolean hasPetExp() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 pet_exp = 3;</code>
       */
      public int getPetExp() {
        return petExp_;
      }
      /**
       * <code>required int32 pet_exp = 3;</code>
       */
      public Builder setPetExp(int value) {
        bitField0_ |= 0x00000004;
        petExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 pet_exp = 3;</code>
       */
      public Builder clearPetExp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        petExp_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePetEggExp)
    }

    static {
      defaultInstance = new ResponsePetEggExp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePetEggExp)
  }

  public interface ResponseAllPetEggExpOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.ResponsePetEggExp data = 1;
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    java.util.List<protocol.PetEggData.ResponsePetEggExp> 
        getDataList();
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    protocol.PetEggData.ResponsePetEggExp getData(int index);
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    int getDataCount();
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    java.util.List<? extends protocol.PetEggData.ResponsePetEggExpOrBuilder> 
        getDataOrBuilderList();
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    protocol.PetEggData.ResponsePetEggExpOrBuilder getDataOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseAllPetEggExp}
   *
   * <pre>
   * 2361
   * </pre>
   */
  public static final class ResponseAllPetEggExp extends
      com.google.protobuf.GeneratedMessage
      implements ResponseAllPetEggExpOrBuilder {
    // Use ResponseAllPetEggExp.newBuilder() to construct.
    private ResponseAllPetEggExp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseAllPetEggExp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseAllPetEggExp defaultInstance;
    public static ResponseAllPetEggExp getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseAllPetEggExp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseAllPetEggExp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                data_ = new java.util.ArrayList<protocol.PetEggData.ResponsePetEggExp>();
                mutable_bitField0_ |= 0x00000001;
              }
              data_.add(input.readMessage(protocol.PetEggData.ResponsePetEggExp.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          data_ = java.util.Collections.unmodifiableList(data_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PetEggData.internal_static_protocol_ResponseAllPetEggExp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PetEggData.internal_static_protocol_ResponseAllPetEggExp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PetEggData.ResponseAllPetEggExp.class, protocol.PetEggData.ResponseAllPetEggExp.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseAllPetEggExp> PARSER =
        new com.google.protobuf.AbstractParser<ResponseAllPetEggExp>() {
      public ResponseAllPetEggExp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseAllPetEggExp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseAllPetEggExp> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.ResponsePetEggExp data = 1;
    public static final int DATA_FIELD_NUMBER = 1;
    private java.util.List<protocol.PetEggData.ResponsePetEggExp> data_;
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    public java.util.List<protocol.PetEggData.ResponsePetEggExp> getDataList() {
      return data_;
    }
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    public java.util.List<? extends protocol.PetEggData.ResponsePetEggExpOrBuilder> 
        getDataOrBuilderList() {
      return data_;
    }
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    public int getDataCount() {
      return data_.size();
    }
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    public protocol.PetEggData.ResponsePetEggExp getData(int index) {
      return data_.get(index);
    }
    /**
     * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
     */
    public protocol.PetEggData.ResponsePetEggExpOrBuilder getDataOrBuilder(
        int index) {
      return data_.get(index);
    }

    private void initFields() {
      data_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getDataCount(); i++) {
        if (!getData(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < data_.size(); i++) {
        output.writeMessage(1, data_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < data_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, data_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.ResponseAllPetEggExp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PetEggData.ResponseAllPetEggExp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseAllPetEggExp}
     *
     * <pre>
     * 2361
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PetEggData.ResponseAllPetEggExpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PetEggData.internal_static_protocol_ResponseAllPetEggExp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PetEggData.internal_static_protocol_ResponseAllPetEggExp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PetEggData.ResponseAllPetEggExp.class, protocol.PetEggData.ResponseAllPetEggExp.Builder.class);
      }

      // Construct using protocol.PetEggData.ResponseAllPetEggExp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          dataBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PetEggData.internal_static_protocol_ResponseAllPetEggExp_descriptor;
      }

      public protocol.PetEggData.ResponseAllPetEggExp getDefaultInstanceForType() {
        return protocol.PetEggData.ResponseAllPetEggExp.getDefaultInstance();
      }

      public protocol.PetEggData.ResponseAllPetEggExp build() {
        protocol.PetEggData.ResponseAllPetEggExp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PetEggData.ResponseAllPetEggExp buildPartial() {
        protocol.PetEggData.ResponseAllPetEggExp result = new protocol.PetEggData.ResponseAllPetEggExp(this);
        int from_bitField0_ = bitField0_;
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            data_ = java.util.Collections.unmodifiableList(data_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PetEggData.ResponseAllPetEggExp) {
          return mergeFrom((protocol.PetEggData.ResponseAllPetEggExp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PetEggData.ResponseAllPetEggExp other) {
        if (other == protocol.PetEggData.ResponseAllPetEggExp.getDefaultInstance()) return this;
        if (dataBuilder_ == null) {
          if (!other.data_.isEmpty()) {
            if (data_.isEmpty()) {
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureDataIsMutable();
              data_.addAll(other.data_);
            }
            onChanged();
          }
        } else {
          if (!other.data_.isEmpty()) {
            if (dataBuilder_.isEmpty()) {
              dataBuilder_.dispose();
              dataBuilder_ = null;
              data_ = other.data_;
              bitField0_ = (bitField0_ & ~0x00000001);
              dataBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getDataFieldBuilder() : null;
            } else {
              dataBuilder_.addAllMessages(other.data_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getDataCount(); i++) {
          if (!getData(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PetEggData.ResponseAllPetEggExp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PetEggData.ResponseAllPetEggExp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.ResponsePetEggExp data = 1;
      private java.util.List<protocol.PetEggData.ResponsePetEggExp> data_ =
        java.util.Collections.emptyList();
      private void ensureDataIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          data_ = new java.util.ArrayList<protocol.PetEggData.ResponsePetEggExp>(data_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PetEggData.ResponsePetEggExp, protocol.PetEggData.ResponsePetEggExp.Builder, protocol.PetEggData.ResponsePetEggExpOrBuilder> dataBuilder_;

      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public java.util.List<protocol.PetEggData.ResponsePetEggExp> getDataList() {
        if (dataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(data_);
        } else {
          return dataBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public int getDataCount() {
        if (dataBuilder_ == null) {
          return data_.size();
        } else {
          return dataBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public protocol.PetEggData.ResponsePetEggExp getData(int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);
        } else {
          return dataBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder setData(
          int index, protocol.PetEggData.ResponsePetEggExp value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.set(index, value);
          onChanged();
        } else {
          dataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder setData(
          int index, protocol.PetEggData.ResponsePetEggExp.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.set(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder addData(protocol.PetEggData.ResponsePetEggExp value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(value);
          onChanged();
        } else {
          dataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder addData(
          int index, protocol.PetEggData.ResponsePetEggExp value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDataIsMutable();
          data_.add(index, value);
          onChanged();
        } else {
          dataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder addData(
          protocol.PetEggData.ResponsePetEggExp.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder addData(
          int index, protocol.PetEggData.ResponsePetEggExp.Builder builderForValue) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.add(index, builderForValue.build());
          onChanged();
        } else {
          dataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder addAllData(
          java.lang.Iterable<? extends protocol.PetEggData.ResponsePetEggExp> values) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          super.addAll(values, data_);
          onChanged();
        } else {
          dataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public Builder removeData(int index) {
        if (dataBuilder_ == null) {
          ensureDataIsMutable();
          data_.remove(index);
          onChanged();
        } else {
          dataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public protocol.PetEggData.ResponsePetEggExp.Builder getDataBuilder(
          int index) {
        return getDataFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public protocol.PetEggData.ResponsePetEggExpOrBuilder getDataOrBuilder(
          int index) {
        if (dataBuilder_ == null) {
          return data_.get(index);  } else {
          return dataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public java.util.List<? extends protocol.PetEggData.ResponsePetEggExpOrBuilder> 
           getDataOrBuilderList() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(data_);
        }
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public protocol.PetEggData.ResponsePetEggExp.Builder addDataBuilder() {
        return getDataFieldBuilder().addBuilder(
            protocol.PetEggData.ResponsePetEggExp.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public protocol.PetEggData.ResponsePetEggExp.Builder addDataBuilder(
          int index) {
        return getDataFieldBuilder().addBuilder(
            index, protocol.PetEggData.ResponsePetEggExp.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.ResponsePetEggExp data = 1;</code>
       */
      public java.util.List<protocol.PetEggData.ResponsePetEggExp.Builder> 
           getDataBuilderList() {
        return getDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PetEggData.ResponsePetEggExp, protocol.PetEggData.ResponsePetEggExp.Builder, protocol.PetEggData.ResponsePetEggExpOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.PetEggData.ResponsePetEggExp, protocol.PetEggData.ResponsePetEggExp.Builder, protocol.PetEggData.ResponsePetEggExpOrBuilder>(
                  data_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseAllPetEggExp)
    }

    static {
      defaultInstance = new ResponseAllPetEggExp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseAllPetEggExp)
  }

  public interface ResponsePetEggFastHatchOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 petUid = 1;
    /**
     * <code>required int32 petUid = 1;</code>
     *
     * <pre>
     * 进行孵化的宠物
     * </pre>
     */
    boolean hasPetUid();
    /**
     * <code>required int32 petUid = 1;</code>
     *
     * <pre>
     * 进行孵化的宠物
     * </pre>
     */
    int getPetUid();
  }
  /**
   * Protobuf type {@code protocol.ResponsePetEggFastHatch}
   *
   * <pre>
   * 1362 快速孵化
   * </pre>
   */
  public static final class ResponsePetEggFastHatch extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePetEggFastHatchOrBuilder {
    // Use ResponsePetEggFastHatch.newBuilder() to construct.
    private ResponsePetEggFastHatch(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePetEggFastHatch(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePetEggFastHatch defaultInstance;
    public static ResponsePetEggFastHatch getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePetEggFastHatch getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePetEggFastHatch(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petUid_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PetEggData.internal_static_protocol_ResponsePetEggFastHatch_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PetEggData.internal_static_protocol_ResponsePetEggFastHatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PetEggData.ResponsePetEggFastHatch.class, protocol.PetEggData.ResponsePetEggFastHatch.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePetEggFastHatch> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePetEggFastHatch>() {
      public ResponsePetEggFastHatch parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePetEggFastHatch(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePetEggFastHatch> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 petUid = 1;
    public static final int PETUID_FIELD_NUMBER = 1;
    private int petUid_;
    /**
     * <code>required int32 petUid = 1;</code>
     *
     * <pre>
     * 进行孵化的宠物
     * </pre>
     */
    public boolean hasPetUid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 petUid = 1;</code>
     *
     * <pre>
     * 进行孵化的宠物
     * </pre>
     */
    public int getPetUid() {
      return petUid_;
    }

    private void initFields() {
      petUid_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetUid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petUid_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petUid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PetEggData.ResponsePetEggFastHatch parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PetEggData.ResponsePetEggFastHatch prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePetEggFastHatch}
     *
     * <pre>
     * 1362 快速孵化
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PetEggData.ResponsePetEggFastHatchOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PetEggData.internal_static_protocol_ResponsePetEggFastHatch_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PetEggData.internal_static_protocol_ResponsePetEggFastHatch_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PetEggData.ResponsePetEggFastHatch.class, protocol.PetEggData.ResponsePetEggFastHatch.Builder.class);
      }

      // Construct using protocol.PetEggData.ResponsePetEggFastHatch.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petUid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PetEggData.internal_static_protocol_ResponsePetEggFastHatch_descriptor;
      }

      public protocol.PetEggData.ResponsePetEggFastHatch getDefaultInstanceForType() {
        return protocol.PetEggData.ResponsePetEggFastHatch.getDefaultInstance();
      }

      public protocol.PetEggData.ResponsePetEggFastHatch build() {
        protocol.PetEggData.ResponsePetEggFastHatch result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PetEggData.ResponsePetEggFastHatch buildPartial() {
        protocol.PetEggData.ResponsePetEggFastHatch result = new protocol.PetEggData.ResponsePetEggFastHatch(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petUid_ = petUid_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PetEggData.ResponsePetEggFastHatch) {
          return mergeFrom((protocol.PetEggData.ResponsePetEggFastHatch)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PetEggData.ResponsePetEggFastHatch other) {
        if (other == protocol.PetEggData.ResponsePetEggFastHatch.getDefaultInstance()) return this;
        if (other.hasPetUid()) {
          setPetUid(other.getPetUid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetUid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PetEggData.ResponsePetEggFastHatch parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PetEggData.ResponsePetEggFastHatch) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 petUid = 1;
      private int petUid_ ;
      /**
       * <code>required int32 petUid = 1;</code>
       *
       * <pre>
       * 进行孵化的宠物
       * </pre>
       */
      public boolean hasPetUid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 petUid = 1;</code>
       *
       * <pre>
       * 进行孵化的宠物
       * </pre>
       */
      public int getPetUid() {
        return petUid_;
      }
      /**
       * <code>required int32 petUid = 1;</code>
       *
       * <pre>
       * 进行孵化的宠物
       * </pre>
       */
      public Builder setPetUid(int value) {
        bitField0_ |= 0x00000001;
        petUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 petUid = 1;</code>
       *
       * <pre>
       * 进行孵化的宠物
       * </pre>
       */
      public Builder clearPetUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petUid_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePetEggFastHatch)
    }

    static {
      defaultInstance = new ResponsePetEggFastHatch(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePetEggFastHatch)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPetEggExp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPetEggExp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestAddPetEggExp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestAddPetEggExp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePetEggExp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePetEggExp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseAllPetEggExp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseAllPetEggExp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePetEggFastHatch_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePetEggFastHatch_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014petegg.proto\022\010protocol\"#\n\020RequestPetEg" +
      "gExp\022\017\n\007pet_uid\030\001 \002(\005\"7\n\023RequestAddPetEg" +
      "gExp\022\017\n\007pet_uid\030\001 \002(\005\022\017\n\007add_exp\030\002 \002(\005\"E" +
      "\n\021ResponsePetEggExp\022\017\n\007pet_uid\030\001 \002(\005\022\016\n\006" +
      "pet_lv\030\002 \002(\005\022\017\n\007pet_exp\030\003 \002(\005\"A\n\024Respons" +
      "eAllPetEggExp\022)\n\004data\030\001 \003(\0132\033.protocol.R" +
      "esponsePetEggExp\")\n\027ResponsePetEggFastHa" +
      "tch\022\016\n\006petUid\030\001 \002(\005B\014B\nPetEggData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestPetEggExp_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestPetEggExp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPetEggExp_descriptor,
              new java.lang.String[] { "PetUid", });
          internal_static_protocol_RequestAddPetEggExp_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestAddPetEggExp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestAddPetEggExp_descriptor,
              new java.lang.String[] { "PetUid", "AddExp", });
          internal_static_protocol_ResponsePetEggExp_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ResponsePetEggExp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePetEggExp_descriptor,
              new java.lang.String[] { "PetUid", "PetLv", "PetExp", });
          internal_static_protocol_ResponseAllPetEggExp_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseAllPetEggExp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseAllPetEggExp_descriptor,
              new java.lang.String[] { "Data", });
          internal_static_protocol_ResponsePetEggFastHatch_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponsePetEggFastHatch_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePetEggFastHatch_descriptor,
              new java.lang.String[] { "PetUid", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
