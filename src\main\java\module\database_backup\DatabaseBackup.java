package module.database_backup;

import manager.MySql;
import org.hibernate.Session;
import org.hibernate.query.NativeQuery;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class DatabaseBackup {
    private static DatabaseBackup inst = null;
    public static DatabaseBackup getInstance() {
        if (inst == null) {
            inst = new DatabaseBackup();
        }
        return inst;
    }

    private DatabaseBackup() {
//        System.err.println("初始化执行-创建数据库备份");
//        try {
//            CreatDatabaseBackup();
//        }catch (Exception e){
//            System.err.println("备份失败");
//            throw e;
//        }
    }

    // 一天的秒数  86400
    int maxTime = 86400;
    int curTime = 0;
    // 每秒执行
    public void PerSecondRun() {
//        curTime++;
//        if (curTime >= maxTime){
//            curTime = 0;
//            System.err.println("计划备份");
//            try {
//                CreatDatabaseBackup();
//            }catch (Exception e){
//                System.err.println("备份失败");
//                throw e;
//            }
//        }
    }

    private String dicName = "resources";
    // 导出
    private void CreatDatabaseBackup(){
        String time = GetDate();

        Session session = MySql.getSession();
        NativeQuery query = session.createNativeQuery("show tables");
        List tables = query.list();

        // List tables = (List) MySql.queryForOne("show tables");
        for (Object name :
                tables) {
            String fileName = name + ".csv";
            String creatBackupSql = String.format(
                    "select * into outfile '%s/%s_%s' from %s",
                    dicName,
                    time,
                    fileName,
                    name);
//            System.out.println(creatBackupSql);
//            MySql.queryForOne(creatBackupSql);
            NativeQuery query2 = session.createNativeQuery(creatBackupSql);

            try {
                List res = query2.list();
            }catch (Exception e){

            }
        }
        session.close();
        System.err.println("备份完成");
    }

    private String GetDate(){
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd_hh!mm!ss");
        return simpleDateFormat.format(date);
    }
}
