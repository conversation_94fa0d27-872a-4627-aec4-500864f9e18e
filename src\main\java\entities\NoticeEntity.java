package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "notice", schema = "", catalog = "super_star_fruit")
public class NoticeEntity {
    private Integer id;
    private Integer nid;
    private String content;
    private String owner;
    private String sender;
    private String title;
    private long timestamp;

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)//主键生成策略
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }


    @Basic
    @Column(name = "content")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Basic
    @Column(name = "owner")
    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    @Basic
    @Column(name = "sender")
    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    @Basic
    @Column(name = "title")
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Basic
    @Column(name = "timestamp")
    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Basic
    @Column(name = "nid")
    public Integer getNid() {
        return nid;
    }

    public void setNid(Integer nid) {
        this.nid = nid;
    }

    @Override
    public String toString() {
        return "NoticeEntity{" +
                "id=" + id +
                ", nid=" + nid +
                ", content='" + content + '\'' +
                ", owner='" + owner + '\'' +
                ", sender='" + sender + '\'' +
                ", title='" + title + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        NoticeEntity entity = (NoticeEntity) o;
        return getTimestamp() == entity.getTimestamp() &&
                Objects.equals(getId(), entity.getId()) &&
                Objects.equals(getNid(), entity.getNid()) &&
                Objects.equals(getContent(), entity.getContent()) &&
                Objects.equals(getOwner(), entity.getOwner()) &&
                Objects.equals(getSender(), entity.getSender()) &&
                Objects.equals(getTitle(), entity.getTitle());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getNid(), getContent(), getOwner(), getSender(), getTitle(), getTimestamp());
    }
}
