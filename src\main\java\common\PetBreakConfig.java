package common;

import model.CommonInfo;

@ExcelConfigObject(key = "levelSetBreak")
public class PetBreakConfig {
    @ExcelColumn(name = "ID")
    private int id;
    @ExcelColumn(name = "level_set_break")
    private int levelLimit;
    @ExcelColumn(name = "break_number")
    private int breakLv;
    @ExcelColumn(name = "break_coin", isCommonInfo = true)
    private CommonInfo costGold;
    @ExcelColumn(name = "break_starcrystal", isCommonInfo = true)
    private CommonInfo costItem;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLevelLimit() {
        return levelLimit;
    }

    public void setLevelLimit(int levelLimit) {
        this.levelLimit = levelLimit;
    }

    public int getBreakLv() {
        return breakLv;
    }

    public void setBreakLv(int breakLv) {
        this.breakLv = breakLv;
    }

    public CommonInfo getCostGold() {
        return costGold;
    }

    public void setCostGold(CommonInfo costGold) {
        this.costGold = costGold;
    }

    public CommonInfo getCostItem() {
        return costItem;
    }

    public void setCostItem(CommonInfo costItem) {
        this.costItem = costItem;
    }

    @Override
    public String toString() {
        return "PetBreakConfig{" +
                "id=" + id +
                ", levelLimit=" + levelLimit +
                ", breakLv=" + breakLv +
                ", costGold=" + costGold +
                ", costItem=" + costItem +
                '}';
    }
}
