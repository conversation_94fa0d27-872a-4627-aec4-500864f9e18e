package module.equipA;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.EquipAEntity;
import manager.MySql;
import manager.ReportManager;
import protocol.EquipAData;
import protocol.ProtoData;

import java.util.*;

public class EquipAService {
    private static EquipAService inst = null;

    public static EquipAService getInstance() {
        if (inst == null) {
            inst = new EquipAService();
        }
        return inst;
    }

    public byte[] RequestEquipA(byte[] bytes, String uid) {
        EquipAData.RequestGetEquipA requestGetEquipA = null;
        EquipAData.ResponseGetEquipA.Builder builder = EquipAData.ResponseGetEquipA.newBuilder();
        if (requestGetEquipA == null) {
            EquipAEntity EquipAEntity = new EquipAEntity();
            EquipAEntity.setUid(uid);
            Random ranDom = new Random();
            int randomNum = ranDom.nextInt(32) + 1;
            if (randomNum == 1) {
                Random random = new Random();
                int ad = random.nextInt(6) + 20;
                EquipAEntity.setEid(1);
                EquipAEntity.setAd(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(1);
                Equia.setAd(ad);

                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 7;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 7;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 7;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 7;
                        EquipAEntity.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 7;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEarm(earm1);
                        Equia.setEmdf(emdf1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 7;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                    }
                    builder.setEquipA(Equia);

                }

            }
            if (randomNum == 2) {
                Random random = new Random();
                int ap = random.nextInt(6) + 20;
                EquipAEntity.setEid(2);
                EquipAEntity.setAp(ap);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(2);
                Equia.setAd(ap);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 7;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 7;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 7;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 7;
                        EquipAEntity.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 7;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEarm(earm1);

                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 7;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                        Equia.setEhp(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 3) {
                Random random = new Random();
                int ad = random.nextInt(6) + 40;
                EquipAEntity.setEid(3);
                EquipAEntity.setAd(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(3);
                Equia.setAd(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 16;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 36;
                        EquipAEntity.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEhp(hp11);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 16;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 16;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 4) {
                Random random = new Random();
                int ad = random.nextInt(6) + 40;
                EquipAEntity.setEid(4);
                EquipAEntity.setAp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(4);
                Equia.setAp(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 16;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 16;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 16;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                        Equia.setEhp(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 5) {
                Random random = new Random();
                int ad = random.nextInt(6) + 70;
                EquipAEntity.setEid(5);
                EquipAEntity.setAd(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(5);
                Equia.setAd(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 30;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 30;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 30;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 16;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 30;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 30;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                        Equia.setEhp(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 6) {
                Random random = new Random();
                int ad = random.nextInt(6) + 70;
                EquipAEntity.setEid(6);
                EquipAEntity.setAp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(6);
                Equia.setAp(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 30;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 30;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 30;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 30;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 30;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(5) + 30;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                        Equia.setEhp(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 7) {
                Random random = new Random();
                int ad = random.nextInt(20) + 100;
                EquipAEntity.setEid(7);
                EquipAEntity.setAd(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(7);
                Equia.setAd(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(20) + 40;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(20) + 40;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(20) + 40;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(20) + 40;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(20) + 40;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(20) + 40;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                        Equia.setEhp(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 8) {
                Random random = new Random();
                int ad = random.nextInt(20) + 100;
                EquipAEntity.setEid(8);
                EquipAEntity.setAp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(8);
                Equia.setAp(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(20) + 40;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(20) + 40;
                        Equia.setEmdf(emdf1);
                        EquipAEntity.setEmdf(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(20) + 40;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(20) + 40;
                        EquipAEntity.setEarm(earm1);
                        Equia.setEarm(earm1);
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(20) + 40;
                        EquipAEntity.setEmdf(emdf1);
                        Equia.setEmdf(emdf1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(20) + 40;
                        EquipAEntity.setEmdf(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEmdf(emdf1);
                        Equia.setEhp(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 9) {
                Random random = new Random();
                int ad = random.nextInt(20) + 80;
                int eam = random.nextInt(10) + 10;
                EquipAEntity.setEarm(eam);
                EquipAEntity.setEid(9);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(9);
                Equia.setHp(ad);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(2) + 6;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(2) + 6;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(2) + 6;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 18;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(2) + 6;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(2) + 6;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(2) + 6;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 10) {
                Random random = new Random();
                int ad = random.nextInt(20) + 80;
                int eam = random.nextInt(10) + 10;
                EquipAEntity.setEmdf(eam);
                EquipAEntity.setEid(10);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(10);
                Equia.setHp(ad);
                Equia.setEmdf(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(2) + 6;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(2) + 6;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(5) + 18;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(2) + 6;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(5) + 18;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(2) + 6;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(2) + 6;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(2) + 6;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 11) {
                Random random = new Random();
                int ad = random.nextInt(40) + 180;
                int eam = random.nextInt(10) + 40;
                EquipAEntity.setEarm(eam);
                EquipAEntity.setEid(11);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(11);
                Equia.setHp(ad);
                Equia.setEarm(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(9) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(8) + 10;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(8) + 10;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(9) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(8) + 10;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(9) + 36;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(8) + 10;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(8) + 10;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(8) + 10;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 12) {
                Random random = new Random();
                int ad = random.nextInt(40) + 180;
                int eam = random.nextInt(10) + 40;
                EquipAEntity.setEmdf(eam);
                EquipAEntity.setEid(12);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(12);
                Equia.setHp(ad);
                Equia.setEmdf(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(9) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(8) + 10;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(8) + 10;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(9) + 36;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(8) + 10;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(9) + 36;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(8) + 10;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(8) + 10;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(8) + 10;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 13) {
                Random random = new Random();
                int ad = random.nextInt(50) + 300;
                int eam = random.nextInt(20) + 80;
                EquipAEntity.setEarm(eam);
                EquipAEntity.setEid(13);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(13);
                Equia.setHp(ad);
                Equia.setEarm(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(16) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(4) + 20;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(4) + 20;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(16) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(4) + 20;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(16) + 62;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(4) + 20;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(4) + 20;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(4) + 20;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 14) {
                Random random = new Random();
                int ad = random.nextInt(50) + 300;
                int eam = random.nextInt(20) + 80;
                EquipAEntity.setEmdf(eam);
                EquipAEntity.setEid(14);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(14);
                Equia.setHp(ad);
                Equia.setEmdf(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(16) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(4) + 20;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(4) + 20;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(16) + 62;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(4) + 20;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(16) + 62;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(4) + 20;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(4) + 20;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(4) + 20;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 15) {
                Random random = new Random();
                int ad = random.nextInt(50) + 500;
                int eam = random.nextInt(30) + 120;
                EquipAEntity.setEarm(eam);
                EquipAEntity.setEid(15);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(15);
                Equia.setHp(ad);
                Equia.setEarm(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(10) + 30;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(10) + 30;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(10) + 30;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(40) + 80;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(10) + 30;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(10) + 30;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(10) + 30;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 16) {
                Random random = new Random();
                int ad = random.nextInt(50) + 500;
                int eam = random.nextInt(30) + 120;
                EquipAEntity.setEmdf(eam);
                EquipAEntity.setEid(16);
                EquipAEntity.setHp(ad);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(16);
                Equia.setHp(ad);
                Equia.setEmdf(eam);
                Random Termp = new Random();//随机属性
                int term = Termp.nextInt(3);
                if (term == 0) {
                } else if (term == 1) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(10) + 30;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(10) + 30;
                        Equia.setEap(emdf1);
                        EquipAEntity.setEap(emdf1);
                    }
                } else if (term == 2) {
                    Random pro = new Random();
                    int prom = pro.nextInt(3);
                    if (prom == 0) {
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(40) + 80;
                        EquipAEntity.setEhp(hp11);
                        Equia.setEhp(hp11);
                        Random earm = new Random();
                        int earm1 = earm.nextInt(10) + 30;
                        EquipAEntity.setEad(earm1);
                        Equia.setEad(earm1);
                    } else if (prom == 1) {
                        Random earm = new Random();
                        int earm1 = earm.nextInt(40) + 80;
                        EquipAEntity.setEhp(earm1);
                        Equia.setEhp(earm1);
                        Random ap = new Random();
                        int ap1 = ap.nextInt(10) + 30;
                        EquipAEntity.setAp(ap1);
                        Equia.setEap(ap1);
                    } else if (prom == 2) {
                        Random Emdf = new Random();
                        int emdf1 = Emdf.nextInt(10) + 30;
                        EquipAEntity.setEad(emdf1);
                        Random hp1 = new Random();
                        int hp11 = hp1.nextInt(10) + 30;
                        EquipAEntity.setEap(hp11);
                        Equia.setEap(emdf1);
                        Equia.setEad(hp11);
                    }
                }
                builder.setEquipA(Equia);
            }
            if (randomNum == 17) {
                Random random = new Random();
                int sp = random.nextInt(5) + 4;
                EquipAEntity.setEid(17);
                EquipAEntity.setSp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(17);

                builder.setEquipA(Equia);
            }
            if (randomNum == 18) {
                Random random = new Random();
                int sp = random.nextInt(4) + 12;
                EquipAEntity.setEid(18);
                EquipAEntity.setSp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(18);
                Equia.setEsp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 19) {
                Random random = new Random();
                int sp = random.nextInt(4) + 20;
                EquipAEntity.setEid(19);
                EquipAEntity.setSp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(19);
                Equia.setEsp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 20) {
                Random random = new Random();
                int sp = random.nextInt(10) + 30;
                EquipAEntity.setEid(20);
                EquipAEntity.setSp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(20);
                Equia.setEsp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 21) {
                Random random = new Random();
                int sp = random.nextInt(2) + 10;
                EquipAEntity.setEid(21);
                EquipAEntity.setAp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(21);
                Equia.setEap(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 22) {
                Random random = new Random();
                int sp = random.nextInt(4) + 20;
                EquipAEntity.setEid(22);
                EquipAEntity.setAp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(22);
                Equia.setEap(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 23) {
                Random random = new Random();
                int sp = random.nextInt(5) + 35;
                EquipAEntity.setEid(23);
                EquipAEntity.setAp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(23);
                Equia.setEap(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 24) {
                Random random = new Random();
                int sp = random.nextInt(5) + 50;
                EquipAEntity.setEid(24);
                EquipAEntity.setAp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(24);
                Equia.setEap(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 25) {
                Random random = new Random();
                int sp = random.nextInt(10) + 40;
                EquipAEntity.setEid(25);
                EquipAEntity.setHp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(25);
                Equia.setEhp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 26) {
                Random random = new Random();
                int sp = random.nextInt(20) + 90;
                EquipAEntity.setEid(26);
                EquipAEntity.setHp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(26);
                Equia.setEhp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 27) {
                Random random = new Random();
                int sp = random.nextInt(30) + 150;
                EquipAEntity.setEid(27);
                EquipAEntity.setHp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(27);
                Equia.setEhp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 28) {
                Random random = new Random();
                int sp = random.nextInt(50) + 250;
                EquipAEntity.setEid(28);
                EquipAEntity.setHp(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(28);
                Equia.setEhp(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 29) {
                Random random = new Random();
                int sp = random.nextInt(2) + 10;
                EquipAEntity.setEid(29);
                EquipAEntity.setAd(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(29);
                Equia.setEad(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 30) {
                Random random = new Random();
                int sp = random.nextInt(4) + 20;
                EquipAEntity.setEid(30);
                EquipAEntity.setAd(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(30);
                Equia.setEad(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 31) {
                Random random = new Random();
                int sp = random.nextInt(5) + 35;
                EquipAEntity.setEid(31);
                EquipAEntity.setAd(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(31);
                Equia.setEad(sp);
                builder.setEquipA(Equia);
            }
            if (randomNum == 32) {
                Random random = new Random();
                int sp = random.nextInt(5) + 50;
                EquipAEntity.setEid(32);
                EquipAEntity.setAd(sp);
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(32);
                Equia.setEad(sp);
                builder.setEquipA(Equia);
            }
            MySql.insert(EquipAEntity);
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestEquipAll(byte[] bytes, String uid) {
        EquipAData.RequestDDDEquip requestDDDEquip = null;
        EquipAData.ResponseDDDEquip.Builder builder = EquipAData.ResponseDDDEquip.newBuilder();
        if (requestDDDEquip == null) {
            List<Object> list = EquipADao.getInstance().queryEquipA(uid);

            for (Object data :
                    list) {
                EquipAEntity item = (EquipAEntity) data;
                EquipAData.EquipA.Builder Equia = EquipAData.EquipA.newBuilder();
                Equia.setEid(item.getEid());
                Equia.setAd(item.getAd());
                Equia.setAp(item.getAp());
                Equia.setArm(item.getArm());
                Equia.setMdf(item.getMdf());
                Equia.setSpeed(item.getSp());
                Equia.setHp(item.getHp());
                Equia.setEad(item.getEad());
                Equia.setEmdf(item.getEmdf());
                Equia.setEsp(item.getEsp());
                Equia.setEhp(item.getEhp());
                Equia.setEap(item.getEap());
                Equia.setEarm(item.getEarm());
                builder.addEquipA(Equia);

            }
        }
        return builder.build().toByteArray();
    }

    public void RequestEquipB(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        EquipAData.RequestEquipF item = EquipAData.RequestEquipF.parseFrom(bytes);

        EquipAEntity Equia = new EquipAEntity();
        Equia.setEid(item.getEid());
        Equia.setAd(item.getAd());
        Equia.setAp(item.getAp());
        Equia.setArm(item.getArm());
        Equia.setMdf(item.getMdf());
        Equia.setSp(item.getSpeed());
        Equia.setHp(item.getHp());
        Equia.setEad(item.getEad());
        Equia.setEmdf(item.getEmdf());
        Equia.setEsp(item.getEsp());
        Equia.setEhp(item.getEhp());
        Equia.setEap(item.getEap());
        Equia.setEarm(item.getEarm());
        Equia.setUid(uid);
        MySql.insert(Equia);


        EquipAData.EquipA.Builder EquiaF = EquipAData.EquipA.newBuilder();
        EquiaF.setEid(item.getEid());
        EquiaF.setAd(item.getAd());
        EquiaF.setAp(item.getAp());
        EquiaF.setArm(item.getArm());
        EquiaF.setMdf(item.getMdf());
        EquiaF.setSpeed(item.getSpeed());
        EquiaF.setHp(item.getHp());
        EquiaF.setEad(item.getEad());
        EquiaF.setEmdf(item.getEmdf());
        EquiaF.setEsp(item.getEsp());
        EquiaF.setEhp(item.getEhp());
        EquiaF.setEap(item.getEap());
        EquiaF.setEarm(item.getEarm());


        EquipAData.ResponseGetEquipA.Builder builder = EquipAData.ResponseGetEquipA.newBuilder();
        builder.setEquipA(EquiaF);
        // 2322
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEGetEquipA_VALUE, builder.build().toByteArray());
    }

    public void GetEquip(String uid, EquipAData.EquipA equip) {
        EquipAEntity Equia = new EquipAEntity();
        Equia.setEid(equip.getEid());
        Equia.setAd(equip.getAd());
        Equia.setAp(equip.getAp());
        Equia.setArm(equip.getArm());
        Equia.setMdf(equip.getMdf());
        Equia.setSp(equip.getSpeed());
        Equia.setHp(equip.getHp());
        Equia.setEad(equip.getEad());
        Equia.setEmdf(equip.getEmdf());
        Equia.setEsp(equip.getEsp());
        Equia.setEhp(equip.getEhp());
        Equia.setEap(equip.getEap());
        Equia.setEarm(equip.getEarm());
        Equia.setUid(uid);
        MySql.insert(Equia);

        EquipAData.EquipA.Builder EquiaF = EquipAData.EquipA.newBuilder();
        EquiaF.setEid(equip.getEid());
        EquiaF.setAd(equip.getAd());
        EquiaF.setAp(equip.getAp());
        EquiaF.setArm(equip.getArm());
        EquiaF.setMdf(equip.getMdf());
        EquiaF.setSpeed(equip.getSpeed());
        EquiaF.setHp(equip.getHp());
        EquiaF.setEad(equip.getEad());
        EquiaF.setEmdf(equip.getEmdf());
        EquiaF.setEsp(equip.getEsp());
        EquiaF.setEhp(equip.getEhp());
        EquiaF.setEap(equip.getEap());
        EquiaF.setEarm(equip.getEarm());


        EquipAData.ResponseGetEquipA.Builder builder = EquipAData.ResponseGetEquipA.newBuilder();
        builder.setEquipA(EquiaF);
        // 2322
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEGetEquipA_VALUE, builder.build().toByteArray());
    }
}
