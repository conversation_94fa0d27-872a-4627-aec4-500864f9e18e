<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.AdLogEntity" table="adlog" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="user_id" column="user_id"/>
        <property name="item_type" column="item_type"/>
        <property name="pre_num" column="pre_num"/>
        <property name="after_num" column="after_num"/>
        <property name="event_id" column="event_id"/>
        <property name="operate_time" column="operate_time"/>

    </class>
</hibernate-mapping>