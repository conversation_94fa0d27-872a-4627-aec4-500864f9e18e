package module.friend;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import common.SuperConfig;
import entities.RelativeshipEntity;
import entities.RoleEntity;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import model.MailInfo;
import model.MessageInfo;
import module.callback.CallBack;
import module.callback.CallBackManager;
import module.callback.CallBackOrder;
import module.login.ILogin;
import module.login.LoginDao;
import net.sf.json.JSONArray;
import protocol.FriendData;
import protocol.ProtoData;
import utils.MyUtils;


/**
 * Created by nara on 2018/5/4.
 */
public class FriendCallBack extends CallBackManager {
    private static Logger log = LoggerFactory.getLogger(FriendCallBack.class);

    public FriendCallBack(int callBackId) {
        super(callBackId);
    }

    public void execute(Object object) {
        switch (callBackId) {
            case CallBackOrder.FINDFRIENDBACK:
                findFriendBack(object);
                break;
            case CallBackOrder.ADDFRIENDBACK:
                addFriendBack(object);
                break;
            case CallBackOrder.SENDCHATTOSTRANGER:
                sendChatToStrangerBack(object);
                break;
            case CallBackOrder.PUBLISHMAILBACK:
                publishMailBack(object);
                break;
            case CallBackOrder.AGREEWITHFRIENFBACK:
                agreeWithFriendBack(object);
                break;
            case CallBackOrder.JUDGEFRIENGVALUEBACK:
                judgeFriendValueBack(object);
                break;
            case CallBackOrder.RELATIVESHIPGETNAMEBACK:
                relativeshipGetNameBack(object);
                break;
            default:
                break;
        }
    }

    public void execute(List<Object> objectList) {
        switch (callBackId) {
            case CallBackOrder.FRIENDRECOMMEND:
                friendRecommendBack(objectList);
                break;
            case CallBackOrder.CLOTHINGRECOMMEND:
                clothingRecommendBack(objectList);
                break;
            case CallBackOrder.RELATIVESHIPVALUEBACK:
                relativeshipValueBack(objectList);
                break;

            default:
                break;
        }
    }

    private void findFriendBack(Object object) {
        String uid = parameterList.get(0).toString();
        int type = Integer.parseInt(parameterList.get(1).toString());
        int id = Integer.parseInt(parameterList.get(2).toString());
        FriendData.ResponseOperateFriend.Builder builder = FriendData.ResponseOperateFriend.newBuilder();
        builder.setId(id);
        builder.setType(type);
        builder.setErrorId(0);
        RoleEntity roleEntity = object == null ? null : (RoleEntity) object;
        if (roleEntity == null) {
            log.error(uid + ":[operateFriend] error 6");
            builder.setErrorId(ProtoData.ErrorCode.NOUSER_VALUE);
        } else {
            String friendUid = roleEntity.getUid();
            if (friendUid.equals(uid)) {
                log.error(uid + ":[operateFriend] error 7 >>> can not be self");
                builder.setErrorId(ProtoData.ErrorCode.ADDDSELFERROR_VALUE);
            } else {
                FriendData.FindPlayer.Builder findBu = FriendData.FindPlayer.newBuilder();
                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
                findBu.setStatus(0);
                playBu.setId(roleEntity.getId());
                playBu.setName(roleEntity.getName());
                playBu.setLv(roleEntity.getLv());
                playBu.setHead(roleEntity.getHead());

                ILogin iLogin = LoginDao.getInstance();
                findBu.setIsOnline(iLogin.getRoleOnlineStatus(friendUid));
                findBu.setPlayer(playBu);
                builder.setPlayer(findBu);
            }
        }

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEOPERATEFRIEND_VALUE, builder.build().toByteArray());
    }

    private void addFriendBack(Object object) {
        String uid = parameterList.get(0).toString();
        int type = Integer.parseInt(parameterList.get(1).toString());
        int id = Integer.parseInt(parameterList.get(2).toString());
        FriendData.ResponseOperateFriend.Builder builder = FriendData.ResponseOperateFriend.newBuilder();
        builder.setId(id);
        builder.setType(type);
        builder.setErrorId(0);
        String friendUid = object == null ? null : object.toString();
        if (friendUid == null) {
            log.error(uid + ":[operateFriend] error 3");
            builder.setErrorId(ProtoData.ErrorCode.NOUSER_VALUE);
        } else {
            if (friendUid.equals(uid)) {
                log.error(uid + ":[operateFriend] error 4 >>> can not be self");
                builder.setErrorId(ProtoData.ErrorCode.ADDDSELFERROR_VALUE);
            } else {
                Redis jedis = Redis.getInstance();
                String roleName = jedis.hget("role:" + uid, "name");
                String roleHead = jedis.hget("role:" + uid, "head");
                String content = uid + "&" + roleName + "|" + roleHead;
                String mid = MyUtils.setRandom();
                IFriend iFriend = FriendDao.getInstance();
                MessageInfo info = iFriend.addNewMessage(friendUid, 1, content, mid, System.currentTimeMillis());
                if (info != null) {
                    iFriend.reportMessage(mid, 1, roleName + "|" + roleHead, friendUid);
                }
            }
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEOPERATEFRIEND_VALUE, builder.build().toByteArray());
    }

    private void sendChatToStrangerBack(Object object) {
        String uid = parameterList.get(0).toString();
        int friendId = Integer.parseInt(parameterList.get(1).toString());
        FriendData.Player player = (FriendData.Player) parameterList.get(2);
        FriendData.Chat chat = (FriendData.Chat) parameterList.get(3);
        String friendUid = object == null ? null : object.toString();
        if (friendUid != null) {
            IFriend iFriend = FriendDao.getInstance();
            iFriend.reportChat(uid, chat, player, friendUid, 0);
            iFriend.reportChat(uid, chat, player, uid, friendId);//自己
        }
    }

    //推送用户信息
    private void publishMailBack(Object object) {
        String key = parameterList.get(0).toString();
        MailInfo mailInfo = (MailInfo) parameterList.get(1);
        byte[] bytes = (byte[]) parameterList.get(2);
        String roleUid = object == null ? null : object.toString();
        if (roleUid != null) {
            IFriend iFriend = FriendDao.getInstance();
            MessageInfo info = iFriend.addNewMessage(roleUid, mailInfo.getMailId(), mailInfo.getContent(), mailInfo.getMid(), mailInfo.getPublishTime());
//            /// System.out.println(info.toString());
            //返回用户信息
            if (info != null) {
                //ReportManager.reportInfo(roleUid, ProtoData.SToC.REPORTMESSAGE_VALUE, bytes);
//                System.out.println("发送邮件22222");
//                ReportManager.reportInfo(roleUid, ProtoData.SToC.REPORTNEWMAIL_VALUE, bytes);

            }
        }
        Redis jedis = Redis.getInstance();
        if(jedis.exists(key))
        {
            jedis.hset(key, "status", "1");
        }
    }

    private void agreeWithFriendBack(Object object) {
        String uid = parameterList.get(0).toString();
        String mid = parameterList.get(1).toString();
        int type = Integer.parseInt(parameterList.get(2).toString());
        String friendUid = parameterList.get(3).toString();
        FriendData.ResponseOperateMessage.Builder builder = FriendData.ResponseOperateMessage.newBuilder();
        builder.setMid(mid);
        builder.setType(type);
        builder.setErrorId(0);
        RoleEntity roleEntity = object == null ? null : (RoleEntity) object;
        if (roleEntity == null) {
            log.error(uid + ":[operateMessage] error 2 >>>friendUid:" + friendUid);
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
        } else {
            IFriend iFriend = FriendDao.getInstance();
            FriendData.Friend.Builder friendBu = iFriend.agreeWithFriend(roleEntity, false, uid, mid);
            builder.setFriend(friendBu);
        }

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEOPERATEMESSAGE_VALUE, builder.build().toByteArray());
    }

    private void judgeFriendValueBack(Object object) {
        String uid = parameterList.get(0).toString();
        String friendUid = parameterList.get(1).toString();
        int totalVal = Integer.parseInt(parameterList.get(2).toString());
        if (object != null) {
            String sql = "update RelativeshipEntity set value = " + totalVal + " where roleuid1='" + uid + "' and roleuid2='" + friendUid + "'";
            MySql.updateSomes(sql);
        } else {
            String sql = "update RelativeshipEntity set value = " + totalVal + " where roleuid2='" + uid + "' and roleuid1='" + friendUid + "'";
            MySql.updateSomes(sql);
        }
    }

    private void friendRecommendBack(List<Object> objectList) {
        FriendData.ResponseRecommendFriend.Builder builder = FriendData.ResponseRecommendFriend.newBuilder();
        builder.setErrorId(0);
        int id = Integer.parseInt(this.parameterList.get(3).toString());
        int type = Integer.parseInt(this.parameterList.get(2).toString());
        builder.setType(type);
        String uid = this.parameterList.get(0).toString();
        IFriend iFriend = FriendDao.getInstance();
        List dumpList = new ArrayList<String>();
        if (objectList.size() <= 10) {
            int other = 10;
            for (int i = 0; i < objectList.size(); i++) {
                RoleEntity roleEntity = (RoleEntity) objectList.get(i);
                if (id == roleEntity.getId()) {
                    continue;
                }
                if (iFriend.getFriendInfoById(uid, roleEntity.getId()) != null) {  ///~~~~~~~~~~~~~~
                    continue;
                }
                FriendData.Recommend.Builder recBu = FriendData.Recommend.newBuilder();
                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
                playBu.setId(roleEntity.getId());
                playBu.setName(roleEntity.getName());
                playBu.setLv(roleEntity.getLv());
                playBu.setHead(roleEntity.getHead());
                recBu.setPlayer(playBu);
                builder.addPlayerList(recBu);
                dumpList.add(roleEntity.getUid());
                other--;
            }
            if (other > 0) {
                int server = Integer.parseInt(this.parameterList.get(1).toString());
                List<FriendData.Recommend> list1 = iFriend.getOnlineRecommend(server, other, type, dumpList);
                for (int i = 0; list1 != null && i < list1.size(); i++) {
                    int tmpId = list1.get(i).getPlayer().getId();
                    if (id == tmpId) {
                        continue;
                    }
                    if (iFriend.getFriendInfoById(uid, tmpId) != null) {
                        continue;
                    }
                    builder.addPlayerList(list1.get(i));
                }
            }
        } else {
            for (int i = 0; i < 10; i++) {
                RoleEntity roleEntity = (RoleEntity) objectList.get(i);
                if (id == roleEntity.getId()) {
                    continue;
                }
                if (iFriend.getFriendInfoById(uid, roleEntity.getId()) != null) {
                    continue;
                }
                FriendData.Recommend.Builder recBu = FriendData.Recommend.newBuilder();
                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
                playBu.setId(roleEntity.getId());
                playBu.setName(roleEntity.getName());
                playBu.setLv(roleEntity.getLv());
                playBu.setHead(roleEntity.getHead());

                recBu.setPlayer(playBu);
                builder.addPlayerList(recBu);
            }
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSERECOMMENDFRIEND_VALUE, builder.build().toByteArray());
    }

    private void clothingRecommendBack(List<Object> objectList) {
        FriendData.ResponseRecommendFriend.Builder builder = FriendData.ResponseRecommendFriend.newBuilder();
        builder.setErrorId(0);
        int id = Integer.parseInt(this.parameterList.get(3).toString());
        int type = Integer.parseInt(this.parameterList.get(2).toString());
        builder.setType(type);
        String uid = this.parameterList.get(0).toString();
        IFriend iFriend = FriendDao.getInstance();
        List dumpLsit = new ArrayList<String>();//防止重复添加进推介列表
        if (objectList.size() <= 10) {
            int other = 10;
            for (int i = 0; i < objectList.size(); i++) {
                JSONArray jsonArray = JSONArray.fromObject(objectList.get(i));
                if (id == jsonArray.getInt(0)) {
                    continue;
                }
                if (iFriend.getFriendInfoById(uid, jsonArray.getInt(0)) != null) {
                    continue;
                }
                FriendData.Recommend.Builder recBu = FriendData.Recommend.newBuilder();
                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
                playBu.setId(jsonArray.getInt(0));
                playBu.setName(jsonArray.getString(1));
                playBu.setLv(jsonArray.getInt(2));
                playBu.setHead(jsonArray.getInt(3));
//                playBu.setStation(0);
                recBu.setPlayer(playBu);
                recBu.setValue(jsonArray.getInt(4));
                builder.addPlayerList(recBu);
                dumpLsit.add(uid);
                other--;
            }

            if (other > 0) {
                int server = Integer.parseInt(this.parameterList.get(1).toString());
                List<FriendData.Recommend> list1 = iFriend.getOnlineRecommend(server, other, type, dumpLsit);
                for (int i = 0; list1 != null && i < list1.size(); i++) {
                    int tmpId = list1.get(i).getPlayer().getId();
                    if (id == tmpId) {
                        continue;
                    }
                    if (iFriend.getFriendInfoById(uid, tmpId) != null) {
                        continue;
                    }
                    builder.addPlayerList(list1.get(i));
                }
            }
        } else {
            for (int i = 0; i < 10 && objectList.size() > 0; i++) {
                int rand = (int) (1 + Math.random() * (objectList.size() - 1 + 1));
                Object object = objectList.remove(rand - 1);
                JSONArray jsonArray = JSONArray.fromObject(object);
                if (id == jsonArray.getInt(0)) {
                    i--;
                    continue;
                }
                if (iFriend.getFriendInfoById(uid, jsonArray.getInt(0)) != null) {
                    i--;
                    continue;
                }
                FriendData.Recommend.Builder recBu = FriendData.Recommend.newBuilder();
                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
                playBu.setId(jsonArray.getInt(0));
                playBu.setName(jsonArray.getString(1));
                playBu.setLv(jsonArray.getInt(2));
                playBu.setHead(jsonArray.getInt(3));

//                playBu.setStation(0);
                recBu.setPlayer(playBu);
                recBu.setValue(jsonArray.getInt(4));
                builder.addPlayerList(recBu);
            }
        }
       /* int[] blackArray=new int[builder.getPlayerListCount()];
        for(int i=0;i<builder.getPlayerListCount();i++){
            FriendData.Recommend  playerInfo=builder.getPlayerList(i);
            blackArray[i]=playerInfo.getPlayer().getId();
        }*/ //记录recommand list里已有玩家的id，防止重复（前面用了list做了黑名单机制）
        if (builder.getPlayerListCount() < 5) {    //保证至少推荐五个玩家
            String sql = "from RoleEntity ";
            List<Object> roleList = MySql.queryForList(sql, 100);
            int listSize = roleList.size();
            int index;
            int loopnums = builder.getPlayerListCount();
            for (int i = 0; i < 5 - loopnums; i++) {
                index = (int) (Math.random() * listSize);
                RoleEntity roleEntity = (RoleEntity) roleList.get(index);
                if (dumpLsit.contains(roleEntity.getUid())) {
                    i--;
                    continue;
                }
                FriendData.Recommend.Builder recBu = FriendData.Recommend.newBuilder();
                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
                playBu.setId(roleEntity.getId());
                playBu.setName(roleEntity.getName());
                playBu.setLv(roleEntity.getLv());
                playBu.setHead(roleEntity.getHead());
//                playBu.setStation(0);
                recBu.setPlayer(playBu);
                builder.addPlayerList(recBu);
            }
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSERECOMMENDFRIEND_VALUE, builder.build().toByteArray());
    }

    private void relativeshipValueBack(List<Object> objectList) {
        //设置周日6点发放缘分值奖励
        for (int i = 0; i < objectList.size(); i++) {
            RelativeshipEntity relativeshipEntity = (RelativeshipEntity) objectList.get(i);
            String uid1 = relativeshipEntity.getRoleuid1();
            String uid2 = relativeshipEntity.getRoleuid2();
            String hql = "select name from RoleEntity where uid = '" + uid2 + "'";
            CallBack callBack = new FriendCallBack(CallBackOrder.RELATIVESHIPGETNAMEBACK);
            callBack.addParameter(uid1);
            MySql.queryInSql(hql, callBack, SuperConfig.DB_QUERYFORONELCALLBACK);

            String hql2 = "select name from RoleEntity where uid = '" + uid1 + "'";
            CallBack callBack2 = new FriendCallBack(CallBackOrder.RELATIVESHIPGETNAMEBACK);
            callBack2.addParameter(uid2);
            MySql.queryInSql(hql2, callBack2, SuperConfig.DB_QUERYFORONELCALLBACK);
        }
    }

    private void relativeshipGetNameBack(Object object) {

    }
}
