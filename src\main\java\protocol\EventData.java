// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: event.proto

package protocol;

public final class EventData {
  private EventData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestGetEventInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetEventInfo}
   */
  public static final class RequestGetEventInfo extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetEventInfoOrBuilder {
    // Use RequestGetEventInfo.newBuilder() to construct.
    private RequestGetEventInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetEventInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetEventInfo defaultInstance;
    public static RequestGetEventInfo getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetEventInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetEventInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EventData.internal_static_protocol_RequestGetEventInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EventData.internal_static_protocol_RequestGetEventInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EventData.RequestGetEventInfo.class, protocol.EventData.RequestGetEventInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetEventInfo> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetEventInfo>() {
      public RequestGetEventInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetEventInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetEventInfo> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EventData.RequestGetEventInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EventData.RequestGetEventInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EventData.RequestGetEventInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.RequestGetEventInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EventData.RequestGetEventInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetEventInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EventData.RequestGetEventInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EventData.internal_static_protocol_RequestGetEventInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EventData.internal_static_protocol_RequestGetEventInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EventData.RequestGetEventInfo.class, protocol.EventData.RequestGetEventInfo.Builder.class);
      }

      // Construct using protocol.EventData.RequestGetEventInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EventData.internal_static_protocol_RequestGetEventInfo_descriptor;
      }

      public protocol.EventData.RequestGetEventInfo getDefaultInstanceForType() {
        return protocol.EventData.RequestGetEventInfo.getDefaultInstance();
      }

      public protocol.EventData.RequestGetEventInfo build() {
        protocol.EventData.RequestGetEventInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EventData.RequestGetEventInfo buildPartial() {
        protocol.EventData.RequestGetEventInfo result = new protocol.EventData.RequestGetEventInfo(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EventData.RequestGetEventInfo) {
          return mergeFrom((protocol.EventData.RequestGetEventInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EventData.RequestGetEventInfo other) {
        if (other == protocol.EventData.RequestGetEventInfo.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EventData.RequestGetEventInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EventData.RequestGetEventInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetEventInfo)
    }

    static {
      defaultInstance = new RequestGetEventInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetEventInfo)
  }

  public interface ResponseGetEventInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.EventInfo events = 1;
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    java.util.List<protocol.EventData.EventInfo> 
        getEventsList();
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    protocol.EventData.EventInfo getEvents(int index);
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    int getEventsCount();
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    java.util.List<? extends protocol.EventData.EventInfoOrBuilder> 
        getEventsOrBuilderList();
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    protocol.EventData.EventInfoOrBuilder getEventsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseGetEventInfo}
   */
  public static final class ResponseGetEventInfo extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetEventInfoOrBuilder {
    // Use ResponseGetEventInfo.newBuilder() to construct.
    private ResponseGetEventInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetEventInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetEventInfo defaultInstance;
    public static ResponseGetEventInfo getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetEventInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetEventInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                events_ = new java.util.ArrayList<protocol.EventData.EventInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              events_.add(input.readMessage(protocol.EventData.EventInfo.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          events_ = java.util.Collections.unmodifiableList(events_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EventData.internal_static_protocol_ResponseGetEventInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EventData.internal_static_protocol_ResponseGetEventInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EventData.ResponseGetEventInfo.class, protocol.EventData.ResponseGetEventInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetEventInfo> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetEventInfo>() {
      public ResponseGetEventInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetEventInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetEventInfo> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.EventInfo events = 1;
    public static final int EVENTS_FIELD_NUMBER = 1;
    private java.util.List<protocol.EventData.EventInfo> events_;
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    public java.util.List<protocol.EventData.EventInfo> getEventsList() {
      return events_;
    }
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    public java.util.List<? extends protocol.EventData.EventInfoOrBuilder> 
        getEventsOrBuilderList() {
      return events_;
    }
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    public int getEventsCount() {
      return events_.size();
    }
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    public protocol.EventData.EventInfo getEvents(int index) {
      return events_.get(index);
    }
    /**
     * <code>repeated .protocol.EventInfo events = 1;</code>
     *
     * <pre>
     *事件
     * </pre>
     */
    public protocol.EventData.EventInfoOrBuilder getEventsOrBuilder(
        int index) {
      return events_.get(index);
    }

    private void initFields() {
      events_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getEventsCount(); i++) {
        if (!getEvents(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < events_.size(); i++) {
        output.writeMessage(1, events_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < events_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, events_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EventData.ResponseGetEventInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EventData.ResponseGetEventInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EventData.ResponseGetEventInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.ResponseGetEventInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EventData.ResponseGetEventInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetEventInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EventData.ResponseGetEventInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EventData.internal_static_protocol_ResponseGetEventInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EventData.internal_static_protocol_ResponseGetEventInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EventData.ResponseGetEventInfo.class, protocol.EventData.ResponseGetEventInfo.Builder.class);
      }

      // Construct using protocol.EventData.ResponseGetEventInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEventsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (eventsBuilder_ == null) {
          events_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          eventsBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EventData.internal_static_protocol_ResponseGetEventInfo_descriptor;
      }

      public protocol.EventData.ResponseGetEventInfo getDefaultInstanceForType() {
        return protocol.EventData.ResponseGetEventInfo.getDefaultInstance();
      }

      public protocol.EventData.ResponseGetEventInfo build() {
        protocol.EventData.ResponseGetEventInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EventData.ResponseGetEventInfo buildPartial() {
        protocol.EventData.ResponseGetEventInfo result = new protocol.EventData.ResponseGetEventInfo(this);
        int from_bitField0_ = bitField0_;
        if (eventsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            events_ = java.util.Collections.unmodifiableList(events_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.events_ = events_;
        } else {
          result.events_ = eventsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EventData.ResponseGetEventInfo) {
          return mergeFrom((protocol.EventData.ResponseGetEventInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EventData.ResponseGetEventInfo other) {
        if (other == protocol.EventData.ResponseGetEventInfo.getDefaultInstance()) return this;
        if (eventsBuilder_ == null) {
          if (!other.events_.isEmpty()) {
            if (events_.isEmpty()) {
              events_ = other.events_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureEventsIsMutable();
              events_.addAll(other.events_);
            }
            onChanged();
          }
        } else {
          if (!other.events_.isEmpty()) {
            if (eventsBuilder_.isEmpty()) {
              eventsBuilder_.dispose();
              eventsBuilder_ = null;
              events_ = other.events_;
              bitField0_ = (bitField0_ & ~0x00000001);
              eventsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getEventsFieldBuilder() : null;
            } else {
              eventsBuilder_.addAllMessages(other.events_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getEventsCount(); i++) {
          if (!getEvents(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EventData.ResponseGetEventInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EventData.ResponseGetEventInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.EventInfo events = 1;
      private java.util.List<protocol.EventData.EventInfo> events_ =
        java.util.Collections.emptyList();
      private void ensureEventsIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          events_ = new java.util.ArrayList<protocol.EventData.EventInfo>(events_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.EventData.EventInfo, protocol.EventData.EventInfo.Builder, protocol.EventData.EventInfoOrBuilder> eventsBuilder_;

      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public java.util.List<protocol.EventData.EventInfo> getEventsList() {
        if (eventsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(events_);
        } else {
          return eventsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public int getEventsCount() {
        if (eventsBuilder_ == null) {
          return events_.size();
        } else {
          return eventsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public protocol.EventData.EventInfo getEvents(int index) {
        if (eventsBuilder_ == null) {
          return events_.get(index);
        } else {
          return eventsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder setEvents(
          int index, protocol.EventData.EventInfo value) {
        if (eventsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEventsIsMutable();
          events_.set(index, value);
          onChanged();
        } else {
          eventsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder setEvents(
          int index, protocol.EventData.EventInfo.Builder builderForValue) {
        if (eventsBuilder_ == null) {
          ensureEventsIsMutable();
          events_.set(index, builderForValue.build());
          onChanged();
        } else {
          eventsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder addEvents(protocol.EventData.EventInfo value) {
        if (eventsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEventsIsMutable();
          events_.add(value);
          onChanged();
        } else {
          eventsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder addEvents(
          int index, protocol.EventData.EventInfo value) {
        if (eventsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEventsIsMutable();
          events_.add(index, value);
          onChanged();
        } else {
          eventsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder addEvents(
          protocol.EventData.EventInfo.Builder builderForValue) {
        if (eventsBuilder_ == null) {
          ensureEventsIsMutable();
          events_.add(builderForValue.build());
          onChanged();
        } else {
          eventsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder addEvents(
          int index, protocol.EventData.EventInfo.Builder builderForValue) {
        if (eventsBuilder_ == null) {
          ensureEventsIsMutable();
          events_.add(index, builderForValue.build());
          onChanged();
        } else {
          eventsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder addAllEvents(
          java.lang.Iterable<? extends protocol.EventData.EventInfo> values) {
        if (eventsBuilder_ == null) {
          ensureEventsIsMutable();
          super.addAll(values, events_);
          onChanged();
        } else {
          eventsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder clearEvents() {
        if (eventsBuilder_ == null) {
          events_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          eventsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public Builder removeEvents(int index) {
        if (eventsBuilder_ == null) {
          ensureEventsIsMutable();
          events_.remove(index);
          onChanged();
        } else {
          eventsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public protocol.EventData.EventInfo.Builder getEventsBuilder(
          int index) {
        return getEventsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public protocol.EventData.EventInfoOrBuilder getEventsOrBuilder(
          int index) {
        if (eventsBuilder_ == null) {
          return events_.get(index);  } else {
          return eventsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public java.util.List<? extends protocol.EventData.EventInfoOrBuilder> 
           getEventsOrBuilderList() {
        if (eventsBuilder_ != null) {
          return eventsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(events_);
        }
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public protocol.EventData.EventInfo.Builder addEventsBuilder() {
        return getEventsFieldBuilder().addBuilder(
            protocol.EventData.EventInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public protocol.EventData.EventInfo.Builder addEventsBuilder(
          int index) {
        return getEventsFieldBuilder().addBuilder(
            index, protocol.EventData.EventInfo.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.EventInfo events = 1;</code>
       *
       * <pre>
       *事件
       * </pre>
       */
      public java.util.List<protocol.EventData.EventInfo.Builder> 
           getEventsBuilderList() {
        return getEventsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.EventData.EventInfo, protocol.EventData.EventInfo.Builder, protocol.EventData.EventInfoOrBuilder> 
          getEventsFieldBuilder() {
        if (eventsBuilder_ == null) {
          eventsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.EventData.EventInfo, protocol.EventData.EventInfo.Builder, protocol.EventData.EventInfoOrBuilder>(
                  events_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          events_ = null;
        }
        return eventsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetEventInfo)
    }

    static {
      defaultInstance = new ResponseGetEventInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetEventInfo)
  }

  public interface EventInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 eventType = 1;
    /**
     * <code>required int32 eventType = 1;</code>
     *
     * <pre>
     *事件類型
     * </pre>
     */
    boolean hasEventType();
    /**
     * <code>required int32 eventType = 1;</code>
     *
     * <pre>
     *事件類型
     * </pre>
     */
    int getEventType();

    // required int32 mapId = 2;
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *場景Id
     * </pre>
     */
    boolean hasMapId();
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *場景Id
     * </pre>
     */
    int getMapId();

    // required bool isFinished = 3;
    /**
     * <code>required bool isFinished = 3;</code>
     *
     * <pre>
     *是否完成
     * </pre>
     */
    boolean hasIsFinished();
    /**
     * <code>required bool isFinished = 3;</code>
     *
     * <pre>
     *是否完成
     * </pre>
     */
    boolean getIsFinished();

    // optional int32 countdown = 4;
    /**
     * <code>optional int32 countdown = 4;</code>
     *
     * <pre>
     *事件刷新倒計時
     * </pre>
     */
    boolean hasCountdown();
    /**
     * <code>optional int32 countdown = 4;</code>
     *
     * <pre>
     *事件刷新倒計時
     * </pre>
     */
    int getCountdown();

    // optional int32 eventAdvance = 5;
    /**
     * <code>optional int32 eventAdvance = 5;</code>
     *
     * <pre>
     *事件完成進度
     * </pre>
     */
    boolean hasEventAdvance();
    /**
     * <code>optional int32 eventAdvance = 5;</code>
     *
     * <pre>
     *事件完成進度
     * </pre>
     */
    int getEventAdvance();
  }
  /**
   * Protobuf type {@code protocol.EventInfo}
   */
  public static final class EventInfo extends
      com.google.protobuf.GeneratedMessage
      implements EventInfoOrBuilder {
    // Use EventInfo.newBuilder() to construct.
    private EventInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private EventInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final EventInfo defaultInstance;
    public static EventInfo getDefaultInstance() {
      return defaultInstance;
    }

    public EventInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private EventInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              eventType_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mapId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              isFinished_ = input.readBool();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              countdown_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              eventAdvance_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EventData.internal_static_protocol_EventInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EventData.internal_static_protocol_EventInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EventData.EventInfo.class, protocol.EventData.EventInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<EventInfo> PARSER =
        new com.google.protobuf.AbstractParser<EventInfo>() {
      public EventInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EventInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<EventInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 eventType = 1;
    public static final int EVENTTYPE_FIELD_NUMBER = 1;
    private int eventType_;
    /**
     * <code>required int32 eventType = 1;</code>
     *
     * <pre>
     *事件類型
     * </pre>
     */
    public boolean hasEventType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 eventType = 1;</code>
     *
     * <pre>
     *事件類型
     * </pre>
     */
    public int getEventType() {
      return eventType_;
    }

    // required int32 mapId = 2;
    public static final int MAPID_FIELD_NUMBER = 2;
    private int mapId_;
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *場景Id
     * </pre>
     */
    public boolean hasMapId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *場景Id
     * </pre>
     */
    public int getMapId() {
      return mapId_;
    }

    // required bool isFinished = 3;
    public static final int ISFINISHED_FIELD_NUMBER = 3;
    private boolean isFinished_;
    /**
     * <code>required bool isFinished = 3;</code>
     *
     * <pre>
     *是否完成
     * </pre>
     */
    public boolean hasIsFinished() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required bool isFinished = 3;</code>
     *
     * <pre>
     *是否完成
     * </pre>
     */
    public boolean getIsFinished() {
      return isFinished_;
    }

    // optional int32 countdown = 4;
    public static final int COUNTDOWN_FIELD_NUMBER = 4;
    private int countdown_;
    /**
     * <code>optional int32 countdown = 4;</code>
     *
     * <pre>
     *事件刷新倒計時
     * </pre>
     */
    public boolean hasCountdown() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 countdown = 4;</code>
     *
     * <pre>
     *事件刷新倒計時
     * </pre>
     */
    public int getCountdown() {
      return countdown_;
    }

    // optional int32 eventAdvance = 5;
    public static final int EVENTADVANCE_FIELD_NUMBER = 5;
    private int eventAdvance_;
    /**
     * <code>optional int32 eventAdvance = 5;</code>
     *
     * <pre>
     *事件完成進度
     * </pre>
     */
    public boolean hasEventAdvance() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 eventAdvance = 5;</code>
     *
     * <pre>
     *事件完成進度
     * </pre>
     */
    public int getEventAdvance() {
      return eventAdvance_;
    }

    private void initFields() {
      eventType_ = 0;
      mapId_ = 0;
      isFinished_ = false;
      countdown_ = 0;
      eventAdvance_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEventType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMapId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIsFinished()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, eventType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, mapId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBool(3, isFinished_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, countdown_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, eventAdvance_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, eventType_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mapId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isFinished_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, countdown_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, eventAdvance_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EventData.EventInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.EventInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.EventInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.EventInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.EventInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.EventInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EventData.EventInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EventData.EventInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EventData.EventInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.EventInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EventData.EventInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.EventInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EventData.EventInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EventData.internal_static_protocol_EventInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EventData.internal_static_protocol_EventInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EventData.EventInfo.class, protocol.EventData.EventInfo.Builder.class);
      }

      // Construct using protocol.EventData.EventInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        eventType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        mapId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        isFinished_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        countdown_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        eventAdvance_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EventData.internal_static_protocol_EventInfo_descriptor;
      }

      public protocol.EventData.EventInfo getDefaultInstanceForType() {
        return protocol.EventData.EventInfo.getDefaultInstance();
      }

      public protocol.EventData.EventInfo build() {
        protocol.EventData.EventInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EventData.EventInfo buildPartial() {
        protocol.EventData.EventInfo result = new protocol.EventData.EventInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.eventType_ = eventType_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.mapId_ = mapId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.isFinished_ = isFinished_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.countdown_ = countdown_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.eventAdvance_ = eventAdvance_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EventData.EventInfo) {
          return mergeFrom((protocol.EventData.EventInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EventData.EventInfo other) {
        if (other == protocol.EventData.EventInfo.getDefaultInstance()) return this;
        if (other.hasEventType()) {
          setEventType(other.getEventType());
        }
        if (other.hasMapId()) {
          setMapId(other.getMapId());
        }
        if (other.hasIsFinished()) {
          setIsFinished(other.getIsFinished());
        }
        if (other.hasCountdown()) {
          setCountdown(other.getCountdown());
        }
        if (other.hasEventAdvance()) {
          setEventAdvance(other.getEventAdvance());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEventType()) {
          
          return false;
        }
        if (!hasMapId()) {
          
          return false;
        }
        if (!hasIsFinished()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EventData.EventInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EventData.EventInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 eventType = 1;
      private int eventType_ ;
      /**
       * <code>required int32 eventType = 1;</code>
       *
       * <pre>
       *事件類型
       * </pre>
       */
      public boolean hasEventType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 eventType = 1;</code>
       *
       * <pre>
       *事件類型
       * </pre>
       */
      public int getEventType() {
        return eventType_;
      }
      /**
       * <code>required int32 eventType = 1;</code>
       *
       * <pre>
       *事件類型
       * </pre>
       */
      public Builder setEventType(int value) {
        bitField0_ |= 0x00000001;
        eventType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 eventType = 1;</code>
       *
       * <pre>
       *事件類型
       * </pre>
       */
      public Builder clearEventType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        eventType_ = 0;
        onChanged();
        return this;
      }

      // required int32 mapId = 2;
      private int mapId_ ;
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *場景Id
       * </pre>
       */
      public boolean hasMapId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *場景Id
       * </pre>
       */
      public int getMapId() {
        return mapId_;
      }
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *場景Id
       * </pre>
       */
      public Builder setMapId(int value) {
        bitField0_ |= 0x00000002;
        mapId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *場景Id
       * </pre>
       */
      public Builder clearMapId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mapId_ = 0;
        onChanged();
        return this;
      }

      // required bool isFinished = 3;
      private boolean isFinished_ ;
      /**
       * <code>required bool isFinished = 3;</code>
       *
       * <pre>
       *是否完成
       * </pre>
       */
      public boolean hasIsFinished() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required bool isFinished = 3;</code>
       *
       * <pre>
       *是否完成
       * </pre>
       */
      public boolean getIsFinished() {
        return isFinished_;
      }
      /**
       * <code>required bool isFinished = 3;</code>
       *
       * <pre>
       *是否完成
       * </pre>
       */
      public Builder setIsFinished(boolean value) {
        bitField0_ |= 0x00000004;
        isFinished_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool isFinished = 3;</code>
       *
       * <pre>
       *是否完成
       * </pre>
       */
      public Builder clearIsFinished() {
        bitField0_ = (bitField0_ & ~0x00000004);
        isFinished_ = false;
        onChanged();
        return this;
      }

      // optional int32 countdown = 4;
      private int countdown_ ;
      /**
       * <code>optional int32 countdown = 4;</code>
       *
       * <pre>
       *事件刷新倒計時
       * </pre>
       */
      public boolean hasCountdown() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 countdown = 4;</code>
       *
       * <pre>
       *事件刷新倒計時
       * </pre>
       */
      public int getCountdown() {
        return countdown_;
      }
      /**
       * <code>optional int32 countdown = 4;</code>
       *
       * <pre>
       *事件刷新倒計時
       * </pre>
       */
      public Builder setCountdown(int value) {
        bitField0_ |= 0x00000008;
        countdown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 countdown = 4;</code>
       *
       * <pre>
       *事件刷新倒計時
       * </pre>
       */
      public Builder clearCountdown() {
        bitField0_ = (bitField0_ & ~0x00000008);
        countdown_ = 0;
        onChanged();
        return this;
      }

      // optional int32 eventAdvance = 5;
      private int eventAdvance_ ;
      /**
       * <code>optional int32 eventAdvance = 5;</code>
       *
       * <pre>
       *事件完成進度
       * </pre>
       */
      public boolean hasEventAdvance() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 eventAdvance = 5;</code>
       *
       * <pre>
       *事件完成進度
       * </pre>
       */
      public int getEventAdvance() {
        return eventAdvance_;
      }
      /**
       * <code>optional int32 eventAdvance = 5;</code>
       *
       * <pre>
       *事件完成進度
       * </pre>
       */
      public Builder setEventAdvance(int value) {
        bitField0_ |= 0x00000010;
        eventAdvance_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 eventAdvance = 5;</code>
       *
       * <pre>
       *事件完成進度
       * </pre>
       */
      public Builder clearEventAdvance() {
        bitField0_ = (bitField0_ & ~0x00000010);
        eventAdvance_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.EventInfo)
    }

    static {
      defaultInstance = new EventInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.EventInfo)
  }

  public interface RequestFlushEventOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 eventId = 1;
    /**
     * <code>required int32 eventId = 1;</code>
     *
     * <pre>
     *刷新的事件Id
     * </pre>
     */
    boolean hasEventId();
    /**
     * <code>required int32 eventId = 1;</code>
     *
     * <pre>
     *刷新的事件Id
     * </pre>
     */
    int getEventId();
  }
  /**
   * Protobuf type {@code protocol.RequestFlushEvent}
   */
  public static final class RequestFlushEvent extends
      com.google.protobuf.GeneratedMessage
      implements RequestFlushEventOrBuilder {
    // Use RequestFlushEvent.newBuilder() to construct.
    private RequestFlushEvent(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestFlushEvent(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestFlushEvent defaultInstance;
    public static RequestFlushEvent getDefaultInstance() {
      return defaultInstance;
    }

    public RequestFlushEvent getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestFlushEvent(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              eventId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EventData.internal_static_protocol_RequestFlushEvent_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EventData.internal_static_protocol_RequestFlushEvent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EventData.RequestFlushEvent.class, protocol.EventData.RequestFlushEvent.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestFlushEvent> PARSER =
        new com.google.protobuf.AbstractParser<RequestFlushEvent>() {
      public RequestFlushEvent parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestFlushEvent(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestFlushEvent> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 eventId = 1;
    public static final int EVENTID_FIELD_NUMBER = 1;
    private int eventId_;
    /**
     * <code>required int32 eventId = 1;</code>
     *
     * <pre>
     *刷新的事件Id
     * </pre>
     */
    public boolean hasEventId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 eventId = 1;</code>
     *
     * <pre>
     *刷新的事件Id
     * </pre>
     */
    public int getEventId() {
      return eventId_;
    }

    private void initFields() {
      eventId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEventId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, eventId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, eventId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EventData.RequestFlushEvent parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EventData.RequestFlushEvent parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EventData.RequestFlushEvent parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.RequestFlushEvent parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EventData.RequestFlushEvent prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestFlushEvent}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EventData.RequestFlushEventOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EventData.internal_static_protocol_RequestFlushEvent_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EventData.internal_static_protocol_RequestFlushEvent_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EventData.RequestFlushEvent.class, protocol.EventData.RequestFlushEvent.Builder.class);
      }

      // Construct using protocol.EventData.RequestFlushEvent.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        eventId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EventData.internal_static_protocol_RequestFlushEvent_descriptor;
      }

      public protocol.EventData.RequestFlushEvent getDefaultInstanceForType() {
        return protocol.EventData.RequestFlushEvent.getDefaultInstance();
      }

      public protocol.EventData.RequestFlushEvent build() {
        protocol.EventData.RequestFlushEvent result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EventData.RequestFlushEvent buildPartial() {
        protocol.EventData.RequestFlushEvent result = new protocol.EventData.RequestFlushEvent(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.eventId_ = eventId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EventData.RequestFlushEvent) {
          return mergeFrom((protocol.EventData.RequestFlushEvent)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EventData.RequestFlushEvent other) {
        if (other == protocol.EventData.RequestFlushEvent.getDefaultInstance()) return this;
        if (other.hasEventId()) {
          setEventId(other.getEventId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEventId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EventData.RequestFlushEvent parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EventData.RequestFlushEvent) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 eventId = 1;
      private int eventId_ ;
      /**
       * <code>required int32 eventId = 1;</code>
       *
       * <pre>
       *刷新的事件Id
       * </pre>
       */
      public boolean hasEventId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 eventId = 1;</code>
       *
       * <pre>
       *刷新的事件Id
       * </pre>
       */
      public int getEventId() {
        return eventId_;
      }
      /**
       * <code>required int32 eventId = 1;</code>
       *
       * <pre>
       *刷新的事件Id
       * </pre>
       */
      public Builder setEventId(int value) {
        bitField0_ |= 0x00000001;
        eventId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 eventId = 1;</code>
       *
       * <pre>
       *刷新的事件Id
       * </pre>
       */
      public Builder clearEventId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        eventId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestFlushEvent)
    }

    static {
      defaultInstance = new RequestFlushEvent(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestFlushEvent)
  }

  public interface ResponseFlushEventOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
     * </pre>
     */
    int getErrorId();

    // required int32 eventId = 2;
    /**
     * <code>required int32 eventId = 2;</code>
     *
     * <pre>
     *事件Id
     * </pre>
     */
    boolean hasEventId();
    /**
     * <code>required int32 eventId = 2;</code>
     *
     * <pre>
     *事件Id
     * </pre>
     */
    int getEventId();

    // optional .protocol.EventInfo event = 3;
    /**
     * <code>optional .protocol.EventInfo event = 3;</code>
     *
     * <pre>
     *刷新的事件
     * </pre>
     */
    boolean hasEvent();
    /**
     * <code>optional .protocol.EventInfo event = 3;</code>
     *
     * <pre>
     *刷新的事件
     * </pre>
     */
    protocol.EventData.EventInfo getEvent();
    /**
     * <code>optional .protocol.EventInfo event = 3;</code>
     *
     * <pre>
     *刷新的事件
     * </pre>
     */
    protocol.EventData.EventInfoOrBuilder getEventOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseFlushEvent}
   */
  public static final class ResponseFlushEvent extends
      com.google.protobuf.GeneratedMessage
      implements ResponseFlushEventOrBuilder {
    // Use ResponseFlushEvent.newBuilder() to construct.
    private ResponseFlushEvent(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseFlushEvent(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseFlushEvent defaultInstance;
    public static ResponseFlushEvent getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseFlushEvent getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseFlushEvent(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              eventId_ = input.readInt32();
              break;
            }
            case 26: {
              protocol.EventData.EventInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = event_.toBuilder();
              }
              event_ = input.readMessage(protocol.EventData.EventInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(event_);
                event_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EventData.internal_static_protocol_ResponseFlushEvent_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EventData.internal_static_protocol_ResponseFlushEvent_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EventData.ResponseFlushEvent.class, protocol.EventData.ResponseFlushEvent.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseFlushEvent> PARSER =
        new com.google.protobuf.AbstractParser<ResponseFlushEvent>() {
      public ResponseFlushEvent parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseFlushEvent(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseFlushEvent> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 eventId = 2;
    public static final int EVENTID_FIELD_NUMBER = 2;
    private int eventId_;
    /**
     * <code>required int32 eventId = 2;</code>
     *
     * <pre>
     *事件Id
     * </pre>
     */
    public boolean hasEventId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 eventId = 2;</code>
     *
     * <pre>
     *事件Id
     * </pre>
     */
    public int getEventId() {
      return eventId_;
    }

    // optional .protocol.EventInfo event = 3;
    public static final int EVENT_FIELD_NUMBER = 3;
    private protocol.EventData.EventInfo event_;
    /**
     * <code>optional .protocol.EventInfo event = 3;</code>
     *
     * <pre>
     *刷新的事件
     * </pre>
     */
    public boolean hasEvent() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional .protocol.EventInfo event = 3;</code>
     *
     * <pre>
     *刷新的事件
     * </pre>
     */
    public protocol.EventData.EventInfo getEvent() {
      return event_;
    }
    /**
     * <code>optional .protocol.EventInfo event = 3;</code>
     *
     * <pre>
     *刷新的事件
     * </pre>
     */
    public protocol.EventData.EventInfoOrBuilder getEventOrBuilder() {
      return event_;
    }

    private void initFields() {
      errorId_ = 0;
      eventId_ = 0;
      event_ = protocol.EventData.EventInfo.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasEventId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasEvent()) {
        if (!getEvent().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, eventId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(3, event_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, eventId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, event_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EventData.ResponseFlushEvent parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EventData.ResponseFlushEvent parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EventData.ResponseFlushEvent parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EventData.ResponseFlushEvent parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EventData.ResponseFlushEvent prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseFlushEvent}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EventData.ResponseFlushEventOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EventData.internal_static_protocol_ResponseFlushEvent_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EventData.internal_static_protocol_ResponseFlushEvent_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EventData.ResponseFlushEvent.class, protocol.EventData.ResponseFlushEvent.Builder.class);
      }

      // Construct using protocol.EventData.ResponseFlushEvent.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEventFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        eventId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (eventBuilder_ == null) {
          event_ = protocol.EventData.EventInfo.getDefaultInstance();
        } else {
          eventBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EventData.internal_static_protocol_ResponseFlushEvent_descriptor;
      }

      public protocol.EventData.ResponseFlushEvent getDefaultInstanceForType() {
        return protocol.EventData.ResponseFlushEvent.getDefaultInstance();
      }

      public protocol.EventData.ResponseFlushEvent build() {
        protocol.EventData.ResponseFlushEvent result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EventData.ResponseFlushEvent buildPartial() {
        protocol.EventData.ResponseFlushEvent result = new protocol.EventData.ResponseFlushEvent(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.eventId_ = eventId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        if (eventBuilder_ == null) {
          result.event_ = event_;
        } else {
          result.event_ = eventBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EventData.ResponseFlushEvent) {
          return mergeFrom((protocol.EventData.ResponseFlushEvent)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EventData.ResponseFlushEvent other) {
        if (other == protocol.EventData.ResponseFlushEvent.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasEventId()) {
          setEventId(other.getEventId());
        }
        if (other.hasEvent()) {
          mergeEvent(other.getEvent());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasEventId()) {
          
          return false;
        }
        if (hasEvent()) {
          if (!getEvent().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EventData.ResponseFlushEvent parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EventData.ResponseFlushEvent) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *1代表未查詢到事件 2沒到刷新時間 3 刷新失敗
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 eventId = 2;
      private int eventId_ ;
      /**
       * <code>required int32 eventId = 2;</code>
       *
       * <pre>
       *事件Id
       * </pre>
       */
      public boolean hasEventId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 eventId = 2;</code>
       *
       * <pre>
       *事件Id
       * </pre>
       */
      public int getEventId() {
        return eventId_;
      }
      /**
       * <code>required int32 eventId = 2;</code>
       *
       * <pre>
       *事件Id
       * </pre>
       */
      public Builder setEventId(int value) {
        bitField0_ |= 0x00000002;
        eventId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 eventId = 2;</code>
       *
       * <pre>
       *事件Id
       * </pre>
       */
      public Builder clearEventId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        eventId_ = 0;
        onChanged();
        return this;
      }

      // optional .protocol.EventInfo event = 3;
      private protocol.EventData.EventInfo event_ = protocol.EventData.EventInfo.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EventData.EventInfo, protocol.EventData.EventInfo.Builder, protocol.EventData.EventInfoOrBuilder> eventBuilder_;
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public boolean hasEvent() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public protocol.EventData.EventInfo getEvent() {
        if (eventBuilder_ == null) {
          return event_;
        } else {
          return eventBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public Builder setEvent(protocol.EventData.EventInfo value) {
        if (eventBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          event_ = value;
          onChanged();
        } else {
          eventBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public Builder setEvent(
          protocol.EventData.EventInfo.Builder builderForValue) {
        if (eventBuilder_ == null) {
          event_ = builderForValue.build();
          onChanged();
        } else {
          eventBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public Builder mergeEvent(protocol.EventData.EventInfo value) {
        if (eventBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004) &&
              event_ != protocol.EventData.EventInfo.getDefaultInstance()) {
            event_ =
              protocol.EventData.EventInfo.newBuilder(event_).mergeFrom(value).buildPartial();
          } else {
            event_ = value;
          }
          onChanged();
        } else {
          eventBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public Builder clearEvent() {
        if (eventBuilder_ == null) {
          event_ = protocol.EventData.EventInfo.getDefaultInstance();
          onChanged();
        } else {
          eventBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public protocol.EventData.EventInfo.Builder getEventBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getEventFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      public protocol.EventData.EventInfoOrBuilder getEventOrBuilder() {
        if (eventBuilder_ != null) {
          return eventBuilder_.getMessageOrBuilder();
        } else {
          return event_;
        }
      }
      /**
       * <code>optional .protocol.EventInfo event = 3;</code>
       *
       * <pre>
       *刷新的事件
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EventData.EventInfo, protocol.EventData.EventInfo.Builder, protocol.EventData.EventInfoOrBuilder> 
          getEventFieldBuilder() {
        if (eventBuilder_ == null) {
          eventBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.EventData.EventInfo, protocol.EventData.EventInfo.Builder, protocol.EventData.EventInfoOrBuilder>(
                  event_,
                  getParentForChildren(),
                  isClean());
          event_ = null;
        }
        return eventBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseFlushEvent)
    }

    static {
      defaultInstance = new ResponseFlushEvent(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseFlushEvent)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetEventInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetEventInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetEventInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetEventInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_EventInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_EventInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestFlushEvent_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestFlushEvent_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseFlushEvent_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseFlushEvent_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013event.proto\022\010protocol\"\025\n\023RequestGetEve" +
      "ntInfo\";\n\024ResponseGetEventInfo\022#\n\006events" +
      "\030\001 \003(\0132\023.protocol.EventInfo\"j\n\tEventInfo" +
      "\022\021\n\teventType\030\001 \002(\005\022\r\n\005mapId\030\002 \002(\005\022\022\n\nis" +
      "Finished\030\003 \002(\010\022\021\n\tcountdown\030\004 \001(\005\022\024\n\014eve" +
      "ntAdvance\030\005 \001(\005\"$\n\021RequestFlushEvent\022\017\n\007" +
      "eventId\030\001 \002(\005\"Z\n\022ResponseFlushEvent\022\017\n\007e" +
      "rrorId\030\001 \002(\005\022\017\n\007eventId\030\002 \002(\005\022\"\n\005event\030\003" +
      " \001(\0132\023.protocol.EventInfoB\013B\tEventData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestGetEventInfo_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestGetEventInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetEventInfo_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetEventInfo_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseGetEventInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetEventInfo_descriptor,
              new java.lang.String[] { "Events", });
          internal_static_protocol_EventInfo_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_EventInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_EventInfo_descriptor,
              new java.lang.String[] { "EventType", "MapId", "IsFinished", "Countdown", "EventAdvance", });
          internal_static_protocol_RequestFlushEvent_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestFlushEvent_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestFlushEvent_descriptor,
              new java.lang.String[] { "EventId", });
          internal_static_protocol_ResponseFlushEvent_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponseFlushEvent_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseFlushEvent_descriptor,
              new java.lang.String[] { "ErrorId", "EventId", "Event", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
