package table.ItemCompose;

import table.LineKey;
import table.TableManager;

public class ItemComposeLine implements LineKey {
    public int id;
    public String ItemID;
    public String  CostItem;

    @Override
    public String toString() {
        return "ItemComposeLine{" +
                "id=" + id +
                ", ItemID='" + ItemID + '\'' +
                ", CostItem='" + CostItem + '\'' +
                '}';
    }

    @Override
    public int Key() {
        return id;
    }
}


