package module.synchronization;

import entities.GameBattleEntity;
import entities.RoleEntity;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import module.room.RoomDao;
import module.synchronization.match.RoomShowInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MissionData;
import protocol.ProtoData;
import server.SuperClient;
import server.SuperServerHandler;
import utils.MyUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by nara on 2018/5/29.
 */
public class RoomManager {
    private static Logger log = LoggerFactory.getLogger(RoomManager.class);
   /* public static List<Integer> roomPool1 = null;
    public static List<Integer> roomPool2 = null;*/
   public static List<Boolean> roomPool1 = null;
    public static List<Boolean> roomPool2 = null;
    /**
     * 初级、高级房间
     */
    public static Map<Integer,Map<Integer,RoomInfo>> roomInfoMap1 = new ConcurrentHashMap<Integer, Map<Integer, RoomInfo>>();
    static {
        roomInfoMap1.put(1,new ConcurrentHashMap<Integer, RoomInfo>());
        roomInfoMap1.put(2,new ConcurrentHashMap<Integer, RoomInfo>());
    }
    public static Map<Integer,Map<Integer,RoomInfo>> roomInfoMap2 = new ConcurrentHashMap<Integer, Map<Integer, RoomInfo>>();
    static {
        roomInfoMap2.put(1,new ConcurrentHashMap<Integer, RoomInfo>());
        roomInfoMap2.put(2,new ConcurrentHashMap<Integer, RoomInfo>());
    }
    /**
     * 玩家对应房间号
     */
    private static Map<String,RoomCtxInfo> roomRoleMap = new ConcurrentHashMap<String, RoomCtxInfo>();

    public static Map<String, RoomCtxInfo> getRoomRoleMap() {
        synchronized(roomRoleMap) {
            return roomRoleMap;
        }
    }

    public static RoomInfo getRoomInfoById(int type,int hall,int roomId){

        if (hall != 1 && hall != 2){
            return null;
        }
        Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
        Map<Integer,RoomInfo> map = roomInfoMap.get(hall);
        return map.get(roomId);
    }

    private static void changeRoomId(String uid,int roomId,int type){
        RoomCtxInfo roomCtxInfo = roomRoleMap.get(uid);
        roomCtxInfo.setRoomId(roomId);
        if (type != -1){
            roomCtxInfo.setType(type);
        }
    }

    public synchronized static boolean updateRoomRoleMap(int actionType,int hall,String uid){
        if (actionType == 1){//加入
            if (roomRoleMap.size() >= 1000){
                return false;
            }
            int nowHall = getRoleHall(uid);
            if (nowHall <= 0){
                ChannelHandlerContext ctx = SuperServerHandler.getServerIdCtxFromUid(uid);
                if (ctx == null){
                    return false;
                }
                RoomCtxInfo roomCtxInfo = new RoomCtxInfo(ctx);
                roomCtxInfo.setHall(hall);
                synchronized(roomRoleMap) {
                    roomRoleMap.put(uid, roomCtxInfo);
                }
                //离开车站
                SuperServerHandler.removeFromStation(uid,ctx);
            }
        }else if (actionType == 2){//离开
            leaveFightHall(uid);
            //回到车站
            SuperServerHandler.comeBackToStation(uid);
        }
        return true;
    }

    public static void leaveFightHall(String uid){
        int roomId = RoomManager.getRoleRoomId(uid);
        if (roomId != -1){
            leaveRoom(uid,roomId);
        }
        synchronized(roomRoleMap) {
            roomRoleMap.remove(uid);
        }
    }

    public static List<RoomShowInfo> getRoomList(int hall){
        if (hall != 1 && hall != 2){
            return null;
        }
        Map<Integer,RoomInfo> map = roomInfoMap1.get(hall);
        List<RoomShowInfo> list = new ArrayList<RoomShowInfo>();
        for (Map.Entry<Integer,RoomInfo>entry:map.entrySet()){
            RoomInfo roomInfo = entry.getValue();
            if(roomInfo.isRobot()==true ){
                RoomShowInfo roomShowInfo = new RoomShowInfo();
                roomShowInfo.setRobotRoom(roomInfo.isRobot());
                roomShowInfo.setId(entry.getKey());
                roomShowInfo.setName(roomInfo.getName());
                roomShowInfo.setPlayerNum(1);
                roomShowInfo.setIsPlaying(roomInfo.isPlaying());
                roomShowInfo.setType(roomInfo.getType());
                int val = (roomInfo.getPwd() == null || roomInfo.getPwd().equals("")) ? 0 : 1;
                roomShowInfo.setIsSecret(val);
                list.add(roomShowInfo);
            }else {
                RoomShowInfo roomShowInfo = new RoomShowInfo();
                roomShowInfo.setRobotRoom(roomInfo.isRobot());
                roomShowInfo.setId(entry.getKey());
                roomShowInfo.setName(roomInfo.getName());
                int playerNum = roomInfo.getRoomRoleList().size();
                roomShowInfo.setPlayerNum(playerNum);
                roomShowInfo.setIsPlaying(roomInfo.isPlaying());
                roomShowInfo.setType(roomInfo.getType());
                int val = (roomInfo.getPwd() == null || roomInfo.getPwd().equals("")) ? 0 : 1;
                roomShowInfo.setIsSecret(val);

                list.add(roomShowInfo);
            }
        }
        map = roomInfoMap2.get(hall);
        for (Map.Entry<Integer,RoomInfo>entry:map.entrySet()){
            RoomInfo roomInfo = entry.getValue();
            RoomShowInfo roomShowInfo = new RoomShowInfo();
            roomShowInfo.setId(entry.getKey());
            roomShowInfo.setName(roomInfo.getName());
            int playerNum=roomInfo.getRoomRoleList()==null?2:roomInfo.getRoomRoleList().size();
            roomShowInfo.setPlayerNum(playerNum);
            roomShowInfo.setIsPlaying(roomInfo.isPlaying());
            roomShowInfo.setType(roomInfo.getType());
            int val = (roomInfo.getPwd() == null || roomInfo.getPwd().equals("")) ? 0 : 1;
            roomShowInfo.setIsSecret(val);
            list.add(roomShowInfo);
        }
        return list;
    }


    public static int randRoomId(int type,int hall){
        if (hall != 1 && hall != 2){
            return -1;
        }
        Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
        Map<Integer,RoomInfo> map = roomInfoMap.get(hall);
        int roomId = 0;
        for (Map.Entry<Integer,RoomInfo>entry:map.entrySet()){
            RoomInfo roomInfo = entry.getValue();
            if (roomInfo.getRoomRoleList().size() < 2 && (roomInfo.getPwd() == null || roomInfo.getPwd().equals(""))){
                roomId = entry.getKey();
                break;
            }
        }
        return roomId;
    }

    public static int judgePassword(int type,int hall,int roomId,String pwd){
        if (hall != 1 && hall != 2){
            return -1;
        }
        Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
        Map<Integer,RoomInfo> map = roomInfoMap.get(hall);
        RoomInfo roomInfo = map.get(roomId);
        if (roomInfo == null){
            return 1;
        }
        String roomPwd = roomInfo.getPwd();
        if (roomPwd != null && !roomPwd.equals("") && !roomPwd.trim().equals(pwd.trim())){
            return 2;
        }
        return 0;
    }

    public static int getRoleRoomId(String uid){
        if (roomRoleMap.containsKey(uid)){
            RoomCtxInfo roomCtxInfo = roomRoleMap.get(uid);
            return roomCtxInfo.getRoomId();
        }
        return -1;
    }

    public static int getRoleHall(String uid){
        if (roomRoleMap.containsKey(uid)){
            RoomCtxInfo roomCtxInfo = roomRoleMap.get(uid);
            return roomCtxInfo.getHall();
        }
        return -1;
    }

    public static int getRoleRoomType(String uid){
        if (roomRoleMap.containsKey(uid)){
            RoomCtxInfo roomCtxInfo = roomRoleMap.get(uid);
            return roomCtxInfo.getType();
        }
        return -1;
    }

    public static void boardcastRoom(int type,int hall,int roomId,int playerNum,boolean isPlaying,String name,int isSecret){
        MissionData.ReportRoom.Builder builder = MissionData.ReportRoom.newBuilder();
        MissionData.RoomShow.Builder showBu = MissionData.RoomShow.newBuilder();
        showBu.setId(roomId);
        showBu.setPlayerNum(playerNum);
        showBu.setIsPlaying(isPlaying);

        if (isSecret != -1){
            showBu.setIsSecret(isSecret);
        }

        RoomInfo roomInfo = getRoomInfoById(type,hall,roomId);
       // /// System.err.println(roomInfo);
        showBu.setType(roomInfo.getType());
        if (roomInfo != null){
            List<RoomRoleInfo> roomRoleList = roomInfo.getRoomRoleList();
            if (roomRoleList != null){
                for (int i = 0 ; i < roomRoleList.size() ; i++){
                    RoomRoleInfo roomRoleInfo = roomRoleList.get(i);
                    MissionData.RoomShowOne.Builder oneBu = MissionData.RoomShowOne.newBuilder();
                    oneBu.setStatus(roomRoleInfo.getStatus());
                    oneBu.setQueue(roomRoleInfo.getQueue());
                    oneBu.setPosition(roomRoleInfo.getPosition());
                    showBu.addList(oneBu);
                }
            }
            if (name != null||roomInfo.getName()!=null){
                showBu.setName(roomInfo.getName());
            }
            builder.setRoom(showBu);
            ReportManager.boardcastInfo(0,hall,ProtoData.SToC.REPORTROOM_VALUE,builder.build().toByteArray());
        }

        if (playerNum == -1){
            Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
            RoomInfo robotRoomInfo =roomInfoMap.get(hall).remove(roomId);
            if(robotRoomInfo==null){

            }else if(robotRoomInfo.isPlaying()){
                RoomDao.HandlerRobotMatch.isPalyingRoomNums--;
            }else if(!robotRoomInfo.isPlaying()){
                RoomDao.HandlerRobotMatch.accessableRoomNums--;
            }
            List<Boolean> roomPool = type == 1 ? roomPool1 : roomPool2;
            roomPool.set(--roomId,false);
        }
    }
///第一版
  /*  public static int getRoomId1(int type){
        int min = 9999;
        int index = -1;
        List<Integer> roomPool = type == 1 ? roomPool1 : roomPool2;
        for (int i = 0 ; i < roomPool.size() ; i++){
            if (roomPool.get(i) < min){
                min = roomPool.get(i);
                index = i;
            }
        }
        if (index != -1){
            roomPool.remove(index);
        }

        return min;
    }*/

    public static int getRoomId(int type){
        int index = -1;
        List<Boolean> roomPool = type == 1 ? roomPool1 : roomPool2;
        for (int i = 0 ; i < roomPool.size() ; i++){
            Boolean isAssigned=roomPool.get(i);
            if (!isAssigned){
                index=i;
                roomPool.set(i,true);
                break;
            }
        }
       // /// System.err.println(index+"~~~"+type);

        return ++index;
    }
    public synchronized static int addRoom(String uid,RoomRoleInfo roomRoleInfo,String name,String pwd,int type){
        List<Boolean> roomPool = type == 1 ? roomPool1 : roomPool2;
        if (roomPool.size() == 0){
            return -1;
        }
        int roomId = getRoomId(type);
        /// System.out.println(roomId+"~~~~~~~~~~~~~~roodid");
        RoomInfo roomInfo = new RoomInfo();
        long startTime=0;
        // ①30%概率2-5秒进去 ②40%概率6-10秒进去 ③30%概率11-15秒进去
        double interval=Math.random();
        if(interval<=0.3){
            startTime=(long)(((Math.random()*3)+2)*1000);
        }else if(interval>0.3&&interval<=0.7){
            startTime=(long)(((Math.random()*4)+6)*1000);
        }else if(interval>0.7){
            startTime=(long)(((Math.random()*4)+11)*1000);
        }
        /// System.out.println(startTime+"startTime");
        roomInfo.setCreateRoomTime(TimerHandler.nowTimeStamp+startTime);

        roomInfo.setType(type);
        roomInfo.setName(name);
        roomInfo.setPwd(pwd);
        roomInfo.getRoomRoleList().add(roomRoleInfo);
        int hall = getRoleHall(uid);
        if (hall == -1){
            return -2;
        }
        Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
        roomInfoMap.get(hall).put(roomId,roomInfo);

        changeRoomId(uid, roomId,type);
        //广播房间
        int val = pwd.equals("") ? 0 : 1;
        boardcastRoom(type,hall,roomId, 1,false,name,val);
        return roomId;
    }
    public static RoomInfo joinRobotRoom(String uid,int type,int roomId,RoomRoleInfo roomRoleInfo){
        int hall=1;
        String key = hall+"&"+roomId;
        synchronized (key){
            Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
            RoomInfo roomInfo = roomInfoMap.get(hall).get(roomId);
            if (roomInfo == null){
                return null;
            }
            if (roomInfo.getRoomRoleList().size() >= 2){
                return null;
            }

        }
        return null;
    }
    public static RoomInfo joinRoom(String uid,int type,int roomId,RoomRoleInfo roomRoleInfo){
        int hall = getRoleHall(uid);
        String key = hall+"&"+roomId;
        synchronized (key){
            Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
            RoomInfo roomInfo = roomInfoMap.get(hall).get(roomId);
            if (roomInfo == null){
                return null;
            }
            if (roomInfo.getRoomRoleList().size() >= 2){
                return null;
            }
            //如果是机器人房间，机器人信息失效，重新生成进redis
            if(roomInfo.isRobot()){
                List<RoomRoleInfo> roomRoleList=roomInfo.getRoomRoleList();
                for(int i=0;i<roomRoleList.size();i++){
                  RoomRoleInfo  roleInfo=roomRoleList.get(i);
                  if(roleInfo.isRobot()){
                      if(!Redis.exists(roleInfo.getUid())){
                          RoomRoleInfo  newRoleInfo=new RoomDao.HandlerRobotMatch().setRoomRobotRoleInfo(1);
                          roleInfo.setUid(newRoleInfo.getUid());
                          roleInfo.setName(newRoleInfo.getName());
                          roleInfo.setLv(newRoleInfo.getLv());
                          roleInfo.setRoleId(newRoleInfo.getRoleId());
                          roleInfo.setCupboard(newRoleInfo.getCupboard());
                          roomRoleList.set(i,roleInfo);
                      }
                  }
                }
            }
            changeRoomId(uid,roomId,type);
            //更新房间玩家
            MissionData.ReportAddRoomRole.Builder builder = MissionData.ReportAddRoomRole.newBuilder();
            int p1 = 0;
            int p2 = 0;
            int val = roomInfo.getRoomRoleList().get(0).getQueue() == 1 ? 1 : -1;
            for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
                RoomRoleInfo info = roomInfo.getRoomRoleList().get(i);
                if (info.getQueue() == 1){
                    p1 += info.getPosition();
                }else {
                    p2 += info.getPosition();
                }
                val *= -1;
            }

            if (val == 1){
                roomRoleInfo.setQueue(1);
                int pos = 3-p1 == 3 ? 1 : (3-p1);
                roomRoleInfo.setPosition(pos);
            }else {
                roomRoleInfo.setQueue(2);
                int pos = 3-p2 == 3 ? 1 : (3-p2);
                roomRoleInfo.setPosition(pos);
            }
            MissionData.RoomRole roomRole = RoomRoleInfo.toRoomRoleData(roomRoleInfo);
            builder.setRoomRole(roomRole);
            for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
                RoomRoleInfo info = roomInfo.getRoomRoleList().get(i);
                String pUid = info.getUid();
                ReportManager.reportInfo(pUid, ProtoData.SToC.REPORTADDROOMROLE_VALUE, builder.build().toByteArray());
            }

            roomInfo.getRoomRoleList().add(roomRoleInfo);
            //广播房间
            boardcastRoom(type,hall,roomId,roomInfo.getRoomRoleList().size(),false,null,-1);
            return roomInfo;
        }
    }

    public static void leaveRoom(String uid,int roomId){
        try {
            int hall = getRoleHall(uid);
            int type = getRoleRoomType(uid);
            Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = type == 1 ? roomInfoMap1 : roomInfoMap2;
            RoomInfo roomInfo = roomInfoMap.get(hall).get(roomId);
            String playerName=getPlayerName(uid);
            if (roomInfo == null){
                return;
            }
            String roomName=roomInfo.getName();
            RoomRoleInfo tmp = null;
            for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
                tmp = roomInfo.getRoomRoleList().get(i);
                if (tmp.getUid().equals(uid)){
                    roomInfo.getRoomRoleList().remove(i);
                    break;
                }
            }

            if (roomInfo.getRoomRoleList().size() == 0||roomInfo.isRobot()){
                //广播解散房间
                boardcastRoom(type,hall,roomId,-1,false,roomName,-1);
                if(roomInfo.isRobot()){
                    if( RoomDao.HandlerRobotMatch.accessableRoomNums<=10){   //减少一个机器人房间就增加一个
                        new RoomDao.HandlerRobotMatch().addRoom(0);
                    }
                    List<RoomRoleInfo>  list =roomInfo.getRoomRoleList();
                    for(int i=0;i<list.size();i++){
                        RoomRoleInfo roomRoleInfo=list.get(i);
                        if(roomRoleInfo.isRobot()){
                            Redis.del(roomRoleInfo.getUid());
                        }
                    }

                    //移除玩家在机器人房间的游戏开始请求
                    RoomDao.HandlerRobotMatch.robotGameHandleMap.remove(uid);
                }
            }else if (tmp != null && tmp.getType() == 1&&(!roomInfo.isRobot())){
                roomInfo.getRoomRoleList().get(0).setType(1);
                roomInfo.getRoomRoleList().get(0).setStatus(1);
                //更新变房主
                updateRoomRoleInside(roomInfo.getRoomRoleList(),roomInfo.getRoomRoleList().get(0).getPlayerId(), 1, -1, -1, false,roomName,-1,-1);
                //广播房间
                boardcastRoom(type,hall,roomId,roomInfo.getRoomRoleList().size(),false,roomName,-1);
                //更新玩家离开
                updateRoomRoleInside(roomInfo.getRoomRoleList(),tmp.getPlayerId(), -1, -1, -1, true,playerName,-1,-1);
            }else if (tmp != null){
                //广播房间
                boardcastRoom(type,hall,roomId,roomInfo.getRoomRoleList().size(),false,roomName,-1);
                //更新玩家离开
                updateRoomRoleInside(roomInfo.getRoomRoleList(),tmp.getPlayerId(), -1, -1, -1, true,playerName,-1,-1);
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            changeRoomId(uid,0,0);
        }
    }

    public static void updateRoomRolePosition(List<RoomRoleInfo> roomRoleInfoList,int playerId,int position){
        updateRoomRoleInside(roomRoleInfoList,playerId,-1,-1,-1,false,null,-1,position);
    }
    public static void updateRoomRoleStatus(List<RoomRoleInfo> roomRoleInfoList,int playerId,int status){
        updateRoomRoleInside(roomRoleInfoList,playerId,-1,status,-1,false,null,-1,-1);
    }
    public static void updateRoomRoleQueue(List<RoomRoleInfo> roomRoleInfoList,int playerId,int queue,int position){
        updateRoomRoleInside(roomRoleInfoList,playerId,-1,-1,queue,false,null,-1,position);
    }
    public static void updateRoomName(List<RoomRoleInfo> roomRoleInfoList,int playerId,String name){
        updateRoomRoleInside(roomRoleInfoList,playerId,-1,-1,-1,false,name,-1,-1);
    }
    public static void updateRoomPwd(List<RoomRoleInfo> roomRoleInfoList,int playerId,String pwd,String oldPwd){
        if (pwd.equals("") && !oldPwd.equals("") || !pwd.equals("") && oldPwd.equals("")){
            int val = pwd.equals("") ? 0 : 1;
            updateRoomRoleInside(roomRoleInfoList,playerId,-1,-1,-1,false,null,val,-1);
        }
    }

    public static void updateRoomNameAndPwd(List<RoomRoleInfo> roomRoleInfoList,int playerId,String name,String pwd,String oldPwd){
        if (pwd.equals("") && !oldPwd.equals("") || !pwd.equals("") && oldPwd.equals("")){
            int val = pwd.equals("") ? 0 : 1;
            updateRoomRoleInside(roomRoleInfoList,playerId,-1,-1,-1,false,name,val,-1);
        }else {
            updateRoomName(roomRoleInfoList,playerId,name);
        }
    }

    private static void updateRoomRoleInside(List<RoomRoleInfo> roomRoleInfoList,int playerId,int roleType,int status,int queue,boolean goOut,String name, int isSecret,int position){
        MissionData.ReportUpdateRoomInside.Builder builder = MissionData.ReportUpdateRoomInside.newBuilder();
        builder.setPlayerId(playerId);
        if (roleType != -1){
            builder.setType(roleType);
        }
        if (status != -1){
            builder.setStatus(status);
        }
        if (queue != -1){
            builder.setQueue(queue);
        }
        if (goOut != false){
            builder.setGoOut(true);
        }
        if (name != null && isSecret != -1){
            String tmpUid = roomRoleInfoList.get(0).getUid();
            int hall = getRoleHall(tmpUid);
            int roomId = getRoleRoomId(tmpUid);
            int type = getRoleRoomType(tmpUid);
            boardcastRoom(type,hall, roomId, roomRoleInfoList.size(), false, name, isSecret);
        }else {
            if (name != null){
                builder.setName(name);

                String tmpUid = roomRoleInfoList.get(0).getUid();
                int hall = getRoleHall(tmpUid);
                int roomId = getRoleRoomId(tmpUid);
                int type = getRoleRoomType(tmpUid);
                boardcastRoom(type,hall, roomId, roomRoleInfoList.size(), false, name, -1);
            }
            if (isSecret != -1){
                builder.setIsSecret(isSecret);

                String tmpUid = roomRoleInfoList.get(0).getUid();
                int hall = getRoleHall(tmpUid);
                int roomId = getRoleRoomId(tmpUid);
                int type = getRoleRoomType(tmpUid);
                boardcastRoom(type,hall, roomId, roomRoleInfoList.size(), false, null, isSecret);
            }
        }

        if (position != -1){
            builder.setPosition(position);
        }
        for (int i = 0 ; i<roomRoleInfoList.size() ; i++){
            RoomRoleInfo roomRoleInfo = roomRoleInfoList.get(i);
            if (roomRoleInfo.getPlayerId() != playerId){
                ReportManager.reportInfo(roomRoleInfo.getUid(),ProtoData.SToC.REPORTUPDATEROOMINSIDE_VALUE,builder.build().toByteArray());
            }
        }
    }

    public static void comeToFight(int hall,int roomId,RoomInfo roomInfo){
        /// System.out.println("cometoFight__"+hall+"hall"+roomId+"roomid"+roomInfo);
        roomInfo.setIsPlaying(true);
        boardcastRoom(roomInfo.getType(),hall,roomId,roomInfo.getRoomRoleList().size(),true,null,-1);
        //记录游戏
        GameBattleEntity game=new GameBattleEntity();
        game.setType(1);
        game.setTime(new Timestamp(new Date().getTime()));
        String key = MyUtils.setRandom();
        //向战斗服传递房间玩家数据
        MissionData.SRequestEnterMission.Builder sBuilder = MissionData.SRequestEnterMission.newBuilder();
        sBuilder.setHall(hall);
        sBuilder.setRoomId(roomId);
        sBuilder.setKey(key);
        sBuilder.setType(roomInfo.getType());
        for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
            RoomRoleInfo roomRoleInfo = roomInfo.getRoomRoleList().get(i);
            MissionData.TransferPlayerInfo.Builder transferBu = MissionData.TransferPlayerInfo.newBuilder();
            transferBu.setBobot(false);
            if(roomRoleInfo.isRobot()){
                transferBu.setBobot(true);
            }
            transferBu.setId(roomRoleInfo.getPlayerId());
            transferBu.setUid(roomRoleInfo.getUid());
            transferBu.setQueue(roomRoleInfo.getQueue());
            sBuilder.addPlayers(transferBu);
            if(game.getPlayer1()==null){
                game.setPlayer1(roomRoleInfo.getUid());
            }else{
                game.setPlayer2(roomRoleInfo.getUid());
            }
        }
        if(!roomInfo.isRobot()){
            MySql.insert(game);
        }

      //  /// System.out.println(game);
        //发送消息告诉战斗服加入一场战斗
        try {
            SuperClient.sendMsg(ProtoData.SToM.REQUESTENTERMISSION_VALUE, sBuilder.build().toByteArray());
        }catch (Exception e){
            e.printStackTrace();
        }
    }
    //成功后广播去战斗服
    public static void boardcastToFight(byte[] bytes){
        MissionData.MResponseEnterMission mResponseEnterMission = null;
        try {
            mResponseEnterMission = MissionData.MResponseEnterMission.parseFrom(bytes);
        }catch (Exception e){
            return;
        }
       // /// System.out.println(mResponseEnterMission.getKey()+"key"+mResponseEnterMission.getType()+"type"+mResponseEnterMission.getHall()+"hall"+mResponseEnterMission.getRoomId()+"roomId");
        MissionData.ReportComeToFight.Builder builder = MissionData.ReportComeToFight.newBuilder();
        builder.setKey(mResponseEnterMission.getKey());
        builder.setType(mResponseEnterMission.getType());
        Map<Integer,Map<Integer,RoomInfo>> roomInfoMap = mResponseEnterMission.getType() == 1 ? roomInfoMap1 : roomInfoMap2;
        RoomInfo roomInfo = roomInfoMap.get(mResponseEnterMission.getHall()).get(mResponseEnterMission.getRoomId());
        for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
            RoomRoleInfo roomRoleInfo = roomInfo.getRoomRoleList().get(i);
            /// System.out.println(roomRoleInfo);
            if(roomRoleInfo.isRobot()){
                continue;
            }
            ReportManager.reportInfo(roomRoleInfo.getUid(), ProtoData.SToC.REPORTCOMETOFIGHT_VALUE, builder.build().toByteArray());
        }
    }

    public static void comeBackFight(int type,int hall,int roomId){
        try {
            RoomInfo roomInfo = getRoomInfoById(type,hall,roomId);
            if (roomInfo == null){
                log.error("?????????????????????[comeBackFight] roomInfo is NULL");
                return;
            }
            roomInfo.setIsPlaying(false);
            for (int i = 0 ; i < roomInfo.getRoomRoleList().size() ; i++){
                RoomRoleInfo roomRoleInfo = roomInfo.getRoomRoleList().get(i);
                if (roomRoleInfo.getType() != 1){
                    roomRoleInfo.setStatus(0);
                    updateRoomRoleStatus(roomInfo.getRoomRoleList(),roomRoleInfo.getPlayerId(),0);
                }
            }
            boardcastRoom(type,hall, roomId, roomInfo.getRoomRoleList().size(),false,null,-1);

        }catch (Exception e){
            log.error("??????????????????comeBackFight unknown error?????????????????");
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }
    public static String  getPlayerName(String uid){
        Redis jedis = Redis.getInstance();
        String name=jedis.hget("role:"+uid,"name");
        if(name==null){
            StringBuffer sql=new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity roleEntity=(RoleEntity)MySql.queryForOne(sql.toString());
            name=roleEntity.getName();
        }
        return name;
    }
}