<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.ActivitiesEntity" table="activities" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="type" column="type"/>
        <property name="uid" column="uid"/>
        <property name="activitiesId" column="activitiesId"/>
        <property name="num" column="num"/>
        <property name="status" column="status"/>
        <property name="timestamp" column="timestamp"/>
        <property name="overdue" column="overdue"/>
    </class>
</hibernate-mapping>