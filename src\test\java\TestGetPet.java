import common.PetConfig;
import common.SuperConfig;
import entities.PetEntity;
import manager.*;
import model.ItemInfo;
import module.event.EventUtils;
import module.item.ItemDao;
import module.item.ItemService;
import module.item.ItemUtils;
import module.pet.PetUtils;
import module.robot.ranName;
import protocol.ItemData;
import protocol.LottoData;
import protocol.PetData;
import protocol.ProtoData;
import sun.misc.Signal;
import sun.misc.SignalHandler;

import java.util.HashMap;
import java.util.Map;

public class TestGetPet {
    public static void main(String[] args) {
//        Signal.handle(signal, new SignalHandler() {
//            public void handle(Signal signal) {
//                Thread t=new Thread(new ShutdownHook());
//                Runtime.getRuntime().addShutdownHook(t);
//                Runtime.getRuntime().exit(0);
//            }
//        });
        SuperConfig.setGAMEMODE(SuperConfig.GAMEMODE_MAIN);
        Redis.getInstance();
        if (SuperConfig.getGAMEMODE() != SuperConfig.GAMEMODE_MISSION && SuperConfig.getGAMEMODE() != SuperConfig.GAMEMODE_MISSION_PLOTTER){
            MySql.getInstance();
            TimerHandler timerHandler = new TimerHandler();
            timerHandler.openTimer();
            InitData.initGameConfig();
            SuperConfig.initconfigMap();
        }
        ItemService.getInstance();
        new EventUtils();
        ReportManager.init();
//        ItemService.getInstance().countLotto(null,null);
//        SuperConfig.initconfigMap();
        CountPet();
    }

    private static void  CountPet(){
        long[] starNumber = new long[6];
        long[] rareNumber = new long[6];
        PetEntity petEntity = null;
        for (int i = 0; i < 10000; i++) {
            System.out.println("抽卡 = " + i);
            petEntity = GetPet();
            starNumber[petEntity.getStarLevel()]++;
            rareNumber[petEntity.getRarity()]++;
        }
        for (int i = 1; i < starNumber.length; i++) {
            System.out.println(i + " StarNum = " + starNumber[i]);
        }
        for (int i = 1; i < rareNumber.length; i++) {
            System.out.println(i + " RareNum = " + rareNumber[i]);
        }
//        System.out.println(petEntity.toString());
//        System.out.println(
//                "宠物ID = " + petEntity.getPetType()
//                + "宠物星级 = " + petEntity.getStarLevel()
//                + "宠物稀有度 = " + petEntity.getRarity());
    }

    private static PetEntity GetPet(){
        PetEntity petEntity = null;
        try {
//            int poolId = requestCountLotto.getLottoId();
            int poolId = 1;
            int num = 1;
            Map<String, String> raffleConfig = Redis.getExcelMap("raffleTypeconfig", poolId);
//            ItemInfo itemCost = ItemUtils.getItemInfo(raffleConfig.get("cost" + num));
//            System.out.println(itemCost+"lllllllllllllllllllllllllllllll");
//            double nowValue = ItemDao.getInstance().updateItemInfo(uid, itemCost.getId(), -(long) itemCost.getNum());
//            if (nowValue < 0) {
//
//            } else {

//                ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//                reportBuilder.addItem(ItemUtils.getItemData(itemCost.getId(), nowValue));
//                String getItemString = null;
//                if (num == 1) {
//                    getItemString = raffleConfig.get("get_item").split("\\|")[0];
//                } else if (num == 11) {
//                    getItemString = raffleConfig.get("get_item").split("\\|")[1];
//                }
//                ItemInfo getitem = ItemUtils.getItemInfo(getItemString);
//                double cuurValue = ItemDao.getInstance().updateItemInfo(uid, getitem.getId(), (long) getitem.getNum());
//                reportBuilder.addItem(ItemUtils.getItemData(getitem.getId(), cuurValue));
//                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//            }
            int access = Integer.parseInt(raffleConfig.get("access"));
//            PetData.ResponseOperatePet.Builder petbuilder = PetData.ResponseOperatePet.newBuilder();
//            petbuilder.setErrorId(0);
//            petbuilder.setType(1);

            for (int i = 0; i < num; i++) {
//                int index = ranName.ranGift(ItemService.getInstance().rafflePool.get(poolId));
//                int exchageId = ItemService.getInstance().exchangeMap.get(poolId).get(index);
//                int petType = Integer.parseInt(Redis.getExcelInfo("rafflePetBasicconfig", exchageId + "", "pet_id"));
                if (access == PetConfig.petFromRafflePool) {
//                    petEntity = PetUtils.createEvlutionPet(petType, access);
                } else {
//                    petEntity = PetUtils.createPet(petType, access);
                }
//                petbuilder.addPet(PetEntity.entityToPb(petEntity));
//                PetData.PlainPet.Builder plainPetBuilder = PetData.PlainPet.newBuilder();
//                plainPetBuilder.setPetId(petEntity.getPetType());
//                plainPetBuilder.setRarity(petEntity.getStarLevel());
//                plainPetBuilder.setPetType(petEntity.getPetType());

//                builder.addPet(plainPetBuilder);
            }

//            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petbuilder.build().toByteArray());

        } catch (Exception e) {
        }
        return petEntity;
    }
}
