// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: lotto.proto

package protocol;

public final class LottoData {
  private LottoData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestCountLottoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 num = 1;
    /**
     * <code>required int32 num = 1;</code>
     *
     * <pre>
     *num的值为1/10
     * </pre>
     */
    boolean hasNum();
    /**
     * <code>required int32 num = 1;</code>
     *
     * <pre>
     *num的值为1/10
     * </pre>
     */
    int getNum();

    // required int32 lottoId = 2;
    /**
     * <code>required int32 lottoId = 2;</code>
     */
    boolean hasLottoId();
    /**
     * <code>required int32 lottoId = 2;</code>
     */
    int getLottoId();

    // optional int32 free = 3;
    /**
     * <code>optional int32 free = 3;</code>
     *
     * <pre>
     *1：true
     * </pre>
     */
    boolean hasFree();
    /**
     * <code>optional int32 free = 3;</code>
     *
     * <pre>
     *1：true
     * </pre>
     */
    int getFree();
  }
  /**
   * Protobuf type {@code protocol.RequestCountLotto}
   *
   * <pre>
   *REQUESTCOUNTLOTTO=1703				//请求合成道具
   * </pre>
   */
  public static final class RequestCountLotto extends
      com.google.protobuf.GeneratedMessage
      implements RequestCountLottoOrBuilder {
    // Use RequestCountLotto.newBuilder() to construct.
    private RequestCountLotto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestCountLotto(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestCountLotto defaultInstance;
    public static RequestCountLotto getDefaultInstance() {
      return defaultInstance;
    }

    public RequestCountLotto getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestCountLotto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              num_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              lottoId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              free_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LottoData.internal_static_protocol_RequestCountLotto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LottoData.internal_static_protocol_RequestCountLotto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LottoData.RequestCountLotto.class, protocol.LottoData.RequestCountLotto.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestCountLotto> PARSER =
        new com.google.protobuf.AbstractParser<RequestCountLotto>() {
      public RequestCountLotto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestCountLotto(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestCountLotto> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 num = 1;
    public static final int NUM_FIELD_NUMBER = 1;
    private int num_;
    /**
     * <code>required int32 num = 1;</code>
     *
     * <pre>
     *num的值为1/10
     * </pre>
     */
    public boolean hasNum() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 num = 1;</code>
     *
     * <pre>
     *num的值为1/10
     * </pre>
     */
    public int getNum() {
      return num_;
    }

    // required int32 lottoId = 2;
    public static final int LOTTOID_FIELD_NUMBER = 2;
    private int lottoId_;
    /**
     * <code>required int32 lottoId = 2;</code>
     */
    public boolean hasLottoId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 lottoId = 2;</code>
     */
    public int getLottoId() {
      return lottoId_;
    }

    // optional int32 free = 3;
    public static final int FREE_FIELD_NUMBER = 3;
    private int free_;
    /**
     * <code>optional int32 free = 3;</code>
     *
     * <pre>
     *1：true
     * </pre>
     */
    public boolean hasFree() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 free = 3;</code>
     *
     * <pre>
     *1：true
     * </pre>
     */
    public int getFree() {
      return free_;
    }

    private void initFields() {
      num_ = 0;
      lottoId_ = 0;
      free_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLottoId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, num_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, lottoId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, free_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, num_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, lottoId_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, free_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LottoData.RequestCountLotto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.RequestCountLotto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LottoData.RequestCountLotto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.RequestCountLotto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LottoData.RequestCountLotto prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestCountLotto}
     *
     * <pre>
     *REQUESTCOUNTLOTTO=1703				//请求合成道具
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LottoData.RequestCountLottoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LottoData.internal_static_protocol_RequestCountLotto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LottoData.internal_static_protocol_RequestCountLotto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LottoData.RequestCountLotto.class, protocol.LottoData.RequestCountLotto.Builder.class);
      }

      // Construct using protocol.LottoData.RequestCountLotto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        lottoId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        free_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LottoData.internal_static_protocol_RequestCountLotto_descriptor;
      }

      public protocol.LottoData.RequestCountLotto getDefaultInstanceForType() {
        return protocol.LottoData.RequestCountLotto.getDefaultInstance();
      }

      public protocol.LottoData.RequestCountLotto build() {
        protocol.LottoData.RequestCountLotto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LottoData.RequestCountLotto buildPartial() {
        protocol.LottoData.RequestCountLotto result = new protocol.LottoData.RequestCountLotto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.num_ = num_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.lottoId_ = lottoId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.free_ = free_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LottoData.RequestCountLotto) {
          return mergeFrom((protocol.LottoData.RequestCountLotto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LottoData.RequestCountLotto other) {
        if (other == protocol.LottoData.RequestCountLotto.getDefaultInstance()) return this;
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        if (other.hasLottoId()) {
          setLottoId(other.getLottoId());
        }
        if (other.hasFree()) {
          setFree(other.getFree());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNum()) {
          
          return false;
        }
        if (!hasLottoId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LottoData.RequestCountLotto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LottoData.RequestCountLotto) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 num = 1;
      private int num_ ;
      /**
       * <code>required int32 num = 1;</code>
       *
       * <pre>
       *num的值为1/10
       * </pre>
       */
      public boolean hasNum() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 num = 1;</code>
       *
       * <pre>
       *num的值为1/10
       * </pre>
       */
      public int getNum() {
        return num_;
      }
      /**
       * <code>required int32 num = 1;</code>
       *
       * <pre>
       *num的值为1/10
       * </pre>
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000001;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 num = 1;</code>
       *
       * <pre>
       *num的值为1/10
       * </pre>
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        onChanged();
        return this;
      }

      // required int32 lottoId = 2;
      private int lottoId_ ;
      /**
       * <code>required int32 lottoId = 2;</code>
       */
      public boolean hasLottoId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 lottoId = 2;</code>
       */
      public int getLottoId() {
        return lottoId_;
      }
      /**
       * <code>required int32 lottoId = 2;</code>
       */
      public Builder setLottoId(int value) {
        bitField0_ |= 0x00000002;
        lottoId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 lottoId = 2;</code>
       */
      public Builder clearLottoId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lottoId_ = 0;
        onChanged();
        return this;
      }

      // optional int32 free = 3;
      private int free_ ;
      /**
       * <code>optional int32 free = 3;</code>
       *
       * <pre>
       *1：true
       * </pre>
       */
      public boolean hasFree() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 free = 3;</code>
       *
       * <pre>
       *1：true
       * </pre>
       */
      public int getFree() {
        return free_;
      }
      /**
       * <code>optional int32 free = 3;</code>
       *
       * <pre>
       *1：true
       * </pre>
       */
      public Builder setFree(int value) {
        bitField0_ |= 0x00000004;
        free_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 free = 3;</code>
       *
       * <pre>
       *1：true
       * </pre>
       */
      public Builder clearFree() {
        bitField0_ = (bitField0_ & ~0x00000004);
        free_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestCountLotto)
    }

    static {
      defaultInstance = new RequestCountLotto(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestCountLotto)
  }

  public interface ResponseCountLottoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // repeated .protocol.PlainPet pet = 2;
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    java.util.List<protocol.PetData.PlainPet> 
        getPetList();
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    protocol.PetData.PlainPet getPet(int index);
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    int getPetCount();
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    java.util.List<? extends protocol.PetData.PlainPetOrBuilder> 
        getPetOrBuilderList();
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    protocol.PetData.PlainPetOrBuilder getPetOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseCountLotto}
   *
   * <pre>
   *RESPONSECOUNTLOTTO=2703				//返回抽取道具
   * </pre>
   */
  public static final class ResponseCountLotto extends
      com.google.protobuf.GeneratedMessage
      implements ResponseCountLottoOrBuilder {
    // Use ResponseCountLotto.newBuilder() to construct.
    private ResponseCountLotto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseCountLotto(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseCountLotto defaultInstance;
    public static ResponseCountLotto getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseCountLotto getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseCountLotto(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                pet_ = new java.util.ArrayList<protocol.PetData.PlainPet>();
                mutable_bitField0_ |= 0x00000002;
              }
              pet_.add(input.readMessage(protocol.PetData.PlainPet.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          pet_ = java.util.Collections.unmodifiableList(pet_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LottoData.internal_static_protocol_ResponseCountLotto_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LottoData.internal_static_protocol_ResponseCountLotto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LottoData.ResponseCountLotto.class, protocol.LottoData.ResponseCountLotto.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseCountLotto> PARSER =
        new com.google.protobuf.AbstractParser<ResponseCountLotto>() {
      public ResponseCountLotto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseCountLotto(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseCountLotto> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // repeated .protocol.PlainPet pet = 2;
    public static final int PET_FIELD_NUMBER = 2;
    private java.util.List<protocol.PetData.PlainPet> pet_;
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    public java.util.List<protocol.PetData.PlainPet> getPetList() {
      return pet_;
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    public java.util.List<? extends protocol.PetData.PlainPetOrBuilder> 
        getPetOrBuilderList() {
      return pet_;
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    public int getPetCount() {
      return pet_.size();
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    public protocol.PetData.PlainPet getPet(int index) {
      return pet_.get(index);
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 2;</code>
     *
     * <pre>
     *  展示页面物品
     * </pre>
     */
    public protocol.PetData.PlainPetOrBuilder getPetOrBuilder(
        int index) {
      return pet_.get(index);
    }

    private void initFields() {
      errorId_ = 0;
      pet_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getPetCount(); i++) {
        if (!getPet(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      for (int i = 0; i < pet_.size(); i++) {
        output.writeMessage(2, pet_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      for (int i = 0; i < pet_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, pet_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LottoData.ResponseCountLotto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.ResponseCountLotto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LottoData.ResponseCountLotto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.ResponseCountLotto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LottoData.ResponseCountLotto prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseCountLotto}
     *
     * <pre>
     *RESPONSECOUNTLOTTO=2703				//返回抽取道具
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LottoData.ResponseCountLottoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LottoData.internal_static_protocol_ResponseCountLotto_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LottoData.internal_static_protocol_ResponseCountLotto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LottoData.ResponseCountLotto.class, protocol.LottoData.ResponseCountLotto.Builder.class);
      }

      // Construct using protocol.LottoData.ResponseCountLotto.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPetFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (petBuilder_ == null) {
          pet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          petBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LottoData.internal_static_protocol_ResponseCountLotto_descriptor;
      }

      public protocol.LottoData.ResponseCountLotto getDefaultInstanceForType() {
        return protocol.LottoData.ResponseCountLotto.getDefaultInstance();
      }

      public protocol.LottoData.ResponseCountLotto build() {
        protocol.LottoData.ResponseCountLotto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LottoData.ResponseCountLotto buildPartial() {
        protocol.LottoData.ResponseCountLotto result = new protocol.LottoData.ResponseCountLotto(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (petBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            pet_ = java.util.Collections.unmodifiableList(pet_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.pet_ = pet_;
        } else {
          result.pet_ = petBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LottoData.ResponseCountLotto) {
          return mergeFrom((protocol.LottoData.ResponseCountLotto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LottoData.ResponseCountLotto other) {
        if (other == protocol.LottoData.ResponseCountLotto.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (petBuilder_ == null) {
          if (!other.pet_.isEmpty()) {
            if (pet_.isEmpty()) {
              pet_ = other.pet_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensurePetIsMutable();
              pet_.addAll(other.pet_);
            }
            onChanged();
          }
        } else {
          if (!other.pet_.isEmpty()) {
            if (petBuilder_.isEmpty()) {
              petBuilder_.dispose();
              petBuilder_ = null;
              pet_ = other.pet_;
              bitField0_ = (bitField0_ & ~0x00000002);
              petBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getPetFieldBuilder() : null;
            } else {
              petBuilder_.addAllMessages(other.pet_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getPetCount(); i++) {
          if (!getPet(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LottoData.ResponseCountLotto parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LottoData.ResponseCountLotto) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.PlainPet pet = 2;
      private java.util.List<protocol.PetData.PlainPet> pet_ =
        java.util.Collections.emptyList();
      private void ensurePetIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          pet_ = new java.util.ArrayList<protocol.PetData.PlainPet>(pet_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PetData.PlainPet, protocol.PetData.PlainPet.Builder, protocol.PetData.PlainPetOrBuilder> petBuilder_;

      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public java.util.List<protocol.PetData.PlainPet> getPetList() {
        if (petBuilder_ == null) {
          return java.util.Collections.unmodifiableList(pet_);
        } else {
          return petBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public int getPetCount() {
        if (petBuilder_ == null) {
          return pet_.size();
        } else {
          return petBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public protocol.PetData.PlainPet getPet(int index) {
        if (petBuilder_ == null) {
          return pet_.get(index);
        } else {
          return petBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder setPet(
          int index, protocol.PetData.PlainPet value) {
        if (petBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePetIsMutable();
          pet_.set(index, value);
          onChanged();
        } else {
          petBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder setPet(
          int index, protocol.PetData.PlainPet.Builder builderForValue) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.set(index, builderForValue.build());
          onChanged();
        } else {
          petBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder addPet(protocol.PetData.PlainPet value) {
        if (petBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePetIsMutable();
          pet_.add(value);
          onChanged();
        } else {
          petBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder addPet(
          int index, protocol.PetData.PlainPet value) {
        if (petBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePetIsMutable();
          pet_.add(index, value);
          onChanged();
        } else {
          petBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder addPet(
          protocol.PetData.PlainPet.Builder builderForValue) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.add(builderForValue.build());
          onChanged();
        } else {
          petBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder addPet(
          int index, protocol.PetData.PlainPet.Builder builderForValue) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.add(index, builderForValue.build());
          onChanged();
        } else {
          petBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder addAllPet(
          java.lang.Iterable<? extends protocol.PetData.PlainPet> values) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          super.addAll(values, pet_);
          onChanged();
        } else {
          petBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder clearPet() {
        if (petBuilder_ == null) {
          pet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          petBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public Builder removePet(int index) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.remove(index);
          onChanged();
        } else {
          petBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public protocol.PetData.PlainPet.Builder getPetBuilder(
          int index) {
        return getPetFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public protocol.PetData.PlainPetOrBuilder getPetOrBuilder(
          int index) {
        if (petBuilder_ == null) {
          return pet_.get(index);  } else {
          return petBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public java.util.List<? extends protocol.PetData.PlainPetOrBuilder> 
           getPetOrBuilderList() {
        if (petBuilder_ != null) {
          return petBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(pet_);
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public protocol.PetData.PlainPet.Builder addPetBuilder() {
        return getPetFieldBuilder().addBuilder(
            protocol.PetData.PlainPet.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public protocol.PetData.PlainPet.Builder addPetBuilder(
          int index) {
        return getPetFieldBuilder().addBuilder(
            index, protocol.PetData.PlainPet.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 2;</code>
       *
       * <pre>
       *  展示页面物品
       * </pre>
       */
      public java.util.List<protocol.PetData.PlainPet.Builder> 
           getPetBuilderList() {
        return getPetFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PetData.PlainPet, protocol.PetData.PlainPet.Builder, protocol.PetData.PlainPetOrBuilder> 
          getPetFieldBuilder() {
        if (petBuilder_ == null) {
          petBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.PetData.PlainPet, protocol.PetData.PlainPet.Builder, protocol.PetData.PlainPetOrBuilder>(
                  pet_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          pet_ = null;
        }
        return petBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseCountLotto)
    }

    static {
      defaultInstance = new ResponseCountLotto(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseCountLotto)
  }

  public interface RequestRafflePoolOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestRafflePool}
   *
   * <pre>
   *1150
   * </pre>
   */
  public static final class RequestRafflePool extends
      com.google.protobuf.GeneratedMessage
      implements RequestRafflePoolOrBuilder {
    // Use RequestRafflePool.newBuilder() to construct.
    private RequestRafflePool(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestRafflePool(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestRafflePool defaultInstance;
    public static RequestRafflePool getDefaultInstance() {
      return defaultInstance;
    }

    public RequestRafflePool getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestRafflePool(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LottoData.internal_static_protocol_RequestRafflePool_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LottoData.internal_static_protocol_RequestRafflePool_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LottoData.RequestRafflePool.class, protocol.LottoData.RequestRafflePool.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestRafflePool> PARSER =
        new com.google.protobuf.AbstractParser<RequestRafflePool>() {
      public RequestRafflePool parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestRafflePool(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestRafflePool> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LottoData.RequestRafflePool parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.RequestRafflePool parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LottoData.RequestRafflePool parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.RequestRafflePool parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LottoData.RequestRafflePool prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestRafflePool}
     *
     * <pre>
     *1150
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LottoData.RequestRafflePoolOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LottoData.internal_static_protocol_RequestRafflePool_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LottoData.internal_static_protocol_RequestRafflePool_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LottoData.RequestRafflePool.class, protocol.LottoData.RequestRafflePool.Builder.class);
      }

      // Construct using protocol.LottoData.RequestRafflePool.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LottoData.internal_static_protocol_RequestRafflePool_descriptor;
      }

      public protocol.LottoData.RequestRafflePool getDefaultInstanceForType() {
        return protocol.LottoData.RequestRafflePool.getDefaultInstance();
      }

      public protocol.LottoData.RequestRafflePool build() {
        protocol.LottoData.RequestRafflePool result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LottoData.RequestRafflePool buildPartial() {
        protocol.LottoData.RequestRafflePool result = new protocol.LottoData.RequestRafflePool(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LottoData.RequestRafflePool) {
          return mergeFrom((protocol.LottoData.RequestRafflePool)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LottoData.RequestRafflePool other) {
        if (other == protocol.LottoData.RequestRafflePool.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LottoData.RequestRafflePool parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LottoData.RequestRafflePool) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestRafflePool)
    }

    static {
      defaultInstance = new RequestRafflePool(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestRafflePool)
  }

  public interface ResponseRafflePoolOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated int32 poolId = 1;
    /**
     * <code>repeated int32 poolId = 1;</code>
     *
     * <pre>
     *对应raffle_type表格里的id
     * </pre>
     */
    java.util.List<java.lang.Integer> getPoolIdList();
    /**
     * <code>repeated int32 poolId = 1;</code>
     *
     * <pre>
     *对应raffle_type表格里的id
     * </pre>
     */
    int getPoolIdCount();
    /**
     * <code>repeated int32 poolId = 1;</code>
     *
     * <pre>
     *对应raffle_type表格里的id
     * </pre>
     */
    int getPoolId(int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseRafflePool}
   *
   * <pre>
   *2150
   * </pre>
   */
  public static final class ResponseRafflePool extends
      com.google.protobuf.GeneratedMessage
      implements ResponseRafflePoolOrBuilder {
    // Use ResponseRafflePool.newBuilder() to construct.
    private ResponseRafflePool(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseRafflePool(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseRafflePool defaultInstance;
    public static ResponseRafflePool getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseRafflePool getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseRafflePool(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                poolId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              poolId_.add(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001) && input.getBytesUntilLimit() > 0) {
                poolId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                poolId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          poolId_ = java.util.Collections.unmodifiableList(poolId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LottoData.internal_static_protocol_ResponseRafflePool_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LottoData.internal_static_protocol_ResponseRafflePool_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LottoData.ResponseRafflePool.class, protocol.LottoData.ResponseRafflePool.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseRafflePool> PARSER =
        new com.google.protobuf.AbstractParser<ResponseRafflePool>() {
      public ResponseRafflePool parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseRafflePool(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseRafflePool> getParserForType() {
      return PARSER;
    }

    // repeated int32 poolId = 1;
    public static final int POOLID_FIELD_NUMBER = 1;
    private java.util.List<java.lang.Integer> poolId_;
    /**
     * <code>repeated int32 poolId = 1;</code>
     *
     * <pre>
     *对应raffle_type表格里的id
     * </pre>
     */
    public java.util.List<java.lang.Integer>
        getPoolIdList() {
      return poolId_;
    }
    /**
     * <code>repeated int32 poolId = 1;</code>
     *
     * <pre>
     *对应raffle_type表格里的id
     * </pre>
     */
    public int getPoolIdCount() {
      return poolId_.size();
    }
    /**
     * <code>repeated int32 poolId = 1;</code>
     *
     * <pre>
     *对应raffle_type表格里的id
     * </pre>
     */
    public int getPoolId(int index) {
      return poolId_.get(index);
    }

    private void initFields() {
      poolId_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < poolId_.size(); i++) {
        output.writeInt32(1, poolId_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < poolId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(poolId_.get(i));
        }
        size += dataSize;
        size += 1 * getPoolIdList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LottoData.ResponseRafflePool parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.ResponseRafflePool parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LottoData.ResponseRafflePool parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LottoData.ResponseRafflePool parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LottoData.ResponseRafflePool prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseRafflePool}
     *
     * <pre>
     *2150
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LottoData.ResponseRafflePoolOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LottoData.internal_static_protocol_ResponseRafflePool_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LottoData.internal_static_protocol_ResponseRafflePool_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LottoData.ResponseRafflePool.class, protocol.LottoData.ResponseRafflePool.Builder.class);
      }

      // Construct using protocol.LottoData.ResponseRafflePool.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        poolId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LottoData.internal_static_protocol_ResponseRafflePool_descriptor;
      }

      public protocol.LottoData.ResponseRafflePool getDefaultInstanceForType() {
        return protocol.LottoData.ResponseRafflePool.getDefaultInstance();
      }

      public protocol.LottoData.ResponseRafflePool build() {
        protocol.LottoData.ResponseRafflePool result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LottoData.ResponseRafflePool buildPartial() {
        protocol.LottoData.ResponseRafflePool result = new protocol.LottoData.ResponseRafflePool(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) == 0x00000001)) {
          poolId_ = java.util.Collections.unmodifiableList(poolId_);
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.poolId_ = poolId_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LottoData.ResponseRafflePool) {
          return mergeFrom((protocol.LottoData.ResponseRafflePool)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LottoData.ResponseRafflePool other) {
        if (other == protocol.LottoData.ResponseRafflePool.getDefaultInstance()) return this;
        if (!other.poolId_.isEmpty()) {
          if (poolId_.isEmpty()) {
            poolId_ = other.poolId_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePoolIdIsMutable();
            poolId_.addAll(other.poolId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LottoData.ResponseRafflePool parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LottoData.ResponseRafflePool) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated int32 poolId = 1;
      private java.util.List<java.lang.Integer> poolId_ = java.util.Collections.emptyList();
      private void ensurePoolIdIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          poolId_ = new java.util.ArrayList<java.lang.Integer>(poolId_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public java.util.List<java.lang.Integer>
          getPoolIdList() {
        return java.util.Collections.unmodifiableList(poolId_);
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public int getPoolIdCount() {
        return poolId_.size();
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public int getPoolId(int index) {
        return poolId_.get(index);
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public Builder setPoolId(
          int index, int value) {
        ensurePoolIdIsMutable();
        poolId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public Builder addPoolId(int value) {
        ensurePoolIdIsMutable();
        poolId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public Builder addAllPoolId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensurePoolIdIsMutable();
        super.addAll(values, poolId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 poolId = 1;</code>
       *
       * <pre>
       *对应raffle_type表格里的id
       * </pre>
       */
      public Builder clearPoolId() {
        poolId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseRafflePool)
    }

    static {
      defaultInstance = new ResponseRafflePool(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseRafflePool)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestCountLotto_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestCountLotto_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseCountLotto_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseCountLotto_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestRafflePool_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestRafflePool_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseRafflePool_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseRafflePool_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013lotto.proto\022\010protocol\032\nitem.proto\032\014com" +
      "mon.proto\032\tpet.proto\"?\n\021RequestCountLott" +
      "o\022\013\n\003num\030\001 \002(\005\022\017\n\007lottoId\030\002 \002(\005\022\014\n\004free\030" +
      "\003 \001(\005\"F\n\022ResponseCountLotto\022\017\n\007errorId\030\001" +
      " \002(\005\022\037\n\003pet\030\002 \003(\0132\022.protocol.PlainPet\"\023\n" +
      "\021RequestRafflePool\"$\n\022ResponseRafflePool" +
      "\022\016\n\006poolId\030\001 \003(\005B\013B\tLottoData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestCountLotto_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestCountLotto_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestCountLotto_descriptor,
              new java.lang.String[] { "Num", "LottoId", "Free", });
          internal_static_protocol_ResponseCountLotto_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseCountLotto_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseCountLotto_descriptor,
              new java.lang.String[] { "ErrorId", "Pet", });
          internal_static_protocol_RequestRafflePool_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestRafflePool_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestRafflePool_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseRafflePool_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseRafflePool_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseRafflePool_descriptor,
              new java.lang.String[] { "PoolId", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
          protocol.CommonData.getDescriptor(),
          protocol.PetData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
