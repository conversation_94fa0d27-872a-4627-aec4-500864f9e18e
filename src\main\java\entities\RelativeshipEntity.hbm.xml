<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.RelativeshipEntity" table="relativeship" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="type" column="type"/>
        <property name="roleuid1" column="roleuid1"/>
        <property name="roleuid2" column="roleuid2"/>
        <property name="value" column="value"/>
    </class>
</hibernate-mapping>