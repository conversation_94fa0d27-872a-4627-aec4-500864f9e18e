package server;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.DatagramPacket;
import io.netty.channel.socket.nio.NioDatagramChannel;
import io.netty.util.CharsetUtil;
import module.callback.CallBack;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MissionData;

import java.net.InetSocketAddress;

/**
 * Created by nara on 2018/5/23.
 */
public class UdpClient {
    private static Logger logger = LoggerFactory.getLogger(UdpClient.class);

    private static UdpClient inst = null;
    private static final int port = 8567;
    private static CallBack callBack = null;

    public static UdpClient getInstance() {
        if (inst == null){
            inst = new UdpClient();
        }
        return inst;
    }

    private static class ClientHandler extends SimpleChannelInboundHandler<DatagramPacket> {

        @Override
        protected void channelRead0(ChannelHandlerContext ctx, DatagramPacket msg) throws Exception {
//            String response = msg.content().toString(CharsetUtil.UTF_8);
//
//            if(response.startsWith("结果：")){
//                /// System.out.println(response);
//                ctx.close();
//            }
            SuperProtocol superProtocol = decoder(msg.content());
            if (callBack != null){
                callBack.execute(superProtocol);
            }
        }

        private SuperProtocol decoder(ByteBuf buffer){
            SuperProtocol protocol  = null;
            int BASE_LENGTH = 9;
            if (buffer.readableBytes() >= BASE_LENGTH) {
                if (buffer.readableBytes() > 2048) {
                    buffer.skipBytes(buffer.readableBytes());
                }
                int beginReader;
                while (true) {
                    beginReader = buffer.readerIndex();
                    buffer.markReaderIndex();
                    if (buffer.readByte() == 0X76) {
                        break;
                    }
                    buffer.resetReaderIndex();
                    buffer.readByte();
                    if (buffer.readableBytes() < BASE_LENGTH) {
                        return null;
                    }
                }
                int msgId = buffer.readInt();
                int length = buffer.readInt();
                if (buffer.readableBytes() < length) {
                    buffer.readerIndex(beginReader);
                    return null;
                }
                byte[] data = new byte[length];
                buffer.readBytes(data);
                protocol = new SuperProtocol(msgId,data.length, data);
            }
            return protocol;
        }
    }

    public void sendPackage(int msgId,byte[] bytes,CallBack callBack) {
        EventLoopGroup group = new NioEventLoopGroup();
        try {
            Bootstrap b = new Bootstrap();
            b.group(group)
                    .channel(NioDatagramChannel.class)
                    .option(ChannelOption.SO_BROADCAST, true)
                    .handler(new ClientHandler());

            Channel ch = b.bind(0).sync().channel();

            this.callBack = callBack;
//            MissionData.SRequestEnterMission.Builder sBuilder = MissionData.SRequestEnterMission.newBuilder();
//            sBuilder.setHall(1);
//            sBuilder.setRoomId(1);
//            sBuilder.setKey("123123");
//            byte[] bytes = sBuilder.build().toByteArray();
            SuperProtocol msg = new SuperProtocol(msgId,bytes.length,
                    bytes);
            ByteBuf out = Unpooled.buffer();
            out.writeByte(msg.getHead_data());
            out.writeInt(msg.getMsgId());
            out.writeInt(msg.getContentLength());
            out.writeBytes(msg.getContent());
            ch.writeAndFlush(new DatagramPacket(
                    Unpooled.copiedBuffer(out),
                    new InetSocketAddress("localhost", port))).sync();
            out.release();
            logger.info("Search, sendPackage()");
            // QuoteOfTheMomentClientHandler will close the DatagramChannel when a
            // response is received.  If the channel is not closed within 5 seconds,
            // print an error message and quit.
            // 等待10秒钟
            if (!ch.closeFuture().await(10000)) {
                logger.info("Search request timed out.");
            }
        }catch (Exception e){
            this.callBack = null;
            e.printStackTrace();
            logger.info("Search, An Error Occur ==>" + e);
        }finally {
            group.shutdownGracefully();
        }
    }
}
