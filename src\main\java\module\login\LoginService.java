package module.login;

import java.io.IOException;

import com.googlecode.protobuf.format.JsonFormat;

import common.SuperConfig;
import entities.*;
import io.netty.channel.ChannelHandlerContext;
import manager.*;
import model.*;
import module.item.IItem;
import module.item.ItemDao;
import module.item.ItemService;
import module.mission.MissionDao;
import module.playerstatus.PlayerCreateRole;
import module.role.RoleDao;
import module.room.IRoom;
import module.room.RoomDao;
import net.sf.json.JSONObject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import protocol.ItemData;
import protocol.ProtoData;
import protocol.TaskData;
import protocol.UserData;
import server.SuperProtocol;
import server.SuperServerHandler;
import utils.*;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.google.protobuf.InvalidProtocolBufferException;

/**
 * Created by nara on 2017/11/17.
 */
public class LoginService {
    private static Logger log = LoggerFactory.getLogger(LoginService.class);

    private static LoginService inst = null;

    public static LoginService getInstance() {
        if (inst == null) {
            inst = new LoginService();
        }
        return inst;
    }

    public byte[] getServerInfo() {
        UserData.ResponseServer.Builder builder = UserData.ResponseServer.newBuilder();
        try {
            ILogin iLogin = LoginDao.getInstance();
            List<UserData.Server> serverList = iLogin.getServerList(0);
            builder.addAllServerList(serverList);
            String announcement = iLogin.getAnnouncement();
            builder.setAnnouncement(announcement);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            return builder.build().toByteArray();
        }
        return builder.build().toByteArray();
    }

    public byte[] register(ChannelHandlerContext ctx, byte[] bytes) {
//        /// System.out.println("注册");
        UserData.RequestRegister requestRegister = null;
        UserData.ResponseRegister.Builder builder = UserData.ResponseRegister.newBuilder();
        try {
            requestRegister = UserData.RequestRegister.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestRegister == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {
            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(3);
            loginInfo.setCtx(ctx);
            loginInfo.setName(requestRegister.getAccount());
            loginInfo.setPwd(requestRegister.getPwd());
            String phone = "123456";
            if (requestRegister.hasPhone()) {
                phone = requestRegister.getPhone();
            }
            if (requestRegister.hasIDFA()) {
                loginInfo.setIDFA(requestRegister.getIDFA());
            }

            loginInfo.setPhone(phone);
            builder = null;
            MySql.loginList.add(loginInfo);
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] visitorRegister(ChannelHandlerContext ctx, byte[] bytes) {
        UserData.RequestVisitorOperation visitorOperation = null;
        UserData.ResponseVisitorOperation.Builder builder = UserData.ResponseVisitorOperation.newBuilder();
        try {
            visitorOperation = UserData.RequestVisitorOperation.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (visitorOperation.getType() == 1) {
            ILogin iLogin = LoginDao.getInstance();
            StringBuffer name = new StringBuffer(MyUtils.stringToMD5(System.currentTimeMillis() + ""));
            StringBuffer userName = new StringBuffer(name.substring(0, 10));
            StringBuffer prefixName = new StringBuffer("start_");
            String password = MyUtils.stringToMD5(userName.toString()).substring(0, 10);
            StringBuffer sql = new StringBuffer(" from UserEntity where openid='").append(prefixName.append(userName)).append("'");
            Object object = MySql.queryForOne(sql.toString());
            while (object != null) {
                name = userName.reverse();
                userName = new StringBuffer(MyUtils.stringToMD5(name.toString()).substring(0, 10));
                sql = new StringBuffer(" from UserEntity where openid='").append(new StringBuffer("start_").append(userName)).append("'");
                object = MySql.queryForOne(sql.toString());
            }
            String idfa = visitorOperation.getIDFA();
            int val = iLogin.visitorRegister(new StringBuffer("start_").append(userName).toString(), password, "", idfa);//idfa允许null
            builder.setType(visitorOperation.getType());
            builder.setErrorId(0);
            builder.setUserName(userName.toString());
            builder.setPassword(password);
        } else if (visitorOperation.getType() == 2) {
            String sql = "from UserEntity where openid='" + visitorOperation.getLastUserName() + "' and pwd='" + visitorOperation.getLastPassword() + "'";
            Object userInfo = MySql.queryForOne(sql);
            if (userInfo == null) {
                builder.setErrorId(ProtoData.ErrorCode.ACCOUNTPWDERROR_VALUE);
                //  /// System.err.println( ProtoData.ErrorCode.ACCOUNTPWDERROR_VALUE+"userInfo==null");
                builder.setType(2);
            } else {
                String hql = "from UserEntity where openid='" + visitorOperation.getNowUserName() + "'";
                Object userInfo1 = MySql.queryForOne(hql);
                if (userInfo1 != null) {
                    //   /// System.err.println( ProtoData.ErrorCode.ACCOUNTEXIST_VALUE+"userInfo1 != null");
                    builder.setErrorId(ProtoData.ErrorCode.ACCOUNTEXIST_VALUE);
                    builder.setType(2);
                } else {
                    UserEntity userEntity = (UserEntity) userInfo;
                  /*userEntity.setOpenid(visitorOperation.getNowUserName());
                  userEntity.setPwd(visitorOperation.getNowPassword());
                  userEntity.setVisitor(0);*/
                    //  MySql.update(userEntity);

                    String mustHql = "update user set openid='" + visitorOperation.getNowUserName() + "',pwd='" + visitorOperation.getNowPassword() + "', visitor=0 where userid='" + userEntity.getUserid() + "'";

                    Connection connection = null;
                    Statement statement = null;
                    try {
                        connection = JdbcUtils.getConnection();
                        statement = connection.createStatement();
                    } catch (ClassNotFoundException e) {
                        e.printStackTrace();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                    try {
                        statement.executeUpdate(mustHql);
                    } catch (SQLException e) {
                        e.printStackTrace();
//                      /// System.out.println("sql语句错误");
                    }
                    try {
                        statement.close();
                        connection.close();
                    } catch (SQLException e) {
                        e.printStackTrace();
                    }
                    Redis jedis = Redis.getInstance();
                    Map userMap = jedis.hgetAll("user:" + ((UserEntity) userInfo).getUserid());
                    if (userMap != null && !userMap.isEmpty()) {
                        jedis.hset("user:" + ((UserEntity) userInfo).getUserid(), "pwd", visitorOperation.getNowPassword());
                        jedis.hset("user:" + ((UserEntity) userInfo).getUserid(), "openid", visitorOperation.getNowUserName());
                    }
                    builder.setUserName(visitorOperation.getNowUserName().substring(6));
                    builder.setPassword(visitorOperation.getNowPassword());
                    builder.setType(2);
                    builder.setErrorId(0);
                    //   /// System.out.println("ok~~~~~~~~~~~~~~~~~~~");
                }
            }
        }
        return builder.build().toByteArray();
    }


    public void register(LoginInfo loginInfo) {
        try {
            UserData.ResponseRegister.Builder builder = UserData.ResponseRegister.newBuilder();
            ILogin iLogin = LoginDao.getInstance();
            int val = iLogin.register(loginInfo.getName(), loginInfo.getPwd(), loginInfo.getPhone(), loginInfo.getIDFA());
            builder.setErrorId(val);
            byte[] bytes = builder.build().toByteArray();
            SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSEREGISTER_VALUE, bytes.length, bytes);
            loginInfo.getCtx().writeAndFlush(response);
        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
    }

    public byte[] getLoginUserInfo(ChannelHandlerContext ctx, byte[] bytes) {
        // /// System.out.println("=================loginService===============");
        UserData.RequestLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestLogin.parseFrom(bytes);
            System.out.println("1001：" + requestLogin);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {
//            RoleInfo roleInfo = iLogin.getLoginRoleInfo(requestLogin.getServerId(), requestLogin.getType(), requestLogin.getParam());
//            if (roleInfo != null && roleInfo.getType() == 9){
//                log.error(roleInfo.getUid()+":[getLo
// ginUserInfo] error 1>>>"+roleInfo.getName()+" ban to login");
//                builder.setErrorId(ProtoData.ErrorCode.ACCOUNTERROR_VALUE);
//            }else {
//                UserData.Role role = iLogin.roleInfoToUserData(roleInfo);
//                if (role != null){
//                    builder.setRole(role);
////                if (SuperServerHandler.onLineRoleMap.containsKey(roleInfo.getUid()) == true){
////                    ReportManager.reportRepeatLogin(SuperServerHandler.onLineRoleMap.get(roleInfo.getUid()));
////                }
////                SuperServerHandler.onLineRoleMap.put(roleInfo.getUid(),ctx);
//                    ChannelHandlerContext otherCtx = SuperServerHandler.getCtxFromUid(roleInfo.getUid());
//                    if (otherCtx != null){
//                        ReportManager.reportRepeatLogin(otherCtx);
//                        SuperServerHandler.removeCtxFromRoleMap(otherCtx,roleInfo.getUid());
//                    }
//                    SuperServerHandler.addCtxToRoleMap(ctx,roleInfo.getUid());
//                    Redis.setRoleOnLine(roleInfo.getUid());
//                }else {
//                    String userId = iLogin.getUserIdFromRedis(requestLogin.getServerId(), requestLogin.getType(), requestLogin.getParam());
//                    if (userId == null){
//                        log.error(roleInfo.getUid()+":[getLoginUserInfo] error 2");
//                        builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
//                    }else {
//                        SuperServerHandler.newUserMap.put(ctx, userId);
//                    }
//                }
//            }
            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(1);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setType(requestLogin.getType());
            loginInfo.setParam(requestLogin.getParam());
            loginInfo.setPwd(requestLogin.getPwd());
            MySql.loginList.add(loginInfo);
            builder=null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] getLoginWeChatUserInfo(ChannelHandlerContext ctx, byte[] bytes) {
//        /// System.out.println("=================getLoginWeChatUserInfo===============");
        UserData.RequestWeChatLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestWeChatLogin.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(4);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setParam(requestLogin.getCode());
            if (requestLogin.hasIDFA()) {
                loginInfo.setIDFA(requestLogin.getIDFA());
            }
            loginInfo.setType(0);

            MySql.loginList.add(loginInfo);
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }


    public byte[] getLoginAppleUserInfo(ChannelHandlerContext ctx, byte[] bytes) {
//      /// System.out.println("=================getLoginAppleUserInfo===============");
        UserData.RequestAppleLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestAppleLogin.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(5);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setParam(requestLogin.getIdentityToken());
            loginInfo.setUserId(requestLogin.getUserID());
            loginInfo.setType(0);
            if (requestLogin.hasIDFA()) {
                loginInfo.setIDFA(requestLogin.getIDFA());
            }
            MySql.loginList.add(loginInfo);
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();

    }

    public byte[] getLoginQQUserInfo(ChannelHandlerContext ctx, byte[] bytes) {
//    /// System.out.println("=================getLoginQQUserInfo===============");
        UserData.RequestQQLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestQQLogin.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(6);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setUserId(requestLogin.getOpenID());
            loginInfo.setType(0);
            if (requestLogin.hasIDFA()) {
                loginInfo.setIDFA(requestLogin.getIDFA());
            }
            MySql.loginList.add(loginInfo);
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] getLoginYSDKUserInfo(ChannelHandlerContext ctx, byte[] bytes) {
//        /// System.out.println("=================getLoginYSDKUserInfo===============");
        UserData.RequestYSDKLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestYSDKLogin.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(7);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setUserId(requestLogin.getOpenId());
            loginInfo.setType(0);
            if (requestLogin.hasIDFA()) {
                loginInfo.setIDFA(requestLogin.getIDFA());
            }
            MySql.loginList.add(loginInfo);
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] getLoginHuaWeiUserInfo(ChannelHandlerContext ctx, byte[] bytes) {
//        /// System.out.println("=================getLoginHuaWeiUserInfo===============");
        UserData.RequestHuaWeiLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestHuaWeiLogin.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(8);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setUserId(requestLogin.getAuthorizationCode());
            loginInfo.setType(0);
            if (requestLogin.hasIDFA()) {
                loginInfo.setIDFA(requestLogin.getIDFA());
            }
            MySql.loginList.add(loginInfo);
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] getLoginGoogleInfo(ChannelHandlerContext ctx, byte[] bytes) 
    {
        System.out.println("=================getLoginGoogleInfo===============");
        UserData.RequestGoogleLogin requestLogin = null;
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        try {
            requestLogin = UserData.RequestGoogleLogin.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        if (requestLogin == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        } else {

            LoginInfo loginInfo = new LoginInfo();
            loginInfo.setLoginType(9);
            loginInfo.setCtx(ctx);
            loginInfo.setServerId(requestLogin.getServerId());
            loginInfo.setUserId(requestLogin.getGoogleId());
            loginInfo.setType(0);

            MySql.loginList.add(loginInfo);
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }
        

    public void getLoginUserInfo(LoginInfo loginInfo) {

        try {
            UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();
            builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
            ILogin iLogin = LoginDao.getInstance();
            LoginJudgeAccount account = iLogin.getLoginRoleInfo(loginInfo.getServerId(), loginInfo.getType(), loginInfo.getParam(), loginInfo.getPwd());
            if (account.getErrorId() != 0) {
                builder.setErrorId(account.getErrorId());
            } else {
                RoleInfo roleInfo = account.getRoleInfo();
                if (roleInfo != null && roleInfo.getType() == 9) {
                    log.error(roleInfo.getUid() + ":[getLoginUserInfo] error 1>>>" + roleInfo.getName() + " ban to login");
                    builder.setErrorId(ProtoData.ErrorCode.ACCOUNTERROR_VALUE);
                } else {
                    UserData.Role role = iLogin.roleInfoToUserData(roleInfo);
                    if (role != null) {
                        builder.setRole(role);
//                if (SuperServerHandler.onLineRoleMap.containsKey(roleInfo.getUid()) == true){
//                    ReportManager.reportRepeatLogin(SuperServerHandler.onLineRoleMap.get(roleInfo.getUid()));
//                }
//                SuperServerHandler.onLineRoleMap.put(roleInfo.getUid(),ctx);
                        ChannelHandlerContext otherCtx = SuperServerHandler.getServerIdCtxFromUid(roleInfo.getUid());
                        if (otherCtx != null) {
                            SuperServerHandler.removeCtxFromRoleMap(otherCtx, roleInfo.getUid());
                            ReportManager.reportRepeatLogin(otherCtx);
                        }
                        Map<String, ChannelHandlerContext> roleCtxPool = SuperServerHandler.rolectx;
                        if (!roleCtxPool.containsKey(roleInfo.getUid())) {
                            roleCtxPool.put(roleInfo.getUid(), loginInfo.getCtx());
                        }
                        SuperServerHandler.addCtxToRoleMap(loginInfo.getCtx(), roleInfo.getUid());
                        Redis.setRoleOnLine(roleInfo.getUid());
                    } else {
                        String userId = account.getUserEntity().getUserid();
                        if (userId == null) {
                            log.error(roleInfo.getUid() + ":[getLoginUserInfo] error 2");
                            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                        } else {
                            SuperServerHandler.newUserMap.put(loginInfo.getCtx(), userId);
                        }
                    }
                }
            }
            byte[] bytes = builder.build().toByteArray();
            SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSELOGIN_VALUE, bytes.length, bytes);
            loginInfo.getCtx().writeAndFlush(response);

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
    }

    public void WeChatLogin(LoginInfo loginInfo) {
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();

        String code = loginInfo.getParam();
        Map<String, String> params = new HashMap<String, String>();
        params.put("appid", "wxf43ac15bc6a2322d");
        params.put("secret", "0e9d5b0598b0f8252f103af2ce64fd95");
        params.put("grant_type", "authorization_code");
        params.put("code", code);
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token";
        JSONObject jsonObject = null;
        try {
            jsonObject = HttpClientUtils.getHttpsRequestSingleton().sendGet(url, params);
        } catch (IOException e) {
            jsonObject = null;
            e.printStackTrace();
        }
        if (jsonObject != null) {
            String openID = jsonObject.getString("openid");

            loginInfo.setUserId(openID);
            loginInfo.setPwd("123456");
            loginInfo.setPhone("***********");

            PlatformLogin(loginInfo, builder);
        } else {
            builder.setTimeStamp(TimerHandler.nowTimeStamp + "");

            builder.setErrorId(ProtoData.ErrorCode.ACCOUNTERROR_VALUE);

            byte[] bytes = builder.build().toByteArray();
            SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSELOGIN_VALUE, bytes.length, bytes);

            loginInfo.getCtx().writeAndFlush(response);
        }
    }

    public void AppleLogin(LoginInfo loginInfo) {
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();

        String identityToken = loginInfo.getParam();
        Map<String, Object> result = SignInWithAppleHelper.verify(identityToken);
        if (result != null) {
            loginInfo.setUserId(loginInfo.getUserId());
            loginInfo.setPwd("123456");
            loginInfo.setPhone("***********");

            PlatformLogin(loginInfo, builder);
        } else {
            builder.setTimeStamp(TimerHandler.nowTimeStamp + "");

            builder.setErrorId(ProtoData.ErrorCode.ACCOUNTERROR_VALUE);

            byte[] bytes = builder.build().toByteArray();
            SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSELOGIN_VALUE, bytes.length, bytes);

            loginInfo.getCtx().writeAndFlush(response);
        }
    }

    public void QQLogin(LoginInfo loginInfo) {
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();

        loginInfo.setUserId(loginInfo.getUserId());
        loginInfo.setPwd("123456");
        loginInfo.setPhone("***********");

        PlatformLogin(loginInfo, builder);
    }

    public void YSDKLogin(LoginInfo loginInfo) {
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();

        loginInfo.setUserId(loginInfo.getUserId());
        loginInfo.setPwd("123456");
        loginInfo.setPhone("***********");

        PlatformLogin(loginInfo, builder);
    }

    public void HuaWeiLogin(LoginInfo loginInfo) {
//        /// System.out.println("HuaWeiLogin !");
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();

        String authorizationCode = loginInfo.getUserId();

        String unionID = HuaWeiUtil.getUnionID(authorizationCode);
        if (unionID != null) {
            loginInfo.setUserId(unionID);
            loginInfo.setPwd("123456");
            loginInfo.setPhone("***********");

            PlatformLogin(loginInfo, builder);
        } else {
            builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
            builder.setErrorId(ProtoData.ErrorCode.ACCOUNTERROR_VALUE);

            byte[] bytes = builder.build().toByteArray();
            SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSELOGIN_VALUE, bytes.length, bytes);

            loginInfo.getCtx().writeAndFlush(response);
        }
    }

    public void GoogleLogin(LoginInfo loginInfo) {
        UserData.ResponseLogin.Builder builder = UserData.ResponseLogin.newBuilder();

        String googleId = loginInfo.getUserId();

        loginInfo.setUserId(googleId);
        loginInfo.setPwd("123456");
        loginInfo.setPhone("***********");

        PlatformLogin(loginInfo, builder);
        
    }

    public void PlatformLogin(LoginInfo loginInfo, UserData.ResponseLogin.Builder builder) {

        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");

        ILogin iLogin = LoginDao.getInstance();
        LoginJudgeAccount account = iLogin.getLoginRoleInfo(loginInfo.getServerId(), loginInfo.getType(), loginInfo.getUserId(), loginInfo.getPwd());
        if (account.getErrorId() == ProtoData.ErrorCode.ACCOUNTNAMEERROR_VALUE) {
            //builder.setErrorId(account.getErrorId());
            int val = iLogin.register(loginInfo.getUserId(), loginInfo.getPwd(), loginInfo.getPhone(), loginInfo.getIDFA());
            if (val != 0) return;

            account = iLogin.getRoleAllInfoFromDB(loginInfo.getServerId(), loginInfo.getType(), loginInfo.getUserId(), loginInfo.getPwd());
        }


        RoleInfo roleInfo = account.getRoleInfo();

        if (roleInfo != null && roleInfo.getType() == 9) {
            log.error(roleInfo.getUid() + ":[getLoginUserInfo] error 1>>>" + roleInfo.getName() + " ban to login");
            builder.setErrorId(ProtoData.ErrorCode.ACCOUNTERROR_VALUE);
        } else {
            UserData.Role role = iLogin.roleInfoToUserData(roleInfo);

            if (role != null) {
                builder.setRole(role);
                ChannelHandlerContext otherCtx = SuperServerHandler.getServerIdCtxFromUid(roleInfo.getUid());
                if (otherCtx != null) {
                    ReportManager.reportRepeatLogin(otherCtx);
                    SuperServerHandler.removeCtxFromRoleMap(otherCtx, roleInfo.getUid());
                }
                SuperServerHandler.addCtxToRoleMap(loginInfo.getCtx(), roleInfo.getUid());
                Redis.setRoleOnLine(roleInfo.getUid());
            } else {
                String userId = account.getUserEntity().getUserid();
                if (userId == null) {
                    log.error(roleInfo.getUid() + ":[getLoginUserInfo] error 2");
                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                } else {
                    SuperServerHandler.newUserMap.put(loginInfo.getCtx(), userId);
                }
            }
        }

        byte[] bytes = builder.build().toByteArray();
        SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSELOGIN_VALUE, bytes.length, bytes);
        // /// System.out.println(loginInfo.getCtx().isRemoved());
        loginInfo.getCtx().writeAndFlush(response);
    }

    public byte[] createRole(ChannelHandlerContext ctx, String userId, byte[] bytes) {
        UserData.RequestCreate requestCreate = null;
        UserData.ResponseRole.Builder builder = UserData.ResponseRole.newBuilder();
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        if (userId == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestCreate = UserData.RequestCreate.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }


            if (requestCreate == null) {
                log.error(userId + ":[createRole] error");
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            } else {
//                ILogin iLogin = LoginDao.getInstance();
//                int errorId = iLogin.judgeName(requestCreate.getName());
//                if (errorId == 0){
//                    RoleInfo roleInfo = iLogin.setInitRoleInfo(userId, requestCreate.getName());
//                    if (roleInfo != null && SuperServerHandler.newUserMap.containsKey(ctx) == true) {
//                        SuperServerHandler.newUserMap.remove(ctx);
////                        SuperServerHandler.onLineRoleMap.put(roleInfo.getUid(), ctx);
//                        SuperServerHandler.addCtxToRoleMap(ctx,roleInfo.getUid());
//                        Redis.setRoleOnLine(roleInfo.getUid());
//                    }
//                    UserData.Role role = iLogin.roleInfoToUserData(roleInfo);
//                    iLogin.setRoleToUser(roleInfo);
//                    builder.setRole(role);
//                }else {
//                    builder.setErrorId(errorId);
//                }
                LoginInfo loginInfo = new LoginInfo();
                loginInfo.setLoginType(2);
                loginInfo.setCtx(ctx);
                loginInfo.setUserId(userId);
                loginInfo.setName(requestCreate.getName());
                MySql.loginList.add(loginInfo);
//                PlayerCreateRole.getInstance().SendMessage(userId);
            }
            builder = null;
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public void createRole(LoginInfo loginInfo) {
        try {
            UserData.ResponseRole.Builder builder = UserData.ResponseRole.newBuilder();
            builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
            ILogin iLogin = LoginDao.getInstance();
            int errorId = iLogin.judgeName(loginInfo.getName());
            String uid = null;
            if (errorId == 0) {
                RoleInfo roleInfo = iLogin.setInitRoleInfo(loginInfo.getUserId(), loginInfo.getName());
                if (roleInfo != null) {
                    SuperServerHandler.addCtxToRoleMap(loginInfo.getCtx(), roleInfo.getUid());
                }
                if (roleInfo != null && SuperServerHandler.newUserMap.containsKey(loginInfo.getCtx()) == true) {
                    SuperServerHandler.newUserMap.remove(loginInfo.getCtx());
                    Redis.setRoleOnLine(roleInfo.getUid());
                }
                UserData.Role role = iLogin.roleInfoToUserData(roleInfo);
                uid = roleInfo.getUid();
                //            ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, 3);
//            if (itemEntity == null){
//                double a = 31;
//                itemEntity  = new ItemEntity();
//                itemEntity.setUid(uid);
//                itemEntity.setItemid(3);
//                itemEntity.setItemnum(a);
//                MySql.insert(itemEntity);
//            }else {
//                double a = 31;
//                itemEntity.setUid(s);
//                itemEntity.setItemid(3);
//                itemEntity.setItemnum(a);
//                MySql.insert(itemEntity);
//            }
                Map<String, ChannelHandlerContext> roleCtxPool = SuperServerHandler.rolectx;
                if (!roleCtxPool.containsKey(uid)) {
                    roleCtxPool.put(uid, loginInfo.getCtx());
                }
                iLogin.setRoleToUser(roleInfo);
                builder.setRole(role);
                builder.setErrorId(0);
            } else {
                builder.setErrorId(errorId);
            }
            byte[] bytes = builder.build().toByteArray();
            PlayerCreateRole.getInstance().SendMessage(builder.getRole().getUid());
            SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSECREATE_VALUE, bytes.length, bytes);
//            /// System.out.println(response+"创建角色"+JsonFormat.printToString(builder.build()));
            loginInfo.getCtx().writeAndFlush(response);
            //
            if (errorId == 0) {/*
                ItemData.ResponsePlayerItem.Builder builder1= ItemData.ResponsePlayerItem.newBuilder();
                builder1.setErrorId(0);
                List itemInfoList=ItemDao.getInstance().getplayerItem(uid);
                for(int i=0;i<itemInfoList.size();i++){
                    ItemEntity entity=(ItemEntity)itemInfoList.get(i);
                    builder1.addItems(ItemUtils.entityToPBData(entity));
                }
                byte[] bytes1 = builder1.build().toByteArray();
                SuperProtocol response1 = new SuperProtocol(ProtoData.SToC.RESPOSEPLAYERITEMS_VALUE,bytes1.length, bytes1);
                loginInfo.getCtx().writeAndFlush(response1);*/
                ////
        /*        PetData.ResponseOperatePet.Builder  updatePetbuild=PetData.ResponseOperatePet.newBuilder();
                updatePetbuild.setType(1);
                updatePetbuild.setErrorId(0);
                for(int i=0;i<6;i++){
                    PetData.Pet pet =PetEntity.entityToPb(PetUtils.createPet(uid,i+1,PetConfig.PetFromCompose));
                    updatePetbuild.addPet(pet);
                    ReportManager.reportInfo(uid,ProtoData.SToC.RESPOSEOPERATEPET_VALUE,updatePetbuild.build().toByteArray());
                }
*/
                //   Map<Integer, List<ItemInfo>> itemMap = ItemDao.getInstance().initItemData(uid);

            }


        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
    }

    public byte[] reconnection(ChannelHandlerContext ctx, String uid, byte[] bytes) {
        UserData.RequestReconnection requestReconnection = null;
        UserData.ResponseReconnection.Builder builder = UserData.ResponseReconnection.newBuilder();
        try {
            requestReconnection = UserData.RequestReconnection.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
     /*   ChannelHandlerContext otherCtx =SuperServerHandler.getCtxFromUid(requestReconnection.getUid());
        if(otherCtx!=null){
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            log.error(uid + ":重复登陆");
        }else {*/
        if (requestReconnection == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            log.error(uid + ":[reconnection] error");
        } else {
            if (uid != null) {
                builder.setErrorId(0);
            } else {
                boolean bo = Redis.judgeRoleCacheExit(requestReconnection.getUid());
                if (bo == true) {
                    SuperServerHandler.addCtxToRoleMap(ctx, requestReconnection.getUid());
                    Redis.setRoleOnLine(requestReconnection.getUid());
                    builder.setErrorId(0);
                } else {
                    builder.setErrorId(ProtoData.ErrorCode.TOOLONGTIMEFORCONNECT_VALUE);
                }
            }
        }
        // }
        return builder.build().toByteArray();
    }

    public byte[] setRoleBaseInfo(String uid, byte[] bytes) {
        UserData.RequestSetInfo requestSetInfo = null;
        UserData.ResponseSetInfo.Builder builder = UserData.ResponseSetInfo.newBuilder();

        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);

        } else {
            try {
                requestSetInfo = UserData.RequestSetInfo.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestSetInfo == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[setRoleBaseInfo] error");
            } else {
                //如果客户端发来的数据不为空对数据进行处理
                ILogin iLogin = LoginDao.getInstance();
                builder = iLogin.setRoleBaseInfo(uid, requestSetInfo.getType(), requestSetInfo.getInfo());
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] chooseRole(String uid, byte[] bytes) {
        UserData.RequestChooseRole requestChooseRole = null;
        UserData.ResponseChooseRole.Builder builder = UserData.ResponseChooseRole.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestChooseRole = UserData.RequestChooseRole.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestChooseRole == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[chooseRole] error");
            } else {
                ILogin iLogin = LoginDao.getInstance();
                builder = iLogin.chooseRole(uid, requestChooseRole.getType(), requestChooseRole.getRoleId(), requestChooseRole.getPayChoice());
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] changeDress(String uid, byte[] bytes) {
        UserData.RequestChangeDress requestChangeDress = null;
        UserData.ResponseChangeDress.Builder builder = UserData.ResponseChangeDress.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestChangeDress = UserData.RequestChangeDress.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestChangeDress == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[changeDress] error");
            } else {
                ILogin iLogin = LoginDao.getInstance();
                int n = iLogin.changeDress(uid, requestChangeDress.getPart());
                if (n == 1) {
                    builder.setStatus(true);
                } else {
                    builder.setStatus(false);
                    if (n > 1) {
                        builder.setErrorId(n);
                    }
                }
                builder.setPart(requestChangeDress.getPart());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] buyClothing(String uid, byte[] bytes) {
        UserData.RequestBuyClothing requestBuyClothing = null;
        UserData.ResponseBuyClothing.Builder builder = UserData.ResponseBuyClothing.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestBuyClothing = UserData.RequestBuyClothing.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestBuyClothing == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[buyClothing] error");
            } else {
                ILogin iLogin = LoginDao.getInstance();
                builder = iLogin.buyClothing(uid, requestBuyClothing.getBuyType(), requestBuyClothing.getClothingListList());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] changeCupboard(String uid, byte[] bytes) {
        UserData.RequestChangeCupboard requestChangeCupboard = null;
        UserData.ResponseChangeCupboard.Builder builder = UserData.ResponseChangeCupboard.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestChangeCupboard = UserData.RequestChangeCupboard.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestChangeCupboard == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[changeCupboard] error");
            } else {
                ILogin iLogin = LoginDao.getInstance();
                builder = iLogin.changeCupboard(uid, requestChangeCupboard.getId(), requestChangeCupboard.getType(), requestChangeCupboard.getPart());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] cupboardToBody(String uid, byte[] bytes) {
        UserData.RequestCupboardToBody requestCupboardToBody = null;
        UserData.ResponseCupboardToBody.Builder builder = UserData.ResponseCupboardToBody.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestCupboardToBody = UserData.RequestCupboardToBody.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestCupboardToBody == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[cupboardToBody] error");
            } else {
                ILogin iLogin = LoginDao.getInstance();
                int val = iLogin.cupboardToBody(uid, requestCupboardToBody.getId());
                if (val == 0) {
                    builder.setStatus(true);
                } else {
                    builder.setStatus(false);
                    builder.setErrorId(val);
                }
                builder.setId(requestCupboardToBody.getId());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] starbiToMoney(String uid) {
        UserData.ResponseStarbiToMoney.Builder builder = UserData.ResponseStarbiToMoney.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            ILogin iLogin = LoginDao.getInstance();
            builder = iLogin.starbiToMoney(uid);
        }
        return builder.build().toByteArray();
    }

    public byte[] stationLocation(String uid, byte[] bytes, ChannelHandlerContext ctx) {
        UserData.RequestStationLocation requestStationLocation = null;
        if (uid != null) {
            try {
                requestStationLocation = UserData.RequestStationLocation.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestStationLocation != null) {
                int type = requestStationLocation.getType();
                if (type == 1) {//车站走动
                    ILogin iLogin = LoginDao.getInstance();
                    iLogin.stationLocation(ctx, uid, requestStationLocation.getPoint(), requestStationLocation.getDirection(), requestStationLocation.getSpeed());
                } else {//房间走动
                    IRoom iRoom = RoomDao.getInstance();
                    iRoom.moveInRoom(uid, requestStationLocation.getPoint(), requestStationLocation.getDirection(), requestStationLocation.getSpeed());
                }
            }
        }

        return null;
    }

    public byte[] operateView(String uid, byte[] bytes) {
        UserData.RequestOperateView requestOperateView = null;
        if (uid != null) {
            try {
                requestOperateView = UserData.RequestOperateView.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestOperateView != null) {
                ILogin iLogin = LoginDao.getInstance();
                iLogin.operateView(uid, requestOperateView.getView());
            }
        }
        return null;
    }

    public byte[] getTimeStamp() {
        UserData.ResponseStamp.Builder builder = UserData.ResponseStamp.newBuilder();
        builder.setTimeStamp(System.currentTimeMillis() + "");
        return builder.build().toByteArray();
    }

    //新手引导1014
    public byte[] updateNewuser(String uid, byte[] bytes) {
        UserData.RequestUpdateNewUser requestUpdateNewUser = null;
        UserData.ResponseUpdateNewUser.Builder builder = UserData.ResponseUpdateNewUser.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
            log.error(uid + ":[updateNewuser] error");
        } else {
            try {
                requestUpdateNewUser = UserData.RequestUpdateNewUser.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestUpdateNewUser == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[updateNewuser] error");
            } else {
                ILogin iLogin = LoginDao.getInstance();
                boolean bo = iLogin.updateNewUser(uid, requestUpdateNewUser.getAdvance());
                builder.setErrorId(0);
                builder.setAdvance(requestUpdateNewUser.getAdvance());
                if (bo == false) {
                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                }
            }
        }
        return builder.build().toByteArray();
    }

    ///////////////////////////////////////////////////////////////////////////////////////////
    public byte[] setPlayerType(byte[] bytes, ChannelHandlerContext ctx) {
        String string = null;
        try {
            string = new String(bytes, "UTF-8");
        } catch (Exception e) {
            return "analyze error!".getBytes();
        }

        JSONObject jsonObject = JSONObject.fromObject(string);
        ILogin iLogin = LoginDao.getInstance();
        iLogin.setPlayerType(jsonObject, ctx);
        return null;
    }

    public byte[] getGameData(byte[] bytes) {
        String string = null;
        try {
            string = new String(bytes, "UTF-8");
        } catch (Exception e) {
            return "analyze error!".getBytes();
        }

        String result = null;
        JSONObject jsonObject = JSONObject.fromObject(string);
        int type = jsonObject.getInt("type");
        if (type == 4) {
            result = UserDataManager.getGameData(jsonObject.getString("baseDay"), jsonObject.getString("theDay")) + "";
        } else {
            result = UserDataManager.getGameData(type) + "";
        }
        return result.getBytes();
    }

    public byte[] getOnlinePlayers(byte[] bytes) {
        String string = null;
        try {
            string = new String(bytes, "UTF-8");
        } catch (Exception e) {
            return "analyze error!".getBytes();
        }

        JSONObject jsonObject = JSONObject.fromObject(string);
        Map<String, ChannelHandlerContext> map = SuperServerHandler.serverRoleMap.get(jsonObject.getInt("server"));
        int num = map == null ? 0 : map.size();
        String result = num + "";
        return result.getBytes();
    }
//
//    public byte[] approval(byte[] bytes, String uid) {
//        UserData.RequestApproval requestApproval = null;
//        UserData.ResponseApproval.Builder appBuilder = UserData.ResponseApproval.newBuilder();
//        try {
//            requestApproval = UserData.RequestApproval.parseFrom(bytes);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        ILogin loginDao = LoginDao.getInstance();
//        appBuilder = loginDao.approval(uid, requestApproval.getRecognizedUid(), requestApproval.getType());
//        return appBuilder.build().toByteArray();
//    }

    public byte[] authentication(byte[] bytes, String uid) {
        UserData.ResponseAuthentication.Builder builder = UserData.ResponseAuthentication.newBuilder();
        UserData.RequestAuthentication requestAuthentication = null;
        try {
            requestAuthentication = UserData.RequestAuthentication.parseFrom(bytes);
            StringBuffer hql = new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity role = (RoleEntity) MySql.queryForOne(hql.toString());
            StringBuffer sql = new StringBuffer("update UserEntity set name='").append(requestAuthentication.getName()).append("',idcard='")
                    .append(requestAuthentication.getIdcard()).append("', authentication=1 where userid='")
                    .append(role.getUserid()).append("'");
            MySql.updateSomes(sql.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        builder.setErrorId(0);
        return builder.build().toByteArray();
    }


    public byte[] queryRoleInformation(byte[] bytes, String uid) {
        UserData.ResponseQueryRoleInformation.Builder builder = UserData.ResponseQueryRoleInformation.newBuilder();
        UserData.RequestQueryRoleInformation requestQueryRoleInformation = null;
        try {
            requestQueryRoleInformation = UserData.RequestQueryRoleInformation.parseFrom(bytes);
            ILogin loginDao = LoginDao.getInstance();
            builder = loginDao.queryRoleInformation(requestQueryRoleInformation.getUid());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();

    }

    public byte[] getLuckyItem(byte[] bytes, String uid) {
        UserData.ResponseGetLuckyItem.Builder builder = UserData.ResponseGetLuckyItem.newBuilder();
        UserData.RequestGetLuckyItem requestGetLuckyItem = null;
        try {
            requestGetLuckyItem = UserData.RequestGetLuckyItem.parseFrom(bytes);
            builder = LoginDao.getInstance().getLuckyItem(uid, requestGetLuckyItem.getItemId());
        } catch (Exception e) {
            e.printStackTrace();
        }

        return builder.build().toByteArray();
    }

    public byte[] finishActivities(byte[] bytes, String uid) {
        TaskData.ResponseFinishActivities.Builder builder = TaskData.ResponseFinishActivities.newBuilder();
        TaskData.RequestFinishActivities requestFinishActivities = null;
        try {
            requestFinishActivities = TaskData.RequestFinishActivities.parseFrom(bytes);
            builder = LoginDao.getInstance().finishActivities(uid, requestFinishActivities.getActivitiesId());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }


    public byte[] gameMatch(byte[] bytes, String uid) {
        MissionDao.userMacthPool.add("");
        return null;
    }

    public byte[] getMarketInformation(String uid) {
        UserData.ResponseMarketInformation.Builder builder = null;
        ILogin login = LoginDao.getInstance();
        builder = login.getMarketInformation(uid);
        return builder.build().toByteArray();
    }

    public byte[] goodsOperate(byte[] bytes, String uid) {
        UserData.ResponseGoodsOperate.Builder builder = UserData.ResponseGoodsOperate.newBuilder();
        UserData.RequestGoodsOperate request = null;
        try {
            request = UserData.RequestGoodsOperate.parseFrom(bytes);
            ILogin loginDao = LoginDao.getInstance();
            builder = loginDao.goodsOperate(request.getType(), request.getGoodsId(), request.getNumber(), uid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] tickteExchange(byte[] bytes, String uid) {
        UserData.ResponseTickteExchange.Builder builder = UserData.ResponseTickteExchange.newBuilder();
        UserData.RequestTickteExchange request = null;
        try {
            request = UserData.RequestTickteExchange.parseFrom(bytes);
            ILogin loginDao = LoginDao.getInstance();
            builder = loginDao.tickteExchange(request.getId(), request.getNums(), uid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] waitItem(byte[] bytes, String uid) {
        UserData.RequestWaitItem requestWaitItem = null;
        UserData.ResponseWaitItem.Builder builder = null;
        try {
            requestWaitItem = UserData.RequestWaitItem.parseFrom(bytes);
            builder = LoginDao.getInstance().waitItem(uid);
        } catch (Exception e) {
            e.printStackTrace();
        }
        ///  /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    public byte[] upWaitItemLevel(byte[] bytes, String uid) {
        UserData.RequestUpWaitItemLevel requestUpWaitItemLevel = null;
        UserData.ResponseUpWaitItemLevel.Builder builder = UserData.ResponseUpWaitItemLevel.newBuilder();
        long now = System.currentTimeMillis();
        try {
            requestUpWaitItemLevel = UserData.RequestUpWaitItemLevel.parseFrom(bytes);
            StringBuffer queryBagLevel = new StringBuffer("from WaitItemEntity where uid='").append(uid).append("'");
            WaitItemEntity entity = (WaitItemEntity) MySql.queryForOne(queryBagLevel.toString());
            int nowLevel = entity.getBagLevel();
            String upBagLevelConfig = SuperConfig.getBagLimite(nowLevel + 1);
            int needStarCoinNums = Integer.parseInt(upBagLevelConfig.split("\\|")[0]);
            IItem itemDao = ItemDao.getInstance();
            double total = itemDao.updateItemInfo(uid, 2, -needStarCoinNums);
            ItemData.Item.Builder itemBuilder = ItemData.Item.newBuilder();
            itemBuilder.setId(2);
            itemBuilder.setNum(total);
            builder.addItem(itemBuilder);
            builder.setNowlevel(nowLevel + 1);
            entity.setBagLevel(entity.getBagLevel() + 1);
            MySql.fastUpdate(entity);
            String nowBagLevelConfig = SuperConfig.getBagLimite(nowLevel);
            int bagLimite = Integer.parseInt(nowBagLevelConfig.split("\\|")[1]);
            StringBuffer queryWaitItemInfo = new StringBuffer("from WaitItemInfoEntity where uid='").append(uid).append("'");
            List<Object> waitItemInfoList = MySql.queryForList(queryWaitItemInfo.toString());
            for (int i = 0; i < waitItemInfoList.size(); i++) {
                WaitItemInfoEntity waitItemInfoEntity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                if (entity.getRewardTotalNums() >= bagLimite) {
                    waitItemInfoEntity.setLatelyFinishTime(now + waitItemInfoEntity.getLatelyFinishTime() - entity.getLastQueryTime());
                    //   /// System.out.println(MyUtils.stampToDate(waitItemInfoEntity.getLatelyFinishTime()));
                }
                MySql.fastUpdate(waitItemInfoEntity);
            }
            //
        } catch (Exception e) {
            e.printStackTrace();
        }
        builder.setErrorId(0);
        return builder.build().toByteArray();
    }

    //   第一版收集箱
    public byte[] getWaitItem(byte[] bytes, String uid) {

        //  UserData.RequestGetWaitItem requestGetWaitItem=null;
        UserData.ResponseGetWaitItem.Builder builder = UserData.ResponseGetWaitItem.newBuilder();
        builder.setErrorId(0);
        long now = System.currentTimeMillis();
        int status = -1;
        try {
            // requestGetWaitItem =UserData.RequestGetWaitItem.parseFrom(bytes);
            StringBuffer queryWaitItem = new StringBuffer("from WaitItemEntity where uid='").append(uid).append("'");
            WaitItemEntity waitItemEntity = (WaitItemEntity) MySql.queryForOne(queryWaitItem.toString());
            if (waitItemEntity == null) {
                builder.setErrorId(1);
            } else {
                IItem itemDao = ItemDao.getInstance();
                int nowRewardNums = waitItemEntity.getRewardTotalNums();
                int bagLevel = waitItemEntity.getBagLevel();
                int bagLimite = 0;
                if (bagLevel == 0) {
                    bagLimite = 30;
                } else {
                    String bagInfo = SuperConfig.getBagLimite(bagLevel);
                    bagLimite = Integer.parseInt(bagInfo.split("\\|")[1]);
                }
                if (nowRewardNums == 0) {
                    builder.setErrorId(2);
                } else {
                    StringBuffer queryWaitItemInfo = new StringBuffer("from WaitItemInfoEntity where uid='").append(uid).append("' order by latelyFinishTime asc");
                    List<Object> waitItemInfoList = MySql.queryForList(queryWaitItemInfo.toString());
                    for (int i = 0; i < waitItemInfoList.size(); i++) {
                        WaitItemInfoEntity waitItemInfoEntity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                        if (nowRewardNums >= bagLimite) {
                            if (waitItemInfoEntity.getLatelyFinishTime() == -1) { //表示新增加的记录
                                int id = waitItemInfoEntity.getWaitItemId();
                                Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM, id);
                                waitItemInfoEntity.setLatelyFinishTime(now + Integer.parseInt(map.get("timing")) * 1000);
                            } else {
                                waitItemInfoEntity.setLatelyFinishTime(now + waitItemInfoEntity.getLatelyFinishTime() - waitItemEntity.getLastQueryTime());
                            }
                        }
                        int reward = waitItemInfoEntity.getReward();
                        int rewardId = waitItemInfoEntity.getRewardId();
                        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                        double total = itemDao.updateItemInfo(uid, rewardId, reward);
                        itemBu.setNum(total);
                        itemBu.setId(rewardId);
                        builder.addItem(itemBu);
                        waitItemInfoEntity.setReward(0);
                        MySql.fastUpdate(waitItemInfoEntity);
                    }
                }
            }

            waitItemEntity.setRewardTotalNums(0);
            status = MySql.fastUpdate(waitItemEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }

///*   while(status!=0){
//    /// System.err.println(status+"status");
//    if(TimerHandler.nowTimeStamp-now>1000){
//        break;
//    }
// }*/

        return builder.build().toByteArray();
    }

    public byte[] house(byte[] bytes, String uid) {
        UserData.ResponseHouse.Builder builder = UserData.ResponseHouse.newBuilder();
        try {
            StringBuffer sql = new StringBuffer("from RoomEntity where uid='").append(uid).append("'");
            List<Object> roomInfolist = MySql.queryForList(sql.toString());
            for (int i = 0; i < roomInfolist.size(); i++) {
                RoomEntity roomEntity = (RoomEntity) roomInfolist.get(i);
                UserData.RoomPart.Builder roomBu = UserData.RoomPart.newBuilder();
                roomBu.setFloorID(roomEntity.getFloorID());
                roomBu.setWallID(roomEntity.getWallID());
                roomBu.setRoomId(roomEntity.getType());
                byte[] objectByte = roomEntity.getFurnitureInfo();
                HouseInfo houseInfo = null;
                houseInfo = (HouseInfo) MyUtils.bytesToObject(objectByte, houseInfo);
                List<FurnitureInfo> furnitureInfoList = houseInfo.getFurnitureInfosList();
                if (furnitureInfoList.size() != 0) {
                    for (int j = 0; j < furnitureInfoList.size(); j++) {
                        FurnitureInfo furnitureInfo = furnitureInfoList.get(j);
                        UserData.FurnitureInfo.Builder furnitureBu = UserData.FurnitureInfo.newBuilder();
                        furnitureBu.setId(furnitureInfo.getId());
                        furnitureBu.setX(furnitureInfo.getX());
                        furnitureBu.setY(furnitureInfo.getY());
                        furnitureBu.setZ(furnitureInfo.getZ());
                        roomBu.addFurnitureList(furnitureBu);
                    }
                }
                builder.addRoomPart(roomBu);
            }
            StringBuffer furnituresql = new StringBuffer("from FurnitureShopEntity where uid='").append(uid).append("'");
            List<Object> furnituresList = MySql.queryForList(furnituresql.toString());
            for (int i = 0; i < furnituresList.size(); i++) {
                FurnitureShopEntity entity = (FurnitureShopEntity) furnituresList.get(i);
                ItemData.Item.Builder furniture = ItemData.Item.newBuilder();
                furniture.setId(entity.getFurnitureId());
                furniture.setNum(entity.getNums());
                builder.addFurniture(furniture);
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        //  /// System.err.println(JsonFormat.printToString(builder.build()));
        if (builder.build().toByteArray().length == 0) {
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEHOUSE_VALUE, builder.build().toByteArray());
        }
        return builder.build().toByteArray();
    }

    public byte[] housePart(byte[] bytes, String uid) {
        UserData.RequestHousePart requestHousePart = null;
        UserData.ResponseHousePart.Builder responsetHousePart = UserData.ResponseHousePart.newBuilder();
        responsetHousePart.setErrorId(0);
        try {
            requestHousePart = UserData.RequestHousePart.parseFrom(bytes);
            List<UserData.RoomPart> roomPartList = requestHousePart.getRoomPartList();
            for (int i = 0; i < roomPartList.size(); i++) {
                UserData.RoomPart roomPart = roomPartList.get(i);
                int roomId = roomPart.getRoomId();
                int wallId = roomPart.getWallID();
                int floorId = roomPart.getFloorID();
                List<UserData.FurnitureInfo> furnitureInfoList = roomPart.getFurnitureListList();
                StringBuffer sql = new StringBuffer("from RoomEntity where uid='").append(uid).append("' and type=").append(roomId);
                RoomEntity roomEntity = (RoomEntity) MySql.queryForOne(sql.toString());
                HouseInfo houseInfo = new HouseInfo(5);
                if (roomPart.getFurnitureListCount() != 0) {
                    List<FurnitureInfo> furnituresList = houseInfo.getFurnitureInfosList();
                    for (int j = 0; j < furnitureInfoList.size(); j++) {
                        UserData.FurnitureInfo furnitureInfoBu = (UserData.FurnitureInfo) furnitureInfoList.get(j);
                        FurnitureInfo furnitureInfo = new FurnitureInfo();
                        furnitureInfo.setId(furnitureInfoBu.getId());
                        furnitureInfo.setX(furnitureInfoBu.getX());
                        furnitureInfo.setY(furnitureInfoBu.getY());
                        furnitureInfo.setZ(furnitureInfoBu.getZ());
                        furnituresList.add(furnitureInfo);
                    }
                }
                byte[] objectBytes = MyUtils.objectToBytes(houseInfo);
                if (roomEntity == null) {
                    roomEntity = new RoomEntity();
                    roomEntity.setType(roomId);
                    roomEntity.setWallID(wallId);
                    roomEntity.setFloorID(floorId);
                    roomEntity.setFurnitureInfo(objectBytes);
                    roomEntity.setUid(uid);
                    MySql.insert(roomEntity);
                } else {
                    roomEntity.setWallID(wallId);
                    roomEntity.setFloorID(floorId);
                    roomEntity.setFurnitureInfo(objectBytes);
                    roomEntity.setUid(uid);
                    roomEntity.setType(roomId);
                    MySql.update(roomEntity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ///  /// System.err.println(JsonFormat.printToString(requestHousePart));
        return responsetHousePart.build().toByteArray();
    }

    public byte[] buyFurniture(byte[] bytes, String uid) {
        UserData.RequestBuyFurniture requestBuyFurniture = null;
        UserData.ResponseBuyFurniture.Builder builder = UserData.ResponseBuyFurniture.newBuilder();

        builder.setErrorId(0);
        try {
            requestBuyFurniture = UserData.RequestBuyFurniture.parseFrom(bytes);
            //   /// System.err.println(JsonFormat.printToString(requestBuyFurniture));
            int furnitureId = requestBuyFurniture.getFurnitureId();
            Map<String, String> furnitureConfig = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_FURNITURE, furnitureId);
            String price = furnitureConfig.get("price");
            int costId = Integer.parseInt(price.split(",")[0]);
            int costNums = Integer.parseInt(price.split(",")[1]);
            ItemDao itemDao = ItemDao.getInstance();

            double realNums = itemDao.getItemNum(uid, costId);
            if (realNums < costNums) {
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder.build().toByteArray();
            }
            ItemData.Item.Builder item = ItemData.Item.newBuilder();
            double total = itemDao.updateItemInfo(uid, costId, -costNums);
            item.setId(costId);
            item.setNum(total);
            builder.addCostItem(item);
            ItemData.Item.Builder furnitureBu = ItemData.Item.newBuilder();
            int nums = LoginDao.getInstance().updateFurniture(uid, furnitureId, 1);
            furnitureBu.setId(furnitureId);
            furnitureBu.setNum(nums);
            builder.addFurniture(furnitureBu);

        } catch (Exception e) {
            e.printStackTrace();
        }

        // /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    public byte[] bindIDCard(byte[] inBytes, String uid) {
        UserData.RequestBindIDCard requestBindIDCard = null;
        UserData.ResponseBindIDCard.Builder builder = UserData.ResponseBindIDCard.newBuilder();
        try {
            requestBindIDCard = UserData.RequestBindIDCard.parseFrom(inBytes);
            String number = requestBindIDCard.getNumber();
            if (!MyUtils.checkIDCard(number)) {
                builder.setErrorId(1);
                return builder.build().toByteArray();
            } else {
                String name = requestBindIDCard.getName();
                StringBuilder sql = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(sql.toString());
                StringBuilder sql1 = new StringBuilder("update UserEntity set name='").append(name).append("',idcard='").append(number).append(" ' , authentication=1  where userid='").append(roleEntity.getUserid()).append("'");
                MySql.updateSomes(sql1.toString());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        builder.setErrorId(0);
        return builder.build().toByteArray();
    }
}