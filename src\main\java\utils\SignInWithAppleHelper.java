package utils;

import io.jsonwebtoken.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.codec.binary.Base64;
// import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;


import java.math.BigInteger;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.RSAPublicKeySpec;
import java.util.Map;


@Component
public class SignInWithAppleHelper {
    private static JSONArray keysJsonArray = null;

    /**
     * 解密个人信息
     *
     * @param identityToken APP获取的identityToken
     * @return 解密参数：失败返回null
     */
    public static Map<String, Object> verify(String identityToken) {
        String sub = "";
        boolean result = false;
        Map<String, Object> data1 = null;
        try {
            String[] identityTokens = identityToken.split("\\.");
            Map<String, Object> data0 = JSONObject.fromObject(new String(Base64.decodeBase64(identityTokens[0]), "UTF-8"));
            data1 = JSONObject.fromObject(new String(Base64.decodeBase64(identityTokens[1]), "UTF-8"));
            String aud = (String) data1.get("aud");
            sub = (String) data1.get("sub");
            String kid = (String) data0.get("kid");
            result = verify(identityToken, aud, sub, kid);
        } catch (ExpiredJwtException e) {
            e.printStackTrace();
        } catch (Exception e) {
            if (e instanceof SignatureException) {
                updateAppleKeys();
            }
            e.printStackTrace();
           // throw new PassportException(APIResultStatus.UC_VERIFY_FAIL.getCode(), APIResultStatus.UC_VERIFY_FAIL.getMsg());
        }
        if (!result) {
            return null;
        }
        return data1;
    }

    /**
     * 验证
     *
     * @param identityToken APP获取的identityToken
     * @param aud           您在您的Apple Developer帐户中的bundle
     * @param sub           用户的唯一标识符对应APP获取到的：user
     * @return true/false
     */
    public static boolean verify(String identityToken, String aud, String sub, String kid) {
        PublicKey publicKey = getPublicKey(kid);
        JwtParser jwtParser = Jwts.parser().setSigningKey(publicKey);
        jwtParser.requireIssuer("https://appleid.apple.com");
        jwtParser.requireAudience(aud);
        jwtParser.requireSubject(sub);
        Jws<Claims> claim = jwtParser.parseClaimsJws(identityToken);
        if (claim != null && claim.getBody().containsKey("auth_time")) {
            return true;
        }
        return false;
    }

    /**
     *
     * @return 构造好的公钥
     */
    public static PublicKey getPublicKey(String kid) {
        try {
            if (keysJsonArray == null || keysJsonArray.size() == 0) {
                updateAppleKeys();
            }
            String n = "";
            String e = "";
            for (int i = 0; i < keysJsonArray.size(); i++) {
                JSONObject jsonObject = keysJsonArray.getJSONObject(i);
                if (jsonObject.getString("kid").equals(kid)) {
                    n = jsonObject.getString("n");
                    e = jsonObject.getString("e");
                }
            }
            final BigInteger modulus = new BigInteger(1, Base64.decodeBase64(n));
            final BigInteger publicExponent = new BigInteger(1, Base64.decodeBase64(e));

            final RSAPublicKeySpec spec = new RSAPublicKeySpec(modulus, publicExponent);
            final KeyFactory kf = KeyFactory.getInstance("RSA");
            return kf.generatePublic(spec);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private static void updateAppleKeys() {
        RestTemplate restTemplate = new RestTemplate();
        String forObject = restTemplate.getForObject("https://appleid.apple.com/auth/keys", String.class);
        ///// System.out.println(forObject);
        if (forObject.isEmpty()) {
            return;
        }
        JSONObject data = JSONObject.fromObject(forObject);
        keysJsonArray = data.getJSONArray("keys");
    }
 
}

