package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/2/1.
 */
@Entity
@Table(name = "relativeship", schema = "", catalog = "super_star_fruit")
public class RelativeshipEntity {
    private int id;
    private Integer type;
    private String roleuid1;
    private String roleuid2;
    private Integer value;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Basic
    @Column(name = "roleuid1")
    public String getRoleuid1() {
        return roleuid1;
    }

    public void setRoleuid1(String roleuid1) {
        this.roleuid1 = roleuid1;
    }

    @Basic
    @Column(name = "roleuid2")
    public String getRoleuid2() {
        return roleuid2;
    }

    public void setRoleuid2(String roleuid2) {
        this.roleuid2 = roleuid2;
    }

    @Basic
    @Column(name = "value")
    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        RelativeshipEntity that = (RelativeshipEntity) o;

        if (id != that.id) return false;
        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        if (roleuid1 != null ? !roleuid1.equals(that.roleuid1) : that.roleuid1 != null) return false;
        if (roleuid2 != null ? !roleuid2.equals(that.roleuid2) : that.roleuid2 != null) return false;
        if (value != null ? !value.equals(that.value) : that.value != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (type != null ? type.hashCode() : 0);
        result = 31 * result + (roleuid1 != null ? roleuid1.hashCode() : 0);
        result = 31 * result + (roleuid2 != null ? roleuid2.hashCode() : 0);
        result = 31 * result + (value != null ? value.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "RelativeshipEntity{" +
                "id=" + id +
                ", type=" + type +
                ", roleuid1='" + roleuid1 + '\'' +
                ", roleuid2='" + roleuid2 + '\'' +
                ", value=" + value +
                '}';
    }
}
