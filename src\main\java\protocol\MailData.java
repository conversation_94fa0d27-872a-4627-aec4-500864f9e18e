// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: mail.proto

package protocol;

public final class MailData {
  private MailData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface DefaultPetOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 petId = 1;
    /**
     * <code>required int32 petId = 1;</code>
     */
    boolean hasPetId();
    /**
     * <code>required int32 petId = 1;</code>
     */
    int getPetId();

    // required int32 isEgg = 2;
    /**
     * <code>required int32 isEgg = 2;</code>
     *
     * <pre>
     * 0：蛋 1：人
     * </pre>
     */
    boolean hasIsEgg();
    /**
     * <code>required int32 isEgg = 2;</code>
     *
     * <pre>
     * 0：蛋 1：人
     * </pre>
     */
    int getIsEgg();
  }
  /**
   * Protobuf type {@code protocol.DefaultPet}
   */
  public static final class DefaultPet extends
      com.google.protobuf.GeneratedMessage
      implements DefaultPetOrBuilder {
    // Use DefaultPet.newBuilder() to construct.
    private DefaultPet(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private DefaultPet(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final DefaultPet defaultInstance;
    public static DefaultPet getDefaultInstance() {
      return defaultInstance;
    }

    public DefaultPet getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private DefaultPet(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isEgg_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_DefaultPet_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_DefaultPet_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              DefaultPet.class, Builder.class);
    }

    public static com.google.protobuf.Parser<DefaultPet> PARSER =
        new com.google.protobuf.AbstractParser<DefaultPet>() {
      public DefaultPet parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DefaultPet(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<DefaultPet> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 petId = 1;
    public static final int PETID_FIELD_NUMBER = 1;
    private int petId_;
    /**
     * <code>required int32 petId = 1;</code>
     */
    public boolean hasPetId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 petId = 1;</code>
     */
    public int getPetId() {
      return petId_;
    }

    // required int32 isEgg = 2;
    public static final int ISEGG_FIELD_NUMBER = 2;
    private int isEgg_;
    /**
     * <code>required int32 isEgg = 2;</code>
     *
     * <pre>
     * 0：蛋 1：人
     * </pre>
     */
    public boolean hasIsEgg() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 isEgg = 2;</code>
     *
     * <pre>
     * 0：蛋 1：人
     * </pre>
     */
    public int getIsEgg() {
      return isEgg_;
    }

    private void initFields() {
      petId_ = 0;
      isEgg_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIsEgg()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, isEgg_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, isEgg_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static DefaultPet parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DefaultPet parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DefaultPet parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static DefaultPet parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static DefaultPet parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static DefaultPet parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static DefaultPet parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static DefaultPet parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static DefaultPet parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static DefaultPet parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(DefaultPet prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.DefaultPet}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements DefaultPetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_DefaultPet_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_DefaultPet_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                DefaultPet.class, Builder.class);
      }

      // Construct using protocol.MailData.DefaultPet.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        isEgg_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_DefaultPet_descriptor;
      }

      public DefaultPet getDefaultInstanceForType() {
        return DefaultPet.getDefaultInstance();
      }

      public DefaultPet build() {
        DefaultPet result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public DefaultPet buildPartial() {
        DefaultPet result = new DefaultPet(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petId_ = petId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.isEgg_ = isEgg_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof DefaultPet) {
          return mergeFrom((DefaultPet)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(DefaultPet other) {
        if (other == DefaultPet.getDefaultInstance()) return this;
        if (other.hasPetId()) {
          setPetId(other.getPetId());
        }
        if (other.hasIsEgg()) {
          setIsEgg(other.getIsEgg());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetId()) {
          
          return false;
        }
        if (!hasIsEgg()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        DefaultPet parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (DefaultPet) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 petId = 1;
      private int petId_ ;
      /**
       * <code>required int32 petId = 1;</code>
       */
      public boolean hasPetId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 petId = 1;</code>
       */
      public int getPetId() {
        return petId_;
      }
      /**
       * <code>required int32 petId = 1;</code>
       */
      public Builder setPetId(int value) {
        bitField0_ |= 0x00000001;
        petId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 petId = 1;</code>
       */
      public Builder clearPetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petId_ = 0;
        onChanged();
        return this;
      }

      // required int32 isEgg = 2;
      private int isEgg_ ;
      /**
       * <code>required int32 isEgg = 2;</code>
       *
       * <pre>
       * 0：蛋 1：人
       * </pre>
       */
      public boolean hasIsEgg() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 isEgg = 2;</code>
       *
       * <pre>
       * 0：蛋 1：人
       * </pre>
       */
      public int getIsEgg() {
        return isEgg_;
      }
      /**
       * <code>required int32 isEgg = 2;</code>
       *
       * <pre>
       * 0：蛋 1：人
       * </pre>
       */
      public Builder setIsEgg(int value) {
        bitField0_ |= 0x00000002;
        isEgg_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 isEgg = 2;</code>
       *
       * <pre>
       * 0：蛋 1：人
       * </pre>
       */
      public Builder clearIsEgg() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isEgg_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.DefaultPet)
    }

    static {
      defaultInstance = new DefaultPet(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.DefaultPet)
  }

  public interface MailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 mid = 1;
    /**
     * <code>required int32 mid = 1;</code>
     *
     * <pre>
     *邮件id
     * </pre>
     */
    boolean hasMid();
    /**
     * <code>required int32 mid = 1;</code>
     *
     * <pre>
     *邮件id
     * </pre>
     */
    int getMid();

    // required string subjectType = 2;
    /**
     * <code>required string subjectType = 2;</code>
     *
     * <pre>
     *主题类型
     * </pre>
     */
    boolean hasSubjectType();
    /**
     * <code>required string subjectType = 2;</code>
     *
     * <pre>
     *主题类型
     * </pre>
     */
    String getSubjectType();
    /**
     * <code>required string subjectType = 2;</code>
     *
     * <pre>
     *主题类型
     * </pre>
     */
    com.google.protobuf.ByteString
        getSubjectTypeBytes();

    // optional string sender = 3;
    /**
     * <code>optional string sender = 3;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    boolean hasSender();
    /**
     * <code>optional string sender = 3;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    String getSender();
    /**
     * <code>optional string sender = 3;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    com.google.protobuf.ByteString
        getSenderBytes();

    // optional string owner = 4;
    /**
     * <code>optional string owner = 4;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    boolean hasOwner();
    /**
     * <code>optional string owner = 4;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    String getOwner();
    /**
     * <code>optional string owner = 4;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    com.google.protobuf.ByteString
        getOwnerBytes();

    // optional string title = 5;
    /**
     * <code>optional string title = 5;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    boolean hasTitle();
    /**
     * <code>optional string title = 5;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    String getTitle();
    /**
     * <code>optional string title = 5;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    // required string content = 6;
    /**
     * <code>required string content = 6;</code>
     *
     * <pre>
     *内容
     * </pre>
     */
    boolean hasContent();
    /**
     * <code>required string content = 6;</code>
     *
     * <pre>
     *内容
     * </pre>
     */
    String getContent();
    /**
     * <code>required string content = 6;</code>
     *
     * <pre>
     *内容
     * </pre>
     */
    com.google.protobuf.ByteString
        getContentBytes();

    // required int32 status = 7;
    /**
     * <code>required int32 status = 7;</code>
     *
     * <pre>
     *0未读 1已读 2已经处理  
     * </pre>
     */
    boolean hasStatus();
    /**
     * <code>required int32 status = 7;</code>
     *
     * <pre>
     *0未读 1已读 2已经处理  
     * </pre>
     */
    int getStatus();

    // required int64 timeStamp = 8;
    /**
     * <code>required int64 timeStamp = 8;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    boolean hasTimeStamp();
    /**
     * <code>required int64 timeStamp = 8;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    long getTimeStamp();

    // optional .protocol.Attachment attchment = 9;
    /**
     * <code>optional .protocol.Attachment attchment = 9;</code>
     *
     * <pre>
     * 附件
     * </pre>
     */
    boolean hasAttchment();
    /**
     * <code>optional .protocol.Attachment attchment = 9;</code>
     *
     * <pre>
     * 附件
     * </pre>
     */
    Attachment getAttchment();
    /**
     * <code>optional .protocol.Attachment attchment = 9;</code>
     *
     * <pre>
     * 附件
     * </pre>
     */
    AttachmentOrBuilder getAttchmentOrBuilder();

    // required int64 overdueTime = 10;
    /**
     * <code>required int64 overdueTime = 10;</code>
     *
     * <pre>
     *过期时间
     * </pre>
     */
    boolean hasOverdueTime();
    /**
     * <code>required int64 overdueTime = 10;</code>
     *
     * <pre>
     *过期时间
     * </pre>
     */
    long getOverdueTime();

    // required int32 mailId = 11;
    /**
     * <code>required int32 mailId = 11;</code>
     *
     * <pre>
     *邮件类型
     * </pre>
     */
    boolean hasMailId();
    /**
     * <code>required int32 mailId = 11;</code>
     *
     * <pre>
     *邮件类型
     * </pre>
     */
    int getMailId();
  }
  /**
   * Protobuf type {@code protocol.Mail}
   *
   * <pre>
   *邮件
   * </pre>
   */
  public static final class Mail extends
      com.google.protobuf.GeneratedMessage
      implements MailOrBuilder {
    // Use Mail.newBuilder() to construct.
    private Mail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Mail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Mail defaultInstance;
    public static Mail getDefaultInstance() {
      return defaultInstance;
    }

    public Mail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Mail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              mid_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              subjectType_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              sender_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              owner_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              title_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              content_ = input.readBytes();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              status_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              timeStamp_ = input.readInt64();
              break;
            }
            case 74: {
              Attachment.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) == 0x00000100)) {
                subBuilder = attchment_.toBuilder();
              }
              attchment_ = input.readMessage(Attachment.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(attchment_);
                attchment_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              overdueTime_ = input.readInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              mailId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_Mail_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_Mail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Mail.class, Builder.class);
    }

    public static com.google.protobuf.Parser<Mail> PARSER =
        new com.google.protobuf.AbstractParser<Mail>() {
      public Mail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Mail(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<Mail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 mid = 1;
    public static final int MID_FIELD_NUMBER = 1;
    private int mid_;
    /**
     * <code>required int32 mid = 1;</code>
     *
     * <pre>
     *邮件id
     * </pre>
     */
    public boolean hasMid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 mid = 1;</code>
     *
     * <pre>
     *邮件id
     * </pre>
     */
    public int getMid() {
      return mid_;
    }

    // required string subjectType = 2;
    public static final int SUBJECTTYPE_FIELD_NUMBER = 2;
    private Object subjectType_;
    /**
     * <code>required string subjectType = 2;</code>
     *
     * <pre>
     *主题类型
     * </pre>
     */
    public boolean hasSubjectType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string subjectType = 2;</code>
     *
     * <pre>
     *主题类型
     * </pre>
     */
    public String getSubjectType() {
      Object ref = subjectType_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          subjectType_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string subjectType = 2;</code>
     *
     * <pre>
     *主题类型
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSubjectTypeBytes() {
      Object ref = subjectType_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        subjectType_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string sender = 3;
    public static final int SENDER_FIELD_NUMBER = 3;
    private Object sender_;
    /**
     * <code>optional string sender = 3;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    public boolean hasSender() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string sender = 3;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    public String getSender() {
      Object ref = sender_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sender_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string sender = 3;</code>
     *
     * <pre>
     *发送者
     * </pre>
     */
    public com.google.protobuf.ByteString
        getSenderBytes() {
      Object ref = sender_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        sender_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string owner = 4;
    public static final int OWNER_FIELD_NUMBER = 4;
    private Object owner_;
    /**
     * <code>optional string owner = 4;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    public boolean hasOwner() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string owner = 4;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    public String getOwner() {
      Object ref = owner_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          owner_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string owner = 4;</code>
     *
     * <pre>
     *接收方
     * </pre>
     */
    public com.google.protobuf.ByteString
        getOwnerBytes() {
      Object ref = owner_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        owner_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string title = 5;
    public static final int TITLE_FIELD_NUMBER = 5;
    private Object title_;
    /**
     * <code>optional string title = 5;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    public boolean hasTitle() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional string title = 5;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    public String getTitle() {
      Object ref = title_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          title_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string title = 5;</code>
     *
     * <pre>
     * 标题
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTitleBytes() {
      Object ref = title_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string content = 6;
    public static final int CONTENT_FIELD_NUMBER = 6;
    private Object content_;
    /**
     * <code>required string content = 6;</code>
     *
     * <pre>
     *内容
     * </pre>
     */
    public boolean hasContent() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required string content = 6;</code>
     *
     * <pre>
     *内容
     * </pre>
     */
    public String getContent() {
      Object ref = content_;
      if (ref instanceof String) {
        return (String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          content_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string content = 6;</code>
     *
     * <pre>
     *内容
     * </pre>
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      Object ref = content_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 status = 7;
    public static final int STATUS_FIELD_NUMBER = 7;
    private int status_;
    /**
     * <code>required int32 status = 7;</code>
     *
     * <pre>
     *0未读 1已读 2已经处理  
     * </pre>
     */
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required int32 status = 7;</code>
     *
     * <pre>
     *0未读 1已读 2已经处理  
     * </pre>
     */
    public int getStatus() {
      return status_;
    }

    // required int64 timeStamp = 8;
    public static final int TIMESTAMP_FIELD_NUMBER = 8;
    private long timeStamp_;
    /**
     * <code>required int64 timeStamp = 8;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    public boolean hasTimeStamp() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>required int64 timeStamp = 8;</code>
     *
     * <pre>
     *发送时间
     * </pre>
     */
    public long getTimeStamp() {
      return timeStamp_;
    }

    // optional .protocol.Attachment attchment = 9;
    public static final int ATTCHMENT_FIELD_NUMBER = 9;
    private Attachment attchment_;
    /**
     * <code>optional .protocol.Attachment attchment = 9;</code>
     *
     * <pre>
     * 附件
     * </pre>
     */
    public boolean hasAttchment() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional .protocol.Attachment attchment = 9;</code>
     *
     * <pre>
     * 附件
     * </pre>
     */
    public Attachment getAttchment() {
      return attchment_;
    }
    /**
     * <code>optional .protocol.Attachment attchment = 9;</code>
     *
     * <pre>
     * 附件
     * </pre>
     */
    public AttachmentOrBuilder getAttchmentOrBuilder() {
      return attchment_;
    }

    // required int64 overdueTime = 10;
    public static final int OVERDUETIME_FIELD_NUMBER = 10;
    private long overdueTime_;
    /**
     * <code>required int64 overdueTime = 10;</code>
     *
     * <pre>
     *过期时间
     * </pre>
     */
    public boolean hasOverdueTime() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>required int64 overdueTime = 10;</code>
     *
     * <pre>
     *过期时间
     * </pre>
     */
    public long getOverdueTime() {
      return overdueTime_;
    }

    // required int32 mailId = 11;
    public static final int MAILID_FIELD_NUMBER = 11;
    private int mailId_;
    /**
     * <code>required int32 mailId = 11;</code>
     *
     * <pre>
     *邮件类型
     * </pre>
     */
    public boolean hasMailId() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>required int32 mailId = 11;</code>
     *
     * <pre>
     *邮件类型
     * </pre>
     */
    public int getMailId() {
      return mailId_;
    }

    private void initFields() {
      mid_ = 0;
      subjectType_ = "";
      sender_ = "";
      owner_ = "";
      title_ = "";
      content_ = "";
      status_ = 0;
      timeStamp_ = 0L;
      attchment_ = Attachment.getDefaultInstance();
      overdueTime_ = 0L;
      mailId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasMid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSubjectType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasContent()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTimeStamp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasOverdueTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMailId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasAttchment()) {
        if (!getAttchment().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, mid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getSubjectTypeBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getSenderBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getOwnerBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getTitleBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getContentBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, status_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt64(8, timeStamp_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeMessage(9, attchment_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeInt64(10, overdueTime_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeInt32(11, mailId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getSubjectTypeBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getSenderBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getOwnerBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getTitleBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getContentBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, status_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(8, timeStamp_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, attchment_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, overdueTime_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, mailId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static Mail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Mail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Mail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Mail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Mail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static Mail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static Mail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static Mail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static Mail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static Mail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(Mail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Mail}
     *
     * <pre>
     *邮件
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements MailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_Mail_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_Mail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Mail.class, Builder.class);
      }

      // Construct using protocol.MailData.Mail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getAttchmentFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        mid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        subjectType_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        sender_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        owner_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        title_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        content_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        status_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        timeStamp_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000080);
        if (attchmentBuilder_ == null) {
          attchment_ = Attachment.getDefaultInstance();
        } else {
          attchmentBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        overdueTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        mailId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_Mail_descriptor;
      }

      public Mail getDefaultInstanceForType() {
        return Mail.getDefaultInstance();
      }

      public Mail build() {
        Mail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public Mail buildPartial() {
        Mail result = new Mail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.mid_ = mid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.subjectType_ = subjectType_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.sender_ = sender_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.owner_ = owner_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.title_ = title_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.content_ = content_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.status_ = status_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.timeStamp_ = timeStamp_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        if (attchmentBuilder_ == null) {
          result.attchment_ = attchment_;
        } else {
          result.attchment_ = attchmentBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.overdueTime_ = overdueTime_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.mailId_ = mailId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Mail) {
          return mergeFrom((Mail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Mail other) {
        if (other == Mail.getDefaultInstance()) return this;
        if (other.hasMid()) {
          setMid(other.getMid());
        }
        if (other.hasSubjectType()) {
          bitField0_ |= 0x00000002;
          subjectType_ = other.subjectType_;
          onChanged();
        }
        if (other.hasSender()) {
          bitField0_ |= 0x00000004;
          sender_ = other.sender_;
          onChanged();
        }
        if (other.hasOwner()) {
          bitField0_ |= 0x00000008;
          owner_ = other.owner_;
          onChanged();
        }
        if (other.hasTitle()) {
          bitField0_ |= 0x00000010;
          title_ = other.title_;
          onChanged();
        }
        if (other.hasContent()) {
          bitField0_ |= 0x00000020;
          content_ = other.content_;
          onChanged();
        }
        if (other.hasStatus()) {
          setStatus(other.getStatus());
        }
        if (other.hasTimeStamp()) {
          setTimeStamp(other.getTimeStamp());
        }
        if (other.hasAttchment()) {
          mergeAttchment(other.getAttchment());
        }
        if (other.hasOverdueTime()) {
          setOverdueTime(other.getOverdueTime());
        }
        if (other.hasMailId()) {
          setMailId(other.getMailId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasMid()) {
          
          return false;
        }
        if (!hasSubjectType()) {
          
          return false;
        }
        if (!hasContent()) {
          
          return false;
        }
        if (!hasStatus()) {
          
          return false;
        }
        if (!hasTimeStamp()) {
          
          return false;
        }
        if (!hasOverdueTime()) {
          
          return false;
        }
        if (!hasMailId()) {
          
          return false;
        }
        if (hasAttchment()) {
          if (!getAttchment().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        Mail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (Mail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 mid = 1;
      private int mid_ ;
      /**
       * <code>required int32 mid = 1;</code>
       *
       * <pre>
       *邮件id
       * </pre>
       */
      public boolean hasMid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 mid = 1;</code>
       *
       * <pre>
       *邮件id
       * </pre>
       */
      public int getMid() {
        return mid_;
      }
      /**
       * <code>required int32 mid = 1;</code>
       *
       * <pre>
       *邮件id
       * </pre>
       */
      public Builder setMid(int value) {
        bitField0_ |= 0x00000001;
        mid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mid = 1;</code>
       *
       * <pre>
       *邮件id
       * </pre>
       */
      public Builder clearMid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        mid_ = 0;
        onChanged();
        return this;
      }

      // required string subjectType = 2;
      private Object subjectType_ = "";
      /**
       * <code>required string subjectType = 2;</code>
       *
       * <pre>
       *主题类型
       * </pre>
       */
      public boolean hasSubjectType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string subjectType = 2;</code>
       *
       * <pre>
       *主题类型
       * </pre>
       */
      public String getSubjectType() {
        Object ref = subjectType_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          subjectType_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>required string subjectType = 2;</code>
       *
       * <pre>
       *主题类型
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSubjectTypeBytes() {
        Object ref = subjectType_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          subjectType_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string subjectType = 2;</code>
       *
       * <pre>
       *主题类型
       * </pre>
       */
      public Builder setSubjectType(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        subjectType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string subjectType = 2;</code>
       *
       * <pre>
       *主题类型
       * </pre>
       */
      public Builder clearSubjectType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        subjectType_ = getDefaultInstance().getSubjectType();
        onChanged();
        return this;
      }
      /**
       * <code>required string subjectType = 2;</code>
       *
       * <pre>
       *主题类型
       * </pre>
       */
      public Builder setSubjectTypeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        subjectType_ = value;
        onChanged();
        return this;
      }

      // optional string sender = 3;
      private Object sender_ = "";
      /**
       * <code>optional string sender = 3;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public boolean hasSender() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string sender = 3;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public String getSender() {
        Object ref = sender_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          sender_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>optional string sender = 3;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public com.google.protobuf.ByteString
          getSenderBytes() {
        Object ref = sender_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          sender_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string sender = 3;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public Builder setSender(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sender_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string sender = 3;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public Builder clearSender() {
        bitField0_ = (bitField0_ & ~0x00000004);
        sender_ = getDefaultInstance().getSender();
        onChanged();
        return this;
      }
      /**
       * <code>optional string sender = 3;</code>
       *
       * <pre>
       *发送者
       * </pre>
       */
      public Builder setSenderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sender_ = value;
        onChanged();
        return this;
      }

      // optional string owner = 4;
      private Object owner_ = "";
      /**
       * <code>optional string owner = 4;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public boolean hasOwner() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string owner = 4;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public String getOwner() {
        Object ref = owner_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          owner_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>optional string owner = 4;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public com.google.protobuf.ByteString
          getOwnerBytes() {
        Object ref = owner_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          owner_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string owner = 4;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public Builder setOwner(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        owner_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string owner = 4;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public Builder clearOwner() {
        bitField0_ = (bitField0_ & ~0x00000008);
        owner_ = getDefaultInstance().getOwner();
        onChanged();
        return this;
      }
      /**
       * <code>optional string owner = 4;</code>
       *
       * <pre>
       *接收方
       * </pre>
       */
      public Builder setOwnerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        owner_ = value;
        onChanged();
        return this;
      }

      // optional string title = 5;
      private Object title_ = "";
      /**
       * <code>optional string title = 5;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public boolean hasTitle() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional string title = 5;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public String getTitle() {
        Object ref = title_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>optional string title = 5;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string title = 5;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public Builder setTitle(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        title_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 5;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public Builder clearTitle() {
        bitField0_ = (bitField0_ & ~0x00000010);
        title_ = getDefaultInstance().getTitle();
        onChanged();
        return this;
      }
      /**
       * <code>optional string title = 5;</code>
       *
       * <pre>
       * 标题
       * </pre>
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        title_ = value;
        onChanged();
        return this;
      }

      // required string content = 6;
      private Object content_ = "";
      /**
       * <code>required string content = 6;</code>
       *
       * <pre>
       *内容
       * </pre>
       */
      public boolean hasContent() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required string content = 6;</code>
       *
       * <pre>
       *内容
       * </pre>
       */
      public String getContent() {
        Object ref = content_;
        if (!(ref instanceof String)) {
          String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (String) ref;
        }
      }
      /**
       * <code>required string content = 6;</code>
       *
       * <pre>
       *内容
       * </pre>
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string content = 6;</code>
       *
       * <pre>
       *内容
       * </pre>
       */
      public Builder setContent(
          String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string content = 6;</code>
       *
       * <pre>
       *内容
       * </pre>
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x00000020);
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <code>required string content = 6;</code>
       *
       * <pre>
       *内容
       * </pre>
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        content_ = value;
        onChanged();
        return this;
      }

      // required int32 status = 7;
      private int status_ ;
      /**
       * <code>required int32 status = 7;</code>
       *
       * <pre>
       *0未读 1已读 2已经处理  
       * </pre>
       */
      public boolean hasStatus() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required int32 status = 7;</code>
       *
       * <pre>
       *0未读 1已读 2已经处理  
       * </pre>
       */
      public int getStatus() {
        return status_;
      }
      /**
       * <code>required int32 status = 7;</code>
       *
       * <pre>
       *0未读 1已读 2已经处理  
       * </pre>
       */
      public Builder setStatus(int value) {
        bitField0_ |= 0x00000040;
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 status = 7;</code>
       *
       * <pre>
       *0未读 1已读 2已经处理  
       * </pre>
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000040);
        status_ = 0;
        onChanged();
        return this;
      }

      // required int64 timeStamp = 8;
      private long timeStamp_ ;
      /**
       * <code>required int64 timeStamp = 8;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public boolean hasTimeStamp() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>required int64 timeStamp = 8;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public long getTimeStamp() {
        return timeStamp_;
      }
      /**
       * <code>required int64 timeStamp = 8;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public Builder setTimeStamp(long value) {
        bitField0_ |= 0x00000080;
        timeStamp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 timeStamp = 8;</code>
       *
       * <pre>
       *发送时间
       * </pre>
       */
      public Builder clearTimeStamp() {
        bitField0_ = (bitField0_ & ~0x00000080);
        timeStamp_ = 0L;
        onChanged();
        return this;
      }

      // optional .protocol.Attachment attchment = 9;
      private Attachment attchment_ = Attachment.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          Attachment, Attachment.Builder, AttachmentOrBuilder> attchmentBuilder_;
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public boolean hasAttchment() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public Attachment getAttchment() {
        if (attchmentBuilder_ == null) {
          return attchment_;
        } else {
          return attchmentBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public Builder setAttchment(Attachment value) {
        if (attchmentBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          attchment_ = value;
          onChanged();
        } else {
          attchmentBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public Builder setAttchment(
          Attachment.Builder builderForValue) {
        if (attchmentBuilder_ == null) {
          attchment_ = builderForValue.build();
          onChanged();
        } else {
          attchmentBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public Builder mergeAttchment(Attachment value) {
        if (attchmentBuilder_ == null) {
          if (((bitField0_ & 0x00000100) == 0x00000100) &&
              attchment_ != Attachment.getDefaultInstance()) {
            attchment_ =
              Attachment.newBuilder(attchment_).mergeFrom(value).buildPartial();
          } else {
            attchment_ = value;
          }
          onChanged();
        } else {
          attchmentBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public Builder clearAttchment() {
        if (attchmentBuilder_ == null) {
          attchment_ = Attachment.getDefaultInstance();
          onChanged();
        } else {
          attchmentBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public Attachment.Builder getAttchmentBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getAttchmentFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      public AttachmentOrBuilder getAttchmentOrBuilder() {
        if (attchmentBuilder_ != null) {
          return attchmentBuilder_.getMessageOrBuilder();
        } else {
          return attchment_;
        }
      }
      /**
       * <code>optional .protocol.Attachment attchment = 9;</code>
       *
       * <pre>
       * 附件
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          Attachment, Attachment.Builder, AttachmentOrBuilder> 
          getAttchmentFieldBuilder() {
        if (attchmentBuilder_ == null) {
          attchmentBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              Attachment, Attachment.Builder, AttachmentOrBuilder>(
                  attchment_,
                  getParentForChildren(),
                  isClean());
          attchment_ = null;
        }
        return attchmentBuilder_;
      }

      // required int64 overdueTime = 10;
      private long overdueTime_ ;
      /**
       * <code>required int64 overdueTime = 10;</code>
       *
       * <pre>
       *过期时间
       * </pre>
       */
      public boolean hasOverdueTime() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>required int64 overdueTime = 10;</code>
       *
       * <pre>
       *过期时间
       * </pre>
       */
      public long getOverdueTime() {
        return overdueTime_;
      }
      /**
       * <code>required int64 overdueTime = 10;</code>
       *
       * <pre>
       *过期时间
       * </pre>
       */
      public Builder setOverdueTime(long value) {
        bitField0_ |= 0x00000200;
        overdueTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 overdueTime = 10;</code>
       *
       * <pre>
       *过期时间
       * </pre>
       */
      public Builder clearOverdueTime() {
        bitField0_ = (bitField0_ & ~0x00000200);
        overdueTime_ = 0L;
        onChanged();
        return this;
      }

      // required int32 mailId = 11;
      private int mailId_ ;
      /**
       * <code>required int32 mailId = 11;</code>
       *
       * <pre>
       *邮件类型
       * </pre>
       */
      public boolean hasMailId() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>required int32 mailId = 11;</code>
       *
       * <pre>
       *邮件类型
       * </pre>
       */
      public int getMailId() {
        return mailId_;
      }
      /**
       * <code>required int32 mailId = 11;</code>
       *
       * <pre>
       *邮件类型
       * </pre>
       */
      public Builder setMailId(int value) {
        bitField0_ |= 0x00000400;
        mailId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mailId = 11;</code>
       *
       * <pre>
       *邮件类型
       * </pre>
       */
      public Builder clearMailId() {
        bitField0_ = (bitField0_ & ~0x00000400);
        mailId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Mail)
    }

    static {
      defaultInstance = new Mail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Mail)
  }

  public interface AttachmentOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *0：无  1：道具 2：装备 3：宠物
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *0：无  1：道具 2：装备 3：宠物
     * </pre>
     */
    int getType();

    // repeated .protocol.Item item = 2;
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    java.util.List<ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    java.util.List<? extends ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    ItemData.ItemOrBuilder getItemOrBuilder(
            int index);

    // repeated .protocol.EquipA equip = 3;
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    java.util.List<EquipAData.EquipA> 
        getEquipList();
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    EquipAData.EquipA getEquip(int index);
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    int getEquipCount();
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    java.util.List<? extends EquipAData.EquipAOrBuilder> 
        getEquipOrBuilderList();
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    EquipAData.EquipAOrBuilder getEquipOrBuilder(
            int index);

    // repeated .protocol.DefaultPet defaultPet = 4;
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    java.util.List<DefaultPet> 
        getDefaultPetList();
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    DefaultPet getDefaultPet(int index);
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    int getDefaultPetCount();
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    java.util.List<? extends DefaultPetOrBuilder> 
        getDefaultPetOrBuilderList();
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    DefaultPetOrBuilder getDefaultPetOrBuilder(
            int index);
  }
  /**
   * Protobuf type {@code protocol.Attachment}
   *
   * <pre>
   * 邮件附件 
   * </pre>
   */
  public static final class Attachment extends
      com.google.protobuf.GeneratedMessage
      implements AttachmentOrBuilder {
    // Use Attachment.newBuilder() to construct.
    private Attachment(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Attachment(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Attachment defaultInstance;
    public static Attachment getDefaultInstance() {
      return defaultInstance;
    }

    public Attachment getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Attachment(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                item_ = new java.util.ArrayList<ItemData.Item>();
                mutable_bitField0_ |= 0x00000002;
              }
              item_.add(input.readMessage(ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                equip_ = new java.util.ArrayList<EquipAData.EquipA>();
                mutable_bitField0_ |= 0x00000004;
              }
              equip_.add(input.readMessage(EquipAData.EquipA.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                defaultPet_ = new java.util.ArrayList<DefaultPet>();
                mutable_bitField0_ |= 0x00000008;
              }
              defaultPet_.add(input.readMessage(DefaultPet.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          equip_ = java.util.Collections.unmodifiableList(equip_);
        }
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          defaultPet_ = java.util.Collections.unmodifiableList(defaultPet_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_Attachment_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_Attachment_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              Attachment.class, Builder.class);
    }

    public static com.google.protobuf.Parser<Attachment> PARSER =
        new com.google.protobuf.AbstractParser<Attachment>() {
      public Attachment parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Attachment(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<Attachment> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *0：无  1：道具 2：装备 3：宠物
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *0：无  1：道具 2：装备 3：宠物
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // repeated .protocol.Item item = 2;
    public static final int ITEM_FIELD_NUMBER = 2;
    private java.util.List<ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public java.util.List<ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public java.util.List<? extends ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // repeated .protocol.EquipA equip = 3;
    public static final int EQUIP_FIELD_NUMBER = 3;
    private java.util.List<EquipAData.EquipA> equip_;
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public java.util.List<EquipAData.EquipA> getEquipList() {
      return equip_;
    }
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public java.util.List<? extends EquipAData.EquipAOrBuilder> 
        getEquipOrBuilderList() {
      return equip_;
    }
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public int getEquipCount() {
      return equip_.size();
    }
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public EquipAData.EquipA getEquip(int index) {
      return equip_.get(index);
    }
    /**
     * <code>repeated .protocol.EquipA equip = 3;</code>
     *
     * <pre>
     *物品奖励
     * </pre>
     */
    public EquipAData.EquipAOrBuilder getEquipOrBuilder(
        int index) {
      return equip_.get(index);
    }

    // repeated .protocol.DefaultPet defaultPet = 4;
    public static final int DEFAULTPET_FIELD_NUMBER = 4;
    private java.util.List<DefaultPet> defaultPet_;
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    public java.util.List<DefaultPet> getDefaultPetList() {
      return defaultPet_;
    }
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    public java.util.List<? extends DefaultPetOrBuilder> 
        getDefaultPetOrBuilderList() {
      return defaultPet_;
    }
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    public int getDefaultPetCount() {
      return defaultPet_.size();
    }
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    public DefaultPet getDefaultPet(int index) {
      return defaultPet_.get(index);
    }
    /**
     * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
     *
     * <pre>
     *默认的宠物生成方式
     * </pre>
     */
    public DefaultPetOrBuilder getDefaultPetOrBuilder(
        int index) {
      return defaultPet_.get(index);
    }

    private void initFields() {
      type_ = 0;
      item_ = java.util.Collections.emptyList();
      equip_ = java.util.Collections.emptyList();
      defaultPet_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getEquipCount(); i++) {
        if (!getEquip(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getDefaultPetCount(); i++) {
        if (!getDefaultPet(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(2, item_.get(i));
      }
      for (int i = 0; i < equip_.size(); i++) {
        output.writeMessage(3, equip_.get(i));
      }
      for (int i = 0; i < defaultPet_.size(); i++) {
        output.writeMessage(4, defaultPet_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, item_.get(i));
      }
      for (int i = 0; i < equip_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, equip_.get(i));
      }
      for (int i = 0; i < defaultPet_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, defaultPet_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static Attachment parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Attachment parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Attachment parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static Attachment parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static Attachment parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static Attachment parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static Attachment parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static Attachment parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static Attachment parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static Attachment parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(Attachment prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Attachment}
     *
     * <pre>
     * 邮件附件 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements AttachmentOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_Attachment_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_Attachment_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                Attachment.class, Builder.class);
      }

      // Construct using protocol.MailData.Attachment.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
          getEquipFieldBuilder();
          getDefaultPetFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          itemBuilder_.clear();
        }
        if (equipBuilder_ == null) {
          equip_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          equipBuilder_.clear();
        }
        if (defaultPetBuilder_ == null) {
          defaultPet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          defaultPetBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_Attachment_descriptor;
      }

      public Attachment getDefaultInstanceForType() {
        return Attachment.getDefaultInstance();
      }

      public Attachment build() {
        Attachment result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public Attachment buildPartial() {
        Attachment result = new Attachment(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (equipBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            equip_ = java.util.Collections.unmodifiableList(equip_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.equip_ = equip_;
        } else {
          result.equip_ = equipBuilder_.build();
        }
        if (defaultPetBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            defaultPet_ = java.util.Collections.unmodifiableList(defaultPet_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.defaultPet_ = defaultPet_;
        } else {
          result.defaultPet_ = defaultPetBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof Attachment) {
          return mergeFrom((Attachment)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(Attachment other) {
        if (other == Attachment.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000002);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (equipBuilder_ == null) {
          if (!other.equip_.isEmpty()) {
            if (equip_.isEmpty()) {
              equip_ = other.equip_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureEquipIsMutable();
              equip_.addAll(other.equip_);
            }
            onChanged();
          }
        } else {
          if (!other.equip_.isEmpty()) {
            if (equipBuilder_.isEmpty()) {
              equipBuilder_.dispose();
              equipBuilder_ = null;
              equip_ = other.equip_;
              bitField0_ = (bitField0_ & ~0x00000004);
              equipBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getEquipFieldBuilder() : null;
            } else {
              equipBuilder_.addAllMessages(other.equip_);
            }
          }
        }
        if (defaultPetBuilder_ == null) {
          if (!other.defaultPet_.isEmpty()) {
            if (defaultPet_.isEmpty()) {
              defaultPet_ = other.defaultPet_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureDefaultPetIsMutable();
              defaultPet_.addAll(other.defaultPet_);
            }
            onChanged();
          }
        } else {
          if (!other.defaultPet_.isEmpty()) {
            if (defaultPetBuilder_.isEmpty()) {
              defaultPetBuilder_.dispose();
              defaultPetBuilder_ = null;
              defaultPet_ = other.defaultPet_;
              bitField0_ = (bitField0_ & ~0x00000008);
              defaultPetBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getDefaultPetFieldBuilder() : null;
            } else {
              defaultPetBuilder_.addAllMessages(other.defaultPet_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        for (int i = 0; i < getEquipCount(); i++) {
          if (!getEquip(i).isInitialized()) {
            
            return false;
          }
        }
        for (int i = 0; i < getDefaultPetCount(); i++) {
          if (!getDefaultPet(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        Attachment parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (Attachment) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *0：无  1：道具 2：装备 3：宠物
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *0：无  1：道具 2：装备 3：宠物
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *0：无  1：道具 2：装备 3：宠物
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *0：无  1：道具 2：装备 3：宠物
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 2;
      private java.util.List<ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          item_ = new java.util.ArrayList<ItemData.Item>(item_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          ItemData.Item, ItemData.Item.Builder, ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public java.util.List<ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder setItem(
          int index, ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder setItem(
          int index, ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addItem(ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addItem(
          int index, ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addItem(
          ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addItem(
          int index, ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addAllItem(
          Iterable<? extends ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public java.util.List<? extends ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public java.util.List<ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          ItemData.Item, ItemData.Item.Builder, ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              ItemData.Item, ItemData.Item.Builder, ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // repeated .protocol.EquipA equip = 3;
      private java.util.List<EquipAData.EquipA> equip_ =
        java.util.Collections.emptyList();
      private void ensureEquipIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          equip_ = new java.util.ArrayList<EquipAData.EquipA>(equip_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          EquipAData.EquipA, EquipAData.EquipA.Builder, EquipAData.EquipAOrBuilder> equipBuilder_;

      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public java.util.List<EquipAData.EquipA> getEquipList() {
        if (equipBuilder_ == null) {
          return java.util.Collections.unmodifiableList(equip_);
        } else {
          return equipBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public int getEquipCount() {
        if (equipBuilder_ == null) {
          return equip_.size();
        } else {
          return equipBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public EquipAData.EquipA getEquip(int index) {
        if (equipBuilder_ == null) {
          return equip_.get(index);
        } else {
          return equipBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder setEquip(
          int index, EquipAData.EquipA value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipIsMutable();
          equip_.set(index, value);
          onChanged();
        } else {
          equipBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder setEquip(
          int index, EquipAData.EquipA.Builder builderForValue) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.set(index, builderForValue.build());
          onChanged();
        } else {
          equipBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addEquip(EquipAData.EquipA value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipIsMutable();
          equip_.add(value);
          onChanged();
        } else {
          equipBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addEquip(
          int index, EquipAData.EquipA value) {
        if (equipBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipIsMutable();
          equip_.add(index, value);
          onChanged();
        } else {
          equipBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addEquip(
          EquipAData.EquipA.Builder builderForValue) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.add(builderForValue.build());
          onChanged();
        } else {
          equipBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addEquip(
          int index, EquipAData.EquipA.Builder builderForValue) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.add(index, builderForValue.build());
          onChanged();
        } else {
          equipBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder addAllEquip(
          Iterable<? extends EquipAData.EquipA> values) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          super.addAll(values, equip_);
          onChanged();
        } else {
          equipBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder clearEquip() {
        if (equipBuilder_ == null) {
          equip_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          equipBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public Builder removeEquip(int index) {
        if (equipBuilder_ == null) {
          ensureEquipIsMutable();
          equip_.remove(index);
          onChanged();
        } else {
          equipBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public EquipAData.EquipA.Builder getEquipBuilder(
          int index) {
        return getEquipFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public EquipAData.EquipAOrBuilder getEquipOrBuilder(
          int index) {
        if (equipBuilder_ == null) {
          return equip_.get(index);  } else {
          return equipBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public java.util.List<? extends EquipAData.EquipAOrBuilder> 
           getEquipOrBuilderList() {
        if (equipBuilder_ != null) {
          return equipBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(equip_);
        }
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public EquipAData.EquipA.Builder addEquipBuilder() {
        return getEquipFieldBuilder().addBuilder(
            EquipAData.EquipA.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public EquipAData.EquipA.Builder addEquipBuilder(
          int index) {
        return getEquipFieldBuilder().addBuilder(
            index, EquipAData.EquipA.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.EquipA equip = 3;</code>
       *
       * <pre>
       *物品奖励
       * </pre>
       */
      public java.util.List<EquipAData.EquipA.Builder> 
           getEquipBuilderList() {
        return getEquipFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          EquipAData.EquipA, EquipAData.EquipA.Builder, EquipAData.EquipAOrBuilder> 
          getEquipFieldBuilder() {
        if (equipBuilder_ == null) {
          equipBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              EquipAData.EquipA, EquipAData.EquipA.Builder, EquipAData.EquipAOrBuilder>(
                  equip_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          equip_ = null;
        }
        return equipBuilder_;
      }

      // repeated .protocol.DefaultPet defaultPet = 4;
      private java.util.List<DefaultPet> defaultPet_ =
        java.util.Collections.emptyList();
      private void ensureDefaultPetIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          defaultPet_ = new java.util.ArrayList<DefaultPet>(defaultPet_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          DefaultPet, DefaultPet.Builder, DefaultPetOrBuilder> defaultPetBuilder_;

      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public java.util.List<DefaultPet> getDefaultPetList() {
        if (defaultPetBuilder_ == null) {
          return java.util.Collections.unmodifiableList(defaultPet_);
        } else {
          return defaultPetBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public int getDefaultPetCount() {
        if (defaultPetBuilder_ == null) {
          return defaultPet_.size();
        } else {
          return defaultPetBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public DefaultPet getDefaultPet(int index) {
        if (defaultPetBuilder_ == null) {
          return defaultPet_.get(index);
        } else {
          return defaultPetBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder setDefaultPet(
          int index, DefaultPet value) {
        if (defaultPetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDefaultPetIsMutable();
          defaultPet_.set(index, value);
          onChanged();
        } else {
          defaultPetBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder setDefaultPet(
          int index, DefaultPet.Builder builderForValue) {
        if (defaultPetBuilder_ == null) {
          ensureDefaultPetIsMutable();
          defaultPet_.set(index, builderForValue.build());
          onChanged();
        } else {
          defaultPetBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder addDefaultPet(DefaultPet value) {
        if (defaultPetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDefaultPetIsMutable();
          defaultPet_.add(value);
          onChanged();
        } else {
          defaultPetBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder addDefaultPet(
          int index, DefaultPet value) {
        if (defaultPetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDefaultPetIsMutable();
          defaultPet_.add(index, value);
          onChanged();
        } else {
          defaultPetBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder addDefaultPet(
          DefaultPet.Builder builderForValue) {
        if (defaultPetBuilder_ == null) {
          ensureDefaultPetIsMutable();
          defaultPet_.add(builderForValue.build());
          onChanged();
        } else {
          defaultPetBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder addDefaultPet(
          int index, DefaultPet.Builder builderForValue) {
        if (defaultPetBuilder_ == null) {
          ensureDefaultPetIsMutable();
          defaultPet_.add(index, builderForValue.build());
          onChanged();
        } else {
          defaultPetBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder addAllDefaultPet(
          Iterable<? extends DefaultPet> values) {
        if (defaultPetBuilder_ == null) {
          ensureDefaultPetIsMutable();
          super.addAll(values, defaultPet_);
          onChanged();
        } else {
          defaultPetBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder clearDefaultPet() {
        if (defaultPetBuilder_ == null) {
          defaultPet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          defaultPetBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public Builder removeDefaultPet(int index) {
        if (defaultPetBuilder_ == null) {
          ensureDefaultPetIsMutable();
          defaultPet_.remove(index);
          onChanged();
        } else {
          defaultPetBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public DefaultPet.Builder getDefaultPetBuilder(
          int index) {
        return getDefaultPetFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public DefaultPetOrBuilder getDefaultPetOrBuilder(
          int index) {
        if (defaultPetBuilder_ == null) {
          return defaultPet_.get(index);  } else {
          return defaultPetBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public java.util.List<? extends DefaultPetOrBuilder> 
           getDefaultPetOrBuilderList() {
        if (defaultPetBuilder_ != null) {
          return defaultPetBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(defaultPet_);
        }
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public DefaultPet.Builder addDefaultPetBuilder() {
        return getDefaultPetFieldBuilder().addBuilder(
            DefaultPet.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public DefaultPet.Builder addDefaultPetBuilder(
          int index) {
        return getDefaultPetFieldBuilder().addBuilder(
            index, DefaultPet.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.DefaultPet defaultPet = 4;</code>
       *
       * <pre>
       *默认的宠物生成方式
       * </pre>
       */
      public java.util.List<DefaultPet.Builder> 
           getDefaultPetBuilderList() {
        return getDefaultPetFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          DefaultPet, DefaultPet.Builder, DefaultPetOrBuilder> 
          getDefaultPetFieldBuilder() {
        if (defaultPetBuilder_ == null) {
          defaultPetBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              DefaultPet, DefaultPet.Builder, DefaultPetOrBuilder>(
                  defaultPet_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          defaultPet_ = null;
        }
        return defaultPetBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Attachment)
    }

    static {
      defaultInstance = new Attachment(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Attachment)
  }

  public interface RequestOperateMailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *  //0删除 1领取 2已读
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *  //0删除 1领取 2已读
     * </pre>
     */
    int getType();

    // required int32 mid = 2;
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *邮件唯一id		
     * </pre>
     */
    boolean hasMid();
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *邮件唯一id		
     * </pre>
     */
    int getMid();
  }
  /**
   * Protobuf type {@code protocol.RequestOperateMail}
   *
   * <pre>
   * 1110操作邮件
   * </pre>
   */
  public static final class RequestOperateMail extends
      com.google.protobuf.GeneratedMessage
      implements RequestOperateMailOrBuilder {
    // Use RequestOperateMail.newBuilder() to construct.
    private RequestOperateMail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestOperateMail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestOperateMail defaultInstance;
    public static RequestOperateMail getDefaultInstance() {
      return defaultInstance;
    }

    public RequestOperateMail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestOperateMail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mid_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_RequestOperateMail_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_RequestOperateMail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RequestOperateMail.class, Builder.class);
    }

    public static com.google.protobuf.Parser<RequestOperateMail> PARSER =
        new com.google.protobuf.AbstractParser<RequestOperateMail>() {
      public RequestOperateMail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestOperateMail(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<RequestOperateMail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *  //0删除 1领取 2已读
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *  //0删除 1领取 2已读
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // required int32 mid = 2;
    public static final int MID_FIELD_NUMBER = 2;
    private int mid_;
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *邮件唯一id		
     * </pre>
     */
    public boolean hasMid() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *邮件唯一id		
     * </pre>
     */
    public int getMid() {
      return mid_;
    }

    private void initFields() {
      type_ = 0;
      mid_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, mid_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static RequestOperateMail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestOperateMail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestOperateMail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestOperateMail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestOperateMail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestOperateMail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static RequestOperateMail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static RequestOperateMail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static RequestOperateMail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestOperateMail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(RequestOperateMail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestOperateMail}
     *
     * <pre>
     * 1110操作邮件
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements RequestOperateMailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_RequestOperateMail_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_RequestOperateMail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RequestOperateMail.class, Builder.class);
      }

      // Construct using protocol.MailData.RequestOperateMail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        mid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_RequestOperateMail_descriptor;
      }

      public RequestOperateMail getDefaultInstanceForType() {
        return RequestOperateMail.getDefaultInstance();
      }

      public RequestOperateMail build() {
        RequestOperateMail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RequestOperateMail buildPartial() {
        RequestOperateMail result = new RequestOperateMail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.mid_ = mid_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RequestOperateMail) {
          return mergeFrom((RequestOperateMail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RequestOperateMail other) {
        if (other == RequestOperateMail.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasMid()) {
          setMid(other.getMid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (!hasMid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RequestOperateMail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RequestOperateMail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *  //0删除 1领取 2已读
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *  //0删除 1领取 2已读
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *  //0删除 1领取 2已读
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *  //0删除 1领取 2已读
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // required int32 mid = 2;
      private int mid_ ;
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *邮件唯一id		
       * </pre>
       */
      public boolean hasMid() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *邮件唯一id		
       * </pre>
       */
      public int getMid() {
        return mid_;
      }
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *邮件唯一id		
       * </pre>
       */
      public Builder setMid(int value) {
        bitField0_ |= 0x00000002;
        mid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *邮件唯一id		
       * </pre>
       */
      public Builder clearMid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mid_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestOperateMail)
    }

    static {
      defaultInstance = new RequestOperateMail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestOperateMail)
  }

  public interface ResponseOperateMailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // required int32 mid = 2;
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *消息号
     * </pre>
     */
    boolean hasMid();
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *消息号
     * </pre>
     */
    int getMid();

    // required int32 type = 3;
    /**
     * <code>required int32 type = 3;</code>
     *
     * <pre>
     *0删除 1领取 2已读
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 3;</code>
     *
     * <pre>
     *0删除 1领取 2已读
     * </pre>
     */
    int getType();
  }
  /**
   * Protobuf type {@code protocol.ResponseOperateMail}
   *
   * <pre>
   *2110	操作邮件
   * </pre>
   */
  public static final class ResponseOperateMail extends
      com.google.protobuf.GeneratedMessage
      implements ResponseOperateMailOrBuilder {
    // Use ResponseOperateMail.newBuilder() to construct.
    private ResponseOperateMail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseOperateMail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseOperateMail defaultInstance;
    public static ResponseOperateMail getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseOperateMail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseOperateMail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mid_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              type_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_ResponseOperateMail_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_ResponseOperateMail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ResponseOperateMail.class, Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseOperateMail> PARSER =
        new com.google.protobuf.AbstractParser<ResponseOperateMail>() {
      public ResponseOperateMail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseOperateMail(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<ResponseOperateMail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 mid = 2;
    public static final int MID_FIELD_NUMBER = 2;
    private int mid_;
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *消息号
     * </pre>
     */
    public boolean hasMid() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 mid = 2;</code>
     *
     * <pre>
     *消息号
     * </pre>
     */
    public int getMid() {
      return mid_;
    }

    // required int32 type = 3;
    public static final int TYPE_FIELD_NUMBER = 3;
    private int type_;
    /**
     * <code>required int32 type = 3;</code>
     *
     * <pre>
     *0删除 1领取 2已读
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 type = 3;</code>
     *
     * <pre>
     *0删除 1领取 2已读
     * </pre>
     */
    public int getType() {
      return type_;
    }

    private void initFields() {
      errorId_ = 0;
      mid_ = 0;
      type_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, mid_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, type_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mid_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ResponseOperateMail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResponseOperateMail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResponseOperateMail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ResponseOperateMail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ResponseOperateMail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ResponseOperateMail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ResponseOperateMail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ResponseOperateMail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ResponseOperateMail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ResponseOperateMail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ResponseOperateMail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseOperateMail}
     *
     * <pre>
     *2110	操作邮件
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ResponseOperateMailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_ResponseOperateMail_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_ResponseOperateMail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ResponseOperateMail.class, Builder.class);
      }

      // Construct using protocol.MailData.ResponseOperateMail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        mid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_ResponseOperateMail_descriptor;
      }

      public ResponseOperateMail getDefaultInstanceForType() {
        return ResponseOperateMail.getDefaultInstance();
      }

      public ResponseOperateMail build() {
        ResponseOperateMail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ResponseOperateMail buildPartial() {
        ResponseOperateMail result = new ResponseOperateMail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.mid_ = mid_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.type_ = type_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ResponseOperateMail) {
          return mergeFrom((ResponseOperateMail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ResponseOperateMail other) {
        if (other == ResponseOperateMail.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasMid()) {
          setMid(other.getMid());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasMid()) {
          
          return false;
        }
        if (!hasType()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ResponseOperateMail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ResponseOperateMail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 mid = 2;
      private int mid_ ;
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *消息号
       * </pre>
       */
      public boolean hasMid() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *消息号
       * </pre>
       */
      public int getMid() {
        return mid_;
      }
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *消息号
       * </pre>
       */
      public Builder setMid(int value) {
        bitField0_ |= 0x00000002;
        mid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mid = 2;</code>
       *
       * <pre>
       *消息号
       * </pre>
       */
      public Builder clearMid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mid_ = 0;
        onChanged();
        return this;
      }

      // required int32 type = 3;
      private int type_ ;
      /**
       * <code>required int32 type = 3;</code>
       *
       * <pre>
       *0删除 1领取 2已读
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 type = 3;</code>
       *
       * <pre>
       *0删除 1领取 2已读
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 3;</code>
       *
       * <pre>
       *0删除 1领取 2已读
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000004;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 3;</code>
       *
       * <pre>
       *0删除 1领取 2已读
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        type_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseOperateMail)
    }

    static {
      defaultInstance = new ResponseOperateMail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseOperateMail)
  }

  public interface ReportNewMailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.Mail newMail = 1;
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    boolean hasNewMail();
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    Mail getNewMail();
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    MailOrBuilder getNewMailOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ReportNewMail}
   *
   * <pre>
   *通知新邮件2111
   * </pre>
   */
  public static final class ReportNewMail extends
      com.google.protobuf.GeneratedMessage
      implements ReportNewMailOrBuilder {
    // Use ReportNewMail.newBuilder() to construct.
    private ReportNewMail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ReportNewMail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ReportNewMail defaultInstance;
    public static ReportNewMail getDefaultInstance() {
      return defaultInstance;
    }

    public ReportNewMail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ReportNewMail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              Mail.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = newMail_.toBuilder();
              }
              newMail_ = input.readMessage(Mail.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newMail_);
                newMail_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_ReportNewMail_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_ReportNewMail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              ReportNewMail.class, Builder.class);
    }

    public static com.google.protobuf.Parser<ReportNewMail> PARSER =
        new com.google.protobuf.AbstractParser<ReportNewMail>() {
      public ReportNewMail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReportNewMail(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<ReportNewMail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.Mail newMail = 1;
    public static final int NEWMAIL_FIELD_NUMBER = 1;
    private Mail newMail_;
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    public boolean hasNewMail() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    public Mail getNewMail() {
      return newMail_;
    }
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    public MailOrBuilder getNewMailOrBuilder() {
      return newMail_;
    }

    private void initFields() {
      newMail_ = Mail.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNewMail()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getNewMail().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, newMail_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, newMail_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static ReportNewMail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReportNewMail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReportNewMail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static ReportNewMail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static ReportNewMail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ReportNewMail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static ReportNewMail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static ReportNewMail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static ReportNewMail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static ReportNewMail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(ReportNewMail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ReportNewMail}
     *
     * <pre>
     *通知新邮件2111
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements ReportNewMailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_ReportNewMail_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_ReportNewMail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                ReportNewMail.class, Builder.class);
      }

      // Construct using protocol.MailData.ReportNewMail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getNewMailFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (newMailBuilder_ == null) {
          newMail_ = Mail.getDefaultInstance();
        } else {
          newMailBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_ReportNewMail_descriptor;
      }

      public ReportNewMail getDefaultInstanceForType() {
        return ReportNewMail.getDefaultInstance();
      }

      public ReportNewMail build() {
        ReportNewMail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public ReportNewMail buildPartial() {
        ReportNewMail result = new ReportNewMail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (newMailBuilder_ == null) {
          result.newMail_ = newMail_;
        } else {
          result.newMail_ = newMailBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof ReportNewMail) {
          return mergeFrom((ReportNewMail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(ReportNewMail other) {
        if (other == ReportNewMail.getDefaultInstance()) return this;
        if (other.hasNewMail()) {
          mergeNewMail(other.getNewMail());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNewMail()) {
          
          return false;
        }
        if (!getNewMail().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        ReportNewMail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (ReportNewMail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.Mail newMail = 1;
      private Mail newMail_ = Mail.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          Mail, Mail.Builder, MailOrBuilder> newMailBuilder_;
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public boolean hasNewMail() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Mail getNewMail() {
        if (newMailBuilder_ == null) {
          return newMail_;
        } else {
          return newMailBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder setNewMail(Mail value) {
        if (newMailBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newMail_ = value;
          onChanged();
        } else {
          newMailBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder setNewMail(
          Mail.Builder builderForValue) {
        if (newMailBuilder_ == null) {
          newMail_ = builderForValue.build();
          onChanged();
        } else {
          newMailBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder mergeNewMail(Mail value) {
        if (newMailBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              newMail_ != Mail.getDefaultInstance()) {
            newMail_ =
              Mail.newBuilder(newMail_).mergeFrom(value).buildPartial();
          } else {
            newMail_ = value;
          }
          onChanged();
        } else {
          newMailBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder clearNewMail() {
        if (newMailBuilder_ == null) {
          newMail_ = Mail.getDefaultInstance();
          onChanged();
        } else {
          newMailBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Mail.Builder getNewMailBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNewMailFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public MailOrBuilder getNewMailOrBuilder() {
        if (newMailBuilder_ != null) {
          return newMailBuilder_.getMessageOrBuilder();
        } else {
          return newMail_;
        }
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          Mail, Mail.Builder, MailOrBuilder> 
          getNewMailFieldBuilder() {
        if (newMailBuilder_ == null) {
          newMailBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              Mail, Mail.Builder, MailOrBuilder>(
                  newMail_,
                  getParentForChildren(),
                  isClean());
          newMail_ = null;
        }
        return newMailBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ReportNewMail)
    }

    static {
      defaultInstance = new ReportNewMail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ReportNewMail)
  }

  public interface RequestNewMailTestOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestNewMailTest}
   *
   * <pre>
   * 1340 获取一个新邮件
   * </pre>
   */
  public static final class RequestNewMailTest extends
      com.google.protobuf.GeneratedMessage
      implements RequestNewMailTestOrBuilder {
    // Use RequestNewMailTest.newBuilder() to construct.
    private RequestNewMailTest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestNewMailTest(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestNewMailTest defaultInstance;
    public static RequestNewMailTest getDefaultInstance() {
      return defaultInstance;
    }

    public RequestNewMailTest getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestNewMailTest(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_RequestNewMailTest_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_RequestNewMailTest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RequestNewMailTest.class, Builder.class);
    }

    public static com.google.protobuf.Parser<RequestNewMailTest> PARSER =
        new com.google.protobuf.AbstractParser<RequestNewMailTest>() {
      public RequestNewMailTest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestNewMailTest(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<RequestNewMailTest> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static RequestNewMailTest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestNewMailTest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestNewMailTest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestNewMailTest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestNewMailTest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestNewMailTest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static RequestNewMailTest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static RequestNewMailTest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static RequestNewMailTest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestNewMailTest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(RequestNewMailTest prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestNewMailTest}
     *
     * <pre>
     * 1340 获取一个新邮件
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements RequestNewMailTestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_RequestNewMailTest_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_RequestNewMailTest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RequestNewMailTest.class, Builder.class);
      }

      // Construct using protocol.MailData.RequestNewMailTest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_RequestNewMailTest_descriptor;
      }

      public RequestNewMailTest getDefaultInstanceForType() {
        return RequestNewMailTest.getDefaultInstance();
      }

      public RequestNewMailTest build() {
        RequestNewMailTest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RequestNewMailTest buildPartial() {
        RequestNewMailTest result = new RequestNewMailTest(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RequestNewMailTest) {
          return mergeFrom((RequestNewMailTest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RequestNewMailTest other) {
        if (other == RequestNewMailTest.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RequestNewMailTest parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RequestNewMailTest) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestNewMailTest)
    }

    static {
      defaultInstance = new RequestNewMailTest(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestNewMailTest)
  }

  public interface RequestCreateNewMailOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.Mail newMail = 1;
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    boolean hasNewMail();
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    Mail getNewMail();
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    MailOrBuilder getNewMailOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.RequestCreateNewMail}
   *
   * <pre>
   *1400 创建一个新邮件
   * </pre>
   */
  public static final class RequestCreateNewMail extends
      com.google.protobuf.GeneratedMessage
      implements RequestCreateNewMailOrBuilder {
    // Use RequestCreateNewMail.newBuilder() to construct.
    private RequestCreateNewMail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestCreateNewMail(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestCreateNewMail defaultInstance;
    public static RequestCreateNewMail getDefaultInstance() {
      return defaultInstance;
    }

    public RequestCreateNewMail getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestCreateNewMail(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              Mail.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = newMail_.toBuilder();
              }
              newMail_ = input.readMessage(Mail.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newMail_);
                newMail_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return MailData.internal_static_protocol_RequestCreateNewMail_descriptor;
    }

    protected FieldAccessorTable
        internalGetFieldAccessorTable() {
      return MailData.internal_static_protocol_RequestCreateNewMail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              RequestCreateNewMail.class, Builder.class);
    }

    public static com.google.protobuf.Parser<RequestCreateNewMail> PARSER =
        new com.google.protobuf.AbstractParser<RequestCreateNewMail>() {
      public RequestCreateNewMail parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestCreateNewMail(input, extensionRegistry);
      }
    };

    @Override
    public com.google.protobuf.Parser<RequestCreateNewMail> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.Mail newMail = 1;
    public static final int NEWMAIL_FIELD_NUMBER = 1;
    private Mail newMail_;
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    public boolean hasNewMail() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    public Mail getNewMail() {
      return newMail_;
    }
    /**
     * <code>required .protocol.Mail newMail = 1;</code>
     */
    public MailOrBuilder getNewMailOrBuilder() {
      return newMail_;
    }

    private void initFields() {
      newMail_ = Mail.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNewMail()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getNewMail().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, newMail_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, newMail_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @Override
    protected Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static RequestCreateNewMail parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestCreateNewMail parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestCreateNewMail parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static RequestCreateNewMail parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static RequestCreateNewMail parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestCreateNewMail parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static RequestCreateNewMail parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static RequestCreateNewMail parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static RequestCreateNewMail parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static RequestCreateNewMail parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(RequestCreateNewMail prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @Override
    protected Builder newBuilderForType(
        BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestCreateNewMail}
     *
     * <pre>
     *1400 创建一个新邮件
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements RequestCreateNewMailOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return MailData.internal_static_protocol_RequestCreateNewMail_descriptor;
      }

      protected FieldAccessorTable
          internalGetFieldAccessorTable() {
        return MailData.internal_static_protocol_RequestCreateNewMail_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                RequestCreateNewMail.class, Builder.class);
      }

      // Construct using protocol.MailData.RequestCreateNewMail.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getNewMailFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (newMailBuilder_ == null) {
          newMail_ = Mail.getDefaultInstance();
        } else {
          newMailBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return MailData.internal_static_protocol_RequestCreateNewMail_descriptor;
      }

      public RequestCreateNewMail getDefaultInstanceForType() {
        return RequestCreateNewMail.getDefaultInstance();
      }

      public RequestCreateNewMail build() {
        RequestCreateNewMail result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public RequestCreateNewMail buildPartial() {
        RequestCreateNewMail result = new RequestCreateNewMail(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (newMailBuilder_ == null) {
          result.newMail_ = newMail_;
        } else {
          result.newMail_ = newMailBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof RequestCreateNewMail) {
          return mergeFrom((RequestCreateNewMail)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(RequestCreateNewMail other) {
        if (other == RequestCreateNewMail.getDefaultInstance()) return this;
        if (other.hasNewMail()) {
          mergeNewMail(other.getNewMail());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNewMail()) {
          
          return false;
        }
        if (!getNewMail().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        RequestCreateNewMail parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (RequestCreateNewMail) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.Mail newMail = 1;
      private Mail newMail_ = Mail.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          Mail, Mail.Builder, MailOrBuilder> newMailBuilder_;
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public boolean hasNewMail() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Mail getNewMail() {
        if (newMailBuilder_ == null) {
          return newMail_;
        } else {
          return newMailBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder setNewMail(Mail value) {
        if (newMailBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newMail_ = value;
          onChanged();
        } else {
          newMailBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder setNewMail(
          Mail.Builder builderForValue) {
        if (newMailBuilder_ == null) {
          newMail_ = builderForValue.build();
          onChanged();
        } else {
          newMailBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder mergeNewMail(Mail value) {
        if (newMailBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              newMail_ != Mail.getDefaultInstance()) {
            newMail_ =
              Mail.newBuilder(newMail_).mergeFrom(value).buildPartial();
          } else {
            newMail_ = value;
          }
          onChanged();
        } else {
          newMailBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Builder clearNewMail() {
        if (newMailBuilder_ == null) {
          newMail_ = Mail.getDefaultInstance();
          onChanged();
        } else {
          newMailBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public Mail.Builder getNewMailBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNewMailFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      public MailOrBuilder getNewMailOrBuilder() {
        if (newMailBuilder_ != null) {
          return newMailBuilder_.getMessageOrBuilder();
        } else {
          return newMail_;
        }
      }
      /**
       * <code>required .protocol.Mail newMail = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          Mail, Mail.Builder, MailOrBuilder> 
          getNewMailFieldBuilder() {
        if (newMailBuilder_ == null) {
          newMailBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              Mail, Mail.Builder, MailOrBuilder>(
                  newMail_,
                  getParentForChildren(),
                  isClean());
          newMail_ = null;
        }
        return newMailBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestCreateNewMail)
    }

    static {
      defaultInstance = new RequestCreateNewMail(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestCreateNewMail)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_DefaultPet_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_DefaultPet_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Mail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Mail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Attachment_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Attachment_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestOperateMail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestOperateMail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseOperateMail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseOperateMail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ReportNewMail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ReportNewMail_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestNewMailTest_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestNewMailTest_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestCreateNewMail_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestCreateNewMail_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    String[] descriptorData = {
      "\n\nmail.proto\022\010protocol\032\nitem.proto\032\014equi" +
      "pA.proto\"*\n\nDefaultPet\022\r\n\005petId\030\001 \002(\005\022\r\n" +
      "\005isEgg\030\002 \002(\005\"\330\001\n\004Mail\022\013\n\003mid\030\001 \002(\005\022\023\n\013su" +
      "bjectType\030\002 \002(\t\022\016\n\006sender\030\003 \001(\t\022\r\n\005owner" +
      "\030\004 \001(\t\022\r\n\005title\030\005 \001(\t\022\017\n\007content\030\006 \002(\t\022\016" +
      "\n\006status\030\007 \002(\005\022\021\n\ttimeStamp\030\010 \002(\003\022\'\n\tatt" +
      "chment\030\t \001(\0132\024.protocol.Attachment\022\023\n\013ov" +
      "erdueTime\030\n \002(\003\022\016\n\006mailId\030\013 \002(\005\"\203\001\n\nAtta" +
      "chment\022\014\n\004type\030\001 \002(\005\022\034\n\004item\030\002 \003(\0132\016.pro" +
      "tocol.Item\022\037\n\005equip\030\003 \003(\0132\020.protocol.Equ",
      "ipA\022(\n\ndefaultPet\030\004 \003(\0132\024.protocol.Defau" +
      "ltPet\"/\n\022RequestOperateMail\022\014\n\004type\030\001 \002(" +
      "\005\022\013\n\003mid\030\002 \002(\005\"A\n\023ResponseOperateMail\022\017\n" +
      "\007errorId\030\001 \002(\005\022\013\n\003mid\030\002 \002(\005\022\014\n\004type\030\003 \002(" +
      "\005\"0\n\rReportNewMail\022\037\n\007newMail\030\001 \002(\0132\016.pr" +
      "otocol.Mail\"\024\n\022RequestNewMailTest\"7\n\024Req" +
      "uestCreateNewMail\022\037\n\007newMail\030\001 \002(\0132\016.pro" +
      "tocol.MailB\nB\010MailData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_DefaultPet_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_DefaultPet_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_DefaultPet_descriptor,
              new String[] { "PetId", "IsEgg", });
          internal_static_protocol_Mail_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_Mail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Mail_descriptor,
              new String[] { "Mid", "SubjectType", "Sender", "Owner", "Title", "Content", "Status", "TimeStamp", "Attchment", "OverdueTime", "MailId", });
          internal_static_protocol_Attachment_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_Attachment_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Attachment_descriptor,
              new String[] { "Type", "Item", "Equip", "DefaultPet", });
          internal_static_protocol_RequestOperateMail_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestOperateMail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestOperateMail_descriptor,
              new String[] { "Type", "Mid", });
          internal_static_protocol_ResponseOperateMail_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponseOperateMail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseOperateMail_descriptor,
              new String[] { "ErrorId", "Mid", "Type", });
          internal_static_protocol_ReportNewMail_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_ReportNewMail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ReportNewMail_descriptor,
              new String[] { "NewMail", });
          internal_static_protocol_RequestNewMailTest_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_protocol_RequestNewMailTest_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestNewMailTest_descriptor,
              new String[] { });
          internal_static_protocol_RequestCreateNewMail_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_protocol_RequestCreateNewMail_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestCreateNewMail_descriptor,
              new String[] { "NewMail", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          ItemData.getDescriptor(),
          EquipAData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
