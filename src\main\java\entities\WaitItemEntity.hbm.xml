<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.WaitItemEntity" table="waitItem" schema="" catalog="super_star_fruit">
        <id name="id" column="id">
                <generator class="identity"/>
                </id>
        <property name="lastQueryTime" column="lastQueryTime"/>
        <property name="uid" column="uid"/>
        <property name="rewardTotalNums" column="rewardTotalNums"/>
        <property name="latelyWaitItemId" column="latelyWaitItemId"/>
        <property name="latelyWaitTime" column="latelyWaitTime"/>
        <property name="upToParItemIdCollections" column="upToParItemIdCollections"/>
        <property name="ItemTypeNums" column="ItemTypeNums"/>
        <property name="bagLevel" column="bagLevel"/>
    </class>
</hibernate-mapping>