package server;

import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;
import module.room.RoomService;
import module.synchronization.RoomManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ProtoData;

/**
 * Created by nara on 2017/12/8.
 */
public class SuperClientHandler extends ChannelInboundHandlerAdapter {
    private static final Logger logger = LoggerFactory.getLogger(SuperClientHandler.class);

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg)
            throws Exception {
        // messageReceived方法,名称很别扭，像是一个内部方法.
        SuperProtocol body = (SuperProtocol) msg;
        if (body.getMsgId() == ProtoData.SToC.RESPONSESTAMP_VALUE)
            return;
        if (body.getMsgId() == ProtoData.MToS.RESPONSEENTERMISSION_VALUE){
            RoomManager.boardcastToFight(body.getContent());
        }else if (body.getMsgId() == ProtoData.MToS.REQUESTOVERMISSION_VALUE){
            RoomService.getInstance().overMission(body.getContent());
        }
        logger.info("client接收到服务器返回的消息:" + body.toString());
        logger.info(new String(body.getContent()));
        ReferenceCountUtil.release(body);
    }
}
