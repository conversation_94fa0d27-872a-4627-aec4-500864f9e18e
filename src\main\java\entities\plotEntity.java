package entities;
import javax.persistence.*;
@Entity
@Table(name = "plot", schema = "", catalog = "super_star_fruit")
public class plotEntity {
    private int id;
    private int chapter;
    private int section;
    private String uid;

    @Override
    public String toString() {
        return "plotEntity{" +
                "id=" + id +
                ", chapter=" + chapter +
                ", section=" + section +
                ", uid='" + uid + '\'' +
                '}';
    }
    @Id
    @Basic
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "chapter")
    public int getChapter() {
        return chapter;
    }

    public void setChapter(int chapter) {
        this.chapter = chapter;
    }
    @Basic
    @Column(name = "section")
    public int getSection() {
        return section;
    }

    public void setSection(int section) {
        this.section = section;
    }
    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
}
