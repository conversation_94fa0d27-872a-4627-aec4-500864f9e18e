package module.plot;
import com.google.protobuf.InvalidProtocolBufferException;
import entities.plotEntity;
import manager.MySql;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.plotData;
public class plotService {
    private static Logger log = LoggerFactory.getLogger(plotService.class);
    private static plotService inst = null;
    public static plotService getInstance() {
        if (inst == null) {
            inst = new plotService();
        }
        return inst;
    }
    public byte[] Requestplotid(byte[] bytes, String uid) {
        plotData.RequestPlont requestLevelNum = null;
        plotData.ResponsePlont.Builder builder = plotData.ResponsePlont.newBuilder();
        try {
            requestLevelNum = plotData.RequestPlont.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        if (requestLevelNum != null) {
            plotEntity levelEntity = PlotDao.getInstance().queryUid(uid);
            if (levelEntity == null) {
                plotEntity ab =new plotEntity();
                ab.setChapter(requestLevelNum.getChapterd());
                ab.setSection(requestLevelNum.getSectiond());
                ab.setUid(uid);
                MySql.insert(ab);
            } else {
                levelEntity.setChapter(requestLevelNum.getChapterd());
                levelEntity.setSection(requestLevelNum.getSectiond());

                MySql.update(levelEntity);
            }

        } else {
            log.error("error");
        }
        return builder.build().toByteArray();
    }
    public byte[] Selectplotid( String uid) {
        plotData.RequestChap requestLevelNum = null;
        plotData.ResponseChap.Builder builder = plotData.ResponseChap.newBuilder();

        plotEntity levelEntity = PlotDao.getInstance().queryUid(uid);
        if (levelEntity==null){
            builder.setChapter(1);
            builder.setSection(1);
        }else { builder.setChapter(levelEntity.getChapter());
            builder.setSection(levelEntity.getSection());}

        return builder.build().toByteArray();
    }
}
