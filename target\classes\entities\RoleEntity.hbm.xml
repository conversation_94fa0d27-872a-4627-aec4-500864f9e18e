<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.RoleEntity" table="role" schema="super_star_fruit">
        <id name="id" column="id"/>
        <property name="type" column="type"/>
        <property name="uid" column="uid"/>
        <property name="signaTure" column="signature"/>
        <property name="ranknumber" column="ranknumber"/>
        <property name="userid" column="userid"/>
        <property name="score" column="score"/>
        <property name="head" column="head"/>
        <property name="name" column="name"/>
        <property name="action" column="action"/>
        <property name="actionstamp" column="actionstamp"/>
        <property name="lv" column="lv"/>
        <property name="sex" column="sex"/>
        <property name="exp" column="exp"/>
        <property name="bagmax" column="bagmax"/>
        <property name="robot" column="robot"/>
        <property name="dailyGameTime" column="dailygametime"/>
        <property name="dailyLoginLoop" column="dailyLoginLoop"/>
        <property name="dailyLoginflag" column="dailyLoginflag"/>
        <property name="totalmoney" column="totalmoney"/>
        <property name="sign" column="sign"/>
        <property name="firstRecharge" column="firstRecharge"/>
    </class>
</hibernate-mapping>