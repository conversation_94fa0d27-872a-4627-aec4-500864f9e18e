package entities;
import java.util.Date;
import javax.persistence.*;


@Entity
@Table(name = "limited_time_reward_complete_task", schema = "", catalog = "super_star_fruit")
public class LimitedTimeRewardCompleteTaskEntity {
    private int id;
    private String uid;
    private Date time;
    private int taskId;
    private boolean isComplete;
    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
    @Basic
    @Column(name = "time")
    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }
    @Basic
    @Column(name = "task_id")
    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int dailyTaskId) {
        this.taskId = dailyTaskId;
    }
    @Basic
    @Column(name = "is_complete")
    public boolean isComplete() {
        return isComplete;
    }

    public void setComplete(boolean complete) {
        isComplete = complete;
    }

    @Override
    public String toString() {
        return "CompleteAchievementEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", time=" + time +
                ", dailyTaskId=" + taskId +
                ", isComplete=" + isComplete +
                '}';
    }
}
