<?xml version='1.0' encoding='utf-8' ?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd"><hibernate-mapping>
    <class name="entities.EquipAEntity" table="equida" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="eid" column="eid"/>
        <property name="hp" column="hp"/>
        <property name="ad" column="ad"/>
        <property name="ap" column="ap"/>
        <property name="arm" column="arm"/>
        <property name="mdf" column="mdf"/>
        <property name="uid" column="uid"/>
        <property name="sp" column="sp"/>
        <property name="ead" column="adFactor"/>
        <property name="eap" column="apFactor"/>
        <property name="earm" column="armFactor"/>
        <property name="ehp" column="mdfFactor"/>
        <property name="emdf" column="speedFactor"/>
        <property name="esp" column="speedFactor"/>
    </class>
</hibernate-mapping>