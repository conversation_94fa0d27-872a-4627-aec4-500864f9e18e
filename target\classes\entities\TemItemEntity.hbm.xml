<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.TemItemEntity" table="tem_item" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="uid" column="uid"/>
        <property name="wood" column="wood"/>
        <property name="leaf" column="leaf"/>
        <property name="water" column="water"/>
        <property name="soil" column="soil"/>
        <property name="battle" column="battle"/>
        <property name="run" column="run"/>
        <property name="puzzle" column="puzzle"/>
    </class>
</hibernate-mapping>