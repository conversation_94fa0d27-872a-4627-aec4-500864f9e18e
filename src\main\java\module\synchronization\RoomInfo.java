package module.synchronization;

import java.util.LinkedList;
import java.util.List;

/**
 * Created by nara on 2018/5/31.
 */
public class RoomInfo {
    private int type;//1竞技 2竞速
    private String name;
    private String pwd;
    private boolean isPlaying;
    private boolean isRobot=false;
    private List<RoomRoleInfo> roomRoleList;
    private long createRoomTime;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public void setIsPlaying(boolean isPlaying) {
        this.isPlaying = isPlaying;
    }

    public RoomInfo(){
        roomRoleList = new LinkedList<RoomRoleInfo>();
    }

    public List<RoomRoleInfo> getRoomRoleList() {
        return roomRoleList;
    }

    public void setRoomRoleList(List<RoomRoleInfo> roomRoleList) {
        this.roomRoleList = roomRoleList;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isRobot() {
        return isRobot;
    }

    public void setRobot(boolean robot) {
        isRobot = robot;
    }

    public long getCreateRoomTime() {
        return createRoomTime;
    }

    public void setCreateRoomTime(long createRoomTime) {
        this.createRoomTime = createRoomTime;
    }

    @Override
    public String toString() {
        return "RoomInfo{" +
                "type=" + type +
                ", name='" + name + '\'' +
                ", pwd='" + pwd + '\'' +
                ", isPlaying=" + isPlaying +
                ", isRobot=" + isRobot +
                ", roomRoleList=" + roomRoleList +
                ", createRoomTime=" + createRoomTime +
                '}';
    }
}
