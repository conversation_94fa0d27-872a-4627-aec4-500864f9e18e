//import com.sun.org.apache.regexp.internal.RE;
import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import org.springframework.scheduling.annotation.EnableScheduling;

import common.SuperConfig;
import io.netty.channel.ChannelHandlerContext;
import manager.InitData;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import module.event.EventUtils;
import module.item.ItemService;
import module.pvp.PVPRank;
import protocol.ProtoData;
import protocol.UserData;
import server.SuperServer;
import server.SuperServerHandler;
import sun.misc.Signal;
import sun.misc.SignalHandler;

/**
 * Created by nara on 2017/11/16.
 */
@EnableScheduling
public class superStar {

    public static void main(String[] args) {

        final Boolean isProxy;
        if( args.length > 0 && !args[0].isEmpty() )
            isProxy = Boolean.parseBoolean(args[0]);
        else
            isProxy = false;

        Signal signal = new Signal(getOSSignalType());
        Signal.handle(signal, new SignalHandler() {
            public void handle(Signal signal) {
                Thread t=new Thread(new ShutdownHook());
                Runtime.getRuntime().addShutdownHook(t);
                Runtime.getRuntime().exit(0);
            }
        });
          SuperConfig.setGAMEMODE(SuperConfig.GAMEMODE_MAIN);
        Redis.getInstance();
        if (SuperConfig.getGAMEMODE() != SuperConfig.GAMEMODE_MISSION && SuperConfig.getGAMEMODE() != SuperConfig.GAMEMODE_MISSION_PLOTTER){
            MySql.getInstance();
            TimerHandler timerHandler = new TimerHandler();
            timerHandler.openTimer();
            InitData.initGameConfig();
            SuperConfig.initconfigMap();
        }
        ItemService.getInstance();
        new EventUtils();
        ReportManager.init();
        PVPRank.getInstance();
        // Thread t=new Thread(new ADServer());
        // t.start();

        // 启动服务器在单独的线程中
        CompletableFuture<Void> serverFuture = CompletableFuture.runAsync(() -> {
            SuperServer.getInstance().start(isProxy);
        });

        // 在主线程中监听控制台输入
        startConsoleListener(serverFuture);

    }

    /**
     * 启动控制台监听器，允许用户通过输入命令来控制服务器
     */
    private static void startConsoleListener(CompletableFuture<Void> serverFuture) {
        System.out.println("===========================================");
        System.out.println("服务器启动完成！");
        System.out.println("输入 'quit' 或 'exit' 来优雅关闭服务器");
        System.out.println("输入 'status' 查看服务器状态");
        System.out.println("按回车键继续运行...");
        System.out.println("===========================================");

        Scanner scanner = new Scanner(System.in);
        String input;

        while (true) {
            try {
                input = scanner.nextLine().trim().toLowerCase();

                if ("quit".equals(input) || "exit".equals(input)) {
                    System.out.println("正在关闭服务器...");
                    shutdownServer();
                    break;
                } else if ("status".equals(input)) {
                    System.out.println("服务器运行状态: " + (serverFuture.isDone() ? "已停止" : "运行中"));
                    System.out.println("当前时间: " + new java.util.Date());
                } else if (input.isEmpty()) {
                    // 用户按回车键，继续运行
                    System.out.println("服务器继续运行中...");
                } else {
                    System.out.println("未知命令: " + input);
                    System.out.println("可用命令: quit, exit, status, 或按回车键继续");
                }
            } catch (Exception e) {
                System.err.println("控制台输入处理错误: " + e.getMessage());
            }
        }

        scanner.close();
    }

    /**
     * 优雅关闭服务器
     */
    private static void shutdownServer() {
        try {
            if (SuperServer.workerGroup != null) {
                SuperServer.workerGroup.shutdownGracefully();
            }
            if (SuperServer.bossGroup != null) {
                SuperServer.bossGroup.shutdownGracefully();
            }
            System.out.println("服务器已安全关闭");
            System.exit(0);
        } catch (Exception e) {
            System.err.println("关闭服务器时发生错误: " + e.getMessage());
            System.exit(1);
        }
    }

    private static String getOSSignalType(){
        return System.getProperties().getProperty("os.name").toLowerCase().startsWith("win")?"INT":"USR2";
    }
}
class ShutdownHook implements Runnable{
    public void run() {
        long now = TimerHandler.nowTimeStamp;
        long stopTime = now + (60 * 1000);
        while (TimerHandler.nowTimeStamp <= stopTime) {
            UserData.ReportBoardcastLuckyUser.Builder boardcastBuilder = UserData.ReportBoardcastLuckyUser.newBuilder();
            boardcastBuilder.setItemId(1);
            boardcastBuilder.setMessageId(2071);
            boardcastBuilder.setRoleName("要关服了");
            byte[] inbytes = boardcastBuilder.build().toByteArray();
            //   /// System.out.println("notice"+notice+"name"+name+"itemId"+itemId);
            for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
                //  /// System.out.println(entry.getKey()+"~~"+entry.getValue());
                ReportManager.reportInfo(entry.getKey(), ProtoData.SToC.REPORTBROADCASTLUCKYUSER_VALUE, inbytes);
            }
            try {
                TimeUnit.SECONDS.sleep(5);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
            SuperServer.workerGroup.shutdownGracefully();
            SuperServer.bossGroup.shutdownGracefully();
     /*   try{TimeUnit.MILLISECONDS.sleep(2);}
        catch(InterruptedException e){
            e.printStackTrace();
        }*/
            /// System.out.println("安全退出~~");
        }

}