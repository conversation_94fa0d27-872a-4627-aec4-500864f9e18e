//import com.sun.org.apache.regexp.internal.RE;
import common.ItemComposeConfig;
import common.PetBreakConfig;
import common.SuperConfig;

import io.netty.channel.ChannelHandlerContext;
import manager.*;

import module.Dispatch.Dispatch;
import module.Job.TimeDingShi;
import module.battle.BattleService;
import module.event.EventUtils;
import module.item.ItemService;
import module.pet.PetUtils;
import module.pvp.PVPDao;
import module.pvp.PVPRank;
import org.springframework.scheduling.annotation.EnableScheduling;
import protocol.BattleData;
import protocol.ProtoData;
import protocol.UserData;
import server.ADServer;
import server.SuperServer;
import server.SuperServerHandler;
import utils.MyUtils;
 import sun.misc.Signal;
import sun.misc.SignalHandler;

import java.util.ArrayList;
import java.util.Map;
import java.util.Properties;
import java.util.Scanner;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * Created by nara on 2017/11/16.
 */
@EnableScheduling
public class superStar {

    public static void main(String[] args) {

        Boolean isProxy = false;
        if( args.length > 0 && !args[0].isEmpty() )
            isProxy = Boolean.parseBoolean(args[0]);

        Signal signal = new Signal(getOSSignalType());
        Signal.handle(signal, new SignalHandler() {
            public void handle(Signal signal) {
                Thread t=new Thread(new ShutdownHook());
                Runtime.getRuntime().addShutdownHook(t);
                Runtime.getRuntime().exit(0);
            }
        });
          SuperConfig.setGAMEMODE(SuperConfig.GAMEMODE_MAIN);
        Redis.getInstance();
        if (SuperConfig.getGAMEMODE() != SuperConfig.GAMEMODE_MISSION && SuperConfig.getGAMEMODE() != SuperConfig.GAMEMODE_MISSION_PLOTTER){
            MySql.getInstance();
            TimerHandler timerHandler = new TimerHandler();
            timerHandler.openTimer();
            InitData.initGameConfig();
            SuperConfig.initconfigMap();
        }
        ItemService.getInstance();
        new EventUtils();
        ReportManager.init();
        PVPRank.getInstance();
        // Thread t=new Thread(new ADServer());
        // t.start();
        
        SuperServer.getInstance().start(isProxy);

    }
    private static String getOSSignalType(){
        return System.getProperties().getProperty("os.name").toLowerCase().startsWith("win")?"INT":"USR2";
    }
}
class ShutdownHook implements Runnable{
    public void run() {
        long now = TimerHandler.nowTimeStamp;
        long stopTime = now + (60 * 1000);
        while (TimerHandler.nowTimeStamp <= stopTime) {
            UserData.ReportBoardcastLuckyUser.Builder boardcastBuilder = UserData.ReportBoardcastLuckyUser.newBuilder();
            boardcastBuilder.setItemId(1);
            boardcastBuilder.setMessageId(2071);
            boardcastBuilder.setRoleName("要关服了");
            byte[] inbytes = boardcastBuilder.build().toByteArray();
            //   /// System.out.println("notice"+notice+"name"+name+"itemId"+itemId);
            for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
                //  /// System.out.println(entry.getKey()+"~~"+entry.getValue());
                ReportManager.reportInfo(entry.getKey(), ProtoData.SToC.REPORTBROADCASTLUCKYUSER_VALUE, inbytes);
            }
            try {
                TimeUnit.SECONDS.sleep(5);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
            SuperServer.workerGroup.shutdownGracefully();
            SuperServer.bossGroup.shutdownGracefully();
     /*   try{TimeUnit.MILLISECONDS.sleep(2);}
        catch(InterruptedException e){
            e.printStackTrace();
        }*/
            /// System.out.println("安全退出~~");
        }

}