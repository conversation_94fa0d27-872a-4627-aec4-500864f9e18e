import manager.MySql;

public class TaskTest {
    public static void main(String[] args) {
        StringBuffer sql = new StringBuffer("select id from AchievementEntity where uid='").append("2022081516193861x8830yRYO").append("'");
//        /// System.out.println(sql.toString());
        int id = (int) MySql.queryForOne(sql.toString());
//        /// System.out.println(id);

        sql = new StringBuffer("update AdLogEntity set event_id='").append("test").append("' where id=1");
//        /// System.out.println(sql.toString());

        MySql.updateSomes(sql.toString());
    }
}
