package entities;

import javax.persistence.*;

@Entity
@Table(name = "waitItem", schema = "", catalog = "super_star_fruit")
public class WaitItemEntity {

    private int id;
    private String uid;
    private long lastQueryTime;
    private int rewardTotalNums;
    private int latelyWaitItemId;
    private long latelyWaitTime;
    private long upToParItemIdCollections;
    private int ItemTypeNums;
    private int bagLevel;

    @Id
    @Column(name = "id", unique = true, nullable = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "lastQueryTime")
    public long getLastQueryTime() {
        return lastQueryTime;
    }

    public void setLastQueryTime(long lastQueryTime) {
        this.lastQueryTime = lastQueryTime;
    }

    @Basic
    @Column(name = "rewardTotalNums")
    public int getRewardTotalNums() {
        return rewardTotalNums;
    }

    public void setRewardTotalNums(int rewardTotalNums) {
        this.rewardTotalNums = rewardTotalNums;
    }

    @Basic
    @Column(name = "latelyWaitItemId")
    public int getLatelyWaitItemId() {
        return latelyWaitItemId;
    }

    public void setLatelyWaitItemId(int latelyWaitItemId) {
        this.latelyWaitItemId = latelyWaitItemId;
    }

    @Basic
    @Column(name = "latelyWaitTime")
    public long getLatelyWaitTime() {
        return latelyWaitTime;
    }

    public void setLatelyWaitTime(long latelyWaitTime) {
        this.latelyWaitTime = latelyWaitTime;
    }

    @Basic
    @Column(name = "upToParItemIdCollections")
    public long getUpToParItemIdCollections() {
        return upToParItemIdCollections;
    }

    public void setUpToParItemIdCollections(long upToParItemIdCollections) {
        this.upToParItemIdCollections = upToParItemIdCollections;
    }

    @Basic
    @Column(name = "ItemTypeNums")
    public int getItemTypeNums() {
        return ItemTypeNums;
    }

    public void setItemTypeNums(int itemTypeNums) {
        ItemTypeNums = itemTypeNums;
    }

    @Basic
    @Column(name = "bagLevel")
    public int getBagLevel() {
        return bagLevel;
    }

    public void setBagLevel(int bagLevel) {
        this.bagLevel = bagLevel;
    }

    @Override
    public String toString() {
        return "WaitItemEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", lastQueryTime=" + lastQueryTime +
                ", rewardTotalNums=" + rewardTotalNums +
                ", latelyWaitItemId=" + latelyWaitItemId +
                ", latelyWaitTime=" + latelyWaitTime +
                ", upToParItemIdCollections=" + upToParItemIdCollections +
                ", ItemTypeNums=" + ItemTypeNums +
                ", bagLevel=" + bagLevel +
                '}';
    }
}
