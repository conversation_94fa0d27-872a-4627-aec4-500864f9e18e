package table.pvp_robot;

import table.LineKey;

import java.util.Arrays;

public class pvp_robotLine implements LineKey {
    public int id;
    public String group;
    public String  member;
    public String  currentLevel;


    public int[] petIds;
    public void Parse() {
        petIds = new int[5];
        String[] pets = member.split(",");
        for (int i = 0; i < pets.length && i < petIds.length; i++) {
            petIds[i] = Integer.parseInt(pets[i]);
        }
    }

    @Override
    public String toString() {
        return "pvp_robotLine{" +
                "id=" + id +
                ", group='" + group + '\'' +
                ", member='" + member + '\'' +
                ", currentLevel='" + currentLevel + '\'' +
                ", petIds=" + Arrays.toString(petIds) +
                '}';
    }

    @Override
    public int Key() {
        return id;
    }
}
