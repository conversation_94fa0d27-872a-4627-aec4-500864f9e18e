package module.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ProtoData;
import protocol.TaskData;
import protocol.UserData;

/**
 * Created by nara on 2018/1/11.
 */
public class TaskService {
    private static Logger log = LoggerFactory.getLogger(TaskService.class);
    private static TaskService inst = null;
    public static TaskService getInstance() {
        if (inst == null) {
            inst = new TaskService();
        }
        return inst;
    }

    public byte[] finishTask(String uid,byte[] bytes){
        TaskData.RequestFinishTask requestFinishTask = null;
        TaskData.ResponseFinishTask.Builder builder = TaskData.ResponseFinishTask.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestFinishTask = TaskData.RequestFinishTask.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestFinishTask == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[finishTask] error");
            } else {
                ITask iTask = TaskDao.getInstance();
                builder = iTask.finishOneTask(uid,requestFinishTask.getTaskId());
            }
        }
        return builder.build().toByteArray();
    }
}
