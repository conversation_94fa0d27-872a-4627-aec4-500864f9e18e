package common;

import java.util.List;

@ExcelConfigObject(key = "map")
public class MapConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "temid")
    private int temid;
    @ExcelColumn(name = "maplevel")
    private int maplevel;
    @ExcelColumn(name = "mapname")
    private int mapname;
    @ExcelColumn(name = "maptype")
    private int mapType;
    @ExcelColumn(name = "monsterlv", isList = true)
    private List<Integer> monsterLv;
    @ExcelColumn(name = "offlinegold")
    private int offlineGold;
    @ExcelColumn(name = "offlineexpliquid")
    private int offlineExp;
    @ExcelColumn(name = "suppressnum")
    private int suppressnum;
//    @ExcelColumn(name = "monsterid1")
//    private int monsterid1;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getMapType() {
        return mapType;
    }

    public void setMapType(int mapType) {
        this.mapType = mapType;
    }

    public List<Integer> getMonsterLv() {
        return monsterLv;
    }

    public void setMonsterLv(List<Integer> monsterLv) {
        this.monsterLv = monsterLv;
    }

    public int getOfflineGold() {
        return offlineGold;
    }

    public void setOfflineGold(int offlineGold) {
        this.offlineGold = offlineGold;
    }

    public int getOfflineExp() {
        return offlineExp;
    }

    public void setOfflineExp(int offlineExp) {
        this.offlineExp = offlineExp;
    }

    public int getTemid() {
        return temid;
    }

    public void setTemid(int temid) {
        this.temid = temid;
    }

    public int getMaplevel() {
        return maplevel;
    }

    public void setMaplevel(int maplevel) {
        this.maplevel = maplevel;
    }

    public int getMapname() {
        return mapname;
    }

    public void setMapname(int mapname) {
        this.mapname = mapname;
    }

    public int getSuppressnum() {
        return suppressnum;
    }

    public void setSuppressnum(int suppressnum) {
        this.suppressnum = suppressnum;
    }

//    public int getMonsterid1() {
//        return monsterid1;
//    }
//
//    public void setMonsterid1(int monsterid1) {
//        this.monsterid1 = monsterid1;
//    }

    @Override
    public String toString() {
        return "MapConfig{" +
                "id=" + id +
                ", temid=" + temid +
                ", maplevel=" + maplevel +
                ", mapname=" + mapname +
                ", mapType=" + mapType +
                ", monsterLv=" + monsterLv +
                ", offlineGold=" + offlineGold +
                ", offlineExp=" + offlineExp +
                ", suppressnum=" + suppressnum +
                '}';
    }
}
