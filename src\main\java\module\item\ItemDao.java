package module.item;

import common.ItemConfig;
import common.SuperConfig;
import entities.*;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import model.ItemInfo;
import model.RewardInfo;
import model.RoleDressInfo;
import model.UtilityInfo;
import module.login.ILogin;
import module.login.LoginDao;
import module.robot.ranName;
import module.role.RoleDao;
import module.task.ITask;
import module.task.TaskDao;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import protocol.*;
import server.SuperServerHandler;
import utils.MyUtils;

import java.util.*;

/**
 * Created by nara on 2018/1/3.
 */
@Component
@EnableScheduling
public class ItemDao implements IItem {
    private static ArrayList eSuit = new ArrayList<Integer>(Arrays.asList(new Integer[]{374, 375, 376, 377, 378, 379}));
    private static ArrayList meloSuit = new ArrayList<Integer>(Arrays.asList(new Integer[]{392, 393, 394, 395, 396, 397}));
    private static ArrayList jadeSuit = new ArrayList<Integer>(Arrays.asList(new Integer[]{443, 444, 445, 446, 447, 448}));
    private static ArrayList bwWarSuit = new ArrayList<Integer>(Arrays.asList(new Integer[]{472, 473, 474, 475, 476, 477}));
    private static ArrayList inframeSuit = new ArrayList<Integer>(Arrays.asList(new Integer[]{496, 497, 498, 499, 500, 501}));
    private static Logger log = LoggerFactory.getLogger(ItemDao.class);
    private static ItemDao inst = null;

    public static ItemDao getInstance() {
        if (inst == null) {
            inst = new ItemDao();
        }
        return inst;
    }

    public Map<Integer, List<ItemInfo>> initItemData(String uid) {
        //   ItemDao itemDao=ItemDao.getInstance();
        ItemUtils.updatePlayerItems(uid, 1, 900000000, true);
        ItemUtils.updatePlayerItems(uid, 2, 100000, true);
        ItemUtils.updatePlayerItems(uid, 3, 50, true);
        ItemUtils.updatePlayerItems(uid, 4, 100, true);
        ItemUtils.updatePlayerItems(uid, 5, 99999, true);
        ItemUtils.updatePlayerItems(uid, 6, 1000, true);
        ItemUtils.updatePlayerItems(uid, 10, 99999, true);
        ItemUtils.updatePlayerItems(uid, 11, 99999, true);
        ItemUtils.updatePlayerItems(uid, 13, 1000, true);
        return null;
    }

//


    public double getItemNum(String uid, int itemId) {
        StringBuffer sql = new StringBuffer("from ItemEntity where uid='").append(uid).append("' and itemid=").append(itemId);
        ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(sql.toString());
        //先查询物品在数据库的情况，如果没有标记为0，之后进行插入操作。

        return itemEntity == null ? -1 : itemEntity.getItemnum();
    }

    public ItemEntity queryItemGildCoins(String uid, int itemId) {
        StringBuffer sql = new StringBuffer("from ItemEntity where uid='").append(uid).append("' and itemid=").append(itemId);
        ItemEntity itemEntity = null;
        try{
            itemEntity = (ItemEntity) MySql.queryForOne(sql.toString());
        }catch (Exception e){
            e.printStackTrace();
        }
        return itemEntity;
        //先查询物品在数据库的情况，如果没有标记为0，之后进行插入操作。
    }

    public Map<String, String> FullSet(Set<String> set, int lottoid) {
        String[] dress = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_LOTTO, lottoid + "", "item_id").split("\\|");
        String[] dressprobability = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_LOTTO, lottoid + "", "dress_probability").split("\\|");
        String[] getprobability = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_LOTTO, lottoid + "", "get_probability").split("\\|");
        Integer index = 0;
        Map<String, String> map = new HashMap<String, String>();
        while (1 == 1) {
            index = ranName.ranGift(dressprobability);
            if (set.size() < 6) {
                set.add(index + "");
            }
            if (set.size() >= 6) {
                break;
            }
        }
        for (String mset : set) {
            map.put(mset + "", dress[Integer.valueOf(mset)] + "|" + getprobability[Integer.valueOf(mset)]);
        }
        return map;
    }

    public List<ItemData.Item> getBag(String uid) {
        Redis jedis = Redis.getInstance();
        List<ItemData.Item> headList = new ArrayList<ItemData.Item>();
        Map<String, String> bagmap = jedis.hgetAll("roleitem:" + uid + "#0");
        ItemData.Item.Builder item = null;
        for (String key : bagmap.keySet()) {
            item = ItemData.Item.newBuilder();
            item.setId(Integer.valueOf(key));
            item.setNum(Double.parseDouble(bagmap.get(key)));
            headList.add(item.build());
        }
        return headList;
    }

    public boolean judgebagCell(String uid, List<ItemInfo> itemList) {
        Map<Integer, Integer> cellMap = new HashMap<Integer, Integer>();
        for (int i = 0; i < itemList.size(); i++) {
            ItemInfo itemInfo = itemList.get(i);
            int itemId = itemInfo.getId();
            double addNum = itemInfo.getNum();
            if (addNum > 0) {
                Redis jedis = Redis.getInstance();
                int type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag"));
                if (type == 0) {
                    continue;
                }
                String key = "roleitem:" + uid + "#" + type;
                int nowCell = 0;
                if (cellMap.containsKey(type) == true) {
                    nowCell = cellMap.get(type);
                } else {
                    if (jedis.hget(key, "nowcell") == null) {
                        jedis.hset(key, "nowcell", nowCell + "");
                    } else {
                        nowCell = Integer.parseInt(jedis.hget(key, "nowcell"));
                    }
                    cellMap.put(type, nowCell);
                }
                double itemNum = getItemNum(uid, itemId);
                int oneMax = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "group_max"));
                if (itemNum == 0) {
                    int bagMax = 13 * 4;
                    if (type == 1) {
                        bagMax = Integer.parseInt(jedis.hget("role:" + uid, "bagmax"));
                    } else if (type == 2) {
                        String bagMaxNums = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DECISION, "1", "bag_2");
                        bagMax = Integer.parseInt(bagMaxNums);
                    } else if (type == 3) {
                        String bagMaxNums = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DECISION, "1", "bag_3");
                        bagMax = Integer.parseInt(bagMaxNums);
                    }
                    int addCell = (int) Math.ceil(addNum / oneMax);
                    nowCell += addCell;
                    if (nowCell > bagMax) {
                        return false;
                    }
                } else {
                    double totalNum = addNum + itemNum;
                    int oldCell = (int) Math.ceil(itemNum / oneMax);
                    int newCell = (int) Math.ceil(totalNum / oneMax);
                    if (newCell > oldCell) {
                        int bagMax = 4 * 13;
                        if (type == 1) {
                            bagMax = Integer.parseInt(jedis.hget("role:" + uid, "bagmax"));
                        } else if (type == 2) {
                            String bagMaxNums = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DECISION, "1", "bag_2");
                            bagMax = Integer.parseInt(bagMaxNums);
                        } else if (type == 3) {
                            String bagMaxNums = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DECISION, "1", "bag_3");
                            bagMax = Integer.parseInt(bagMaxNums);
                        }
                        nowCell = newCell - oldCell + nowCell;
                        if (nowCell > bagMax) {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    public void update(ItemEntity entity) {
        Session session = MySql.getSession();
        session.update(entity);
        session.beginTransaction().commit();
        session.close();
    }

    //-2背包格子不够
    //-3到达物品的上限
    public double updateItemInfo(String uid, int itemId, long addNum) {
        if (addNum == 0) {
            return -1;
        }
        //     /// System.err.println(itemId+"itemId"+addNum+"addNum"+System.currentTimeMillis());

        Redis jedis = Redis.getInstance();
        int type = 1;
        try {
            //     type = Integer.parseInt(Redis.getExcelInfofromRedis0(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag"));
            ItemConfig itemConfig = (ItemConfig) (SuperConfig.getCongifObject(SuperConfig.itemConfig, itemId));
            type = itemConfig.getBag();
        } catch (Exception e) {

            e.printStackTrace();
        }

        String key = "roleitem:" + uid + "#" + type;
        int much = 1;
        if (type == 0) {
            much = judgeItemUtility(uid, itemId);
        }
        double totalNum = 0;
        synchronized (uid + itemId) {
            //   double itemNum = getItemNum(uid,itemId);
            StringBuilder stringBuilder = new StringBuilder("from ItemEntity where uid='").append(uid).append("' and itemId=").append(itemId);
            ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
            //     /// System.err.println(itemEntity+"~!#$@!$!%");
            if (itemEntity == null) {

                itemEntity = new ItemEntity();
                itemEntity.setUid(uid);
                itemEntity.setType(type);
                itemEntity.setItemid(itemId);
                if (addNum < 0) {
                    return -3;
                }
                itemEntity.setItemnum((double) addNum);
                jedis.hset(key, itemId + "", addNum + "");
                totalNum = itemEntity.getItemnum();
                System.out.println("gonna insert:"+itemId+"insert num:"+addNum);
                MySql.mustInsert(itemEntity);
            } else {
                if (itemId == 2) {
                    /// System.err.println(itemEntity.getItemnum()+"itemnums");
                }


                totalNum = addNum * much + itemEntity.getItemnum();
                if (totalNum < 0) {
                    return -1;
                }
                jedis.hset(key, itemId + "", totalNum + "");
                StringBuffer hql = new StringBuffer();
                hql.append("update ItemEntity set itemnum = ").append(totalNum)
                        .append(" where uid = '").append(uid).append("' and itemid = ").append(itemId);
                MySql.mustUpdateSomes(hql.toString());
            }


            return totalNum;
        }
    }

    public ItemData.ResponseBuyItem.Builder buyItem(String uid, int id, int num) {
        ItemData.ResponseBuyItem.Builder builder = ItemData.ResponseBuyItem.newBuilder();
        builder.setErrorId(0);
        Redis jedis = Redis.getInstance();
        Map<String, String> shopItemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_SHOPITEM, id);
        int itemId = Integer.parseInt(shopItemMap.get("itemID"));
        int addItemNum = Integer.parseInt(shopItemMap.get("num")) * num;
        String[] changes = shopItemMap.get("change").split(",");
        int costId = Integer.parseInt(changes[0]);
        int costNum = Integer.parseInt(changes[1]) * num;
        double costBagNum = getItemNum(uid, costId);
        if (costBagNum == 0) {
            log.error(uid + ":[buyItem] error 1");
            return builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
        }
        double updateNum = costBagNum - costNum;
        if (updateNum < 0) {
            log.error(uid + ":[buyItem] error 2 >>>costBagNum:" + costBagNum + ",costNum:" + costNum);
            return builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
        }
        String itemType = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "bag");
        String itemNum = jedis.hget("roleitem:" + uid + "#" + itemType, itemId + "");
        double totalNum = itemNum == null ? addItemNum : Double.parseDouble(itemNum) + addItemNum;
        String itemMax = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, itemId + "", "max");
        double val = updateItemInfo(uid, itemId, addItemNum);

        if (val == -2) {
            log.error(uid + ":[buyItem] error 3 >>>full of bag ! type:" + itemType);
            return builder.setErrorId(ProtoData.ErrorCode.BAGFULLERROR_VALUE);
        }
        updateItemInfo(uid, costId, (-costNum));
        ShoporderEntity shopOrder = new ShoporderEntity();
        shopOrder.setNums(num);
        shopOrder.setShopid(itemId);
        shopOrder.setUid(uid);
        MySql.insert(shopOrder);
        ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
        addItemBu.setId(itemId);
        addItemBu.setNum(val);
        builder.addItem(addItemBu);
        ItemData.Item.Builder costItemBu = ItemData.Item.newBuilder();
        costItemBu.setId(costId);
        costItemBu.setNum(updateNum);
        builder.addItem(costItemBu);
        ITask iTask = TaskDao.getInstance();
        iTask.updateAchievement(uid, 7, 1);
        log.info("[buyItem]>>>uid:" + uid + ">>>id=" + id + ",num=" + num);
        return builder;
    }

    public ItemData.ResponseUseItem.Builder useItem(String uid, int id, int num, int type) {
        ItemData.ResponseUseItem.Builder builder = ItemData.ResponseUseItem.newBuilder();
        builder.setType(type);
        builder.setErrorId(0);
        if (type == 1) {//使用
            Map<String, String> itemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, id);
            int other = Integer.parseInt(itemMap.get("other"));
            if (other != 0) {
                int otherNum = (int) getItemNum(uid, other);
                if (otherNum <= 0) {
                    log.error(uid + ":[useItem] error 1 >>>id:" + id + ",num:" + otherNum);
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                    return builder;
                }
            }
            int useType = Integer.parseInt(itemMap.get("use"));
            List<Integer> addItemIdList = new ArrayList<Integer>();
            List<Integer> addItemNumList = new ArrayList<Integer>();
//            int addItemId = 0;
//            int addItemNum = 0;
            switch (useType) {
                case 3:


                    String getId = itemMap.get("get_id");
                    if (!"0".equals(getId) && getId != null) {
                        String[] addItemIdStrs = itemMap.get("get_id").
                                split("\\|");
                        String[] addItemNumStrs = itemMap.get("get_num").split("\\|");
                        int index = 0;
                        for (int i = 0; i < addItemIdStrs.length; i++) {
                            int addItemId = Integer.parseInt(addItemIdStrs[i]);
                            int addItemNum = Integer.parseInt(addItemNumStrs[i]) * num;
                         /*    //   Map<String,String> itemDressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM,addItemId);
                          //  ItemData.Item.Builder showItemBu = ItemData.Item.newBuilder();
                       int dress_type = Integer.parseInt( itemDressMap.get("dress_type"));
                            if(dress_type==1)
                            {
                                String get_id = itemDressMap.get("get_id");
                                String[] strArray = get_id.split("\\|");
                                int dressId = Integer.parseInt(strArray[0]);
                                int cType = Integer.parseInt(strArray[1]);
                                showItemBu.setId(addItemId);
                                showItemBu.setNum(1);
                                builder.addItemShow(showItemBu);
                                useClothing(uid, dressId, cType,builder);
                            }
                            else
                            {
                                addItemIdList.add(index,addItemId);
                                addItemNumList.add(index,addItemNum);
                                index++;
                            }*/
                            //新版礼包代码
                            addItemIdList.add(index, addItemId);
                            addItemNumList.add(index, addItemNum);
                            index++;
                            //

                        }
                    }
                    //新版本礼包
                    String getDress = itemMap.get("dress_id");
                    if (getDress != null && !"0".equals(getDress)) {
                        String[] dressConfig = getDress.split("\\|");
                        for (int i = 0; i < dressConfig.length; i++) {
                            int dressId = Integer.parseInt(dressConfig[i].split(",")[0]);
                            int cType = Integer.parseInt(dressConfig[i].split(",")[1]);
                            useClothing(uid, dressId, cType, builder);
                        }
                    }

                    break;
                case 4:
                    int total = 0;
                    Map<Integer, Integer> rateMap = new HashMap<Integer, Integer>();
                    List<Integer> listItemID = new ArrayList<Integer>();
                    int belongId = Integer.parseInt(itemMap.get("get_id"));
                    Redis jedis = Redis.getInstance();
                    Iterator<String> iterator = jedis.keys(SuperConfig.REDIS_EXCEL_BAGUSE + "*").iterator();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        String[] str = key.split(":");
                        if (str.length < 2) {
                            continue;
                        }
                        int idBaguse = Integer.parseInt(str[1]);
                        int belongIdRedis = Integer.parseInt(jedis.hget(key, "belong_to"));
                        if (belongId == belongIdRedis) {
                            int rate = Integer.parseInt(jedis.hget(key, "rate"));
                            total += rate;
                            rateMap.put(idBaguse, rate);

                            listItemID.add(idBaguse);
                        }
                        /*listItemID.add(idBaguse);*/
                    }
                    int size = listItemID.size();
                    Random random = new Random();
                    int index = random.nextInt(size);
                    int SureID = listItemID.get(index);
                    listItemID.clear();
                    listItemID.add(SureID);

                    int rand = (int) (Math.random() * total);

                    total = 0;
               /*     for (Map.Entry<Integer,Integer>entry:rateMap.entrySet()){
                        if(SureID == entry.getKey()) continue;


                        if (entry.getValue() >= rand){
                            /// System.out.println(rand+"!!!!!!!!!!!!!!!!!!!!!");
                          
                            listItemID.add(entry.getKey());
                        }
                    }*/
                    for (int i = 0; i < listItemID.size(); i++) {
                        Map<String, String> bagUseMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_BAGUSE, listItemID.get(i));

                        int addItemId = Integer.parseInt(bagUseMap.get("get_item"));
                        int addItemNum = Integer.parseInt(bagUseMap.get("number")) * num;
                        addItemIdList.add(addItemId);

                        addItemNumList.add(addItemNum);
                    }
                    break;
                case 6://效果使用
                    int utilityId = Integer.parseInt(itemMap.get("get_id"));
                    long timeStamp = getUtilityStamp(uid, utilityId);
                    if (timeStamp > TimerHandler.nowTimeStamp) {
                        log.error(uid + ":[useItem] error 4");
                        builder.setErrorId(ProtoData.ErrorCode.ITEMCANTUSEERROR_VALUE);
                        return builder;
                    } else if (timeStamp != 0) {
                        deleteUtility(uid, utilityId);
                    }
                    String effect = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_UTILITY, utilityId + "", "effect");
                    long overStamp = TimerHandler.nowTimeStamp + Integer.parseInt(effect) * 1000;
                    addUtility(uid, utilityId, overStamp);
                    break;
                case 8:
                    String get_id = itemMap.get("get_id");
                    String[] strArray = get_id.split("\\|");
                    int dressId = Integer.parseInt(strArray[0]);
                    int cType = Integer.parseInt(strArray[1]);
                    useClothing(uid, dressId, cType, builder);
                    break;
                default:
                    log.error(uid + ":[useItem] error 5");
                    builder.setErrorId(ProtoData.ErrorCode.ITEMCANTUSEERROR_VALUE);
                    return builder;
            }
            List<ItemInfo> itemList = new ArrayList<ItemInfo>();
            for (int i = 0; i < addItemIdList.size(); i++) {
                int addItemId = addItemIdList.get(i);
                int addItemNum = addItemNumList.get(i);
                ItemInfo itemInfo = new ItemInfo();
                itemInfo.setId(addItemId);
                itemInfo.setNum(addItemNum);
                itemList.add(itemInfo);
            }
            boolean bo = judgebagCell(uid, itemList);
            if (bo == false) {
                log.error(uid + ":[useItem] error 6 >>>itemList:" + MyUtils.objectToJson(itemList));
                builder.setErrorId(ProtoData.ErrorCode.BAGFULLERROR_VALUE);
                return builder;
            }
            double idNum = getItemNum(uid, id);
            if (idNum - num < 0) {
                log.error(uid + ":[useItem] error 7 >>>num:" + idNum + ",needNum:" + num);
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
            double newNum = updateItemInfo(uid, id, (-num));
            ItemData.Item.Builder deItemBu = ItemData.Item.newBuilder();
            deItemBu.setId(id);
            deItemBu.setNum(newNum);
            builder.addItem(deItemBu);
            RewardInfo[] rewardInfos = new RewardInfo[addItemIdList.size()];
            for (int i = 0; i < addItemIdList.size(); i++) {
                RewardInfo rewardInfo = new RewardInfo();
                int addItemId = addItemIdList.get(i);
                int addItemNum = addItemNumList.get(i);
                double totalNum = 0;
                if (id == 22) {
                    /*totalNum = SuperConfig.getActionLimit();
                    rewardInfo.setItemtotal(new Double(totalNum).intValue());*/
                } else {
                    totalNum = updateItemInfo(uid, addItemId, addItemNum);
                    rewardInfo.setItemtotal(new Double(totalNum).intValue());
                }
                ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
                addItemBu.setId(addItemId);
                addItemBu.setNum(totalNum);
                builder.addItem(addItemBu);
                ItemData.Item.Builder showItemBu = ItemData.Item.newBuilder();
                showItemBu.setId(addItemId);
                showItemBu.setNum(addItemNum);
                builder.addItemShow(showItemBu);
                rewardInfo.setItemnums(addItemNum);
                rewardInfo.setItemid(addItemId);
                rewardInfos[i] = rewardInfo;
            }
            StringBuffer rewardRecord = new StringBuffer("");
            for (int i = 0; i < rewardInfos.length; i++) {
                String rewardSub = MyUtils.objectToJson(rewardInfos[i]);
                rewardRecord.append(rewardSub);
                if (i != rewardInfos.length - 1) {
                    rewardRecord.append(",");
                }
            }
            if (useType == 3 || useType == 4) {
                ConsumeRecordEntity record = new ConsumeRecordEntity();
                record.setItemid(id);
                record.setUid(uid);
                record.setType(useType);
                record.setNums(num);
                record.setNownums(new Double(newNum).intValue());
                record.setRecord(rewardRecord.toString());
                MySql.insert(record);
            }
            if (other > 0) {
                double otherNum = updateItemInfo(uid, other, (-1));
                ItemData.Item.Builder deItemBu2 = ItemData.Item.newBuilder();
                deItemBu2.setId(other);
                deItemBu2.setNum(otherNum);
                builder.addItem(deItemBu2);
            }
        } else if (type == 2) {//丢弃
            int discard = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, id + "", "discard"));
            if (discard == 0) {
                log.error(uid + ":[useItem] error 8 >>>id:" + id + ",discard:" + discard);
                builder.setErrorId(ProtoData.ErrorCode.ITEMERROR_VALUE);
                return builder;
            }
            double total = getItemNum(uid, id);
            if (num > total) {
                log.error(uid + ":[useItem] error 9 >>>id:" + id + ",num:" + num + ",total:" + total);
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
            double totalAfter = updateItemInfo(uid, id, -num);
            ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
            addItemBu.setId(id);
            addItemBu.setNum(totalAfter);
            builder.addItem(addItemBu);
            RewardInfo rewardInfo = new RewardInfo();
            rewardInfo.setItemid(id);
            rewardInfo.setItemnums(-num);
            rewardInfo.setItemtotal(new Double(totalAfter).intValue());
            ConsumeRecordEntity consumeRecord = new ConsumeRecordEntity();
            consumeRecord.setItemid(id);
            consumeRecord.setUid(uid);
            consumeRecord.setType(-1);//代表丢弃
            consumeRecord.setRecord(MyUtils.objectToJson(rewardInfo));//
            consumeRecord.setNums(-num);
            MySql.insert(consumeRecord);
            ItemData.Item.Builder showItemBu = ItemData.Item.newBuilder();
            showItemBu.setId(id);
            showItemBu.setNum(-num);
            builder.addItemShow(showItemBu);
        } else {
            log.error(uid + ":[useItem] error 10 >>>type:" + type);
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
        }
        return builder;
    }

    @Override
    public ItemData.ReportItem.Builder PhyItem(String uid,int id,double num) {
//        ConsumeData.RequestPhysical requestPhysical = null;
        ItemData.ReportItem.Builder builder=ItemData.ReportItem.newBuilder();
//        StringBuilder stringBuilder = new StringBuilder("from ItemEntity where uid='").append(uid).append("'");
//        ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
//        itemEntity.setItemnum(itemEntity.getItemnum() - requestPhysical.getNum());
        StringBuilder  hql=new StringBuilder("update ItemEntity set itemnum = ").append(num)
                .append(" where uid = '").append(uid).append("' and itemid = ").append(id);
        MySql.updateSomes(hql.toString());
        return builder;
    }

    private long getUtilityStamp(String uid, int id) {
        Redis jedis = Redis.getInstance();
        String stamp = null;
        int type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_UTILITY, id + "", "type"));
        Map<String, String> map = jedis.hgetAll("roleutility:" + uid);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            int tmp = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_UTILITY, entry.getKey() + "", "type"));
            if (tmp == type) {
                stamp = entry.getValue();
                break;
            }
        }
        return stamp == null ? 0 : Long.parseLong(stamp);
    }

    public ItemData.ResponseOpenBagCell.Builder openBagCell(String uid) {
        ItemData.ResponseOpenBagCell.Builder builder = ItemData.ResponseOpenBagCell.newBuilder();
        builder.setErrorId(0);
        Redis jedis = Redis.getInstance();
        int bagMax = Integer.parseInt(jedis.hget("role:" + uid, "bagmax"));
        int index = bagMax / 4 + 1;
        int needMoney = 0;
        switch (index) {
            case 3:
                needMoney = SuperConfig.getMoneyCell3();
                break;
            case 4:
                needMoney = SuperConfig.getMoneyCell4();
                break;
            case 5:
                needMoney = SuperConfig.getMoneyCell5();
                break;
            case 6:
                needMoney = SuperConfig.getMoneyCell6();
                break;
            case 7:
                needMoney = SuperConfig.getMoneyCell7();
                break;
            case 8:
                needMoney = SuperConfig.getMoneyCell8();
                break;
            case 9:
                needMoney = SuperConfig.getMoneyCell9();
                break;
            case 10:
                needMoney = SuperConfig.getMoneyCell10();
                break;
            case 11:
                needMoney = SuperConfig.getMoneyCell11();
                break;
            case 12:
                needMoney = SuperConfig.getMoneyCell12();
                break;
            case 13:
                needMoney = SuperConfig.getMoneyCell13();
                break;
        }
        double num = getItemNum(uid, 1);
        if (num - needMoney < 0) {
            log.error(uid + ":[openBagCell] error >>>num:" + num + ",needNum:" + needMoney);
            builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
            return builder;
        }
        double totalNum = updateItemInfo(uid, 1, (-needMoney));
        bagMax += 4;
        jedis.hset("role:" + uid, "bagmax", bagMax + "");
        StringBuffer hql = new StringBuffer("update RoleEntity set bagmax = ").append(bagMax)
                .append(" where uid = '").append(uid).append("'");
        MySql.updateSomes(hql.toString());

        ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
        addItemBu.setId(1);
        addItemBu.setNum(totalNum);
        builder.addItem(addItemBu);

        ITask iTask = TaskDao.getInstance();
        iTask.updateAchievement(uid, 13, 1);

        return builder;
    }

    public ItemData.ResponseCompose.Builder compose(String uid, int id) {
        ItemData.ResponseCompose.Builder builder = ItemData.ResponseCompose.newBuilder();
        builder.setErrorId(0);
       /* if (type != 1 && type != 2){
            log.error(uid+":[compose] error 5");
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            return builder;
        }*/
        Map<String, String> composeMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_COMPOSE, id);
        String getItem = composeMap.get("get_item");
        Integer type = Integer.valueOf(getItem.split(",")[0]);
        Integer item_dress = Integer.valueOf(getItem.split(",")[1]);
        String needItem1 = composeMap.get("need_item1");
        String needItem2 = composeMap.get("need_item2");
        String needItem3 = composeMap.get("need_item3");
        String needItem4 = composeMap.get("need_item4");
        int coin = Integer.parseInt(composeMap.get("coin"));
        int needId1 = 0;
        int needId2 = 0;
        int needId3 = 0;
        int needId4 = 0;
        int needNum1 = 0;
        int needNum2 = 0;
        int needNum3 = 0;
        int needNum4 = 0;
        double itemNum1 = 0;
        double itemNum2 = 0;
        double itemNum3 = 0;
        double itemNum4 = 0;
        if (needItem1 != null && !needItem1.equals("0")) {
            needId1 = Integer.parseInt(needItem1.split(",")[0]);
            needNum1 = Integer.parseInt(needItem1.split(",")[1]);
            itemNum1 = getItemNum(uid, needId1);
            if (itemNum1 < needNum1) {
                log.error(uid + ":[compose] error 1>>>num:" + itemNum1 + ",needNum:" + needNum1);
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
        }
        if (needItem2 != null && !needItem2.equals("0")) {
            needId2 = Integer.parseInt(needItem2.split(",")[0]);
            needNum2 = Integer.parseInt(needItem2.split(",")[1]);
            itemNum2 = getItemNum(uid, needId2);
            if (itemNum2 < needNum2) {
                log.error(uid + ":[compose] error 2>>>num:" + itemNum2 + ",needNum:" + needNum2);
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
        }
        if (needItem3 != null && !needItem3.equals("0")) {
            needId3 = Integer.parseInt(needItem3.split(",")[0]);
            needNum3 = Integer.parseInt(needItem3.split(",")[1]);
            itemNum3 = getItemNum(uid, needId3);
            if (itemNum3 < needNum3) {
                log.error(uid + ":[compose] error 3>>>num:" + itemNum3 + ",needNum:" + needNum3);
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
        }
        if (needItem4 != null && !needItem4.equals("0")) {
            needId4 = Integer.parseInt(needItem4.split(",")[0]);
            needNum4 = Integer.parseInt(needItem4.split(",")[1]);
            itemNum4 = getItemNum(uid, needId4);
            if (itemNum4 < needNum4) {
                log.error(uid + ":[compose] error 4>>>num:" + itemNum4 + ",needNum:" + needNum4);
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                return builder;
            }
        }


        int val = 1;
        /*if (type == 2){
            val = (int)itemNum1/needNum1;
            val = needId2 != 0 && (int)itemNum2/needNum2 < val ? (int)itemNum2/needNum2 : val;
            val = needId3 != 0 && (int)itemNum3/needNum3 < val ? (int)itemNum3/needNum3 : val;
            val = needId4 != 0 && (int)itemNum4/needNum4 < val ? (int)itemNum4/needNum4 : val;
        }*/

        if (type == 1) {
            ItemInfo itemInfo = new ItemInfo();
            itemInfo.setId(item_dress);
            itemInfo.setNum(1 * val);
            List<ItemInfo> list = new ArrayList<ItemInfo>();
            list.add(itemInfo);
            boolean bo = judgebagCell(uid, list);
            if (bo == false) {
                log.error(uid + ":[compose] error 6>>>num:" + val);
                builder.setErrorId(ProtoData.ErrorCode.BAGFULLERROR_VALUE);
                return builder;
            }
            double totalNum = updateItemInfo(uid, item_dress, 1 * val);
            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
            updateItemBu.setId(item_dress);
            updateItemBu.setNum(totalNum);
            builder.addItem(updateItemBu);
        } else if (type == 2) {
            ITask iTask = TaskDao.getInstance();
            if (eSuit.contains(item_dress)) {
                iTask.updateMainAchievement(uid, 4, 1);
            } else if (meloSuit.contains(item_dress)) {
                iTask.updateMainAchievement(uid, 7, 1);
            } else if (jadeSuit.contains(item_dress)) {
                iTask.updateMainAchievement(uid, 10, 1);
            } else if (bwWarSuit.contains(item_dress)) {
                iTask.updateMainAchievement(uid, 11, 1);
            } else if (inframeSuit.contains(item_dress)) {
                iTask.updateMainAchievement(uid, 12, 1);
            }
            String dressid = item_dress.toString();
            int mod = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressid, "mod"));
            //如果存在永久的这件衣服返回错误码

            Redis jedis = Redis.getInstance();
            StringBuffer sql = new StringBuffer(" FROM DressEntity where uid = '").append(uid).append("' and dressid= '").append(dressid).append("' ");
            DressEntity dress = (DressEntity) MySql.queryForOne(sql.toString());

            String key = "roledress:" + uid + "#" + mod;
            LoginDao login = LoginDao.getInstance();
            RoleDressInfo roleDressInfo = login.getRoleDressFromRedis(key);
            List<UtilityInfo> dressList = roleDressInfo.getDressList();
            List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
            CommonData.Utility.Builder utilityBu = CommonData.Utility.newBuilder();
            UtilityInfo utilityInfo = new UtilityInfo();
            if (dress == null) {
                utilityInfo.setOverStamp("0");
                utilityInfo.setId(Integer.valueOf(dressid));
                dressList.add(utilityInfo);
                utilityBu.setId(utilityInfo.getId());
                utilityBu.setTimeStamp(utilityInfo.getOverStamp());
                utilityList.add(utilityBu.build());
                builder.setClothing(utilityBu.build());
                DressEntity dressEntity = new DressEntity();
                dressEntity.setUid(uid);
                dressEntity.setRoleid(mod);
                dressEntity.setDressid(Integer.valueOf(dressid));
                dressEntity.setOverstamp("0");
                MySql.insert(dressEntity);//添加

            } else {
                if ("0".equals(dress.getOverstamp())) {
                    log.error(uid + ":[compose] error 5>>>dressid 已拥有:" + dressid);
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                    return builder;
                }
            }
            /*{
                for (int j = 0; j < dressList.size(); j++) 
                {
                    if (dressList.get(j).getId() == Integer.valueOf(dressid)) 
                    {
                        String item_repeat = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressid + "", "item_repeat");
                        if(dressList.get(j).getOverStamp().equals("0") && item_repeat!="0,0")//是否永久
                        {

                            updateItemInfo(uid,Integer.valueOf(item_repeat.split(",")[0]),Integer.valueOf(item_repeat.split(",")[1]));
                            double totalNum = updateItemInfo(uid,Integer.valueOf(item_repeat.split(",")[0]),Integer.valueOf(item_repeat.split(",")[1]));
                            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
                            updateItemBu.setId(Integer.valueOf(item_repeat.split(",")[0]));
                            updateItemBu.setNum(totalNum);
                            builder.addItem(updateItemBu);
                        }
                        else
                        {
                            dressList.get(j).setOverStamp("0");
                            utilityInfo=dressList.get(j);
                            utilityBu.setId(utilityInfo.getId());
                            utilityBu.setTimeStamp(utilityInfo.getOverStamp());
                            utilityList.add(utilityBu.build());

                            builder.setClothing(utilityBu.build());
                        }
                        break;
                    }
                }
                StringBuffer hql = new StringBuffer("update DressEntity set overstamp = '0' where uid = '")
                        .append(uid).append("' and roleid = ").append(mod).append(" and dressid = ").append(Integer.valueOf(dressid));
                MySql.updateSomes(hql.toString());//修改
                for(UtilityInfo clth:dressList){
                    if(clth.getId()==Integer.valueOf(dressid)){
                        clth.setOverStamp("0");
                    }
                }
            }*/
            jedis.hset("roledress:" + uid + "#" + mod, "dresslist", MyUtils.objectToJson(dressList));
        }
        if (needId1 != 0) {
            double totalNum = updateItemInfo(uid, needId1, -needNum1 * val);
            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
            updateItemBu.setId(needId1);
            updateItemBu.setNum(totalNum);
            builder.addItem(updateItemBu);
        }
        if (needId2 != 0) {
            double totalNum = updateItemInfo(uid, needId2, -needNum2 * val);
            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
            updateItemBu.setId(needId2);
            updateItemBu.setNum(totalNum);
            builder.addItem(updateItemBu);
        }
        if (needId3 != 0) {
            double totalNum = updateItemInfo(uid, needId3, -needNum3 * val);
            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
            updateItemBu.setId(needId3);
            updateItemBu.setNum(totalNum);
            builder.addItem(updateItemBu);
        }
        if (needId4 != 0) {
            double totalNum = updateItemInfo(uid, needId4, -needNum4 * val);
            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
            updateItemBu.setId(needId4);
            updateItemBu.setNum(totalNum);
            builder.addItem(updateItemBu);
        }

        if (coin != 0) {
            double totalNum = updateItemInfo(uid, 1, -(coin * val));
            ItemData.Item.Builder updateItemBu = ItemData.Item.newBuilder();
            updateItemBu.setId(1);
            updateItemBu.setNum(totalNum);
            builder.addItem(updateItemBu);
        }


        /*ITask iTask = TaskDao.getInstance();
        iTask.updateOneTask(uid,10006,1);
        iTask.updateAchievement(uid,10,val);*/

        StringBuffer sql = new StringBuffer("update RoleEntity set dressnums=dressnums+1 where uid='")
                .append(uid).append("'");
        MySql.updateSomes(sql.toString());
        Redis jedis = Redis.getInstance();
        int nums = Integer.parseInt(jedis.hget("role:" + uid, "dressnums"));
        jedis.hset("role:" + uid, "dressnums", nums + 1 + "");
        FriendData.ReportBroadcast.Builder reportBroadcast = FriendData.ReportBroadcast.newBuilder();
        reportBroadcast.setType(0);
        reportBroadcast.setId(2118);
        String name = jedis.hget("role:" + uid, "name");
        String clothesName = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, item_dress.toString(), "design");
        reportBroadcast.setContent(name + "|" + clothesName);
        reportBroadcast.setTimeStamp(TimerHandler.nowTimeStamp + "");
        for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
            ReportManager.reportInfo(entry.getKey(), ProtoData.SToC.REPORTSYSTEM_VALUE, reportBroadcast.build().toByteArray());
        }

        return builder;
    }

    public void deleteUtility(String uid, int id) {
        Redis jedis = Redis.getInstance();
        jedis.hdel("roleutility:" + uid, id + "");
        StringBuffer hql = new StringBuffer("delete from UtilityEntity")
                .append(" where uid = '").append(uid).append("' and eid = ").append(id);
        MySql.updateSomes(hql.toString());
    }

    public static void addUtility(String uid, int id, long overStamp) {
        Redis jedis = Redis.getInstance();
        jedis.hset("roleutility:" + uid, id + "", overStamp + "");
        UtilityEntity utilityEntity = new UtilityEntity();
        utilityEntity.setUid(uid);
        utilityEntity.setEid(id);
        utilityEntity.setOverstamp(overStamp + "");
        MySql.insert(utilityEntity);
    }

    private int judgeItemUtility(String uid, int itemId) {
        int val = 1;
        if (itemId != 25) {
            val = judgeUtility(uid, itemId);
        }
        return val;
    }

    public int judgeUtility(String uid, int itemId) {
        Redis jedis = Redis.getInstance();
        Map<String, String> map = jedis.hgetAll("roleutility:" + uid);
        for (Map.Entry<String, String> entry : map.entrySet()) {
            int tmp = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_UTILITY, entry.getKey() + "", "itemid"));
            if (tmp == itemId) {
                int much = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_UTILITY, entry.getKey() + "", "much"));
                return much;
            }
        }
        return 1;
    }

    public ItemData.ResponseGetTotalEndless.Builder getTotalEndless(String uid, int id) {
        ItemData.ResponseGetTotalEndless.Builder builder = ItemData.ResponseGetTotalEndless.newBuilder();
        builder.setErrorId(0);
        builder.setId(id);
        Redis jedis = Redis.getInstance();
        int getTotalEndless = Integer.parseInt(jedis.hget("role:" + uid, "gettotalendless"));
        if (id != getTotalEndless + 1) {
            log.error(uid + ":[getTotalEndless] error 1>>>id:" + id + ",nowGetId:" + getTotalEndless);
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
        } else {
            Map<String, String> needEndlessMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ENDLESSREWARD, id);
            if (needEndlessMap.size() == 0) {
                log.error(uid + ":[getTotalEndless] error 2>>>id:" + id);
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            } else {
                long needEndlessNum = Long.parseLong(needEndlessMap.get("integral"));
                long totalEndless = Long.parseLong(jedis.hget("role:" + uid, "totalendless"));
                if (totalEndless < needEndlessNum) {
                    log.error(uid + ":[getTotalEndless] error 3>>>id:" + id + ",nowTotal:" + totalEndless);
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                } else {
                    IItem iItem = ItemDao.getInstance();
                    String[] itemIds = needEndlessMap.get("item").split("\\|");
                    String[] itemNums = needEndlessMap.get("num").split("\\|");
                    for (int i = 0; i < itemIds.length; i++) {
                        int itemId = Integer.parseInt(itemIds[i]);
                        int itemNum = Integer.parseInt(itemNums[i]);
                        double totalNum = iItem.updateItemInfo(uid, itemId, itemNum);
                        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                        itemBu.setId(itemId);
                        itemBu.setNum(totalNum);
                        builder.addItem(itemBu);
                    }
                    jedis.hset("role:" + uid, "gettotalendless", id + "");
                    StringBuffer hql = new StringBuffer("update RoleEntity set gettotalendless = ").append(id)
                            .append(" where uid = '").append(uid).append("'");
                    MySql.updateSomes(hql.toString());
                }
            }
        }
        return builder;
    }

    boolean useClothing(String uid, int dressId, int cType, ItemData.ResponseUseItem.Builder builder) {
        synchronized (uid) {
            ILogin iLogin = LoginDao.getInstance();

            Redis jedis = Redis.getInstance();

            Map<String, String> dressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_DRESS, dressId);
            //int nowRoleId = Integer.parseInt(dressMap.get("mod"));
            String roleId = dressMap.get("mod");

            String key = "roledress:" + uid + "#" + roleId;
            RoleDressInfo roleDressInfo = iLogin.getRoleDressFromRedis(key);
            if (roleDressInfo == null) return false;

            List<UtilityInfo> dressList = roleDressInfo.getDressList();
            List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
            // Map<Integer,ItemInfo> itemMap = new HashMap<Integer, ItemInfo>();
            List<Object> hqlObj = new ArrayList<Object>();

            long addTime = getAddTime(cType);

            UtilityInfo utilityInfo = null;
            for (int j = 0; j < dressList.size(); j++) {
                if (dressList.get(j).getId() == dressId) {
                    utilityInfo = dressList.get(j);
                    break;
                }
            }
            if (dressMap.size() == 0) {
                log.error(uid + ":[awardClothing] error 2 >>>dressId:" + dressId);
                return false;
            }

            if (utilityInfo == null) {
                utilityInfo = new UtilityInfo();
                utilityInfo.setId(dressId);
                long overTime = addTime == 0 ? 0 : (TimerHandler.nowTimeStamp + addTime);
                utilityInfo.setOverStamp(overTime + "");
                dressList.add(utilityInfo);
                DressEntity dressEntity = new DressEntity();
                dressEntity.setUid(uid);
                dressEntity.setRoleid(Integer.parseInt(roleId));
                dressEntity.setDressid(dressId);
                dressEntity.setOverstamp(overTime + "");
                hqlObj.add(dressEntity);

            } else {
                if (utilityInfo.getOverStamp().equals("0")) {
                    log.error(uid + ":[awardClothing] error 4 >>>dressId:" + utilityInfo.getId());
                    return false;
                }
                long overTime = addTime == 0 ? 0 : (Long.parseLong(utilityInfo.getOverStamp()) + addTime);
                utilityInfo.setOverStamp(overTime + "");

                StringBuffer hql = new StringBuffer("update DressEntity set overstamp = '").append(overTime).append("' where uid = '")
                        .append(uid).append("' and roleid = ").append(Integer.parseInt(roleId)).append(" and dressid = ").append(dressId);
                hqlObj.add(hql.toString());
            }
            CommonData.Utility.Builder utilityBu = CommonData.Utility.newBuilder();
            utilityBu.setId(utilityInfo.getId());
            utilityBu.setTimeStamp(utilityInfo.getOverStamp());
            utilityList.add(utilityBu.build());

            builder.addAllClothingList(utilityList);
            //衣物sql
            for (int i = 0; i < hqlObj.size(); i++) {
                Object object = hqlObj.get(i);
                if (object instanceof DressEntity) {
                    MySql.insert(object);
                } else if (object instanceof String) {
                    MySql.updateSomes(object.toString());
                } else {
                    /// System.out.println("???????");
                }
            }

            jedis.hset("roledress:" + uid + "#" + roleId, "dresslist", MyUtils.objectToJson(dressList));

        }
        return true;
    }

    private long getAddTime(int type) {
        long addTime = -1;
        if (type == 1) {
            addTime = 7 * 24 * 60 * 60 * 1000;
        } else if (type == 2) {
            addTime = (long) 30 * 24 * 60 * 60 * 1000;
        } else if (type == 3) {
            addTime = 0;
        }
        return addTime;
    }

    public List getplayerItem(String uid) {
        StringBuilder sql = new StringBuilder("from ItemEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(sql.toString());
        ////
        if (list == null || list.size() == 0) {
              /* /// System.err.println(uid+"~~~~~~~~~~~~~~~~~~~~~~~~~~");
               list =new ArrayList<Object>();
               ItemDao   dao =ItemDao.getInstance();
               dao.updateItemInfo(uid,1,500000);
               dao.updateItemInfo(uid,2,50000);
               dao.updateItemInfo(uid,5,50000);
               dao.updateItemInfo(uid,6,100);
               dao.updateItemInfo(uid,10,50000);
               dao.updateItemInfo(uid,11,50000);

               ItemEntity itemEntity1   =new ItemEntity();
               itemEntity1.setUid(uid);
               itemEntity1.setItemid(1);
               itemEntity1.setItemnum(500000);
               itemEntity1.setType(0);
               list.add(itemEntity1);
               ItemEntity itemEntity2   =new ItemEntity();
               itemEntity2.setUid(uid);
               itemEntity2.setItemid(2);
               itemEntity2.setItemnum(50000);
               itemEntity2.setType(0);
               list.add(itemEntity2);
               ItemEntity itemEntity3   =new ItemEntity();
               itemEntity3.setUid(uid);
               itemEntity3.setItemid(5);
               itemEntity3.setItemnum(50000);
               itemEntity3.setType(1);
               list.add(itemEntity3);
               ItemEntity itemEntity6   =new ItemEntity();
               itemEntity6.setUid(uid);
               itemEntity6.setItemid(6);
               itemEntity6.setItemnum(100);
               itemEntity6.setType(1);
               list.add(itemEntity6);
               ItemEntity itemEntity10  =new ItemEntity();
               itemEntity10.setUid(uid);
               itemEntity10.setItemid(10);
               itemEntity10.setItemnum(50000);
               itemEntity10.setType(1);
               list.add(itemEntity10);
               ItemEntity itemEntity11   =new ItemEntity();
               itemEntity11.setUid(uid);
               itemEntity11.setItemid(11);
               itemEntity11.setItemnum(50000);
               itemEntity11.setType(1);
               list.add(itemEntity11);*/
        }
        return list;
    }

    public int getItemCurNums(String uid, int itemId) {
        StringBuilder stringBuilder = new StringBuilder("from ItemEntity where uid='").append(uid).append("' and itemId=").append(itemId);
        ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
        if (itemEntity == null) {
            return 0;
        } else {
            return (int) itemEntity.getItemnum();
        }
    }

}