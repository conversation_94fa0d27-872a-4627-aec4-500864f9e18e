package manager;

import io.netty.channel.ChannelHandlerContext;
import module.mission.MissionDao;
import protocol.MissionData;
import protocol.OnlineConfrontationData;
import server.SuperProtocol;
import server.SuperServer;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class PlayerBattleHandle implements Runnable{
    //  public static Map<String,ChannelHandlerContext> battleMap=new ConcurrentHashMap<String, ChannelHandlerContext>();
   // private static Iterator<String>  matchPoolIt=MissionDao.userMacthPool.iterator();
    private List<String> macthPool=MissionDao.userMacthPool;
    public void run() {
    while( macthPool.size()>=2){
         String user1=macthPool.remove(0);
         String user2=macthPool.remove(0);
     int nowIndex=MissionDao.roomIndex.get();
     if(nowIndex==MissionDao.roomNums){
         MissionDao.roomIndex.set(0);
         nowIndex=0;
     }
     while( MissionDao.battleRoomMap.get(nowIndex)!=null){
         nowIndex=MissionDao.roomIndex.incrementAndGet();
     }
        OnlineConfrontationData.RequestAllocateRoom.Builder builder   =OnlineConfrontationData.RequestAllocateRoom.newBuilder();
      builder.setRoomIndex(nowIndex);
        byte[] bytes=builder.build().toByteArray();
        SuperProtocol data=  new SuperProtocol(OnlineConfrontationData.SToM.REQUESTALLOCATEROOM_VALUE, bytes.length,bytes);
        SuperServer.BattelServer.writeAndFlush(data);
       MissionDao.battleRoomMap.put(nowIndex,"");
        MissionDao.roomIndex.getAndIncrement();
    }
    }
}
