package manager;

import common.SuperConfig;
import model.SqlCallBackInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by nara on 2018/3/6.
 */
public class UpdateHandler implements Runnable 
{
    private static Logger log = LoggerFactory.getLogger(UpdateHandler.class);
    private static UpdateHandler inst;
    public static UpdateHandler getInstance()
    {
        if (inst == null) 
        {
            inst = new UpdateHandler();
        }
        return inst;
    }

    public void run() 
    {
        while (true)
        {
            try 
            {
                updateToSql();
                Thread.sleep(100);
            }
            catch (Exception e)
            {
                e.printStackTrace();
                log.error(e.getMessage(),e);
            }
        }
    }

    public static void updateToSql()
    {
        while (MySql.sqlList.size() > 0)
        {
        //   /// System.out.println(MySql.getNowSize()+"_____MySql.sqlList.size()>>>>>>>"+MySql.sqlList.size());
           // if (MySql.getNowSize() < MySql.maxSize)
            if (MySql.getNowSize() < MySql.maxSize)
            {
                SqlCallBackInfo sqlCallBackInfo = MySql.sqlList.remove(0);
                if (sqlCallBackInfo == null)
                {
//                    /// System.out.println("sqlCallBackInfo null???");
                }
                else 
                {
                    MySql.addNowSize();
                    if (sqlCallBackInfo.getMold() == SuperConfig.DB_UPDATESOMES)
                    {
                        MySql.mustUpdateSomes(sqlCallBackInfo.getHql());
                    }
                    else if (sqlCallBackInfo.getMold() == SuperConfig.DB_INSERT)
                    {
                        MySql.mustInsert(sqlCallBackInfo.getObject());
                    }
                    else 
                    {
                       // /// System.out.println(sqlCallBackInfo.getHql());
                        MySql.queryInSql(sqlCallBackInfo);
                    }
                }
            }
        }
    }
}
