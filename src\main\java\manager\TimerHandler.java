package manager;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by nara on 2017/11/23.
 * 定时器
 */
public class TimerHandler {

    public static String todayStr = null;

    public static int temptime=0;
    public static int xxtime=0;

    public static long nowTimeStamp = 0;

    public static int timeSign = 0;

    public static int dailyThreeHour = 0;

    public static int oneHourRank = 0;

    public static int dailyOneHour = 0;
    public static int daily12th = 0;
    public static int daily18th = 0;
    public static int luckItemVersion=0;
    public static volatile int halfHour=0;
//    public static int mapCountDown = 60*60*24;
//
//    public static int mapType = 1;

    public TimerHandler(){
        nowTimeStamp = System.currentTimeMillis();
    }
    public void openTimer(){
        GameTimer gameTimer = new GameTimer();
        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
        // 第二个参数为首次执行的延时时间，第三个参数为定时执行的间隔时间
        service.scheduleAtFixedRate(gameTimer, 1, 1, TimeUnit.SECONDS);
    }
}
