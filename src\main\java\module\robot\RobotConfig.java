package module.robot;

import java.util.HashMap;
import java.util.Map;

public class RobotConfig {

    public static final String newusers="{\"EndlessMatchOrigin\":0,\"EndlessMatchFriend\":0,\"ShowLevelLetter\":2,\"newbie\":false,\"newbieLevelPhase\":0,\"newbieLevelPass\":false,\"newbieTaskPass\":true,\"newbieFashionPass\":true,\"newbieSignPass\":false,\"newbieAddFriendPass\":false,\"newbieAccomplishment\":true,\"newbieCultivate\":false,\"newbiePkSpeedPass\":false,\"newbieHeadBallPass\":true,\"newbieCartoonPass\":true,\"newbieCartoonPlan\":0,\"newbiePass\":false,\"newbieIntroduce\":true,\"newbiemailPass\":false}";
    public static final int people=1;//加经验值的机器人
    public static final int fenshu=100;//无尽通关分数
    public static final int jinyan=1000;//无尽通关经验

}
