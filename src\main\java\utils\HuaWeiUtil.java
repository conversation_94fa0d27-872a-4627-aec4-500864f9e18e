package utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;
import java.io.IOException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;

public class HuaWeiUtil 
{

    static String urlToken = "https://oauth-login.cloud.huawei.com/oauth2/v3/token";

    static String urlScope = "https://api.cloud.huawei.com/rest.php";

    static String grant_type = "authorization_code";

    static String redirect_uri = "hms://redirect_url";

    static String client_id = "102599673";

    static String client_secret = "d24d1469c01926ec441b0b99a68f6ed9b5cd0f747ee97caa3c2720c7db26a9bf";

    public static String getUnionID (String authorizationCode)
    {
        try
        {
            JSONObject tokens = getTokenByCode(redirect_uri, urlToken, authorizationCode, client_secret, client_id, grant_type);
            String accessToken = tokens.getString("access_token");

            if(accessToken == null || accessToken.isEmpty() ) return null;

            JSONObject tokenInfo = getClientTokenInfo(accessToken, urlScope);
            if(tokenInfo==null ) return null;
            String unionID = tokenInfo.getString("union_id");

            return unionID;
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
        return null;
    }

    private static JSONObject getTokenByCode(String redirect_uri, String urlToken, String code, String client_secret,
                                             String client_id, String grant_type) throws IOException {
        HttpPost httpPost = new HttpPost(urlToken);
        List<NameValuePair> request = new ArrayList<NameValuePair>();
        request.add(new BasicNameValuePair("redirect_uri", redirect_uri));
        request.add(new BasicNameValuePair("code", code));
        request.add(new BasicNameValuePair("client_secret", client_secret));
        request.add(new BasicNameValuePair("client_id", client_id));
        request.add(new BasicNameValuePair("grant_type", grant_type));
        httpPost.setEntity(new UrlEncodedFormEntity(request));

        CloseableHttpResponse response = getClient().execute(httpPost);

        try {
            HttpEntity responseEntity = response.getEntity();
            String ret = responseEntity != null ? EntityUtils.toString(responseEntity) : null;
            JSONObject jsonObject = (JSONObject) JSON.parse(ret);
            EntityUtils.consume(responseEntity);
            return jsonObject;
        } finally {
            response.close();
        }
    }

    /**
     *
     * @param accessToken AT
     * @param urlScope
     * @return JSON
     * @throws IOException
     */
    private static JSONObject getClientTokenInfo(String accessToken, String urlScope) throws IOException {
        HttpPost httppost = new HttpPost(urlScope);
        httppost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("nsp_svc=");
        stringBuffer.append("huawei.oauth2.user.getTokenInfo");
        stringBuffer.append("&open_id=");
        stringBuffer.append("OPENID");
        stringBuffer.append("&access_token=");
        stringBuffer.append(URLEncoder.encode(accessToken, "UTF-8"));
        StringEntity entity = new StringEntity(stringBuffer.toString());

        httppost.setEntity(entity);
        CloseableHttpResponse response = getClient().execute(httppost);
        try {
            HttpEntity entity1 = response.getEntity();
            String ret = entity1 != null ? EntityUtils.toString(entity1, "UTF-8") : null;
            JSONObject jsonObject = (JSONObject) JSON.parse(ret);
            EntityUtils.consume(entity1);
            return jsonObject;
        } finally {
            response.close();
        }
    }    
    /**
     * get httpclient
     * @return
     */
    private static CloseableHttpClient getClient() {
        PoolingHttpClientConnectionManager connectionManager = buildConnectionManager("TLSv1.2", new String[]{"TLSv1.2","TLSv1.1"},
                new String[]{"TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA256","TLS_DHE_RSA_WITH_AES_128_CBC_SHA256", "TLS_DHE_RSA_WITH_AES_128_CBC_SHA", "TLS_DHE_DSS_WITH_AES_128_CBC_SHA"});
        connectionManager.setMaxTotal(400);
        connectionManager.setDefaultMaxPerRoute(400);
        RequestConfig config =
                RequestConfig.custom().setConnectionRequestTimeout(100).setRedirectsEnabled(false).build();

        return HttpClients.custom()
                .useSystemProperties()
                .setConnectionManager(connectionManager)
                .setDefaultRequestConfig(config)
                .build();
    }

    /**
     *
     * @param protocol
     * @param supportedProtocols
     * @param supportedCipherSuites
     * @return
     */
    private static PoolingHttpClientConnectionManager buildConnectionManager(String protocol,
                                                                             String[] supportedProtocols, String[] supportedCipherSuites) {
        PoolingHttpClientConnectionManager connectionManager = null;
        try {
            SSLContext sc = SSLContext.getInstance(protocol);
            TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
            tmf.init((KeyStore) null);
            sc.init(null, tmf.getTrustManagers(), null);
            SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sc, supportedProtocols,
                    supportedCipherSuites, SSLConnectionSocketFactory.getDefaultHostnameVerifier());

            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", new PlainConnectionSocketFactory())
                    .register("https", sslsf)
                    .build();
            connectionManager = new PoolingHttpClientConnectionManager(registry);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        catch (KeyStoreException e) {
            e.printStackTrace();
        }
        catch (KeyManagementException e) {
            e.printStackTrace();
        }
        return connectionManager;
    }	
}