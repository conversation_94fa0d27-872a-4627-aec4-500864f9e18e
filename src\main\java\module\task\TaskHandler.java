package module.task;

/**
 * Created by nara on 2019/3/6.
 */
public class Task<PERSON>and<PERSON> implements Runnable {
    private String uid;
    private int taskId;
    private long taskNowNum;

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public void setTaskNowNum(long taskNowNum) {
        this.taskNowNum = taskNowNum;
    }

    public void run() {
        try {
            Thread.sleep(3000);
            ITask iTask = TaskDao.getInstance();
            iTask.reportTask(uid, taskId, taskNowNum);
        }catch (Exception e){
            e.printStackTrace();
        }
    }
}
