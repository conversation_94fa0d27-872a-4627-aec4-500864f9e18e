package module.item;

import com.google.protobuf.InvalidProtocolBufferException;
import com.googlecode.protobuf.format.JsonFormat;
import common.*;
import entities.ItemEntity;
import entities.PetEntity;
import entities.RoleAdditionalEntity;
import manager.*;
import model.CommonInfo;
import model.ItemInfo;
import module.pet.PetDao;
import module.pet.PetUtils;
import module.robot.ranName;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.*;
import utils.MyUtils;

import javax.persistence.OptimisticLockException;
import java.util.*;

/**
 * Created by nara on 2018/1/3.
 */
public class ItemService {

    private static Logger log = LoggerFactory.getLogger(ItemService.class);
    private static Map<Integer, List<String>> rafflePool;
    private static Map<Integer, Map<Integer, Integer>> exchangeMap;
    private static ItemService inst = null;
    private static ItemDao itemDao = null;

    public static ItemService getInstance() {
        if (inst == null) {
            itemDao = ItemDao.getInstance();
            inst = new ItemService();
        }
        return inst;
    }

    static {
        System.out.println("1111_——————————————————————————————————————————————————");
        rafflePool = new HashMap<Integer, List<String>>();
        exchangeMap = new HashMap<Integer, Map<Integer, Integer>>();
        Set<String> raffleConfigSet = Redis.keys("rafflePetBasicconfig*");
        Iterator<String> iterator = raffleConfigSet.iterator();
        Redis jedis = Redis.getInstance();
        while (iterator.hasNext()) {
            Map<String, String> configMap = jedis.hgetAll(iterator.next());
            int poolId = Integer.parseInt(configMap.get("raffleClass"));
            List<String> list = rafflePool.get(poolId);
            Map<Integer, Integer> map = exchangeMap.get(poolId);
            if (list == null) {
                list = new ArrayList<String>();
                rafflePool.put(poolId, list);
                map = new HashMap<Integer, Integer>();
                exchangeMap.put(poolId, map);
                map.put(list.size(), Integer.parseInt(configMap.get("id")));
                list.add(configMap.get("probability"));
            } else {
                map.put(list.size(), Integer.parseInt(configMap.get("id")));
                list.add(configMap.get("probability"));
            }
        }

    }
    /*
     * public byte[] buyItem(String uid,byte[] bytes){
     * ItemData.RequestBuyItem requestBuyItem = null;
     * ItemData.ResponseBuyItem.Builder builder =
     * ItemData.ResponseBuyItem.newBuilder();
     * try {
     * requestBuyItem = ItemData.RequestBuyItem.parseFrom(bytes);
     * }catch (Exception e){
     * log.error(e.getMessage(),e);
     * }
     * if (requestBuyItem == null){
     * builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
     * log.error(uid+":[buyItem] error");
     * }else {
     * IItem iItem = ItemDao.getInstance();
     * builder = iItem.buyItem(uid,requestBuyItem.getId(),requestBuyItem.getNum());
     * }
     * return builder.build().toByteArray();
     * }
     */

    // 1007
    public byte[] useItem(String uid, byte[] bytes) {
        ItemData.RequestUseItem requestUseItem = null;
        ItemData.ResponseUseItem.Builder builder = ItemData.ResponseUseItem.newBuilder();
        try {
            requestUseItem = ItemData.RequestUseItem.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestUseItem == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            log.error(uid + ":[useItem] error");
        } else {
            IItem iItem = ItemDao.getInstance();
            double value = iItem.updateItemInfo(uid, requestUseItem.getId(), requestUseItem.getNum());
            ItemData.Item itemData = ItemUtils.getItemData(requestUseItem.getId(), value);
            // builder = iItem.useItem(uid, requestUseItem.getId(), requestUseItem.getNum(), requestUseItem.getType());
            builder.addItem(itemData);
        }
        builder.setType(requestUseItem.getType());
        builder.setErrorId(0);
        return builder.build().toByteArray();
    }

    public byte[] openBagCell(String uid) {
        IItem iItem = ItemDao.getInstance();
        ItemData.ResponseOpenBagCell.Builder builder = iItem.openBagCell(uid);
        return builder.build().toByteArray();
    }

    // 请求物品合成道具
    public byte[] compose(String uid, byte[] bytes) {
        ItemData.RequestCompose requestCompose = null;
        ItemData.ResponseCompose.Builder builder = ItemData.ResponseCompose.newBuilder();
        try {
            requestCompose = ItemData.RequestCompose.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (requestCompose == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            log.error(uid + ":[compose] error");
        } else {
            IItem iItem = ItemDao.getInstance();
            builder = iItem.compose(uid, requestCompose.getId());
        }
        return builder.build().toByteArray();
    }

    public byte[]  countLotto(byte[] bytes, String uid) {
        LottoData.RequestCountLotto requestCountLotto = null;
        LottoData.ResponseCountLotto.Builder builder = LottoData.ResponseCountLotto.newBuilder();
        try {
            requestCountLotto = LottoData.RequestCountLotto.parseFrom(bytes);
            int poolId = requestCountLotto.getLottoId();
            int num = requestCountLotto.getNum();
            Map<String, String> raffleConfig = Redis.getExcelMap("raffleTypeconfig", poolId);

            /// /// System.err.println(raffleConfig);
//            System.err.println(num);
            if (num == 10) {
                num = 11;
            }
            ItemInfo itemCost = ItemUtils.getItemInfo(raffleConfig.get("cost" + num));
            // System.out.println(itemCost+"lllllllllllllllllllllllllllllll");
            double nowValue = 0;
//            System.out.println(requestCountLotto.getFree() + "  -free   lllllllllllllllllllllllllllllll");
            //不是免费抽的情况消耗金币
            if (requestCountLotto.getFree() != 1){
                nowValue = ItemDao.getInstance().updateItemInfo(uid, itemCost.getId(), -(long) itemCost.getNum());
            }else {
                nowValue = ItemDao.getInstance().getItemNum(uid, itemCost.getId());
            }
//            System.err.println(nowValue + "-金币数量");
            if (nowValue < 0 && requestCountLotto.getFree() != 1) {
                builder.setErrorId(-1);
                /// /// System.err.println("钱不够");
                return builder.build().toByteArray();
            } else {

                ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                reportBuilder.addItem(ItemUtils.getItemData(itemCost.getId(), nowValue));
                String getItemString = null;
                if (num == 1) {
                    getItemString = raffleConfig.get("get_item").split("\\|")[0];
                } else if (num == 11) {
                    getItemString = raffleConfig.get("get_item").split("\\|")[1];
                }
                ItemInfo getitem = ItemUtils.getItemInfo(getItemString);
                double cuurValue = ItemDao.getInstance().updateItemInfo(uid, getitem.getId(), (long) getitem.getNum());
                reportBuilder.addItem(ItemUtils.getItemData(getitem.getId(), cuurValue));
                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
            }
            int access = Integer.parseInt(raffleConfig.get("access"));
            PetData.ResponseOperatePet.Builder petbuilder = PetData.ResponseOperatePet.newBuilder();
            petbuilder.setErrorId(0);
            petbuilder.setType(1);
            for (int i = 0; i < num; i++) {
                int index = ranName.ranGift(rafflePool.get(poolId));
                int exchageId = exchangeMap.get(poolId).get(index);
                int petType = Integer.parseInt(Redis.getExcelInfo("rafflePetBasicconfig", exchageId + "", "pet_id"));
                PetEntity petEntity = null;
                if (access == PetConfig.petFromRafflePool) {
                    petEntity = PetUtils.createEvlutionPet(uid, petType, access);
                } else {
                    petEntity = PetUtils.createPet(uid, petType, access);
                }
                petbuilder.addPet(PetEntity.entityToPb(petEntity));
                PetData.PlainPet.Builder plainPetBuilder = PetData.PlainPet.newBuilder();
                plainPetBuilder.setPetId(petEntity.getPetType());
                plainPetBuilder.setRarity(petEntity.getStarLevel());
                plainPetBuilder.setPetType(petEntity.getPetType());
                builder.addPet(plainPetBuilder);
            }

            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petbuilder.build().toByteArray());

        } catch (Exception e) {
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        builder.setErrorId(0);
        /// /// System.err.println(JsonFormat.printToString(builder.build()));
        System.out.println("抽卡完成");
        return builder.build().toByteArray();
    }

    public void GetPetByRecharge(String uid) {
        PetData.ResponseOperatePet.Builder petbuilder = PetData.ResponseOperatePet.newBuilder();
        petbuilder.setErrorId(0);
        petbuilder.setType(1);

        int petType = 8;// 樱桃
        PetEntity petEntity = null;

        petEntity = PetUtils.createPet(uid, petType, 2);

        petbuilder.addPet(PetEntity.entityToPb(petEntity));
        PetData.PlainPet.Builder plainPetBuilder = PetData.PlainPet.newBuilder();
        plainPetBuilder.setPetId(petEntity.getPetType());
        plainPetBuilder.setRarity(petEntity.getStarLevel());
        plainPetBuilder.setPetType(petEntity.getPetType());
        petEntity.setStarLevel(5);
        ItemUtils.updatePlayerItems(uid, 31, 3, true);
        ItemUtils.updatePlayerItems(uid, 11, 5, true);
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petbuilder.build().toByteArray());
    }

    public byte[] getRafflePool(byte[] bytes, String uid) {
        LottoData.ResponseRafflePool.Builder builder = LottoData.ResponseRafflePool.newBuilder();
        Set<String> rafflePoolSet = Redis.scan("raffleTypeconfig*");
        Iterator<String> iterator = rafflePoolSet.iterator();
        Redis jedis = Redis.getInstance();
        while (iterator.hasNext()) {
            Map<String, String> rafflePoolConfig = jedis.hgetAll(iterator.next());
            String openTime = rafflePoolConfig.get("time_open");
            if (StringUtils.isEmpty(openTime)) {
                builder.addPoolId(Integer.parseInt(rafflePoolConfig.get("id")));
            } else {
                String endTime = rafflePoolConfig.get("time_end");
                if (!MyUtils.isOverdue(openTime, endTime)) {
                    /// /// System.err.println(openTime + "~~" + endTime);
                    builder.addPoolId(Integer.parseInt(rafflePoolConfig.get("id")));
                }
            }
        }
        /// /// System.err.println(JsonFormat.printToString(builder.build()) + "抽卡池" +
        /// rafflePoolSet.size());
        return builder.build().toByteArray();
    }

    public byte[] getPlayerItems(byte[] bytes, String uid) {
        ItemData.ResponsePlayerItem.Builder builder = ItemData.ResponsePlayerItem.newBuilder();
        try {
            List itemInfoList = itemDao.getplayerItem(uid);

            // 疑似新角色获取初始物品
            // 废弃
//            if (itemInfoList.size() < 2 && itemInfoList.size() >= 0) {
            if (false) {
                builder.setErrorId(0);
                ItemDao dao = ItemDao.getInstance();
                dao.updateItemInfo(uid, 1, 100000);
                dao.updateItemInfo(uid, 2, 3500);
                dao.updateItemInfo(uid, 3, 70);
                dao.updateItemInfo(uid, 5, 1);
                dao.updateItemInfo(uid, 6, 50);
                dao.updateItemInfo(uid, 10, 0);
                dao.updateItemInfo(uid, 11, 0);
                dao.updateItemInfo(uid, 13, 50);
                dao.updateItemInfo(uid, 21, 50);
                dao.updateItemInfo(uid, 22, 1);
                ItemData.ReportItem.Builder reportItem = ItemData.ReportItem.newBuilder();
                ItemData.Item.Builder item1 = ItemData.Item.newBuilder();

                // 金币
                item1.setNum(100000);
                item1.setId(1);
                builder.addItems(item1);
                reportItem.addItem(item1);
                ItemData.Item.Builder item2 = ItemData.Item.newBuilder();
                // 钻石
                item2.setNum(3500);
                item2.setId(2);
                builder.addItems(item2);
                reportItem.addItem(item2);
                ItemData.Item.Builder item5 = ItemData.Item.newBuilder();
                // 初级经验药水
                item5.setNum(1);
                item5.setId(5);
                builder.addItems(item5);
                reportItem.addItem(item5);
                ItemData.Item.Builder item6 = ItemData.Item.newBuilder();
                item6.setNum(50);
                item6.setId(6);
                builder.addItems(item6);
                reportItem.addItem(item6);
                ItemData.Item.Builder item10 = ItemData.Item.newBuilder();
                // 中级经验药水
                item10.setNum(0);
                item10.setId(10);
                builder.addItems(item10);
                reportItem.addItem(item10);
                ItemData.Item.Builder item11 = ItemData.Item.newBuilder();
                // 高级经验药水
                item11.setNum(0);
                item11.setId(11);
                builder.addItems(item11);
                reportItem.addItem(item11);
                ItemData.Item.Builder item13 = ItemData.Item.newBuilder();
                item13.setNum(50);
                item13.setId(13);
                builder.addItems(item13);
                reportItem.addItem(item13);
                ItemData.Item.Builder item14 = ItemData.Item.newBuilder();
                item14.setNum(70);
                item14.setId(3);
                builder.addItems(item14);
                reportItem.addItem(item14);
                ItemData.Item.Builder item15 = ItemData.Item.newBuilder();
                item15.setNum(50);
                item15.setId(21);
                builder.addItems(item15);
                reportItem.addItem(item15);
                ItemData.Item.Builder item16 = ItemData.Item.newBuilder();
                item16.setNum(1);
                item16.setId(22);
                builder.addItems(item16);
                reportItem.addItem(item16);
                PetEntity petEntity = new PetEntity();
                  //   PetUtils.createPet(uid,60,5);
                petEntity.setFriendId(uid);
                petEntity.setPetUId(60);
                petEntity.setPetType(60);
                petEntity.setCurrentLevel(20);
                petEntity.setCurrentExp(200);
                petEntity.setStarLevel(3);
                petEntity.setStrongLevel(1);
                petEntity.setPetCharacter(1);
                petEntity.setHp(1);
                petEntity.setAtk(1);
                petEntity.setDef(1);
                petEntity.setSatk(1);
                petEntity.setSdef(1);
                petEntity.setSpeed(1);
                petEntity.setNormalSkillId(126);
                petEntity.setSpSkillId(127);
                petEntity.setNormalSkillLv(1);
                petEntity.setSpSkillLv(1);
                petEntity.setName("a");
                petEntity.setLockStatus(0);
                petEntity.setBreakLV(0);
                petEntity.setGrowing(true);
                petEntity.setGetTime(TimerHandler.nowTimeStamp);
                petEntity.setRarity(3);
                petEntity.getAccessType();
                petEntity.setMainAttribute(3);
                petEntity.setSubAttribute(1);
               petEntity.setAccessType(2);
               petEntity.setMode(1);
                MySql.insert(petEntity);


                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportItem.build().toByteArray());
            } else {
                builder.setErrorId(0);

                for (int i = 0; i < itemInfoList.size(); i++) {
                    ItemEntity entity = (ItemEntity) itemInfoList.get(i);

                    builder.addItems(ItemUtils.entityToPBData(entity));
                }
            }

        } catch (Exception e) {
            e.printStackTrace();

        }
        /// /// System.err.println("playerItems:" +
        /// JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    public byte[] buyItem(String uid, byte[] bytes) {
        ItemData.ResponseBuyItem.Builder builder = ItemData.ResponseBuyItem.newBuilder();
        ItemData.RequestBuyItem requestBuyItem = null;
        builder.setErrorId(0);
        try {
            requestBuyItem = ItemData.RequestBuyItem.parseFrom(bytes);
            /// /// System.err.println(requestBuyItem);
            int goodsId = requestBuyItem.getId();
            int nums = requestBuyItem.getNum();
            ShopItemConfig shopItemConfig = (ShopItemConfig) SuperConfig.getCongifObject(SuperConfig.shopItemConfig,
                    goodsId);
            if (shopItemConfig == null) {
                builder.setErrorId(1);
                return builder.build().toByteArray();
            }
            int getItemId = shopItemConfig.getLinkItemId();
            int itemNums = shopItemConfig.getGetNums() * nums;
            int costItemId = shopItemConfig.getCostItemId();
            int costItemNums = shopItemConfig.getCotsNums() * nums;
            ItemUtils.updatePlayerItems(uid, costItemId, costItemNums, false);
            ItemUtils.updatePlayerItems(uid, getItemId, itemNums, true);
            builder.addItem(
                    ItemUtils.getItemData(shopItemConfig.getLinkItemId(), (double) shopItemConfig.getGetNums()));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] blackMarket(byte[] bytes, String uid) {
        ItemData.RequestBalckMarket requestBalckMarket = null;
        ItemData.ResponseBalckMarket.Builder builder = ItemData.ResponseBalckMarket.newBuilder();

        try {
            requestBalckMarket = ItemData.RequestBalckMarket.parseFrom(bytes);
            int type = requestBalckMarket.getType();
            builder.setErrorId(0);
            builder.setType(type);
            int flushNums = RoleAdditionalEntityDao.getInstance().getBlackMarketFlushNums(uid);
            CommonConfig commonConfigObject = (CommonConfig) SuperConfig.configMap.get(SuperConfig.commonConfig)
                    .get(60);
            int flushLimit = commonConfigObject.getMarketRefreshNum();
            // 获取黑市商品信息
            if (type == 1) {
                Object object = RoleAdditionalEntityDao.getInstance().getROleBlackMarket(uid);
                RoleAdditionalEntity entity = (RoleAdditionalEntity) object;
                if (entity.getBlackMarket() == null) {
                    List<Integer> list = reflushBlackMarket();
                    ItemData.GoodsInfos.Builder goodsInfosBu = ItemData.GoodsInfos.newBuilder();
                    for (Integer id : list) {
                        ItemData.GoodsInfo.Builder goodsBu = ItemData.GoodsInfo.newBuilder();
                        goodsBu.setId(id);
                        goodsBu.setIsSellOut(false);
                        builder.addGoodsInfo(goodsBu);
                        ItemData.GoodsInfo.Builder goods = ItemData.GoodsInfo.newBuilder();
                        goods.setId(id);
                        goods.setIsSellOut(false);
                        goodsInfosBu.addGoodsInfo(goods);
                    }
                    builder.setReflushNums(flushLimit);

                    /// /// System.err.println(JsonFormat.printToString(goodsInfosBu.build()));
                    entity.setBlackMarket(goodsInfosBu.build().toByteArray());
                    entity.setMarketFlushNums(0);
                    entity.setUid(uid);
                    MySql.update(entity);
                } else {
                    ItemData.GoodsInfos goodsInfos = ItemData.GoodsInfos.parseFrom(entity.getBlackMarket());
                    List<ItemData.GoodsInfo> goodsInfoList = goodsInfos.getGoodsInfoList();
                    /// /// System.err.println(goodsInfoList.size() + "size");
                    for (ItemData.GoodsInfo goodsInfo : goodsInfoList) {
                        builder.addGoodsInfo(goodsInfo);
                    }
                    builder.setReflushNums(flushLimit - flushNums);
                }

                // 2刷新商品信息
            } else if (type == 2) {
                List<Integer> list = reflushBlackMarket();
                ItemData.GoodsInfos.Builder goodsInfosBu = ItemData.GoodsInfos.newBuilder();
                for (Integer id : list) {
                    ItemData.GoodsInfo.Builder goodsBu = ItemData.GoodsInfo.newBuilder();
                    goodsBu.setId(id);
                    goodsBu.setIsSellOut(false);
                    builder.addGoodsInfo(goodsBu);
                    goodsInfosBu.addGoodsInfo(goodsBu);
                }
                ItemUtils.updatePlayerItems(uid, 2, 15, false);
                builder.setReflushNums(flushLimit - flushNums - 1);
                byte[] goodsInfoByteArray = goodsInfosBu.build().toByteArray();
                StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
                RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
                entity.setMarketFlushNums(entity.getMarketFlushNums() + 1);
                entity.setBlackMarket(goodsInfoByteArray);
                MySql.update(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        /// /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    public static List<Integer> reflushBlackMarket() {
        if (blackMarketPool.size() == 0) {
            initBlackMarketPool();
        }
        List<Integer> list = new ArrayList<Integer>();
        int[] goodsPool = new int[blackMarketPool.size()];
        for (int i = 0; i < blackMarketPool.size(); i++) {
            goodsPool[i] = blackMarketPool.get(i);
        }
        int goodsRealSize = goodsPool.length;
        int loop = (goodsRealSize >= 9 ? 9 : goodsRealSize);
        for (int i = 0; i < loop; i++) {
            int index = (int) (Math.random() * goodsRealSize);

            list.add(goodsPool[index]);
            goodsPool[index] = goodsPool[goodsRealSize - 1];
            goodsRealSize--;
        }

        return list;
    }

    private static List<Integer> blackMarketPool = new ArrayList<Integer>();

    public static void initBlackMarketPool() {
        try {
            Map<Integer, Object> configMaps = SuperConfig.getCongifMap(SuperConfig.shopItemConfig);
            for (Map.Entry<Integer, Object> entry : configMaps.entrySet()) {
                ShopItemConfig config = (ShopItemConfig) entry.getValue();
                if (config.getType() == 1) {
                    blackMarketPool.add(config.getId());
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public byte[] buyBlackMarketItem(byte[] bytes, String uid) {
        ItemData.ResponseBuyBalckMarketItem.Builder builder = ItemData.ResponseBuyBalckMarketItem.newBuilder();
        ItemData.RequestBuyBalckMarketItem requestBuyItem = null;
        builder.setErrorId(0);
        try {
            requestBuyItem = ItemData.RequestBuyBalckMarketItem.parseFrom(bytes);
            int goodsId = requestBuyItem.getGoodsId();
            ShopItemConfig shopItemConfig = (ShopItemConfig) SuperConfig.getCongifObject(SuperConfig.shopItemConfig,
                    goodsId);
            if (shopItemConfig == null) {

            }
            if (shopItemConfig.getType() != 1) {

            }
            int getItemId = shopItemConfig.getLinkItemId();
            int itemNums = shopItemConfig.getGetNums();
            int costItemId = shopItemConfig.getCostItemId();
            int costItemNums = shopItemConfig.getCotsNums();
            ItemUtils.updatePlayerItems(uid, costItemId, costItemNums, false);
            ItemUtils.updatePlayerItems(uid, getItemId, itemNums, true);
            builder.addItem(
                    ItemUtils.getItemData(shopItemConfig.getLinkItemId(), (double) shopItemConfig.getGetNums()));
            Object object = RoleAdditionalEntityDao.getInstance().getROleBlackMarket(uid);
            byte[] blackMarketInfos = ((RoleAdditionalEntity) object).getBlackMarket();
            ItemData.GoodsInfos balckMarket = ItemData.GoodsInfos.parseFrom(blackMarketInfos);
            List<protocol.ItemData.GoodsInfo> goodsList = balckMarket.getGoodsInfoList();
            ItemData.GoodsInfos.Builder goodsInfosBu = null;
            int i = 0;
            for (protocol.ItemData.GoodsInfo goodsInfo : goodsList) {
                if (goodsInfo.getId() == goodsId) {
                    protocol.ItemData.GoodsInfo.Builder goods = goodsInfo.toBuilder();
                    goods.setIsSellOut(true);
                    goodsInfosBu = balckMarket.toBuilder();
                    goodsInfosBu.setGoodsInfo(i, goods);
                    /// /// System.err.println("~~~~~~~~~~~~~~买了" + goodsId);
                    break;
                }
                i++;
            }

            StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
            RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
            /// /// System.err.println("1" + JsonFormat.printToString(balckMarket));
            /// /// System.err.println("2" +
            /// JsonFormat.printToString(goodsInfosBu.build()));
            entity.setBlackMarket(goodsInfosBu.build().toByteArray());
            MySql.update(entity);
            ItemData.ResponseBalckMarket.Builder balckMarketBuilder = ItemData.ResponseBalckMarket.newBuilder();
            balckMarketBuilder.setType(1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    // 合成道具
    public byte[] itemCompose(byte[] bytes, String uid) {
        /// System.out.println("合成道具");
        ItemData.ResponseItemCompose.Builder builder = ItemData.ResponseItemCompose.newBuilder();
        builder.setErrorId(0);
        ItemData.RequestItemCompose itemCompose = null;
        try {
            ItemDao itemDao = ItemDao.getInstance();
            /// System.err.println("bytes" + bytes);
            itemCompose = ItemData.RequestItemCompose.parseFrom(bytes);
            int composeConfigId = itemCompose.getItemID();
            ItemComposeConfig itemComposeConfig = (ItemComposeConfig) SuperConfig.getCongifObject(SuperConfig.itemComposeConfig, composeConfigId);
            List<CommonInfo> costItemList = itemComposeConfig.getCostItem();
            int composeNums = itemCompose.getItemNum();
            for (CommonInfo item : costItemList) {
                int itemId = item.getKey();
                int needItem = item.getValue() * composeNums;
                // 检验玩家是否拥有相应物品数量
                int curNums = itemDao.getItemCurNums(uid, itemId);
                if (needItem > curNums) {
                    builder.setErrorId(1);
                    return builder.build().toByteArray();
                }
            }
            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();

            // 更新消耗的物品
            for (CommonInfo item : costItemList) {
                int itemId = item.getKey();
                int needItem = item.getValue() * composeNums;
                int curItemNums = (int) itemDao.updateItemInfo(uid, itemId, -needItem);
                ItemData.Item.Builder itemBuilder = ItemData.Item.newBuilder();
                itemBuilder.setNum(curItemNums);
                itemBuilder.setId(itemId);
                reportBuilder.addItem(itemBuilder);
            }
            // 生成新物品
            int newItemId = itemComposeConfig.getItemId();
            int newItemNums = (int) itemDao.updateItemInfo(uid, newItemId, composeNums);
            ItemData.Item.Builder itemBuilder = ItemData.Item.newBuilder();
            itemBuilder.setId(newItemId);
            itemBuilder.setNum(composeNums);
            builder.addCostItem(itemBuilder);
            ItemData.Item.Builder curItemBuilder = ItemData.Item.newBuilder();
            curItemBuilder.setId(newItemId);
            curItemBuilder.setNum(newItemNums);
            reportBuilder.addItem(curItemBuilder);
            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestAddItem(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
        ItemData.RequestAddItem requestAddItem = ItemData.RequestAddItem.parseFrom(inBytes);
        ItemData.ReportItem.Builder builder = ItemData.ReportItem.newBuilder();

        ItemDao itemDao = ItemDao.getInstance();

        for (int i = 0; i < requestAddItem.getItemList().size(); i++) {
            double total = itemDao.updateItemInfo(uid,requestAddItem.getItemList().get(i).getId(),(long)requestAddItem.getItemList().get(i).getNum());
//            ItemData.Item.Builder itemBuilder = ItemData.Item.newBuilder();
//            StringBuilder stringBuilder = new StringBuilder("select count(1) from ItemEntity where itemid=")
//                    .append(requestAddItem.getItemList().get(i).getId()).append(" and uid='").append(uid).append("'");
//            long isItemId = (long) MySql.queryForOne(stringBuilder.toString());
//
//            if (isItemId == 0) {
//                ItemEntity itemEntity = new ItemEntity();
//                itemEntity.setUid(uid);
//                itemEntity.setItemid(requestAddItem.getItemList().get(i).getId());
//                itemEntity.setItemnum(requestAddItem.getItemList().get(i).getNum());
//                itemEntity.setType(1);
//                itemEntity.setVersion(0);
//                MySql.insert(itemEntity);
//                itemBuilder.setId(itemEntity.getItemid());
//                itemBuilder.setNum(itemEntity.getItemnum());
//                builder.addItem(itemBuilder);
//
//            } else {
//                stringBuilder = new StringBuilder("from ItemEntity where itemid=")
//                        .append(requestAddItem.getItemList().get(i).getId()).append(" and uid='").append(uid)
//                        .append("'");
//                ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
//                itemEntity.setItemnum(requestAddItem.getItemList().get(i).getNum() + itemEntity.getItemnum());
//                // ItemUtils.updatePlayerItems(uid,requestAddItem.getItemList().get(i).getId(),
//                // (int) requestAddItem.getItemList().get(i).getNum(),true);
//                itemBuilder.setId(itemEntity.getItemid());
//                itemBuilder.setNum(itemEntity.getItemnum());
//                builder.addItem(itemBuilder);
//                MySql.update(itemEntity);
//            }


            ItemData.Item.Builder itemBuilder = ItemData.Item.newBuilder();
            itemBuilder.setId(requestAddItem.getItemList().get(i).getId());
            itemBuilder.setNum(total);
            System.out.println("total:::::::::::::::::::::::::::::" + total);
            builder.addItem(itemBuilder);
        }

        // ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE,
        // builder.build().toByteArray());


        return builder.build().toByteArray();
    }

    // 消耗物品
    public byte[] RequestPhysical(String uid, byte[] bytes) {
        try {
            ItemData.ReportItem.Builder builder = ItemData.ReportItem.newBuilder();
            ConsumeData.RequestPhysical requestData = ConsumeData.RequestPhysical.parseFrom(bytes);
            for (ItemData.Item item : requestData.getConsumeList()) {
                System.err.println("消耗物品：\n" + item.getId() + "\n" + item.getNum());
                ItemEntity dataBaseItem = ItemDao.getInstance().queryItemGildCoins(uid, item.getId());
                if (dataBaseItem != null) {
                    double num = dataBaseItem.getItemnum() - item.getNum();
                    num = num < 0 ? 0 : num;
                    dataBaseItem.setItemnum(num);
                    ItemDao.getInstance().update(dataBaseItem);

                    // 装载返回数据
                    ItemData.Item.Builder itemBuilder = ItemData.Item.newBuilder();
                    itemBuilder.setId(item.getId());
                    itemBuilder.setNum(num);
                    builder.addItem(itemBuilder);
                }
            }
            // 更新物品
            if (builder.getItemCount() > 0) {
                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, builder.build().toByteArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public byte[] RequestSynthesis(byte[] inBytes, String uid) throws Exception {
        ItemData.RequestSynthesis requestSynthesis = ItemData.RequestSynthesis.parseFrom(inBytes);
        ItemData.ResponseSynthesis.Builder responseSynthesis = ItemData.ResponseSynthesis.newBuilder();
        ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
        ItemData.Item.Builder item = ItemData.Item.newBuilder();
        responseSynthesis.setErrorId(0);
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("ItemComposeconfig:*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> mailMap = jedis.hgetAll(key);
            if (Integer.parseInt(mailMap.get("id")) == requestSynthesis.getId()) {
                Map<String, String> map = itemsMap(mailMap.get("CostItem"));
                for (String str : map.keySet()) {
                    StringBuilder stringBuilder = new StringBuilder("from ItemEntity where uid='").append(uid)
                            .append("' and itemid=").append(str);
                    ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
                    if (itemEntity == null) {
                        itemEntity = new ItemEntity();
                        itemEntity.setUid(uid);
                        itemEntity.setItemnum(0);
                        itemEntity.setItemid(Integer.parseInt(str));
                        itemEntity.setType(1);
                        itemEntity.setVersion(0);
                        MySql.insert(itemEntity);
                        responseSynthesis.setErrorId(1);
                        System.err.println("1111111111");
                        return responseSynthesis.build().toByteArray();
                    }
                    if (itemEntity.getItemnum() <= 0) {
                        responseSynthesis.setErrorId(1);
                        System.err.println("2222222222");
                        return responseSynthesis.build().toByteArray();
                    }
                    if (itemEntity.getItemnum() < (Integer.parseInt(map.get(str)) * requestSynthesis.getNum())) {
                        responseSynthesis.setErrorId(1);
                        System.err.println("3333333333");
                        return responseSynthesis.build().toByteArray();
                    }
                    itemEntity.setItemnum(
                            itemEntity.getItemnum() - (Integer.parseInt(map.get(str)) * requestSynthesis.getNum()));
                    item.setNum(itemEntity.getItemnum());
                    item.setId(itemEntity.getItemid());
                    reportBuilder.addItem(item);
                    MySql.update(itemEntity);
                }

                StringBuilder stringBuilder = new StringBuilder("from ItemEntity where uid='").append(uid)
                        .append("' and itemid=").append(Integer.parseInt(mailMap.get("ItemID")));
                ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
                if (itemEntity == null) {
                    itemEntity = new ItemEntity();
                    // 添加
                    itemEntity.setItemnum(requestSynthesis.getNum());
                    itemEntity.setVersion(0);
                    itemEntity.setType(1);
                    itemEntity.setItemid(Integer.parseInt(mailMap.get("ItemID")));
                    itemEntity.setUid(uid);
                    item.setNum(itemEntity.getItemnum());
                    item.setId(itemEntity.getItemid());
                    reportBuilder.addItem(item);
                    MySql.insert(itemEntity);
                } else {
                    // 修改
                    itemEntity.setItemnum(itemEntity.getItemnum() + requestSynthesis.getNum());
                    item.setNum(itemEntity.getItemnum());
                    item.setId(itemEntity.getItemid());
                    reportBuilder.addItem(item);
                    MySql.update(itemEntity);
                }
            }
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
        return responseSynthesis.build().toByteArray();
    }

    public static Map<String, String> itemsMap(String str) {
        Map<String, String> map = new HashMap<>();
        String[] keys = str.split("\\|");
        for (int i = 0; i < keys.length; i++) {
            String[] strs = keys[i].split(",");
            map.put(strs[0], strs[1]);
        }
        return map;
    }
}