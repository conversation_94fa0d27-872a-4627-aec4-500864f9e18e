package model;

import protocol.UserData;

/**
 * Created by nara on 2018/4/12.
 */
public class PointDoubleInfo {
    private double x;
    private double y;

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public static UserData.PointDouble toPointDoubleData(PointDoubleInfo pointDoubleInfo) {
        UserData.PointDouble.Builder builder = UserData.PointDouble.newBuilder();
        builder.setX(pointDoubleInfo.getX());
        builder.setY(pointDoubleInfo.getY());
        return builder.build();
    }

    public static PointDoubleInfo pointDoubleDataToInfo(UserData.PointDouble pointDouble) {
        PointDoubleInfo pointDoubleInfo = new PointDoubleInfo();
        pointDoubleInfo.setX(pointDouble.getX());
        pointDoubleInfo.setY(pointDouble.getY());
        return pointDoubleInfo;
    }
}
