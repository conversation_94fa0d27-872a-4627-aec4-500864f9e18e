public class Test5 implements Runnable {
    private int ticket=10;
    private boolean flag=true;
    @Override
    public void run() {
//        while (flag) {
            Multithreading();
//        }
    }

    private void Multithreading() {
//        if (ticket <= 0) {
//            System.out.println("没了");
//            flag=false;
//            return;
//        }
        System.out.println(Thread.currentThread().getName()+"\t票数\t"+ticket);
//        ticket--;
    }
}