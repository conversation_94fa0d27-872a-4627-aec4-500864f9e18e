//package module.ranking;
//
//import common.SuperConfig;
//import manager.MySql;
//import manager.Redis;
//import model.RankInfo;
//import module.callback.CallBack;
//import module.callback.CallBackOrder;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import protocol.FriendData;
//import protocol.RankingData;
//import utils.MyUtils;
//
//import java.util.Map;
//
//public class RankingDao implements IRanking{
//    private static Logger log = LoggerFactory.getLogger(RankingDao.class);
//    private static RankingDao inst = null;
//    public static RankingDao getInstance() {
//        if (inst == null) {
//            inst = new RankingDao();
//        }
//        return inst;
//    }
//    public void getLvRankFromSqlToRedis(){
//        String sql = "from RoleEntity as t1, PartEntity as t2, ItemEntity as t3 where t1.uid = t2.uid and t2.uid = t3.uid and t3.itemid=25 and t2.cupboard = 0 AND t1.roleid = t2.roleid ORDER BY t1.lv DESC,t3.itemid DESC";
////        String sql = " FROM RoleEntity LEFT JOIN ItemEntity ON RoleEntity.uid = ItemEntity.uid LEFT JOIN PartEntity ON ItemEntity.uid = PartEntity.uid WHERE ItemEntity.itemid=25 AND PartEntity.cupboard = 0 ORDER BY RoleEntity.lv DESC,ItemEntity.itemid DESC limit 1,"+SuperConfig.RANKSIZE;
//        CallBack callBack = new RankingCallBack(CallBackOrder.RANKFORLVBACK);
//        MySql.queryInSql(sql,callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
//    }
//
//    @Override
//    public void getScoreRankFromSqlToRedis() {
//        String sql = "SELECT r.userid,r.name,r.lv,r.head, COUNT(*)*10 AS score FROM  RoleEntity r LEFT JOIN PetEntity p ON r.uid=p.friendId  GROUP BY p.friendId ORDER BY score DESC";
//        CallBack callBack = new RankingCallBack(CallBackOrder.RANKFORSCORE);
//        MySql.queryInSql(sql,callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
//    }
//
//    @Override
//    public void getPetNumRankSqlToRedis() {
//        String sql = "SELECT r.userid,r.name,r.lv,r.head, COUNT(*) AS ranknumber FROM  PetEntity p LEFT JOIN RoleEntity r ON r.uid=p.friendId  GROUP BY p.friendId ORDER BY ranknumber DESC";
//        CallBack callBack = new RankingCallBack(CallBackOrder.RANKFORPETNUMBER);
//        MySql.queryInSql(sql,callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
//    }
//
//    private RankingData.Rank.Builder getRankList(int type){
//        Redis jedis = Redis.getInstance();
//        Map<String,String> rankMap = jedis.hgetAll("rankinfo:"+type);
//        if (rankMap.size() == 0){
//            return null;
//        }
//        RankingData.Rank.Builder rankBu = RankingData.Rank.newBuilder();
//        rankBu.setType(type);
//        for (Map.Entry<String,String> entry:rankMap.entrySet()){
//            RankInfo rankInfo = (RankInfo)MyUtils.jsonToBean(entry.getValue(),RankInfo.class);
//            RankingData.RankPlayer.Builder rankPlayerBu = RankingData.RankPlayer.newBuilder();
//            rankPlayerBu.setId(rankInfo.getRank());
//            rankPlayerBu.setScore(rankInfo.getScore());
//            rankPlayerBu.setRoleId(rankInfo.getRoleId());
//            FriendData.Player.Builder playerBu = FriendData.Player.newBuilder();
//            playerBu.setId(rankInfo.getId());
//            playerBu.setName(rankInfo.getName());
//            playerBu.setLv(rankInfo.getLv());
//            playerBu.setHead(rankInfo.getHead());
//            rankPlayerBu.setPlayer(playerBu);
//            rankPlayerBu.setNumber(rankInfo.getPetNumber());
//            rankBu.addRankPlayers(rankPlayerBu);
//        }
//        return rankBu;
//    }
//
//    public RankingData.ResponseGetRanking.Builder getRankList(String uid){
//        RankingData.ResponseGetRanking.Builder builder = RankingData.ResponseGetRanking.newBuilder();
//        builder.setErrorId(0);
//        Redis jedis = Redis.getInstance();
//        Map<String,String> map = jedis.hgetAll("role:"+uid);
//        RankingData.Rank.Builder rank1 = getRankList(1);
//        if (rank1 != null){
//            rank1.setScore(Integer.parseInt(map.get("lv")));
//            builder.addRankList(rank1);
//        }
//        RankingData.Rank.Builder rank2 = getRankList(2);
//        if (rank2 != null){
//            rank2.setScore(Integer.parseInt(map.get("score")));
//            builder.addRankList(rank2);
//        }
//        RankingData.Rank.Builder rank3 = getRankList(3);
//        if (rank3 != null){
//            rank3.setScore(Integer.parseInt(map.get("petnum")));
//            builder.addRankList(rank3);
//        }
//        return builder;
//    }
//}
