package common;

import model.CommonInfo;

import java.util.List;

@ExcelConfigObject(key = "daily")
public class DailyConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "num")
    private int num;
    @ExcelColumn(name = "item")
    private List<CommonInfo> award;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public List<CommonInfo> getAward() {
        return award;
    }

    public void setAward(List<CommonInfo> award) {
        this.award = award;
    }

    @Override
    public String toString() {
        return "DailyConfig{" +
                "id=" + id +
                ", num=" + num +
                ", award=" + award +
                '}';
    }
}
