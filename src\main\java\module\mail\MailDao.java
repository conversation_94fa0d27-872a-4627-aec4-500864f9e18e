package module.mail;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.ItemEntity;
import entities.MailEntity;
import manager.MySql;
import manager.ReportManager;
import module.item.ItemDao;
import module.login.LoginDao;
import module.mail.mail_tool.MailGetRewards;
import org.hibernate.NonUniqueResultException;
import protocol.ItemData;
import protocol.MailData;
import protocol.ProtoData;
import protocol.UserData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MailDao {
    private static MailDao inst;

    public static MailDao getInstance() {
        if (inst == null) {
            inst = new MailDao();
        }
        return inst;
    }

    /**
     * //操作邮件
     * //type = 0删除（拒绝） 1领取（同意）
     * //0表示成功其余为错误id
     */
    public byte[] operateMail(byte[] bytes, String uid) {
        MailData.RequestOperateMail requestOperateMail = null;
        //创建MailData.ResponseOperateMail.Builder对象
        MailData.ResponseOperateMail.Builder responseOperateMailBu = MailData.ResponseOperateMail.newBuilder();
        try {
            //反序列化操作
            requestOperateMail = MailData.RequestOperateMail.parseFrom(bytes);
            int mid = requestOperateMail.getMid();
            int type = requestOperateMail.getType();
            responseOperateMailBu.setErrorId(0);
            responseOperateMailBu.setMid(mid);
            responseOperateMailBu.setType(type);
//            ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEMAIL_VALUE,responseOperateMailBu.build().toByteArray());
            //0删除 1领取 2已读
            //删除（拒绝）
            if (type == 0) {
                StringBuilder hql = new StringBuilder("delete from MailEntity where mid=").append(mid).append("and owner='").append(uid).append("'");
                MySql.updateSomes(hql.toString());
                //领取（同意）
            } else if (type == 1) {
                StringBuilder hql = new StringBuilder("from MailEntity where mid=").append(mid).append("and owner='").append(uid).append("'");
                MailEntity mailEntity = (MailEntity) MySql.queryForOne(hql.toString());
                mailEntity.setStatus(1);
                MySql.update(mailEntity);
                if (mailEntity == null) {
                    responseOperateMailBu.setErrorId(1);
                    return responseOperateMailBu.build().toByteArray();
                }
                byte[] byteObject = mailEntity.getAttchment();
                if (byteObject == null || byteObject.length == 0) {
                    responseOperateMailBu.setErrorId(0);
                } else {
                    MailData.Attachment attacment = MailData.Attachment.parseFrom(byteObject);
                    MailGetRewards.getInstance().GetRewards(uid, attacment);


//                    ItemDao itemDao = ItemDao.getInstance();
//                    ItemData.ReportItem.Builder reportBu = ItemData.ReportItem.newBuilder();
//                    List<ItemData.Item> itemList = attacment.getItemList();
//                    //领取邮件的奖励
//                    responseOperateMailBu.addAllItem(itemList);
//                    for (ItemData.Item item : itemList) {
//                        double addNums = item.getNum();
//                        int itemId = item.getId();
//                        double nowValue = itemDao.updateItemInfo(uid, itemId, (long) addNums);
//                        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
//                        itemBu.setNum(nowValue);
//                        itemBu.setId(itemId);
//                        reportBu.addItem(itemBu);
//                    }
//                    ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBu.build().toByteArray());


//                    mailEntity.setStatus(1);
//                    MySql.update(mailEntity);
                }
            } else if (type == 2) {
                StringBuilder hql = new StringBuilder("update MailEntity set status=").append(1).append(" where mid=").append(mid).append("and owner='").append(uid).append("' ");
                MySql.updateSomes(hql.toString());
            } else if (type == 3) {
                //获取到已读未领的邮件
                StringBuilder stringBuilder = new StringBuilder("from MailEntity where owner='").append(uid).append("' and status!=1");
                List<Object> mailList = MySql.queryForList(stringBuilder.toString());
                Map<Integer, Double> itemMap = new HashMap<>();
                for (int i = 0; i < mailList.size(); i++) {
                    MailEntity mailEntity = (MailEntity) mailList.get(i);
                    responseOperateMailBu.setMid(mailEntity.getMid());
                    mailEntity.setStatus(1);
                    responseOperateMailBu.setType(1);
                    responseOperateMailBu.setErrorId(0);
                    System.out.println(mailEntity.toString());
                    MailData.Attachment attacment = MailData.Attachment.parseFrom(mailEntity.getAttchment());
                    System.out.println("邮件中的附件");
                    for (int j = 0; j < attacment.getItemList().size(); j++) {
                        //更新道具
                        if (itemMap.get(attacment.getItemList().get(j).getId()) == null) {
                            itemMap.put(attacment.getItemList().get(j).getId(), attacment.getItemList().get(j).getNum());
                        } else {
                            double num = (itemMap.get(attacment.getItemList().get(j).getId()) + attacment.getItemList().get(j).getNum());
                            itemMap.remove(attacment.getItemList().get(j).getId());
                            itemMap.put(attacment.getItemList().get(j).getId(), num);
                        }
                    }
                    MySql.update(mailEntity);
                    ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEMAIL_VALUE, responseOperateMailBu.build().toByteArray());
                }
                ItemData.ReportItem.Builder itemBuilder = ItemData.ReportItem.newBuilder();
                ItemData.Item.Builder item = ItemData.Item.newBuilder();
                for (Integer key : itemMap.keySet()) {
                    stringBuilder = new StringBuilder("from ItemEntity where uid='").append(uid).append("' and itemid=").append(key);
                    ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
                    itemEntity.setItemnum(itemMap.get(key) + itemEntity.getItemnum());
                    item.setId(itemEntity.getItemid());
                    item.setNum(itemEntity.getItemnum());
                    itemBuilder.addItem(item);
                    MySql.update(itemEntity);
                }
                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, itemBuilder.build().toByteArray());
                responseOperateMailBu.setMid(0);
                return responseOperateMailBu.build().toByteArray();
            } else {
                responseOperateMailBu.setErrorId(3);
            }
        } catch (NonUniqueResultException queryException) {
            queryException.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseOperateMailBu.build().toByteArray();
    }

    //传输邮件信息给前端
    public MailData.Mail entityToPb(MailEntity entity) {
        MailData.Mail.Builder builder = MailData.Mail.newBuilder();
        builder.setMid(entity.getMid());
        builder.setMailId(entity.getMailId());
        builder.setSubjectType(entity.getSubjectType());
        builder.setContent(entity.getContent());
        builder.setStatus(entity.getStatus());
        builder.setTimeStamp(entity.getTimestamp());
        builder.setOverdueTime(entity.getOverdueTime());
        String sender, owner, title = null;
        if ((sender = entity.getSender()) != null) {
            builder.setSender(sender);
        }
        if ((owner = entity.getOwner()) != null) {
            builder.setOwner(owner);
        }
        if ((title = entity.getTitle()) != null) {
            builder.setTitle(title);
        }
        byte[] attchment = null;
        if ((attchment = entity.getAttchment()) != null) {
            try {
                builder.setAttchment(MailData.Attachment.parseFrom(attchment));
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
            }

        }
        return builder.build();

    }
}
