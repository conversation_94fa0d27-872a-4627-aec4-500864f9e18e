package utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONObject;



/**
 * 2018-07-20
 * <AUTHOR>
 *
 */
public class HttpClientUtils {
    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtils.class);

    //设置超时时间
    private int timeout = 30000;

    private RequestConfig requestConfig;

    private static final HttpClientUtils httpsRequest = new HttpClientUtils();
    
    private HttpClientUtils(){
        requestConfig = RequestConfig.custom().setSocketTimeout(timeout).setConnectionRequestTimeout(timeout).build();

    }

    public static HttpClientUtils getHttpsRequestSingleton() {
        return httpsRequest;
    }

    /**
     * 发送get请求
     * 
     * @param url
     * @param params
     * @return
     * @throws IOException
     */
    public JSONObject sendGet(String url, Map<String, String> params) throws IOException {
        Iterator<Map.Entry<String, String>> iter = params.entrySet().iterator();
        StringBuffer urlParamsBuffer = new StringBuffer();
        while(iter.hasNext()) {
            Map.Entry<String, String> entry = iter.next();
            urlParamsBuffer.append(entry.getKey()+"="+entry.getValue()+"&");
        }
        String getUrl = url;
        if(urlParamsBuffer.length() > 0) {
            urlParamsBuffer.deleteCharAt(urlParamsBuffer.length() - 1);
            getUrl += '?'+ urlParamsBuffer.toString();
        }

        CloseableHttpClient httpClient = HttpClients.createDefault();
        logger.info(getUrl);
        HttpGet httpGet;
        httpGet = new HttpGet(getUrl);
        httpGet.setConfig(requestConfig);
        
        try {
            HttpResponse response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            String responseContent = EntityUtils.toString(entity);
            logger.info("&*&*&*&*&*&*&*"+responseContent+"#%^$^&*@$^#%^%$");

			JSONObject responseJson = JSONObject.fromObject(responseContent);

			return responseJson;
        } catch (IOException e) {
              logger.error(e.getMessage(),e);
              throw e;
        }
        finally {
           
        }
    }

}