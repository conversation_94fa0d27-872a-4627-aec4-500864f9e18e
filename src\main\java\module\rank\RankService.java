package module.rank;

import com.google.protobuf.InvalidProtocolBufferException;
import module.login.ILogin;
import module.login.LoginDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ProtoData;
import protocol.RankData;
import protocol.UserData;

/**
 * Created by nara on 2018/5/14.
 */
public class RankService {
    private static Logger log = LoggerFactory.getLogger(RankService.class);
    private static RankService inst = null;
    public static RankService getInstance() {
        if (inst == null) {
            inst = new RankService();
        }
        return inst;
    }

    public byte[] getRank(byte[] bytes, String uid){
        // 获取默认数据
        RankData.RequestRank requestRanking=null;
        RankData.ResponseRank.Builder builder = RankData.ResponseRank.newBuilder();
        try {
                requestRanking=RankData.RequestRank.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
        }
        // 初始化
        builder.setMold(requestRanking.getMold());
        builder.setErrorId(0);

        RankDao rankDao = RankDao.getInstance();
        switch (requestRanking.getMold()){
            case 1:
                // 综合分
                rankDao.GetRankingScoreList(uid, builder);
                break;
            case 2:
                // 等级
                rankDao.GetRankingLevelList(uid, builder);
                break;
            case 3:
                // 数量
                rankDao.GetRankingPetList(uid, builder);
                break;
            default:
                builder.setErrorId(1);
                break;
        }
        return builder.build().toByteArray();
    }

}