package module.task;

import model.ActivitiesInfo;
import model.TaskInfo;
import protocol.TaskData;

import java.util.List;

/**
 * Created by nara on 2018/1/11.
 */
public interface ITask {
    List<TaskInfo> initDailyTask(String uid);
    List<TaskInfo> initAchievement(String uid);
    Boolean updateOneTask(String uid,int taskId,long increment);
    TaskData.ResponseFinishTask.Builder finishOneTask(String uid,int taskId);
    Boolean updateAchievement(String uid,int type,long num);
    void reportTask(String uid,int taskId,long nowNum);
    public  List<TaskInfo> addAchievement(String uid);
    public List<TaskInfo> addDailyTask(String uid);
    public void  updateMainAchievement(String uid,int type,long num);
    public List<TaskInfo> initMainAchievement(String uid);
    public List<ActivitiesInfo> initActivities(String uid);
    public List <Object> addActivities(String uid,List<Integer> list);
    public void updateActivities(String uid,int id,long num);
}
