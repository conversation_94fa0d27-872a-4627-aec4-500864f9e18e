package manager;

import common.SuperConfig;
import entities.MessageEntity;
import entities.RelativeshipEntity;
import io.netty.channel.ChannelHandlerContext;
import model.MailInfo;
import model.MessageInfo;
import model.RankInfo;
import module.activity.LimitedTime.LimitedTimeRewardTimer;
import module.boss.BossService;
import module.callback.CallBack;
import module.callback.CallBackOrder;
import module.database_backup.DatabaseBackup;
import module.friend.FriendCallBack;
import module.friend.FriendDao;
import module.friend.IFriend;
//import module.ranking.IRanking;
import module.playerstatus.PlayerOnlineTimer;
import module.pvp.PVPTImer;
import module.robot.Factory;
import module.robot.ranName;
import module.role.RoleDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.BossData;
import protocol.FriendData;
import protocol.ProtoData;
import protocol.UserData;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import server.SuperServerHandler;
import utils.MyUtils;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by nara on 2017/11/23.
 * 每秒执行的内容
 */
public class GameTimer implements Runnable {
    private static Logger log = LoggerFactory.getLogger(GameTimer.class);
  //   private static SimpleDateFormat sdf=new SimpleDateFormat("HH-mm-ss");
    public void run() {
        TimerHandler.timeSign += 1;
        if (TimerHandler.timeSign >= 3600) {
            TimerHandler.nowTimeStamp = System.currentTimeMillis();
            TimerHandler.timeSign = 0;
        } else {
            TimerHandler.nowTimeStamp += 1000;
        }
        judgeMail();
        dailyThreeHourOperate();
        dailyOneHourOperate();
        setRank();
//        bosstiem();
//  BossService.getInstance().ResopnseBossJL();
        BossService.getInstance().CheckWorldTime();
        try{
            PlayerOnlineTimer.getInstance().PerSecondRun();
        }catch (Exception e){
            System.err.println("Damn!!!!!!!!!!!!!!!!!");
            e.printStackTrace();
        }
        LimitedTimeRewardTimer.getInstance().PerSecondRun();
        PVPTImer.getInstance().PerSecondRun();
        DatabaseBackup.getInstance().PerSecondRun();


        }

        // addRobot();  //关闭机器人
      //  weekendLuckOperate();
      //  daily12thOperate();
      //  daily18thOperate();
     //   halfHourOperate();
        /*   String time=sdf.format(new Date());//time长度固定为8 （HH-mm-ss)
            if(time.charAt(3)=='0'&&time.charAt(4)=='0'&&time.charAt(6)=='0'&&time.charAt(7)=='0'){//表示整点
               String hour=time.substring(0,2);
               int nowHour=Integer.parseInt(hour);*/


    private synchronized void addRobot(){
        try {
            TimerHandler.temptime ++;
            TimerHandler.xxtime++;
            if (TimerHandler.temptime >=60*60*12){
                log.info("<<<<<<<<<<<<<up>>>>>>>>>>>");
                TimerHandler.temptime=0;
            }
                if (TimerHandler.xxtime >=60*60*24){
                log.info("<<<<<<<<<<<<<addRobot>>>>>>>>>>>");
                Factory.addRobot();
                TimerHandler.xxtime=0;
            }

            if (TimerHandler.xxtime >= 60*60*12){
                log.info("<<<<<<<<<<<<<addRobot>>>>>>>>>>>");
                Factory.addRobot();
                TimerHandler.xxtime=0;
            }

            // if (TimerHandler.xxtime >= 60*60*12){
            //     log.info("<<<<<<<<<<<<<addRobot>>>>>>>>>>>");
            //     Factory.addRobot();
            //     TimerHandler.xxtime=0;
            // }

        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }
    private  boolean executed = false;
    public   void  bosstiem(){
        Date date1 = new Date();
        SimpleDateFormat format1 = new SimpleDateFormat("HH:mm");
        String time1 = format1.format(date1);
        if (time1.compareTo("11:58")==0||time1.compareTo("17:50")==0){
            executed=false;
        }
        if (!executed){
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("HH:mm");
            String time = format.format(date);
            if (time.compareTo("12:00")==0||time.compareTo("18:00")==0){
                System.out.println("st");
                ChannelHandlerContext ctx = null;
                for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
                    if (entry.getValue()!=null) {
                        BossData.Responsebosstime.Builder builder = BossData.Responsebosstime.newBuilder();
                        ReportManager.reportInfo(entry.getValue(), ProtoData.SToC.RESPONSEbosstime_VALUE, builder.build().toByteArray());
                        break;
                    }
                }
                executed=true;
        }

//            RoleDao roleDao = new RoleDao();
//            String uid =null;
//            List<Object> list = roleDao.queryUid();
//            for (int i = 0; i < list.size(); i++) {
//                Object o = list.get(i);
//                uid =(String)o;
//                BossData.Responsebosstime.Builder builder = BossData.Responsebosstime.newBuilder();
//                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEbosstime_VALUE, builder.build().toByteArray());
//            }

        }

    }
    private synchronized void setRank(){
        try {
            TimerHandler.oneHourRank ++;
        if (TimerHandler.oneHourRank >= 60*60*1) {
            long timeStamp = System.currentTimeMillis();
            String string = MyUtils.stampToDate(timeStamp);
            String hourStr = string.split(":")[0].split(" ")[1];
            String newStr = string.split(":")[0] + ":00:00";
            String oneStamp = MyUtils.dateToStampComplete(newStr);
            if (oneStamp != null){
                int val = (int)(timeStamp - Long.parseLong(oneStamp))/1000;
                if (val > 3500){
                    return;
                }
                TimerHandler.oneHourRank = val;
            }
            log.info("<<<<<<<<<<<<<setRank>>>>>>>>>>>");
//            IRanking iRank = RankingDao.getInstance();
//            iRank.getLvRankFromSqlToRedis();
//            iRank.getScoreRankFromSqlToRedis();
//            iRank.getPetNumRankSqlToRedis();
//            System.out.println("11111111111111111111111111111"+iRank);

            if (!hourStr.equals("00")){
              //  iRank.getDailyPkNumRankFromSqlToRedis();
            }else {
                //每日0点竞速奖励(暂时关闭)

                //  sendDailyPkReward();
                StringBuffer hql = new StringBuffer("update RoleEntity set dailypknum = 0");
                MySql.updateSomes(hql.toString());
                Redis jedis = Redis.getInstance();
                Iterator<String> iterator = jedis.keys("role:*").iterator();
                Pipeline pipeline = Redis.pipelined();
                while (iterator.hasNext()){
                    String key = iterator.next();
                    pipeline.hset(key,"dailypknum","0");
                }

                pipeline.del("rankinfo:4");
                pipeline.sync();
            }
        }
    }catch (Exception e){
        e.printStackTrace();
        log.error(e.getMessage(),e);
    }
    }
    private void sendDailyPkReward(){
        Redis jedis = Redis.getInstance();
        IFriend iFriend = FriendDao.getInstance();
        for (int i = 1 ; i <= 3 ; i++ ){
            String rankCache = jedis.hget("rankinfo:4",i+"");
            if (rankCache == null){
                break;
            }
            RankInfo rankInfo = (RankInfo)MyUtils.jsonToBean(rankCache,RankInfo.class);
            int mailId = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_RANKING, i+"","email_id"));
            String mid = MyUtils.setRandom();
            iFriend.addNewMessage(rankInfo.getUid(), mailId, "",mid, TimerHandler.nowTimeStamp);
        }
    }

    private synchronized void dailyThreeHourOperate(){
        try {
            TimerHandler.dailyThreeHour ++;
            if (TimerHandler.dailyThreeHour >= 60*60*24){
                log.info("<<<<<<<<<<<<<dailyThreeHourOperate>>>>>>>>>>>");
                TimerHandler.dailyThreeHour = 0;
                long tmp = TimerHandler.nowTimeStamp - 2*60*60*24*1000;
                StringBuffer hql = new StringBuffer("delete from PresentEntity ")
                        .append(" where type = 2 and timestamp < ").append(tmp);
                MySql.updateSomes(hql.toString());
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }

    private synchronized void dailyOneHourOperate(){
        try {
            TimerHandler.dailyOneHour ++;
            if (TimerHandler.dailyOneHour >= 60*60*24-10){
                String newStr = MyUtils.stampToDate2(TimerHandler.nowTimeStamp);
                if (!TimerHandler.todayStr.equals(newStr)){
                    TimerHandler.dailyOneHour = 0;
                    TimerHandler.todayStr = newStr;
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }

    //工具生成邮件注入redis后server在处理推送
    private synchronized void judgeMail(){
        try {
            Redis jedis = Redis.getInstance();
            IFriend iFriend = FriendDao.getInstance();
            Iterator<String> iterator = jedis.keys("mailinfo:*").iterator();
            while (iterator.hasNext())
            {
                String key = iterator.next();
                Map<String,String> mailMap = jedis.hgetAll(key);
                MailInfo mailInfo = iFriend.mapToMailInfo(mailMap);
                if (mailInfo.getStatus() == 0 && mailInfo.getPublishTime() <= TimerHandler.nowTimeStamp)
                {
                    FriendData.ReportMessage.Builder builder = FriendData.ReportMessage.newBuilder();
                    FriendData.Message.Builder messagebu = FriendData.Message.newBuilder();
                    messagebu.setMid(mailInfo.getMid());
                    messagebu.setType(mailInfo.getMailId());
                    messagebu.setContent(mailInfo.getContent());
                    messagebu.setStatus(0);
                    messagebu.setTimeStamp(mailInfo.getPublishTime()+"");
                    builder.setMessage(messagebu);
                    //自定义邮件后回调函数调用指定玩家 type = part
                    if (mailInfo.getType().equals("part"))
                    {

                        List<Object> list = new ArrayList<Object>();
                        list.add(0,key);
                        list.add(1,mailInfo);
                        list.add(2,builder.build().toByteArray());
                        CallBack callBack = new FriendCallBack(CallBackOrder.PUBLISHMAILBACK);
                        callBack.addParameterList(list);
                        iFriend.getRoleUidById(mailInfo.getRoleId(), callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
                    }
                    else 
                    {
                        for (Map.Entry<ChannelHandlerContext,String>entry: SuperServerHandler.linkMap.entrySet())
                        {

                            String roleUid = entry.getValue();
                            MessageInfo info = iFriend.addNewMessage(roleUid,mailInfo.getMailId(),mailInfo.getContent(),mailInfo.getMid(),mailInfo.getPublishTime());
                            if (info == null){
                                ReportManager.reportInfo(roleUid, ProtoData.SToC.REPORTMESSAGE_VALUE, builder.build().toByteArray());
                                //ReportManager.reportInfo(roleUid, ProtoData.SToC.REPORTNEWMAIL_VALUE, builder.build().toByteArray());
                            }
                        }
                        if(jedis.exists(key))
                        {
                            jedis.hset(key,"status","1");
                        }
                    }
                }
                else 
                {
                    long publishTime = mailInfo.getPublishTime();
                    if (publishTime+ SuperConfig.MAILEXPIRE < TimerHandler.nowTimeStamp)
                    {
                        jedis.del(key);
                        String hql = "delete from MessageEntity  where mid = '" + mailInfo.getMid() + "'";
                        MySql.updateSomes(hql);
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }

    private synchronized void weekendLuckOperate(){
        long timeStamp = System.currentTimeMillis();
        String string = MyUtils.stampToDate(timeStamp);
        String hourStr = string.split(" ")[0]+ " 08:00:00";
        if(!string.equals(hourStr)){
            return;
        }
        Redis jedis = Redis.getInstance();
        MessageEntity newmes=null;
        int oldLucky=0;
        if(jedis.hget("common:1","lucky")==null){
            int luckys = ranName.ranNum(150,350);
            Map<String,String> map = new HashMap<String, String>();
            map.put("lucky",luckys+"");
            map.put("region1","0|0");
            map.put("region2","0|0");
            map.put("region3","0|0");
            jedis.hmset("common:1",map);
        }
        switch (ranName.week()){
            case 1:
                ///// System.out.println("周1");
                oldLucky = Integer.parseInt(jedis.hget("common:1","lucky"));
                StringBuffer a=new StringBuffer("").append(oldLucky-100).append("|").append(oldLucky+100).append("");
                jedis.hset("common:1","region3",a.toString());
                jedis.hset("common:1", "lucky", ranName.ranNum(oldLucky-200,oldLucky+200)+"");
                ranName.fabuNews();
                break;
            case 3:
               // /// System.out.println("周3");
                oldLucky = Integer.parseInt(jedis.hget("common:1","lucky"));
                StringBuffer a3=new StringBuffer("").append(oldLucky-70).append("|").append(oldLucky+70).append("");
                jedis.hset("common:1","region3",a3.toString());
                jedis.hset("common:1", "lucky", ranName.ranNum(oldLucky-100,oldLucky+100)+"");
                ranName.fabuNews();
                break;
            case 5:
               // /// System.out.println("周5");
                oldLucky = Integer.parseInt(jedis.hget("common:1","lucky"));
                StringBuffer a5=new StringBuffer("").append(oldLucky-50).append("|").append(oldLucky+50).append("");
                jedis.hset("common:1","region3",a5.toString());
                jedis.hset("common:1", "lucky", ranName.ranNum(oldLucky-50,oldLucky+50)+"");
                ranName.fabuNews();
                break;
            case 6:
                StringBuffer hql=new StringBuffer("delete from MessageEntity where type =20");
                MySql.mustUpdateSomes(hql.toString());
                String lucky=jedis.hget("common:1","lucky");
                MessageEntity newmess=new MessageEntity();
                newmess.setVersion(0);
                newmess.setMid("admin");
                newmess.setType(23);
                newmess.setUid("user");
                newmess.setContent(lucky);
                newmess.setTimestamp(ranName.time());
                newmess.setStatus(0);
                MySql.mustInsert(newmess);
                break;
            case 7:
                StringBuffer hqls=new StringBuffer("delete from MessageEntity where type =23");
                MySql.mustUpdateSomes(hqls.toString());
                List<String> friends=new ArrayList<String>();
                oldLucky = Integer.parseInt(jedis.hget("common:1","lucky"));
                List<Object> relat=MySql.queryForList(" FROM RelativeshipEntity where value="+oldLucky);
                RelativeshipEntity rea;
                for(int num=0;num<relat.size();num++){
                    rea=(RelativeshipEntity)relat.get(num);
                    friends.add(rea.getRoleuid1());
                    friends.add(rea.getRoleuid2());
                }
                List<String> newlist=ranName.removeMore(friends);
                for (String peo:newlist) {
                    newmes=new MessageEntity();
                    newmes.setVersion(0);
                    newmes.setMid("admin");
                    newmes.setType(21);
                    newmes.setUid(peo);
                    newmes.setContent("");
                    newmes.setTimestamp(ranName.time());
                    newmes.setStatus(0);
                    MySql.mustInsert(newmes);
                }
                int luckys = ranName.ranNum(200,400);
                Map<String,String> map = new HashMap<String, String>();
                map.put("lucky",luckys+"");
                map.put("region1","0|0");
                map.put("region2","0|0");
                map.put("region3","0|0");
                jedis.hmset("common:1",map);
                break;
        }
    }
    private void daily12thOperate(){
        try {
            TimerHandler.daily12th++;
            if (TimerHandler.daily12th>=60*60*24){
                TimerHandler.daily12th=0;
                TimerHandler.luckItemVersion++;
                Jedis redis =Redis.getJedis(-1);
                redis.set("luckItemVersion",TimerHandler.luckItemVersion+"");
                redis.close();
                notifyUpdate(1);
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }
    private void daily18thOperate(){
        try {
            TimerHandler.daily18th++;
            if (TimerHandler.daily18th>=60*60*24){
                TimerHandler.daily18th=0;
                TimerHandler.luckItemVersion++;
                Jedis redis =Redis.getJedis(-1);
                redis.set("luckItemVersion",TimerHandler.luckItemVersion+"");
                redis.close();
                notifyUpdate(2);

            }
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }
    private void  notifyUpdate(int type){
        log.info("<<<<<<<<<<<<<刷新幸运物品>>>>>>>>>>>");
        Iterator<String> iterator =Redis.keys(SuperConfig.REDIS_EXCEL_TIMING+"*").iterator();
        Redis jedis=Redis.getInstance();
        List<Map<String,String>> list=new ArrayList();
        List<String> weightList=new ArrayList();
        while(iterator.hasNext()){
            String key=iterator.next();
            Map<String,String> luckyItemMap=jedis.hgetAll(key);
            String itemType=luckyItemMap.get("class_time");
            if(type==Integer.parseInt(itemType)){
                String rate=luckyItemMap.get("rate");
                list.add(luckyItemMap);
                weightList.add(rate);
            }
        }
        synchronized (SuperServerHandler.linkMap) {
            for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
                ChannelHandlerContext context = entry.getKey();
                String uid = entry.getValue();
                Integer index = ranName.ranGift(weightList);
                Map<String, String> map = list.get(index);
                UserData.ResponseUpdateLuckyItem.Builder builder = UserData.ResponseUpdateLuckyItem.newBuilder();
                int itemId = Integer.parseInt(map.get("ID"));
                builder.setItemId(itemId);
                StringBuffer sql = new StringBuffer("update RoleEntity set luckyItemVersion=").append(TimerHandler.luckItemVersion).append(", luckyItemId=")
                        .append(itemId).append(" where uid='").append(uid).append("'");
                MySql.updateSomes(sql.toString());
                jedis.hset("role:" + uid, "luckyItemVersion", TimerHandler.luckItemVersion + "");
                jedis.hset("role:" + uid, "luckyItemId", itemId + "");
                ReportManager.reportInfo(context, ProtoData.SToC.RESPOSEUPDATELUCKYITEM_VALUE, builder.build().toByteArray());
            }
        }
    }


    private synchronized void halfHourOperate(){
        try {
            TimerHandler.halfHour ++;
            if (TimerHandler.halfHour >=60*3){//目前三分钟刷新
                log.info("<<<<<<<<<<<<<halfHourOperate>>>>>>>>>>>");
                TimerHandler.halfHour = 0;
                Iterator<String> it=Redis.keys(SuperConfig.REDIS_EXCEL_TICKRT+"*").iterator();
                String goodsInfoKey="goodsInfo:";
                Redis jedis=Redis.getInstance();
                while(it.hasNext()){
                    String key=it.next();
                    List<String> weightList=new ArrayList();
                    Map<String,String> goodsConfig=jedis.hgetAll(key);
                    String priceProbability1=goodsConfig.get("price_probability1");
                    weightList.add(priceProbability1);
                    String priceProbability2=goodsConfig.get("price_probability2");
                    weightList.add(priceProbability2);
                    String priceProbability3=goodsConfig.get("price_probability3");
                    weightList.add(priceProbability3);
                    String priceProbability4=goodsConfig.get("price_probability4");
                    weightList.add(priceProbability4);
                    String priceProbability5=goodsConfig.get("price_probability5");
                    weightList.add(priceProbability5);
                    Integer index=ranName.ranGift(weightList);
                   String priceSection=goodsConfig.get("price_section"+(index+1));
                   int down=Integer.parseInt(priceSection.split(",")[0]);
                    int up=Integer.parseInt(priceSection.split(",")[1]);
                    int price=((int)(Math.random()*(up-down+1)))+down;
                    String id=goodsConfig.get("id");
                    Map<String,String> goodsInfo=jedis.hgetAll(goodsInfoKey+id);
                    if(goodsInfo==null||goodsInfo.size()==0){
                        goodsInfo=new HashMap<String,String>();
                        goodsInfo.put("id",id);
                        goodsInfo.put("nowPrice",price+"");
                        goodsInfo.put("fluctuate","1");
                        goodsInfo.put("scale","0");
                    }else{
                       int lastPrice =Integer.parseInt( goodsInfo.get("nowPrice"));
                       int fluctuate=1;
                       int scale=0;
                       if(lastPrice==price){
                           fluctuate=1;
                       }else if(lastPrice>price){
                           fluctuate=0;
                           scale=(int)((lastPrice-price)*100/lastPrice);
                       }else if(lastPrice<price){
                           fluctuate=2;
                           scale=(int)((price-lastPrice)*100/lastPrice);
                       }
                   //   /// System.out.println("last"+lastPrice+"~now"+price+"比率"+scale);
                        goodsInfo.put("id",id);
                        goodsInfo.put("nowPrice",price+"");
                        goodsInfo.put("fluctuate",fluctuate+"");
                        goodsInfo.put("scale",scale+"");
                    }
                    jedis.hmset(goodsInfoKey+id,goodsInfo);
                }
            //
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error(e.getMessage(),e);
        }
    }
}
