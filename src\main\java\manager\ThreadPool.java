package manager;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by nara on 2019/3/20.
 */
public class ThreadPool {
    private final static int maxSize = 100;
    private static int nowSize = 0;
    private static List<Thread> threadList = new ArrayList<Thread>();

    public synchronized static Thread newThread(Runnable runnable){
        Thread thread = null;
        if (runnable != null){
            if (threadList.size() >= 0){
                thread = threadList.remove(threadList.size()-1);
                //???
                nowSize++;
            }else if (nowSize <= maxSize){
                thread = new Thread(runnable);
                nowSize++;
            }
        }
        return thread;
    }

    public synchronized static void remove(Thread thread){
        if (thread != null){
            if (threadList.size() < maxSize){
                threadList.add(thread);
            }
            nowSize--;
        }
    }
}
