package common;

import model.CommonInfo;

import java.util.List;

@ExcelConfigObject(key = "DisPatch")
public class DispatchConfig {
    @ExcelColumn(name = "ID")
    private int id;
    @ExcelColumn(name = "NumberOfPeople")
    private int peopleLimite;
    @ExcelColumn(name = "condition_1", isCommonInfo = true, isList = true)
    private List<CommonInfo> rules;
    @ExcelColumn(name = "time")
    private int finishTime;
    @ExcelColumn(name = "fail_item", isCommonInfo = true)
    private CommonInfo basicAward;
    @ExcelColumn(name = "extra_item_id", isList = true)
    private List<Integer> extraAWardId;
    @ExcelColumn(name = "extra_item_range", isList = true, isCommonInfo = true)
    private List<CommonInfo> extraAwardNumsRange;
    @ExcelColumn(name = "end_time")
    private int loseRate;
    @ExcelColumn(name = "get_item", isCommonInfo = true, isList = true)
    private List<CommonInfo> normalAWard;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPeopleLimite() {
        return peopleLimite;
    }

    public void setPeopleLimite(int peopleLimite) {
        this.peopleLimite = peopleLimite;
    }

    public List<CommonInfo> getRules() {
        return rules;
    }

    public void setRules(List<CommonInfo> rules) {
        this.rules = rules;
    }

    public int getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(int finishTime) {
        this.finishTime = finishTime;
    }

    public CommonInfo getBasicAward() {
        return basicAward;
    }

    public void setBasicAward(CommonInfo basicAward) {
        this.basicAward = basicAward;
    }

    public List<Integer> getExtraAWardId() {
        return extraAWardId;
    }

    public void setExtraAWardId(List<Integer> extraAWardId) {
        this.extraAWardId = extraAWardId;
    }

    public List<CommonInfo> getExtraAwardNumsRange() {
        return extraAwardNumsRange;
    }

    public void setExtraAwardNumsRange(List<CommonInfo> extraAwardNumsRange) {
        this.extraAwardNumsRange = extraAwardNumsRange;
    }

    public int getLoseRate() {
        return loseRate;
    }

    public void setLoseRate(int loseRate) {
        this.loseRate = loseRate;
    }

    public List<CommonInfo> getNormalAWard() {
        return normalAWard;
    }

    public void setNormalAWard(List<CommonInfo> normalAWard) {
        this.normalAWard = normalAWard;
    }


    @Override
    public String toString() {
        return "DispatchConfig{" +
                "id=" + id +
                ", peopleLimite=" + peopleLimite +
                ", rules=" + rules +
                ", finishTime=" + finishTime +
                ", basicAward=" + basicAward +
                ", extraAWardId=" + extraAWardId +
                ", extraAwardNumsRange=" + extraAwardNumsRange +
                ", loseRate=" + loseRate +
                ", normalAWard=" + normalAWard +
                '}';
    }
}
