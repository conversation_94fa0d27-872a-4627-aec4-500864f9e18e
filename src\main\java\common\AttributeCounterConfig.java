package common;

@ExcelConfigObject(key = "attributeCounter")
public class AttributeCounterConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "huo")
    private int fire;
    @ExcelColumn(name = "shui")
    private int water;
    @ExcelColumn(name = "tu")
    private int plant;
    @ExcelColumn(name = "mu")
    private int wooden;
    @ExcelColumn(name = "guang")
    private int light;
    @ExcelColumn(name = "an")
    private int dark;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getFire() {
        return fire;
    }

    public void setFire(int fire) {
        this.fire = fire;
    }

    public int getWater() {
        return water;
    }

    public void setWater(int water) {
        this.water = water;
    }

    public int getPlant() {
        return plant;
    }

    public void setPlant(int plant) {
        this.plant = plant;
    }

    public int getWooden() {
        return wooden;
    }

    public void setWooden(int wooden) {
        this.wooden = wooden;
    }

    public int getLight() {
        return light;
    }

    public void setLight(int light) {
        this.light = light;
    }

    public int getDark() {
        return dark;
    }

    public void setDark(int dark) {
        this.dark = dark;
    }

    @Override
    public String toString() {
        return "AttributeCounterConfig{" +
                "id=" + id +
                ", fire=" + fire +
                ", water=" + water +
                ", plant=" + plant +
                ", wooden=" + wooden +
                ", light=" + light +
                ", dark=" + dark +
                '}';
    }
}
