package model;

import entities.ItemEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by nara on 2017/12/19.
 */
public class BigBallInfo {
    private int bigBallId;
    private PointInfo point;
    private boolean isSpeed;
    private int integral;
    private int skillId;
    private boolean isBuffBall;
    private ItemEntity item;

    public ItemEntity getItem() {
        return item;
    }

    public void setItem(ItemEntity item) {
        this.item = item;
    }


    //    private int speed;
//    private List<Integer> smallIdList;
//    private List<Integer> smallPosList;

    public BigBallInfo() {
        point = new PointInfo();
//        smallIdList = new ArrayList<Integer>();
//        smallPosList = new ArrayList<Integer>();
        integral = 0;
        skillId = 0;
        isBuffBall = false;
    }

    public boolean getIsBuffBall() {
        return isBuffBall;
    }

    public void setIsBuffBall(boolean isBuffBall) {
        this.isBuffBall = isBuffBall;
    }

    public int getSkillId() {
        return skillId;
    }

    public void setSkillId(int skillId) {
        this.skillId = skillId;
    }

    public int getIntegral() {
        return integral;
    }

    public void setIntegral(int integral) {
        this.integral = integral;
    }

    public boolean isSpeed() {
        return isSpeed;
    }

    public void setIsSpeed(boolean isSpeed) {
        this.isSpeed = isSpeed;


    }
    //    public List<Integer> getSmallPosList() {
//        return smallPosList;
//    }
//
//    public void setSmallPosList(List<Integer> smallPosList) {
//        this.smallPosList = smallPosList;
//    }
//
//    public List<Integer> getSmallIdList() {
//        return smallIdList;
//    }
//
//    public void setSmallIdList(List<Integer> smallIdList) {
//        this.smallIdList = smallIdList;
//    }
//
//    public int getSpeed() {
//        return speed;
//    }
//
//    public void setSpeed(int speed) {
//        this.speed = speed;
//    }

    public PointInfo getPoint() {
        return point;
    }

    public void setPoint(PointInfo point) {
        this.point = point;
    }

    public int getBigBallId() {
        return bigBallId;
    }

    public void setBigBallId(int bigBallId) {
        this.bigBallId = bigBallId;
    }
}
