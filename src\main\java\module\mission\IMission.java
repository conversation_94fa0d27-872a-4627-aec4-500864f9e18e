package module.mission;

import entities.HeadBallEntity;
import io.netty.channel.ChannelHandlerContext;
import model.MissionInfo;
import protocol.ItemData;
import protocol.MissionData;

import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2017/11/30.
 */
public interface IMission {
    int judgeMission(int missionId,String uid);
    MissionData.ResponseBeginMission.Builder getMissionMap(int missionId,String uid);
    MissionData.ResponseBeginGold.Builder createList(Integer missionid,String uid,Integer type);
    MissionData.ResponseCountMission.Builder countMission(int missionId,String uid,boolean isTimeOut,List<Integer> ballIdList);
    MissionInfo getMissionInfo(Map<String,String> missionMap,int type);
    int getMissionMax(String uid);
    int getEndlessMax(String uid);
    MissionData.ResponseBeginEndless.Builder getEndlessMap(int type,int missionId,String uid);
    MissionData.ResponseCountEndless.Builder countEndless(String uid,int endlessId,int totalTime,int totalIntegral,int maxCombo);

    boolean isFirst(String uid, int builder);

    MissionData.ResponseCountHeadBall.Builder countHeadBallMission(HeadBallEntity headBallEntity, boolean isSuccess, int exp, List<ItemData.Item> itemList);
    MissionData.ResponseCountHeadBall.Builder countJumpHeadBallMission(int missionId, String uid, int jumpnum, int starNum, Boolean isSuccess);
    MissionData.ResponseCountGold.Builder countGold(List<ItemData.Item> itemList,String uid);
    MissionData.ResponseCountDown.Builder countDown(MissionData.RequestCountDown request,String uid);
    MissionData.ResposeSourceMachine.Builder sourceMachine(ChannelHandlerContext ctx, String uid);
    MissionData.ResposeMakeItem.Builder makeItem(int id,int nums,String uid);
    MissionData.ResposeProduct.Builder getProduct(int id,String uid);
    public void initSourceMachine(String uid);
    MissionData.ResposeStartGameCopy.Builder startGameCopy(String uid,int id);
    MissionData.ResposeCountGameCopy.Builder countGameCopy(String uid,int id,int integral);
    MissionData.ResponseCountStack.Builder  countStack(MissionData.RequestCountStack request,String uid);
}
