package model;

/**
 * Created by nara on 2018/1/11.
 */
public class TaskInfo {
    private int taskId;
    private long taskNowNum;
    private int status;
    private String timeStamp;

    public String getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(String timeStamp) {
        this.timeStamp = timeStamp;
    }

    public TaskInfo() {
        taskNowNum = 0;
        status = 0;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getTaskNowNum() {
        return taskNowNum;
    }

    public void setTaskNowNum(long taskNowNum) {
        this.taskNowNum = taskNowNum;
    }

    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    @Override
    public String toString() {
        return "TaskInfo{" +
                "taskId=" + taskId +
                ", taskNowNum=" + taskNowNum +
                ", status=" + status +
                ", timeStamp='" + timeStamp + '\'' +
                '}';
    }
}
