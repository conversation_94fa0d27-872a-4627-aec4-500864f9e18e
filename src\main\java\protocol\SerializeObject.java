// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: serverSerializeObject.proto

package protocol;

public final class SerializeObject {
  private SerializeObject() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface PetBreedInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 petBreedStatus = 1;
    /**
     * <code>required int32 petBreedStatus = 1;</code>
     *
     * <pre>
     *1代表无孵化 2代表在孵化
     * </pre>
     */
    boolean hasPetBreedStatus();
    /**
     * <code>required int32 petBreedStatus = 1;</code>
     *
     * <pre>
     *1代表无孵化 2代表在孵化
     * </pre>
     */
    int getPetBreedStatus();

    // repeated .protocol.PetBreedCD breedCDPet = 2;
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    java.util.List<protocol.SerializeObject.PetBreedCD> 
        getBreedCDPetList();
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    protocol.SerializeObject.PetBreedCD getBreedCDPet(int index);
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    int getBreedCDPetCount();
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    java.util.List<? extends protocol.SerializeObject.PetBreedCDOrBuilder> 
        getBreedCDPetOrBuilderList();
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    protocol.SerializeObject.PetBreedCDOrBuilder getBreedCDPetOrBuilder(
        int index);

    // repeated int32 breedPetId = 3;
    /**
     * <code>repeated int32 breedPetId = 3;</code>
     *
     * <pre>
     * 正在孵化的宠物
     * </pre>
     */
    java.util.List<java.lang.Integer> getBreedPetIdList();
    /**
     * <code>repeated int32 breedPetId = 3;</code>
     *
     * <pre>
     * 正在孵化的宠物
     * </pre>
     */
    int getBreedPetIdCount();
    /**
     * <code>repeated int32 breedPetId = 3;</code>
     *
     * <pre>
     * 正在孵化的宠物
     * </pre>
     */
    int getBreedPetId(int index);

    // optional int64 breedfinishTime = 4;
    /**
     * <code>optional int64 breedfinishTime = 4;</code>
     *
     * <pre>
     * 孵化完成时间
     * </pre>
     */
    boolean hasBreedfinishTime();
    /**
     * <code>optional int64 breedfinishTime = 4;</code>
     *
     * <pre>
     * 孵化完成时间
     * </pre>
     */
    long getBreedfinishTime();

    // optional .protocol.Item useItem = 5;
    /**
     * <code>optional .protocol.Item useItem = 5;</code>
     *
     * <pre>
     * 额外使用的物品
     * </pre>
     */
    boolean hasUseItem();
    /**
     * <code>optional .protocol.Item useItem = 5;</code>
     *
     * <pre>
     * 额外使用的物品
     * </pre>
     */
    protocol.ItemData.Item getUseItem();
    /**
     * <code>optional .protocol.Item useItem = 5;</code>
     *
     * <pre>
     * 额外使用的物品
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getUseItemOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.PetBreedInfo}
   */
  public static final class PetBreedInfo extends
      com.google.protobuf.GeneratedMessage
      implements PetBreedInfoOrBuilder {
    // Use PetBreedInfo.newBuilder() to construct.
    private PetBreedInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PetBreedInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PetBreedInfo defaultInstance;
    public static PetBreedInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PetBreedInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PetBreedInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petBreedStatus_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                breedCDPet_ = new java.util.ArrayList<protocol.SerializeObject.PetBreedCD>();
                mutable_bitField0_ |= 0x00000002;
              }
              breedCDPet_.add(input.readMessage(protocol.SerializeObject.PetBreedCD.PARSER, extensionRegistry));
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                breedPetId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              breedPetId_.add(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                breedPetId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                breedPetId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 32: {
              bitField0_ |= 0x00000002;
              breedfinishTime_ = input.readInt64();
              break;
            }
            case 42: {
              protocol.ItemData.Item.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = useItem_.toBuilder();
              }
              useItem_ = input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(useItem_);
                useItem_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          breedCDPet_ = java.util.Collections.unmodifiableList(breedCDPet_);
        }
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          breedPetId_ = java.util.Collections.unmodifiableList(breedPetId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SerializeObject.internal_static_protocol_PetBreedInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SerializeObject.internal_static_protocol_PetBreedInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SerializeObject.PetBreedInfo.class, protocol.SerializeObject.PetBreedInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PetBreedInfo> PARSER =
        new com.google.protobuf.AbstractParser<PetBreedInfo>() {
      public PetBreedInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PetBreedInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PetBreedInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 petBreedStatus = 1;
    public static final int PETBREEDSTATUS_FIELD_NUMBER = 1;
    private int petBreedStatus_;
    /**
     * <code>required int32 petBreedStatus = 1;</code>
     *
     * <pre>
     *1代表无孵化 2代表在孵化
     * </pre>
     */
    public boolean hasPetBreedStatus() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 petBreedStatus = 1;</code>
     *
     * <pre>
     *1代表无孵化 2代表在孵化
     * </pre>
     */
    public int getPetBreedStatus() {
      return petBreedStatus_;
    }

    // repeated .protocol.PetBreedCD breedCDPet = 2;
    public static final int BREEDCDPET_FIELD_NUMBER = 2;
    private java.util.List<protocol.SerializeObject.PetBreedCD> breedCDPet_;
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    public java.util.List<protocol.SerializeObject.PetBreedCD> getBreedCDPetList() {
      return breedCDPet_;
    }
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    public java.util.List<? extends protocol.SerializeObject.PetBreedCDOrBuilder> 
        getBreedCDPetOrBuilderList() {
      return breedCDPet_;
    }
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    public int getBreedCDPetCount() {
      return breedCDPet_.size();
    }
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    public protocol.SerializeObject.PetBreedCD getBreedCDPet(int index) {
      return breedCDPet_.get(index);
    }
    /**
     * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
     *
     * <pre>
     *存在孵化cd的宠物链表
     * </pre>
     */
    public protocol.SerializeObject.PetBreedCDOrBuilder getBreedCDPetOrBuilder(
        int index) {
      return breedCDPet_.get(index);
    }

    // repeated int32 breedPetId = 3;
    public static final int BREEDPETID_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Integer> breedPetId_;
    /**
     * <code>repeated int32 breedPetId = 3;</code>
     *
     * <pre>
     * 正在孵化的宠物
     * </pre>
     */
    public java.util.List<java.lang.Integer>
        getBreedPetIdList() {
      return breedPetId_;
    }
    /**
     * <code>repeated int32 breedPetId = 3;</code>
     *
     * <pre>
     * 正在孵化的宠物
     * </pre>
     */
    public int getBreedPetIdCount() {
      return breedPetId_.size();
    }
    /**
     * <code>repeated int32 breedPetId = 3;</code>
     *
     * <pre>
     * 正在孵化的宠物
     * </pre>
     */
    public int getBreedPetId(int index) {
      return breedPetId_.get(index);
    }

    // optional int64 breedfinishTime = 4;
    public static final int BREEDFINISHTIME_FIELD_NUMBER = 4;
    private long breedfinishTime_;
    /**
     * <code>optional int64 breedfinishTime = 4;</code>
     *
     * <pre>
     * 孵化完成时间
     * </pre>
     */
    public boolean hasBreedfinishTime() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int64 breedfinishTime = 4;</code>
     *
     * <pre>
     * 孵化完成时间
     * </pre>
     */
    public long getBreedfinishTime() {
      return breedfinishTime_;
    }

    // optional .protocol.Item useItem = 5;
    public static final int USEITEM_FIELD_NUMBER = 5;
    private protocol.ItemData.Item useItem_;
    /**
     * <code>optional .protocol.Item useItem = 5;</code>
     *
     * <pre>
     * 额外使用的物品
     * </pre>
     */
    public boolean hasUseItem() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional .protocol.Item useItem = 5;</code>
     *
     * <pre>
     * 额外使用的物品
     * </pre>
     */
    public protocol.ItemData.Item getUseItem() {
      return useItem_;
    }
    /**
     * <code>optional .protocol.Item useItem = 5;</code>
     *
     * <pre>
     * 额外使用的物品
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getUseItemOrBuilder() {
      return useItem_;
    }

    private void initFields() {
      petBreedStatus_ = 0;
      breedCDPet_ = java.util.Collections.emptyList();
      breedPetId_ = java.util.Collections.emptyList();
      breedfinishTime_ = 0L;
      useItem_ = protocol.ItemData.Item.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetBreedStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getBreedCDPetCount(); i++) {
        if (!getBreedCDPet(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasUseItem()) {
        if (!getUseItem().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petBreedStatus_);
      }
      for (int i = 0; i < breedCDPet_.size(); i++) {
        output.writeMessage(2, breedCDPet_.get(i));
      }
      for (int i = 0; i < breedPetId_.size(); i++) {
        output.writeInt32(3, breedPetId_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(4, breedfinishTime_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(5, useItem_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petBreedStatus_);
      }
      for (int i = 0; i < breedCDPet_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, breedCDPet_.get(i));
      }
      {
        int dataSize = 0;
        for (int i = 0; i < breedPetId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(breedPetId_.get(i));
        }
        size += dataSize;
        size += 1 * getBreedPetIdList().size();
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, breedfinishTime_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, useItem_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SerializeObject.PetBreedInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SerializeObject.PetBreedInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.PetBreedInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SerializeObject.PetBreedInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PetBreedInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SerializeObject.PetBreedInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SerializeObject.internal_static_protocol_PetBreedInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SerializeObject.internal_static_protocol_PetBreedInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SerializeObject.PetBreedInfo.class, protocol.SerializeObject.PetBreedInfo.Builder.class);
      }

      // Construct using protocol.SerializeObject.PetBreedInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getBreedCDPetFieldBuilder();
          getUseItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petBreedStatus_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (breedCDPetBuilder_ == null) {
          breedCDPet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          breedCDPetBuilder_.clear();
        }
        breedPetId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        breedfinishTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        if (useItemBuilder_ == null) {
          useItem_ = protocol.ItemData.Item.getDefaultInstance();
        } else {
          useItemBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SerializeObject.internal_static_protocol_PetBreedInfo_descriptor;
      }

      public protocol.SerializeObject.PetBreedInfo getDefaultInstanceForType() {
        return protocol.SerializeObject.PetBreedInfo.getDefaultInstance();
      }

      public protocol.SerializeObject.PetBreedInfo build() {
        protocol.SerializeObject.PetBreedInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SerializeObject.PetBreedInfo buildPartial() {
        protocol.SerializeObject.PetBreedInfo result = new protocol.SerializeObject.PetBreedInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petBreedStatus_ = petBreedStatus_;
        if (breedCDPetBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            breedCDPet_ = java.util.Collections.unmodifiableList(breedCDPet_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.breedCDPet_ = breedCDPet_;
        } else {
          result.breedCDPet_ = breedCDPetBuilder_.build();
        }
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          breedPetId_ = java.util.Collections.unmodifiableList(breedPetId_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.breedPetId_ = breedPetId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000002;
        }
        result.breedfinishTime_ = breedfinishTime_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000004;
        }
        if (useItemBuilder_ == null) {
          result.useItem_ = useItem_;
        } else {
          result.useItem_ = useItemBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SerializeObject.PetBreedInfo) {
          return mergeFrom((protocol.SerializeObject.PetBreedInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SerializeObject.PetBreedInfo other) {
        if (other == protocol.SerializeObject.PetBreedInfo.getDefaultInstance()) return this;
        if (other.hasPetBreedStatus()) {
          setPetBreedStatus(other.getPetBreedStatus());
        }
        if (breedCDPetBuilder_ == null) {
          if (!other.breedCDPet_.isEmpty()) {
            if (breedCDPet_.isEmpty()) {
              breedCDPet_ = other.breedCDPet_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureBreedCDPetIsMutable();
              breedCDPet_.addAll(other.breedCDPet_);
            }
            onChanged();
          }
        } else {
          if (!other.breedCDPet_.isEmpty()) {
            if (breedCDPetBuilder_.isEmpty()) {
              breedCDPetBuilder_.dispose();
              breedCDPetBuilder_ = null;
              breedCDPet_ = other.breedCDPet_;
              bitField0_ = (bitField0_ & ~0x00000002);
              breedCDPetBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getBreedCDPetFieldBuilder() : null;
            } else {
              breedCDPetBuilder_.addAllMessages(other.breedCDPet_);
            }
          }
        }
        if (!other.breedPetId_.isEmpty()) {
          if (breedPetId_.isEmpty()) {
            breedPetId_ = other.breedPetId_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureBreedPetIdIsMutable();
            breedPetId_.addAll(other.breedPetId_);
          }
          onChanged();
        }
        if (other.hasBreedfinishTime()) {
          setBreedfinishTime(other.getBreedfinishTime());
        }
        if (other.hasUseItem()) {
          mergeUseItem(other.getUseItem());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetBreedStatus()) {
          
          return false;
        }
        for (int i = 0; i < getBreedCDPetCount(); i++) {
          if (!getBreedCDPet(i).isInitialized()) {
            
            return false;
          }
        }
        if (hasUseItem()) {
          if (!getUseItem().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SerializeObject.PetBreedInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SerializeObject.PetBreedInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 petBreedStatus = 1;
      private int petBreedStatus_ ;
      /**
       * <code>required int32 petBreedStatus = 1;</code>
       *
       * <pre>
       *1代表无孵化 2代表在孵化
       * </pre>
       */
      public boolean hasPetBreedStatus() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 petBreedStatus = 1;</code>
       *
       * <pre>
       *1代表无孵化 2代表在孵化
       * </pre>
       */
      public int getPetBreedStatus() {
        return petBreedStatus_;
      }
      /**
       * <code>required int32 petBreedStatus = 1;</code>
       *
       * <pre>
       *1代表无孵化 2代表在孵化
       * </pre>
       */
      public Builder setPetBreedStatus(int value) {
        bitField0_ |= 0x00000001;
        petBreedStatus_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 petBreedStatus = 1;</code>
       *
       * <pre>
       *1代表无孵化 2代表在孵化
       * </pre>
       */
      public Builder clearPetBreedStatus() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petBreedStatus_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.PetBreedCD breedCDPet = 2;
      private java.util.List<protocol.SerializeObject.PetBreedCD> breedCDPet_ =
        java.util.Collections.emptyList();
      private void ensureBreedCDPetIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          breedCDPet_ = new java.util.ArrayList<protocol.SerializeObject.PetBreedCD>(breedCDPet_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.SerializeObject.PetBreedCD, protocol.SerializeObject.PetBreedCD.Builder, protocol.SerializeObject.PetBreedCDOrBuilder> breedCDPetBuilder_;

      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public java.util.List<protocol.SerializeObject.PetBreedCD> getBreedCDPetList() {
        if (breedCDPetBuilder_ == null) {
          return java.util.Collections.unmodifiableList(breedCDPet_);
        } else {
          return breedCDPetBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public int getBreedCDPetCount() {
        if (breedCDPetBuilder_ == null) {
          return breedCDPet_.size();
        } else {
          return breedCDPetBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public protocol.SerializeObject.PetBreedCD getBreedCDPet(int index) {
        if (breedCDPetBuilder_ == null) {
          return breedCDPet_.get(index);
        } else {
          return breedCDPetBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder setBreedCDPet(
          int index, protocol.SerializeObject.PetBreedCD value) {
        if (breedCDPetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBreedCDPetIsMutable();
          breedCDPet_.set(index, value);
          onChanged();
        } else {
          breedCDPetBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder setBreedCDPet(
          int index, protocol.SerializeObject.PetBreedCD.Builder builderForValue) {
        if (breedCDPetBuilder_ == null) {
          ensureBreedCDPetIsMutable();
          breedCDPet_.set(index, builderForValue.build());
          onChanged();
        } else {
          breedCDPetBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder addBreedCDPet(protocol.SerializeObject.PetBreedCD value) {
        if (breedCDPetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBreedCDPetIsMutable();
          breedCDPet_.add(value);
          onChanged();
        } else {
          breedCDPetBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder addBreedCDPet(
          int index, protocol.SerializeObject.PetBreedCD value) {
        if (breedCDPetBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBreedCDPetIsMutable();
          breedCDPet_.add(index, value);
          onChanged();
        } else {
          breedCDPetBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder addBreedCDPet(
          protocol.SerializeObject.PetBreedCD.Builder builderForValue) {
        if (breedCDPetBuilder_ == null) {
          ensureBreedCDPetIsMutable();
          breedCDPet_.add(builderForValue.build());
          onChanged();
        } else {
          breedCDPetBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder addBreedCDPet(
          int index, protocol.SerializeObject.PetBreedCD.Builder builderForValue) {
        if (breedCDPetBuilder_ == null) {
          ensureBreedCDPetIsMutable();
          breedCDPet_.add(index, builderForValue.build());
          onChanged();
        } else {
          breedCDPetBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder addAllBreedCDPet(
          java.lang.Iterable<? extends protocol.SerializeObject.PetBreedCD> values) {
        if (breedCDPetBuilder_ == null) {
          ensureBreedCDPetIsMutable();
          super.addAll(values, breedCDPet_);
          onChanged();
        } else {
          breedCDPetBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder clearBreedCDPet() {
        if (breedCDPetBuilder_ == null) {
          breedCDPet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          breedCDPetBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public Builder removeBreedCDPet(int index) {
        if (breedCDPetBuilder_ == null) {
          ensureBreedCDPetIsMutable();
          breedCDPet_.remove(index);
          onChanged();
        } else {
          breedCDPetBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public protocol.SerializeObject.PetBreedCD.Builder getBreedCDPetBuilder(
          int index) {
        return getBreedCDPetFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public protocol.SerializeObject.PetBreedCDOrBuilder getBreedCDPetOrBuilder(
          int index) {
        if (breedCDPetBuilder_ == null) {
          return breedCDPet_.get(index);  } else {
          return breedCDPetBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public java.util.List<? extends protocol.SerializeObject.PetBreedCDOrBuilder> 
           getBreedCDPetOrBuilderList() {
        if (breedCDPetBuilder_ != null) {
          return breedCDPetBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(breedCDPet_);
        }
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public protocol.SerializeObject.PetBreedCD.Builder addBreedCDPetBuilder() {
        return getBreedCDPetFieldBuilder().addBuilder(
            protocol.SerializeObject.PetBreedCD.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public protocol.SerializeObject.PetBreedCD.Builder addBreedCDPetBuilder(
          int index) {
        return getBreedCDPetFieldBuilder().addBuilder(
            index, protocol.SerializeObject.PetBreedCD.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PetBreedCD breedCDPet = 2;</code>
       *
       * <pre>
       *存在孵化cd的宠物链表
       * </pre>
       */
      public java.util.List<protocol.SerializeObject.PetBreedCD.Builder> 
           getBreedCDPetBuilderList() {
        return getBreedCDPetFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.SerializeObject.PetBreedCD, protocol.SerializeObject.PetBreedCD.Builder, protocol.SerializeObject.PetBreedCDOrBuilder> 
          getBreedCDPetFieldBuilder() {
        if (breedCDPetBuilder_ == null) {
          breedCDPetBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.SerializeObject.PetBreedCD, protocol.SerializeObject.PetBreedCD.Builder, protocol.SerializeObject.PetBreedCDOrBuilder>(
                  breedCDPet_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          breedCDPet_ = null;
        }
        return breedCDPetBuilder_;
      }

      // repeated int32 breedPetId = 3;
      private java.util.List<java.lang.Integer> breedPetId_ = java.util.Collections.emptyList();
      private void ensureBreedPetIdIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          breedPetId_ = new java.util.ArrayList<java.lang.Integer>(breedPetId_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public java.util.List<java.lang.Integer>
          getBreedPetIdList() {
        return java.util.Collections.unmodifiableList(breedPetId_);
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public int getBreedPetIdCount() {
        return breedPetId_.size();
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public int getBreedPetId(int index) {
        return breedPetId_.get(index);
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public Builder setBreedPetId(
          int index, int value) {
        ensureBreedPetIdIsMutable();
        breedPetId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public Builder addBreedPetId(int value) {
        ensureBreedPetIdIsMutable();
        breedPetId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public Builder addAllBreedPetId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureBreedPetIdIsMutable();
        super.addAll(values, breedPetId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 breedPetId = 3;</code>
       *
       * <pre>
       * 正在孵化的宠物
       * </pre>
       */
      public Builder clearBreedPetId() {
        breedPetId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      // optional int64 breedfinishTime = 4;
      private long breedfinishTime_ ;
      /**
       * <code>optional int64 breedfinishTime = 4;</code>
       *
       * <pre>
       * 孵化完成时间
       * </pre>
       */
      public boolean hasBreedfinishTime() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int64 breedfinishTime = 4;</code>
       *
       * <pre>
       * 孵化完成时间
       * </pre>
       */
      public long getBreedfinishTime() {
        return breedfinishTime_;
      }
      /**
       * <code>optional int64 breedfinishTime = 4;</code>
       *
       * <pre>
       * 孵化完成时间
       * </pre>
       */
      public Builder setBreedfinishTime(long value) {
        bitField0_ |= 0x00000008;
        breedfinishTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 breedfinishTime = 4;</code>
       *
       * <pre>
       * 孵化完成时间
       * </pre>
       */
      public Builder clearBreedfinishTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        breedfinishTime_ = 0L;
        onChanged();
        return this;
      }

      // optional .protocol.Item useItem = 5;
      private protocol.ItemData.Item useItem_ = protocol.ItemData.Item.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> useItemBuilder_;
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public boolean hasUseItem() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public protocol.ItemData.Item getUseItem() {
        if (useItemBuilder_ == null) {
          return useItem_;
        } else {
          return useItemBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public Builder setUseItem(protocol.ItemData.Item value) {
        if (useItemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          useItem_ = value;
          onChanged();
        } else {
          useItemBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public Builder setUseItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (useItemBuilder_ == null) {
          useItem_ = builderForValue.build();
          onChanged();
        } else {
          useItemBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public Builder mergeUseItem(protocol.ItemData.Item value) {
        if (useItemBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010) &&
              useItem_ != protocol.ItemData.Item.getDefaultInstance()) {
            useItem_ =
              protocol.ItemData.Item.newBuilder(useItem_).mergeFrom(value).buildPartial();
          } else {
            useItem_ = value;
          }
          onChanged();
        } else {
          useItemBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public Builder clearUseItem() {
        if (useItemBuilder_ == null) {
          useItem_ = protocol.ItemData.Item.getDefaultInstance();
          onChanged();
        } else {
          useItemBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder getUseItemBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getUseItemFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getUseItemOrBuilder() {
        if (useItemBuilder_ != null) {
          return useItemBuilder_.getMessageOrBuilder();
        } else {
          return useItem_;
        }
      }
      /**
       * <code>optional .protocol.Item useItem = 5;</code>
       *
       * <pre>
       * 额外使用的物品
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getUseItemFieldBuilder() {
        if (useItemBuilder_ == null) {
          useItemBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  useItem_,
                  getParentForChildren(),
                  isClean());
          useItem_ = null;
        }
        return useItemBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PetBreedInfo)
    }

    static {
      defaultInstance = new PetBreedInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PetBreedInfo)
  }

  public interface PetBreedCDOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 petId = 1;
    /**
     * <code>required int32 petId = 1;</code>
     */
    boolean hasPetId();
    /**
     * <code>required int32 petId = 1;</code>
     */
    int getPetId();

    // required int64 time = 2;
    /**
     * <code>required int64 time = 2;</code>
     */
    boolean hasTime();
    /**
     * <code>required int64 time = 2;</code>
     */
    long getTime();
  }
  /**
   * Protobuf type {@code protocol.PetBreedCD}
   */
  public static final class PetBreedCD extends
      com.google.protobuf.GeneratedMessage
      implements PetBreedCDOrBuilder {
    // Use PetBreedCD.newBuilder() to construct.
    private PetBreedCD(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PetBreedCD(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PetBreedCD defaultInstance;
    public static PetBreedCD getDefaultInstance() {
      return defaultInstance;
    }

    public PetBreedCD getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PetBreedCD(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              time_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SerializeObject.internal_static_protocol_PetBreedCD_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SerializeObject.internal_static_protocol_PetBreedCD_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SerializeObject.PetBreedCD.class, protocol.SerializeObject.PetBreedCD.Builder.class);
    }

    public static com.google.protobuf.Parser<PetBreedCD> PARSER =
        new com.google.protobuf.AbstractParser<PetBreedCD>() {
      public PetBreedCD parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PetBreedCD(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PetBreedCD> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 petId = 1;
    public static final int PETID_FIELD_NUMBER = 1;
    private int petId_;
    /**
     * <code>required int32 petId = 1;</code>
     */
    public boolean hasPetId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 petId = 1;</code>
     */
    public int getPetId() {
      return petId_;
    }

    // required int64 time = 2;
    public static final int TIME_FIELD_NUMBER = 2;
    private long time_;
    /**
     * <code>required int64 time = 2;</code>
     */
    public boolean hasTime() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int64 time = 2;</code>
     */
    public long getTime() {
      return time_;
    }

    private void initFields() {
      petId_ = 0;
      time_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt64(2, time_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, time_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SerializeObject.PetBreedCD parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedCD parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SerializeObject.PetBreedCD parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.PetBreedCD parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SerializeObject.PetBreedCD prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PetBreedCD}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SerializeObject.PetBreedCDOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SerializeObject.internal_static_protocol_PetBreedCD_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SerializeObject.internal_static_protocol_PetBreedCD_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SerializeObject.PetBreedCD.class, protocol.SerializeObject.PetBreedCD.Builder.class);
      }

      // Construct using protocol.SerializeObject.PetBreedCD.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        time_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SerializeObject.internal_static_protocol_PetBreedCD_descriptor;
      }

      public protocol.SerializeObject.PetBreedCD getDefaultInstanceForType() {
        return protocol.SerializeObject.PetBreedCD.getDefaultInstance();
      }

      public protocol.SerializeObject.PetBreedCD build() {
        protocol.SerializeObject.PetBreedCD result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SerializeObject.PetBreedCD buildPartial() {
        protocol.SerializeObject.PetBreedCD result = new protocol.SerializeObject.PetBreedCD(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petId_ = petId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.time_ = time_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SerializeObject.PetBreedCD) {
          return mergeFrom((protocol.SerializeObject.PetBreedCD)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SerializeObject.PetBreedCD other) {
        if (other == protocol.SerializeObject.PetBreedCD.getDefaultInstance()) return this;
        if (other.hasPetId()) {
          setPetId(other.getPetId());
        }
        if (other.hasTime()) {
          setTime(other.getTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetId()) {
          
          return false;
        }
        if (!hasTime()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SerializeObject.PetBreedCD parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SerializeObject.PetBreedCD) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 petId = 1;
      private int petId_ ;
      /**
       * <code>required int32 petId = 1;</code>
       */
      public boolean hasPetId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 petId = 1;</code>
       */
      public int getPetId() {
        return petId_;
      }
      /**
       * <code>required int32 petId = 1;</code>
       */
      public Builder setPetId(int value) {
        bitField0_ |= 0x00000001;
        petId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 petId = 1;</code>
       */
      public Builder clearPetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petId_ = 0;
        onChanged();
        return this;
      }

      // required int64 time = 2;
      private long time_ ;
      /**
       * <code>required int64 time = 2;</code>
       */
      public boolean hasTime() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int64 time = 2;</code>
       */
      public long getTime() {
        return time_;
      }
      /**
       * <code>required int64 time = 2;</code>
       */
      public Builder setTime(long value) {
        bitField0_ |= 0x00000002;
        time_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 time = 2;</code>
       */
      public Builder clearTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        time_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PetBreedCD)
    }

    static {
      defaultInstance = new PetBreedCD(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PetBreedCD)
  }

  public interface PetEvolutionInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 PetId = 1;
    /**
     * <code>required int32 PetId = 1;</code>
     *
     * <pre>
     * 宠物id
     * </pre>
     */
    boolean hasPetId();
    /**
     * <code>required int32 PetId = 1;</code>
     *
     * <pre>
     * 宠物id
     * </pre>
     */
    int getPetId();

    // required int32 status = 2;
    /**
     * <code>required int32 status = 2;</code>
     *
     * <pre>
     *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
     * </pre>
     */
    boolean hasStatus();
    /**
     * <code>required int32 status = 2;</code>
     *
     * <pre>
     *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
     * </pre>
     */
    int getStatus();

    // required int64 lastRecordTime = 3;
    /**
     * <code>required int64 lastRecordTime = 3;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasLastRecordTime();
    /**
     * <code>required int64 lastRecordTime = 3;</code>
     *
     * <pre>
     * </pre>
     */
    long getLastRecordTime();

    // required int32 inMapId = 4;
    /**
     * <code>required int32 inMapId = 4;</code>
     */
    boolean hasInMapId();
    /**
     * <code>required int32 inMapId = 4;</code>
     */
    int getInMapId();
  }
  /**
   * Protobuf type {@code protocol.PetEvolutionInfo}
   */
  public static final class PetEvolutionInfo extends
      com.google.protobuf.GeneratedMessage
      implements PetEvolutionInfoOrBuilder {
    // Use PetEvolutionInfo.newBuilder() to construct.
    private PetEvolutionInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PetEvolutionInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PetEvolutionInfo defaultInstance;
    public static PetEvolutionInfo getDefaultInstance() {
      return defaultInstance;
    }

    public PetEvolutionInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PetEvolutionInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              petId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              status_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              lastRecordTime_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              inMapId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SerializeObject.internal_static_protocol_PetEvolutionInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SerializeObject.internal_static_protocol_PetEvolutionInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SerializeObject.PetEvolutionInfo.class, protocol.SerializeObject.PetEvolutionInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<PetEvolutionInfo> PARSER =
        new com.google.protobuf.AbstractParser<PetEvolutionInfo>() {
      public PetEvolutionInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PetEvolutionInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PetEvolutionInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 PetId = 1;
    public static final int PETID_FIELD_NUMBER = 1;
    private int petId_;
    /**
     * <code>required int32 PetId = 1;</code>
     *
     * <pre>
     * 宠物id
     * </pre>
     */
    public boolean hasPetId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 PetId = 1;</code>
     *
     * <pre>
     * 宠物id
     * </pre>
     */
    public int getPetId() {
      return petId_;
    }

    // required int32 status = 2;
    public static final int STATUS_FIELD_NUMBER = 2;
    private int status_;
    /**
     * <code>required int32 status = 2;</code>
     *
     * <pre>
     *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
     * </pre>
     */
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 status = 2;</code>
     *
     * <pre>
     *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
     * </pre>
     */
    public int getStatus() {
      return status_;
    }

    // required int64 lastRecordTime = 3;
    public static final int LASTRECORDTIME_FIELD_NUMBER = 3;
    private long lastRecordTime_;
    /**
     * <code>required int64 lastRecordTime = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasLastRecordTime() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int64 lastRecordTime = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public long getLastRecordTime() {
      return lastRecordTime_;
    }

    // required int32 inMapId = 4;
    public static final int INMAPID_FIELD_NUMBER = 4;
    private int inMapId_;
    /**
     * <code>required int32 inMapId = 4;</code>
     */
    public boolean hasInMapId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 inMapId = 4;</code>
     */
    public int getInMapId() {
      return inMapId_;
    }

    private void initFields() {
      petId_ = 0;
      status_ = 0;
      lastRecordTime_ = 0L;
      inMapId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPetId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLastRecordTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasInMapId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, petId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, status_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt64(3, lastRecordTime_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, inMapId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, petId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, status_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, lastRecordTime_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, inMapId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.PetEvolutionInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SerializeObject.PetEvolutionInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PetEvolutionInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SerializeObject.PetEvolutionInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SerializeObject.internal_static_protocol_PetEvolutionInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SerializeObject.internal_static_protocol_PetEvolutionInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SerializeObject.PetEvolutionInfo.class, protocol.SerializeObject.PetEvolutionInfo.Builder.class);
      }

      // Construct using protocol.SerializeObject.PetEvolutionInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        petId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        status_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        lastRecordTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        inMapId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SerializeObject.internal_static_protocol_PetEvolutionInfo_descriptor;
      }

      public protocol.SerializeObject.PetEvolutionInfo getDefaultInstanceForType() {
        return protocol.SerializeObject.PetEvolutionInfo.getDefaultInstance();
      }

      public protocol.SerializeObject.PetEvolutionInfo build() {
        protocol.SerializeObject.PetEvolutionInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SerializeObject.PetEvolutionInfo buildPartial() {
        protocol.SerializeObject.PetEvolutionInfo result = new protocol.SerializeObject.PetEvolutionInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.petId_ = petId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.status_ = status_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.lastRecordTime_ = lastRecordTime_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.inMapId_ = inMapId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SerializeObject.PetEvolutionInfo) {
          return mergeFrom((protocol.SerializeObject.PetEvolutionInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SerializeObject.PetEvolutionInfo other) {
        if (other == protocol.SerializeObject.PetEvolutionInfo.getDefaultInstance()) return this;
        if (other.hasPetId()) {
          setPetId(other.getPetId());
        }
        if (other.hasStatus()) {
          setStatus(other.getStatus());
        }
        if (other.hasLastRecordTime()) {
          setLastRecordTime(other.getLastRecordTime());
        }
        if (other.hasInMapId()) {
          setInMapId(other.getInMapId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPetId()) {
          
          return false;
        }
        if (!hasStatus()) {
          
          return false;
        }
        if (!hasLastRecordTime()) {
          
          return false;
        }
        if (!hasInMapId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SerializeObject.PetEvolutionInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SerializeObject.PetEvolutionInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 PetId = 1;
      private int petId_ ;
      /**
       * <code>required int32 PetId = 1;</code>
       *
       * <pre>
       * 宠物id
       * </pre>
       */
      public boolean hasPetId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 PetId = 1;</code>
       *
       * <pre>
       * 宠物id
       * </pre>
       */
      public int getPetId() {
        return petId_;
      }
      /**
       * <code>required int32 PetId = 1;</code>
       *
       * <pre>
       * 宠物id
       * </pre>
       */
      public Builder setPetId(int value) {
        bitField0_ |= 0x00000001;
        petId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 PetId = 1;</code>
       *
       * <pre>
       * 宠物id
       * </pre>
       */
      public Builder clearPetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        petId_ = 0;
        onChanged();
        return this;
      }

      // required int32 status = 2;
      private int status_ ;
      /**
       * <code>required int32 status = 2;</code>
       *
       * <pre>
       *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
       * </pre>
       */
      public boolean hasStatus() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 status = 2;</code>
       *
       * <pre>
       *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
       * </pre>
       */
      public int getStatus() {
        return status_;
      }
      /**
       * <code>required int32 status = 2;</code>
       *
       * <pre>
       *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
       * </pre>
       */
      public Builder setStatus(int value) {
        bitField0_ |= 0x00000002;
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 status = 2;</code>
       *
       * <pre>
       *因为玩家上线的时候交由客户端维护，所以设置1为数据库状态 2为客户端内存状态
       * </pre>
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000002);
        status_ = 0;
        onChanged();
        return this;
      }

      // required int64 lastRecordTime = 3;
      private long lastRecordTime_ ;
      /**
       * <code>required int64 lastRecordTime = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasLastRecordTime() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int64 lastRecordTime = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public long getLastRecordTime() {
        return lastRecordTime_;
      }
      /**
       * <code>required int64 lastRecordTime = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setLastRecordTime(long value) {
        bitField0_ |= 0x00000004;
        lastRecordTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 lastRecordTime = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearLastRecordTime() {
        bitField0_ = (bitField0_ & ~0x00000004);
        lastRecordTime_ = 0L;
        onChanged();
        return this;
      }

      // required int32 inMapId = 4;
      private int inMapId_ ;
      /**
       * <code>required int32 inMapId = 4;</code>
       */
      public boolean hasInMapId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 inMapId = 4;</code>
       */
      public int getInMapId() {
        return inMapId_;
      }
      /**
       * <code>required int32 inMapId = 4;</code>
       */
      public Builder setInMapId(int value) {
        bitField0_ |= 0x00000008;
        inMapId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 inMapId = 4;</code>
       */
      public Builder clearInMapId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        inMapId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PetEvolutionInfo)
    }

    static {
      defaultInstance = new PetEvolutionInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PetEvolutionInfo)
  }

  public interface GrowingPetInfoOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 growth = 1;
    /**
     * <code>required int32 growth = 1;</code>
     *
     * <pre>
     *成长值
     * </pre>
     */
    boolean hasGrowth();
    /**
     * <code>required int32 growth = 1;</code>
     *
     * <pre>
     *成长值
     * </pre>
     */
    int getGrowth();

    // required int32 age = 2;
    /**
     * <code>required int32 age = 2;</code>
     *
     * <pre>
     * 进化的总时间
     * </pre>
     */
    boolean hasAge();
    /**
     * <code>required int32 age = 2;</code>
     *
     * <pre>
     * 进化的总时间
     * </pre>
     */
    int getAge();

    // required int32 mood = 3;
    /**
     * <code>required int32 mood = 3;</code>
     *
     * <pre>
     *当前心情值
     * </pre>
     */
    boolean hasMood();
    /**
     * <code>required int32 mood = 3;</code>
     *
     * <pre>
     *当前心情值
     * </pre>
     */
    int getMood();

    // required int32 happayTime = 4;
    /**
     * <code>required int32 happayTime = 4;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasHappayTime();
    /**
     * <code>required int32 happayTime = 4;</code>
     *
     * <pre>
     * </pre>
     */
    int getHappayTime();
  }
  /**
   * Protobuf type {@code protocol.GrowingPetInfo}
   */
  public static final class GrowingPetInfo extends
      com.google.protobuf.GeneratedMessage
      implements GrowingPetInfoOrBuilder {
    // Use GrowingPetInfo.newBuilder() to construct.
    private GrowingPetInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private GrowingPetInfo(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final GrowingPetInfo defaultInstance;
    public static GrowingPetInfo getDefaultInstance() {
      return defaultInstance;
    }

    public GrowingPetInfo getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private GrowingPetInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              growth_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              age_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              mood_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              happayTime_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SerializeObject.internal_static_protocol_GrowingPetInfo_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SerializeObject.internal_static_protocol_GrowingPetInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SerializeObject.GrowingPetInfo.class, protocol.SerializeObject.GrowingPetInfo.Builder.class);
    }

    public static com.google.protobuf.Parser<GrowingPetInfo> PARSER =
        new com.google.protobuf.AbstractParser<GrowingPetInfo>() {
      public GrowingPetInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new GrowingPetInfo(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<GrowingPetInfo> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 growth = 1;
    public static final int GROWTH_FIELD_NUMBER = 1;
    private int growth_;
    /**
     * <code>required int32 growth = 1;</code>
     *
     * <pre>
     *成长值
     * </pre>
     */
    public boolean hasGrowth() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 growth = 1;</code>
     *
     * <pre>
     *成长值
     * </pre>
     */
    public int getGrowth() {
      return growth_;
    }

    // required int32 age = 2;
    public static final int AGE_FIELD_NUMBER = 2;
    private int age_;
    /**
     * <code>required int32 age = 2;</code>
     *
     * <pre>
     * 进化的总时间
     * </pre>
     */
    public boolean hasAge() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 age = 2;</code>
     *
     * <pre>
     * 进化的总时间
     * </pre>
     */
    public int getAge() {
      return age_;
    }

    // required int32 mood = 3;
    public static final int MOOD_FIELD_NUMBER = 3;
    private int mood_;
    /**
     * <code>required int32 mood = 3;</code>
     *
     * <pre>
     *当前心情值
     * </pre>
     */
    public boolean hasMood() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 mood = 3;</code>
     *
     * <pre>
     *当前心情值
     * </pre>
     */
    public int getMood() {
      return mood_;
    }

    // required int32 happayTime = 4;
    public static final int HAPPAYTIME_FIELD_NUMBER = 4;
    private int happayTime_;
    /**
     * <code>required int32 happayTime = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasHappayTime() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 happayTime = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public int getHappayTime() {
      return happayTime_;
    }

    private void initFields() {
      growth_ = 0;
      age_ = 0;
      mood_ = 0;
      happayTime_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasGrowth()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAge()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMood()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHappayTime()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, growth_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, age_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, mood_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, happayTime_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, growth_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, age_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, mood_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, happayTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SerializeObject.GrowingPetInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SerializeObject.GrowingPetInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SerializeObject.GrowingPetInfo prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.GrowingPetInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SerializeObject.GrowingPetInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SerializeObject.internal_static_protocol_GrowingPetInfo_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SerializeObject.internal_static_protocol_GrowingPetInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SerializeObject.GrowingPetInfo.class, protocol.SerializeObject.GrowingPetInfo.Builder.class);
      }

      // Construct using protocol.SerializeObject.GrowingPetInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        growth_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        age_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        mood_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        happayTime_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SerializeObject.internal_static_protocol_GrowingPetInfo_descriptor;
      }

      public protocol.SerializeObject.GrowingPetInfo getDefaultInstanceForType() {
        return protocol.SerializeObject.GrowingPetInfo.getDefaultInstance();
      }

      public protocol.SerializeObject.GrowingPetInfo build() {
        protocol.SerializeObject.GrowingPetInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SerializeObject.GrowingPetInfo buildPartial() {
        protocol.SerializeObject.GrowingPetInfo result = new protocol.SerializeObject.GrowingPetInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.growth_ = growth_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.age_ = age_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.mood_ = mood_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.happayTime_ = happayTime_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SerializeObject.GrowingPetInfo) {
          return mergeFrom((protocol.SerializeObject.GrowingPetInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SerializeObject.GrowingPetInfo other) {
        if (other == protocol.SerializeObject.GrowingPetInfo.getDefaultInstance()) return this;
        if (other.hasGrowth()) {
          setGrowth(other.getGrowth());
        }
        if (other.hasAge()) {
          setAge(other.getAge());
        }
        if (other.hasMood()) {
          setMood(other.getMood());
        }
        if (other.hasHappayTime()) {
          setHappayTime(other.getHappayTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasGrowth()) {
          
          return false;
        }
        if (!hasAge()) {
          
          return false;
        }
        if (!hasMood()) {
          
          return false;
        }
        if (!hasHappayTime()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SerializeObject.GrowingPetInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SerializeObject.GrowingPetInfo) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 growth = 1;
      private int growth_ ;
      /**
       * <code>required int32 growth = 1;</code>
       *
       * <pre>
       *成长值
       * </pre>
       */
      public boolean hasGrowth() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 growth = 1;</code>
       *
       * <pre>
       *成长值
       * </pre>
       */
      public int getGrowth() {
        return growth_;
      }
      /**
       * <code>required int32 growth = 1;</code>
       *
       * <pre>
       *成长值
       * </pre>
       */
      public Builder setGrowth(int value) {
        bitField0_ |= 0x00000001;
        growth_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 growth = 1;</code>
       *
       * <pre>
       *成长值
       * </pre>
       */
      public Builder clearGrowth() {
        bitField0_ = (bitField0_ & ~0x00000001);
        growth_ = 0;
        onChanged();
        return this;
      }

      // required int32 age = 2;
      private int age_ ;
      /**
       * <code>required int32 age = 2;</code>
       *
       * <pre>
       * 进化的总时间
       * </pre>
       */
      public boolean hasAge() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 age = 2;</code>
       *
       * <pre>
       * 进化的总时间
       * </pre>
       */
      public int getAge() {
        return age_;
      }
      /**
       * <code>required int32 age = 2;</code>
       *
       * <pre>
       * 进化的总时间
       * </pre>
       */
      public Builder setAge(int value) {
        bitField0_ |= 0x00000002;
        age_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 age = 2;</code>
       *
       * <pre>
       * 进化的总时间
       * </pre>
       */
      public Builder clearAge() {
        bitField0_ = (bitField0_ & ~0x00000002);
        age_ = 0;
        onChanged();
        return this;
      }

      // required int32 mood = 3;
      private int mood_ ;
      /**
       * <code>required int32 mood = 3;</code>
       *
       * <pre>
       *当前心情值
       * </pre>
       */
      public boolean hasMood() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 mood = 3;</code>
       *
       * <pre>
       *当前心情值
       * </pre>
       */
      public int getMood() {
        return mood_;
      }
      /**
       * <code>required int32 mood = 3;</code>
       *
       * <pre>
       *当前心情值
       * </pre>
       */
      public Builder setMood(int value) {
        bitField0_ |= 0x00000004;
        mood_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mood = 3;</code>
       *
       * <pre>
       *当前心情值
       * </pre>
       */
      public Builder clearMood() {
        bitField0_ = (bitField0_ & ~0x00000004);
        mood_ = 0;
        onChanged();
        return this;
      }

      // required int32 happayTime = 4;
      private int happayTime_ ;
      /**
       * <code>required int32 happayTime = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasHappayTime() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 happayTime = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public int getHappayTime() {
        return happayTime_;
      }
      /**
       * <code>required int32 happayTime = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setHappayTime(int value) {
        bitField0_ |= 0x00000008;
        happayTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 happayTime = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearHappayTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        happayTime_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.GrowingPetInfo)
    }

    static {
      defaultInstance = new GrowingPetInfo(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.GrowingPetInfo)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PetBreedInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PetBreedInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PetBreedCD_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PetBreedCD_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PetEvolutionInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PetEvolutionInfo_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_GrowingPetInfo_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_GrowingPetInfo_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\033serverSerializeObject.proto\022\010protocol\032" +
      "\nitem.proto\"\236\001\n\014PetBreedInfo\022\026\n\016petBreed" +
      "Status\030\001 \002(\005\022(\n\nbreedCDPet\030\002 \003(\0132\024.proto" +
      "col.PetBreedCD\022\022\n\nbreedPetId\030\003 \003(\005\022\027\n\017br" +
      "eedfinishTime\030\004 \001(\003\022\037\n\007useItem\030\005 \001(\0132\016.p" +
      "rotocol.Item\")\n\nPetBreedCD\022\r\n\005petId\030\001 \002(" +
      "\005\022\014\n\004time\030\002 \002(\003\"Z\n\020PetEvolutionInfo\022\r\n\005P" +
      "etId\030\001 \002(\005\022\016\n\006status\030\002 \002(\005\022\026\n\016lastRecord" +
      "Time\030\003 \002(\003\022\017\n\007inMapId\030\004 \002(\005\"O\n\016GrowingPe" +
      "tInfo\022\016\n\006growth\030\001 \002(\005\022\013\n\003age\030\002 \002(\005\022\014\n\004mo",
      "od\030\003 \002(\005\022\022\n\nhappayTime\030\004 \002(\005B\021B\017Serializ" +
      "eObject"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_PetBreedInfo_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_PetBreedInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PetBreedInfo_descriptor,
              new java.lang.String[] { "PetBreedStatus", "BreedCDPet", "BreedPetId", "BreedfinishTime", "UseItem", });
          internal_static_protocol_PetBreedCD_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_PetBreedCD_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PetBreedCD_descriptor,
              new java.lang.String[] { "PetId", "Time", });
          internal_static_protocol_PetEvolutionInfo_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_PetEvolutionInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PetEvolutionInfo_descriptor,
              new java.lang.String[] { "PetId", "Status", "LastRecordTime", "InMapId", });
          internal_static_protocol_GrowingPetInfo_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_GrowingPetInfo_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_GrowingPetInfo_descriptor,
              new java.lang.String[] { "Growth", "Age", "Mood", "HappayTime", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
