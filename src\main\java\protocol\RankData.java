// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rank.proto

package protocol;

public final class RankData {
  private RankData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RankingOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 rankNumber = 1;
    /**
     * <code>required int32 rankNumber = 1;</code>
     *
     * <pre>
     * 排名
     * </pre>
     */
    boolean hasRankNumber();
    /**
     * <code>required int32 rankNumber = 1;</code>
     *
     * <pre>
     * 排名
     * </pre>
     */
    int getRankNumber();

    // required int32 score = 2;
    /**
     * <code>required int32 score = 2;</code>
     *
     * <pre>
     * 分数
     * </pre>
     */
    boolean hasScore();
    /**
     * <code>required int32 score = 2;</code>
     *
     * <pre>
     * 分数
     * </pre>
     */
    int getScore();

    // required int32 lv = 3;
    /**
     * <code>required int32 lv = 3;</code>
     */
    boolean hasLv();
    /**
     * <code>required int32 lv = 3;</code>
     */
    int getLv();

    // required int32 head = 4;
    /**
     * <code>required int32 head = 4;</code>
     */
    boolean hasHead();
    /**
     * <code>required int32 head = 4;</code>
     */
    int getHead();

    // required string name = 5;
    /**
     * <code>required string name = 5;</code>
     */
    boolean hasName();
    /**
     * <code>required string name = 5;</code>
     */
    java.lang.String getName();
    /**
     * <code>required string name = 5;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    // required int32 id = 6;
    /**
     * <code>required int32 id = 6;</code>
     */
    boolean hasId();
    /**
     * <code>required int32 id = 6;</code>
     */
    int getId();

    // required int32 sex = 7;
    /**
     * <code>required int32 sex = 7;</code>
     */
    boolean hasSex();
    /**
     * <code>required int32 sex = 7;</code>
     */
    int getSex();
  }
  /**
   * Protobuf type {@code protocol.Ranking}
   */
  public static final class Ranking extends
      com.google.protobuf.GeneratedMessage
      implements RankingOrBuilder {
    // Use Ranking.newBuilder() to construct.
    private Ranking(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Ranking(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Ranking defaultInstance;
    public static Ranking getDefaultInstance() {
      return defaultInstance;
    }

    public Ranking getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Ranking(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              rankNumber_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              score_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              lv_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              head_ = input.readInt32();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              name_ = input.readBytes();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              id_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              sex_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankData.internal_static_protocol_Ranking_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankData.internal_static_protocol_Ranking_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankData.Ranking.class, protocol.RankData.Ranking.Builder.class);
    }

    public static com.google.protobuf.Parser<Ranking> PARSER =
        new com.google.protobuf.AbstractParser<Ranking>() {
      public Ranking parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Ranking(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Ranking> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 rankNumber = 1;
    public static final int RANKNUMBER_FIELD_NUMBER = 1;
    private int rankNumber_;
    /**
     * <code>required int32 rankNumber = 1;</code>
     *
     * <pre>
     * 排名
     * </pre>
     */
    public boolean hasRankNumber() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 rankNumber = 1;</code>
     *
     * <pre>
     * 排名
     * </pre>
     */
    public int getRankNumber() {
      return rankNumber_;
    }

    // required int32 score = 2;
    public static final int SCORE_FIELD_NUMBER = 2;
    private int score_;
    /**
     * <code>required int32 score = 2;</code>
     *
     * <pre>
     * 分数
     * </pre>
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 score = 2;</code>
     *
     * <pre>
     * 分数
     * </pre>
     */
    public int getScore() {
      return score_;
    }

    // required int32 lv = 3;
    public static final int LV_FIELD_NUMBER = 3;
    private int lv_;
    /**
     * <code>required int32 lv = 3;</code>
     */
    public boolean hasLv() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 lv = 3;</code>
     */
    public int getLv() {
      return lv_;
    }

    // required int32 head = 4;
    public static final int HEAD_FIELD_NUMBER = 4;
    private int head_;
    /**
     * <code>required int32 head = 4;</code>
     */
    public boolean hasHead() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 head = 4;</code>
     */
    public int getHead() {
      return head_;
    }

    // required string name = 5;
    public static final int NAME_FIELD_NUMBER = 5;
    private java.lang.Object name_;
    /**
     * <code>required string name = 5;</code>
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string name = 5;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string name = 5;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 id = 6;
    public static final int ID_FIELD_NUMBER = 6;
    private int id_;
    /**
     * <code>required int32 id = 6;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required int32 id = 6;</code>
     */
    public int getId() {
      return id_;
    }

    // required int32 sex = 7;
    public static final int SEX_FIELD_NUMBER = 7;
    private int sex_;
    /**
     * <code>required int32 sex = 7;</code>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required int32 sex = 7;</code>
     */
    public int getSex() {
      return sex_;
    }

    private void initFields() {
      rankNumber_ = 0;
      score_ = 0;
      lv_ = 0;
      head_ = 0;
      name_ = "";
      id_ = 0;
      sex_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRankNumber()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasScore()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLv()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHead()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, rankNumber_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, score_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, lv_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, head_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getNameBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, id_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, sex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rankNumber_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, score_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, lv_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, head_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getNameBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, id_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, sex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankData.Ranking parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankData.Ranking parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankData.Ranking parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankData.Ranking parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankData.Ranking parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankData.Ranking parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankData.Ranking parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankData.Ranking parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankData.Ranking parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankData.Ranking parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankData.Ranking prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Ranking}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankData.RankingOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankData.internal_static_protocol_Ranking_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankData.internal_static_protocol_Ranking_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankData.Ranking.class, protocol.RankData.Ranking.Builder.class);
      }

      // Construct using protocol.RankData.Ranking.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        rankNumber_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        score_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        lv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        head_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        sex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankData.internal_static_protocol_Ranking_descriptor;
      }

      public protocol.RankData.Ranking getDefaultInstanceForType() {
        return protocol.RankData.Ranking.getDefaultInstance();
      }

      public protocol.RankData.Ranking build() {
        protocol.RankData.Ranking result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankData.Ranking buildPartial() {
        protocol.RankData.Ranking result = new protocol.RankData.Ranking(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.rankNumber_ = rankNumber_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.score_ = score_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.lv_ = lv_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.head_ = head_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.sex_ = sex_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankData.Ranking) {
          return mergeFrom((protocol.RankData.Ranking)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankData.Ranking other) {
        if (other == protocol.RankData.Ranking.getDefaultInstance()) return this;
        if (other.hasRankNumber()) {
          setRankNumber(other.getRankNumber());
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        if (other.hasLv()) {
          setLv(other.getLv());
        }
        if (other.hasHead()) {
          setHead(other.getHead());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000010;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasSex()) {
          setSex(other.getSex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRankNumber()) {
          
          return false;
        }
        if (!hasScore()) {
          
          return false;
        }
        if (!hasLv()) {
          
          return false;
        }
        if (!hasHead()) {
          
          return false;
        }
        if (!hasName()) {
          
          return false;
        }
        if (!hasId()) {
          
          return false;
        }
        if (!hasSex()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankData.Ranking parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankData.Ranking) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 rankNumber = 1;
      private int rankNumber_ ;
      /**
       * <code>required int32 rankNumber = 1;</code>
       *
       * <pre>
       * 排名
       * </pre>
       */
      public boolean hasRankNumber() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 rankNumber = 1;</code>
       *
       * <pre>
       * 排名
       * </pre>
       */
      public int getRankNumber() {
        return rankNumber_;
      }
      /**
       * <code>required int32 rankNumber = 1;</code>
       *
       * <pre>
       * 排名
       * </pre>
       */
      public Builder setRankNumber(int value) {
        bitField0_ |= 0x00000001;
        rankNumber_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 rankNumber = 1;</code>
       *
       * <pre>
       * 排名
       * </pre>
       */
      public Builder clearRankNumber() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rankNumber_ = 0;
        onChanged();
        return this;
      }

      // required int32 score = 2;
      private int score_ ;
      /**
       * <code>required int32 score = 2;</code>
       *
       * <pre>
       * 分数
       * </pre>
       */
      public boolean hasScore() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 score = 2;</code>
       *
       * <pre>
       * 分数
       * </pre>
       */
      public int getScore() {
        return score_;
      }
      /**
       * <code>required int32 score = 2;</code>
       *
       * <pre>
       * 分数
       * </pre>
       */
      public Builder setScore(int value) {
        bitField0_ |= 0x00000002;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 score = 2;</code>
       *
       * <pre>
       * 分数
       * </pre>
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        score_ = 0;
        onChanged();
        return this;
      }

      // required int32 lv = 3;
      private int lv_ ;
      /**
       * <code>required int32 lv = 3;</code>
       */
      public boolean hasLv() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 lv = 3;</code>
       */
      public int getLv() {
        return lv_;
      }
      /**
       * <code>required int32 lv = 3;</code>
       */
      public Builder setLv(int value) {
        bitField0_ |= 0x00000004;
        lv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 lv = 3;</code>
       */
      public Builder clearLv() {
        bitField0_ = (bitField0_ & ~0x00000004);
        lv_ = 0;
        onChanged();
        return this;
      }

      // required int32 head = 4;
      private int head_ ;
      /**
       * <code>required int32 head = 4;</code>
       */
      public boolean hasHead() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 head = 4;</code>
       */
      public int getHead() {
        return head_;
      }
      /**
       * <code>required int32 head = 4;</code>
       */
      public Builder setHead(int value) {
        bitField0_ |= 0x00000008;
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 head = 4;</code>
       */
      public Builder clearHead() {
        bitField0_ = (bitField0_ & ~0x00000008);
        head_ = 0;
        onChanged();
        return this;
      }

      // required string name = 5;
      private java.lang.Object name_ = "";
      /**
       * <code>required string name = 5;</code>
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string name = 5;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string name = 5;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string name = 5;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 5;</code>
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 5;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        name_ = value;
        onChanged();
        return this;
      }

      // required int32 id = 6;
      private int id_ ;
      /**
       * <code>required int32 id = 6;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required int32 id = 6;</code>
       */
      public int getId() {
        return id_;
      }
      /**
       * <code>required int32 id = 6;</code>
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000020;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 id = 6;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        id_ = 0;
        onChanged();
        return this;
      }

      // required int32 sex = 7;
      private int sex_ ;
      /**
       * <code>required int32 sex = 7;</code>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required int32 sex = 7;</code>
       */
      public int getSex() {
        return sex_;
      }
      /**
       * <code>required int32 sex = 7;</code>
       */
      public Builder setSex(int value) {
        bitField0_ |= 0x00000040;
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sex = 7;</code>
       */
      public Builder clearSex() {
        bitField0_ = (bitField0_ & ~0x00000040);
        sex_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Ranking)
    }

    static {
      defaultInstance = new Ranking(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Ranking)
  }

  public interface RequestRankOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 mold = 1;
    /**
     * <code>required int32 mold = 1;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    boolean hasMold();
    /**
     * <code>required int32 mold = 1;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    int getMold();
  }
  /**
   * Protobuf type {@code protocol.RequestRank}
   *
   * <pre>
   *1307
   * </pre>
   */
  public static final class RequestRank extends
      com.google.protobuf.GeneratedMessage
      implements RequestRankOrBuilder {
    // Use RequestRank.newBuilder() to construct.
    private RequestRank(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestRank(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestRank defaultInstance;
    public static RequestRank getDefaultInstance() {
      return defaultInstance;
    }

    public RequestRank getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestRank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              mold_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankData.internal_static_protocol_RequestRank_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankData.internal_static_protocol_RequestRank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankData.RequestRank.class, protocol.RankData.RequestRank.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestRank> PARSER =
        new com.google.protobuf.AbstractParser<RequestRank>() {
      public RequestRank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestRank(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestRank> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 mold = 1;
    public static final int MOLD_FIELD_NUMBER = 1;
    private int mold_;
    /**
     * <code>required int32 mold = 1;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    public boolean hasMold() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 mold = 1;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    public int getMold() {
      return mold_;
    }

    private void initFields() {
      mold_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasMold()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, mold_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mold_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankData.RequestRank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankData.RequestRank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankData.RequestRank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankData.RequestRank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankData.RequestRank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankData.RequestRank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankData.RequestRank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankData.RequestRank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankData.RequestRank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankData.RequestRank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankData.RequestRank prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestRank}
     *
     * <pre>
     *1307
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankData.RequestRankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankData.internal_static_protocol_RequestRank_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankData.internal_static_protocol_RequestRank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankData.RequestRank.class, protocol.RankData.RequestRank.Builder.class);
      }

      // Construct using protocol.RankData.RequestRank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        mold_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankData.internal_static_protocol_RequestRank_descriptor;
      }

      public protocol.RankData.RequestRank getDefaultInstanceForType() {
        return protocol.RankData.RequestRank.getDefaultInstance();
      }

      public protocol.RankData.RequestRank build() {
        protocol.RankData.RequestRank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankData.RequestRank buildPartial() {
        protocol.RankData.RequestRank result = new protocol.RankData.RequestRank(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.mold_ = mold_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankData.RequestRank) {
          return mergeFrom((protocol.RankData.RequestRank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankData.RequestRank other) {
        if (other == protocol.RankData.RequestRank.getDefaultInstance()) return this;
        if (other.hasMold()) {
          setMold(other.getMold());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasMold()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankData.RequestRank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankData.RequestRank) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 mold = 1;
      private int mold_ ;
      /**
       * <code>required int32 mold = 1;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public boolean hasMold() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 mold = 1;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public int getMold() {
        return mold_;
      }
      /**
       * <code>required int32 mold = 1;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public Builder setMold(int value) {
        bitField0_ |= 0x00000001;
        mold_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mold = 1;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public Builder clearMold() {
        bitField0_ = (bitField0_ & ~0x00000001);
        mold_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestRank)
    }

    static {
      defaultInstance = new RequestRank(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestRank)
  }

  public interface ResponseRankOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0 成功 1 错误
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0 成功 1 错误
     * </pre>
     */
    int getErrorId();

    // required int32 mold = 2;
    /**
     * <code>required int32 mold = 2;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    boolean hasMold();
    /**
     * <code>required int32 mold = 2;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    int getMold();

    // repeated .protocol.Ranking rankList = 3;
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    java.util.List<protocol.RankData.Ranking> 
        getRankListList();
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    protocol.RankData.Ranking getRankList(int index);
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    int getRankListCount();
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    java.util.List<? extends protocol.RankData.RankingOrBuilder> 
        getRankListOrBuilderList();
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    protocol.RankData.RankingOrBuilder getRankListOrBuilder(
        int index);

    // required .protocol.Ranking user = 4;
    /**
     * <code>required .protocol.Ranking user = 4;</code>
     *
     * <pre>
     * 自己的
     * </pre>
     */
    boolean hasUser();
    /**
     * <code>required .protocol.Ranking user = 4;</code>
     *
     * <pre>
     * 自己的
     * </pre>
     */
    protocol.RankData.Ranking getUser();
    /**
     * <code>required .protocol.Ranking user = 4;</code>
     *
     * <pre>
     * 自己的
     * </pre>
     */
    protocol.RankData.RankingOrBuilder getUserOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseRank}
   *
   * <pre>
   *2307
   * </pre>
   */
  public static final class ResponseRank extends
      com.google.protobuf.GeneratedMessage
      implements ResponseRankOrBuilder {
    // Use ResponseRank.newBuilder() to construct.
    private ResponseRank(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseRank(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseRank defaultInstance;
    public static ResponseRank getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseRank getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseRank(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mold_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                rankList_ = new java.util.ArrayList<protocol.RankData.Ranking>();
                mutable_bitField0_ |= 0x00000004;
              }
              rankList_.add(input.readMessage(protocol.RankData.Ranking.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              protocol.RankData.Ranking.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = user_.toBuilder();
              }
              user_ = input.readMessage(protocol.RankData.Ranking.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(user_);
                user_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          rankList_ = java.util.Collections.unmodifiableList(rankList_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RankData.internal_static_protocol_ResponseRank_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RankData.internal_static_protocol_ResponseRank_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RankData.ResponseRank.class, protocol.RankData.ResponseRank.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseRank> PARSER =
        new com.google.protobuf.AbstractParser<ResponseRank>() {
      public ResponseRank parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseRank(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseRank> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0 成功 1 错误
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0 成功 1 错误
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 mold = 2;
    public static final int MOLD_FIELD_NUMBER = 2;
    private int mold_;
    /**
     * <code>required int32 mold = 2;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    public boolean hasMold() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 mold = 2;</code>
     *
     * <pre>
     *1、等级 2、果宝数量 3、分数
     * </pre>
     */
    public int getMold() {
      return mold_;
    }

    // repeated .protocol.Ranking rankList = 3;
    public static final int RANKLIST_FIELD_NUMBER = 3;
    private java.util.List<protocol.RankData.Ranking> rankList_;
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    public java.util.List<protocol.RankData.Ranking> getRankListList() {
      return rankList_;
    }
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    public java.util.List<? extends protocol.RankData.RankingOrBuilder> 
        getRankListOrBuilderList() {
      return rankList_;
    }
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    public int getRankListCount() {
      return rankList_.size();
    }
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    public protocol.RankData.Ranking getRankList(int index) {
      return rankList_.get(index);
    }
    /**
     * <code>repeated .protocol.Ranking rankList = 3;</code>
     *
     * <pre>
     * 50条数据
     * </pre>
     */
    public protocol.RankData.RankingOrBuilder getRankListOrBuilder(
        int index) {
      return rankList_.get(index);
    }

    // required .protocol.Ranking user = 4;
    public static final int USER_FIELD_NUMBER = 4;
    private protocol.RankData.Ranking user_;
    /**
     * <code>required .protocol.Ranking user = 4;</code>
     *
     * <pre>
     * 自己的
     * </pre>
     */
    public boolean hasUser() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required .protocol.Ranking user = 4;</code>
     *
     * <pre>
     * 自己的
     * </pre>
     */
    public protocol.RankData.Ranking getUser() {
      return user_;
    }
    /**
     * <code>required .protocol.Ranking user = 4;</code>
     *
     * <pre>
     * 自己的
     * </pre>
     */
    public protocol.RankData.RankingOrBuilder getUserOrBuilder() {
      return user_;
    }

    private void initFields() {
      errorId_ = 0;
      mold_ = 0;
      rankList_ = java.util.Collections.emptyList();
      user_ = protocol.RankData.Ranking.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMold()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasUser()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getRankListCount(); i++) {
        if (!getRankList(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (!getUser().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, mold_);
      }
      for (int i = 0; i < rankList_.size(); i++) {
        output.writeMessage(3, rankList_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(4, user_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mold_);
      }
      for (int i = 0; i < rankList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, rankList_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, user_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RankData.ResponseRank parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankData.ResponseRank parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankData.ResponseRank parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RankData.ResponseRank parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RankData.ResponseRank parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankData.ResponseRank parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RankData.ResponseRank parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RankData.ResponseRank parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RankData.ResponseRank parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RankData.ResponseRank parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RankData.ResponseRank prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseRank}
     *
     * <pre>
     *2307
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RankData.ResponseRankOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RankData.internal_static_protocol_ResponseRank_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RankData.internal_static_protocol_ResponseRank_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RankData.ResponseRank.class, protocol.RankData.ResponseRank.Builder.class);
      }

      // Construct using protocol.RankData.ResponseRank.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRankListFieldBuilder();
          getUserFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        mold_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          rankListBuilder_.clear();
        }
        if (userBuilder_ == null) {
          user_ = protocol.RankData.Ranking.getDefaultInstance();
        } else {
          userBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RankData.internal_static_protocol_ResponseRank_descriptor;
      }

      public protocol.RankData.ResponseRank getDefaultInstanceForType() {
        return protocol.RankData.ResponseRank.getDefaultInstance();
      }

      public protocol.RankData.ResponseRank build() {
        protocol.RankData.ResponseRank result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RankData.ResponseRank buildPartial() {
        protocol.RankData.ResponseRank result = new protocol.RankData.ResponseRank(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.mold_ = mold_;
        if (rankListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            rankList_ = java.util.Collections.unmodifiableList(rankList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.rankList_ = rankList_;
        } else {
          result.rankList_ = rankListBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        if (userBuilder_ == null) {
          result.user_ = user_;
        } else {
          result.user_ = userBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RankData.ResponseRank) {
          return mergeFrom((protocol.RankData.ResponseRank)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RankData.ResponseRank other) {
        if (other == protocol.RankData.ResponseRank.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasMold()) {
          setMold(other.getMold());
        }
        if (rankListBuilder_ == null) {
          if (!other.rankList_.isEmpty()) {
            if (rankList_.isEmpty()) {
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureRankListIsMutable();
              rankList_.addAll(other.rankList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankList_.isEmpty()) {
            if (rankListBuilder_.isEmpty()) {
              rankListBuilder_.dispose();
              rankListBuilder_ = null;
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              rankListBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRankListFieldBuilder() : null;
            } else {
              rankListBuilder_.addAllMessages(other.rankList_);
            }
          }
        }
        if (other.hasUser()) {
          mergeUser(other.getUser());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasMold()) {
          
          return false;
        }
        if (!hasUser()) {
          
          return false;
        }
        for (int i = 0; i < getRankListCount(); i++) {
          if (!getRankList(i).isInitialized()) {
            
            return false;
          }
        }
        if (!getUser().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RankData.ResponseRank parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RankData.ResponseRank) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0 成功 1 错误
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0 成功 1 错误
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0 成功 1 错误
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0 成功 1 错误
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 mold = 2;
      private int mold_ ;
      /**
       * <code>required int32 mold = 2;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public boolean hasMold() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 mold = 2;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public int getMold() {
        return mold_;
      }
      /**
       * <code>required int32 mold = 2;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public Builder setMold(int value) {
        bitField0_ |= 0x00000002;
        mold_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mold = 2;</code>
       *
       * <pre>
       *1、等级 2、果宝数量 3、分数
       * </pre>
       */
      public Builder clearMold() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mold_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Ranking rankList = 3;
      private java.util.List<protocol.RankData.Ranking> rankList_ =
        java.util.Collections.emptyList();
      private void ensureRankListIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          rankList_ = new java.util.ArrayList<protocol.RankData.Ranking>(rankList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RankData.Ranking, protocol.RankData.Ranking.Builder, protocol.RankData.RankingOrBuilder> rankListBuilder_;

      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public java.util.List<protocol.RankData.Ranking> getRankListList() {
        if (rankListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankList_);
        } else {
          return rankListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public int getRankListCount() {
        if (rankListBuilder_ == null) {
          return rankList_.size();
        } else {
          return rankListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public protocol.RankData.Ranking getRankList(int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);
        } else {
          return rankListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder setRankList(
          int index, protocol.RankData.Ranking value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.set(index, value);
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder setRankList(
          int index, protocol.RankData.Ranking.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder addRankList(protocol.RankData.Ranking value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder addRankList(
          int index, protocol.RankData.Ranking value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(index, value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder addRankList(
          protocol.RankData.Ranking.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder addRankList(
          int index, protocol.RankData.Ranking.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder addAllRankList(
          java.lang.Iterable<? extends protocol.RankData.Ranking> values) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          super.addAll(values, rankList_);
          onChanged();
        } else {
          rankListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder clearRankList() {
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public Builder removeRankList(int index) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.remove(index);
          onChanged();
        } else {
          rankListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public protocol.RankData.Ranking.Builder getRankListBuilder(
          int index) {
        return getRankListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public protocol.RankData.RankingOrBuilder getRankListOrBuilder(
          int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);  } else {
          return rankListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public java.util.List<? extends protocol.RankData.RankingOrBuilder> 
           getRankListOrBuilderList() {
        if (rankListBuilder_ != null) {
          return rankListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankList_);
        }
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public protocol.RankData.Ranking.Builder addRankListBuilder() {
        return getRankListFieldBuilder().addBuilder(
            protocol.RankData.Ranking.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public protocol.RankData.Ranking.Builder addRankListBuilder(
          int index) {
        return getRankListFieldBuilder().addBuilder(
            index, protocol.RankData.Ranking.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Ranking rankList = 3;</code>
       *
       * <pre>
       * 50条数据
       * </pre>
       */
      public java.util.List<protocol.RankData.Ranking.Builder> 
           getRankListBuilderList() {
        return getRankListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RankData.Ranking, protocol.RankData.Ranking.Builder, protocol.RankData.RankingOrBuilder> 
          getRankListFieldBuilder() {
        if (rankListBuilder_ == null) {
          rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.RankData.Ranking, protocol.RankData.Ranking.Builder, protocol.RankData.RankingOrBuilder>(
                  rankList_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          rankList_ = null;
        }
        return rankListBuilder_;
      }

      // required .protocol.Ranking user = 4;
      private protocol.RankData.Ranking user_ = protocol.RankData.Ranking.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.RankData.Ranking, protocol.RankData.Ranking.Builder, protocol.RankData.RankingOrBuilder> userBuilder_;
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public boolean hasUser() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public protocol.RankData.Ranking getUser() {
        if (userBuilder_ == null) {
          return user_;
        } else {
          return userBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public Builder setUser(protocol.RankData.Ranking value) {
        if (userBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          user_ = value;
          onChanged();
        } else {
          userBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public Builder setUser(
          protocol.RankData.Ranking.Builder builderForValue) {
        if (userBuilder_ == null) {
          user_ = builderForValue.build();
          onChanged();
        } else {
          userBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public Builder mergeUser(protocol.RankData.Ranking value) {
        if (userBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008) &&
              user_ != protocol.RankData.Ranking.getDefaultInstance()) {
            user_ =
              protocol.RankData.Ranking.newBuilder(user_).mergeFrom(value).buildPartial();
          } else {
            user_ = value;
          }
          onChanged();
        } else {
          userBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public Builder clearUser() {
        if (userBuilder_ == null) {
          user_ = protocol.RankData.Ranking.getDefaultInstance();
          onChanged();
        } else {
          userBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public protocol.RankData.Ranking.Builder getUserBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getUserFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      public protocol.RankData.RankingOrBuilder getUserOrBuilder() {
        if (userBuilder_ != null) {
          return userBuilder_.getMessageOrBuilder();
        } else {
          return user_;
        }
      }
      /**
       * <code>required .protocol.Ranking user = 4;</code>
       *
       * <pre>
       * 自己的
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.RankData.Ranking, protocol.RankData.Ranking.Builder, protocol.RankData.RankingOrBuilder> 
          getUserFieldBuilder() {
        if (userBuilder_ == null) {
          userBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.RankData.Ranking, protocol.RankData.Ranking.Builder, protocol.RankData.RankingOrBuilder>(
                  user_,
                  getParentForChildren(),
                  isClean());
          user_ = null;
        }
        return userBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseRank)
    }

    static {
      defaultInstance = new ResponseRank(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseRank)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Ranking_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Ranking_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestRank_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestRank_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseRank_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseRank_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nrank.proto\022\010protocol\032\013proto.proto\"m\n\007R" +
      "anking\022\022\n\nrankNumber\030\001 \002(\005\022\r\n\005score\030\002 \002(" +
      "\005\022\n\n\002lv\030\003 \002(\005\022\014\n\004head\030\004 \002(\005\022\014\n\004name\030\005 \002(" +
      "\t\022\n\n\002id\030\006 \002(\005\022\013\n\003sex\030\007 \002(\005\"\033\n\013RequestRan" +
      "k\022\014\n\004mold\030\001 \002(\005\"s\n\014ResponseRank\022\017\n\007error" +
      "Id\030\001 \002(\005\022\014\n\004mold\030\002 \002(\005\022#\n\010rankList\030\003 \003(\013" +
      "2\021.protocol.Ranking\022\037\n\004user\030\004 \002(\0132\021.prot" +
      "ocol.RankingB\nB\010RankData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_Ranking_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_Ranking_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Ranking_descriptor,
              new java.lang.String[] { "RankNumber", "Score", "Lv", "Head", "Name", "Id", "Sex", });
          internal_static_protocol_RequestRank_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestRank_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestRank_descriptor,
              new java.lang.String[] { "Mold", });
          internal_static_protocol_ResponseRank_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ResponseRank_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseRank_descriptor,
              new java.lang.String[] { "ErrorId", "Mold", "RankList", "User", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
