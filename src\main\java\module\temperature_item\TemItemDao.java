package module.temperature_item;

import entities.TemItemEntity;
import manager.MySql;
import org.hibernate.annotations.Synchronize;
import protocol.TemItemData;
import protocol.TemperData;

public class TemItemDao {

    private static TemItemDao inst = null;

    public static TemItemDao getInstance() {
        if (inst == null) {
            inst = new TemItemDao();
        }
        return inst;
    }

    private void InitData(TemItemEntity data, String uid){
        data.setWood(0);
        data.setLeaf(0);
        data.setWater(0);
        data.setSoil(0);

        data.setBattle(0);
        data.setRun(0);
        data.setPuzzle(0);

        data.setUid(uid);
        MySql.insert(data);
    }

    private void  UpdateData(TemItemEntity data,
                            TemItemData.RequestTemItem updateData){
        System.err.println("UpdateData？？？？？？？？？？");
        if (updateData.hasWood()){
            data.setWood(updateData.getWood());
        }
        if (updateData.hasLeaf()){
            data.setLeaf(updateData.getLeaf());
        }
        if (updateData.hasWater()){
            data.setWater(updateData.getWater());
        }
        if (updateData.hasSoil()){
            data.setSoil(updateData.getSoil());
        }


        if (updateData.hasBattle()){
            System.err.println("修改战斗次数？？？？？？？？？？");
            data.setBattle(updateData.getBattle());
        }
        if (updateData.hasRun()){
            data.setRun(updateData.getRun());
        }
        if (updateData.hasPuzzle()){
            data.setPuzzle(updateData.getPuzzle());
        }

        MySql.update(data);
    }

    public TemItemEntity Get(String uid){
        TemItemEntity data = null;
        StringBuffer sql = new StringBuffer("from TemItemEntity where uid='").append(uid).append("'");

        data = (TemItemEntity) MySql.queryForOne(sql.toString());

        if (data == null){
            data = new TemItemEntity();

            // 初始化数据
            InitData(data, uid);
        }
        return data;
    }

    public synchronized TemItemEntity Update(String uid, TemItemData.RequestTemItem updateData) {
        TemItemEntity data = null;
        StringBuffer sql = new StringBuffer("from TemItemEntity where uid='").append(uid).append("'");
        data = (TemItemEntity) MySql.queryForOne(sql.toString());

        if (data == null){
            data = new TemItemEntity();
            // 初始化数据
            InitData(data, uid);
        }else {
            UpdateData(data, updateData);
        }
        return data;
    }

    public void Delete() {
        StringBuffer stringBuffer = new StringBuffer("Delete from TemItemEntity");
        MySql.updateSomes(stringBuffer.toString());
    }
}
