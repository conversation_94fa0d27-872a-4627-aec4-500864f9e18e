package entities;

import javax.persistence.*;

@Entity
@Table(name = "pvp_base_data", schema = "", catalog = "super_star_fruit")
public class PVPBaseDataEntity {
    private int id;
    private String roleUid;

    private int rank;
    private int score;
    private int victory;
    private int fail;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "roleUid")
    public String getRoleUid() {
        return roleUid;
    }

    public void setRoleUid(String roleUid) {
        this.roleUid = roleUid;
    }

    @Basic
    @Column(name = "rank")
    public int getRank() {
        return rank;
    }

    public void setRank(int rank) {
        this.rank = rank;
    }

    @Basic
    @Column(name = "score")
    public int getScore() {
        return score;
    }

    public void setScore(int score) {
        this.score = score;
    }

    @Basic
    @Column(name = "victory")
    public int getVictory() {
        return victory;
    }

    public void setVictory(int victory) {
        this.victory = victory;
    }

    @Basic
    @Column(name = "fail")
    public int getFail() {
        return fail;
    }

    public void setFail(int fail) {
        this.fail = fail;
    }

    @Override
    public String toString() {
        return "PVPBaseDataEntity{" +
                "id=" + id +
                ", roleUid='" + roleUid + '\'' +
                ", rank=" + rank +
                ", score=" + score +
                ", victory=" + victory +
                ", fail=" + fail +
                '}';
    }
}
