package table.equitment_attribute;

import protocol.EquipAData;
import table.LineKey;

import java.lang.reflect.Array;
import java.util.*;

public class EquipmentAttributeLine implements LineKey {
    public int ID;
    public String name;
    public String type;
    public String design;
    public int sort;
    public String gender;
    public String source;
    public String describe;
    public String resource;
    public int level_required;

    public String hp;
    public String physical_attack;
    public String magical_attack;
    public String physical_defence;
    public String magical_defence;
    public String speed;

    public String extra_hp;
    public String extre_atk;
    public String extre_satk;
    public String extre_def;
    public String extre_sdef;
    public String extre_spd;

    @Override
    public int Key() {
        return ID;
    }

    public List<List<Integer>> hpList = new ArrayList<>();
    public List<List<Integer>> physical_attackList = new ArrayList<>();
    public List<List<Integer>> magical_attackList = new ArrayList<>();
    public List<List<Integer>> physical_defenceList = new ArrayList<>();
    public List<List<Integer>> magical_defenceList = new ArrayList<>();
    public List<List<Integer>> speedList = new ArrayList<>();

    /// <summary>
    /// extreDataList[0]  保存是哪个值 如：extra_hp extre_atk······
    /// </summary>
    public List<List<String>> extreDataList = new ArrayList<>();
    public void Parse() {
        // 额外词条处理
        {
            AddExtreDataList(extra_hp, "extra_hp");
            AddExtreDataList(extre_atk, "extre_atk");
            AddExtreDataList(extre_satk, "extre_satk");
            AddExtreDataList(extre_def, "extre_def");
            AddExtreDataList(extre_sdef, "extre_sdef");
            AddExtreDataList(extre_spd, "extre_spd");
        }

        {
            if (hp != null && hp.length() > 0) {
                String[] d1 = hp.split("\\|");
                for (int i = 0; i < d1.length; i++) {
                    hpList.add(new ArrayList<>());
                    String[] d2 = d1[i].split(",");
                    for (int j = 0; j < d2.length; j++) {
                        hpList.get(hpList.size() - 1).add(Integer.valueOf(d2[j]));
                    }
                }
            }
        }

        {
            if (physical_attack != null && physical_attack.length() > 0) {
                String[] d1 = physical_attack.split("\\|");
                for (int i = 0; i < d1.length; i++) {
                    physical_attackList.add(new ArrayList<>());
                    String[] d2 = d1[i].split(",");
                    for (int j = 0; j < d2.length; j++) {
//                        physical_attackList[physical_attackList.Count - 1].Add(int.Parse(d2[j]));
                        physical_attackList.get(physical_attackList.size() - 1).add(Integer.valueOf(d2[j]));
                    }
                }
            }
        }

        {
            if (magical_attack != null && magical_attack.length() > 0) {
                String[] d1 = magical_attack.split("\\|");
                for (int i = 0; i < d1.length; i++) {
                    magical_attackList.add(new ArrayList<>());
                    String[] d2 = d1[i].split(",");
                    for (int j = 0; j < d2.length; j++) {
//                        magical_attackList[magical_attackList.Count - 1].Add(int.Parse(d2[j]));
                        magical_attackList.get(magical_attackList.size() - 1).add(Integer.valueOf(d2[j]));
                    }
                }
            }
        }

        {
            if (physical_defence != null && physical_defence.length() > 0) {
                String[] d1 = physical_defence.split("\\|");
                for (int i = 0; i < d1.length; i++) {
                    physical_defenceList.add(new ArrayList<>());
                    String[] d2 = d1[i].split(",");
                    for (int j = 0; j < d2.length; j++) {
//                        physical_defenceList[physical_defenceList.Count - 1].Add(int.Parse(d2[j]));
                        physical_defenceList.get(physical_defenceList.size() - 1).add(Integer.valueOf(d2[j]));
                    }
                }
            }
        }

        {
            if (magical_defence != null && magical_defence.length() > 0) {
                String[] d1 = magical_defence.split("\\|");
                for (int i = 0; i < d1.length; i++) {
                    magical_defenceList.add(new ArrayList<>());
                    String[] d2 = d1[i].split(",");
                    for (int j = 0; j < d2.length; j++) {
//                        magical_defenceList[magical_defenceList.Count - 1].Add(int.Parse(d2[j]));
                        magical_defenceList.get(magical_defenceList.size() - 1).add(Integer.valueOf(d2[j]));
                    }
                }
            }
        }

        {
            if (speed != null && speed.length() > 0) {
                String[] d1 = speed.split("\\|");
                for (int i = 0; i < d1.length; i++) {
                    speedList.add(new ArrayList<>());
                    String[] d2 = d1[i].split(",");
                    for (int j = 0; j < d2.length; j++) {
//                        speedList[speedList.Count - 1].Add(int.Parse(d2[j]));
                        speedList.get(speedList.size() - 1).add(Integer.valueOf(d2[j]));
                    }
                }
            }
        }
    }

    public EquipAData.EquipA.Builder  GenerateEquip() {
//        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
        EquipAData.EquipA.Builder request = EquipAData.EquipA.newBuilder();
//        request.Eid = ID;

//        request.hp = GenerateRandomNum(hpList);
//        request.ad = GenerateRandomNum(physical_attackList);
//        request.ap = GenerateRandomNum(magical_attackList);
//        request.arm = GenerateRandomNum(physical_defenceList);
//        request.mdf = GenerateRandomNum(magical_defenceList);
//        request.speed = GenerateRandomNum(speedList);

        request.setEid(ID);
        request.setHp(GenerateRandomNum(hpList));
        request.setAd(GenerateRandomNum(physical_attackList));
        request.setAp(GenerateRandomNum(magical_attackList));
        request.setArm(GenerateRandomNum(physical_defenceList));
        request.setMdf(GenerateRandomNum(magical_defenceList));
        request.setSpeed(GenerateRandomNum(speedList));


        for (List<String> item : GenerateExtreData()) {
            switch (item.get(0)) {
                case "extra_hp":
//                    request.ehp = int.Parse(item[UnityEngine.Random.Range(1, item.Count)]);
                    request.setEhp(Integer.parseInt(item.get(new Random().nextInt((item.size() - 1)) + 1)));
                    break;
                case "extre_atk":
//                    request.ead = int.Parse(item[UnityEngine.Random.Range(1, item.Count)]);
                    request.setEad(Integer.parseInt(item.get(new Random().nextInt((item.size() - 1)) + 1)));
                    break;
                case "extre_satk":
//                    request.eap = int.Parse(item[UnityEngine.Random.Range(1, item.Count)]);
                    request.setEap(Integer.parseInt(item.get(new Random().nextInt((item.size() - 1)) + 1)));
                    break;
                case "extre_def":
//                    request.earm = int.Parse(item[UnityEngine.Random.Range(1, item.Count)]);
                    request.setEarm(Integer.parseInt(item.get(new Random().nextInt((item.size() - 1)) + 1)));
                    break;
                case "extre_sdef":
//                    request.emdf = int.Parse(item[UnityEngine.Random.Range(1, item.Count)]);
                    request.setEmdf(Integer.parseInt(item.get(new Random().nextInt((item.size() - 1)) + 1)));
                    break;
                case "extre_spd":
//                    request.esp = int.Parse(item[UnityEngine.Random.Range(1, item.Count)]);
                    request.setEsp(Integer.parseInt(item.get(new Random().nextInt((item.size() - 1)) + 1)));
                    break;
            }
        }

        return request;
    }

    private List<List<String>> GenerateExtreData() {
        List<List<String>> data = new ArrayList<>();
        if (extreDataList == null || extreDataList.size() <= 0) {
            return data;
        }
        int r = new Random().nextInt( 10);
        if (r < 1) {
            // 0
            //return null;
        } else if(r < 3) {
            // 1
            data.add(extreDataList.get(new Random().nextInt(extreDataList.size())));
        } else {
            //2
            if (extreDataList.size() == 1) {
                data.add(extreDataList.get(0));
            } else {
                int firstr = -1;
                int secondr = -1;
                firstr = new Random().nextInt(extreDataList.size());
                secondr = new Random().nextInt(extreDataList.size());
                while (firstr == secondr) {
                    secondr = new Random().nextInt(extreDataList.size());
                }

                data.add(extreDataList.get(firstr));
                data.add(extreDataList.get(secondr));
            }
        }
        return data;
    }
    private int GenerateRandomNum(List<List<Integer>> dataList){
        if (dataList == null || dataList.size() == 0) {
            return 0;
        }
        float totalWeight = 0;
        // 0 下标 1 权重
        List<float[]> weightValue = new ArrayList<>();
        for (int i = 0; i < dataList.size(); i++) {
            totalWeight += (float)dataList.get(i).get(2);
        }
        for (int i = 0; i < dataList.size(); i++) {
            weightValue.add(new float[] {i, (float)dataList.get(i).get(2) / totalWeight});
        }
//        weightValue.sort((x, y) => x[1].CompareTo(y[1]));
//        weightValue.sort(Comparator.comparing());
        Comparator<float[]> comparator = new Comparator<float[]>() {
            @Override
            public int compare(float[] o1, float[] o2) {
                return (int) (o1[1] - o2[1]);
            }
        };
        Collections.sort(weightValue, comparator);
        //Debug.LogError("权重：" + weightValue[0][1]);
        for (int i = 1; i < weightValue.size(); i++) {
            weightValue.get(i)[1] += weightValue.get(i - 1)[1];
            //Debug.LogError("权重：" + weightValue[i][1]);
        }

        int index = 0;
//        float r = UnityEngine.Random.Range(0.0f, 1.0f);
        float r = new Random().nextFloat();
        for (int i = 0; i < weightValue.size(); i++) {
            if (r < weightValue.get(i)[1]) {
                index = (int)weightValue.get(i)[0];
                //Debug.LogError("命中下标：" + index);
                //Debug.LogError("命中范围：" + dataList[index][0] + "  " + dataList[index][1]);
                break;
            }
        }


        int leftValue = dataList.get(index).get(0);
//        int rightValue = dataList[index][1] + 1;
        int rightValue = dataList.get(index).get(1) + 1;

        int min = leftValue <= rightValue ? leftValue : rightValue;
        int max = leftValue > rightValue ? leftValue : rightValue;
//        return UnityEngine.Random.Range(leftValue, rightValue); ;
        return new Random().nextInt((max - min)) + min;
    }
    private void AddExtreDataList(String data, String name) {
        if (data == null || data.length() == 0) {
            return;
        }
        List<String> d = new ArrayList<>();
        d.add(name);
//        foreach (var item in data.Split('\\|')) {
//            d.Add(item);
//        }
        for (String item:
             data.split("\\|")) {
            d.add(item);
        }
        extreDataList.add(d);
    }

    @Override
    public String toString() {
        return "EquipmentAttributeLine{" +
                "ID=" + ID +
                ", name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", design='" + design + '\'' +
                ", sort=" + sort +
                ", gender='" + gender + '\'' +
                ", source='" + source + '\'' +
                ", describe='" + describe + '\'' +
                ", resource='" + resource + '\'' +
                ", level_required=" + level_required +
                ", hp='" + hp + '\'' +
                ", physical_attack='" + physical_attack + '\'' +
                ", magical_attack='" + magical_attack + '\'' +
                ", physical_defence='" + physical_defence + '\'' +
                ", magical_defence='" + magical_defence + '\'' +
                ", speed='" + speed + '\'' +
                ", extra_hp='" + extra_hp + '\'' +
                ", extre_atk='" + extre_atk + '\'' +
                ", extre_satk='" + extre_satk + '\'' +
                ", extre_def='" + extre_def + '\'' +
                ", extre_sdef='" + extre_sdef + '\'' +
                ", extre_spd='" + extre_spd + '\'' +
                ", hpList=" + hpList +
                ", physical_attackList=" + physical_attackList +
                ", magical_attackList=" + magical_attackList +
                ", physical_defenceList=" + physical_defenceList +
                ", magical_defenceList=" + magical_defenceList +
                ", speedList=" + speedList +
                ", extreDataList=" + extreDataList +
                '}';
    }
}
