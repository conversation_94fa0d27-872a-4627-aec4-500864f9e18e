// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: onlineConfrontation.proto

package protocol;

public final class OnlineConfrontationData {
  private OnlineConfrontationData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  /**
   * Protobuf enum {@code protocol.CToS}
   */
  public enum CToS
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REQUESTGAMEMATCH = 10001;</code>
     *
     * <pre>
     *客户端请求游戏匹配
     * </pre>
     */
    REQUESTGAMEMATCH(0, 10001),
    ;

    /**
     * <code>REQUESTGAMEMATCH = 10001;</code>
     *
     * <pre>
     *客户端请求游戏匹配
     * </pre>
     */
    public static final int REQUESTGAMEMATCH_VALUE = 10001;


    public final int getNumber() { return value; }

    public static CToS valueOf(int value) {
      switch (value) {
        case 10001: return REQUESTGAMEMATCH;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<CToS>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<CToS>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<CToS>() {
            public CToS findValueByNumber(int number) {
              return CToS.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.getDescriptor().getEnumTypes().get(0);
    }

    private static final CToS[] VALUES = values();

    public static CToS valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private CToS(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.CToS)
  }

  /**
   * Protobuf enum {@code protocol.SToC}
   */
  public enum SToC
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>RESPONSEGAMEMATCH = 20001;</code>
     *
     * <pre>
     *通知客户端匹配成功
     * </pre>
     */
    RESPONSEGAMEMATCH(0, 20001),
    ;

    /**
     * <code>RESPONSEGAMEMATCH = 20001;</code>
     *
     * <pre>
     *通知客户端匹配成功
     * </pre>
     */
    public static final int RESPONSEGAMEMATCH_VALUE = 20001;


    public final int getNumber() { return value; }

    public static SToC valueOf(int value) {
      switch (value) {
        case 20001: return RESPONSEGAMEMATCH;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SToC>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<SToC>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SToC>() {
            public SToC findValueByNumber(int number) {
              return SToC.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.getDescriptor().getEnumTypes().get(1);
    }

    private static final SToC[] VALUES = values();

    public static SToC valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private SToC(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.SToC)
  }

  /**
   * Protobuf enum {@code protocol.MToS}
   */
  public enum MToS
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REPORTBATTLEONCALL = 10000;</code>
     *
     * <pre>
     *战斗服通知已经准备好
     * </pre>
     */
    REPORTBATTLEONCALL(0, 10000),
    /**
     * <code>RESPONSEALLOCATEROOM = 20002;</code>
     *
     * <pre>
     *回复主服务器分配房间的信息
     * </pre>
     */
    RESPONSEALLOCATEROOM(1, 20002),
    ;

    /**
     * <code>REPORTBATTLEONCALL = 10000;</code>
     *
     * <pre>
     *战斗服通知已经准备好
     * </pre>
     */
    public static final int REPORTBATTLEONCALL_VALUE = 10000;
    /**
     * <code>RESPONSEALLOCATEROOM = 20002;</code>
     *
     * <pre>
     *回复主服务器分配房间的信息
     * </pre>
     */
    public static final int RESPONSEALLOCATEROOM_VALUE = 20002;


    public final int getNumber() { return value; }

    public static MToS valueOf(int value) {
      switch (value) {
        case 10000: return REPORTBATTLEONCALL;
        case 20002: return RESPONSEALLOCATEROOM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MToS>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<MToS>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MToS>() {
            public MToS findValueByNumber(int number) {
              return MToS.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.getDescriptor().getEnumTypes().get(2);
    }

    private static final MToS[] VALUES = values();

    public static MToS valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private MToS(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.MToS)
  }

  /**
   * Protobuf enum {@code protocol.SToM}
   */
  public enum SToM
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REQUESTALLOCATEROOM = 10002;</code>
     *
     * <pre>
     *请求战斗服分配房间
     * </pre>
     */
    REQUESTALLOCATEROOM(0, 10002),
    ;

    /**
     * <code>REQUESTALLOCATEROOM = 10002;</code>
     *
     * <pre>
     *请求战斗服分配房间
     * </pre>
     */
    public static final int REQUESTALLOCATEROOM_VALUE = 10002;


    public final int getNumber() { return value; }

    public static SToM valueOf(int value) {
      switch (value) {
        case 10002: return REQUESTALLOCATEROOM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SToM>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<SToM>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SToM>() {
            public SToM findValueByNumber(int number) {
              return SToM.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.getDescriptor().getEnumTypes().get(3);
    }

    private static final SToM[] VALUES = values();

    public static SToM valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private SToM(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.SToM)
  }

  public interface RequestGameMatchOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGameMatch}
   *
   * <pre>
   *10001
   * </pre>
   */
  public static final class RequestGameMatch extends
      com.google.protobuf.GeneratedMessage
      implements RequestGameMatchOrBuilder {
    // Use RequestGameMatch.newBuilder() to construct.
    private RequestGameMatch(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGameMatch(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGameMatch defaultInstance;
    public static RequestGameMatch getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGameMatch getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGameMatch(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.internal_static_protocol_RequestGameMatch_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.OnlineConfrontationData.internal_static_protocol_RequestGameMatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.OnlineConfrontationData.RequestGameMatch.class, protocol.OnlineConfrontationData.RequestGameMatch.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGameMatch> PARSER =
        new com.google.protobuf.AbstractParser<RequestGameMatch>() {
      public RequestGameMatch parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGameMatch(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGameMatch> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.RequestGameMatch parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.OnlineConfrontationData.RequestGameMatch prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGameMatch}
     *
     * <pre>
     *10001
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.OnlineConfrontationData.RequestGameMatchOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.OnlineConfrontationData.internal_static_protocol_RequestGameMatch_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.OnlineConfrontationData.internal_static_protocol_RequestGameMatch_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.OnlineConfrontationData.RequestGameMatch.class, protocol.OnlineConfrontationData.RequestGameMatch.Builder.class);
      }

      // Construct using protocol.OnlineConfrontationData.RequestGameMatch.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.OnlineConfrontationData.internal_static_protocol_RequestGameMatch_descriptor;
      }

      public protocol.OnlineConfrontationData.RequestGameMatch getDefaultInstanceForType() {
        return protocol.OnlineConfrontationData.RequestGameMatch.getDefaultInstance();
      }

      public protocol.OnlineConfrontationData.RequestGameMatch build() {
        protocol.OnlineConfrontationData.RequestGameMatch result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.OnlineConfrontationData.RequestGameMatch buildPartial() {
        protocol.OnlineConfrontationData.RequestGameMatch result = new protocol.OnlineConfrontationData.RequestGameMatch(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.OnlineConfrontationData.RequestGameMatch) {
          return mergeFrom((protocol.OnlineConfrontationData.RequestGameMatch)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.OnlineConfrontationData.RequestGameMatch other) {
        if (other == protocol.OnlineConfrontationData.RequestGameMatch.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.OnlineConfrontationData.RequestGameMatch parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.OnlineConfrontationData.RequestGameMatch) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGameMatch)
    }

    static {
      defaultInstance = new RequestGameMatch(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGameMatch)
  }

  public interface ResponseGameMatchOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string ip = 1;
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    boolean hasIp();
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    java.lang.String getIp();
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    com.google.protobuf.ByteString
        getIpBytes();

    // required int32 port = 2;
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    boolean hasPort();
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    int getPort();

    // required string key = 3;
    /**
     * <code>required string key = 3;</code>
     */
    boolean hasKey();
    /**
     * <code>required string key = 3;</code>
     */
    java.lang.String getKey();
    /**
     * <code>required string key = 3;</code>
     */
    com.google.protobuf.ByteString
        getKeyBytes();
  }
  /**
   * Protobuf type {@code protocol.ResponseGameMatch}
   *
   * <pre>
   *20001
   * </pre>
   */
  public static final class ResponseGameMatch extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGameMatchOrBuilder {
    // Use ResponseGameMatch.newBuilder() to construct.
    private ResponseGameMatch(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGameMatch(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGameMatch defaultInstance;
    public static ResponseGameMatch getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGameMatch getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGameMatch(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              ip_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              port_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              key_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.internal_static_protocol_ResponseGameMatch_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.OnlineConfrontationData.internal_static_protocol_ResponseGameMatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.OnlineConfrontationData.ResponseGameMatch.class, protocol.OnlineConfrontationData.ResponseGameMatch.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGameMatch> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGameMatch>() {
      public ResponseGameMatch parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGameMatch(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGameMatch> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string ip = 1;
    public static final int IP_FIELD_NUMBER = 1;
    private java.lang.Object ip_;
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 port = 2;
    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    public int getPort() {
      return port_;
    }

    // required string key = 3;
    public static final int KEY_FIELD_NUMBER = 3;
    private java.lang.Object key_;
    /**
     * <code>required string key = 3;</code>
     */
    public boolean hasKey() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string key = 3;</code>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          key_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string key = 3;</code>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      ip_ = "";
      port_ = 0;
      key_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasIp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPort()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getIpBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getKeyBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getIpBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getKeyBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.ResponseGameMatch parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.OnlineConfrontationData.ResponseGameMatch prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGameMatch}
     *
     * <pre>
     *20001
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.OnlineConfrontationData.ResponseGameMatchOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ResponseGameMatch_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ResponseGameMatch_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.OnlineConfrontationData.ResponseGameMatch.class, protocol.OnlineConfrontationData.ResponseGameMatch.Builder.class);
      }

      // Construct using protocol.OnlineConfrontationData.ResponseGameMatch.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        ip_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        port_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        key_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ResponseGameMatch_descriptor;
      }

      public protocol.OnlineConfrontationData.ResponseGameMatch getDefaultInstanceForType() {
        return protocol.OnlineConfrontationData.ResponseGameMatch.getDefaultInstance();
      }

      public protocol.OnlineConfrontationData.ResponseGameMatch build() {
        protocol.OnlineConfrontationData.ResponseGameMatch result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.OnlineConfrontationData.ResponseGameMatch buildPartial() {
        protocol.OnlineConfrontationData.ResponseGameMatch result = new protocol.OnlineConfrontationData.ResponseGameMatch(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.port_ = port_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.key_ = key_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.OnlineConfrontationData.ResponseGameMatch) {
          return mergeFrom((protocol.OnlineConfrontationData.ResponseGameMatch)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.OnlineConfrontationData.ResponseGameMatch other) {
        if (other == protocol.OnlineConfrontationData.ResponseGameMatch.getDefaultInstance()) return this;
        if (other.hasIp()) {
          bitField0_ |= 0x00000001;
          ip_ = other.ip_;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasKey()) {
          bitField0_ |= 0x00000004;
          key_ = other.key_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasIp()) {
          
          return false;
        }
        if (!hasPort()) {
          
          return false;
        }
        if (!hasKey()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.OnlineConfrontationData.ResponseGameMatch parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.OnlineConfrontationData.ResponseGameMatch) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string ip = 1;
      private java.lang.Object ip_ = "";
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          ip_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ip_ = getDefaultInstance().getIp();
        onChanged();
        return this;
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }

      // required int32 port = 2;
      private int port_ ;
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public int getPort() {
        return port_;
      }
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public Builder setPort(int value) {
        bitField0_ |= 0x00000002;
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      // required string key = 3;
      private java.lang.Object key_ = "";
      /**
       * <code>required string key = 3;</code>
       */
      public boolean hasKey() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string key = 3;</code>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string key = 3;</code>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string key = 3;</code>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 3;</code>
       */
      public Builder clearKey() {
        bitField0_ = (bitField0_ & ~0x00000004);
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 3;</code>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        key_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGameMatch)
    }

    static {
      defaultInstance = new ResponseGameMatch(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGameMatch)
  }

  public interface ReportBattleOnCallOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 portNums = 1;
    /**
     * <code>required int32 portNums = 1;</code>
     *
     * <pre>
     *战斗服的数量
     * </pre>
     */
    boolean hasPortNums();
    /**
     * <code>required int32 portNums = 1;</code>
     *
     * <pre>
     *战斗服的数量
     * </pre>
     */
    int getPortNums();
  }
  /**
   * Protobuf type {@code protocol.ReportBattleOnCall}
   *
   * <pre>
   *10000
   * </pre>
   */
  public static final class ReportBattleOnCall extends
      com.google.protobuf.GeneratedMessage
      implements ReportBattleOnCallOrBuilder {
    // Use ReportBattleOnCall.newBuilder() to construct.
    private ReportBattleOnCall(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ReportBattleOnCall(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ReportBattleOnCall defaultInstance;
    public static ReportBattleOnCall getDefaultInstance() {
      return defaultInstance;
    }

    public ReportBattleOnCall getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ReportBattleOnCall(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              portNums_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.internal_static_protocol_ReportBattleOnCall_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.OnlineConfrontationData.internal_static_protocol_ReportBattleOnCall_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.OnlineConfrontationData.ReportBattleOnCall.class, protocol.OnlineConfrontationData.ReportBattleOnCall.Builder.class);
    }

    public static com.google.protobuf.Parser<ReportBattleOnCall> PARSER =
        new com.google.protobuf.AbstractParser<ReportBattleOnCall>() {
      public ReportBattleOnCall parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReportBattleOnCall(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ReportBattleOnCall> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 portNums = 1;
    public static final int PORTNUMS_FIELD_NUMBER = 1;
    private int portNums_;
    /**
     * <code>required int32 portNums = 1;</code>
     *
     * <pre>
     *战斗服的数量
     * </pre>
     */
    public boolean hasPortNums() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 portNums = 1;</code>
     *
     * <pre>
     *战斗服的数量
     * </pre>
     */
    public int getPortNums() {
      return portNums_;
    }

    private void initFields() {
      portNums_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPortNums()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, portNums_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, portNums_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.ReportBattleOnCall parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.OnlineConfrontationData.ReportBattleOnCall prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ReportBattleOnCall}
     *
     * <pre>
     *10000
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.OnlineConfrontationData.ReportBattleOnCallOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ReportBattleOnCall_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ReportBattleOnCall_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.OnlineConfrontationData.ReportBattleOnCall.class, protocol.OnlineConfrontationData.ReportBattleOnCall.Builder.class);
      }

      // Construct using protocol.OnlineConfrontationData.ReportBattleOnCall.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        portNums_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ReportBattleOnCall_descriptor;
      }

      public protocol.OnlineConfrontationData.ReportBattleOnCall getDefaultInstanceForType() {
        return protocol.OnlineConfrontationData.ReportBattleOnCall.getDefaultInstance();
      }

      public protocol.OnlineConfrontationData.ReportBattleOnCall build() {
        protocol.OnlineConfrontationData.ReportBattleOnCall result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.OnlineConfrontationData.ReportBattleOnCall buildPartial() {
        protocol.OnlineConfrontationData.ReportBattleOnCall result = new protocol.OnlineConfrontationData.ReportBattleOnCall(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.portNums_ = portNums_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.OnlineConfrontationData.ReportBattleOnCall) {
          return mergeFrom((protocol.OnlineConfrontationData.ReportBattleOnCall)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.OnlineConfrontationData.ReportBattleOnCall other) {
        if (other == protocol.OnlineConfrontationData.ReportBattleOnCall.getDefaultInstance()) return this;
        if (other.hasPortNums()) {
          setPortNums(other.getPortNums());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPortNums()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.OnlineConfrontationData.ReportBattleOnCall parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.OnlineConfrontationData.ReportBattleOnCall) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 portNums = 1;
      private int portNums_ ;
      /**
       * <code>required int32 portNums = 1;</code>
       *
       * <pre>
       *战斗服的数量
       * </pre>
       */
      public boolean hasPortNums() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 portNums = 1;</code>
       *
       * <pre>
       *战斗服的数量
       * </pre>
       */
      public int getPortNums() {
        return portNums_;
      }
      /**
       * <code>required int32 portNums = 1;</code>
       *
       * <pre>
       *战斗服的数量
       * </pre>
       */
      public Builder setPortNums(int value) {
        bitField0_ |= 0x00000001;
        portNums_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 portNums = 1;</code>
       *
       * <pre>
       *战斗服的数量
       * </pre>
       */
      public Builder clearPortNums() {
        bitField0_ = (bitField0_ & ~0x00000001);
        portNums_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ReportBattleOnCall)
    }

    static {
      defaultInstance = new ReportBattleOnCall(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ReportBattleOnCall)
  }

  public interface ResponseAllocateRoomOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string ip = 1;
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    boolean hasIp();
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    java.lang.String getIp();
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    com.google.protobuf.ByteString
        getIpBytes();

    // required int32 port = 2;
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    boolean hasPort();
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    int getPort();

    // required string key = 3;
    /**
     * <code>required string key = 3;</code>
     */
    boolean hasKey();
    /**
     * <code>required string key = 3;</code>
     */
    java.lang.String getKey();
    /**
     * <code>required string key = 3;</code>
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    // required int32 roomIndex = 4;
    /**
     * <code>required int32 roomIndex = 4;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    boolean hasRoomIndex();
    /**
     * <code>required int32 roomIndex = 4;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    int getRoomIndex();
  }
  /**
   * Protobuf type {@code protocol.ResponseAllocateRoom}
   *
   * <pre>
   *20002
   * </pre>
   */
  public static final class ResponseAllocateRoom extends
      com.google.protobuf.GeneratedMessage
      implements ResponseAllocateRoomOrBuilder {
    // Use ResponseAllocateRoom.newBuilder() to construct.
    private ResponseAllocateRoom(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseAllocateRoom(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseAllocateRoom defaultInstance;
    public static ResponseAllocateRoom getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseAllocateRoom getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseAllocateRoom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              ip_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              port_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              key_ = input.readBytes();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              roomIndex_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.internal_static_protocol_ResponseAllocateRoom_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.OnlineConfrontationData.internal_static_protocol_ResponseAllocateRoom_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.OnlineConfrontationData.ResponseAllocateRoom.class, protocol.OnlineConfrontationData.ResponseAllocateRoom.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseAllocateRoom> PARSER =
        new com.google.protobuf.AbstractParser<ResponseAllocateRoom>() {
      public ResponseAllocateRoom parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseAllocateRoom(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseAllocateRoom> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string ip = 1;
    public static final int IP_FIELD_NUMBER = 1;
    private java.lang.Object ip_;
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    public boolean hasIp() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    public java.lang.String getIp() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ip_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string ip = 1;</code>
     *
     * <pre>
     *战斗服的ip
     * </pre>
     */
    public com.google.protobuf.ByteString
        getIpBytes() {
      java.lang.Object ref = ip_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ip_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 port = 2;
    public static final int PORT_FIELD_NUMBER = 2;
    private int port_;
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    public boolean hasPort() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 port = 2;</code>
     *
     * <pre>
     *端口号
     * </pre>
     */
    public int getPort() {
      return port_;
    }

    // required string key = 3;
    public static final int KEY_FIELD_NUMBER = 3;
    private java.lang.Object key_;
    /**
     * <code>required string key = 3;</code>
     */
    public boolean hasKey() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string key = 3;</code>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          key_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string key = 3;</code>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 roomIndex = 4;
    public static final int ROOMINDEX_FIELD_NUMBER = 4;
    private int roomIndex_;
    /**
     * <code>required int32 roomIndex = 4;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    public boolean hasRoomIndex() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 roomIndex = 4;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    public int getRoomIndex() {
      return roomIndex_;
    }

    private void initFields() {
      ip_ = "";
      port_ = 0;
      key_ = "";
      roomIndex_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasIp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPort()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRoomIndex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getIpBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, port_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getKeyBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, roomIndex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getIpBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, port_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getKeyBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, roomIndex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.ResponseAllocateRoom parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.OnlineConfrontationData.ResponseAllocateRoom prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseAllocateRoom}
     *
     * <pre>
     *20002
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.OnlineConfrontationData.ResponseAllocateRoomOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ResponseAllocateRoom_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ResponseAllocateRoom_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.OnlineConfrontationData.ResponseAllocateRoom.class, protocol.OnlineConfrontationData.ResponseAllocateRoom.Builder.class);
      }

      // Construct using protocol.OnlineConfrontationData.ResponseAllocateRoom.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        ip_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        port_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        key_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        roomIndex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.OnlineConfrontationData.internal_static_protocol_ResponseAllocateRoom_descriptor;
      }

      public protocol.OnlineConfrontationData.ResponseAllocateRoom getDefaultInstanceForType() {
        return protocol.OnlineConfrontationData.ResponseAllocateRoom.getDefaultInstance();
      }

      public protocol.OnlineConfrontationData.ResponseAllocateRoom build() {
        protocol.OnlineConfrontationData.ResponseAllocateRoom result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.OnlineConfrontationData.ResponseAllocateRoom buildPartial() {
        protocol.OnlineConfrontationData.ResponseAllocateRoom result = new protocol.OnlineConfrontationData.ResponseAllocateRoom(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.ip_ = ip_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.port_ = port_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.key_ = key_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.roomIndex_ = roomIndex_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.OnlineConfrontationData.ResponseAllocateRoom) {
          return mergeFrom((protocol.OnlineConfrontationData.ResponseAllocateRoom)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.OnlineConfrontationData.ResponseAllocateRoom other) {
        if (other == protocol.OnlineConfrontationData.ResponseAllocateRoom.getDefaultInstance()) return this;
        if (other.hasIp()) {
          bitField0_ |= 0x00000001;
          ip_ = other.ip_;
          onChanged();
        }
        if (other.hasPort()) {
          setPort(other.getPort());
        }
        if (other.hasKey()) {
          bitField0_ |= 0x00000004;
          key_ = other.key_;
          onChanged();
        }
        if (other.hasRoomIndex()) {
          setRoomIndex(other.getRoomIndex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasIp()) {
          
          return false;
        }
        if (!hasPort()) {
          
          return false;
        }
        if (!hasKey()) {
          
          return false;
        }
        if (!hasRoomIndex()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.OnlineConfrontationData.ResponseAllocateRoom parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.OnlineConfrontationData.ResponseAllocateRoom) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string ip = 1;
      private java.lang.Object ip_ = "";
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public boolean hasIp() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public java.lang.String getIp() {
        java.lang.Object ref = ip_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          ip_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public com.google.protobuf.ByteString
          getIpBytes() {
        java.lang.Object ref = ip_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ip_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public Builder setIp(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public Builder clearIp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ip_ = getDefaultInstance().getIp();
        onChanged();
        return this;
      }
      /**
       * <code>required string ip = 1;</code>
       *
       * <pre>
       *战斗服的ip
       * </pre>
       */
      public Builder setIpBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        ip_ = value;
        onChanged();
        return this;
      }

      // required int32 port = 2;
      private int port_ ;
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public boolean hasPort() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public int getPort() {
        return port_;
      }
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public Builder setPort(int value) {
        bitField0_ |= 0x00000002;
        port_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 port = 2;</code>
       *
       * <pre>
       *端口号
       * </pre>
       */
      public Builder clearPort() {
        bitField0_ = (bitField0_ & ~0x00000002);
        port_ = 0;
        onChanged();
        return this;
      }

      // required string key = 3;
      private java.lang.Object key_ = "";
      /**
       * <code>required string key = 3;</code>
       */
      public boolean hasKey() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string key = 3;</code>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string key = 3;</code>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string key = 3;</code>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 3;</code>
       */
      public Builder clearKey() {
        bitField0_ = (bitField0_ & ~0x00000004);
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 3;</code>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        key_ = value;
        onChanged();
        return this;
      }

      // required int32 roomIndex = 4;
      private int roomIndex_ ;
      /**
       * <code>required int32 roomIndex = 4;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public boolean hasRoomIndex() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 roomIndex = 4;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public int getRoomIndex() {
        return roomIndex_;
      }
      /**
       * <code>required int32 roomIndex = 4;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public Builder setRoomIndex(int value) {
        bitField0_ |= 0x00000008;
        roomIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roomIndex = 4;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public Builder clearRoomIndex() {
        bitField0_ = (bitField0_ & ~0x00000008);
        roomIndex_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseAllocateRoom)
    }

    static {
      defaultInstance = new ResponseAllocateRoom(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseAllocateRoom)
  }

  public interface RequestAllocateRoomOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 roomIndex = 1;
    /**
     * <code>required int32 roomIndex = 1;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    boolean hasRoomIndex();
    /**
     * <code>required int32 roomIndex = 1;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    int getRoomIndex();
  }
  /**
   * Protobuf type {@code protocol.RequestAllocateRoom}
   *
   * <pre>
   *10002
   * </pre>
   */
  public static final class RequestAllocateRoom extends
      com.google.protobuf.GeneratedMessage
      implements RequestAllocateRoomOrBuilder {
    // Use RequestAllocateRoom.newBuilder() to construct.
    private RequestAllocateRoom(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestAllocateRoom(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestAllocateRoom defaultInstance;
    public static RequestAllocateRoom getDefaultInstance() {
      return defaultInstance;
    }

    public RequestAllocateRoom getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestAllocateRoom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              roomIndex_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.OnlineConfrontationData.internal_static_protocol_RequestAllocateRoom_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.OnlineConfrontationData.internal_static_protocol_RequestAllocateRoom_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.OnlineConfrontationData.RequestAllocateRoom.class, protocol.OnlineConfrontationData.RequestAllocateRoom.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestAllocateRoom> PARSER =
        new com.google.protobuf.AbstractParser<RequestAllocateRoom>() {
      public RequestAllocateRoom parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestAllocateRoom(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestAllocateRoom> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 roomIndex = 1;
    public static final int ROOMINDEX_FIELD_NUMBER = 1;
    private int roomIndex_;
    /**
     * <code>required int32 roomIndex = 1;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    public boolean hasRoomIndex() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 roomIndex = 1;</code>
     *
     * <pre>
     *分配的房间索引
     * </pre>
     */
    public int getRoomIndex() {
      return roomIndex_;
    }

    private void initFields() {
      roomIndex_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRoomIndex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, roomIndex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, roomIndex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.OnlineConfrontationData.RequestAllocateRoom parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.OnlineConfrontationData.RequestAllocateRoom prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestAllocateRoom}
     *
     * <pre>
     *10002
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.OnlineConfrontationData.RequestAllocateRoomOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.OnlineConfrontationData.internal_static_protocol_RequestAllocateRoom_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.OnlineConfrontationData.internal_static_protocol_RequestAllocateRoom_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.OnlineConfrontationData.RequestAllocateRoom.class, protocol.OnlineConfrontationData.RequestAllocateRoom.Builder.class);
      }

      // Construct using protocol.OnlineConfrontationData.RequestAllocateRoom.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        roomIndex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.OnlineConfrontationData.internal_static_protocol_RequestAllocateRoom_descriptor;
      }

      public protocol.OnlineConfrontationData.RequestAllocateRoom getDefaultInstanceForType() {
        return protocol.OnlineConfrontationData.RequestAllocateRoom.getDefaultInstance();
      }

      public protocol.OnlineConfrontationData.RequestAllocateRoom build() {
        protocol.OnlineConfrontationData.RequestAllocateRoom result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.OnlineConfrontationData.RequestAllocateRoom buildPartial() {
        protocol.OnlineConfrontationData.RequestAllocateRoom result = new protocol.OnlineConfrontationData.RequestAllocateRoom(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.roomIndex_ = roomIndex_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.OnlineConfrontationData.RequestAllocateRoom) {
          return mergeFrom((protocol.OnlineConfrontationData.RequestAllocateRoom)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.OnlineConfrontationData.RequestAllocateRoom other) {
        if (other == protocol.OnlineConfrontationData.RequestAllocateRoom.getDefaultInstance()) return this;
        if (other.hasRoomIndex()) {
          setRoomIndex(other.getRoomIndex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRoomIndex()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.OnlineConfrontationData.RequestAllocateRoom parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.OnlineConfrontationData.RequestAllocateRoom) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 roomIndex = 1;
      private int roomIndex_ ;
      /**
       * <code>required int32 roomIndex = 1;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public boolean hasRoomIndex() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 roomIndex = 1;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public int getRoomIndex() {
        return roomIndex_;
      }
      /**
       * <code>required int32 roomIndex = 1;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public Builder setRoomIndex(int value) {
        bitField0_ |= 0x00000001;
        roomIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roomIndex = 1;</code>
       *
       * <pre>
       *分配的房间索引
       * </pre>
       */
      public Builder clearRoomIndex() {
        bitField0_ = (bitField0_ & ~0x00000001);
        roomIndex_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestAllocateRoom)
    }

    static {
      defaultInstance = new RequestAllocateRoom(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestAllocateRoom)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGameMatch_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGameMatch_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGameMatch_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGameMatch_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ReportBattleOnCall_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ReportBattleOnCall_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseAllocateRoom_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseAllocateRoom_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestAllocateRoom_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestAllocateRoom_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031onlineConfrontation.proto\022\010protocol\"\022\n" +
      "\020RequestGameMatch\":\n\021ResponseGameMatch\022\n" +
      "\n\002ip\030\001 \002(\t\022\014\n\004port\030\002 \002(\005\022\013\n\003key\030\003 \002(\t\"&\n" +
      "\022ReportBattleOnCall\022\020\n\010portNums\030\001 \002(\005\"P\n" +
      "\024ResponseAllocateRoom\022\n\n\002ip\030\001 \002(\t\022\014\n\004por" +
      "t\030\002 \002(\005\022\013\n\003key\030\003 \002(\t\022\021\n\troomIndex\030\004 \002(\005\"" +
      "(\n\023RequestAllocateRoom\022\021\n\troomIndex\030\001 \002(" +
      "\005*\035\n\004CToS\022\025\n\020REQUESTGAMEMATCH\020\221N*\037\n\004SToC" +
      "\022\027\n\021RESPONSEGAMEMATCH\020\241\234\001*;\n\004MToS\022\027\n\022REP" +
      "ORTBATTLEONCALL\020\220N\022\032\n\024RESPONSEALLOCATERO",
      "OM\020\242\234\001* \n\004SToM\022\030\n\023REQUESTALLOCATEROOM\020\222N" +
      "B\031B\027OnlineConfrontationData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestGameMatch_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestGameMatch_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGameMatch_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGameMatch_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseGameMatch_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGameMatch_descriptor,
              new java.lang.String[] { "Ip", "Port", "Key", });
          internal_static_protocol_ReportBattleOnCall_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ReportBattleOnCall_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ReportBattleOnCall_descriptor,
              new java.lang.String[] { "PortNums", });
          internal_static_protocol_ResponseAllocateRoom_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseAllocateRoom_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseAllocateRoom_descriptor,
              new java.lang.String[] { "Ip", "Port", "Key", "RoomIndex", });
          internal_static_protocol_RequestAllocateRoom_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_RequestAllocateRoom_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestAllocateRoom_descriptor,
              new java.lang.String[] { "RoomIndex", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
