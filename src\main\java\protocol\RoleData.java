// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rexp.proto

package protocol;

public final class RoleData {
  private RoleData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestOperateRoleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 roleExp = 1;
    /**
     * <code>required int32 roleExp = 1;</code>
     */
    boolean hasRoleExp();
    /**
     * <code>required int32 roleExp = 1;</code>
     */
    int getRoleExp();
  }
  /**
   * Protobuf type {@code protocol.RequestOperateRole}
   *
   * <pre>
   *1311
   * </pre>
   */
  public static final class RequestOperateRole extends
      com.google.protobuf.GeneratedMessage
      implements RequestOperateRoleOrBuilder {
    // Use RequestOperateRole.newBuilder() to construct.
    private RequestOperateRole(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestOperateRole(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestOperateRole defaultInstance;
    public static RequestOperateRole getDefaultInstance() {
      return defaultInstance;
    }

    public RequestOperateRole getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestOperateRole(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              roleExp_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RoleData.internal_static_protocol_RequestOperateRole_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RoleData.internal_static_protocol_RequestOperateRole_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RoleData.RequestOperateRole.class, protocol.RoleData.RequestOperateRole.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestOperateRole> PARSER =
        new com.google.protobuf.AbstractParser<RequestOperateRole>() {
      public RequestOperateRole parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestOperateRole(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestOperateRole> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 roleExp = 1;
    public static final int ROLEEXP_FIELD_NUMBER = 1;
    private int roleExp_;
    /**
     * <code>required int32 roleExp = 1;</code>
     */
    public boolean hasRoleExp() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 roleExp = 1;</code>
     */
    public int getRoleExp() {
      return roleExp_;
    }

    private void initFields() {
      roleExp_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRoleExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, roleExp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, roleExp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RoleData.RequestOperateRole parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.RequestOperateRole parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RoleData.RequestOperateRole parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.RequestOperateRole parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RoleData.RequestOperateRole prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestOperateRole}
     *
     * <pre>
     *1311
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RoleData.RequestOperateRoleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RoleData.internal_static_protocol_RequestOperateRole_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RoleData.internal_static_protocol_RequestOperateRole_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RoleData.RequestOperateRole.class, protocol.RoleData.RequestOperateRole.Builder.class);
      }

      // Construct using protocol.RoleData.RequestOperateRole.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        roleExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RoleData.internal_static_protocol_RequestOperateRole_descriptor;
      }

      public protocol.RoleData.RequestOperateRole getDefaultInstanceForType() {
        return protocol.RoleData.RequestOperateRole.getDefaultInstance();
      }

      public protocol.RoleData.RequestOperateRole build() {
        protocol.RoleData.RequestOperateRole result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RoleData.RequestOperateRole buildPartial() {
        protocol.RoleData.RequestOperateRole result = new protocol.RoleData.RequestOperateRole(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.roleExp_ = roleExp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RoleData.RequestOperateRole) {
          return mergeFrom((protocol.RoleData.RequestOperateRole)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RoleData.RequestOperateRole other) {
        if (other == protocol.RoleData.RequestOperateRole.getDefaultInstance()) return this;
        if (other.hasRoleExp()) {
          setRoleExp(other.getRoleExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRoleExp()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RoleData.RequestOperateRole parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RoleData.RequestOperateRole) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 roleExp = 1;
      private int roleExp_ ;
      /**
       * <code>required int32 roleExp = 1;</code>
       */
      public boolean hasRoleExp() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 roleExp = 1;</code>
       */
      public int getRoleExp() {
        return roleExp_;
      }
      /**
       * <code>required int32 roleExp = 1;</code>
       */
      public Builder setRoleExp(int value) {
        bitField0_ |= 0x00000001;
        roleExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roleExp = 1;</code>
       */
      public Builder clearRoleExp() {
        bitField0_ = (bitField0_ & ~0x00000001);
        roleExp_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestOperateRole)
    }

    static {
      defaultInstance = new RequestOperateRole(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestOperateRole)
  }

  public interface ResponseOperateRoleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.PlainRole role = 3;
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    java.util.List<protocol.RoleData.PlainRole> 
        getRoleList();
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    protocol.RoleData.PlainRole getRole(int index);
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    int getRoleCount();
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    java.util.List<? extends protocol.RoleData.PlainRoleOrBuilder> 
        getRoleOrBuilderList();
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    protocol.RoleData.PlainRoleOrBuilder getRoleOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseOperateRole}
   *
   * <pre>
   *2311
   * </pre>
   */
  public static final class ResponseOperateRole extends
      com.google.protobuf.GeneratedMessage
      implements ResponseOperateRoleOrBuilder {
    // Use ResponseOperateRole.newBuilder() to construct.
    private ResponseOperateRole(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseOperateRole(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseOperateRole defaultInstance;
    public static ResponseOperateRole getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseOperateRole getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseOperateRole(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                role_ = new java.util.ArrayList<protocol.RoleData.PlainRole>();
                mutable_bitField0_ |= 0x00000001;
              }
              role_.add(input.readMessage(protocol.RoleData.PlainRole.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          role_ = java.util.Collections.unmodifiableList(role_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RoleData.internal_static_protocol_ResponseOperateRole_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RoleData.internal_static_protocol_ResponseOperateRole_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RoleData.ResponseOperateRole.class, protocol.RoleData.ResponseOperateRole.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseOperateRole> PARSER =
        new com.google.protobuf.AbstractParser<ResponseOperateRole>() {
      public ResponseOperateRole parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseOperateRole(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseOperateRole> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.PlainRole role = 3;
    public static final int ROLE_FIELD_NUMBER = 3;
    private java.util.List<protocol.RoleData.PlainRole> role_;
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    public java.util.List<protocol.RoleData.PlainRole> getRoleList() {
      return role_;
    }
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    public java.util.List<? extends protocol.RoleData.PlainRoleOrBuilder> 
        getRoleOrBuilderList() {
      return role_;
    }
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    public int getRoleCount() {
      return role_.size();
    }
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    public protocol.RoleData.PlainRole getRole(int index) {
      return role_.get(index);
    }
    /**
     * <code>repeated .protocol.PlainRole role = 3;</code>
     */
    public protocol.RoleData.PlainRoleOrBuilder getRoleOrBuilder(
        int index) {
      return role_.get(index);
    }

    private void initFields() {
      role_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getRoleCount(); i++) {
        if (!getRole(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < role_.size(); i++) {
        output.writeMessage(3, role_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < role_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, role_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RoleData.ResponseOperateRole parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.ResponseOperateRole parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RoleData.ResponseOperateRole parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.ResponseOperateRole parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RoleData.ResponseOperateRole prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseOperateRole}
     *
     * <pre>
     *2311
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RoleData.ResponseOperateRoleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RoleData.internal_static_protocol_ResponseOperateRole_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RoleData.internal_static_protocol_ResponseOperateRole_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RoleData.ResponseOperateRole.class, protocol.RoleData.ResponseOperateRole.Builder.class);
      }

      // Construct using protocol.RoleData.ResponseOperateRole.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRoleFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (roleBuilder_ == null) {
          role_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          roleBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RoleData.internal_static_protocol_ResponseOperateRole_descriptor;
      }

      public protocol.RoleData.ResponseOperateRole getDefaultInstanceForType() {
        return protocol.RoleData.ResponseOperateRole.getDefaultInstance();
      }

      public protocol.RoleData.ResponseOperateRole build() {
        protocol.RoleData.ResponseOperateRole result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RoleData.ResponseOperateRole buildPartial() {
        protocol.RoleData.ResponseOperateRole result = new protocol.RoleData.ResponseOperateRole(this);
        int from_bitField0_ = bitField0_;
        if (roleBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            role_ = java.util.Collections.unmodifiableList(role_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.role_ = role_;
        } else {
          result.role_ = roleBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RoleData.ResponseOperateRole) {
          return mergeFrom((protocol.RoleData.ResponseOperateRole)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RoleData.ResponseOperateRole other) {
        if (other == protocol.RoleData.ResponseOperateRole.getDefaultInstance()) return this;
        if (roleBuilder_ == null) {
          if (!other.role_.isEmpty()) {
            if (role_.isEmpty()) {
              role_ = other.role_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRoleIsMutable();
              role_.addAll(other.role_);
            }
            onChanged();
          }
        } else {
          if (!other.role_.isEmpty()) {
            if (roleBuilder_.isEmpty()) {
              roleBuilder_.dispose();
              roleBuilder_ = null;
              role_ = other.role_;
              bitField0_ = (bitField0_ & ~0x00000001);
              roleBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRoleFieldBuilder() : null;
            } else {
              roleBuilder_.addAllMessages(other.role_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getRoleCount(); i++) {
          if (!getRole(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RoleData.ResponseOperateRole parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RoleData.ResponseOperateRole) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.PlainRole role = 3;
      private java.util.List<protocol.RoleData.PlainRole> role_ =
        java.util.Collections.emptyList();
      private void ensureRoleIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          role_ = new java.util.ArrayList<protocol.RoleData.PlainRole>(role_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RoleData.PlainRole, protocol.RoleData.PlainRole.Builder, protocol.RoleData.PlainRoleOrBuilder> roleBuilder_;

      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public java.util.List<protocol.RoleData.PlainRole> getRoleList() {
        if (roleBuilder_ == null) {
          return java.util.Collections.unmodifiableList(role_);
        } else {
          return roleBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public int getRoleCount() {
        if (roleBuilder_ == null) {
          return role_.size();
        } else {
          return roleBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public protocol.RoleData.PlainRole getRole(int index) {
        if (roleBuilder_ == null) {
          return role_.get(index);
        } else {
          return roleBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder setRole(
          int index, protocol.RoleData.PlainRole value) {
        if (roleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIsMutable();
          role_.set(index, value);
          onChanged();
        } else {
          roleBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder setRole(
          int index, protocol.RoleData.PlainRole.Builder builderForValue) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder addRole(protocol.RoleData.PlainRole value) {
        if (roleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIsMutable();
          role_.add(value);
          onChanged();
        } else {
          roleBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder addRole(
          int index, protocol.RoleData.PlainRole value) {
        if (roleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIsMutable();
          role_.add(index, value);
          onChanged();
        } else {
          roleBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder addRole(
          protocol.RoleData.PlainRole.Builder builderForValue) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.add(builderForValue.build());
          onChanged();
        } else {
          roleBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder addRole(
          int index, protocol.RoleData.PlainRole.Builder builderForValue) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder addAllRole(
          java.lang.Iterable<? extends protocol.RoleData.PlainRole> values) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          super.addAll(values, role_);
          onChanged();
        } else {
          roleBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder clearRole() {
        if (roleBuilder_ == null) {
          role_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          roleBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public Builder removeRole(int index) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.remove(index);
          onChanged();
        } else {
          roleBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public protocol.RoleData.PlainRole.Builder getRoleBuilder(
          int index) {
        return getRoleFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public protocol.RoleData.PlainRoleOrBuilder getRoleOrBuilder(
          int index) {
        if (roleBuilder_ == null) {
          return role_.get(index);  } else {
          return roleBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public java.util.List<? extends protocol.RoleData.PlainRoleOrBuilder> 
           getRoleOrBuilderList() {
        if (roleBuilder_ != null) {
          return roleBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(role_);
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public protocol.RoleData.PlainRole.Builder addRoleBuilder() {
        return getRoleFieldBuilder().addBuilder(
            protocol.RoleData.PlainRole.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public protocol.RoleData.PlainRole.Builder addRoleBuilder(
          int index) {
        return getRoleFieldBuilder().addBuilder(
            index, protocol.RoleData.PlainRole.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainRole role = 3;</code>
       */
      public java.util.List<protocol.RoleData.PlainRole.Builder> 
           getRoleBuilderList() {
        return getRoleFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RoleData.PlainRole, protocol.RoleData.PlainRole.Builder, protocol.RoleData.PlainRoleOrBuilder> 
          getRoleFieldBuilder() {
        if (roleBuilder_ == null) {
          roleBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.RoleData.PlainRole, protocol.RoleData.PlainRole.Builder, protocol.RoleData.PlainRoleOrBuilder>(
                  role_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          role_ = null;
        }
        return roleBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseOperateRole)
    }

    static {
      defaultInstance = new ResponseOperateRole(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseOperateRole)
  }

  public interface PlainRoleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 roleCurrentLV = 1;
    /**
     * <code>required int32 roleCurrentLV = 1;</code>
     *
     * <pre>
     * 当前等级
     * </pre>
     */
    boolean hasRoleCurrentLV();
    /**
     * <code>required int32 roleCurrentLV = 1;</code>
     *
     * <pre>
     * 当前等级
     * </pre>
     */
    int getRoleCurrentLV();

    // required int32 roleExp = 2;
    /**
     * <code>required int32 roleExp = 2;</code>
     *
     * <pre>
     * 当前经验
     * </pre>
     */
    boolean hasRoleExp();
    /**
     * <code>required int32 roleExp = 2;</code>
     *
     * <pre>
     * 当前经验
     * </pre>
     */
    int getRoleExp();

    // required int32 roleNextNeedExp = 3;
    /**
     * <code>required int32 roleNextNeedExp = 3;</code>
     *
     * <pre>
     *升级所需经验
     * </pre>
     */
    boolean hasRoleNextNeedExp();
    /**
     * <code>required int32 roleNextNeedExp = 3;</code>
     *
     * <pre>
     *升级所需经验
     * </pre>
     */
    int getRoleNextNeedExp();
  }
  /**
   * Protobuf type {@code protocol.PlainRole}
   */
  public static final class PlainRole extends
      com.google.protobuf.GeneratedMessage
      implements PlainRoleOrBuilder {
    // Use PlainRole.newBuilder() to construct.
    private PlainRole(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PlainRole(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PlainRole defaultInstance;
    public static PlainRole getDefaultInstance() {
      return defaultInstance;
    }

    public PlainRole getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PlainRole(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              roleCurrentLV_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              roleExp_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              roleNextNeedExp_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RoleData.internal_static_protocol_PlainRole_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RoleData.internal_static_protocol_PlainRole_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RoleData.PlainRole.class, protocol.RoleData.PlainRole.Builder.class);
    }

    public static com.google.protobuf.Parser<PlainRole> PARSER =
        new com.google.protobuf.AbstractParser<PlainRole>() {
      public PlainRole parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlainRole(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PlainRole> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 roleCurrentLV = 1;
    public static final int ROLECURRENTLV_FIELD_NUMBER = 1;
    private int roleCurrentLV_;
    /**
     * <code>required int32 roleCurrentLV = 1;</code>
     *
     * <pre>
     * 当前等级
     * </pre>
     */
    public boolean hasRoleCurrentLV() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 roleCurrentLV = 1;</code>
     *
     * <pre>
     * 当前等级
     * </pre>
     */
    public int getRoleCurrentLV() {
      return roleCurrentLV_;
    }

    // required int32 roleExp = 2;
    public static final int ROLEEXP_FIELD_NUMBER = 2;
    private int roleExp_;
    /**
     * <code>required int32 roleExp = 2;</code>
     *
     * <pre>
     * 当前经验
     * </pre>
     */
    public boolean hasRoleExp() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 roleExp = 2;</code>
     *
     * <pre>
     * 当前经验
     * </pre>
     */
    public int getRoleExp() {
      return roleExp_;
    }

    // required int32 roleNextNeedExp = 3;
    public static final int ROLENEXTNEEDEXP_FIELD_NUMBER = 3;
    private int roleNextNeedExp_;
    /**
     * <code>required int32 roleNextNeedExp = 3;</code>
     *
     * <pre>
     *升级所需经验
     * </pre>
     */
    public boolean hasRoleNextNeedExp() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 roleNextNeedExp = 3;</code>
     *
     * <pre>
     *升级所需经验
     * </pre>
     */
    public int getRoleNextNeedExp() {
      return roleNextNeedExp_;
    }

    private void initFields() {
      roleCurrentLV_ = 0;
      roleExp_ = 0;
      roleNextNeedExp_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRoleCurrentLV()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRoleExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRoleNextNeedExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, roleCurrentLV_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, roleExp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, roleNextNeedExp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, roleCurrentLV_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, roleExp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, roleNextNeedExp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RoleData.PlainRole parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.PlainRole parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.PlainRole parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.PlainRole parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.PlainRole parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.PlainRole parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.PlainRole parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RoleData.PlainRole parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.PlainRole parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.PlainRole parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RoleData.PlainRole prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PlainRole}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RoleData.PlainRoleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RoleData.internal_static_protocol_PlainRole_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RoleData.internal_static_protocol_PlainRole_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RoleData.PlainRole.class, protocol.RoleData.PlainRole.Builder.class);
      }

      // Construct using protocol.RoleData.PlainRole.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        roleCurrentLV_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        roleExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        roleNextNeedExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RoleData.internal_static_protocol_PlainRole_descriptor;
      }

      public protocol.RoleData.PlainRole getDefaultInstanceForType() {
        return protocol.RoleData.PlainRole.getDefaultInstance();
      }

      public protocol.RoleData.PlainRole build() {
        protocol.RoleData.PlainRole result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RoleData.PlainRole buildPartial() {
        protocol.RoleData.PlainRole result = new protocol.RoleData.PlainRole(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.roleCurrentLV_ = roleCurrentLV_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.roleExp_ = roleExp_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.roleNextNeedExp_ = roleNextNeedExp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RoleData.PlainRole) {
          return mergeFrom((protocol.RoleData.PlainRole)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RoleData.PlainRole other) {
        if (other == protocol.RoleData.PlainRole.getDefaultInstance()) return this;
        if (other.hasRoleCurrentLV()) {
          setRoleCurrentLV(other.getRoleCurrentLV());
        }
        if (other.hasRoleExp()) {
          setRoleExp(other.getRoleExp());
        }
        if (other.hasRoleNextNeedExp()) {
          setRoleNextNeedExp(other.getRoleNextNeedExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRoleCurrentLV()) {
          
          return false;
        }
        if (!hasRoleExp()) {
          
          return false;
        }
        if (!hasRoleNextNeedExp()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RoleData.PlainRole parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RoleData.PlainRole) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 roleCurrentLV = 1;
      private int roleCurrentLV_ ;
      /**
       * <code>required int32 roleCurrentLV = 1;</code>
       *
       * <pre>
       * 当前等级
       * </pre>
       */
      public boolean hasRoleCurrentLV() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 roleCurrentLV = 1;</code>
       *
       * <pre>
       * 当前等级
       * </pre>
       */
      public int getRoleCurrentLV() {
        return roleCurrentLV_;
      }
      /**
       * <code>required int32 roleCurrentLV = 1;</code>
       *
       * <pre>
       * 当前等级
       * </pre>
       */
      public Builder setRoleCurrentLV(int value) {
        bitField0_ |= 0x00000001;
        roleCurrentLV_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roleCurrentLV = 1;</code>
       *
       * <pre>
       * 当前等级
       * </pre>
       */
      public Builder clearRoleCurrentLV() {
        bitField0_ = (bitField0_ & ~0x00000001);
        roleCurrentLV_ = 0;
        onChanged();
        return this;
      }

      // required int32 roleExp = 2;
      private int roleExp_ ;
      /**
       * <code>required int32 roleExp = 2;</code>
       *
       * <pre>
       * 当前经验
       * </pre>
       */
      public boolean hasRoleExp() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 roleExp = 2;</code>
       *
       * <pre>
       * 当前经验
       * </pre>
       */
      public int getRoleExp() {
        return roleExp_;
      }
      /**
       * <code>required int32 roleExp = 2;</code>
       *
       * <pre>
       * 当前经验
       * </pre>
       */
      public Builder setRoleExp(int value) {
        bitField0_ |= 0x00000002;
        roleExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roleExp = 2;</code>
       *
       * <pre>
       * 当前经验
       * </pre>
       */
      public Builder clearRoleExp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        roleExp_ = 0;
        onChanged();
        return this;
      }

      // required int32 roleNextNeedExp = 3;
      private int roleNextNeedExp_ ;
      /**
       * <code>required int32 roleNextNeedExp = 3;</code>
       *
       * <pre>
       *升级所需经验
       * </pre>
       */
      public boolean hasRoleNextNeedExp() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 roleNextNeedExp = 3;</code>
       *
       * <pre>
       *升级所需经验
       * </pre>
       */
      public int getRoleNextNeedExp() {
        return roleNextNeedExp_;
      }
      /**
       * <code>required int32 roleNextNeedExp = 3;</code>
       *
       * <pre>
       *升级所需经验
       * </pre>
       */
      public Builder setRoleNextNeedExp(int value) {
        bitField0_ |= 0x00000004;
        roleNextNeedExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 roleNextNeedExp = 3;</code>
       *
       * <pre>
       *升级所需经验
       * </pre>
       */
      public Builder clearRoleNextNeedExp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        roleNextNeedExp_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PlainRole)
    }

    static {
      defaultInstance = new PlainRole(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PlainRole)
  }

  public interface RequestGetRoleExpOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetRoleExp}
   *
   * <pre>
   *1318
   *REQUESTGETROLEEXP
   * </pre>
   */
  public static final class RequestGetRoleExp extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetRoleExpOrBuilder {
    // Use RequestGetRoleExp.newBuilder() to construct.
    private RequestGetRoleExp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetRoleExp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetRoleExp defaultInstance;
    public static RequestGetRoleExp getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetRoleExp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetRoleExp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RoleData.internal_static_protocol_RequestGetRoleExp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RoleData.internal_static_protocol_RequestGetRoleExp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RoleData.RequestGetRoleExp.class, protocol.RoleData.RequestGetRoleExp.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetRoleExp> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetRoleExp>() {
      public RequestGetRoleExp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetRoleExp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetRoleExp> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RoleData.RequestGetRoleExp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.RequestGetRoleExp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RoleData.RequestGetRoleExp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.RequestGetRoleExp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RoleData.RequestGetRoleExp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetRoleExp}
     *
     * <pre>
     *1318
     *REQUESTGETROLEEXP
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RoleData.RequestGetRoleExpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RoleData.internal_static_protocol_RequestGetRoleExp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RoleData.internal_static_protocol_RequestGetRoleExp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RoleData.RequestGetRoleExp.class, protocol.RoleData.RequestGetRoleExp.Builder.class);
      }

      // Construct using protocol.RoleData.RequestGetRoleExp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RoleData.internal_static_protocol_RequestGetRoleExp_descriptor;
      }

      public protocol.RoleData.RequestGetRoleExp getDefaultInstanceForType() {
        return protocol.RoleData.RequestGetRoleExp.getDefaultInstance();
      }

      public protocol.RoleData.RequestGetRoleExp build() {
        protocol.RoleData.RequestGetRoleExp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RoleData.RequestGetRoleExp buildPartial() {
        protocol.RoleData.RequestGetRoleExp result = new protocol.RoleData.RequestGetRoleExp(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RoleData.RequestGetRoleExp) {
          return mergeFrom((protocol.RoleData.RequestGetRoleExp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RoleData.RequestGetRoleExp other) {
        if (other == protocol.RoleData.RequestGetRoleExp.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RoleData.RequestGetRoleExp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RoleData.RequestGetRoleExp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetRoleExp)
    }

    static {
      defaultInstance = new RequestGetRoleExp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetRoleExp)
  }

  public interface ResponseGetRoleExpOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // repeated .protocol.PlainRole role = 2;
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    java.util.List<protocol.RoleData.PlainRole> 
        getRoleList();
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    protocol.RoleData.PlainRole getRole(int index);
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    int getRoleCount();
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    java.util.List<? extends protocol.RoleData.PlainRoleOrBuilder> 
        getRoleOrBuilderList();
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    protocol.RoleData.PlainRoleOrBuilder getRoleOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseGetRoleExp}
   *
   * <pre>
   *2318
   * </pre>
   */
  public static final class ResponseGetRoleExp extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetRoleExpOrBuilder {
    // Use ResponseGetRoleExp.newBuilder() to construct.
    private ResponseGetRoleExp(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetRoleExp(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetRoleExp defaultInstance;
    public static ResponseGetRoleExp getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetRoleExp getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetRoleExp(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                role_ = new java.util.ArrayList<protocol.RoleData.PlainRole>();
                mutable_bitField0_ |= 0x00000002;
              }
              role_.add(input.readMessage(protocol.RoleData.PlainRole.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          role_ = java.util.Collections.unmodifiableList(role_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RoleData.internal_static_protocol_ResponseGetRoleExp_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RoleData.internal_static_protocol_ResponseGetRoleExp_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RoleData.ResponseGetRoleExp.class, protocol.RoleData.ResponseGetRoleExp.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetRoleExp> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetRoleExp>() {
      public ResponseGetRoleExp parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetRoleExp(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetRoleExp> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // repeated .protocol.PlainRole role = 2;
    public static final int ROLE_FIELD_NUMBER = 2;
    private java.util.List<protocol.RoleData.PlainRole> role_;
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    public java.util.List<protocol.RoleData.PlainRole> getRoleList() {
      return role_;
    }
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    public java.util.List<? extends protocol.RoleData.PlainRoleOrBuilder> 
        getRoleOrBuilderList() {
      return role_;
    }
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    public int getRoleCount() {
      return role_.size();
    }
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    public protocol.RoleData.PlainRole getRole(int index) {
      return role_.get(index);
    }
    /**
     * <code>repeated .protocol.PlainRole role = 2;</code>
     */
    public protocol.RoleData.PlainRoleOrBuilder getRoleOrBuilder(
        int index) {
      return role_.get(index);
    }

    private void initFields() {
      errorId_ = 0;
      role_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getRoleCount(); i++) {
        if (!getRole(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      for (int i = 0; i < role_.size(); i++) {
        output.writeMessage(2, role_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      for (int i = 0; i < role_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, role_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RoleData.ResponseGetRoleExp parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RoleData.ResponseGetRoleExp parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RoleData.ResponseGetRoleExp prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetRoleExp}
     *
     * <pre>
     *2318
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RoleData.ResponseGetRoleExpOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RoleData.internal_static_protocol_ResponseGetRoleExp_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RoleData.internal_static_protocol_ResponseGetRoleExp_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RoleData.ResponseGetRoleExp.class, protocol.RoleData.ResponseGetRoleExp.Builder.class);
      }

      // Construct using protocol.RoleData.ResponseGetRoleExp.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRoleFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (roleBuilder_ == null) {
          role_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          roleBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RoleData.internal_static_protocol_ResponseGetRoleExp_descriptor;
      }

      public protocol.RoleData.ResponseGetRoleExp getDefaultInstanceForType() {
        return protocol.RoleData.ResponseGetRoleExp.getDefaultInstance();
      }

      public protocol.RoleData.ResponseGetRoleExp build() {
        protocol.RoleData.ResponseGetRoleExp result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RoleData.ResponseGetRoleExp buildPartial() {
        protocol.RoleData.ResponseGetRoleExp result = new protocol.RoleData.ResponseGetRoleExp(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (roleBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            role_ = java.util.Collections.unmodifiableList(role_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.role_ = role_;
        } else {
          result.role_ = roleBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RoleData.ResponseGetRoleExp) {
          return mergeFrom((protocol.RoleData.ResponseGetRoleExp)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RoleData.ResponseGetRoleExp other) {
        if (other == protocol.RoleData.ResponseGetRoleExp.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (roleBuilder_ == null) {
          if (!other.role_.isEmpty()) {
            if (role_.isEmpty()) {
              role_ = other.role_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureRoleIsMutable();
              role_.addAll(other.role_);
            }
            onChanged();
          }
        } else {
          if (!other.role_.isEmpty()) {
            if (roleBuilder_.isEmpty()) {
              roleBuilder_.dispose();
              roleBuilder_ = null;
              role_ = other.role_;
              bitField0_ = (bitField0_ & ~0x00000002);
              roleBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRoleFieldBuilder() : null;
            } else {
              roleBuilder_.addAllMessages(other.role_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getRoleCount(); i++) {
          if (!getRole(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RoleData.ResponseGetRoleExp parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RoleData.ResponseGetRoleExp) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.PlainRole role = 2;
      private java.util.List<protocol.RoleData.PlainRole> role_ =
        java.util.Collections.emptyList();
      private void ensureRoleIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          role_ = new java.util.ArrayList<protocol.RoleData.PlainRole>(role_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RoleData.PlainRole, protocol.RoleData.PlainRole.Builder, protocol.RoleData.PlainRoleOrBuilder> roleBuilder_;

      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public java.util.List<protocol.RoleData.PlainRole> getRoleList() {
        if (roleBuilder_ == null) {
          return java.util.Collections.unmodifiableList(role_);
        } else {
          return roleBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public int getRoleCount() {
        if (roleBuilder_ == null) {
          return role_.size();
        } else {
          return roleBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public protocol.RoleData.PlainRole getRole(int index) {
        if (roleBuilder_ == null) {
          return role_.get(index);
        } else {
          return roleBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder setRole(
          int index, protocol.RoleData.PlainRole value) {
        if (roleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIsMutable();
          role_.set(index, value);
          onChanged();
        } else {
          roleBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder setRole(
          int index, protocol.RoleData.PlainRole.Builder builderForValue) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder addRole(protocol.RoleData.PlainRole value) {
        if (roleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIsMutable();
          role_.add(value);
          onChanged();
        } else {
          roleBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder addRole(
          int index, protocol.RoleData.PlainRole value) {
        if (roleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIsMutable();
          role_.add(index, value);
          onChanged();
        } else {
          roleBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder addRole(
          protocol.RoleData.PlainRole.Builder builderForValue) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.add(builderForValue.build());
          onChanged();
        } else {
          roleBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder addRole(
          int index, protocol.RoleData.PlainRole.Builder builderForValue) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder addAllRole(
          java.lang.Iterable<? extends protocol.RoleData.PlainRole> values) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          super.addAll(values, role_);
          onChanged();
        } else {
          roleBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder clearRole() {
        if (roleBuilder_ == null) {
          role_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          roleBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public Builder removeRole(int index) {
        if (roleBuilder_ == null) {
          ensureRoleIsMutable();
          role_.remove(index);
          onChanged();
        } else {
          roleBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public protocol.RoleData.PlainRole.Builder getRoleBuilder(
          int index) {
        return getRoleFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public protocol.RoleData.PlainRoleOrBuilder getRoleOrBuilder(
          int index) {
        if (roleBuilder_ == null) {
          return role_.get(index);  } else {
          return roleBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public java.util.List<? extends protocol.RoleData.PlainRoleOrBuilder> 
           getRoleOrBuilderList() {
        if (roleBuilder_ != null) {
          return roleBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(role_);
        }
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public protocol.RoleData.PlainRole.Builder addRoleBuilder() {
        return getRoleFieldBuilder().addBuilder(
            protocol.RoleData.PlainRole.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public protocol.RoleData.PlainRole.Builder addRoleBuilder(
          int index) {
        return getRoleFieldBuilder().addBuilder(
            index, protocol.RoleData.PlainRole.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainRole role = 2;</code>
       */
      public java.util.List<protocol.RoleData.PlainRole.Builder> 
           getRoleBuilderList() {
        return getRoleFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.RoleData.PlainRole, protocol.RoleData.PlainRole.Builder, protocol.RoleData.PlainRoleOrBuilder> 
          getRoleFieldBuilder() {
        if (roleBuilder_ == null) {
          roleBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.RoleData.PlainRole, protocol.RoleData.PlainRole.Builder, protocol.RoleData.PlainRoleOrBuilder>(
                  role_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          role_ = null;
        }
        return roleBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetRoleExp)
    }

    static {
      defaultInstance = new ResponseGetRoleExp(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetRoleExp)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestOperateRole_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestOperateRole_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseOperateRole_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseOperateRole_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PlainRole_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PlainRole_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetRoleExp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetRoleExp_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetRoleExp_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetRoleExp_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nrexp.proto\022\010protocol\032\013proto.proto\032\nite" +
      "m.proto\032\ntask.proto\032\014friend.proto\032\014commo" +
      "n.proto\032\nmail.proto\032\tpet.proto\"%\n\022Reques" +
      "tOperateRole\022\017\n\007roleExp\030\001 \002(\005\"8\n\023Respons" +
      "eOperateRole\022!\n\004role\030\003 \003(\0132\023.protocol.Pl" +
      "ainRole\"L\n\tPlainRole\022\025\n\rroleCurrentLV\030\001 " +
      "\002(\005\022\017\n\007roleExp\030\002 \002(\005\022\027\n\017roleNextNeedExp\030" +
      "\003 \002(\005\"\023\n\021RequestGetRoleExp\"H\n\022ResponseGe" +
      "tRoleExp\022\017\n\007errorId\030\001 \002(\005\022!\n\004role\030\002 \003(\0132" +
      "\023.protocol.PlainRoleB\nB\010RoleData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestOperateRole_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestOperateRole_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestOperateRole_descriptor,
              new java.lang.String[] { "RoleExp", });
          internal_static_protocol_ResponseOperateRole_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseOperateRole_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseOperateRole_descriptor,
              new java.lang.String[] { "Role", });
          internal_static_protocol_PlainRole_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_PlainRole_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PlainRole_descriptor,
              new java.lang.String[] { "RoleCurrentLV", "RoleExp", "RoleNextNeedExp", });
          internal_static_protocol_RequestGetRoleExp_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestGetRoleExp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetRoleExp_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetRoleExp_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponseGetRoleExp_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetRoleExp_descriptor,
              new java.lang.String[] { "ErrorId", "Role", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
          protocol.ItemData.getDescriptor(),
          protocol.TaskData.getDescriptor(),
          protocol.FriendData.getDescriptor(),
          protocol.CommonData.getDescriptor(),
          protocol.MailData.getDescriptor(),
          protocol.PetData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
