package module.playerstatus;

import entities.LimitedTimeRewardEntity;
import entities.PetEggEntity;
import entities.RoleOfflineTimeEntity;
import entities.TemItemEntity;
import manager.ReportManager;
import module.activity.LimitedTime.LimitedTimeRewardDao;
import module.activity.LimitedTime.LimitedTimeRewardService;
import module.activity.LimitedTime.LimitedTimeRewardTimer;
import module.activity.RechargeRebate.PayDiamondDao;
import module.activity.RechargeRebate.RechargeRebateRewardDao;
import module.petegg.PetEggDao;
import module.role_offline_time.RoleOfflineTimeDao;
import module.temperature_item.TemItemDao;
import protocol.*;
import server.SuperServerHandler;

import java.util.Date;
import java.util.List;

public class PlayerOnline {
    private static PlayerOnline inst = null;
    public static PlayerOnline getInstance() {
        if (inst == null) {
            inst = new PlayerOnline();
        }
        return inst;
    }

    // 上线发送消息
    public void SendMessage(String uid) {
        try {
            PayDiamondNum(uid);
            RechargeRebateReward(uid);
            LimitedTimeReward(uid);
            GetAllEggInfo(uid);
            GetAllLimitedTimeRewardTask(uid);
//            GetLimitedTimeRewardTaskGetRemainTime(uid);

            GetTemItem(uid);
        }catch (Exception e){
            e.printStackTrace();
        }

        try {
            GetBattery(uid);
            System.err.println("try to get battery");
//            GetTemperatureFall(uid);
        }catch (Exception e){
            System.err.println("上线-体力-错误");
        }
    }
    private void GetTemItem(String uid){
        TemItemEntity entity = TemItemDao.getInstance().Get(uid);
        TemItemData.ResponseTemItem.Builder builder = TemItemData.ResponseTemItem.newBuilder();

        builder.setWood(entity.getWood());
        builder.setLeaf(entity.getLeaf());
        builder.setWater(entity.getWater());
        builder.setSoil(entity.getSoil());

        builder.setBattle(entity.getBattle());
        builder.setRun(entity.getRun());
        builder.setPuzzle(entity.getPuzzle());

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSETEMITEM_VALUE, builder.build().toByteArray());
    }

    // 计算需要获得的体力
    private void GetBattery(String uid){
        // 刷新一次体力的次数
        long refreshMaxNum = PlayerOnlineTimer.getInstance().BatteryRefreshMaxNum;
        // 一次增加体力的数量
        int increaseNum = PlayerOnlineTimer.getInstance().BatteryIncreaseNum;

        long offsetTime = System.currentTimeMillis() - RoleOfflineTimeDao.getInstance().Get(uid).getOfflineTime().getTime();

        // 刷新次数
        long refreshNum = (offsetTime / 1000) / refreshMaxNum;

        System.err.println("刷新次數：" + refreshNum);

        // 增加的体力数量
        long addBattery = refreshNum * increaseNum;

        PlayerOnlineTimer.getInstance().AddBattery(uid, addBattery);

    }

    // 计算需要获得的体力
    private void GetTemperatureFall(String uid){
        // 刷新一次体力的次数
        long refreshMaxNum = PlayerOnlineTimer.getInstance().TemperatureDeleteItemMaxNum;

        long offsetTime = System.currentTimeMillis() - RoleOfflineTimeDao.getInstance().Get(uid).getOfflineTime().getTime();

        if (offsetTime <= 1){
            TemperData.ResponseTemper.Builder attachment = TemperData.ResponseTemper.newBuilder();
            attachment.setDeleteItem(true);
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSETemper_VALUE,attachment.build().toByteArray());
        }else {
            // 刷新次数
            long refreshNum = (offsetTime / 1000) / refreshMaxNum;
            if (refreshNum >= 1){
                TemperData.ResponseTemper.Builder attachment = TemperData.ResponseTemper.newBuilder();
                attachment.setDeleteItem(true);
                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSETemper_VALUE,attachment.build().toByteArray());
            }
        }
    }

    // 限时任务
    private void GetAllLimitedTimeRewardTask(String uid) {
        LimitedTimeRewardService.GetAllLimitedTimeRewardTask(uid);
    }

    // 充值的钻石数量
    private void PayDiamondNum(String uid){
        RechargeRebateData.ResponsePayDiamond.Builder builder = RechargeRebateData.ResponsePayDiamond.newBuilder();
        builder.setDiamondNum(PayDiamondDao.getInstance().GetPlayerPayDiamond(uid).getDiamond_num());
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPAYDIAMOND_VALUE, builder.build().toByteArray());
    }

    // 充值返利的領取數量
    private void RechargeRebateReward(String uid){
        RechargeRebateData.ResponseRechargeRebateReward.Builder builder = RechargeRebateData.ResponseRechargeRebateReward.newBuilder();
        builder.setRewardId(RechargeRebateRewardDao.getInstance().GetPlayerRechargeRebateReward(uid).getReward_id());
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSERECHAREGREBATEREWARD_VALUE, builder.build().toByteArray());
    }


    // 限时福利
    private void LimitedTimeReward(String uid){
        LimitedTimeRewardData.ResponseLimitedTimeReward.Builder builder = LimitedTimeRewardData.ResponseLimitedTimeReward.newBuilder();
        LimitedTimeRewardEntity entity = LimitedTimeRewardDao.getInstance().Get(uid);

        builder.setChargeRewardId(entity.getCharge_reward_id());
        builder.setOrdinaryRewardId(entity.getOrdinary_reward_id());
        builder.setChargeReward(entity.isCharge_reward());
        builder.setExp(entity.getExp());

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSELIMITEDTIMEREWARD_VALUE, builder.build().toByteArray());
    }

    // 获取全部蛋的数据
    private void  GetAllEggInfo(String uid){
        List<Object> petList = PetEggDao.getInstance().GetAll(uid);
        PetEggData.ResponseAllPetEggExp.Builder builder = PetEggData.ResponseAllPetEggExp.newBuilder();
        for (int i = 0; i < petList.size(); i++) {
//            PetEntity pet = (PetEntity) petList.get(i);
            PetEggData.ResponsePetEggExp.Builder data = PetEggData.ResponsePetEggExp.newBuilder();

//            PetEggEntity petEgg = PetEggDao.getInstance().Get(uid, pet.getPetUId());
            PetEggEntity petEgg = (PetEggEntity) petList.get(i);
            data.setPetLv(petEgg.getLv());
            data.setPetExp(petEgg.getExp());
            data.setPetUid(petEgg.getPetUid());

            builder.addData(data);
        }

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEALLPETEGGEXP_VALUE, builder.build().toByteArray());
    }


//    private void GetLimitedTimeRewardTaskGetRemainTime(String uid) {
//        LimitedTimeRewardData.ResponseRemainTime.Builder builder = LimitedTimeRewardData.ResponseRemainTime.newBuilder();
//        builder.setTimeSecond(LimitedTimeRewardTimer.getInstance().getCurTime());
//        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEREMAINTIME_VALUE, builder.build().toByteArray());
//    }
}
