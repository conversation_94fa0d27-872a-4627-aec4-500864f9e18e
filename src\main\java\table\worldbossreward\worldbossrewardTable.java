package table.worldbossreward;

import table.TableManager;

import java.util.Comparator;

public class worldbossrewardTable extends TableManager<worldbossrewardLine> {

    private static worldbossrewardTable inst = null;
    public static worldbossrewardTable getInstance() {
        if (inst == null) {
            inst = new worldbossrewardTable();
        }
        return inst;
    }

    @Override
    public void Parse() {
        this.GetAllItem().sort(Comparator.comparingInt(a -> a.percentage));
        for (worldbossrewardLine data :
                GetAllItem()) {
            data.Parse();
        }
    }
    @Override
    public String LinePath() {
        return "table.worldbossreward.worldbossrewardLine";
    }

    @Override
    public String TableName() {
        return "worldbossreward";
    }
}

