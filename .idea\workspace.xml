<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="BookmarkManager">
    <bookmark url="file://$PROJECT_DIR$/src/main/java/common/SuperConfig.java" line="353" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="00a39133-ce51-44ba-b116-63e5fa11db83" name="Default" comment="" />
    <ignored path="$PROJECT_DIR$/target/" />
    <option name="EXCLUDED_CONVERTED_TO_IGNORED" value="true" />
    <option name="TRACKING_ENABLED" value="true" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CoverageDataManager">
    <SUITE FILE_PATH="coverage/superstar$superStar.ic" NAME="superStar Coverage Results" MODIFIED="1678703582922" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="idea" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" />
  </component>
  <component name="DatabaseView">
    <option name="SHOW_INTERMEDIATE" value="true" />
    <option name="GROUP_DATA_SOURCES" value="true" />
    <option name="GROUP_SCHEMA" value="true" />
    <option name="GROUP_CONTENTS" value="false" />
    <option name="SORT_POSITIONED" value="false" />
    <option name="SHOW_EMPTY_GROUPS" value="false" />
    <option name="AUTO_SCROLL_FROM_SOURCE" value="false" />
    <option name="HIDDEN_KINDS">
      <set />
    </option>
    <expand />
    <select />
  </component>
  <component name="FileEditorManager">
    <leaf SIDE_TABS_SIZE_LIMIT_KEY="300">
      <file leaf-file-name="ByteToMessageDecoder.java" pinned="false" current-in-tab="false">
        <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/handler/codec/ByteToMessageDecoder.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="141">
              <caret line="289" selection-start-line="289" selection-end-line="289" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="RoleService.java" pinned="false" current-in-tab="true">
        <entry file="file://$PROJECT_DIR$/src/main/java/module/role/RoleService.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="174">
              <caret line="61" selection-start-line="61" selection-end-line="61" />
              <folding>
                <element signature="imports" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="MysqlHandler.java" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/manager/MysqlHandler.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="3070">
              <caret line="110" selection-start-line="110" selection-end-line="110" />
              <folding>
                <element signature="imports" expanded="true" />
                <element signature="e#906#907#0" expanded="true" />
                <element signature="e#936#937#0" expanded="true" />
              </folding>
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="ItemDao.java" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/module/item/ItemDao.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="6257">
              <caret line="886" column="27" selection-start-line="886" selection-start-column="27" selection-end-line="886" selection-end-column="27" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="SuperServerHandler.java" pinned="false" current-in-tab="false">
        <entry file="file://$PROJECT_DIR$/src/main/java/server/SuperServerHandler.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="273">
              <caret line="136" selection-start-line="136" selection-end-line="136" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="AbstractChannelHandlerContext.java" pinned="false" current-in-tab="false">
        <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/AbstractChannelHandlerContext.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="317">
              <caret line="356" selection-start-line="356" selection-end-line="356" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="DefaultChannelPipeline.java" pinned="false" current-in-tab="false">
        <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/DefaultChannelPipeline.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="141">
              <caret line="935" selection-start-line="935" selection-end-line="935" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="AbstractNioByteChannel.java" pinned="false" current-in-tab="false">
        <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/nio/AbstractNioByteChannel.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="317">
              <caret line="160" selection-start-line="160" selection-end-line="160" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="NioEventLoop.java" pinned="false" current-in-tab="false">
        <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/nio/NioEventLoop.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="405">
              <caret line="461" selection-start-line="461" selection-end-line="461" />
            </state>
          </provider>
        </entry>
      </file>
      <file leaf-file-name="IdleStateHandler.java" pinned="false" current-in-tab="false">
        <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/handler/timeout/IdleStateHandler.java">
          <provider selected="true" editor-type-id="text-editor">
            <state relative-caret-position="220">
              <caret line="286" selection-start-line="286" selection-end-line="286" />
            </state>
          </provider>
        </entry>
      </file>
    </leaf>
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Interface" />
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="FindInProjectRecents">
    <findStrings>
      <find>2303</find>
      <find>RequestOperationFriends</find>
      <find>changeDress</find>
      <find>REQUESTPHYSICAL</find>
      <find>RequestPetHatch</find>
      <find>2001</find>
      <find>RESPONSELOGIN</find>
      <find>ResponseLogin</find>
      <find>RequestSendOutInformation</find>
      <find>2021</find>
      <find>ResponseSendOutInformation</find>
      <find>getCtx</find>
      <find>ReportManager.reportInfo</find>
      <find>messageLis</find>
      <find>ResponseFriendsApply</find>
      <find>1466</find>
      <find>1020</find>
      <find>ResponseChatRecord</find>
      <find>applelogin</find>
      <find>loginList</find>
      <find>apple</find>
      <find>keysJsonArray</find>
      <find>AppleLogin</find>
      <find>1213</find>
      <find>2213</find>
      <find>1212</find>
      <find>ResponseRoleSex</find>
      <find>judgeFunction</find>
      <find>RESPONSEROLESEX</find>
      <find>updateSe</find>
    </findStrings>
    <replaceStrings>
      <replace />
    </replaceStrings>
    <dirStrings>
      <dir>D:\dreamGame\server\newSuperStar\superStar\src\main\java\protocol</dir>
      <dir>D:\dreamGame\server\newSuperStar\superStar\src\main\java\common</dir>
      <dir>D:\dreamGame\server\newSuperStar\superStar\src\main\java\module\Dispatch</dir>
    </dirStrings>
  </component>
  <component name="IdeDocumentHistory">
    <option name="CHANGED_PATHS">
      <list>
        <option value="$PROJECT_DIR$/src/main/java/module/rank/RankCallBack.java" />
        <option value="$PROJECT_DIR$/src/main/java/model/RankInfo.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/rank/RankService.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/ranking/RankingCallBack.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/ranking/RankingDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/ranking/RankingService.java" />
        <option value="$PROJECT_DIR$/src/main/java/manager/GameTimer.java" />
        <option value="$PROJECT_DIR$/src/main/java/common/MonsterConfig.java" />
        <option value="$PROJECT_DIR$/src/main/java/entities/RankEntity.java" />
        <option value="$PROJECT_DIR$/src/main/java/entities/RankEntity.hbm.xml" />
        <option value="$PROJECT_DIR$/src/main/resources/hibernate.cfg.xml" />
        <option value="$PROJECT_DIR$/src/main/java/module/item/ItemUtils.java" />
        <option value="$PROJECT_DIR$/src/main/java/model/RoleInfo.java" />
        <option value="$PROJECT_DIR$/src/main/java/entities/RoleEntity.hbm.xml" />
        <option value="$PROJECT_DIR$/src/main/java/protocol/ProtoData.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/equip/EquipDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/item/IItem.java" />
        <option value="$PROJECT_DIR$/pom.xml" />
        <option value="$PROJECT_DIR$/src/main/java/module/item/ItemDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/rank/Test.java" />
        <option value="$PROJECT_DIR$/src/main/java/protocol/RoleData.java" />
        <option value="$PROJECT_DIR$/src/main/java/entities/RoleEntity.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/pet/PetUtils.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/role/RoleUtils.java" />
        <option value="$PROJECT_DIR$/src/main/java/server/SuperServer.java" />
        <option value="$PROJECT_DIR$/src/main/java/utils/MyUtils.java" />
        <option value="$PROJECT_DIR$/src/main/java/common/RoleExpConfig.java" />
        <option value="$PROJECT_DIR$/src/main/java/common/SuperConfig.java" />
        <option value="$PROJECT_DIR$/src/main/java/model/LoginInfo.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/battle/BattleService.java" />
        <option value="$PROJECT_DIR$/src/main/java/common/PowerupConfig.java" />
        <option value="$PROJECT_DIR$/src/main/java/entities/CompleteTaskEntity.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/rank/IRank.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/rank/RankDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/login/LoginDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/login/ILogin.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/equip/EquipService.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/Dispatch/DispatchService.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/login/LoginService.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/friend/FriendDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/friend/IFriend.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/item/ItemService.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/pet/PetService.java" />
        <option value="$PROJECT_DIR$/src/main/resources/config.properties" />
        <option value="$PROJECT_DIR$/src/main/java/module/friend/FriendService.java" />
        <option value="$PROJECT_DIR$/src/main/java/superStar.java" />
        <option value="$PROJECT_DIR$/src/main/java/server/SuperServerHandler.java" />
        <option value="$PROJECT_DIR$/src/main/java/manager/MysqlHandler.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/role/RoleDao.java" />
        <option value="$PROJECT_DIR$/src/main/java/manager/MySql.java" />
        <option value="$PROJECT_DIR$/src/main/java/module/role/RoleService.java" />
      </list>
    </option>
  </component>
  <component name="JsBuildToolGruntFileManager" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsBuildToolPackageJson" detection-done="true" sorting="DEFINITION_ORDER" />
  <component name="JsGulpfileManager">
    <detection-done>true</detection-done>
    <sorting>DEFINITION_ORDER</sorting>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\Users\<USER>\Desktop\新建文件夹 (2)\apache-maven-3.5.0\apache-maven-3.5.0\Repository" />
      </MavenGeneralSettings>
    </option>
    <option name="importingSettings">
      <MavenImportingSettings>
        <option name="importAutomatically" value="true" />
      </MavenImportingSettings>
    </option>
  </component>
  <component name="MavenProjectNavigator">
    <treeState>
      <expand>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="superstar" type="9519ce18:MavenProjectsStructure$ProjectNode" />
        </path>
        <path>
          <item name="" type="16c1761:MavenProjectsStructure$RootNode" />
          <item name="superstar" type="9519ce18:MavenProjectsStructure$ProjectNode" />
          <item name="Lifecycle" type="58874e2:MavenProjectsStructure$LifecycleNode" />
        </path>
      </expand>
      <select />
    </treeState>
  </component>
  <component name="NodePackageJsonFileManager">
    <packageJsonPaths />
  </component>
  <component name="ProjectFrameBounds" extendedState="6">
    <option name="x" value="463" />
    <option name="y" value="88" />
    <option name="width" value="1651" />
    <option name="height" value="1013" />
  </component>
  <component name="ProjectView">
    <navigator proportions="" version="1">
      <foldersAlwaysOnTop value="true" />
    </navigator>
    <panes>
      <pane id="Scope" />
      <pane id="ProjectPane">
        <subPane>
          <expand>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="module" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="module" type="462c0819:PsiDirectoryNode" />
              <item name="pet" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="module" type="462c0819:PsiDirectoryNode" />
              <item name="rank" type="462c0819:PsiDirectoryNode" />
            </path>
            <path>
              <item name="superstar" type="b2602c69:ProjectViewProjectNode" />
              <item name="superStar" type="462c0819:PsiDirectoryNode" />
              <item name="src" type="462c0819:PsiDirectoryNode" />
              <item name="main" type="462c0819:PsiDirectoryNode" />
              <item name="java" type="462c0819:PsiDirectoryNode" />
              <item name="module" type="462c0819:PsiDirectoryNode" />
              <item name="role" type="462c0819:PsiDirectoryNode" />
            </path>
          </expand>
          <select />
        </subPane>
      </pane>
      <pane id="PackagesPane" />
      <pane id="AndroidView" />
    </panes>
  </component>
  <component name="PropertiesComponent">
    <property name="Downloaded.Files.Path.Enabled" value="false" />
    <property name="Repository.Attach.JavaDocs" value="false" />
    <property name="Repository.Attach.Sources" value="false" />
    <property name="SearchEverywhereHistoryKey" value="queryForOne&#9;PSI&#9;JAVA://manager.MySql#queryForOne&#10;USEROFFLINE_VALUE&#9;PSI&#9;JAVA://protocol.ProtoData.ErrorCode#USEROFFLINE_VALUE" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="D:/superStar" />
    <property name="nodejs_interpreter_path.stuck_in_default_project" value="undefined stuck path" />
    <property name="nodejs_npm_path_reset_for_default_project" value="true" />
    <property name="project.structure.last.edited" value="Project" />
    <property name="project.structure.proportion" value="0.15" />
    <property name="project.structure.side.proportion" value="0.35443038" />
    <property name="settings.editor.selected.configurable" value="preferences.lookFeel" />
  </component>
  <component name="RunDashboard">
    <option name="ruleStates">
      <list>
        <RuleState>
          <option name="name" value="ConfigurationTypeDashboardGroupingRule" />
        </RuleState>
        <RuleState>
          <option name="name" value="StatusDashboardGroupingRule" />
        </RuleState>
      </list>
    </option>
  </component>
  <component name="RunManager" selected="Application.superStar">
    <configuration name="Test (1)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="module.equip.Test" />
      <module name="superstar" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="module.equip.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
    </configuration>
    <configuration name="Test (2)" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="module.map.Test" />
      <module name="superstar" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="module.map.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
    </configuration>
    <configuration name="Test (3)" type="Application" factoryName="Application" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="module.rank.Test" />
      <module name="superstar" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="module.rank.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
    </configuration>
    <configuration name="Test" type="Application" factoryName="Application" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="module.pet.Test" />
      <module name="superstar" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="module.pet.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
    </configuration>
    <configuration default="true" type="Application" factoryName="Application">
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    </configuration>
    <configuration name="superStar" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="superStar" />
      <module name="superstar" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
    </configuration>
    <configuration default="true" type="JUnit" factoryName="JUnit">
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="TEST_OBJECT" value="class" />
      <option name="VM_PARAMETERS" value="-ea" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" value="%MODULE_WORKING_DIR%" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="singleModule" />
      </option>
      <patterns />
    </configuration>
    <configuration default="true" type="TestNG" factoryName="TestNG">
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="false" />
      <option name="ALTERNATIVE_JRE_PATH" />
      <option name="SUITE_NAME" />
      <option name="PACKAGE_NAME" />
      <option name="MAIN_CLASS_NAME" />
      <option name="METHOD_NAME" />
      <option name="GROUP_NAME" />
      <option name="TEST_OBJECT" value="CLASS" />
      <option name="VM_PARAMETERS" value="-ea" />
      <option name="PARAMETERS" />
      <option name="WORKING_DIRECTORY" value="%MODULE_WORKING_DIR%" />
      <option name="OUTPUT_DIRECTORY" />
      <option name="PASS_PARENT_ENVS" value="true" />
      <option name="TEST_SEARCH_SCOPE">
        <value defaultName="singleModule" />
      </option>
      <option name="USE_DEFAULT_REPORTERS" value="false" />
      <option name="PROPERTIES_FILE" />
      <properties />
      <listeners />
    </configuration>
    <list>
      <item itemvalue="Application.Test" />
      <item itemvalue="Application.Test (3)" />
      <item itemvalue="Application.superStar" />
      <item itemvalue="Application.Test (1)" />
      <item itemvalue="Application.Test (2)" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Application.superStar" />
        <item itemvalue="Application.Test (2)" />
        <item itemvalue="Application.Test (1)" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="00a39133-ce51-44ba-b116-63e5fa11db83" name="Default" comment="" />
      <created>1668398143037</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1668398143037</updated>
      <workItem from="1668398144404" duration="15206000" />
      <workItem from="1668475709262" duration="14344000" />
      <workItem from="1668497991177" duration="10418000" />
      <workItem from="1668562825975" duration="16277000" />
      <workItem from="1668649356143" duration="15488000" />
      <workItem from="1668735136620" duration="16460000" />
      <workItem from="1668995383202" duration="14915000" />
      <workItem from="1669080990841" duration="19732000" />
      <workItem from="1669183633323" duration="646000" />
      <workItem from="1669253226018" duration="22030000" />
      <workItem from="1669339908887" duration="19050000" />
      <workItem from="1669599457474" duration="20190000" />
      <workItem from="1669685313257" duration="16215000" />
      <workItem from="1669771896069" duration="14273000" />
      <workItem from="1669859242443" duration="15548000" />
      <workItem from="1669944703081" duration="14730000" />
      <workItem from="1670203601070" duration="21453000" />
      <workItem from="1670290595435" duration="13250000" />
      <workItem from="1670377382641" duration="13470000" />
      <workItem from="1670463180639" duration="21461000" />
      <workItem from="1670549582888" duration="24829000" />
      <workItem from="1670808421481" duration="21405000" />
      <workItem from="1670896804069" duration="127000" />
      <workItem from="1670897185955" duration="10226000" />
      <workItem from="1670910646936" duration="4992000" />
      <workItem from="1670981466189" duration="8064000" />
      <workItem from="1671067956934" duration="12473000" />
      <workItem from="1671104890353" duration="590000" />
      <workItem from="1671154389729" duration="1140000" />
      <workItem from="1671155632033" duration="3575000" />
      <workItem from="1671513598263" duration="648000" />
      <workItem from="1671689630550" duration="611000" />
      <workItem from="1672104615432" duration="4135000" />
      <workItem from="1672194062733" duration="2380000" />
      <workItem from="1672277750568" duration="2576000" />
      <workItem from="1672364115808" duration="3271000" />
      <workItem from="1672709545901" duration="7185000" />
      <workItem from="1672796416965" duration="3717000" />
      <workItem from="1672882255511" duration="12871000" />
      <workItem from="1672969525291" duration="3754000" />
      <workItem from="1672984454482" duration="3332000" />
      <workItem from="1673055432343" duration="10669000" />
      <workItem from="1673228094518" duration="15484000" />
      <workItem from="1673314583630" duration="9982000" />
      <workItem from="1673400870327" duration="13044000" />
      <workItem from="1673487471897" duration="7301000" />
      <workItem from="1673573777345" duration="3247000" />
      <workItem from="1673746341526" duration="9152000" />
      <workItem from="1673848059478" duration="1503000" />
      <workItem from="1673922097159" duration="638000" />
      <workItem from="1674006833856" duration="1077000" />
      <workItem from="1674093798586" duration="4585000" />
      <workItem from="1674711277981" duration="1248000" />
      <workItem from="1674795203696" duration="632000" />
      <workItem from="1674956305817" duration="16968000" />
      <workItem from="1675042526246" duration="14302000" />
      <workItem from="1675128870396" duration="22545000" />
      <workItem from="1675215506091" duration="17650000" />
      <workItem from="1675301829353" duration="11862000" />
      <workItem from="1675317900538" duration="2459000" />
      <workItem from="1675320797059" duration="8621000" />
      <workItem from="1675339852807" duration="739000" />
      <workItem from="1675388440594" duration="25678000" />
      <workItem from="1675565008889" duration="615000" />
      <workItem from="1675647297479" duration="17801000" />
      <workItem from="1675733747003" duration="19725000" />
      <workItem from="1675820110112" duration="15683000" />
      <workItem from="1675906330088" duration="18420000" />
      <workItem from="1675992783178" duration="17171000" />
      <workItem from="1676178694021" duration="617000" />
      <workItem from="1676252057504" duration="20168000" />
      <workItem from="1676278697257" duration="1888000" />
      <workItem from="1676338620254" duration="16449000" />
      <workItem from="1676424739029" duration="11093000" />
      <workItem from="1676511446708" duration="17982000" />
      <workItem from="1676597797384" duration="23152000" />
      <workItem from="1676784007378" duration="752000" />
      <workItem from="1676856757725" duration="29619000" />
      <workItem from="1676943605387" duration="26234000" />
      <workItem from="1677029593393" duration="27361000" />
      <workItem from="1677116087076" duration="7623000" />
      <workItem from="1677135548567" duration="17897000" />
      <workItem from="1677202521190" duration="25463000" />
      <workItem from="1677461288831" duration="23863000" />
      <workItem from="1677548302582" duration="10845000" />
      <workItem from="1677634504002" duration="25761000" />
      <workItem from="1677720904094" duration="19720000" />
      <workItem from="1677807455973" duration="12136000" />
      <workItem from="1677895710867" duration="985000" />
      <workItem from="1678066396082" duration="19018000" />
      <workItem from="1678153837056" duration="611000" />
      <workItem from="1678598322680" duration="2314000" />
      <workItem from="1678671201914" duration="9881000" />
      <workItem from="1678691777858" duration="14708000" />
      <workItem from="1678756830067" duration="28816000" />
      <workItem from="1678843849506" duration="544000" />
      <workItem from="1678844430102" duration="29462000" />
      <workItem from="1678929691110" duration="6468000" />
    </task>
    <servers />
  </component>
  <component name="TimeTrackingManager">
    <option name="totallyTimeSpent" value="1167288000" />
  </component>
  <component name="TodoView">
    <todo-panel id="selected-file">
      <is-autoscroll-to-source value="true" />
    </todo-panel>
    <todo-panel id="all">
      <are-packages-shown value="true" />
      <is-autoscroll-to-source value="true" />
    </todo-panel>
  </component>
  <component name="ToolWindowManager">
    <frame x="-8" y="-8" width="1936" height="1056" extended-state="7" />
    <editor active="true" />
    <layout>
      <window_info anchor="right" id="Palette" order="3" />
      <window_info anchor="bottom" id="TODO" order="10" weight="0.32890365" />
      <window_info anchor="bottom" id="Messages" order="11" weight="0.07087486" />
      <window_info anchor="right" id="Palette&#9;" order="7" />
      <window_info id="Image Layers" order="3" />
      <window_info anchor="right" id="Capture Analysis" order="4" />
      <window_info anchor="bottom" id="Event Log" order="9" side_tool="true" />
      <window_info anchor="right" id="Maven Projects" order="9" sideWeight="0.49594596" weight="0.13219616" />
      <window_info anchor="bottom" id="Database Changes" order="7" show_stripe_button="false" />
      <window_info anchor="right" id="CDI" order="5" />
      <window_info anchor="bottom" id="Version Control" order="8" show_stripe_button="false" />
      <window_info active="true" anchor="bottom" id="Run" order="2" visible="true" weight="0.018952062" />
      <window_info anchor="bottom" id="Spring" order="12" weight="0.32890365" />
      <window_info anchor="bottom" id="Terminal" order="6" weight="0.32890365" />
      <window_info id="Capture Tool" order="6" />
      <window_info id="Designer" order="2" />
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.15511727" />
      <window_info anchor="right" id="Database" order="6" weight="0.32995737" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info id="UI Designer" order="4" />
      <window_info anchor="right" id="Theme Preview" order="8" />
      <window_info id="Favorites" order="5" side_tool="true" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.3645485" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="right" x="0" y="0" width="434" height="856" id="Documentation" order="10" side_tool="true" weight="0.32995737" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="bottom" id="Find" order="1" weight="0.19269103" />
      <window_info anchor="right" id="Coverage" order="10" sideWeight="0.50405407" side_tool="true" visible="true" weight="0.13219616" />
    </layout>
    <layout-to-restore>
      <window_info id="Designer" order="2" />
      <window_info anchor="right" id="Palette" order="3" />
      <window_info anchor="bottom" id="Spring" order="12" weight="0.32890365" />
      <window_info id="Image Layers" order="3" />
      <window_info anchor="bottom" id="Run" order="2" weight="0.019933555" />
      <window_info anchor="bottom" id="Cvs" order="4" weight="0.25" />
      <window_info anchor="right" id="Ant Build" order="1" weight="0.25" />
      <window_info id="UI Designer" order="4" />
      <window_info id="Favorites" order="5" side_tool="true" />
      <window_info anchor="bottom" id="Terminal" order="6" weight="0.32890365" />
      <window_info id="Capture Tool" order="6" />
      <window_info anchor="right" id="Capture Analysis" order="4" />
      <window_info anchor="right" id="CDI" order="5" />
      <window_info anchor="bottom" id="Event Log" order="9" side_tool="true" />
      <window_info anchor="right" x="0" y="0" width="434" height="856" id="Documentation" order="10" side_tool="true" weight="0.32995737" />
      <window_info anchor="right" content_ui="combo" id="Hierarchy" order="2" weight="0.25" />
      <window_info anchor="right" id="Database" order="6" weight="0.32995737" />
      <window_info anchor="right" id="Commander" internal_type="SLIDING" order="0" type="SLIDING" weight="0.4" />
      <window_info anchor="bottom" id="Database Changes" order="7" show_stripe_button="false" />
      <window_info anchor="bottom" id="Version Control" order="8" show_stripe_button="false" />
      <window_info anchor="right" id="Maven Projects" order="9" sideWeight="0.49594596" weight="0.13219616" />
      <window_info anchor="bottom" id="Find" order="1" weight="0.19269103" />
      <window_info content_ui="combo" id="Project" order="0" visible="true" weight="0.15511727" />
      <window_info anchor="bottom" id="Messages" order="11" weight="0.07087486" />
      <window_info anchor="bottom" id="Message" order="0" />
      <window_info anchor="bottom" id="TODO" order="10" weight="0.32890365" />
      <window_info anchor="right" id="Palette&#9;" order="7" />
      <window_info anchor="right" id="Theme Preview" order="8" />
      <window_info id="Structure" order="1" side_tool="true" weight="0.25" />
      <window_info anchor="bottom" id="Inspection" order="5" weight="0.4" />
      <window_info anchor="right" id="Coverage" order="11" sideWeight="0.50405407" side_tool="true" visible="true" weight="0.13219616" />
      <window_info anchor="bottom" id="Debug" order="3" weight="0.29457363" />
    </layout-to-restore>
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="1" />
  </component>
  <component name="VcsContentAnnotationSettings">
    <option name="myLimit" value="2678400000" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/module/pet/PetService.java</url>
          <line>140</line>
          <properties />
          <option name="timeStamp" value="63" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/manager/MysqlHandler.java</url>
          <line>76</line>
          <properties />
          <option name="timeStamp" value="248" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/manager/MysqlHandler.java</url>
          <line>77</line>
          <properties />
          <option name="timeStamp" value="249" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/manager/MysqlHandler.java</url>
          <line>73</line>
          <properties />
          <option name="timeStamp" value="252" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/module/role/RoleService.java</url>
          <line>61</line>
          <properties />
          <option name="timeStamp" value="280" />
        </line-breakpoint>
      </breakpoints>
      <option name="time" value="281" />
    </breakpoint-manager>
  </component>
  <component name="debuggerHistoryManager">
    <expressions id="evaluateExpression">
      <expression>
        <expression-string>sex1</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>sex</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>role</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>builder</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>bytes</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>requestRoleSex</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>roleEntity</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>roleEntity1</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
      <expression>
        <expression-string>entity</expression-string>
        <language-id>JAVA</language-id>
        <evaluation-mode>EXPRESSION</evaluation-mode>
      </expression>
    </expressions>
  </component>
  <component name="editorHistoryManager">
    <entry file="file://$PROJECT_DIR$/src/main/java/module/ad/IAd.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="202">
          <caret line="12" column="17" selection-start-line="12" selection-start-column="17" selection-end-line="12" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/BreedPetRarityMappingConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="220">
          <caret line="5" column="13" selection-start-line="5" selection-start-column="13" selection-end-line="5" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/CommonConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-572">
          <caret line="7" column="13" selection-start-line="7" selection-start-column="13" selection-end-line="7" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/AttributeCounterConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="308">
          <caret line="7" column="21" lean-forward="true" selection-start-line="7" selection-start-column="21" selection-end-line="7" selection-end-column="21" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/MapConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="220">
          <caret line="5" column="13" selection-start-line="5" selection-start-column="13" selection-end-line="5" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/PlayerStatus.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="88">
          <caret line="2" column="12" selection-start-line="2" selection-start-column="12" selection-end-line="2" selection-end-column="12" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/PowerupConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="220">
          <caret line="5" column="13" selection-start-line="5" selection-start-column="13" selection-end-line="5" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/SignConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="132">
          <caret line="3" column="13" selection-start-line="3" selection-start-column="13" selection-end-line="3" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/RoleExpConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="220">
          <caret line="5" column="13" selection-start-line="5" selection-start-column="13" selection-end-line="5" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/RoleAdditionalEntityDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="176">
          <caret line="7" column="13" selection-start-line="7" selection-start-column="13" selection-end-line="7" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/entities/AccusationEntity.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="281">
          <caret line="10" column="13" selection-start-line="10" selection-start-column="13" selection-end-line="10" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/META-INF/MANIFEST.MF">
      <provider selected="true" editor-type-id="text-editor" />
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/entities/RoleEntity.hbm.xml">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-132" />
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/protocol/RoleData.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="492">
          <caret line="2428" column="40" selection-start-line="2428" selection-start-column="40" selection-end-line="2428" selection-end-column="40" />
          <folding>
            <element signature="e#82540#82541#0" expanded="true" />
            <element signature="e#82578#82579#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/role/RoleUtils.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-352">
          <caret line="18" column="23" selection-start-line="18" selection-start-column="23" selection-end-line="18" selection-end-column="23" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/model/RoleDressInfo.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="17">
          <caret line="7" column="13" selection-start-line="7" selection-start-column="13" selection-end-line="7" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/superStar.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="110">
          <caret line="57" selection-start-line="57" selection-end-line="57" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="jar://C:/jdk1.8.0_101/src.zip!/java/util/Properties.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="118">
          <caret line="967" column="18" selection-start-line="967" selection-start-column="18" selection-end-line="967" selection-end-column="18" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/org/hibernate/javax/persistence/hibernate-jpa-2.1-api/1.0.0.Final/hibernate-jpa-2.1-api-1.0.0.Final-sources.jar!/javax/persistence/EntityTransaction.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="253">
          <caret line="33" column="17" selection-start-line="33" selection-start-column="17" selection-end-line="33" selection-end-column="17" />
        </state>
      </provider>
    </entry>
    <entry file="jar://C:/jdk1.8.0_101/src.zip!/java/lang/Thread.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="187">
          <caret line="744" selection-start-line="744" selection-end-line="744" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/org/hibernate/hibernate-core/5.2.6.Final/hibernate-core-5.2.6.Final-sources.jar!/org/hibernate/Query.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-685">
          <caret line="437" column="6" selection-start-line="437" selection-start-column="6" selection-end-line="437" selection-end-column="6" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/rank/RankDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-616">
          <caret line="36" column="22" lean-forward="true" selection-start-line="36" selection-start-column="22" selection-end-line="36" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/org/slf4j/slf4j-api/1.7.12/slf4j-api-1.7.12-sources.jar!/org/slf4j/LoggerFactory.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="57">
          <caret line="59" column="2" lean-forward="true" selection-start-line="59" selection-start-column="2" selection-end-line="59" selection-end-column="2" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/model/RoleInfo.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="149">
          <caret line="10" column="13" selection-start-line="10" selection-start-column="13" selection-end-line="10" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/model/LoginInfo.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="270">
          <caret line="7" column="13" selection-start-line="7" selection-start-column="13" selection-end-line="7" selection-end-column="13" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/login/LoginService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="264">
          <caret line="664" column="22" selection-start-line="664" selection-start-column="22" selection-end-line="664" selection-end-column="22" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/protocol/ProtoData.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="162">
          <caret line="3860" column="56" selection-start-line="3860" selection-start-column="52" selection-end-line="3860" selection-end-column="56" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/entities/RoleEntity.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="44">
          <caret line="404" column="34" selection-start-line="404" selection-start-column="34" selection-end-line="404" selection-end-column="34" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/org/hibernate/hibernate-core/5.2.6.Final/hibernate-core-5.2.6.Final-sources.jar!/org/hibernate/Session.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="174">
          <caret line="456" column="31" lean-forward="true" selection-start-line="456" selection-start-column="31" selection-end-line="456" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/rank/RankService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-921">
          <caret line="27" column="41" lean-forward="true" selection-start-line="27" selection-start-column="41" selection-end-line="27" selection-end-column="41" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/protocol/UserData.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="208">
          <caret line="638" column="12" selection-start-line="638" selection-start-column="12" selection-end-line="638" selection-end-column="12" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/pet/PetService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="308">
          <caret line="45" column="31" lean-forward="true" selection-start-line="45" selection-start-column="31" selection-end-line="45" selection-end-column="31" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/friend/FriendService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="365">
          <caret line="389" column="28" selection-start-line="389" selection-start-column="28" selection-end-line="389" selection-end-column="28" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/rank/Test.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="704">
          <caret line="31" column="67" selection-start-line="31" selection-start-column="67" selection-end-line="31" selection-end-column="67" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/role/RoleDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="1012">
          <caret line="26" column="31" selection-start-line="26" selection-start-column="31" selection-end-line="26" selection-end-column="31" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/protocol/SexData.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="25080">
          <caret line="619" column="32" selection-start-line="619" selection-start-column="32" selection-end-line="619" selection-end-column="32" />
          <folding>
            <element signature="e#17823#17824#0" expanded="true" />
            <element signature="e#17848#17849#0" expanded="true" />
            <element signature="e#39045#39046#0" expanded="true" />
            <element signature="e#39096#39097#0" expanded="true" />
            <element signature="e#39485#39486#0" expanded="true" />
            <element signature="e#39523#39524#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/server/SuperServer.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="656">
          <caret line="89" selection-start-line="89" selection-end-line="89" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/common/SuperConfig.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="-25">
          <caret line="24" column="9" lean-forward="true" selection-start-line="24" selection-start-column="9" selection-end-line="24" selection-end-column="9" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/friend/FriendDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="2974">
          <caret line="375" column="33" selection-start-line="375" selection-start-column="33" selection-end-line="375" selection-end-column="33" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/login/LoginDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="159">
          <caret line="1219" column="12" selection-start-line="1219" selection-start-column="12" selection-end-line="1219" selection-end-column="12" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/manager/MySql.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="221">
          <caret line="232" column="29" selection-start-line="232" selection-start-column="29" selection-end-line="232" selection-end-column="29" />
          <folding>
            <element signature="e#1472#1473#0" expanded="true" />
            <element signature="e#1497#1498#0" expanded="true" />
            <element signature="e#1552#1553#0" expanded="true" />
            <element signature="e#1577#1578#0" expanded="true" />
            <element signature="e#1634#1635#0" expanded="true" />
            <element signature="e#1664#1665#0" expanded="true" />
            <element signature="e#1724#1725#0" expanded="true" />
            <element signature="e#1754#1755#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/item/ItemDao.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="6257">
          <caret line="886" column="27" selection-start-line="886" selection-start-column="27" selection-end-line="886" selection-end-column="27" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/manager/MysqlHandler.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="3070">
          <caret line="110" selection-start-line="110" selection-end-line="110" />
          <folding>
            <element signature="imports" expanded="true" />
            <element signature="e#906#907#0" expanded="true" />
            <element signature="e#936#937#0" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/server/SuperServerHandler.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="273">
          <caret line="136" selection-start-line="136" selection-end-line="136" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/handler/codec/ByteToMessageDecoder.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="141">
          <caret line="289" selection-start-line="289" selection-end-line="289" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/handler/timeout/IdleStateHandler.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="220">
          <caret line="286" selection-start-line="286" selection-end-line="286" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/AbstractChannelHandlerContext.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="317">
          <caret line="356" selection-start-line="356" selection-end-line="356" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/DefaultChannelPipeline.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="141">
          <caret line="935" selection-start-line="935" selection-end-line="935" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/nio/AbstractNioByteChannel.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="317">
          <caret line="160" selection-start-line="160" selection-end-line="160" />
        </state>
      </provider>
    </entry>
    <entry file="jar://$USER_HOME$/Desktop/新建文件夹 (2)/apache-maven-3.5.0/apache-maven-3.5.0/Repository/io/netty/netty-all/4.1.17.Final/netty-all-4.1.17.Final-sources.jar!/io/netty/channel/nio/NioEventLoop.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="405">
          <caret line="461" selection-start-line="461" selection-end-line="461" />
        </state>
      </provider>
    </entry>
    <entry file="file://$PROJECT_DIR$/src/main/java/module/role/RoleService.java">
      <provider selected="true" editor-type-id="text-editor">
        <state relative-caret-position="174">
          <caret line="61" selection-start-line="61" selection-end-line="61" />
          <folding>
            <element signature="imports" expanded="true" />
          </folding>
        </state>
      </provider>
    </entry>
  </component>
  <component name="masterDetails">
    <states>
      <state key="ArtifactsStructureConfigurable.UI">
        <settings>
          <artifact-editor />
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.35443038" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="FacetStructureConfigurable.UI">
        <settings>
          <last-edited>Spring</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.35443038" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="GlobalLibrariesConfigurable.UI">
        <settings>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.35443038" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="JdkListConfigurable.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.35443038" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ModuleStructureConfigurable.UI">
        <settings>
          <last-edited>Spring|superstar</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.35443038" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectJDKs.UI">
        <settings>
          <last-edited>1.8</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.2" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
      <state key="ProjectLibrariesConfigurable.UI">
        <settings>
          <last-edited>Maven: antlr:antlr:2.7.7</last-edited>
          <splitter-proportions>
            <option name="proportions">
              <list>
                <option value="0.35443038" />
              </list>
            </option>
          </splitter-proportions>
        </settings>
      </state>
    </states>
  </component>
</project>