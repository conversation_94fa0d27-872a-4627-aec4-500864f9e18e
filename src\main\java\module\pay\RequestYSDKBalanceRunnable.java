package module.pay;

import net.sf.json.JSONObject;
import utils.HttpClientUtils;
import utils.MyUtils;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

import javassist.bytecode.stackmap.BasicBlock.Catch;

public class RequestYSDKBalanceRunnable implements Runnable 
{
    static String url = "https://ysdk.qq.com/mpay/get_balance_m";
    static String sanbox_url = "https://ysdktest.qq.com/mpay/get_balance_m";
    static String key = "jQko8oEQIsGJRxEhumkMa2vIb6hMEL3O&";
    static String sanbox_key = "wDe1P6ph6vGzCmBb&";
    static String appid = "1110190466";
    static String uri = "%2Fv3%2Fr%2Fmpay%2Fget_balance_m";

    String openid; 	
	String openkey;
	String ts;
	String pf;
	String pfkey;
    static String zoneid = "1";

    public void Set(String ysdk_id, String ysdk_key, String ts, String pf, String pfkey, String zoneid )
    {
        openid = ysdk_id;
        openkey = ysdk_key;
        this.ts = ts;
        this.pf = pf;
        this.pfkey = pfkey;
        this.zoneid = zoneid;
    }
    
    public void run() 
    {
        int totalTime = 0;

        try
        {
            //  /v3/r/mpay/get_balance_m
            //  appid=15499&format=json&openid=00000000000000000000000014BDF6E4&openkey=AB43BF3DC5C3C79D358CC5318E41CF59
            //  &pf=myapp_m_qq-00000000-android-00000000-ysdk&pfkey=CA641BC173479B8C0B35BC84873B3DB9&ts=1340880299&userip=*************&zoneid=1
            String paramString;
            paramString =   "appid=" + appid;
            paramString +=  "&openid=" + openid;
            paramString +=  "&openkey=" + openkey;
            paramString +=  "&pf=" + pf;
            paramString +=  "&pfkey=" + pfkey;
            paramString +=  "&ts=" + ts;
            paramString +=  "&zoneid=" + zoneid;

            String paramEncode = URLEncoder.encode(paramString,"UTF-8");
            String urlEncode = "GET&"+uri+"&"+paramEncode;

            String sign = MyUtils.HmacSHA1Encrypt(urlEncode, sanbox_key);

            Map<String, String> params = new HashMap<String,String>();
            params.put("appid", appid);
            params.put("openid", openid);
            params.put("openkey", openkey);
            params.put("pf", pf);
            params.put("pfkey", pfkey);
            params.put("ts", ts);
            params.put("zoneid", zoneid);
            params.put("sign", sign);

            do
            {
                try
                {

                    JSONObject jsonObject = HttpClientUtils.getHttpsRequestSingleton().sendGet(url, params);
                    int ret = jsonObject.getInt("ret");
                    
                    if( ret != 0 )
                    {
                        Thread.sleep(1500);                            
                        totalTime += 15000;
                        if( totalTime > 120000 )//两分钟
                            break;
                        continue;    
                    }
                    else
                    {
                        
                        break;
                    }
                }
                catch(Exception ex)
                {
                    totalTime += 15000;
                    if( totalTime > 120000 )
                        break;
                    continue;                           
                }
            }while(true);
        }
        catch(Exception ex)
        {

        }
    }
}