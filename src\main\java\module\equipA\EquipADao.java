package module.equipA;
import entities.EquipAEntity;
import manager.MySql;
import java.util.List;
public class EquipADao {
    private static EquipADao inst = null;

    public static EquipADao getInstance() {
        if (inst == null) {
            inst = new EquipADao();
        }
        return inst;
    }
    public List<Object> queryEquipA(String uid) {
        StringBuilder stringBuilder = new StringBuilder(" from EquipAEntity where uid='").append(uid).append("'");
      List<Object>  entity = MySql.queryForList(stringBuilder.toString());
        return(List<Object>) entity;
    }
    public EquipAEntity queryEquipS(String uid) {
        StringBuilder stringBuilder = new StringBuilder("  from EquipAEntity where uid='").append(uid).append("'");
        Object entity = MySql.queryForOne(stringBuilder.toString());
        return (EquipAEntity) entity;
    }



}
