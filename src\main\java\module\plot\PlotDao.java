package module.plot;
import entities.plotEntity;
import manager.MySql;
public class PlotDao {
    private static PlotDao inst = null;

    public static PlotDao getInstance() {
        if (inst == null) {
            inst = new PlotDao();
        }
        return inst;
} public plotEntity queryUid(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from plotEntity where uid='").append(uid).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (plotEntity) entity;
    }

}
