package table.character_xp;

import table.LineKey;

public class character_xpLine implements Line<PERSON>ey {
    public int masterlv;
    public int tolevelneedexp;
    public int actionlimt;
    public int costlimt;

    @Override
    public String toString() {
        return "character_xpLine{" +
                "masterlv=" + masterlv +
                ", tolevelneedexp=" + tolevelneedexp +
                ", actionlimt=" + actionlimt +
                ", costlimt=" + costlimt +
                '}';
    }

    @Override
    public int Key() {
        return masterlv;
    }
}
