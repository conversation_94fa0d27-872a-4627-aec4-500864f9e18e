package common;

@ExcelConfigObject(key = "disposition")
public class PetCharaterConfig {
    @ExcelColumn(name = "id")
    private int id;  //  性格id
    @ExcelColumn(name = "atk", isFloat = true)
    private float atkFactor;//攻击
    @ExcelColumn(name = "def", isFloat = true)
    private float defFactor;//防御
    @ExcelColumn(name = "satk", isFloat = true)
    private float satkFactor;//特殊攻击
    @ExcelColumn(name = "sdef", isFloat = true)
    private float sdefFactor;//特殊防御
    @ExcelColumn(name = "speed", isFloat = true)
    private float speedFactor;//速度

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public float getAtkFactor() {
        return atkFactor;
    }

    public void setAtkFactor(float atkFactor) {
        this.atkFactor = atkFactor;
    }

    public float getDefFactor() {
        return defFactor;
    }

    public void setDefFactor(float defFactor) {
        this.defFactor = defFactor;
    }

    public float getSatkFactor() {
        return satkFactor;
    }

    public void setSatkFactor(float satkFactor) {
        this.satkFactor = satkFactor;
    }

    public float getSdefFactor() {
        return sdefFactor;
    }

    public void setSdefFactor(float sdefFactor) {
        this.sdefFactor = sdefFactor;
    }

    public float getSpeedFactor() {
        return speedFactor;
    }

    public void setSpeedFactor(float speedFactor) {
        this.speedFactor = speedFactor;
    }

    @Override
    public String toString() {
        return "PetCharaterConfig{" +
                "id=" + id +
                ", atkFactor=" + atkFactor +
                ", defFactor=" + defFactor +
                ", satkFactor=" + satkFactor +
                ", sdefFactor=" + sdefFactor +
                ", speedFactor=" + speedFactor +
                '}';
    }
}
