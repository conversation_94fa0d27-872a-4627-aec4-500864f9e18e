package common;

import model.CommonInfo;

import java.util.List;

@ExcelConfigObject(key = "petcultivate")
public class PetCultivateConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "rarity")
    private int ur;
    @ExcelColumn(name = "evolution_level")
    private int evolutionLevel;
    @ExcelColumn(name = "bonus_break", isList = true)
    private List<CommonInfo> breakLV;
    @ExcelColumn(name = "minute_breed")
    private int hatchTime;
    @ExcelColumn(name = "level_breed")
    private int hatchLevelLimit;
    //colddown_breed
    @ExcelColumn(name = "colddown_breed")
    private int breedColdDown;
    @ExcelColumn(name = "probability")
    private int probability;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUr() {
        return ur;
    }

    public void setUr(int ur) {
        this.ur = ur;
    }

    public int getEvolutionLevel() {
        return evolutionLevel;
    }

    public void setEvolutionLevel(int evolutionLevel) {
        this.evolutionLevel = evolutionLevel;
    }

    public List<CommonInfo> getBreakLV() {
        return breakLV;
    }

    public void setBreakLV(List<CommonInfo> breakLV) {
        this.breakLV = breakLV;
    }

    public int getHatchTime() {
        return hatchTime;
    }

    public void setHatchTime(int hatchTime) {
        this.hatchTime = hatchTime;
    }

    public int getHatchLevelLimit() {
        return hatchLevelLimit;
    }

    public void setHatchLevelLimit(int hatchLevelLimit) {
        this.hatchLevelLimit = hatchLevelLimit;
    }

    public int getBreedColdDown() {
        return breedColdDown;
    }

    public void setBreedColdDown(int breedColdDown) {
        this.breedColdDown = breedColdDown;
    }

    public int getProbability() {
        return probability;
    }

    public void setProbability(int probability) {
        this.probability = probability;
    }

    @Override
    public String toString() {
        return "PetCultivateConfig{" +
                "id=" + id +
                ", ur=" + ur +
                ", evolutionLevel=" + evolutionLevel +
                ", breakLV=" + breakLV +
                ", hatchTime=" + hatchTime +
                ", hatchLevelLimit=" + hatchLevelLimit +
                ", breedColdDown=" + breedColdDown +
                ", probability=" + probability +
                '}';
    }
}
