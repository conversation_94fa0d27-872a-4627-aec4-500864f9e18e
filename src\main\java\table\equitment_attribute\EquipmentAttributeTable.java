package table.equitment_attribute;

import protocol.EquipAData;
import table.TableManager;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

public class EquipmentAttributeTable extends TableManager<EquipmentAttributeLine> {
    private static EquipmentAttributeTable inst = null;
    public static EquipmentAttributeTable getInstance() {
        if (inst == null) {
            inst = new EquipmentAttributeTable();
        }
        return inst;
    }

    private List<EquipmentAttributeLine> lines = new ArrayList<>();
    private EquipmentAttributeTable() {
        for (EquipmentAttributeLine data :
                GetAllItem()) {
            data.Parse();
        }

        for (EquipmentAttributeLine data :
                GetAllItem()) {
            lines.add(data);
        }
    }

    /// <summary>
    /// 生成装备，提交服务器
    /// </summary>
    /// <param name="type">手持 衣服 鞋子 饰品</param>
    /// <param name="level_required">5 10 15 20</param>
    /// <returns></returns>
    public EquipAData.EquipA.Builder GenerateEquip(String type, int level_required) {
//        Debug.Log(string.Format("装备类型：{0} 装备等级：{1}", type, level_required));
//        List<EquipmentAttributeLine> curEquips =
//                lines.FindAll(delegate (EquipmentAttributeLine data) { return data.type.Equals(type); })
//        .FindAll(delegate (EquipmentAttributeLine data) { return data.level_required == level_required; });
        List<EquipmentAttributeLine> curEquips =
                lines.stream().filter(data -> data.type.equals(type)).collect(Collectors.toList())
                .stream().filter(data -> data.level_required==level_required).collect(Collectors.toList());

        if (curEquips != null && curEquips.size() > 0) {
//            return curEquips[UnityEngine.Random.Range(0, curEquips.Count)].GenerateEquip();
            return curEquips.get(new Random().nextInt(curEquips.size())).GenerateEquip();
        }

        return null;
    }



    @Override
    public String LinePath() {
        return "table.equitment_attribute.EquipmentAttributeLine";
    }

    @Override
    public String TableName() {
        return "equitmentAttribute";
    }
}
