package entities;

import protocol.MapData;
import protocol.ProtoData;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "map", schema = "", catalog = "super_star_fruit")
public class MapEntity {
    private int id;
    private int mapcount;
    private int mapnumnow;
    private String friendId;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "mapcount")
    public int getMapcount() {
        return mapcount;
    }

    public void setMapcount(int mapcount) {
        this.mapcount = mapcount;
    }

    @Basic
    @Column(name = "mapnumnow")
    public int getMapnumnow() {
        return mapnumnow;
    }

    public void setMapnumnow(int mapnumnow) {
        this.mapnumnow = mapnumnow;
    }

    @Basic
    @Column(name = "friendId")
    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }

    @Override
    public String toString() {
        return "MapEntity{" +
                "id=" + id +
                ", mapcount=" + mapcount +
                ", mapnumnow=" + mapnumnow +
                ", friendId='" + friendId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MapEntity mapEntity = (MapEntity) o;
        return getId() == mapEntity.getId() &&
                getMapcount() == mapEntity.getMapcount() &&
                getMapnumnow() == mapEntity.getMapnumnow() &&
                Objects.equals(getFriendId(), mapEntity.getFriendId());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getMapcount(), getMapnumnow(), getFriendId());
    }

    public static MapData.Map entityToPb(MapEntity entity) {
        MapData.Map map = null;
        if (entity != null) {
            MapData.Map.Builder builder = MapData.Map.newBuilder();
            builder.setMapCount(entity.getMapcount());
            builder.setMapCurrent(entity.getMapnumnow());
        }
        return map;
    }
}
