// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: consume.proto

package protocol;

public final class ConsumeData {
  private ConsumeData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestPhysicalOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.Item consume = 1;
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    java.util.List<protocol.ItemData.Item> 
        getConsumeList();
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    protocol.ItemData.Item getConsume(int index);
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    int getConsumeCount();
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getConsumeOrBuilderList();
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    protocol.ItemData.ItemOrBuilder getConsumeOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.RequestPhysical}
   *
   * <pre>
   *1046
   * </pre>
   */
  public static final class RequestPhysical extends
      com.google.protobuf.GeneratedMessage
      implements RequestPhysicalOrBuilder {
    // Use RequestPhysical.newBuilder() to construct.
    private RequestPhysical(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPhysical(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPhysical defaultInstance;
    public static RequestPhysical getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPhysical getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPhysical(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                consume_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000001;
              }
              consume_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          consume_ = java.util.Collections.unmodifiableList(consume_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ConsumeData.internal_static_protocol_RequestPhysical_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ConsumeData.internal_static_protocol_RequestPhysical_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ConsumeData.RequestPhysical.class, protocol.ConsumeData.RequestPhysical.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPhysical> PARSER =
        new com.google.protobuf.AbstractParser<RequestPhysical>() {
      public RequestPhysical parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPhysical(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPhysical> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.Item consume = 1;
    public static final int CONSUME_FIELD_NUMBER = 1;
    private java.util.List<protocol.ItemData.Item> consume_;
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    public java.util.List<protocol.ItemData.Item> getConsumeList() {
      return consume_;
    }
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getConsumeOrBuilderList() {
      return consume_;
    }
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    public int getConsumeCount() {
      return consume_.size();
    }
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    public protocol.ItemData.Item getConsume(int index) {
      return consume_.get(index);
    }
    /**
     * <code>repeated .protocol.Item consume = 1;</code>
     */
    public protocol.ItemData.ItemOrBuilder getConsumeOrBuilder(
        int index) {
      return consume_.get(index);
    }

    private void initFields() {
      consume_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getConsumeCount(); i++) {
        if (!getConsume(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < consume_.size(); i++) {
        output.writeMessage(1, consume_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < consume_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, consume_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ConsumeData.RequestPhysical parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPhysical parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ConsumeData.RequestPhysical parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.RequestPhysical parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ConsumeData.RequestPhysical prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPhysical}
     *
     * <pre>
     *1046
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ConsumeData.RequestPhysicalOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ConsumeData.internal_static_protocol_RequestPhysical_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ConsumeData.internal_static_protocol_RequestPhysical_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ConsumeData.RequestPhysical.class, protocol.ConsumeData.RequestPhysical.Builder.class);
      }

      // Construct using protocol.ConsumeData.RequestPhysical.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getConsumeFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (consumeBuilder_ == null) {
          consume_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          consumeBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ConsumeData.internal_static_protocol_RequestPhysical_descriptor;
      }

      public protocol.ConsumeData.RequestPhysical getDefaultInstanceForType() {
        return protocol.ConsumeData.RequestPhysical.getDefaultInstance();
      }

      public protocol.ConsumeData.RequestPhysical build() {
        protocol.ConsumeData.RequestPhysical result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ConsumeData.RequestPhysical buildPartial() {
        protocol.ConsumeData.RequestPhysical result = new protocol.ConsumeData.RequestPhysical(this);
        int from_bitField0_ = bitField0_;
        if (consumeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            consume_ = java.util.Collections.unmodifiableList(consume_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.consume_ = consume_;
        } else {
          result.consume_ = consumeBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ConsumeData.RequestPhysical) {
          return mergeFrom((protocol.ConsumeData.RequestPhysical)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ConsumeData.RequestPhysical other) {
        if (other == protocol.ConsumeData.RequestPhysical.getDefaultInstance()) return this;
        if (consumeBuilder_ == null) {
          if (!other.consume_.isEmpty()) {
            if (consume_.isEmpty()) {
              consume_ = other.consume_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureConsumeIsMutable();
              consume_.addAll(other.consume_);
            }
            onChanged();
          }
        } else {
          if (!other.consume_.isEmpty()) {
            if (consumeBuilder_.isEmpty()) {
              consumeBuilder_.dispose();
              consumeBuilder_ = null;
              consume_ = other.consume_;
              bitField0_ = (bitField0_ & ~0x00000001);
              consumeBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getConsumeFieldBuilder() : null;
            } else {
              consumeBuilder_.addAllMessages(other.consume_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getConsumeCount(); i++) {
          if (!getConsume(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ConsumeData.RequestPhysical parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ConsumeData.RequestPhysical) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.Item consume = 1;
      private java.util.List<protocol.ItemData.Item> consume_ =
        java.util.Collections.emptyList();
      private void ensureConsumeIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          consume_ = new java.util.ArrayList<protocol.ItemData.Item>(consume_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> consumeBuilder_;

      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public java.util.List<protocol.ItemData.Item> getConsumeList() {
        if (consumeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consume_);
        } else {
          return consumeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public int getConsumeCount() {
        if (consumeBuilder_ == null) {
          return consume_.size();
        } else {
          return consumeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public protocol.ItemData.Item getConsume(int index) {
        if (consumeBuilder_ == null) {
          return consume_.get(index);
        } else {
          return consumeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder setConsume(
          int index, protocol.ItemData.Item value) {
        if (consumeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeIsMutable();
          consume_.set(index, value);
          onChanged();
        } else {
          consumeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder setConsume(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder addConsume(protocol.ItemData.Item value) {
        if (consumeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeIsMutable();
          consume_.add(value);
          onChanged();
        } else {
          consumeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder addConsume(
          int index, protocol.ItemData.Item value) {
        if (consumeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeIsMutable();
          consume_.add(index, value);
          onChanged();
        } else {
          consumeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder addConsume(
          protocol.ItemData.Item.Builder builderForValue) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.add(builderForValue.build());
          onChanged();
        } else {
          consumeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder addConsume(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder addAllConsume(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          super.addAll(values, consume_);
          onChanged();
        } else {
          consumeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder clearConsume() {
        if (consumeBuilder_ == null) {
          consume_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          consumeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public Builder removeConsume(int index) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.remove(index);
          onChanged();
        } else {
          consumeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public protocol.ItemData.Item.Builder getConsumeBuilder(
          int index) {
        return getConsumeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public protocol.ItemData.ItemOrBuilder getConsumeOrBuilder(
          int index) {
        if (consumeBuilder_ == null) {
          return consume_.get(index);  } else {
          return consumeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getConsumeOrBuilderList() {
        if (consumeBuilder_ != null) {
          return consumeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consume_);
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public protocol.ItemData.Item.Builder addConsumeBuilder() {
        return getConsumeFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public protocol.ItemData.Item.Builder addConsumeBuilder(
          int index) {
        return getConsumeFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item consume = 1;</code>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getConsumeBuilderList() {
        return getConsumeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getConsumeFieldBuilder() {
        if (consumeBuilder_ == null) {
          consumeBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  consume_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          consume_ = null;
        }
        return consumeBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPhysical)
    }

    static {
      defaultInstance = new RequestPhysical(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPhysical)
  }

  public interface ResponsePhysicalOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // repeated .protocol.Item consume = 2;
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    java.util.List<protocol.ItemData.Item> 
        getConsumeList();
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    protocol.ItemData.Item getConsume(int index);
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    int getConsumeCount();
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getConsumeOrBuilderList();
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    protocol.ItemData.ItemOrBuilder getConsumeOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponsePhysical}
   */
  public static final class ResponsePhysical extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePhysicalOrBuilder {
    // Use ResponsePhysical.newBuilder() to construct.
    private ResponsePhysical(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePhysical(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePhysical defaultInstance;
    public static ResponsePhysical getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePhysical getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePhysical(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                consume_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000002;
              }
              consume_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          consume_ = java.util.Collections.unmodifiableList(consume_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ConsumeData.internal_static_protocol_ResponsePhysical_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ConsumeData.internal_static_protocol_ResponsePhysical_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ConsumeData.ResponsePhysical.class, protocol.ConsumeData.ResponsePhysical.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePhysical> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePhysical>() {
      public ResponsePhysical parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePhysical(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePhysical> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // repeated .protocol.Item consume = 2;
    public static final int CONSUME_FIELD_NUMBER = 2;
    private java.util.List<protocol.ItemData.Item> consume_;
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    public java.util.List<protocol.ItemData.Item> getConsumeList() {
      return consume_;
    }
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getConsumeOrBuilderList() {
      return consume_;
    }
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    public int getConsumeCount() {
      return consume_.size();
    }
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    public protocol.ItemData.Item getConsume(int index) {
      return consume_.get(index);
    }
    /**
     * <code>repeated .protocol.Item consume = 2;</code>
     */
    public protocol.ItemData.ItemOrBuilder getConsumeOrBuilder(
        int index) {
      return consume_.get(index);
    }

    private void initFields() {
      errorId_ = 0;
      consume_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getConsumeCount(); i++) {
        if (!getConsume(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      for (int i = 0; i < consume_.size(); i++) {
        output.writeMessage(2, consume_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      for (int i = 0; i < consume_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, consume_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ConsumeData.ResponsePhysical parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePhysical parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ConsumeData.ResponsePhysical parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.ResponsePhysical parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ConsumeData.ResponsePhysical prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePhysical}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ConsumeData.ResponsePhysicalOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ConsumeData.internal_static_protocol_ResponsePhysical_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ConsumeData.internal_static_protocol_ResponsePhysical_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ConsumeData.ResponsePhysical.class, protocol.ConsumeData.ResponsePhysical.Builder.class);
      }

      // Construct using protocol.ConsumeData.ResponsePhysical.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getConsumeFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (consumeBuilder_ == null) {
          consume_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          consumeBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ConsumeData.internal_static_protocol_ResponsePhysical_descriptor;
      }

      public protocol.ConsumeData.ResponsePhysical getDefaultInstanceForType() {
        return protocol.ConsumeData.ResponsePhysical.getDefaultInstance();
      }

      public protocol.ConsumeData.ResponsePhysical build() {
        protocol.ConsumeData.ResponsePhysical result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ConsumeData.ResponsePhysical buildPartial() {
        protocol.ConsumeData.ResponsePhysical result = new protocol.ConsumeData.ResponsePhysical(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (consumeBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            consume_ = java.util.Collections.unmodifiableList(consume_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.consume_ = consume_;
        } else {
          result.consume_ = consumeBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ConsumeData.ResponsePhysical) {
          return mergeFrom((protocol.ConsumeData.ResponsePhysical)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ConsumeData.ResponsePhysical other) {
        if (other == protocol.ConsumeData.ResponsePhysical.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (consumeBuilder_ == null) {
          if (!other.consume_.isEmpty()) {
            if (consume_.isEmpty()) {
              consume_ = other.consume_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureConsumeIsMutable();
              consume_.addAll(other.consume_);
            }
            onChanged();
          }
        } else {
          if (!other.consume_.isEmpty()) {
            if (consumeBuilder_.isEmpty()) {
              consumeBuilder_.dispose();
              consumeBuilder_ = null;
              consume_ = other.consume_;
              bitField0_ = (bitField0_ & ~0x00000002);
              consumeBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getConsumeFieldBuilder() : null;
            } else {
              consumeBuilder_.addAllMessages(other.consume_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getConsumeCount(); i++) {
          if (!getConsume(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ConsumeData.ResponsePhysical parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ConsumeData.ResponsePhysical) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item consume = 2;
      private java.util.List<protocol.ItemData.Item> consume_ =
        java.util.Collections.emptyList();
      private void ensureConsumeIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          consume_ = new java.util.ArrayList<protocol.ItemData.Item>(consume_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> consumeBuilder_;

      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public java.util.List<protocol.ItemData.Item> getConsumeList() {
        if (consumeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consume_);
        } else {
          return consumeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public int getConsumeCount() {
        if (consumeBuilder_ == null) {
          return consume_.size();
        } else {
          return consumeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public protocol.ItemData.Item getConsume(int index) {
        if (consumeBuilder_ == null) {
          return consume_.get(index);
        } else {
          return consumeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder setConsume(
          int index, protocol.ItemData.Item value) {
        if (consumeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeIsMutable();
          consume_.set(index, value);
          onChanged();
        } else {
          consumeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder setConsume(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder addConsume(protocol.ItemData.Item value) {
        if (consumeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeIsMutable();
          consume_.add(value);
          onChanged();
        } else {
          consumeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder addConsume(
          int index, protocol.ItemData.Item value) {
        if (consumeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeIsMutable();
          consume_.add(index, value);
          onChanged();
        } else {
          consumeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder addConsume(
          protocol.ItemData.Item.Builder builderForValue) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.add(builderForValue.build());
          onChanged();
        } else {
          consumeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder addConsume(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder addAllConsume(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          super.addAll(values, consume_);
          onChanged();
        } else {
          consumeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder clearConsume() {
        if (consumeBuilder_ == null) {
          consume_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          consumeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public Builder removeConsume(int index) {
        if (consumeBuilder_ == null) {
          ensureConsumeIsMutable();
          consume_.remove(index);
          onChanged();
        } else {
          consumeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public protocol.ItemData.Item.Builder getConsumeBuilder(
          int index) {
        return getConsumeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public protocol.ItemData.ItemOrBuilder getConsumeOrBuilder(
          int index) {
        if (consumeBuilder_ == null) {
          return consume_.get(index);  } else {
          return consumeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getConsumeOrBuilderList() {
        if (consumeBuilder_ != null) {
          return consumeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consume_);
        }
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public protocol.ItemData.Item.Builder addConsumeBuilder() {
        return getConsumeFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public protocol.ItemData.Item.Builder addConsumeBuilder(
          int index) {
        return getConsumeFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item consume = 2;</code>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getConsumeBuilderList() {
        return getConsumeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getConsumeFieldBuilder() {
        if (consumeBuilder_ == null) {
          consumeBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  consume_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          consume_ = null;
        }
        return consumeBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePhysical)
    }

    static {
      defaultInstance = new ResponsePhysical(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePhysical)
  }

  public interface RequestPetHatchOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 num = 1;
    /**
     * <code>required int32 num = 1;</code>
     */
    boolean hasNum();
    /**
     * <code>required int32 num = 1;</code>
     */
    int getNum();
  }
  /**
   * Protobuf type {@code protocol.RequestPetHatch}
   *
   * <pre>
   *1466 培育消费
   * </pre>
   */
  public static final class RequestPetHatch extends
      com.google.protobuf.GeneratedMessage
      implements RequestPetHatchOrBuilder {
    // Use RequestPetHatch.newBuilder() to construct.
    private RequestPetHatch(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPetHatch(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPetHatch defaultInstance;
    public static RequestPetHatch getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPetHatch getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPetHatch(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              num_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ConsumeData.internal_static_protocol_RequestPetHatch_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ConsumeData.internal_static_protocol_RequestPetHatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ConsumeData.RequestPetHatch.class, protocol.ConsumeData.RequestPetHatch.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPetHatch> PARSER =
        new com.google.protobuf.AbstractParser<RequestPetHatch>() {
      public RequestPetHatch parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPetHatch(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPetHatch> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 num = 1;
    public static final int NUM_FIELD_NUMBER = 1;
    private int num_;
    /**
     * <code>required int32 num = 1;</code>
     */
    public boolean hasNum() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 num = 1;</code>
     */
    public int getNum() {
      return num_;
    }

    private void initFields() {
      num_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasNum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, num_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, num_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ConsumeData.RequestPetHatch parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPetHatch parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ConsumeData.RequestPetHatch parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.RequestPetHatch parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ConsumeData.RequestPetHatch prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPetHatch}
     *
     * <pre>
     *1466 培育消费
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ConsumeData.RequestPetHatchOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ConsumeData.internal_static_protocol_RequestPetHatch_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ConsumeData.internal_static_protocol_RequestPetHatch_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ConsumeData.RequestPetHatch.class, protocol.ConsumeData.RequestPetHatch.Builder.class);
      }

      // Construct using protocol.ConsumeData.RequestPetHatch.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ConsumeData.internal_static_protocol_RequestPetHatch_descriptor;
      }

      public protocol.ConsumeData.RequestPetHatch getDefaultInstanceForType() {
        return protocol.ConsumeData.RequestPetHatch.getDefaultInstance();
      }

      public protocol.ConsumeData.RequestPetHatch build() {
        protocol.ConsumeData.RequestPetHatch result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ConsumeData.RequestPetHatch buildPartial() {
        protocol.ConsumeData.RequestPetHatch result = new protocol.ConsumeData.RequestPetHatch(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.num_ = num_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ConsumeData.RequestPetHatch) {
          return mergeFrom((protocol.ConsumeData.RequestPetHatch)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ConsumeData.RequestPetHatch other) {
        if (other == protocol.ConsumeData.RequestPetHatch.getDefaultInstance()) return this;
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasNum()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ConsumeData.RequestPetHatch parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ConsumeData.RequestPetHatch) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 num = 1;
      private int num_ ;
      /**
       * <code>required int32 num = 1;</code>
       */
      public boolean hasNum() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 num = 1;</code>
       */
      public int getNum() {
        return num_;
      }
      /**
       * <code>required int32 num = 1;</code>
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000001;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 num = 1;</code>
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPetHatch)
    }

    static {
      defaultInstance = new RequestPetHatch(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPetHatch)
  }

  public interface ResponsePetHatchOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.Item item = 1;
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponsePetHatch}
   *
   * <pre>
   *2466
   * </pre>
   */
  public static final class ResponsePetHatch extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePetHatchOrBuilder {
    // Use ResponsePetHatch.newBuilder() to construct.
    private ResponsePetHatch(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePetHatch(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePetHatch defaultInstance;
    public static ResponsePetHatch getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePetHatch getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePetHatch(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000001;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ConsumeData.internal_static_protocol_ResponsePetHatch_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ConsumeData.internal_static_protocol_ResponsePetHatch_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ConsumeData.ResponsePetHatch.class, protocol.ConsumeData.ResponsePetHatch.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePetHatch> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePetHatch>() {
      public ResponsePetHatch parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePetHatch(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePetHatch> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.Item item = 1;
    public static final int ITEM_FIELD_NUMBER = 1;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 1;</code>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    private void initFields() {
      item_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(1, item_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, item_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ConsumeData.ResponsePetHatch parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ConsumeData.ResponsePetHatch parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ConsumeData.ResponsePetHatch prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePetHatch}
     *
     * <pre>
     *2466
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ConsumeData.ResponsePetHatchOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ConsumeData.internal_static_protocol_ResponsePetHatch_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ConsumeData.internal_static_protocol_ResponsePetHatch_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ConsumeData.ResponsePetHatch.class, protocol.ConsumeData.ResponsePetHatch.Builder.class);
      }

      // Construct using protocol.ConsumeData.ResponsePetHatch.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          itemBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ConsumeData.internal_static_protocol_ResponsePetHatch_descriptor;
      }

      public protocol.ConsumeData.ResponsePetHatch getDefaultInstanceForType() {
        return protocol.ConsumeData.ResponsePetHatch.getDefaultInstance();
      }

      public protocol.ConsumeData.ResponsePetHatch build() {
        protocol.ConsumeData.ResponsePetHatch result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ConsumeData.ResponsePetHatch buildPartial() {
        protocol.ConsumeData.ResponsePetHatch result = new protocol.ConsumeData.ResponsePetHatch(this);
        int from_bitField0_ = bitField0_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ConsumeData.ResponsePetHatch) {
          return mergeFrom((protocol.ConsumeData.ResponsePetHatch)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ConsumeData.ResponsePetHatch other) {
        if (other == protocol.ConsumeData.ResponsePetHatch.getDefaultInstance()) return this;
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000001);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ConsumeData.ResponsePetHatch parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ConsumeData.ResponsePetHatch) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.Item item = 1;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 1;</code>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePetHatch)
    }

    static {
      defaultInstance = new ResponsePetHatch(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePetHatch)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPhysical_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPhysical_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePhysical_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePhysical_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPetHatch_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPetHatch_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePetHatch_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePetHatch_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rconsume.proto\022\010protocol\032\nitem.proto\"2\n" +
      "\017RequestPhysical\022\037\n\007consume\030\001 \003(\0132\016.prot" +
      "ocol.Item\"D\n\020ResponsePhysical\022\017\n\007errorId" +
      "\030\001 \002(\005\022\037\n\007consume\030\002 \003(\0132\016.protocol.Item\"" +
      "\036\n\017RequestPetHatch\022\013\n\003num\030\001 \002(\005\"0\n\020Respo" +
      "nsePetHatch\022\034\n\004item\030\001 \003(\0132\016.protocol.Ite" +
      "mB\rB\013ConsumeData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestPhysical_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestPhysical_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPhysical_descriptor,
              new java.lang.String[] { "Consume", });
          internal_static_protocol_ResponsePhysical_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponsePhysical_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePhysical_descriptor,
              new java.lang.String[] { "ErrorId", "Consume", });
          internal_static_protocol_RequestPetHatch_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestPetHatch_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPetHatch_descriptor,
              new java.lang.String[] { "Num", });
          internal_static_protocol_ResponsePetHatch_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponsePetHatch_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePetHatch_descriptor,
              new java.lang.String[] { "Item", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
