package common;

import java.util.Objects;

@ExcelConfigObject(key = "petexp")
public class PetExpConfig {
    @ExcelColumn(name = "petlv")
    private int petLv;
    @ExcelColumn(name = "exp")
    private int exp;
    @ExcelColumn(name = "design")
    private int totalExp;

    public int getPetLv() {
        return petLv;
    }

    public void setPetLv(int petLv) {
        this.petLv = petLv;
    }

    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    public int getTotalExp() {
        return totalExp;
    }

    public void setTotalExp(int totalExp) {
        this.totalExp = totalExp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PetExpConfig that = (PetExpConfig) o;
        return getPetLv() == that.getPetLv() &&
                getExp() == that.getExp() &&
                getTotalExp() == that.getTotalExp();
    }

    @Override
    public int hashCode() {

        return Objects.hash(getPetLv(), getExp(), getTotalExp());
    }

    @Override
    public String toString() {
        return "PetExpConfig{" +
                "petLv=" + petLv +
                ", exp=" + exp +
                ", totalExp=" + totalExp +
                '}';
    }
}
