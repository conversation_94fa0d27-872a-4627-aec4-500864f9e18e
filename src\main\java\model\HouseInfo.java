package model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

public class HouseInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    private ArrayList<FurnitureInfo> furnitureInfosList;

    public HouseInfo(int capacity) {
        furnitureInfosList = new ArrayList<FurnitureInfo>(capacity);
    }

    public ArrayList<FurnitureInfo> getFurnitureInfosList() {
        return furnitureInfosList;
    }

    public void setFurnitureInfosArray(ArrayList<FurnitureInfo> furnitureInfosList) {
        this.furnitureInfosList = furnitureInfosList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        HouseInfo houseInfo = (HouseInfo) o;
        return Objects.equals(getFurnitureInfosList(), houseInfo.getFurnitureInfosList());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getFurnitureInfosList());
    }

    @Override
    public String toString() {
        return "HouseInfo{" +
                "furnitureInfosArray=" + furnitureInfosList +
                '}';
    }
}
