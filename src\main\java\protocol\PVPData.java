// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: pvp.proto

package protocol;

public final class PVPData {
  private PVPData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface PetDataOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 uid = 1;
    /**
     * <code>required int32 uid = 1;</code>
     */
    boolean hasUid();
    /**
     * <code>required int32 uid = 1;</code>
     */
    int getUid();

    // required int32 id = 2;
    /**
     * <code>required int32 id = 2;</code>
     */
    boolean hasId();
    /**
     * <code>required int32 id = 2;</code>
     */
    int getId();

    // required string nameItem = 3;
    /**
     * <code>required string nameItem = 3;</code>
     */
    boolean hasNameItem();
    /**
     * <code>required string nameItem = 3;</code>
     */
    java.lang.String getNameItem();
    /**
     * <code>required string nameItem = 3;</code>
     */
    com.google.protobuf.ByteString
        getNameItemBytes();

    // required int32 currentLevel = 4;
    /**
     * <code>required int32 currentLevel = 4;</code>
     */
    boolean hasCurrentLevel();
    /**
     * <code>required int32 currentLevel = 4;</code>
     */
    int getCurrentLevel();

    // required float hp = 5;
    /**
     * <code>required float hp = 5;</code>
     */
    boolean hasHp();
    /**
     * <code>required float hp = 5;</code>
     */
    float getHp();

    // required float sp = 6;
    /**
     * <code>required float sp = 6;</code>
     */
    boolean hasSp();
    /**
     * <code>required float sp = 6;</code>
     */
    float getSp();

    // required float attack = 7;
    /**
     * <code>required float attack = 7;</code>
     */
    boolean hasAttack();
    /**
     * <code>required float attack = 7;</code>
     */
    float getAttack();

    // required float defense = 8;
    /**
     * <code>required float defense = 8;</code>
     */
    boolean hasDefense();
    /**
     * <code>required float defense = 8;</code>
     */
    float getDefense();

    // required float specialAttack = 9;
    /**
     * <code>required float specialAttack = 9;</code>
     */
    boolean hasSpecialAttack();
    /**
     * <code>required float specialAttack = 9;</code>
     */
    float getSpecialAttack();

    // required float specialDefense = 10;
    /**
     * <code>required float specialDefense = 10;</code>
     */
    boolean hasSpecialDefense();
    /**
     * <code>required float specialDefense = 10;</code>
     */
    float getSpecialDefense();

    // required float speed = 11;
    /**
     * <code>required float speed = 11;</code>
     */
    boolean hasSpeed();
    /**
     * <code>required float speed = 11;</code>
     */
    float getSpeed();

    // required int32 avoid = 12;
    /**
     * <code>required int32 avoid = 12;</code>
     *
     * <pre>
     * 回避
     * </pre>
     */
    boolean hasAvoid();
    /**
     * <code>required int32 avoid = 12;</code>
     *
     * <pre>
     * 回避
     * </pre>
     */
    int getAvoid();

    // required int32 hid = 13;
    /**
     * <code>required int32 hid = 13;</code>
     *
     * <pre>
     * 命中
     * </pre>
     */
    boolean hasHid();
    /**
     * <code>required int32 hid = 13;</code>
     *
     * <pre>
     * 命中
     * </pre>
     */
    int getHid();

    // required int32 crt = 14;
    /**
     * <code>required int32 crt = 14;</code>
     *
     * <pre>
     * 暴击
     * </pre>
     */
    boolean hasCrt();
    /**
     * <code>required int32 crt = 14;</code>
     *
     * <pre>
     * 暴击
     * </pre>
     */
    int getCrt();

    // required int32 typeone = 15;
    /**
     * <code>required int32 typeone = 15;</code>
     *
     * <pre>
     * 属性 金木水火土
     * </pre>
     */
    boolean hasTypeone();
    /**
     * <code>required int32 typeone = 15;</code>
     *
     * <pre>
     * 属性 金木水火土
     * </pre>
     */
    int getTypeone();
  }
  /**
   * Protobuf type {@code protocol.PetData}
   */
  public static final class PetData extends
      com.google.protobuf.GeneratedMessage
      implements PetDataOrBuilder {
    // Use PetData.newBuilder() to construct.
    private PetData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PetData(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PetData defaultInstance;
    public static PetData getDefaultInstance() {
      return defaultInstance;
    }

    public PetData getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PetData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              uid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              id_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              nameItem_ = input.readBytes();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              currentLevel_ = input.readInt32();
              break;
            }
            case 45: {
              bitField0_ |= 0x00000010;
              hp_ = input.readFloat();
              break;
            }
            case 53: {
              bitField0_ |= 0x00000020;
              sp_ = input.readFloat();
              break;
            }
            case 61: {
              bitField0_ |= 0x00000040;
              attack_ = input.readFloat();
              break;
            }
            case 69: {
              bitField0_ |= 0x00000080;
              defense_ = input.readFloat();
              break;
            }
            case 77: {
              bitField0_ |= 0x00000100;
              specialAttack_ = input.readFloat();
              break;
            }
            case 85: {
              bitField0_ |= 0x00000200;
              specialDefense_ = input.readFloat();
              break;
            }
            case 93: {
              bitField0_ |= 0x00000400;
              speed_ = input.readFloat();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              avoid_ = input.readInt32();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              hid_ = input.readInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              crt_ = input.readInt32();
              break;
            }
            case 120: {
              bitField0_ |= 0x00004000;
              typeone_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_PetData_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_PetData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.PetData.class, protocol.PVPData.PetData.Builder.class);
    }

    public static com.google.protobuf.Parser<PetData> PARSER =
        new com.google.protobuf.AbstractParser<PetData>() {
      public PetData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PetData(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PetData> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 uid = 1;
    public static final int UID_FIELD_NUMBER = 1;
    private int uid_;
    /**
     * <code>required int32 uid = 1;</code>
     */
    public boolean hasUid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 uid = 1;</code>
     */
    public int getUid() {
      return uid_;
    }

    // required int32 id = 2;
    public static final int ID_FIELD_NUMBER = 2;
    private int id_;
    /**
     * <code>required int32 id = 2;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 id = 2;</code>
     */
    public int getId() {
      return id_;
    }

    // required string nameItem = 3;
    public static final int NAMEITEM_FIELD_NUMBER = 3;
    private java.lang.Object nameItem_;
    /**
     * <code>required string nameItem = 3;</code>
     */
    public boolean hasNameItem() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string nameItem = 3;</code>
     */
    public java.lang.String getNameItem() {
      java.lang.Object ref = nameItem_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          nameItem_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string nameItem = 3;</code>
     */
    public com.google.protobuf.ByteString
        getNameItemBytes() {
      java.lang.Object ref = nameItem_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nameItem_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 currentLevel = 4;
    public static final int CURRENTLEVEL_FIELD_NUMBER = 4;
    private int currentLevel_;
    /**
     * <code>required int32 currentLevel = 4;</code>
     */
    public boolean hasCurrentLevel() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 currentLevel = 4;</code>
     */
    public int getCurrentLevel() {
      return currentLevel_;
    }

    // required float hp = 5;
    public static final int HP_FIELD_NUMBER = 5;
    private float hp_;
    /**
     * <code>required float hp = 5;</code>
     */
    public boolean hasHp() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required float hp = 5;</code>
     */
    public float getHp() {
      return hp_;
    }

    // required float sp = 6;
    public static final int SP_FIELD_NUMBER = 6;
    private float sp_;
    /**
     * <code>required float sp = 6;</code>
     */
    public boolean hasSp() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required float sp = 6;</code>
     */
    public float getSp() {
      return sp_;
    }

    // required float attack = 7;
    public static final int ATTACK_FIELD_NUMBER = 7;
    private float attack_;
    /**
     * <code>required float attack = 7;</code>
     */
    public boolean hasAttack() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required float attack = 7;</code>
     */
    public float getAttack() {
      return attack_;
    }

    // required float defense = 8;
    public static final int DEFENSE_FIELD_NUMBER = 8;
    private float defense_;
    /**
     * <code>required float defense = 8;</code>
     */
    public boolean hasDefense() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>required float defense = 8;</code>
     */
    public float getDefense() {
      return defense_;
    }

    // required float specialAttack = 9;
    public static final int SPECIALATTACK_FIELD_NUMBER = 9;
    private float specialAttack_;
    /**
     * <code>required float specialAttack = 9;</code>
     */
    public boolean hasSpecialAttack() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>required float specialAttack = 9;</code>
     */
    public float getSpecialAttack() {
      return specialAttack_;
    }

    // required float specialDefense = 10;
    public static final int SPECIALDEFENSE_FIELD_NUMBER = 10;
    private float specialDefense_;
    /**
     * <code>required float specialDefense = 10;</code>
     */
    public boolean hasSpecialDefense() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>required float specialDefense = 10;</code>
     */
    public float getSpecialDefense() {
      return specialDefense_;
    }

    // required float speed = 11;
    public static final int SPEED_FIELD_NUMBER = 11;
    private float speed_;
    /**
     * <code>required float speed = 11;</code>
     */
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>required float speed = 11;</code>
     */
    public float getSpeed() {
      return speed_;
    }

    // required int32 avoid = 12;
    public static final int AVOID_FIELD_NUMBER = 12;
    private int avoid_;
    /**
     * <code>required int32 avoid = 12;</code>
     *
     * <pre>
     * 回避
     * </pre>
     */
    public boolean hasAvoid() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>required int32 avoid = 12;</code>
     *
     * <pre>
     * 回避
     * </pre>
     */
    public int getAvoid() {
      return avoid_;
    }

    // required int32 hid = 13;
    public static final int HID_FIELD_NUMBER = 13;
    private int hid_;
    /**
     * <code>required int32 hid = 13;</code>
     *
     * <pre>
     * 命中
     * </pre>
     */
    public boolean hasHid() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>required int32 hid = 13;</code>
     *
     * <pre>
     * 命中
     * </pre>
     */
    public int getHid() {
      return hid_;
    }

    // required int32 crt = 14;
    public static final int CRT_FIELD_NUMBER = 14;
    private int crt_;
    /**
     * <code>required int32 crt = 14;</code>
     *
     * <pre>
     * 暴击
     * </pre>
     */
    public boolean hasCrt() {
      return ((bitField0_ & 0x00002000) == 0x00002000);
    }
    /**
     * <code>required int32 crt = 14;</code>
     *
     * <pre>
     * 暴击
     * </pre>
     */
    public int getCrt() {
      return crt_;
    }

    // required int32 typeone = 15;
    public static final int TYPEONE_FIELD_NUMBER = 15;
    private int typeone_;
    /**
     * <code>required int32 typeone = 15;</code>
     *
     * <pre>
     * 属性 金木水火土
     * </pre>
     */
    public boolean hasTypeone() {
      return ((bitField0_ & 0x00004000) == 0x00004000);
    }
    /**
     * <code>required int32 typeone = 15;</code>
     *
     * <pre>
     * 属性 金木水火土
     * </pre>
     */
    public int getTypeone() {
      return typeone_;
    }

    private void initFields() {
      uid_ = 0;
      id_ = 0;
      nameItem_ = "";
      currentLevel_ = 0;
      hp_ = 0F;
      sp_ = 0F;
      attack_ = 0F;
      defense_ = 0F;
      specialAttack_ = 0F;
      specialDefense_ = 0F;
      speed_ = 0F;
      avoid_ = 0;
      hid_ = 0;
      crt_ = 0;
      typeone_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasUid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasNameItem()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCurrentLevel()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAttack()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasDefense()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSpecialAttack()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSpecialDefense()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSpeed()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasAvoid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCrt()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTypeone()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, uid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, id_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getNameItemBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, currentLevel_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeFloat(5, hp_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeFloat(6, sp_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeFloat(7, attack_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeFloat(8, defense_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeFloat(9, specialAttack_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeFloat(10, specialDefense_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeFloat(11, speed_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeInt32(12, avoid_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeInt32(13, hid_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        output.writeInt32(14, crt_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        output.writeInt32(15, typeone_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, uid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, id_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getNameItemBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, currentLevel_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(5, hp_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(6, sp_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(7, attack_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(8, defense_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(9, specialAttack_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(10, specialDefense_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(11, speed_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, avoid_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, hid_);
      }
      if (((bitField0_ & 0x00002000) == 0x00002000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(14, crt_);
      }
      if (((bitField0_ & 0x00004000) == 0x00004000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(15, typeone_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.PetData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.PetData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.PetData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.PetData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.PetData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.PetData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.PetData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.PetData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.PetData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.PetData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.PetData prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PetData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.PetDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_PetData_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_PetData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.PetData.class, protocol.PVPData.PetData.Builder.class);
      }

      // Construct using protocol.PVPData.PetData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        uid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        nameItem_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        currentLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        hp_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000010);
        sp_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000020);
        attack_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000040);
        defense_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000080);
        specialAttack_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000100);
        specialDefense_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000200);
        speed_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000400);
        avoid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        hid_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        crt_ = 0;
        bitField0_ = (bitField0_ & ~0x00002000);
        typeone_ = 0;
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_PetData_descriptor;
      }

      public protocol.PVPData.PetData getDefaultInstanceForType() {
        return protocol.PVPData.PetData.getDefaultInstance();
      }

      public protocol.PVPData.PetData build() {
        protocol.PVPData.PetData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.PetData buildPartial() {
        protocol.PVPData.PetData result = new protocol.PVPData.PetData(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.uid_ = uid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.nameItem_ = nameItem_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.currentLevel_ = currentLevel_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.hp_ = hp_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.sp_ = sp_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.attack_ = attack_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.defense_ = defense_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.specialAttack_ = specialAttack_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.specialDefense_ = specialDefense_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.speed_ = speed_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.avoid_ = avoid_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.hid_ = hid_;
        if (((from_bitField0_ & 0x00002000) == 0x00002000)) {
          to_bitField0_ |= 0x00002000;
        }
        result.crt_ = crt_;
        if (((from_bitField0_ & 0x00004000) == 0x00004000)) {
          to_bitField0_ |= 0x00004000;
        }
        result.typeone_ = typeone_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.PetData) {
          return mergeFrom((protocol.PVPData.PetData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.PetData other) {
        if (other == protocol.PVPData.PetData.getDefaultInstance()) return this;
        if (other.hasUid()) {
          setUid(other.getUid());
        }
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasNameItem()) {
          bitField0_ |= 0x00000004;
          nameItem_ = other.nameItem_;
          onChanged();
        }
        if (other.hasCurrentLevel()) {
          setCurrentLevel(other.getCurrentLevel());
        }
        if (other.hasHp()) {
          setHp(other.getHp());
        }
        if (other.hasSp()) {
          setSp(other.getSp());
        }
        if (other.hasAttack()) {
          setAttack(other.getAttack());
        }
        if (other.hasDefense()) {
          setDefense(other.getDefense());
        }
        if (other.hasSpecialAttack()) {
          setSpecialAttack(other.getSpecialAttack());
        }
        if (other.hasSpecialDefense()) {
          setSpecialDefense(other.getSpecialDefense());
        }
        if (other.hasSpeed()) {
          setSpeed(other.getSpeed());
        }
        if (other.hasAvoid()) {
          setAvoid(other.getAvoid());
        }
        if (other.hasHid()) {
          setHid(other.getHid());
        }
        if (other.hasCrt()) {
          setCrt(other.getCrt());
        }
        if (other.hasTypeone()) {
          setTypeone(other.getTypeone());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasUid()) {
          
          return false;
        }
        if (!hasId()) {
          
          return false;
        }
        if (!hasNameItem()) {
          
          return false;
        }
        if (!hasCurrentLevel()) {
          
          return false;
        }
        if (!hasHp()) {
          
          return false;
        }
        if (!hasSp()) {
          
          return false;
        }
        if (!hasAttack()) {
          
          return false;
        }
        if (!hasDefense()) {
          
          return false;
        }
        if (!hasSpecialAttack()) {
          
          return false;
        }
        if (!hasSpecialDefense()) {
          
          return false;
        }
        if (!hasSpeed()) {
          
          return false;
        }
        if (!hasAvoid()) {
          
          return false;
        }
        if (!hasHid()) {
          
          return false;
        }
        if (!hasCrt()) {
          
          return false;
        }
        if (!hasTypeone()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.PetData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.PetData) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 uid = 1;
      private int uid_ ;
      /**
       * <code>required int32 uid = 1;</code>
       */
      public boolean hasUid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 uid = 1;</code>
       */
      public int getUid() {
        return uid_;
      }
      /**
       * <code>required int32 uid = 1;</code>
       */
      public Builder setUid(int value) {
        bitField0_ |= 0x00000001;
        uid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 uid = 1;</code>
       */
      public Builder clearUid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        uid_ = 0;
        onChanged();
        return this;
      }

      // required int32 id = 2;
      private int id_ ;
      /**
       * <code>required int32 id = 2;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 id = 2;</code>
       */
      public int getId() {
        return id_;
      }
      /**
       * <code>required int32 id = 2;</code>
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000002;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 id = 2;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        id_ = 0;
        onChanged();
        return this;
      }

      // required string nameItem = 3;
      private java.lang.Object nameItem_ = "";
      /**
       * <code>required string nameItem = 3;</code>
       */
      public boolean hasNameItem() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string nameItem = 3;</code>
       */
      public java.lang.String getNameItem() {
        java.lang.Object ref = nameItem_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          nameItem_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string nameItem = 3;</code>
       */
      public com.google.protobuf.ByteString
          getNameItemBytes() {
        java.lang.Object ref = nameItem_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          nameItem_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string nameItem = 3;</code>
       */
      public Builder setNameItem(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        nameItem_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string nameItem = 3;</code>
       */
      public Builder clearNameItem() {
        bitField0_ = (bitField0_ & ~0x00000004);
        nameItem_ = getDefaultInstance().getNameItem();
        onChanged();
        return this;
      }
      /**
       * <code>required string nameItem = 3;</code>
       */
      public Builder setNameItemBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        nameItem_ = value;
        onChanged();
        return this;
      }

      // required int32 currentLevel = 4;
      private int currentLevel_ ;
      /**
       * <code>required int32 currentLevel = 4;</code>
       */
      public boolean hasCurrentLevel() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 currentLevel = 4;</code>
       */
      public int getCurrentLevel() {
        return currentLevel_;
      }
      /**
       * <code>required int32 currentLevel = 4;</code>
       */
      public Builder setCurrentLevel(int value) {
        bitField0_ |= 0x00000008;
        currentLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 currentLevel = 4;</code>
       */
      public Builder clearCurrentLevel() {
        bitField0_ = (bitField0_ & ~0x00000008);
        currentLevel_ = 0;
        onChanged();
        return this;
      }

      // required float hp = 5;
      private float hp_ ;
      /**
       * <code>required float hp = 5;</code>
       */
      public boolean hasHp() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required float hp = 5;</code>
       */
      public float getHp() {
        return hp_;
      }
      /**
       * <code>required float hp = 5;</code>
       */
      public Builder setHp(float value) {
        bitField0_ |= 0x00000010;
        hp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float hp = 5;</code>
       */
      public Builder clearHp() {
        bitField0_ = (bitField0_ & ~0x00000010);
        hp_ = 0F;
        onChanged();
        return this;
      }

      // required float sp = 6;
      private float sp_ ;
      /**
       * <code>required float sp = 6;</code>
       */
      public boolean hasSp() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required float sp = 6;</code>
       */
      public float getSp() {
        return sp_;
      }
      /**
       * <code>required float sp = 6;</code>
       */
      public Builder setSp(float value) {
        bitField0_ |= 0x00000020;
        sp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float sp = 6;</code>
       */
      public Builder clearSp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        sp_ = 0F;
        onChanged();
        return this;
      }

      // required float attack = 7;
      private float attack_ ;
      /**
       * <code>required float attack = 7;</code>
       */
      public boolean hasAttack() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required float attack = 7;</code>
       */
      public float getAttack() {
        return attack_;
      }
      /**
       * <code>required float attack = 7;</code>
       */
      public Builder setAttack(float value) {
        bitField0_ |= 0x00000040;
        attack_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float attack = 7;</code>
       */
      public Builder clearAttack() {
        bitField0_ = (bitField0_ & ~0x00000040);
        attack_ = 0F;
        onChanged();
        return this;
      }

      // required float defense = 8;
      private float defense_ ;
      /**
       * <code>required float defense = 8;</code>
       */
      public boolean hasDefense() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>required float defense = 8;</code>
       */
      public float getDefense() {
        return defense_;
      }
      /**
       * <code>required float defense = 8;</code>
       */
      public Builder setDefense(float value) {
        bitField0_ |= 0x00000080;
        defense_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float defense = 8;</code>
       */
      public Builder clearDefense() {
        bitField0_ = (bitField0_ & ~0x00000080);
        defense_ = 0F;
        onChanged();
        return this;
      }

      // required float specialAttack = 9;
      private float specialAttack_ ;
      /**
       * <code>required float specialAttack = 9;</code>
       */
      public boolean hasSpecialAttack() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>required float specialAttack = 9;</code>
       */
      public float getSpecialAttack() {
        return specialAttack_;
      }
      /**
       * <code>required float specialAttack = 9;</code>
       */
      public Builder setSpecialAttack(float value) {
        bitField0_ |= 0x00000100;
        specialAttack_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float specialAttack = 9;</code>
       */
      public Builder clearSpecialAttack() {
        bitField0_ = (bitField0_ & ~0x00000100);
        specialAttack_ = 0F;
        onChanged();
        return this;
      }

      // required float specialDefense = 10;
      private float specialDefense_ ;
      /**
       * <code>required float specialDefense = 10;</code>
       */
      public boolean hasSpecialDefense() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>required float specialDefense = 10;</code>
       */
      public float getSpecialDefense() {
        return specialDefense_;
      }
      /**
       * <code>required float specialDefense = 10;</code>
       */
      public Builder setSpecialDefense(float value) {
        bitField0_ |= 0x00000200;
        specialDefense_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float specialDefense = 10;</code>
       */
      public Builder clearSpecialDefense() {
        bitField0_ = (bitField0_ & ~0x00000200);
        specialDefense_ = 0F;
        onChanged();
        return this;
      }

      // required float speed = 11;
      private float speed_ ;
      /**
       * <code>required float speed = 11;</code>
       */
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>required float speed = 11;</code>
       */
      public float getSpeed() {
        return speed_;
      }
      /**
       * <code>required float speed = 11;</code>
       */
      public Builder setSpeed(float value) {
        bitField0_ |= 0x00000400;
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float speed = 11;</code>
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000400);
        speed_ = 0F;
        onChanged();
        return this;
      }

      // required int32 avoid = 12;
      private int avoid_ ;
      /**
       * <code>required int32 avoid = 12;</code>
       *
       * <pre>
       * 回避
       * </pre>
       */
      public boolean hasAvoid() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>required int32 avoid = 12;</code>
       *
       * <pre>
       * 回避
       * </pre>
       */
      public int getAvoid() {
        return avoid_;
      }
      /**
       * <code>required int32 avoid = 12;</code>
       *
       * <pre>
       * 回避
       * </pre>
       */
      public Builder setAvoid(int value) {
        bitField0_ |= 0x00000800;
        avoid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 avoid = 12;</code>
       *
       * <pre>
       * 回避
       * </pre>
       */
      public Builder clearAvoid() {
        bitField0_ = (bitField0_ & ~0x00000800);
        avoid_ = 0;
        onChanged();
        return this;
      }

      // required int32 hid = 13;
      private int hid_ ;
      /**
       * <code>required int32 hid = 13;</code>
       *
       * <pre>
       * 命中
       * </pre>
       */
      public boolean hasHid() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>required int32 hid = 13;</code>
       *
       * <pre>
       * 命中
       * </pre>
       */
      public int getHid() {
        return hid_;
      }
      /**
       * <code>required int32 hid = 13;</code>
       *
       * <pre>
       * 命中
       * </pre>
       */
      public Builder setHid(int value) {
        bitField0_ |= 0x00001000;
        hid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 hid = 13;</code>
       *
       * <pre>
       * 命中
       * </pre>
       */
      public Builder clearHid() {
        bitField0_ = (bitField0_ & ~0x00001000);
        hid_ = 0;
        onChanged();
        return this;
      }

      // required int32 crt = 14;
      private int crt_ ;
      /**
       * <code>required int32 crt = 14;</code>
       *
       * <pre>
       * 暴击
       * </pre>
       */
      public boolean hasCrt() {
        return ((bitField0_ & 0x00002000) == 0x00002000);
      }
      /**
       * <code>required int32 crt = 14;</code>
       *
       * <pre>
       * 暴击
       * </pre>
       */
      public int getCrt() {
        return crt_;
      }
      /**
       * <code>required int32 crt = 14;</code>
       *
       * <pre>
       * 暴击
       * </pre>
       */
      public Builder setCrt(int value) {
        bitField0_ |= 0x00002000;
        crt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 crt = 14;</code>
       *
       * <pre>
       * 暴击
       * </pre>
       */
      public Builder clearCrt() {
        bitField0_ = (bitField0_ & ~0x00002000);
        crt_ = 0;
        onChanged();
        return this;
      }

      // required int32 typeone = 15;
      private int typeone_ ;
      /**
       * <code>required int32 typeone = 15;</code>
       *
       * <pre>
       * 属性 金木水火土
       * </pre>
       */
      public boolean hasTypeone() {
        return ((bitField0_ & 0x00004000) == 0x00004000);
      }
      /**
       * <code>required int32 typeone = 15;</code>
       *
       * <pre>
       * 属性 金木水火土
       * </pre>
       */
      public int getTypeone() {
        return typeone_;
      }
      /**
       * <code>required int32 typeone = 15;</code>
       *
       * <pre>
       * 属性 金木水火土
       * </pre>
       */
      public Builder setTypeone(int value) {
        bitField0_ |= 0x00004000;
        typeone_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 typeone = 15;</code>
       *
       * <pre>
       * 属性 金木水火土
       * </pre>
       */
      public Builder clearTypeone() {
        bitField0_ = (bitField0_ & ~0x00004000);
        typeone_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PetData)
    }

    static {
      defaultInstance = new PetData(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PetData)
  }

  public interface PVPTeamOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional .protocol.PetData pet1 = 1;
    /**
     * <code>optional .protocol.PetData pet1 = 1;</code>
     */
    boolean hasPet1();
    /**
     * <code>optional .protocol.PetData pet1 = 1;</code>
     */
    protocol.PVPData.PetData getPet1();
    /**
     * <code>optional .protocol.PetData pet1 = 1;</code>
     */
    protocol.PVPData.PetDataOrBuilder getPet1OrBuilder();

    // optional .protocol.PetData pet2 = 2;
    /**
     * <code>optional .protocol.PetData pet2 = 2;</code>
     */
    boolean hasPet2();
    /**
     * <code>optional .protocol.PetData pet2 = 2;</code>
     */
    protocol.PVPData.PetData getPet2();
    /**
     * <code>optional .protocol.PetData pet2 = 2;</code>
     */
    protocol.PVPData.PetDataOrBuilder getPet2OrBuilder();

    // optional .protocol.PetData pet3 = 3;
    /**
     * <code>optional .protocol.PetData pet3 = 3;</code>
     */
    boolean hasPet3();
    /**
     * <code>optional .protocol.PetData pet3 = 3;</code>
     */
    protocol.PVPData.PetData getPet3();
    /**
     * <code>optional .protocol.PetData pet3 = 3;</code>
     */
    protocol.PVPData.PetDataOrBuilder getPet3OrBuilder();

    // optional .protocol.PetData pet4 = 4;
    /**
     * <code>optional .protocol.PetData pet4 = 4;</code>
     */
    boolean hasPet4();
    /**
     * <code>optional .protocol.PetData pet4 = 4;</code>
     */
    protocol.PVPData.PetData getPet4();
    /**
     * <code>optional .protocol.PetData pet4 = 4;</code>
     */
    protocol.PVPData.PetDataOrBuilder getPet4OrBuilder();

    // optional .protocol.PetData pet5 = 5;
    /**
     * <code>optional .protocol.PetData pet5 = 5;</code>
     */
    boolean hasPet5();
    /**
     * <code>optional .protocol.PetData pet5 = 5;</code>
     */
    protocol.PVPData.PetData getPet5();
    /**
     * <code>optional .protocol.PetData pet5 = 5;</code>
     */
    protocol.PVPData.PetDataOrBuilder getPet5OrBuilder();
  }
  /**
   * Protobuf type {@code protocol.PVPTeam}
   */
  public static final class PVPTeam extends
      com.google.protobuf.GeneratedMessage
      implements PVPTeamOrBuilder {
    // Use PVPTeam.newBuilder() to construct.
    private PVPTeam(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PVPTeam(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PVPTeam defaultInstance;
    public static PVPTeam getDefaultInstance() {
      return defaultInstance;
    }

    public PVPTeam getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PVPTeam(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              protocol.PVPData.PetData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = pet1_.toBuilder();
              }
              pet1_ = input.readMessage(protocol.PVPData.PetData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pet1_);
                pet1_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              protocol.PVPData.PetData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = pet2_.toBuilder();
              }
              pet2_ = input.readMessage(protocol.PVPData.PetData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pet2_);
                pet2_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              protocol.PVPData.PetData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = pet3_.toBuilder();
              }
              pet3_ = input.readMessage(protocol.PVPData.PetData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pet3_);
                pet3_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 34: {
              protocol.PVPData.PetData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) == 0x00000008)) {
                subBuilder = pet4_.toBuilder();
              }
              pet4_ = input.readMessage(protocol.PVPData.PetData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pet4_);
                pet4_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              protocol.PVPData.PetData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) == 0x00000010)) {
                subBuilder = pet5_.toBuilder();
              }
              pet5_ = input.readMessage(protocol.PVPData.PetData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pet5_);
                pet5_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_PVPTeam_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_PVPTeam_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.PVPTeam.class, protocol.PVPData.PVPTeam.Builder.class);
    }

    public static com.google.protobuf.Parser<PVPTeam> PARSER =
        new com.google.protobuf.AbstractParser<PVPTeam>() {
      public PVPTeam parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PVPTeam(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PVPTeam> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional .protocol.PetData pet1 = 1;
    public static final int PET1_FIELD_NUMBER = 1;
    private protocol.PVPData.PetData pet1_;
    /**
     * <code>optional .protocol.PetData pet1 = 1;</code>
     */
    public boolean hasPet1() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional .protocol.PetData pet1 = 1;</code>
     */
    public protocol.PVPData.PetData getPet1() {
      return pet1_;
    }
    /**
     * <code>optional .protocol.PetData pet1 = 1;</code>
     */
    public protocol.PVPData.PetDataOrBuilder getPet1OrBuilder() {
      return pet1_;
    }

    // optional .protocol.PetData pet2 = 2;
    public static final int PET2_FIELD_NUMBER = 2;
    private protocol.PVPData.PetData pet2_;
    /**
     * <code>optional .protocol.PetData pet2 = 2;</code>
     */
    public boolean hasPet2() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional .protocol.PetData pet2 = 2;</code>
     */
    public protocol.PVPData.PetData getPet2() {
      return pet2_;
    }
    /**
     * <code>optional .protocol.PetData pet2 = 2;</code>
     */
    public protocol.PVPData.PetDataOrBuilder getPet2OrBuilder() {
      return pet2_;
    }

    // optional .protocol.PetData pet3 = 3;
    public static final int PET3_FIELD_NUMBER = 3;
    private protocol.PVPData.PetData pet3_;
    /**
     * <code>optional .protocol.PetData pet3 = 3;</code>
     */
    public boolean hasPet3() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional .protocol.PetData pet3 = 3;</code>
     */
    public protocol.PVPData.PetData getPet3() {
      return pet3_;
    }
    /**
     * <code>optional .protocol.PetData pet3 = 3;</code>
     */
    public protocol.PVPData.PetDataOrBuilder getPet3OrBuilder() {
      return pet3_;
    }

    // optional .protocol.PetData pet4 = 4;
    public static final int PET4_FIELD_NUMBER = 4;
    private protocol.PVPData.PetData pet4_;
    /**
     * <code>optional .protocol.PetData pet4 = 4;</code>
     */
    public boolean hasPet4() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional .protocol.PetData pet4 = 4;</code>
     */
    public protocol.PVPData.PetData getPet4() {
      return pet4_;
    }
    /**
     * <code>optional .protocol.PetData pet4 = 4;</code>
     */
    public protocol.PVPData.PetDataOrBuilder getPet4OrBuilder() {
      return pet4_;
    }

    // optional .protocol.PetData pet5 = 5;
    public static final int PET5_FIELD_NUMBER = 5;
    private protocol.PVPData.PetData pet5_;
    /**
     * <code>optional .protocol.PetData pet5 = 5;</code>
     */
    public boolean hasPet5() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional .protocol.PetData pet5 = 5;</code>
     */
    public protocol.PVPData.PetData getPet5() {
      return pet5_;
    }
    /**
     * <code>optional .protocol.PetData pet5 = 5;</code>
     */
    public protocol.PVPData.PetDataOrBuilder getPet5OrBuilder() {
      return pet5_;
    }

    private void initFields() {
      pet1_ = protocol.PVPData.PetData.getDefaultInstance();
      pet2_ = protocol.PVPData.PetData.getDefaultInstance();
      pet3_ = protocol.PVPData.PetData.getDefaultInstance();
      pet4_ = protocol.PVPData.PetData.getDefaultInstance();
      pet5_ = protocol.PVPData.PetData.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (hasPet1()) {
        if (!getPet1().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasPet2()) {
        if (!getPet2().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasPet3()) {
        if (!getPet3().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasPet4()) {
        if (!getPet4().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasPet5()) {
        if (!getPet5().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, pet1_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, pet2_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(3, pet3_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeMessage(4, pet4_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeMessage(5, pet5_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, pet1_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, pet2_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, pet3_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, pet4_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, pet5_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.PVPTeam parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.PVPTeam parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.PVPTeam parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.PVPTeam parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.PVPTeam parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.PVPTeam parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.PVPTeam parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.PVPTeam parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.PVPTeam parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.PVPTeam parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.PVPTeam prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PVPTeam}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.PVPTeamOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_PVPTeam_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_PVPTeam_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.PVPTeam.class, protocol.PVPData.PVPTeam.Builder.class);
      }

      // Construct using protocol.PVPData.PVPTeam.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPet1FieldBuilder();
          getPet2FieldBuilder();
          getPet3FieldBuilder();
          getPet4FieldBuilder();
          getPet5FieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (pet1Builder_ == null) {
          pet1_ = protocol.PVPData.PetData.getDefaultInstance();
        } else {
          pet1Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pet2Builder_ == null) {
          pet2_ = protocol.PVPData.PetData.getDefaultInstance();
        } else {
          pet2Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (pet3Builder_ == null) {
          pet3_ = protocol.PVPData.PetData.getDefaultInstance();
        } else {
          pet3Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (pet4Builder_ == null) {
          pet4_ = protocol.PVPData.PetData.getDefaultInstance();
        } else {
          pet4Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (pet5Builder_ == null) {
          pet5_ = protocol.PVPData.PetData.getDefaultInstance();
        } else {
          pet5Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_PVPTeam_descriptor;
      }

      public protocol.PVPData.PVPTeam getDefaultInstanceForType() {
        return protocol.PVPData.PVPTeam.getDefaultInstance();
      }

      public protocol.PVPData.PVPTeam build() {
        protocol.PVPData.PVPTeam result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.PVPTeam buildPartial() {
        protocol.PVPData.PVPTeam result = new protocol.PVPData.PVPTeam(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (pet1Builder_ == null) {
          result.pet1_ = pet1_;
        } else {
          result.pet1_ = pet1Builder_.build();
        }
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (pet2Builder_ == null) {
          result.pet2_ = pet2_;
        } else {
          result.pet2_ = pet2Builder_.build();
        }
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        if (pet3Builder_ == null) {
          result.pet3_ = pet3_;
        } else {
          result.pet3_ = pet3Builder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        if (pet4Builder_ == null) {
          result.pet4_ = pet4_;
        } else {
          result.pet4_ = pet4Builder_.build();
        }
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        if (pet5Builder_ == null) {
          result.pet5_ = pet5_;
        } else {
          result.pet5_ = pet5Builder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.PVPTeam) {
          return mergeFrom((protocol.PVPData.PVPTeam)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.PVPTeam other) {
        if (other == protocol.PVPData.PVPTeam.getDefaultInstance()) return this;
        if (other.hasPet1()) {
          mergePet1(other.getPet1());
        }
        if (other.hasPet2()) {
          mergePet2(other.getPet2());
        }
        if (other.hasPet3()) {
          mergePet3(other.getPet3());
        }
        if (other.hasPet4()) {
          mergePet4(other.getPet4());
        }
        if (other.hasPet5()) {
          mergePet5(other.getPet5());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (hasPet1()) {
          if (!getPet1().isInitialized()) {
            
            return false;
          }
        }
        if (hasPet2()) {
          if (!getPet2().isInitialized()) {
            
            return false;
          }
        }
        if (hasPet3()) {
          if (!getPet3().isInitialized()) {
            
            return false;
          }
        }
        if (hasPet4()) {
          if (!getPet4().isInitialized()) {
            
            return false;
          }
        }
        if (hasPet5()) {
          if (!getPet5().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.PVPTeam parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.PVPTeam) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional .protocol.PetData pet1 = 1;
      private protocol.PVPData.PetData pet1_ = protocol.PVPData.PetData.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> pet1Builder_;
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public boolean hasPet1() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public protocol.PVPData.PetData getPet1() {
        if (pet1Builder_ == null) {
          return pet1_;
        } else {
          return pet1Builder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public Builder setPet1(protocol.PVPData.PetData value) {
        if (pet1Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pet1_ = value;
          onChanged();
        } else {
          pet1Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public Builder setPet1(
          protocol.PVPData.PetData.Builder builderForValue) {
        if (pet1Builder_ == null) {
          pet1_ = builderForValue.build();
          onChanged();
        } else {
          pet1Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public Builder mergePet1(protocol.PVPData.PetData value) {
        if (pet1Builder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              pet1_ != protocol.PVPData.PetData.getDefaultInstance()) {
            pet1_ =
              protocol.PVPData.PetData.newBuilder(pet1_).mergeFrom(value).buildPartial();
          } else {
            pet1_ = value;
          }
          onChanged();
        } else {
          pet1Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public Builder clearPet1() {
        if (pet1Builder_ == null) {
          pet1_ = protocol.PVPData.PetData.getDefaultInstance();
          onChanged();
        } else {
          pet1Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public protocol.PVPData.PetData.Builder getPet1Builder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPet1FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      public protocol.PVPData.PetDataOrBuilder getPet1OrBuilder() {
        if (pet1Builder_ != null) {
          return pet1Builder_.getMessageOrBuilder();
        } else {
          return pet1_;
        }
      }
      /**
       * <code>optional .protocol.PetData pet1 = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> 
          getPet1FieldBuilder() {
        if (pet1Builder_ == null) {
          pet1Builder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder>(
                  pet1_,
                  getParentForChildren(),
                  isClean());
          pet1_ = null;
        }
        return pet1Builder_;
      }

      // optional .protocol.PetData pet2 = 2;
      private protocol.PVPData.PetData pet2_ = protocol.PVPData.PetData.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> pet2Builder_;
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public boolean hasPet2() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public protocol.PVPData.PetData getPet2() {
        if (pet2Builder_ == null) {
          return pet2_;
        } else {
          return pet2Builder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public Builder setPet2(protocol.PVPData.PetData value) {
        if (pet2Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pet2_ = value;
          onChanged();
        } else {
          pet2Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public Builder setPet2(
          protocol.PVPData.PetData.Builder builderForValue) {
        if (pet2Builder_ == null) {
          pet2_ = builderForValue.build();
          onChanged();
        } else {
          pet2Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public Builder mergePet2(protocol.PVPData.PetData value) {
        if (pet2Builder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              pet2_ != protocol.PVPData.PetData.getDefaultInstance()) {
            pet2_ =
              protocol.PVPData.PetData.newBuilder(pet2_).mergeFrom(value).buildPartial();
          } else {
            pet2_ = value;
          }
          onChanged();
        } else {
          pet2Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public Builder clearPet2() {
        if (pet2Builder_ == null) {
          pet2_ = protocol.PVPData.PetData.getDefaultInstance();
          onChanged();
        } else {
          pet2Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public protocol.PVPData.PetData.Builder getPet2Builder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPet2FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      public protocol.PVPData.PetDataOrBuilder getPet2OrBuilder() {
        if (pet2Builder_ != null) {
          return pet2Builder_.getMessageOrBuilder();
        } else {
          return pet2_;
        }
      }
      /**
       * <code>optional .protocol.PetData pet2 = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> 
          getPet2FieldBuilder() {
        if (pet2Builder_ == null) {
          pet2Builder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder>(
                  pet2_,
                  getParentForChildren(),
                  isClean());
          pet2_ = null;
        }
        return pet2Builder_;
      }

      // optional .protocol.PetData pet3 = 3;
      private protocol.PVPData.PetData pet3_ = protocol.PVPData.PetData.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> pet3Builder_;
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public boolean hasPet3() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public protocol.PVPData.PetData getPet3() {
        if (pet3Builder_ == null) {
          return pet3_;
        } else {
          return pet3Builder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public Builder setPet3(protocol.PVPData.PetData value) {
        if (pet3Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pet3_ = value;
          onChanged();
        } else {
          pet3Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public Builder setPet3(
          protocol.PVPData.PetData.Builder builderForValue) {
        if (pet3Builder_ == null) {
          pet3_ = builderForValue.build();
          onChanged();
        } else {
          pet3Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public Builder mergePet3(protocol.PVPData.PetData value) {
        if (pet3Builder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004) &&
              pet3_ != protocol.PVPData.PetData.getDefaultInstance()) {
            pet3_ =
              protocol.PVPData.PetData.newBuilder(pet3_).mergeFrom(value).buildPartial();
          } else {
            pet3_ = value;
          }
          onChanged();
        } else {
          pet3Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public Builder clearPet3() {
        if (pet3Builder_ == null) {
          pet3_ = protocol.PVPData.PetData.getDefaultInstance();
          onChanged();
        } else {
          pet3Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public protocol.PVPData.PetData.Builder getPet3Builder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPet3FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      public protocol.PVPData.PetDataOrBuilder getPet3OrBuilder() {
        if (pet3Builder_ != null) {
          return pet3Builder_.getMessageOrBuilder();
        } else {
          return pet3_;
        }
      }
      /**
       * <code>optional .protocol.PetData pet3 = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> 
          getPet3FieldBuilder() {
        if (pet3Builder_ == null) {
          pet3Builder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder>(
                  pet3_,
                  getParentForChildren(),
                  isClean());
          pet3_ = null;
        }
        return pet3Builder_;
      }

      // optional .protocol.PetData pet4 = 4;
      private protocol.PVPData.PetData pet4_ = protocol.PVPData.PetData.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> pet4Builder_;
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public boolean hasPet4() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public protocol.PVPData.PetData getPet4() {
        if (pet4Builder_ == null) {
          return pet4_;
        } else {
          return pet4Builder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public Builder setPet4(protocol.PVPData.PetData value) {
        if (pet4Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pet4_ = value;
          onChanged();
        } else {
          pet4Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public Builder setPet4(
          protocol.PVPData.PetData.Builder builderForValue) {
        if (pet4Builder_ == null) {
          pet4_ = builderForValue.build();
          onChanged();
        } else {
          pet4Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public Builder mergePet4(protocol.PVPData.PetData value) {
        if (pet4Builder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008) &&
              pet4_ != protocol.PVPData.PetData.getDefaultInstance()) {
            pet4_ =
              protocol.PVPData.PetData.newBuilder(pet4_).mergeFrom(value).buildPartial();
          } else {
            pet4_ = value;
          }
          onChanged();
        } else {
          pet4Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public Builder clearPet4() {
        if (pet4Builder_ == null) {
          pet4_ = protocol.PVPData.PetData.getDefaultInstance();
          onChanged();
        } else {
          pet4Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public protocol.PVPData.PetData.Builder getPet4Builder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getPet4FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      public protocol.PVPData.PetDataOrBuilder getPet4OrBuilder() {
        if (pet4Builder_ != null) {
          return pet4Builder_.getMessageOrBuilder();
        } else {
          return pet4_;
        }
      }
      /**
       * <code>optional .protocol.PetData pet4 = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> 
          getPet4FieldBuilder() {
        if (pet4Builder_ == null) {
          pet4Builder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder>(
                  pet4_,
                  getParentForChildren(),
                  isClean());
          pet4_ = null;
        }
        return pet4Builder_;
      }

      // optional .protocol.PetData pet5 = 5;
      private protocol.PVPData.PetData pet5_ = protocol.PVPData.PetData.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> pet5Builder_;
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public boolean hasPet5() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public protocol.PVPData.PetData getPet5() {
        if (pet5Builder_ == null) {
          return pet5_;
        } else {
          return pet5Builder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public Builder setPet5(protocol.PVPData.PetData value) {
        if (pet5Builder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pet5_ = value;
          onChanged();
        } else {
          pet5Builder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public Builder setPet5(
          protocol.PVPData.PetData.Builder builderForValue) {
        if (pet5Builder_ == null) {
          pet5_ = builderForValue.build();
          onChanged();
        } else {
          pet5Builder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public Builder mergePet5(protocol.PVPData.PetData value) {
        if (pet5Builder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010) &&
              pet5_ != protocol.PVPData.PetData.getDefaultInstance()) {
            pet5_ =
              protocol.PVPData.PetData.newBuilder(pet5_).mergeFrom(value).buildPartial();
          } else {
            pet5_ = value;
          }
          onChanged();
        } else {
          pet5Builder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public Builder clearPet5() {
        if (pet5Builder_ == null) {
          pet5_ = protocol.PVPData.PetData.getDefaultInstance();
          onChanged();
        } else {
          pet5Builder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public protocol.PVPData.PetData.Builder getPet5Builder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getPet5FieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      public protocol.PVPData.PetDataOrBuilder getPet5OrBuilder() {
        if (pet5Builder_ != null) {
          return pet5Builder_.getMessageOrBuilder();
        } else {
          return pet5_;
        }
      }
      /**
       * <code>optional .protocol.PetData pet5 = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder> 
          getPet5FieldBuilder() {
        if (pet5Builder_ == null) {
          pet5Builder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PetData, protocol.PVPData.PetData.Builder, protocol.PVPData.PetDataOrBuilder>(
                  pet5_,
                  getParentForChildren(),
                  isClean());
          pet5_ = null;
        }
        return pet5Builder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PVPTeam)
    }

    static {
      defaultInstance = new PVPTeam(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PVPTeam)
  }

  public interface PVPBattlePlayerOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string name = 1;
    /**
     * <code>required string name = 1;</code>
     */
    boolean hasName();
    /**
     * <code>required string name = 1;</code>
     */
    java.lang.String getName();
    /**
     * <code>required string name = 1;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    // required int32 sex = 2;
    /**
     * <code>required int32 sex = 2;</code>
     */
    boolean hasSex();
    /**
     * <code>required int32 sex = 2;</code>
     */
    int getSex();

    // required int32 head = 3;
    /**
     * <code>required int32 head = 3;</code>
     */
    boolean hasHead();
    /**
     * <code>required int32 head = 3;</code>
     */
    int getHead();
  }
  /**
   * Protobuf type {@code protocol.PVPBattlePlayer}
   *
   * <pre>
   * 界面上的信息，轮播使用
   * </pre>
   */
  public static final class PVPBattlePlayer extends
      com.google.protobuf.GeneratedMessage
      implements PVPBattlePlayerOrBuilder {
    // Use PVPBattlePlayer.newBuilder() to construct.
    private PVPBattlePlayer(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private PVPBattlePlayer(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final PVPBattlePlayer defaultInstance;
    public static PVPBattlePlayer getDefaultInstance() {
      return defaultInstance;
    }

    public PVPBattlePlayer getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private PVPBattlePlayer(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              name_ = input.readBytes();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              sex_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              head_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_PVPBattlePlayer_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_PVPBattlePlayer_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.PVPBattlePlayer.class, protocol.PVPData.PVPBattlePlayer.Builder.class);
    }

    public static com.google.protobuf.Parser<PVPBattlePlayer> PARSER =
        new com.google.protobuf.AbstractParser<PVPBattlePlayer>() {
      public PVPBattlePlayer parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PVPBattlePlayer(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<PVPBattlePlayer> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string name = 1;
    public static final int NAME_FIELD_NUMBER = 1;
    private java.lang.Object name_;
    /**
     * <code>required string name = 1;</code>
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string name = 1;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string name = 1;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 sex = 2;
    public static final int SEX_FIELD_NUMBER = 2;
    private int sex_;
    /**
     * <code>required int32 sex = 2;</code>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 sex = 2;</code>
     */
    public int getSex() {
      return sex_;
    }

    // required int32 head = 3;
    public static final int HEAD_FIELD_NUMBER = 3;
    private int head_;
    /**
     * <code>required int32 head = 3;</code>
     */
    public boolean hasHead() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 head = 3;</code>
     */
    public int getHead() {
      return head_;
    }

    private void initFields() {
      name_ = "";
      sex_ = 0;
      head_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHead()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getNameBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, sex_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, head_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getNameBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, sex_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, head_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.PVPBattlePlayer parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.PVPBattlePlayer parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.PVPBattlePlayer parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.PVPBattlePlayer parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.PVPBattlePlayer prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.PVPBattlePlayer}
     *
     * <pre>
     * 界面上的信息，轮播使用
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.PVPBattlePlayerOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_PVPBattlePlayer_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_PVPBattlePlayer_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.PVPBattlePlayer.class, protocol.PVPData.PVPBattlePlayer.Builder.class);
      }

      // Construct using protocol.PVPData.PVPBattlePlayer.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        sex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        head_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_PVPBattlePlayer_descriptor;
      }

      public protocol.PVPData.PVPBattlePlayer getDefaultInstanceForType() {
        return protocol.PVPData.PVPBattlePlayer.getDefaultInstance();
      }

      public protocol.PVPData.PVPBattlePlayer build() {
        protocol.PVPData.PVPBattlePlayer result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.PVPBattlePlayer buildPartial() {
        protocol.PVPData.PVPBattlePlayer result = new protocol.PVPData.PVPBattlePlayer(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.sex_ = sex_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.head_ = head_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.PVPBattlePlayer) {
          return mergeFrom((protocol.PVPData.PVPBattlePlayer)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.PVPBattlePlayer other) {
        if (other == protocol.PVPData.PVPBattlePlayer.getDefaultInstance()) return this;
        if (other.hasName()) {
          bitField0_ |= 0x00000001;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasSex()) {
          setSex(other.getSex());
        }
        if (other.hasHead()) {
          setHead(other.getHead());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasName()) {
          
          return false;
        }
        if (!hasSex()) {
          
          return false;
        }
        if (!hasHead()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.PVPBattlePlayer parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.PVPBattlePlayer) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string name = 1;
      private java.lang.Object name_ = "";
      /**
       * <code>required string name = 1;</code>
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string name = 1;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string name = 1;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string name = 1;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 1;</code>
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 1;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }

      // required int32 sex = 2;
      private int sex_ ;
      /**
       * <code>required int32 sex = 2;</code>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 sex = 2;</code>
       */
      public int getSex() {
        return sex_;
      }
      /**
       * <code>required int32 sex = 2;</code>
       */
      public Builder setSex(int value) {
        bitField0_ |= 0x00000002;
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sex = 2;</code>
       */
      public Builder clearSex() {
        bitField0_ = (bitField0_ & ~0x00000002);
        sex_ = 0;
        onChanged();
        return this;
      }

      // required int32 head = 3;
      private int head_ ;
      /**
       * <code>required int32 head = 3;</code>
       */
      public boolean hasHead() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 head = 3;</code>
       */
      public int getHead() {
        return head_;
      }
      /**
       * <code>required int32 head = 3;</code>
       */
      public Builder setHead(int value) {
        bitField0_ |= 0x00000004;
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 head = 3;</code>
       */
      public Builder clearHead() {
        bitField0_ = (bitField0_ & ~0x00000004);
        head_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.PVPBattlePlayer)
    }

    static {
      defaultInstance = new PVPBattlePlayer(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.PVPBattlePlayer)
  }

  public interface RequestPVPPetOperateOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
     * </pre>
     */
    int getType();

    // optional int32 userUid = 2;
    /**
     * <code>optional int32 userUid = 2;</code>
     */
    boolean hasUserUid();
    /**
     * <code>optional int32 userUid = 2;</code>
     */
    int getUserUid();

    // optional .protocol.PVPTeam pets = 3;
    /**
     * <code>optional .protocol.PVPTeam pets = 3;</code>
     */
    boolean hasPets();
    /**
     * <code>optional .protocol.PVPTeam pets = 3;</code>
     */
    protocol.PVPData.PVPTeam getPets();
    /**
     * <code>optional .protocol.PVPTeam pets = 3;</code>
     */
    protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.RequestPVPPetOperate}
   *
   * <pre>
   * 1330
   * PVP宠物的存储
   * </pre>
   */
  public static final class RequestPVPPetOperate extends
      com.google.protobuf.GeneratedMessage
      implements RequestPVPPetOperateOrBuilder {
    // Use RequestPVPPetOperate.newBuilder() to construct.
    private RequestPVPPetOperate(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPVPPetOperate(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPVPPetOperate defaultInstance;
    public static RequestPVPPetOperate getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPVPPetOperate getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPVPPetOperate(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              userUid_ = input.readInt32();
              break;
            }
            case 26: {
              protocol.PVPData.PVPTeam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) == 0x00000004)) {
                subBuilder = pets_.toBuilder();
              }
              pets_ = input.readMessage(protocol.PVPData.PVPTeam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pets_);
                pets_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_RequestPVPPetOperate_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_RequestPVPPetOperate_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.RequestPVPPetOperate.class, protocol.PVPData.RequestPVPPetOperate.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPVPPetOperate> PARSER =
        new com.google.protobuf.AbstractParser<RequestPVPPetOperate>() {
      public RequestPVPPetOperate parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPVPPetOperate(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPVPPetOperate> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // optional int32 userUid = 2;
    public static final int USERUID_FIELD_NUMBER = 2;
    private int userUid_;
    /**
     * <code>optional int32 userUid = 2;</code>
     */
    public boolean hasUserUid() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 userUid = 2;</code>
     */
    public int getUserUid() {
      return userUid_;
    }

    // optional .protocol.PVPTeam pets = 3;
    public static final int PETS_FIELD_NUMBER = 3;
    private protocol.PVPData.PVPTeam pets_;
    /**
     * <code>optional .protocol.PVPTeam pets = 3;</code>
     */
    public boolean hasPets() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional .protocol.PVPTeam pets = 3;</code>
     */
    public protocol.PVPData.PVPTeam getPets() {
      return pets_;
    }
    /**
     * <code>optional .protocol.PVPTeam pets = 3;</code>
     */
    public protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder() {
      return pets_;
    }

    private void initFields() {
      type_ = 0;
      userUid_ = 0;
      pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasPets()) {
        if (!getPets().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, userUid_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeMessage(3, pets_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, userUid_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, pets_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.RequestPVPPetOperate parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPPetOperate parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.RequestPVPPetOperate prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPVPPetOperate}
     *
     * <pre>
     * 1330
     * PVP宠物的存储
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.RequestPVPPetOperateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_RequestPVPPetOperate_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_RequestPVPPetOperate_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.RequestPVPPetOperate.class, protocol.PVPData.RequestPVPPetOperate.Builder.class);
      }

      // Construct using protocol.PVPData.RequestPVPPetOperate.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPetsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        userUid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (petsBuilder_ == null) {
          pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
        } else {
          petsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_RequestPVPPetOperate_descriptor;
      }

      public protocol.PVPData.RequestPVPPetOperate getDefaultInstanceForType() {
        return protocol.PVPData.RequestPVPPetOperate.getDefaultInstance();
      }

      public protocol.PVPData.RequestPVPPetOperate build() {
        protocol.PVPData.RequestPVPPetOperate result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.RequestPVPPetOperate buildPartial() {
        protocol.PVPData.RequestPVPPetOperate result = new protocol.PVPData.RequestPVPPetOperate(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.userUid_ = userUid_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        if (petsBuilder_ == null) {
          result.pets_ = pets_;
        } else {
          result.pets_ = petsBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.RequestPVPPetOperate) {
          return mergeFrom((protocol.PVPData.RequestPVPPetOperate)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.RequestPVPPetOperate other) {
        if (other == protocol.PVPData.RequestPVPPetOperate.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasUserUid()) {
          setUserUid(other.getUserUid());
        }
        if (other.hasPets()) {
          mergePets(other.getPets());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (hasPets()) {
          if (!getPets().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.RequestPVPPetOperate parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.RequestPVPPetOperate) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       * 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // optional int32 userUid = 2;
      private int userUid_ ;
      /**
       * <code>optional int32 userUid = 2;</code>
       */
      public boolean hasUserUid() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 userUid = 2;</code>
       */
      public int getUserUid() {
        return userUid_;
      }
      /**
       * <code>optional int32 userUid = 2;</code>
       */
      public Builder setUserUid(int value) {
        bitField0_ |= 0x00000002;
        userUid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 userUid = 2;</code>
       */
      public Builder clearUserUid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userUid_ = 0;
        onChanged();
        return this;
      }

      // optional .protocol.PVPTeam pets = 3;
      private protocol.PVPData.PVPTeam pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder> petsBuilder_;
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public boolean hasPets() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public protocol.PVPData.PVPTeam getPets() {
        if (petsBuilder_ == null) {
          return pets_;
        } else {
          return petsBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public Builder setPets(protocol.PVPData.PVPTeam value) {
        if (petsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pets_ = value;
          onChanged();
        } else {
          petsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public Builder setPets(
          protocol.PVPData.PVPTeam.Builder builderForValue) {
        if (petsBuilder_ == null) {
          pets_ = builderForValue.build();
          onChanged();
        } else {
          petsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public Builder mergePets(protocol.PVPData.PVPTeam value) {
        if (petsBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004) &&
              pets_ != protocol.PVPData.PVPTeam.getDefaultInstance()) {
            pets_ =
              protocol.PVPData.PVPTeam.newBuilder(pets_).mergeFrom(value).buildPartial();
          } else {
            pets_ = value;
          }
          onChanged();
        } else {
          petsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public Builder clearPets() {
        if (petsBuilder_ == null) {
          pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
          onChanged();
        } else {
          petsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public protocol.PVPData.PVPTeam.Builder getPetsBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getPetsFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      public protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder() {
        if (petsBuilder_ != null) {
          return petsBuilder_.getMessageOrBuilder();
        } else {
          return pets_;
        }
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder> 
          getPetsFieldBuilder() {
        if (petsBuilder_ == null) {
          petsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder>(
                  pets_,
                  getParentForChildren(),
                  isClean());
          pets_ = null;
        }
        return petsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPVPPetOperate)
    }

    static {
      defaultInstance = new RequestPVPPetOperate(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPVPPetOperate)
  }

  public interface ResponsePVPPetOperateOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     */
    int getType();

    // optional .protocol.PVPTeam pets = 2;
    /**
     * <code>optional .protocol.PVPTeam pets = 2;</code>
     */
    boolean hasPets();
    /**
     * <code>optional .protocol.PVPTeam pets = 2;</code>
     */
    protocol.PVPData.PVPTeam getPets();
    /**
     * <code>optional .protocol.PVPTeam pets = 2;</code>
     */
    protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponsePVPPetOperate}
   */
  public static final class ResponsePVPPetOperate extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePVPPetOperateOrBuilder {
    // Use ResponsePVPPetOperate.newBuilder() to construct.
    private ResponsePVPPetOperate(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePVPPetOperate(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePVPPetOperate defaultInstance;
    public static ResponsePVPPetOperate getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePVPPetOperate getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePVPPetOperate(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 18: {
              protocol.PVPData.PVPTeam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = pets_.toBuilder();
              }
              pets_ = input.readMessage(protocol.PVPData.PVPTeam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pets_);
                pets_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPPetOperate_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPPetOperate_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.ResponsePVPPetOperate.class, protocol.PVPData.ResponsePVPPetOperate.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePVPPetOperate> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePVPPetOperate>() {
      public ResponsePVPPetOperate parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePVPPetOperate(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePVPPetOperate> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     */
    public int getType() {
      return type_;
    }

    // optional .protocol.PVPTeam pets = 2;
    public static final int PETS_FIELD_NUMBER = 2;
    private protocol.PVPData.PVPTeam pets_;
    /**
     * <code>optional .protocol.PVPTeam pets = 2;</code>
     */
    public boolean hasPets() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional .protocol.PVPTeam pets = 2;</code>
     */
    public protocol.PVPData.PVPTeam getPets() {
      return pets_;
    }
    /**
     * <code>optional .protocol.PVPTeam pets = 2;</code>
     */
    public protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder() {
      return pets_;
    }

    private void initFields() {
      type_ = 0;
      pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (hasPets()) {
        if (!getPets().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, pets_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, pets_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPPetOperate parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.ResponsePVPPetOperate prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePVPPetOperate}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.ResponsePVPPetOperateOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPPetOperate_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPPetOperate_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.ResponsePVPPetOperate.class, protocol.PVPData.ResponsePVPPetOperate.Builder.class);
      }

      // Construct using protocol.PVPData.ResponsePVPPetOperate.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPetsFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (petsBuilder_ == null) {
          pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
        } else {
          petsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPPetOperate_descriptor;
      }

      public protocol.PVPData.ResponsePVPPetOperate getDefaultInstanceForType() {
        return protocol.PVPData.ResponsePVPPetOperate.getDefaultInstance();
      }

      public protocol.PVPData.ResponsePVPPetOperate build() {
        protocol.PVPData.ResponsePVPPetOperate result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.ResponsePVPPetOperate buildPartial() {
        protocol.PVPData.ResponsePVPPetOperate result = new protocol.PVPData.ResponsePVPPetOperate(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (petsBuilder_ == null) {
          result.pets_ = pets_;
        } else {
          result.pets_ = petsBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.ResponsePVPPetOperate) {
          return mergeFrom((protocol.PVPData.ResponsePVPPetOperate)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.ResponsePVPPetOperate other) {
        if (other == protocol.PVPData.ResponsePVPPetOperate.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasPets()) {
          mergePets(other.getPets());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (hasPets()) {
          if (!getPets().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.ResponsePVPPetOperate parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.ResponsePVPPetOperate) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // optional .protocol.PVPTeam pets = 2;
      private protocol.PVPData.PVPTeam pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder> petsBuilder_;
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public boolean hasPets() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public protocol.PVPData.PVPTeam getPets() {
        if (petsBuilder_ == null) {
          return pets_;
        } else {
          return petsBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public Builder setPets(protocol.PVPData.PVPTeam value) {
        if (petsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pets_ = value;
          onChanged();
        } else {
          petsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public Builder setPets(
          protocol.PVPData.PVPTeam.Builder builderForValue) {
        if (petsBuilder_ == null) {
          pets_ = builderForValue.build();
          onChanged();
        } else {
          petsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public Builder mergePets(protocol.PVPData.PVPTeam value) {
        if (petsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              pets_ != protocol.PVPData.PVPTeam.getDefaultInstance()) {
            pets_ =
              protocol.PVPData.PVPTeam.newBuilder(pets_).mergeFrom(value).buildPartial();
          } else {
            pets_ = value;
          }
          onChanged();
        } else {
          petsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public Builder clearPets() {
        if (petsBuilder_ == null) {
          pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
          onChanged();
        } else {
          petsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public protocol.PVPData.PVPTeam.Builder getPetsBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPetsFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      public protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder() {
        if (petsBuilder_ != null) {
          return petsBuilder_.getMessageOrBuilder();
        } else {
          return pets_;
        }
      }
      /**
       * <code>optional .protocol.PVPTeam pets = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder> 
          getPetsFieldBuilder() {
        if (petsBuilder_ == null) {
          petsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder>(
                  pets_,
                  getParentForChildren(),
                  isClean());
          pets_ = null;
        }
        return petsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePVPPetOperate)
    }

    static {
      defaultInstance = new ResponsePVPPetOperate(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePVPPetOperate)
  }

  public interface RequestPVPBaseDataOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestPVPBaseData}
   *
   * <pre>
   * 1331
   * PVP个人信息
   * </pre>
   */
  public static final class RequestPVPBaseData extends
      com.google.protobuf.GeneratedMessage
      implements RequestPVPBaseDataOrBuilder {
    // Use RequestPVPBaseData.newBuilder() to construct.
    private RequestPVPBaseData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPVPBaseData(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPVPBaseData defaultInstance;
    public static RequestPVPBaseData getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPVPBaseData getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPVPBaseData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBaseData_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBaseData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.RequestPVPBaseData.class, protocol.PVPData.RequestPVPBaseData.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPVPBaseData> PARSER =
        new com.google.protobuf.AbstractParser<RequestPVPBaseData>() {
      public RequestPVPBaseData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPVPBaseData(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPVPBaseData> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.RequestPVPBaseData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBaseData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.RequestPVPBaseData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBaseData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.RequestPVPBaseData prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPVPBaseData}
     *
     * <pre>
     * 1331
     * PVP个人信息
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.RequestPVPBaseDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBaseData_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBaseData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.RequestPVPBaseData.class, protocol.PVPData.RequestPVPBaseData.Builder.class);
      }

      // Construct using protocol.PVPData.RequestPVPBaseData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBaseData_descriptor;
      }

      public protocol.PVPData.RequestPVPBaseData getDefaultInstanceForType() {
        return protocol.PVPData.RequestPVPBaseData.getDefaultInstance();
      }

      public protocol.PVPData.RequestPVPBaseData build() {
        protocol.PVPData.RequestPVPBaseData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.RequestPVPBaseData buildPartial() {
        protocol.PVPData.RequestPVPBaseData result = new protocol.PVPData.RequestPVPBaseData(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.RequestPVPBaseData) {
          return mergeFrom((protocol.PVPData.RequestPVPBaseData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.RequestPVPBaseData other) {
        if (other == protocol.PVPData.RequestPVPBaseData.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.RequestPVPBaseData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.RequestPVPBaseData) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPVPBaseData)
    }

    static {
      defaultInstance = new RequestPVPBaseData(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPVPBaseData)
  }

  public interface ResponsePVPBaseDataOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 rank = 1;
    /**
     * <code>required int64 rank = 1;</code>
     */
    boolean hasRank();
    /**
     * <code>required int64 rank = 1;</code>
     */
    long getRank();

    // required int32 score = 2;
    /**
     * <code>required int32 score = 2;</code>
     */
    boolean hasScore();
    /**
     * <code>required int32 score = 2;</code>
     */
    int getScore();

    // required int32 victory = 3;
    /**
     * <code>required int32 victory = 3;</code>
     */
    boolean hasVictory();
    /**
     * <code>required int32 victory = 3;</code>
     */
    int getVictory();

    // required int32 fail = 4;
    /**
     * <code>required int32 fail = 4;</code>
     */
    boolean hasFail();
    /**
     * <code>required int32 fail = 4;</code>
     */
    int getFail();
  }
  /**
   * Protobuf type {@code protocol.ResponsePVPBaseData}
   */
  public static final class ResponsePVPBaseData extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePVPBaseDataOrBuilder {
    // Use ResponsePVPBaseData.newBuilder() to construct.
    private ResponsePVPBaseData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePVPBaseData(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePVPBaseData defaultInstance;
    public static ResponsePVPBaseData getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePVPBaseData getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePVPBaseData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              rank_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              score_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              victory_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              fail_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPBaseData_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPBaseData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.ResponsePVPBaseData.class, protocol.PVPData.ResponsePVPBaseData.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePVPBaseData> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePVPBaseData>() {
      public ResponsePVPBaseData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePVPBaseData(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePVPBaseData> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 rank = 1;
    public static final int RANK_FIELD_NUMBER = 1;
    private long rank_;
    /**
     * <code>required int64 rank = 1;</code>
     */
    public boolean hasRank() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 rank = 1;</code>
     */
    public long getRank() {
      return rank_;
    }

    // required int32 score = 2;
    public static final int SCORE_FIELD_NUMBER = 2;
    private int score_;
    /**
     * <code>required int32 score = 2;</code>
     */
    public boolean hasScore() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 score = 2;</code>
     */
    public int getScore() {
      return score_;
    }

    // required int32 victory = 3;
    public static final int VICTORY_FIELD_NUMBER = 3;
    private int victory_;
    /**
     * <code>required int32 victory = 3;</code>
     */
    public boolean hasVictory() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 victory = 3;</code>
     */
    public int getVictory() {
      return victory_;
    }

    // required int32 fail = 4;
    public static final int FAIL_FIELD_NUMBER = 4;
    private int fail_;
    /**
     * <code>required int32 fail = 4;</code>
     */
    public boolean hasFail() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 fail = 4;</code>
     */
    public int getFail() {
      return fail_;
    }

    private void initFields() {
      rank_ = 0L;
      score_ = 0;
      victory_ = 0;
      fail_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRank()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasScore()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasVictory()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFail()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, rank_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, score_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, victory_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, fail_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, rank_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, score_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, victory_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, fail_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.ResponsePVPBaseData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBaseData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.ResponsePVPBaseData prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePVPBaseData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.ResponsePVPBaseDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBaseData_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBaseData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.ResponsePVPBaseData.class, protocol.PVPData.ResponsePVPBaseData.Builder.class);
      }

      // Construct using protocol.PVPData.ResponsePVPBaseData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        rank_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        score_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        victory_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        fail_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBaseData_descriptor;
      }

      public protocol.PVPData.ResponsePVPBaseData getDefaultInstanceForType() {
        return protocol.PVPData.ResponsePVPBaseData.getDefaultInstance();
      }

      public protocol.PVPData.ResponsePVPBaseData build() {
        protocol.PVPData.ResponsePVPBaseData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.ResponsePVPBaseData buildPartial() {
        protocol.PVPData.ResponsePVPBaseData result = new protocol.PVPData.ResponsePVPBaseData(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.rank_ = rank_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.score_ = score_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.victory_ = victory_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.fail_ = fail_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.ResponsePVPBaseData) {
          return mergeFrom((protocol.PVPData.ResponsePVPBaseData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.ResponsePVPBaseData other) {
        if (other == protocol.PVPData.ResponsePVPBaseData.getDefaultInstance()) return this;
        if (other.hasRank()) {
          setRank(other.getRank());
        }
        if (other.hasScore()) {
          setScore(other.getScore());
        }
        if (other.hasVictory()) {
          setVictory(other.getVictory());
        }
        if (other.hasFail()) {
          setFail(other.getFail());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRank()) {
          
          return false;
        }
        if (!hasScore()) {
          
          return false;
        }
        if (!hasVictory()) {
          
          return false;
        }
        if (!hasFail()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.ResponsePVPBaseData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.ResponsePVPBaseData) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 rank = 1;
      private long rank_ ;
      /**
       * <code>required int64 rank = 1;</code>
       */
      public boolean hasRank() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 rank = 1;</code>
       */
      public long getRank() {
        return rank_;
      }
      /**
       * <code>required int64 rank = 1;</code>
       */
      public Builder setRank(long value) {
        bitField0_ |= 0x00000001;
        rank_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 rank = 1;</code>
       */
      public Builder clearRank() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rank_ = 0L;
        onChanged();
        return this;
      }

      // required int32 score = 2;
      private int score_ ;
      /**
       * <code>required int32 score = 2;</code>
       */
      public boolean hasScore() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 score = 2;</code>
       */
      public int getScore() {
        return score_;
      }
      /**
       * <code>required int32 score = 2;</code>
       */
      public Builder setScore(int value) {
        bitField0_ |= 0x00000002;
        score_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 score = 2;</code>
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        score_ = 0;
        onChanged();
        return this;
      }

      // required int32 victory = 3;
      private int victory_ ;
      /**
       * <code>required int32 victory = 3;</code>
       */
      public boolean hasVictory() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 victory = 3;</code>
       */
      public int getVictory() {
        return victory_;
      }
      /**
       * <code>required int32 victory = 3;</code>
       */
      public Builder setVictory(int value) {
        bitField0_ |= 0x00000004;
        victory_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 victory = 3;</code>
       */
      public Builder clearVictory() {
        bitField0_ = (bitField0_ & ~0x00000004);
        victory_ = 0;
        onChanged();
        return this;
      }

      // required int32 fail = 4;
      private int fail_ ;
      /**
       * <code>required int32 fail = 4;</code>
       */
      public boolean hasFail() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 fail = 4;</code>
       */
      public int getFail() {
        return fail_;
      }
      /**
       * <code>required int32 fail = 4;</code>
       */
      public Builder setFail(int value) {
        bitField0_ |= 0x00000008;
        fail_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 fail = 4;</code>
       */
      public Builder clearFail() {
        bitField0_ = (bitField0_ & ~0x00000008);
        fail_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePVPBaseData)
    }

    static {
      defaultInstance = new ResponsePVPBaseData(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePVPBaseData)
  }

  public interface RequestPVPBattleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestPVPBattle}
   *
   * <pre>
   * 1332
   * PVP战斗
   * </pre>
   */
  public static final class RequestPVPBattle extends
      com.google.protobuf.GeneratedMessage
      implements RequestPVPBattleOrBuilder {
    // Use RequestPVPBattle.newBuilder() to construct.
    private RequestPVPBattle(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPVPBattle(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPVPBattle defaultInstance;
    public static RequestPVPBattle getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPVPBattle getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPVPBattle(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBattle_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBattle_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.RequestPVPBattle.class, protocol.PVPData.RequestPVPBattle.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPVPBattle> PARSER =
        new com.google.protobuf.AbstractParser<RequestPVPBattle>() {
      public RequestPVPBattle parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPVPBattle(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPVPBattle> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.RequestPVPBattle parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattle parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattle parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattle parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.RequestPVPBattle prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPVPBattle}
     *
     * <pre>
     * 1332
     * PVP战斗
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.RequestPVPBattleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattle_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattle_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.RequestPVPBattle.class, protocol.PVPData.RequestPVPBattle.Builder.class);
      }

      // Construct using protocol.PVPData.RequestPVPBattle.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattle_descriptor;
      }

      public protocol.PVPData.RequestPVPBattle getDefaultInstanceForType() {
        return protocol.PVPData.RequestPVPBattle.getDefaultInstance();
      }

      public protocol.PVPData.RequestPVPBattle build() {
        protocol.PVPData.RequestPVPBattle result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.RequestPVPBattle buildPartial() {
        protocol.PVPData.RequestPVPBattle result = new protocol.PVPData.RequestPVPBattle(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.RequestPVPBattle) {
          return mergeFrom((protocol.PVPData.RequestPVPBattle)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.RequestPVPBattle other) {
        if (other == protocol.PVPData.RequestPVPBattle.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.RequestPVPBattle parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.RequestPVPBattle) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPVPBattle)
    }

    static {
      defaultInstance = new RequestPVPBattle(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPVPBattle)
  }

  public interface ResponsePVPBattleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.PVPTeam pets = 1;
    /**
     * <code>required .protocol.PVPTeam pets = 1;</code>
     */
    boolean hasPets();
    /**
     * <code>required .protocol.PVPTeam pets = 1;</code>
     */
    protocol.PVPData.PVPTeam getPets();
    /**
     * <code>required .protocol.PVPTeam pets = 1;</code>
     */
    protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder();

    // required string name = 2;
    /**
     * <code>required string name = 2;</code>
     */
    boolean hasName();
    /**
     * <code>required string name = 2;</code>
     */
    java.lang.String getName();
    /**
     * <code>required string name = 2;</code>
     */
    com.google.protobuf.ByteString
        getNameBytes();

    // required int32 sex = 3;
    /**
     * <code>required int32 sex = 3;</code>
     */
    boolean hasSex();
    /**
     * <code>required int32 sex = 3;</code>
     */
    int getSex();

    // required int32 head = 4;
    /**
     * <code>required int32 head = 4;</code>
     */
    boolean hasHead();
    /**
     * <code>required int32 head = 4;</code>
     */
    int getHead();

    // repeated .protocol.PVPBattlePlayer player = 5;
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    java.util.List<protocol.PVPData.PVPBattlePlayer> 
        getPlayerList();
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    protocol.PVPData.PVPBattlePlayer getPlayer(int index);
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    int getPlayerCount();
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    java.util.List<? extends protocol.PVPData.PVPBattlePlayerOrBuilder> 
        getPlayerOrBuilderList();
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    protocol.PVPData.PVPBattlePlayerOrBuilder getPlayerOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponsePVPBattle}
   */
  public static final class ResponsePVPBattle extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePVPBattleOrBuilder {
    // Use ResponsePVPBattle.newBuilder() to construct.
    private ResponsePVPBattle(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePVPBattle(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePVPBattle defaultInstance;
    public static ResponsePVPBattle getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePVPBattle getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePVPBattle(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              protocol.PVPData.PVPTeam.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = pets_.toBuilder();
              }
              pets_ = input.readMessage(protocol.PVPData.PVPTeam.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pets_);
                pets_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              name_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              sex_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              head_ = input.readInt32();
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                player_ = new java.util.ArrayList<protocol.PVPData.PVPBattlePlayer>();
                mutable_bitField0_ |= 0x00000010;
              }
              player_.add(input.readMessage(protocol.PVPData.PVPBattlePlayer.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          player_ = java.util.Collections.unmodifiableList(player_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPBattle_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPBattle_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.ResponsePVPBattle.class, protocol.PVPData.ResponsePVPBattle.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePVPBattle> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePVPBattle>() {
      public ResponsePVPBattle parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePVPBattle(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePVPBattle> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.PVPTeam pets = 1;
    public static final int PETS_FIELD_NUMBER = 1;
    private protocol.PVPData.PVPTeam pets_;
    /**
     * <code>required .protocol.PVPTeam pets = 1;</code>
     */
    public boolean hasPets() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.PVPTeam pets = 1;</code>
     */
    public protocol.PVPData.PVPTeam getPets() {
      return pets_;
    }
    /**
     * <code>required .protocol.PVPTeam pets = 1;</code>
     */
    public protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder() {
      return pets_;
    }

    // required string name = 2;
    public static final int NAME_FIELD_NUMBER = 2;
    private java.lang.Object name_;
    /**
     * <code>required string name = 2;</code>
     */
    public boolean hasName() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string name = 2;</code>
     */
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string name = 2;</code>
     */
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 sex = 3;
    public static final int SEX_FIELD_NUMBER = 3;
    private int sex_;
    /**
     * <code>required int32 sex = 3;</code>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 sex = 3;</code>
     */
    public int getSex() {
      return sex_;
    }

    // required int32 head = 4;
    public static final int HEAD_FIELD_NUMBER = 4;
    private int head_;
    /**
     * <code>required int32 head = 4;</code>
     */
    public boolean hasHead() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 head = 4;</code>
     */
    public int getHead() {
      return head_;
    }

    // repeated .protocol.PVPBattlePlayer player = 5;
    public static final int PLAYER_FIELD_NUMBER = 5;
    private java.util.List<protocol.PVPData.PVPBattlePlayer> player_;
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    public java.util.List<protocol.PVPData.PVPBattlePlayer> getPlayerList() {
      return player_;
    }
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    public java.util.List<? extends protocol.PVPData.PVPBattlePlayerOrBuilder> 
        getPlayerOrBuilderList() {
      return player_;
    }
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    public int getPlayerCount() {
      return player_.size();
    }
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    public protocol.PVPData.PVPBattlePlayer getPlayer(int index) {
      return player_.get(index);
    }
    /**
     * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
     */
    public protocol.PVPData.PVPBattlePlayerOrBuilder getPlayerOrBuilder(
        int index) {
      return player_.get(index);
    }

    private void initFields() {
      pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
      name_ = "";
      sex_ = 0;
      head_ = 0;
      player_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPets()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasName()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHead()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getPets().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getPlayerCount(); i++) {
        if (!getPlayer(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, pets_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, sex_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, head_);
      }
      for (int i = 0; i < player_.size(); i++) {
        output.writeMessage(5, player_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, pets_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getNameBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, sex_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, head_);
      }
      for (int i = 0; i < player_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, player_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.ResponsePVPBattle parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattle parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBattle parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBattle parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.ResponsePVPBattle prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePVPBattle}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.ResponsePVPBattleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBattle_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBattle_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.ResponsePVPBattle.class, protocol.PVPData.ResponsePVPBattle.Builder.class);
      }

      // Construct using protocol.PVPData.ResponsePVPBattle.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getPetsFieldBuilder();
          getPlayerFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (petsBuilder_ == null) {
          pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
        } else {
          petsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        sex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        head_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        if (playerBuilder_ == null) {
          player_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          playerBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBattle_descriptor;
      }

      public protocol.PVPData.ResponsePVPBattle getDefaultInstanceForType() {
        return protocol.PVPData.ResponsePVPBattle.getDefaultInstance();
      }

      public protocol.PVPData.ResponsePVPBattle build() {
        protocol.PVPData.ResponsePVPBattle result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.ResponsePVPBattle buildPartial() {
        protocol.PVPData.ResponsePVPBattle result = new protocol.PVPData.ResponsePVPBattle(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (petsBuilder_ == null) {
          result.pets_ = pets_;
        } else {
          result.pets_ = petsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.sex_ = sex_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.head_ = head_;
        if (playerBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            player_ = java.util.Collections.unmodifiableList(player_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.player_ = player_;
        } else {
          result.player_ = playerBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.ResponsePVPBattle) {
          return mergeFrom((protocol.PVPData.ResponsePVPBattle)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.ResponsePVPBattle other) {
        if (other == protocol.PVPData.ResponsePVPBattle.getDefaultInstance()) return this;
        if (other.hasPets()) {
          mergePets(other.getPets());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000002;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasSex()) {
          setSex(other.getSex());
        }
        if (other.hasHead()) {
          setHead(other.getHead());
        }
        if (playerBuilder_ == null) {
          if (!other.player_.isEmpty()) {
            if (player_.isEmpty()) {
              player_ = other.player_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensurePlayerIsMutable();
              player_.addAll(other.player_);
            }
            onChanged();
          }
        } else {
          if (!other.player_.isEmpty()) {
            if (playerBuilder_.isEmpty()) {
              playerBuilder_.dispose();
              playerBuilder_ = null;
              player_ = other.player_;
              bitField0_ = (bitField0_ & ~0x00000010);
              playerBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getPlayerFieldBuilder() : null;
            } else {
              playerBuilder_.addAllMessages(other.player_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPets()) {
          
          return false;
        }
        if (!hasName()) {
          
          return false;
        }
        if (!hasSex()) {
          
          return false;
        }
        if (!hasHead()) {
          
          return false;
        }
        if (!getPets().isInitialized()) {
          
          return false;
        }
        for (int i = 0; i < getPlayerCount(); i++) {
          if (!getPlayer(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.ResponsePVPBattle parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.ResponsePVPBattle) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.PVPTeam pets = 1;
      private protocol.PVPData.PVPTeam pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder> petsBuilder_;
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public boolean hasPets() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public protocol.PVPData.PVPTeam getPets() {
        if (petsBuilder_ == null) {
          return pets_;
        } else {
          return petsBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public Builder setPets(protocol.PVPData.PVPTeam value) {
        if (petsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pets_ = value;
          onChanged();
        } else {
          petsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public Builder setPets(
          protocol.PVPData.PVPTeam.Builder builderForValue) {
        if (petsBuilder_ == null) {
          pets_ = builderForValue.build();
          onChanged();
        } else {
          petsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public Builder mergePets(protocol.PVPData.PVPTeam value) {
        if (petsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              pets_ != protocol.PVPData.PVPTeam.getDefaultInstance()) {
            pets_ =
              protocol.PVPData.PVPTeam.newBuilder(pets_).mergeFrom(value).buildPartial();
          } else {
            pets_ = value;
          }
          onChanged();
        } else {
          petsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public Builder clearPets() {
        if (petsBuilder_ == null) {
          pets_ = protocol.PVPData.PVPTeam.getDefaultInstance();
          onChanged();
        } else {
          petsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public protocol.PVPData.PVPTeam.Builder getPetsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPetsFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      public protocol.PVPData.PVPTeamOrBuilder getPetsOrBuilder() {
        if (petsBuilder_ != null) {
          return petsBuilder_.getMessageOrBuilder();
        } else {
          return pets_;
        }
      }
      /**
       * <code>required .protocol.PVPTeam pets = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder> 
          getPetsFieldBuilder() {
        if (petsBuilder_ == null) {
          petsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.PVPData.PVPTeam, protocol.PVPData.PVPTeam.Builder, protocol.PVPData.PVPTeamOrBuilder>(
                  pets_,
                  getParentForChildren(),
                  isClean());
          pets_ = null;
        }
        return petsBuilder_;
      }

      // required string name = 2;
      private java.lang.Object name_ = "";
      /**
       * <code>required string name = 2;</code>
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string name = 2;</code>
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          name_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string name = 2;</code>
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string name = 2;</code>
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 2;</code>
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>required string name = 2;</code>
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }

      // required int32 sex = 3;
      private int sex_ ;
      /**
       * <code>required int32 sex = 3;</code>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 sex = 3;</code>
       */
      public int getSex() {
        return sex_;
      }
      /**
       * <code>required int32 sex = 3;</code>
       */
      public Builder setSex(int value) {
        bitField0_ |= 0x00000004;
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sex = 3;</code>
       */
      public Builder clearSex() {
        bitField0_ = (bitField0_ & ~0x00000004);
        sex_ = 0;
        onChanged();
        return this;
      }

      // required int32 head = 4;
      private int head_ ;
      /**
       * <code>required int32 head = 4;</code>
       */
      public boolean hasHead() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 head = 4;</code>
       */
      public int getHead() {
        return head_;
      }
      /**
       * <code>required int32 head = 4;</code>
       */
      public Builder setHead(int value) {
        bitField0_ |= 0x00000008;
        head_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 head = 4;</code>
       */
      public Builder clearHead() {
        bitField0_ = (bitField0_ & ~0x00000008);
        head_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.PVPBattlePlayer player = 5;
      private java.util.List<protocol.PVPData.PVPBattlePlayer> player_ =
        java.util.Collections.emptyList();
      private void ensurePlayerIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          player_ = new java.util.ArrayList<protocol.PVPData.PVPBattlePlayer>(player_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PVPData.PVPBattlePlayer, protocol.PVPData.PVPBattlePlayer.Builder, protocol.PVPData.PVPBattlePlayerOrBuilder> playerBuilder_;

      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public java.util.List<protocol.PVPData.PVPBattlePlayer> getPlayerList() {
        if (playerBuilder_ == null) {
          return java.util.Collections.unmodifiableList(player_);
        } else {
          return playerBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public int getPlayerCount() {
        if (playerBuilder_ == null) {
          return player_.size();
        } else {
          return playerBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public protocol.PVPData.PVPBattlePlayer getPlayer(int index) {
        if (playerBuilder_ == null) {
          return player_.get(index);
        } else {
          return playerBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder setPlayer(
          int index, protocol.PVPData.PVPBattlePlayer value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.set(index, value);
          onChanged();
        } else {
          playerBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder setPlayer(
          int index, protocol.PVPData.PVPBattlePlayer.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.set(index, builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder addPlayer(protocol.PVPData.PVPBattlePlayer value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.add(value);
          onChanged();
        } else {
          playerBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder addPlayer(
          int index, protocol.PVPData.PVPBattlePlayer value) {
        if (playerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePlayerIsMutable();
          player_.add(index, value);
          onChanged();
        } else {
          playerBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder addPlayer(
          protocol.PVPData.PVPBattlePlayer.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.add(builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder addPlayer(
          int index, protocol.PVPData.PVPBattlePlayer.Builder builderForValue) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.add(index, builderForValue.build());
          onChanged();
        } else {
          playerBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder addAllPlayer(
          java.lang.Iterable<? extends protocol.PVPData.PVPBattlePlayer> values) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          super.addAll(values, player_);
          onChanged();
        } else {
          playerBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder clearPlayer() {
        if (playerBuilder_ == null) {
          player_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          playerBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public Builder removePlayer(int index) {
        if (playerBuilder_ == null) {
          ensurePlayerIsMutable();
          player_.remove(index);
          onChanged();
        } else {
          playerBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public protocol.PVPData.PVPBattlePlayer.Builder getPlayerBuilder(
          int index) {
        return getPlayerFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public protocol.PVPData.PVPBattlePlayerOrBuilder getPlayerOrBuilder(
          int index) {
        if (playerBuilder_ == null) {
          return player_.get(index);  } else {
          return playerBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public java.util.List<? extends protocol.PVPData.PVPBattlePlayerOrBuilder> 
           getPlayerOrBuilderList() {
        if (playerBuilder_ != null) {
          return playerBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(player_);
        }
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public protocol.PVPData.PVPBattlePlayer.Builder addPlayerBuilder() {
        return getPlayerFieldBuilder().addBuilder(
            protocol.PVPData.PVPBattlePlayer.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public protocol.PVPData.PVPBattlePlayer.Builder addPlayerBuilder(
          int index) {
        return getPlayerFieldBuilder().addBuilder(
            index, protocol.PVPData.PVPBattlePlayer.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PVPBattlePlayer player = 5;</code>
       */
      public java.util.List<protocol.PVPData.PVPBattlePlayer.Builder> 
           getPlayerBuilderList() {
        return getPlayerFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PVPData.PVPBattlePlayer, protocol.PVPData.PVPBattlePlayer.Builder, protocol.PVPData.PVPBattlePlayerOrBuilder> 
          getPlayerFieldBuilder() {
        if (playerBuilder_ == null) {
          playerBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.PVPData.PVPBattlePlayer, protocol.PVPData.PVPBattlePlayer.Builder, protocol.PVPData.PVPBattlePlayerOrBuilder>(
                  player_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          player_ = null;
        }
        return playerBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePVPBattle)
    }

    static {
      defaultInstance = new ResponsePVPBattle(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePVPBattle)
  }

  public interface RequestPVPBattleResultOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool victory = 1;
    /**
     * <code>required bool victory = 1;</code>
     */
    boolean hasVictory();
    /**
     * <code>required bool victory = 1;</code>
     */
    boolean getVictory();
  }
  /**
   * Protobuf type {@code protocol.RequestPVPBattleResult}
   *
   * <pre>
   * 1333
   * PVP战斗 输赢
   * </pre>
   */
  public static final class RequestPVPBattleResult extends
      com.google.protobuf.GeneratedMessage
      implements RequestPVPBattleResultOrBuilder {
    // Use RequestPVPBattleResult.newBuilder() to construct.
    private RequestPVPBattleResult(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPVPBattleResult(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPVPBattleResult defaultInstance;
    public static RequestPVPBattleResult getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPVPBattleResult getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPVPBattleResult(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              victory_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBattleResult_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBattleResult_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.RequestPVPBattleResult.class, protocol.PVPData.RequestPVPBattleResult.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPVPBattleResult> PARSER =
        new com.google.protobuf.AbstractParser<RequestPVPBattleResult>() {
      public RequestPVPBattleResult parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPVPBattleResult(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPVPBattleResult> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool victory = 1;
    public static final int VICTORY_FIELD_NUMBER = 1;
    private boolean victory_;
    /**
     * <code>required bool victory = 1;</code>
     */
    public boolean hasVictory() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool victory = 1;</code>
     */
    public boolean getVictory() {
      return victory_;
    }

    private void initFields() {
      victory_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasVictory()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, victory_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, victory_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.RequestPVPBattleResult parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattleResult parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.RequestPVPBattleResult prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPVPBattleResult}
     *
     * <pre>
     * 1333
     * PVP战斗 输赢
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.RequestPVPBattleResultOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattleResult_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattleResult_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.RequestPVPBattleResult.class, protocol.PVPData.RequestPVPBattleResult.Builder.class);
      }

      // Construct using protocol.PVPData.RequestPVPBattleResult.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        victory_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattleResult_descriptor;
      }

      public protocol.PVPData.RequestPVPBattleResult getDefaultInstanceForType() {
        return protocol.PVPData.RequestPVPBattleResult.getDefaultInstance();
      }

      public protocol.PVPData.RequestPVPBattleResult build() {
        protocol.PVPData.RequestPVPBattleResult result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.RequestPVPBattleResult buildPartial() {
        protocol.PVPData.RequestPVPBattleResult result = new protocol.PVPData.RequestPVPBattleResult(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.victory_ = victory_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.RequestPVPBattleResult) {
          return mergeFrom((protocol.PVPData.RequestPVPBattleResult)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.RequestPVPBattleResult other) {
        if (other == protocol.PVPData.RequestPVPBattleResult.getDefaultInstance()) return this;
        if (other.hasVictory()) {
          setVictory(other.getVictory());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasVictory()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.RequestPVPBattleResult parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.RequestPVPBattleResult) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool victory = 1;
      private boolean victory_ ;
      /**
       * <code>required bool victory = 1;</code>
       */
      public boolean hasVictory() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool victory = 1;</code>
       */
      public boolean getVictory() {
        return victory_;
      }
      /**
       * <code>required bool victory = 1;</code>
       */
      public Builder setVictory(boolean value) {
        bitField0_ |= 0x00000001;
        victory_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool victory = 1;</code>
       */
      public Builder clearVictory() {
        bitField0_ = (bitField0_ & ~0x00000001);
        victory_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPVPBattleResult)
    }

    static {
      defaultInstance = new RequestPVPBattleResult(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPVPBattleResult)
  }

  public interface RequestPVPBattleRemainTimeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestPVPBattleRemainTime}
   *
   * <pre>
   * 1334
   * 获取剩余时间
   * </pre>
   */
  public static final class RequestPVPBattleRemainTime extends
      com.google.protobuf.GeneratedMessage
      implements RequestPVPBattleRemainTimeOrBuilder {
    // Use RequestPVPBattleRemainTime.newBuilder() to construct.
    private RequestPVPBattleRemainTime(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPVPBattleRemainTime(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPVPBattleRemainTime defaultInstance;
    public static RequestPVPBattleRemainTime getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPVPBattleRemainTime getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPVPBattleRemainTime(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBattleRemainTime_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_RequestPVPBattleRemainTime_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.RequestPVPBattleRemainTime.class, protocol.PVPData.RequestPVPBattleRemainTime.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPVPBattleRemainTime> PARSER =
        new com.google.protobuf.AbstractParser<RequestPVPBattleRemainTime>() {
      public RequestPVPBattleRemainTime parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPVPBattleRemainTime(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPVPBattleRemainTime> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.RequestPVPBattleRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.RequestPVPBattleRemainTime prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPVPBattleRemainTime}
     *
     * <pre>
     * 1334
     * 获取剩余时间
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.RequestPVPBattleRemainTimeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattleRemainTime_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattleRemainTime_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.RequestPVPBattleRemainTime.class, protocol.PVPData.RequestPVPBattleRemainTime.Builder.class);
      }

      // Construct using protocol.PVPData.RequestPVPBattleRemainTime.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_RequestPVPBattleRemainTime_descriptor;
      }

      public protocol.PVPData.RequestPVPBattleRemainTime getDefaultInstanceForType() {
        return protocol.PVPData.RequestPVPBattleRemainTime.getDefaultInstance();
      }

      public protocol.PVPData.RequestPVPBattleRemainTime build() {
        protocol.PVPData.RequestPVPBattleRemainTime result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.RequestPVPBattleRemainTime buildPartial() {
        protocol.PVPData.RequestPVPBattleRemainTime result = new protocol.PVPData.RequestPVPBattleRemainTime(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.RequestPVPBattleRemainTime) {
          return mergeFrom((protocol.PVPData.RequestPVPBattleRemainTime)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.RequestPVPBattleRemainTime other) {
        if (other == protocol.PVPData.RequestPVPBattleRemainTime.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.RequestPVPBattleRemainTime parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.RequestPVPBattleRemainTime) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPVPBattleRemainTime)
    }

    static {
      defaultInstance = new RequestPVPBattleRemainTime(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPVPBattleRemainTime)
  }

  public interface ResponsePVPBattleRemainTimeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 time_second = 1;
    /**
     * <code>required int64 time_second = 1;</code>
     */
    boolean hasTimeSecond();
    /**
     * <code>required int64 time_second = 1;</code>
     */
    long getTimeSecond();
  }
  /**
   * Protobuf type {@code protocol.ResponsePVPBattleRemainTime}
   *
   * <pre>
   * 2334
   * </pre>
   */
  public static final class ResponsePVPBattleRemainTime extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePVPBattleRemainTimeOrBuilder {
    // Use ResponsePVPBattleRemainTime.newBuilder() to construct.
    private ResponsePVPBattleRemainTime(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePVPBattleRemainTime(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePVPBattleRemainTime defaultInstance;
    public static ResponsePVPBattleRemainTime getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePVPBattleRemainTime getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePVPBattleRemainTime(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              timeSecond_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPBattleRemainTime_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PVPData.internal_static_protocol_ResponsePVPBattleRemainTime_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PVPData.ResponsePVPBattleRemainTime.class, protocol.PVPData.ResponsePVPBattleRemainTime.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePVPBattleRemainTime> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePVPBattleRemainTime>() {
      public ResponsePVPBattleRemainTime parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePVPBattleRemainTime(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePVPBattleRemainTime> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 time_second = 1;
    public static final int TIME_SECOND_FIELD_NUMBER = 1;
    private long timeSecond_;
    /**
     * <code>required int64 time_second = 1;</code>
     */
    public boolean hasTimeSecond() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 time_second = 1;</code>
     */
    public long getTimeSecond() {
      return timeSecond_;
    }

    private void initFields() {
      timeSecond_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasTimeSecond()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, timeSecond_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, timeSecond_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PVPData.ResponsePVPBattleRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PVPData.ResponsePVPBattleRemainTime prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePVPBattleRemainTime}
     *
     * <pre>
     * 2334
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PVPData.ResponsePVPBattleRemainTimeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBattleRemainTime_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBattleRemainTime_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PVPData.ResponsePVPBattleRemainTime.class, protocol.PVPData.ResponsePVPBattleRemainTime.Builder.class);
      }

      // Construct using protocol.PVPData.ResponsePVPBattleRemainTime.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        timeSecond_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PVPData.internal_static_protocol_ResponsePVPBattleRemainTime_descriptor;
      }

      public protocol.PVPData.ResponsePVPBattleRemainTime getDefaultInstanceForType() {
        return protocol.PVPData.ResponsePVPBattleRemainTime.getDefaultInstance();
      }

      public protocol.PVPData.ResponsePVPBattleRemainTime build() {
        protocol.PVPData.ResponsePVPBattleRemainTime result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PVPData.ResponsePVPBattleRemainTime buildPartial() {
        protocol.PVPData.ResponsePVPBattleRemainTime result = new protocol.PVPData.ResponsePVPBattleRemainTime(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.timeSecond_ = timeSecond_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PVPData.ResponsePVPBattleRemainTime) {
          return mergeFrom((protocol.PVPData.ResponsePVPBattleRemainTime)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PVPData.ResponsePVPBattleRemainTime other) {
        if (other == protocol.PVPData.ResponsePVPBattleRemainTime.getDefaultInstance()) return this;
        if (other.hasTimeSecond()) {
          setTimeSecond(other.getTimeSecond());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasTimeSecond()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PVPData.ResponsePVPBattleRemainTime parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PVPData.ResponsePVPBattleRemainTime) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 time_second = 1;
      private long timeSecond_ ;
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public boolean hasTimeSecond() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public long getTimeSecond() {
        return timeSecond_;
      }
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public Builder setTimeSecond(long value) {
        bitField0_ |= 0x00000001;
        timeSecond_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public Builder clearTimeSecond() {
        bitField0_ = (bitField0_ & ~0x00000001);
        timeSecond_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePVPBattleRemainTime)
    }

    static {
      defaultInstance = new ResponsePVPBattleRemainTime(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePVPBattleRemainTime)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PetData_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PetData_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PVPTeam_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PVPTeam_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_PVPBattlePlayer_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_PVPBattlePlayer_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPVPPetOperate_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPVPPetOperate_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePVPPetOperate_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePVPPetOperate_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPVPBaseData_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPVPBaseData_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePVPBaseData_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePVPBaseData_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPVPBattle_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPVPBattle_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePVPBattle_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePVPBattle_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPVPBattleResult_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPVPBattleResult_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPVPBattleRemainTime_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPVPBattleRemainTime_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePVPBattleRemainTime_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePVPBattleRemainTime_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tpvp.proto\022\010protocol\032\013proto.proto\"\373\001\n\007P" +
      "etData\022\013\n\003uid\030\001 \002(\005\022\n\n\002id\030\002 \002(\005\022\020\n\010nameI" +
      "tem\030\003 \002(\t\022\024\n\014currentLevel\030\004 \002(\005\022\n\n\002hp\030\005 " +
      "\002(\002\022\n\n\002sp\030\006 \002(\002\022\016\n\006attack\030\007 \002(\002\022\017\n\007defen" +
      "se\030\010 \002(\002\022\025\n\rspecialAttack\030\t \002(\002\022\026\n\016speci" +
      "alDefense\030\n \002(\002\022\r\n\005speed\030\013 \002(\002\022\r\n\005avoid\030" +
      "\014 \002(\005\022\013\n\003hid\030\r \002(\005\022\013\n\003crt\030\016 \002(\005\022\017\n\007typeo" +
      "ne\030\017 \002(\005\"\256\001\n\007PVPTeam\022\037\n\004pet1\030\001 \001(\0132\021.pro" +
      "tocol.PetData\022\037\n\004pet2\030\002 \001(\0132\021.protocol.P" +
      "etData\022\037\n\004pet3\030\003 \001(\0132\021.protocol.PetData\022",
      "\037\n\004pet4\030\004 \001(\0132\021.protocol.PetData\022\037\n\004pet5" +
      "\030\005 \001(\0132\021.protocol.PetData\":\n\017PVPBattlePl" +
      "ayer\022\014\n\004name\030\001 \002(\t\022\013\n\003sex\030\002 \002(\005\022\014\n\004head\030" +
      "\003 \002(\005\"V\n\024RequestPVPPetOperate\022\014\n\004type\030\001 " +
      "\002(\005\022\017\n\007userUid\030\002 \001(\005\022\037\n\004pets\030\003 \001(\0132\021.pro" +
      "tocol.PVPTeam\"F\n\025ResponsePVPPetOperate\022\014" +
      "\n\004type\030\001 \002(\005\022\037\n\004pets\030\002 \001(\0132\021.protocol.PV" +
      "PTeam\"\024\n\022RequestPVPBaseData\"Q\n\023ResponseP" +
      "VPBaseData\022\014\n\004rank\030\001 \002(\003\022\r\n\005score\030\002 \002(\005\022" +
      "\017\n\007victory\030\003 \002(\005\022\014\n\004fail\030\004 \002(\005\"\022\n\020Reques",
      "tPVPBattle\"\210\001\n\021ResponsePVPBattle\022\037\n\004pets" +
      "\030\001 \002(\0132\021.protocol.PVPTeam\022\014\n\004name\030\002 \002(\t\022" +
      "\013\n\003sex\030\003 \002(\005\022\014\n\004head\030\004 \002(\005\022)\n\006player\030\005 \003" +
      "(\0132\031.protocol.PVPBattlePlayer\")\n\026Request" +
      "PVPBattleResult\022\017\n\007victory\030\001 \002(\010\"\034\n\032Requ" +
      "estPVPBattleRemainTime\"2\n\033ResponsePVPBat" +
      "tleRemainTime\022\023\n\013time_second\030\001 \002(\003B\tB\007PV" +
      "PData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_PetData_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_PetData_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PetData_descriptor,
              new java.lang.String[] { "Uid", "Id", "NameItem", "CurrentLevel", "Hp", "Sp", "Attack", "Defense", "SpecialAttack", "SpecialDefense", "Speed", "Avoid", "Hid", "Crt", "Typeone", });
          internal_static_protocol_PVPTeam_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_PVPTeam_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PVPTeam_descriptor,
              new java.lang.String[] { "Pet1", "Pet2", "Pet3", "Pet4", "Pet5", });
          internal_static_protocol_PVPBattlePlayer_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_PVPBattlePlayer_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_PVPBattlePlayer_descriptor,
              new java.lang.String[] { "Name", "Sex", "Head", });
          internal_static_protocol_RequestPVPPetOperate_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestPVPPetOperate_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPVPPetOperate_descriptor,
              new java.lang.String[] { "Type", "UserUid", "Pets", });
          internal_static_protocol_ResponsePVPPetOperate_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponsePVPPetOperate_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePVPPetOperate_descriptor,
              new java.lang.String[] { "Type", "Pets", });
          internal_static_protocol_RequestPVPBaseData_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_RequestPVPBaseData_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPVPBaseData_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponsePVPBaseData_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_protocol_ResponsePVPBaseData_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePVPBaseData_descriptor,
              new java.lang.String[] { "Rank", "Score", "Victory", "Fail", });
          internal_static_protocol_RequestPVPBattle_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_protocol_RequestPVPBattle_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPVPBattle_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponsePVPBattle_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_protocol_ResponsePVPBattle_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePVPBattle_descriptor,
              new java.lang.String[] { "Pets", "Name", "Sex", "Head", "Player", });
          internal_static_protocol_RequestPVPBattleResult_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_protocol_RequestPVPBattleResult_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPVPBattleResult_descriptor,
              new java.lang.String[] { "Victory", });
          internal_static_protocol_RequestPVPBattleRemainTime_descriptor =
            getDescriptor().getMessageTypes().get(10);
          internal_static_protocol_RequestPVPBattleRemainTime_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPVPBattleRemainTime_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponsePVPBattleRemainTime_descriptor =
            getDescriptor().getMessageTypes().get(11);
          internal_static_protocol_ResponsePVPBattleRemainTime_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePVPBattleRemainTime_descriptor,
              new java.lang.String[] { "TimeSecond", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
