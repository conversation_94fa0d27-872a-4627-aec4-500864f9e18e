package module.friend;

import entities.RoleEntity;
import model.FriendInfo;
import model.MailInfo;
import model.MessageInfo;
import module.callback.CallBack;
import net.sf.json.JSONObject;
import protocol.FriendData;

import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2018/2/1.
 */
public interface IFriend {
    FriendData.ResponseSendChat.Builder sendChat(String uid, FriendData.Chat chat, int friendId);

    FriendData.ResponseOperateFriend.Builder operateFriend(String uid, int type, int id);

    FriendData.ResponseOperateMessage.Builder operateMessage(String uid, String mid, int type);

    FriendData.ReportFriend setReportFriend(String uid, int status);

    FriendData.ReportUpdateFriend setUpdateFriend(String uid, String name, int lv, int head, int status, int station, String signature, long endless);

    void deleteMessage(String uid, String mid);

    void deleteMessageSql(String uid, String mid);

    String publishMail(JSONObject jsonObject);

    //公告
    String publishNotice(JSONObject jsonObject);

    MailInfo mapToMailInfo(Map<String, String> mailMap);

    void getRoleUidById(int id, CallBack callBack, int type);

    //调用消息
    MessageInfo addNewMessage(String uid, int type, String content, String mid, long timeStamp);

    void deleteOffLineMessage(String uid, List<MessageInfo> messageList);

    FriendData.ResponseGivePresent.Builder givePresent(String uid, int friendId, int type, List<Integer> idList);

    int accusation(String uid, int targetId);

    FriendInfo getFriendInfoById(String uid, int friendId);

    FriendData.ReportBroadcast setReportBroadcast(int type, int id, String content);

    //发送消息reportMessage
    void reportMessage(String mid, int type, String content, String sendUid);

    void reportChat(String uid, FriendData.Chat chat, FriendData.Player player, String sendUid, int friendId);

    FriendData.Friend.Builder agreeWithFriend(RoleEntity roleEntity, boolean isOnline, String uid, String mid);

    List<FriendData.Recommend> getOnlineRecommend(int server, int num, int type, List<String> dumpList);

    void recommendFriend(String uid, int type);
}
