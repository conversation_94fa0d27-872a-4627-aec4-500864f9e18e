package manager;

import common.SuperConfig;
import module.synchronization.SyncManager;
import redis.clients.jedis.Jedis;
import utils.MyUtils;

import java.util.List;

/**
 * Created by nara on 2018/5/3.
 */
public class UserDataManager {

    public static void setNewUserToRedis(int id){
        int val = MyUtils.getApartDays(Long.parseLong(SuperConfig.STARTSERVERSTAMP),TimerHandler.nowTimeStamp);
        val += 1;
        String key = null;
        if (val <= 7){
            key = val+"";
        }else {
            key = TimerHandler.todayStr;
        }
        Jedis jedis = Redis.getJedis(1);
        jedis.lpush("dailynewuser:"+key,id+"");
        Redis.destory(jedis);
    }
    public static void setUserLoginToRedis(int id){
        int val = MyUtils.getApartDays(Long.parseLong(SuperConfig.STARTSERVERSTAMP),TimerHandler.nowTimeStamp);
        val += 1;
        String key = null;
        if (val <= 7){
            key = val+"";
        }else {
            key = TimerHandler.todayStr;
        }
        Jedis jedis = Redis.getJedis(1);
        jedis.lpush("dailyuserlogin:"+key,id+"");
        Redis.destory(jedis);
    }

    //1次留 2三日留 3七日留
    public static double getGameData(int type){
        double result = 0;
        int day = 0;
        if (type == 1){
            day = 2;
        }else if (type == 2){
            day = 3;
        }else if (type == 3){
            day = 7;
        }
        Jedis jedis = Redis.getJedis(1);
        long firstDayNewUserLen = jedis.llen("dailynewuser:1");
        List<String> firstDayNewUserList = jedis.lrange("dailynewuser:1",0,-1);
        List<String> theDayUserLoginList = jedis.lrange("dailyuserlogin:"+day,0,-1);
        if (firstDayNewUserList.size() == 0 || theDayUserLoginList.size() == 0){
            return -1;
        }
        int total = 0;
        for (int i = 0 ; i<firstDayNewUserList.size() ; i++){
            String s = firstDayNewUserList.get(i);
            for (int j = 0 ; j<theDayUserLoginList.size() ; j++){
                String tmp = theDayUserLoginList.get(j);
                if (s.equals(tmp)){
                    total++;
                    break;
                }
            }
        }
        result = Math.ceil((total / firstDayNewUserLen)*100)/100;
        Redis.destory(jedis);
        return result;
    }

    public static double getGameData(String baseDay,String theDay){
        double result = 0;

        Jedis jedis = Redis.getJedis(1);
        long firstDayNewUserLen = jedis.llen("dailynewuser:"+baseDay);
        List<String> firstDayNewUserList = jedis.lrange("dailynewuser:"+baseDay,0,-1);
        List<String> theDayUserLoginList = jedis.lrange("dailyuserlogin:"+theDay,0,-1);
        if (firstDayNewUserList.size() == 0 || theDayUserLoginList.size() == 0){
            return -1;
        }
        int total = 0;
        for (int i = 0 ; i<firstDayNewUserList.size() ; i++){
            String s = firstDayNewUserList.get(i);
            for (int j = 0 ; j<theDayUserLoginList.size() ; j++){
                String tmp = theDayUserLoginList.get(j);
                if (s.equals(tmp)){
                    total++;
                    break;
                }
            }
        }
       /* /// System.out.println("firstDayNewUser_Num: "+firstDayNewUserLen);
        /// System.out.println("total: "+total);*/
        result = (double)total / firstDayNewUserLen;
        Redis.destory(jedis);
        return result;
    }
}
