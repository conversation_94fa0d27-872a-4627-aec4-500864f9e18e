package common;

import common.ExcelConfigObject;

@ExcelConfigObject(key = "pet")
public class PetConfig {
    public static final int commonLockIndex = 0;  //宠物通用锁标记位
    public static final int formationLockIndex = 1;//宠物编队锁锁标记位
    public static final int eggFormLockIndex = 2;//宠物蛋形态锁标记位
    public static final int breedLockIndex = 3;//宠物蛋形态锁标记位
    public static final int dispatchLockIndex = 4;//宠物派遣锁标记位
    public static final int petFromGM = 0; //盲盒卡池的宠物
    public static final int petFromRafflePool = 1; //盲盒卡池的宠物
    public static final int PetFromDraw = 2;//普通卡池宠物
    public static final int PetFromBreed = 3;//孵化的宠物
    public static final int PetFromCompose = 4;//合成的宠物
    public static final int PetFromOfficial = 5;//官方赠送宠物
    @ExcelColumn(name = "id")
    private int petType;  //宠物类型
    @ExcelColumn(name = "cost")
    private int cost;   //阵容的限制值
    /*    @ExcelColumn(name="name")
        private String name;//宠物名字*/
    @ExcelColumn(name = "typeone")
    private int typeone; // 主属性
    @ExcelColumn(name = "typetwo")
    private int typetwo; // 副属性
    /*    @ExcelColumn(name="hpgrow")
        private int hpgrow;  //体力成长
        @ExcelColumn(name="atkgrow")
        private int atkgrow;//攻击力成长
        @ExcelColumn(name="defgrow")
        private int defgrow;//防御力成长
        @ExcelColumn(name="satkgrow")
        private int satkgrow;//特殊攻击成长
        @ExcelColumn(name="sdefgrow")
        private int sdefgrow;//特殊防御成长
        @ExcelColumn(name="speedgrow")
        private int speedgrow;//速度成长*/
    @ExcelColumn(name = "normalskillid")
    private int normalskillid;//普通攻击技能ID
    @ExcelColumn(name = "spskillid")
    private int spskillid;//主动技能ID
    @ExcelColumn(name = "profession")
    private int profession;
    @ExcelColumn(name = "breed_YN")
    private int petKind;

    public int getPetType() {
        return petType;
    }

    public void setPetType(int petType) {
        this.petType = petType;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public int getTypeone() {
        return typeone;
    }

    public void setTypeone(int typeone) {
        this.typeone = typeone;
    }

    public int getTypetwo() {
        return typetwo;
    }

    public void setTypetwo(int typetwo) {
        this.typetwo = typetwo;
    }

    public int getNormalskillid() {
        return normalskillid;
    }

    public void setNormalskillid(int normalskillid) {
        this.normalskillid = normalskillid;
    }

    public int getSpskillid() {
        return spskillid;
    }

    public void setSpskillid(int spskillid) {
        this.spskillid = spskillid;
    }

/*    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }*/

    public static int getCommonLockIndex() {
        return commonLockIndex;
    }

    public static int getFormationLockIndex() {
        return formationLockIndex;
    }

    public static int getEggFormLockIndex() {
        return eggFormLockIndex;
    }

    public static int getBreedLockIndex() {
        return breedLockIndex;
    }

    public int getProfession() {
        return profession;
    }

    public void setProfession(int profession) {
        this.profession = profession;
    }

    public int getPetKind() {
        return petKind;
    }

    public void setPetKind(int petKind) {
        this.petKind = petKind;
    }

}
