package module.synchronization;

import manager.ReportManager;
import protocol.MissionData;
import protocol.ProtoData;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

/**
 * 帧同步（暂弃）
 * Created by nara on 2018/5/29.
 */
public class SyncMissionHandler implements Runnable {
    public SyncMissionHandler(String key){
        this.key = key;
    }

    private static int currentFrame = 0;

    private ScheduledExecutorService service;
    private String key;

    public void run() {
        boardcastMissionProgress(currentFrame);
        this.currentFrame ++;
    }

    private void boardcastMissionProgress(int frame){
        GameInfo gameInfo = SyncManager.getSyncProgressList().get(this.key);
        if (gameInfo == null){
            this.service.shutdown();
        }
//        List<SyncMissionInfo> list = gameInfo.getMap().get(frame).getSyncMap();
        List<BallInfo> ballList = gameInfo.getMapInfo().getBallList();
        int index1 = -1;
        int index2 = -1;
        int n = 0;
        for (Map.Entry<Integer,Integer>entry:gameInfo.getMapInfo().getPlayerOnBall().entrySet()){
            if (n == 0){
                index1 = entry.getValue();
            }else {
                index2 = entry.getValue();
            }
            n++;
        }
        int unBall = 0;
        if (index1 > -1 && index2 > -1){
            unBall = 2;
        }else if (index1 > -1 || index2 > -1){
            unBall = 1;
        }
        int size = ballList.size();
        for (int i = 0 ; i<size ; i++){
            BallInfo ballInfo = ballList.get(i);
            if (ballInfo.getIndex() == index1 || ballInfo.getIndex() == index2){
                continue;
            }
            int type = ballInfo.getType();
//            float speed = ballInfo.getSpeed();
            int val = type == 1 ? 280/30 : -280/30 ;
            int x = ballInfo.getPoint().getX()+val;
            if ((type == 1 && x > 2500) || (type == 2 && x < -1380)){//新增一个球
                if (ballInfo.getIntegral() == 0){
                    ballList.remove(i);
                    size -= 1;
                }else if (size - unBall <= 2){
                    //增加一个球
//                /// System.out.println("remove ball>>>>>>>>>>>>>>>>>>>>>>>"+ballInfo.getIndex());
                    MissionData.ReportMissionProgress.Builder progressBu = MissionData.ReportMissionProgress.newBuilder();
                    BallInfo newBallInfo = SyncManager.setFightBall(gameInfo.getMapInfo().getBallNum());

                    int jZhi = newBallInfo.getType() == 1 ? 200 : 1180;
                    for (int j = 0 ; j < ballList.size() ; j++){
                        BallInfo tmp = ballList.get(j);
                        if (i == j || tmp.getIntegral() == 0)
                            continue;
                        if (tmp.getType() == newBallInfo.getType()){
                            if (newBallInfo.getType() == 1 && jZhi > tmp.getPoint().getX()){
                                jZhi = tmp.getPoint().getX();
                            }else if (newBallInfo.getType() == 2 && jZhi < tmp.getPoint().getX()){
                                jZhi = tmp.getPoint().getX();
                            }
                        }
                    }
                    int sep = newBallInfo.getType() == 1 ? -1 : 1;
                    int add = 500*sep;
                    newBallInfo.getPoint().setX(jZhi+add);
                    ballList.remove(i);
                    ballList.add(0,newBallInfo);
                    MissionData.PvpBall.Builder pvpBall = MissionData.PvpBall.newBuilder();
                    MissionData.BigBall bigBall = BallInfo.setBigBall(ballInfo);
                    pvpBall.setSpeed(ballInfo.getSpeed());
                    pvpBall.setBigBall(bigBall);
                    pvpBall.setId(newBallInfo.getIndex());
                    progressBu.setBigBall(pvpBall);
                    for (Map.Entry<Integer,FightPlayerInfo>entry:gameInfo.getFightPlayers().entrySet()){
                        FightPlayerInfo fightPlayerInfo = entry.getValue();
                        ReportManager.reportInfo(fightPlayerInfo.getCtx(), ProtoData.MToC.REPORTMISSIONPROGRESS_VALUE, progressBu.build().toByteArray());
                    }
                    gameInfo.getMapInfo().setBallNum(gameInfo.getMapInfo().getBallNum()+1);
                }

            }else {
                ballInfo.getPoint().setX(x);
            }

            //广播每帧
        }

        //多人战斗结算

        //游戏结束回到房间中
//        SyncManager.overFight(this.key);
    }
}
