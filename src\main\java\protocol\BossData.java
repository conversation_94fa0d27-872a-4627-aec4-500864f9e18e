// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: boss.proto

package protocol;

public final class BossData {
  private BossData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestbossTiemOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestbossTiem}
   *
   * <pre>
   *1325
   * </pre>
   */
  public static final class RequestbossTiem extends
      com.google.protobuf.GeneratedMessage
      implements RequestbossTiemOrBuilder {
    // Use RequestbossTiem.newBuilder() to construct.
    private RequestbossTiem(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestbossTiem(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestbossTiem defaultInstance;
    public static RequestbossTiem getDefaultInstance() {
      return defaultInstance;
    }

    public RequestbossTiem getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestbossTiem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BossData.internal_static_protocol_RequestbossTiem_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BossData.internal_static_protocol_RequestbossTiem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BossData.RequestbossTiem.class, protocol.BossData.RequestbossTiem.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestbossTiem> PARSER =
        new com.google.protobuf.AbstractParser<RequestbossTiem>() {
      public RequestbossTiem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestbossTiem(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestbossTiem> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BossData.RequestbossTiem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BossData.RequestbossTiem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BossData.RequestbossTiem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.RequestbossTiem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BossData.RequestbossTiem prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestbossTiem}
     *
     * <pre>
     *1325
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BossData.RequestbossTiemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BossData.internal_static_protocol_RequestbossTiem_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BossData.internal_static_protocol_RequestbossTiem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BossData.RequestbossTiem.class, protocol.BossData.RequestbossTiem.Builder.class);
      }

      // Construct using protocol.BossData.RequestbossTiem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BossData.internal_static_protocol_RequestbossTiem_descriptor;
      }

      public protocol.BossData.RequestbossTiem getDefaultInstanceForType() {
        return protocol.BossData.RequestbossTiem.getDefaultInstance();
      }

      public protocol.BossData.RequestbossTiem build() {
        protocol.BossData.RequestbossTiem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BossData.RequestbossTiem buildPartial() {
        protocol.BossData.RequestbossTiem result = new protocol.BossData.RequestbossTiem(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BossData.RequestbossTiem) {
          return mergeFrom((protocol.BossData.RequestbossTiem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BossData.RequestbossTiem other) {
        if (other == protocol.BossData.RequestbossTiem.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BossData.RequestbossTiem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BossData.RequestbossTiem) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestbossTiem)
    }

    static {
      defaultInstance = new RequestbossTiem(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestbossTiem)
  }

  public interface ResponsebosstimeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool start = 1;
    /**
     * <code>required bool start = 1;</code>
     */
    boolean hasStart();
    /**
     * <code>required bool start = 1;</code>
     */
    boolean getStart();
  }
  /**
   * Protobuf type {@code protocol.Responsebosstime}
   *
   * <pre>
   *2325
   * </pre>
   */
  public static final class Responsebosstime extends
      com.google.protobuf.GeneratedMessage
      implements ResponsebosstimeOrBuilder {
    // Use Responsebosstime.newBuilder() to construct.
    private Responsebosstime(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Responsebosstime(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Responsebosstime defaultInstance;
    public static Responsebosstime getDefaultInstance() {
      return defaultInstance;
    }

    public Responsebosstime getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Responsebosstime(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              start_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BossData.internal_static_protocol_Responsebosstime_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BossData.internal_static_protocol_Responsebosstime_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BossData.Responsebosstime.class, protocol.BossData.Responsebosstime.Builder.class);
    }

    public static com.google.protobuf.Parser<Responsebosstime> PARSER =
        new com.google.protobuf.AbstractParser<Responsebosstime>() {
      public Responsebosstime parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Responsebosstime(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Responsebosstime> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool start = 1;
    public static final int START_FIELD_NUMBER = 1;
    private boolean start_;
    /**
     * <code>required bool start = 1;</code>
     */
    public boolean hasStart() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool start = 1;</code>
     */
    public boolean getStart() {
      return start_;
    }

    private void initFields() {
      start_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasStart()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, start_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, start_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BossData.Responsebosstime parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Responsebosstime parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Responsebosstime parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Responsebosstime parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Responsebosstime parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Responsebosstime parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Responsebosstime parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BossData.Responsebosstime parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Responsebosstime parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Responsebosstime parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BossData.Responsebosstime prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Responsebosstime}
     *
     * <pre>
     *2325
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BossData.ResponsebosstimeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BossData.internal_static_protocol_Responsebosstime_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BossData.internal_static_protocol_Responsebosstime_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BossData.Responsebosstime.class, protocol.BossData.Responsebosstime.Builder.class);
      }

      // Construct using protocol.BossData.Responsebosstime.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        start_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BossData.internal_static_protocol_Responsebosstime_descriptor;
      }

      public protocol.BossData.Responsebosstime getDefaultInstanceForType() {
        return protocol.BossData.Responsebosstime.getDefaultInstance();
      }

      public protocol.BossData.Responsebosstime build() {
        protocol.BossData.Responsebosstime result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BossData.Responsebosstime buildPartial() {
        protocol.BossData.Responsebosstime result = new protocol.BossData.Responsebosstime(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.start_ = start_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BossData.Responsebosstime) {
          return mergeFrom((protocol.BossData.Responsebosstime)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BossData.Responsebosstime other) {
        if (other == protocol.BossData.Responsebosstime.getDefaultInstance()) return this;
        if (other.hasStart()) {
          setStart(other.getStart());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasStart()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BossData.Responsebosstime parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BossData.Responsebosstime) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool start = 1;
      private boolean start_ ;
      /**
       * <code>required bool start = 1;</code>
       */
      public boolean hasStart() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool start = 1;</code>
       */
      public boolean getStart() {
        return start_;
      }
      /**
       * <code>required bool start = 1;</code>
       */
      public Builder setStart(boolean value) {
        bitField0_ |= 0x00000001;
        start_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool start = 1;</code>
       */
      public Builder clearStart() {
        bitField0_ = (bitField0_ & ~0x00000001);
        start_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Responsebosstime)
    }

    static {
      defaultInstance = new Responsebosstime(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Responsebosstime)
  }

  public interface RequestbossdearOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required float tage = 1;
    /**
     * <code>required float tage = 1;</code>
     */
    boolean hasTage();
    /**
     * <code>required float tage = 1;</code>
     */
    float getTage();
  }
  /**
   * Protobuf type {@code protocol.Requestbossdear}
   *
   * <pre>
   *1326
   * </pre>
   */
  public static final class Requestbossdear extends
      com.google.protobuf.GeneratedMessage
      implements RequestbossdearOrBuilder {
    // Use Requestbossdear.newBuilder() to construct.
    private Requestbossdear(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Requestbossdear(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Requestbossdear defaultInstance;
    public static Requestbossdear getDefaultInstance() {
      return defaultInstance;
    }

    public Requestbossdear getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Requestbossdear(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 13: {
              bitField0_ |= 0x00000001;
              tage_ = input.readFloat();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BossData.internal_static_protocol_Requestbossdear_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BossData.internal_static_protocol_Requestbossdear_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BossData.Requestbossdear.class, protocol.BossData.Requestbossdear.Builder.class);
    }

    public static com.google.protobuf.Parser<Requestbossdear> PARSER =
        new com.google.protobuf.AbstractParser<Requestbossdear>() {
      public Requestbossdear parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Requestbossdear(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Requestbossdear> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required float tage = 1;
    public static final int TAGE_FIELD_NUMBER = 1;
    private float tage_;
    /**
     * <code>required float tage = 1;</code>
     */
    public boolean hasTage() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required float tage = 1;</code>
     */
    public float getTage() {
      return tage_;
    }

    private void initFields() {
      tage_ = 0F;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasTage()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeFloat(1, tage_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(1, tage_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BossData.Requestbossdear parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Requestbossdear parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Requestbossdear parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Requestbossdear parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Requestbossdear parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Requestbossdear parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Requestbossdear parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BossData.Requestbossdear parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Requestbossdear parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Requestbossdear parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BossData.Requestbossdear prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Requestbossdear}
     *
     * <pre>
     *1326
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BossData.RequestbossdearOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BossData.internal_static_protocol_Requestbossdear_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BossData.internal_static_protocol_Requestbossdear_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BossData.Requestbossdear.class, protocol.BossData.Requestbossdear.Builder.class);
      }

      // Construct using protocol.BossData.Requestbossdear.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        tage_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BossData.internal_static_protocol_Requestbossdear_descriptor;
      }

      public protocol.BossData.Requestbossdear getDefaultInstanceForType() {
        return protocol.BossData.Requestbossdear.getDefaultInstance();
      }

      public protocol.BossData.Requestbossdear build() {
        protocol.BossData.Requestbossdear result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BossData.Requestbossdear buildPartial() {
        protocol.BossData.Requestbossdear result = new protocol.BossData.Requestbossdear(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.tage_ = tage_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BossData.Requestbossdear) {
          return mergeFrom((protocol.BossData.Requestbossdear)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BossData.Requestbossdear other) {
        if (other == protocol.BossData.Requestbossdear.getDefaultInstance()) return this;
        if (other.hasTage()) {
          setTage(other.getTage());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasTage()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BossData.Requestbossdear parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BossData.Requestbossdear) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required float tage = 1;
      private float tage_ ;
      /**
       * <code>required float tage = 1;</code>
       */
      public boolean hasTage() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required float tage = 1;</code>
       */
      public float getTage() {
        return tage_;
      }
      /**
       * <code>required float tage = 1;</code>
       */
      public Builder setTage(float value) {
        bitField0_ |= 0x00000001;
        tage_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float tage = 1;</code>
       */
      public Builder clearTage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        tage_ = 0F;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Requestbossdear)
    }

    static {
      defaultInstance = new Requestbossdear(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Requestbossdear)
  }

  public interface ResponsebossdownOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required float bossblood = 1;
    /**
     * <code>required float bossblood = 1;</code>
     */
    boolean hasBossblood();
    /**
     * <code>required float bossblood = 1;</code>
     */
    float getBossblood();
  }
  /**
   * Protobuf type {@code protocol.Responsebossdown}
   *
   * <pre>
   *2326
   * </pre>
   */
  public static final class Responsebossdown extends
      com.google.protobuf.GeneratedMessage
      implements ResponsebossdownOrBuilder {
    // Use Responsebossdown.newBuilder() to construct.
    private Responsebossdown(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Responsebossdown(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Responsebossdown defaultInstance;
    public static Responsebossdown getDefaultInstance() {
      return defaultInstance;
    }

    public Responsebossdown getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Responsebossdown(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 13: {
              bitField0_ |= 0x00000001;
              bossblood_ = input.readFloat();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BossData.internal_static_protocol_Responsebossdown_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BossData.internal_static_protocol_Responsebossdown_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BossData.Responsebossdown.class, protocol.BossData.Responsebossdown.Builder.class);
    }

    public static com.google.protobuf.Parser<Responsebossdown> PARSER =
        new com.google.protobuf.AbstractParser<Responsebossdown>() {
      public Responsebossdown parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Responsebossdown(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Responsebossdown> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required float bossblood = 1;
    public static final int BOSSBLOOD_FIELD_NUMBER = 1;
    private float bossblood_;
    /**
     * <code>required float bossblood = 1;</code>
     */
    public boolean hasBossblood() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required float bossblood = 1;</code>
     */
    public float getBossblood() {
      return bossblood_;
    }

    private void initFields() {
      bossblood_ = 0F;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasBossblood()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeFloat(1, bossblood_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(1, bossblood_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BossData.Responsebossdown parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Responsebossdown parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Responsebossdown parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Responsebossdown parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Responsebossdown parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Responsebossdown parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Responsebossdown parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BossData.Responsebossdown parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Responsebossdown parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Responsebossdown parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BossData.Responsebossdown prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Responsebossdown}
     *
     * <pre>
     *2326
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BossData.ResponsebossdownOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BossData.internal_static_protocol_Responsebossdown_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BossData.internal_static_protocol_Responsebossdown_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BossData.Responsebossdown.class, protocol.BossData.Responsebossdown.Builder.class);
      }

      // Construct using protocol.BossData.Responsebossdown.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        bossblood_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BossData.internal_static_protocol_Responsebossdown_descriptor;
      }

      public protocol.BossData.Responsebossdown getDefaultInstanceForType() {
        return protocol.BossData.Responsebossdown.getDefaultInstance();
      }

      public protocol.BossData.Responsebossdown build() {
        protocol.BossData.Responsebossdown result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BossData.Responsebossdown buildPartial() {
        protocol.BossData.Responsebossdown result = new protocol.BossData.Responsebossdown(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.bossblood_ = bossblood_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BossData.Responsebossdown) {
          return mergeFrom((protocol.BossData.Responsebossdown)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BossData.Responsebossdown other) {
        if (other == protocol.BossData.Responsebossdown.getDefaultInstance()) return this;
        if (other.hasBossblood()) {
          setBossblood(other.getBossblood());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasBossblood()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BossData.Responsebossdown parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BossData.Responsebossdown) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required float bossblood = 1;
      private float bossblood_ ;
      /**
       * <code>required float bossblood = 1;</code>
       */
      public boolean hasBossblood() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required float bossblood = 1;</code>
       */
      public float getBossblood() {
        return bossblood_;
      }
      /**
       * <code>required float bossblood = 1;</code>
       */
      public Builder setBossblood(float value) {
        bitField0_ |= 0x00000001;
        bossblood_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float bossblood = 1;</code>
       */
      public Builder clearBossblood() {
        bitField0_ = (bitField0_ & ~0x00000001);
        bossblood_ = 0F;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Responsebossdown)
    }

    static {
      defaultInstance = new Responsebossdown(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Responsebossdown)
  }

  public interface ResponsebossliveOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool dead = 1;
    /**
     * <code>required bool dead = 1;</code>
     */
    boolean hasDead();
    /**
     * <code>required bool dead = 1;</code>
     */
    boolean getDead();

    // required float percentage = 2;
    /**
     * <code>required float percentage = 2;</code>
     */
    boolean hasPercentage();
    /**
     * <code>required float percentage = 2;</code>
     */
    float getPercentage();

    // required float harm_value = 3;
    /**
     * <code>required float harm_value = 3;</code>
     */
    boolean hasHarmValue();
    /**
     * <code>required float harm_value = 3;</code>
     */
    float getHarmValue();

    // required float bossblood = 4;
    /**
     * <code>required float bossblood = 4;</code>
     */
    boolean hasBossblood();
    /**
     * <code>required float bossblood = 4;</code>
     */
    float getBossblood();
  }
  /**
   * Protobuf type {@code protocol.Responsebosslive}
   *
   * <pre>
   *2327
   * </pre>
   */
  public static final class Responsebosslive extends
      com.google.protobuf.GeneratedMessage
      implements ResponsebossliveOrBuilder {
    // Use Responsebosslive.newBuilder() to construct.
    private Responsebosslive(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Responsebosslive(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Responsebosslive defaultInstance;
    public static Responsebosslive getDefaultInstance() {
      return defaultInstance;
    }

    public Responsebosslive getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Responsebosslive(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              dead_ = input.readBool();
              break;
            }
            case 21: {
              bitField0_ |= 0x00000002;
              percentage_ = input.readFloat();
              break;
            }
            case 29: {
              bitField0_ |= 0x00000004;
              harmValue_ = input.readFloat();
              break;
            }
            case 37: {
              bitField0_ |= 0x00000008;
              bossblood_ = input.readFloat();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BossData.internal_static_protocol_Responsebosslive_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BossData.internal_static_protocol_Responsebosslive_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BossData.Responsebosslive.class, protocol.BossData.Responsebosslive.Builder.class);
    }

    public static com.google.protobuf.Parser<Responsebosslive> PARSER =
        new com.google.protobuf.AbstractParser<Responsebosslive>() {
      public Responsebosslive parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Responsebosslive(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Responsebosslive> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool dead = 1;
    public static final int DEAD_FIELD_NUMBER = 1;
    private boolean dead_;
    /**
     * <code>required bool dead = 1;</code>
     */
    public boolean hasDead() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool dead = 1;</code>
     */
    public boolean getDead() {
      return dead_;
    }

    // required float percentage = 2;
    public static final int PERCENTAGE_FIELD_NUMBER = 2;
    private float percentage_;
    /**
     * <code>required float percentage = 2;</code>
     */
    public boolean hasPercentage() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required float percentage = 2;</code>
     */
    public float getPercentage() {
      return percentage_;
    }

    // required float harm_value = 3;
    public static final int HARM_VALUE_FIELD_NUMBER = 3;
    private float harmValue_;
    /**
     * <code>required float harm_value = 3;</code>
     */
    public boolean hasHarmValue() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required float harm_value = 3;</code>
     */
    public float getHarmValue() {
      return harmValue_;
    }

    // required float bossblood = 4;
    public static final int BOSSBLOOD_FIELD_NUMBER = 4;
    private float bossblood_;
    /**
     * <code>required float bossblood = 4;</code>
     */
    public boolean hasBossblood() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required float bossblood = 4;</code>
     */
    public float getBossblood() {
      return bossblood_;
    }

    private void initFields() {
      dead_ = false;
      percentage_ = 0F;
      harmValue_ = 0F;
      bossblood_ = 0F;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasDead()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPercentage()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHarmValue()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasBossblood()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, dead_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeFloat(2, percentage_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeFloat(3, harmValue_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeFloat(4, bossblood_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, dead_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(2, percentage_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(3, harmValue_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeFloatSize(4, bossblood_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BossData.Responsebosslive parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Responsebosslive parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Responsebosslive parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BossData.Responsebosslive parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BossData.Responsebosslive parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Responsebosslive parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Responsebosslive parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BossData.Responsebosslive parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BossData.Responsebosslive parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BossData.Responsebosslive parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BossData.Responsebosslive prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Responsebosslive}
     *
     * <pre>
     *2327
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BossData.ResponsebossliveOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BossData.internal_static_protocol_Responsebosslive_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BossData.internal_static_protocol_Responsebosslive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BossData.Responsebosslive.class, protocol.BossData.Responsebosslive.Builder.class);
      }

      // Construct using protocol.BossData.Responsebosslive.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        dead_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        percentage_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000002);
        harmValue_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000004);
        bossblood_ = 0F;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BossData.internal_static_protocol_Responsebosslive_descriptor;
      }

      public protocol.BossData.Responsebosslive getDefaultInstanceForType() {
        return protocol.BossData.Responsebosslive.getDefaultInstance();
      }

      public protocol.BossData.Responsebosslive build() {
        protocol.BossData.Responsebosslive result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BossData.Responsebosslive buildPartial() {
        protocol.BossData.Responsebosslive result = new protocol.BossData.Responsebosslive(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.dead_ = dead_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.percentage_ = percentage_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.harmValue_ = harmValue_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.bossblood_ = bossblood_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BossData.Responsebosslive) {
          return mergeFrom((protocol.BossData.Responsebosslive)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BossData.Responsebosslive other) {
        if (other == protocol.BossData.Responsebosslive.getDefaultInstance()) return this;
        if (other.hasDead()) {
          setDead(other.getDead());
        }
        if (other.hasPercentage()) {
          setPercentage(other.getPercentage());
        }
        if (other.hasHarmValue()) {
          setHarmValue(other.getHarmValue());
        }
        if (other.hasBossblood()) {
          setBossblood(other.getBossblood());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasDead()) {
          
          return false;
        }
        if (!hasPercentage()) {
          
          return false;
        }
        if (!hasHarmValue()) {
          
          return false;
        }
        if (!hasBossblood()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BossData.Responsebosslive parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BossData.Responsebosslive) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool dead = 1;
      private boolean dead_ ;
      /**
       * <code>required bool dead = 1;</code>
       */
      public boolean hasDead() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool dead = 1;</code>
       */
      public boolean getDead() {
        return dead_;
      }
      /**
       * <code>required bool dead = 1;</code>
       */
      public Builder setDead(boolean value) {
        bitField0_ |= 0x00000001;
        dead_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool dead = 1;</code>
       */
      public Builder clearDead() {
        bitField0_ = (bitField0_ & ~0x00000001);
        dead_ = false;
        onChanged();
        return this;
      }

      // required float percentage = 2;
      private float percentage_ ;
      /**
       * <code>required float percentage = 2;</code>
       */
      public boolean hasPercentage() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required float percentage = 2;</code>
       */
      public float getPercentage() {
        return percentage_;
      }
      /**
       * <code>required float percentage = 2;</code>
       */
      public Builder setPercentage(float value) {
        bitField0_ |= 0x00000002;
        percentage_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float percentage = 2;</code>
       */
      public Builder clearPercentage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        percentage_ = 0F;
        onChanged();
        return this;
      }

      // required float harm_value = 3;
      private float harmValue_ ;
      /**
       * <code>required float harm_value = 3;</code>
       */
      public boolean hasHarmValue() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required float harm_value = 3;</code>
       */
      public float getHarmValue() {
        return harmValue_;
      }
      /**
       * <code>required float harm_value = 3;</code>
       */
      public Builder setHarmValue(float value) {
        bitField0_ |= 0x00000004;
        harmValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float harm_value = 3;</code>
       */
      public Builder clearHarmValue() {
        bitField0_ = (bitField0_ & ~0x00000004);
        harmValue_ = 0F;
        onChanged();
        return this;
      }

      // required float bossblood = 4;
      private float bossblood_ ;
      /**
       * <code>required float bossblood = 4;</code>
       */
      public boolean hasBossblood() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required float bossblood = 4;</code>
       */
      public float getBossblood() {
        return bossblood_;
      }
      /**
       * <code>required float bossblood = 4;</code>
       */
      public Builder setBossblood(float value) {
        bitField0_ |= 0x00000008;
        bossblood_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required float bossblood = 4;</code>
       */
      public Builder clearBossblood() {
        bitField0_ = (bitField0_ & ~0x00000008);
        bossblood_ = 0F;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Responsebosslive)
    }

    static {
      defaultInstance = new Responsebosslive(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Responsebosslive)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestbossTiem_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestbossTiem_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Responsebosstime_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Responsebosstime_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Requestbossdear_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Requestbossdear_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Responsebossdown_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Responsebossdown_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Responsebosslive_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Responsebosslive_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nboss.proto\022\010protocol\032\013proto.proto\"\021\n\017R" +
      "equestbossTiem\"!\n\020Responsebosstime\022\r\n\005st" +
      "art\030\001 \002(\010\"\037\n\017Requestbossdear\022\014\n\004tage\030\001 \002" +
      "(\002\"%\n\020Responsebossdown\022\021\n\tbossblood\030\001 \002(" +
      "\002\"[\n\020Responsebosslive\022\014\n\004dead\030\001 \002(\010\022\022\n\np" +
      "ercentage\030\002 \002(\002\022\022\n\nharm_value\030\003 \002(\002\022\021\n\tb" +
      "ossblood\030\004 \002(\002B\nB\010BossData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestbossTiem_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestbossTiem_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestbossTiem_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_Responsebosstime_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_Responsebosstime_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Responsebosstime_descriptor,
              new java.lang.String[] { "Start", });
          internal_static_protocol_Requestbossdear_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_Requestbossdear_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Requestbossdear_descriptor,
              new java.lang.String[] { "Tage", });
          internal_static_protocol_Responsebossdown_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_Responsebossdown_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Responsebossdown_descriptor,
              new java.lang.String[] { "Bossblood", });
          internal_static_protocol_Responsebosslive_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_Responsebosslive_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Responsebosslive_descriptor,
              new java.lang.String[] { "Dead", "Percentage", "HarmValue", "Bossblood", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
