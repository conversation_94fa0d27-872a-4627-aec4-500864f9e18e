package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/5/6.
 */
@Entity
@Table(name = "present", schema = "", catalog = "super_star_fruit")
public class PresentEntity {
    private int id;
    private int type;
    private String uid;
    private String friend;
    private int num;
    private long timestamp;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "friend")
    public String getFriend() {
        return friend;
    }

    public void setFriend(String friend) {
        this.friend = friend;
    }

    @Basic
    @Column(name = "num")
    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    @Basic
    @Column(name = "timestamp")
    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PresentEntity that = (PresentEntity) o;

        if (id != that.id) return false;
        if (type != that.type) return false;
        if (num != that.num) return false;
        if (timestamp != that.timestamp) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (friend != null ? !friend.equals(that.friend) : that.friend != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + type;
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + (friend != null ? friend.hashCode() : 0);
        result = 31 * result + num;
        result = 31 * result + (int) (timestamp ^ (timestamp >>> 32));
        return result;
    }
}
