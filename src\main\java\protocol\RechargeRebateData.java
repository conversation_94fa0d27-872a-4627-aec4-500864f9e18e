// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rechargerebate.proto

package protocol;

public final class RechargeRebateData {
  private RechargeRebateData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface ResponsePayDiamondOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 diamond_num = 1;
    /**
     * <code>required int64 diamond_num = 1;</code>
     */
    boolean hasDiamondNum();
    /**
     * <code>required int64 diamond_num = 1;</code>
     */
    long getDiamondNum();
  }
  /**
   * Protobuf type {@code protocol.ResponsePayDiamond}
   *
   * <pre>
   * 2340 充值的总的钻石数量
   * </pre>
   */
  public static final class ResponsePayDiamond extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePayDiamondOrBuilder {
    // Use ResponsePayDiamond.newBuilder() to construct.
    private ResponsePayDiamond(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePayDiamond(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePayDiamond defaultInstance;
    public static ResponsePayDiamond getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePayDiamond getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePayDiamond(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              diamondNum_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RechargeRebateData.internal_static_protocol_ResponsePayDiamond_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RechargeRebateData.internal_static_protocol_ResponsePayDiamond_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RechargeRebateData.ResponsePayDiamond.class, protocol.RechargeRebateData.ResponsePayDiamond.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePayDiamond> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePayDiamond>() {
      public ResponsePayDiamond parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePayDiamond(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePayDiamond> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 diamond_num = 1;
    public static final int DIAMOND_NUM_FIELD_NUMBER = 1;
    private long diamondNum_;
    /**
     * <code>required int64 diamond_num = 1;</code>
     */
    public boolean hasDiamondNum() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 diamond_num = 1;</code>
     */
    public long getDiamondNum() {
      return diamondNum_;
    }

    private void initFields() {
      diamondNum_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasDiamondNum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, diamondNum_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, diamondNum_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RechargeRebateData.ResponsePayDiamond parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RechargeRebateData.ResponsePayDiamond prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePayDiamond}
     *
     * <pre>
     * 2340 充值的总的钻石数量
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RechargeRebateData.ResponsePayDiamondOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RechargeRebateData.internal_static_protocol_ResponsePayDiamond_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RechargeRebateData.internal_static_protocol_ResponsePayDiamond_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RechargeRebateData.ResponsePayDiamond.class, protocol.RechargeRebateData.ResponsePayDiamond.Builder.class);
      }

      // Construct using protocol.RechargeRebateData.ResponsePayDiamond.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        diamondNum_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RechargeRebateData.internal_static_protocol_ResponsePayDiamond_descriptor;
      }

      public protocol.RechargeRebateData.ResponsePayDiamond getDefaultInstanceForType() {
        return protocol.RechargeRebateData.ResponsePayDiamond.getDefaultInstance();
      }

      public protocol.RechargeRebateData.ResponsePayDiamond build() {
        protocol.RechargeRebateData.ResponsePayDiamond result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RechargeRebateData.ResponsePayDiamond buildPartial() {
        protocol.RechargeRebateData.ResponsePayDiamond result = new protocol.RechargeRebateData.ResponsePayDiamond(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.diamondNum_ = diamondNum_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RechargeRebateData.ResponsePayDiamond) {
          return mergeFrom((protocol.RechargeRebateData.ResponsePayDiamond)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RechargeRebateData.ResponsePayDiamond other) {
        if (other == protocol.RechargeRebateData.ResponsePayDiamond.getDefaultInstance()) return this;
        if (other.hasDiamondNum()) {
          setDiamondNum(other.getDiamondNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasDiamondNum()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RechargeRebateData.ResponsePayDiamond parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RechargeRebateData.ResponsePayDiamond) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 diamond_num = 1;
      private long diamondNum_ ;
      /**
       * <code>required int64 diamond_num = 1;</code>
       */
      public boolean hasDiamondNum() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 diamond_num = 1;</code>
       */
      public long getDiamondNum() {
        return diamondNum_;
      }
      /**
       * <code>required int64 diamond_num = 1;</code>
       */
      public Builder setDiamondNum(long value) {
        bitField0_ |= 0x00000001;
        diamondNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 diamond_num = 1;</code>
       */
      public Builder clearDiamondNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        diamondNum_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePayDiamond)
    }

    static {
      defaultInstance = new ResponsePayDiamond(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePayDiamond)
  }

  public interface RequestRechargeRebateRewardOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 curRewardId = 1;
    /**
     * <code>required int32 curRewardId = 1;</code>
     */
    boolean hasCurRewardId();
    /**
     * <code>required int32 curRewardId = 1;</code>
     */
    int getCurRewardId();
  }
  /**
   * Protobuf type {@code protocol.RequestRechargeRebateReward}
   *
   * <pre>
   * 1342 自增任务id
   * </pre>
   */
  public static final class RequestRechargeRebateReward extends
      com.google.protobuf.GeneratedMessage
      implements RequestRechargeRebateRewardOrBuilder {
    // Use RequestRechargeRebateReward.newBuilder() to construct.
    private RequestRechargeRebateReward(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestRechargeRebateReward(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestRechargeRebateReward defaultInstance;
    public static RequestRechargeRebateReward getDefaultInstance() {
      return defaultInstance;
    }

    public RequestRechargeRebateReward getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestRechargeRebateReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              curRewardId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RechargeRebateData.internal_static_protocol_RequestRechargeRebateReward_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RechargeRebateData.internal_static_protocol_RequestRechargeRebateReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RechargeRebateData.RequestRechargeRebateReward.class, protocol.RechargeRebateData.RequestRechargeRebateReward.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestRechargeRebateReward> PARSER =
        new com.google.protobuf.AbstractParser<RequestRechargeRebateReward>() {
      public RequestRechargeRebateReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestRechargeRebateReward(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestRechargeRebateReward> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 curRewardId = 1;
    public static final int CURREWARDID_FIELD_NUMBER = 1;
    private int curRewardId_;
    /**
     * <code>required int32 curRewardId = 1;</code>
     */
    public boolean hasCurRewardId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 curRewardId = 1;</code>
     */
    public int getCurRewardId() {
      return curRewardId_;
    }

    private void initFields() {
      curRewardId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasCurRewardId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, curRewardId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, curRewardId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RechargeRebateData.RequestRechargeRebateReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RechargeRebateData.RequestRechargeRebateReward prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestRechargeRebateReward}
     *
     * <pre>
     * 1342 自增任务id
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RechargeRebateData.RequestRechargeRebateRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RechargeRebateData.internal_static_protocol_RequestRechargeRebateReward_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RechargeRebateData.internal_static_protocol_RequestRechargeRebateReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RechargeRebateData.RequestRechargeRebateReward.class, protocol.RechargeRebateData.RequestRechargeRebateReward.Builder.class);
      }

      // Construct using protocol.RechargeRebateData.RequestRechargeRebateReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        curRewardId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RechargeRebateData.internal_static_protocol_RequestRechargeRebateReward_descriptor;
      }

      public protocol.RechargeRebateData.RequestRechargeRebateReward getDefaultInstanceForType() {
        return protocol.RechargeRebateData.RequestRechargeRebateReward.getDefaultInstance();
      }

      public protocol.RechargeRebateData.RequestRechargeRebateReward build() {
        protocol.RechargeRebateData.RequestRechargeRebateReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RechargeRebateData.RequestRechargeRebateReward buildPartial() {
        protocol.RechargeRebateData.RequestRechargeRebateReward result = new protocol.RechargeRebateData.RequestRechargeRebateReward(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.curRewardId_ = curRewardId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RechargeRebateData.RequestRechargeRebateReward) {
          return mergeFrom((protocol.RechargeRebateData.RequestRechargeRebateReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RechargeRebateData.RequestRechargeRebateReward other) {
        if (other == protocol.RechargeRebateData.RequestRechargeRebateReward.getDefaultInstance()) return this;
        if (other.hasCurRewardId()) {
          setCurRewardId(other.getCurRewardId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasCurRewardId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RechargeRebateData.RequestRechargeRebateReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RechargeRebateData.RequestRechargeRebateReward) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 curRewardId = 1;
      private int curRewardId_ ;
      /**
       * <code>required int32 curRewardId = 1;</code>
       */
      public boolean hasCurRewardId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 curRewardId = 1;</code>
       */
      public int getCurRewardId() {
        return curRewardId_;
      }
      /**
       * <code>required int32 curRewardId = 1;</code>
       */
      public Builder setCurRewardId(int value) {
        bitField0_ |= 0x00000001;
        curRewardId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 curRewardId = 1;</code>
       */
      public Builder clearCurRewardId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        curRewardId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestRechargeRebateReward)
    }

    static {
      defaultInstance = new RequestRechargeRebateReward(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestRechargeRebateReward)
  }

  public interface ResponseRechargeRebateRewardOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 rewardId = 1;
    /**
     * <code>required int32 rewardId = 1;</code>
     */
    boolean hasRewardId();
    /**
     * <code>required int32 rewardId = 1;</code>
     */
    int getRewardId();
  }
  /**
   * Protobuf type {@code protocol.ResponseRechargeRebateReward}
   *
   * <pre>
   * 2342 领取奖励的任务Id
   * </pre>
   */
  public static final class ResponseRechargeRebateReward extends
      com.google.protobuf.GeneratedMessage
      implements ResponseRechargeRebateRewardOrBuilder {
    // Use ResponseRechargeRebateReward.newBuilder() to construct.
    private ResponseRechargeRebateReward(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseRechargeRebateReward(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseRechargeRebateReward defaultInstance;
    public static ResponseRechargeRebateReward getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseRechargeRebateReward getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseRechargeRebateReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              rewardId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.RechargeRebateData.internal_static_protocol_ResponseRechargeRebateReward_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.RechargeRebateData.internal_static_protocol_ResponseRechargeRebateReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.RechargeRebateData.ResponseRechargeRebateReward.class, protocol.RechargeRebateData.ResponseRechargeRebateReward.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseRechargeRebateReward> PARSER =
        new com.google.protobuf.AbstractParser<ResponseRechargeRebateReward>() {
      public ResponseRechargeRebateReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseRechargeRebateReward(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseRechargeRebateReward> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 rewardId = 1;
    public static final int REWARDID_FIELD_NUMBER = 1;
    private int rewardId_;
    /**
     * <code>required int32 rewardId = 1;</code>
     */
    public boolean hasRewardId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 rewardId = 1;</code>
     */
    public int getRewardId() {
      return rewardId_;
    }

    private void initFields() {
      rewardId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasRewardId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, rewardId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, rewardId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.RechargeRebateData.ResponseRechargeRebateReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.RechargeRebateData.ResponseRechargeRebateReward prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseRechargeRebateReward}
     *
     * <pre>
     * 2342 领取奖励的任务Id
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.RechargeRebateData.ResponseRechargeRebateRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.RechargeRebateData.internal_static_protocol_ResponseRechargeRebateReward_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.RechargeRebateData.internal_static_protocol_ResponseRechargeRebateReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.RechargeRebateData.ResponseRechargeRebateReward.class, protocol.RechargeRebateData.ResponseRechargeRebateReward.Builder.class);
      }

      // Construct using protocol.RechargeRebateData.ResponseRechargeRebateReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        rewardId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.RechargeRebateData.internal_static_protocol_ResponseRechargeRebateReward_descriptor;
      }

      public protocol.RechargeRebateData.ResponseRechargeRebateReward getDefaultInstanceForType() {
        return protocol.RechargeRebateData.ResponseRechargeRebateReward.getDefaultInstance();
      }

      public protocol.RechargeRebateData.ResponseRechargeRebateReward build() {
        protocol.RechargeRebateData.ResponseRechargeRebateReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.RechargeRebateData.ResponseRechargeRebateReward buildPartial() {
        protocol.RechargeRebateData.ResponseRechargeRebateReward result = new protocol.RechargeRebateData.ResponseRechargeRebateReward(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.rewardId_ = rewardId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.RechargeRebateData.ResponseRechargeRebateReward) {
          return mergeFrom((protocol.RechargeRebateData.ResponseRechargeRebateReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.RechargeRebateData.ResponseRechargeRebateReward other) {
        if (other == protocol.RechargeRebateData.ResponseRechargeRebateReward.getDefaultInstance()) return this;
        if (other.hasRewardId()) {
          setRewardId(other.getRewardId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasRewardId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.RechargeRebateData.ResponseRechargeRebateReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.RechargeRebateData.ResponseRechargeRebateReward) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 rewardId = 1;
      private int rewardId_ ;
      /**
       * <code>required int32 rewardId = 1;</code>
       */
      public boolean hasRewardId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 rewardId = 1;</code>
       */
      public int getRewardId() {
        return rewardId_;
      }
      /**
       * <code>required int32 rewardId = 1;</code>
       */
      public Builder setRewardId(int value) {
        bitField0_ |= 0x00000001;
        rewardId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 rewardId = 1;</code>
       */
      public Builder clearRewardId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rewardId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseRechargeRebateReward)
    }

    static {
      defaultInstance = new ResponseRechargeRebateReward(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseRechargeRebateReward)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePayDiamond_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePayDiamond_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestRechargeRebateReward_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestRechargeRebateReward_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseRechargeRebateReward_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseRechargeRebateReward_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024rechargerebate.proto\022\010protocol\")\n\022Resp" +
      "onsePayDiamond\022\023\n\013diamond_num\030\001 \002(\003\"2\n\033R" +
      "equestRechargeRebateReward\022\023\n\013curRewardI" +
      "d\030\001 \002(\005\"0\n\034ResponseRechargeRebateReward\022" +
      "\020\n\010rewardId\030\001 \002(\005B\024B\022RechargeRebateData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_ResponsePayDiamond_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_ResponsePayDiamond_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePayDiamond_descriptor,
              new java.lang.String[] { "DiamondNum", });
          internal_static_protocol_RequestRechargeRebateReward_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestRechargeRebateReward_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestRechargeRebateReward_descriptor,
              new java.lang.String[] { "CurRewardId", });
          internal_static_protocol_ResponseRechargeRebateReward_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ResponseRechargeRebateReward_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseRechargeRebateReward_descriptor,
              new java.lang.String[] { "RewardId", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
