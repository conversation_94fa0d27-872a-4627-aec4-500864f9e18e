package module.item;

import com.googlecode.protobuf.format.JsonFormat;
import entities.ItemEntity;
import manager.ReportManager;
import model.CommonInfo;
import model.ItemInfo;
import protocol.ItemData;
import protocol.PetData;
import protocol.ProtoData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ItemUtils {
    public static ItemInfo getItemInfo(String config) {
        int itemId = Integer.parseInt(config.split(",")[0]);
        int num = Integer.parseInt(config.split(",")[1]);
        ItemInfo itemInfo = new ItemInfo();
        itemInfo.setNum(num);
        itemInfo.setId(itemId);
        return itemInfo;
    }

    public static ItemData.Item getItemData(int itemId, double num) {
        ItemData.Item.Builder item = ItemData.Item.newBuilder();
        item.setNum(num);
        item.setId(itemId);
        return item.build();
    }

    public static Map<Integer, ItemInfo> mergeItem(List<ItemInfo> list) {
        Map<Integer, ItemInfo> map = new HashMap<Integer, ItemInfo>();
        for (ItemInfo itemInfo : list) {
            int itemId = itemInfo.getId();
            ///    /// System.err.println(itemId+"itemId!!!!!!!!!!!!");
            ItemInfo itemInfoFromMap = map.get(itemId);
            if (itemInfoFromMap == null) {
                map.put(itemId, itemInfo);
            } else {
                itemInfoFromMap.setNum(itemInfoFromMap.getNum() + itemInfo.getNum());
            }
        }
        return map;
    }

    public static ItemData.Item entityToPBData(ItemEntity entity) {
        return getItemData(entity.getItemid(), entity.getItemnum());
    }

    public static void updatePlayerItems(String uid, List<ItemData.Item> itemList, boolean isAdd) {
        try {
            ItemData.ReportItem.Builder reportItem = ItemData.ReportItem.newBuilder();
            for (ItemData.Item item : itemList) {
                ///       /// System.err.println(item+"item!!!!!"+isAdd);
                double value = 0;
                int id = item.getId();
                long num = (long) item.getNum();
                if (isAdd) {
                    value = ItemDao.getInstance().updateItemInfo(uid, id, num);
                } else {
                    value = ItemDao.getInstance().updateItemInfo(uid, id, -num);
                }
                ItemData.Item itemData = getItemData(id, value);
                reportItem.addItem(itemData);
            }
            ///  /// System.err.println("物品"+JsonFormat.printToString(reportItem.build()));
            //  ReportManager.reportInfo(uid,ProtoData.SToC.REPORTITEM_VALUE,reportItem.build().toByteArray());
            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportItem.build().toByteArray());
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

   /* public  static  void  updatePlayerItems(String uid, int id, List<CommonInfo> num, boolean isAdd){
        /// System.err.println("id:"+id+"~~nums"+num+"isadd"+isAdd);
        ItemData.ReportItem.Builder reportItem=ItemData.ReportItem.newBuilder();
            double value=0;
            if(isAdd){
                value= ItemDao.getInstance().updateItemInfo(uid,id,num);
            }else{
                value= ItemDao.getInstance().updateItemInfo(uid,id,-num);
            }
            ItemData.Item itemData=getItemData(id,value);
            reportItem.addItem(itemData);
        /// System.err.println("物品"+JsonFormat.printToString(reportItem.build()));
        ReportManager.reportInfo(uid,ProtoData.SToC.REPORTITEM_VALUE,reportItem.build().toByteArray());
    }*/

    public static void updatePlayerItems(String uid, int id, int num, boolean isAdd) {
        ItemData.ReportItem.Builder reportItem = ItemData.ReportItem.newBuilder();
        double value = 0;
        if (isAdd) {
            value = ItemDao.getInstance().updateItemInfo(uid, id, num);
        } else {
            value = ItemDao.getInstance().updateItemInfo(uid, id, -num);
        }
        ItemData.Item itemData = getItemData(id, value);


        reportItem.addItem(itemData);
        ///  /// System.err.println("物品"+JsonFormat.printToString(reportItem.build()));
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportItem.build().toByteArray());
    }
}
