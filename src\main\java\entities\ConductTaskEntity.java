package entities;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Objects;

/**
 * Created by nara on 2018/4/26.
 */
@Entity
@Table(name = "conduct_task", schema = "", catalog = "super_star_fruit")
public class ConductTaskEntity {
    private int id;
    private String uid;
    private String time;
    private byte[] conductTask;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "time")
    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    @Basic
    @Column(name = "conduct_task")
    public byte[] getConductTask() {
        return conductTask;
    }

    public void setConductTask(byte[] conductTask) {
        this.conductTask = conductTask;
    }

    @Override
    public String toString() {
        return "ConductTaskEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", time='" + time + '\'' +
                ", conductTask=" + Arrays.toString(conductTask) +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ConductTaskEntity that = (ConductTaskEntity) o;
        return getId() == that.getId() &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getTime(), that.getTime()) &&
                Arrays.equals(getConductTask(), that.getConductTask());
    }

    @Override
    public int hashCode() {

        int result = Objects.hash(getId(), getUid(), getTime());
        result = 31 * result + Arrays.hashCode(getConductTask());
        return result;
    }
}
