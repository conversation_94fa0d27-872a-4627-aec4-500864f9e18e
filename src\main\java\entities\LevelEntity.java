package entities;

import javax.persistence.*;


    @Entity
    @Table(name = "level", schema = "", catalog = "super_star_fruit")
    public class LevelEntity {
        private int id;



        private int levelid;
        private int layerid;
        private String uid;
        @Id
        @Basic
        @Column(name = "id")
        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }
        @Basic
        @Column(name = "Levelid")
        public int getLevelid() {
            return levelid;
        }

        public void setLevelid(int levelid) {
            this.levelid = levelid;
        }
        @Basic
        @Column(name = "Layerid")
        public int getLayerid() {
            return layerid;
        }

        public void setLayerid(int layerid) {
            this.layerid = layerid;
        }

        @Override
        public String toString() {
            return "LevelEntity{" +
                    "id=" + id +
                    ", levelid=" + levelid +
                    ", layerid=" + layerid +
                    ", uid='" + uid + '\'' +
                    '}';
        }
        @Basic
        @Column(name = "uid")
        public String getUid() {
            return uid;
        }

        public void setUid(String uid) {
            this.uid = uid;
        }
    }

