package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "furnitureshop", schema = "", catalog = "super_star_fruit")
public class FurnitureShopEntity {
    private int id;
    private String uid;
    private int furnitureId;
    private int nums;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }


    @Basic
    @Column(name = "furnitureId")
    public int getFurnitureId() {
        return furnitureId;
    }

    public void setFurnitureId(int furnitureId) {
        this.furnitureId = furnitureId;
    }

    @Basic
    @Column(name = "nums")
    public int getNums() {
        return nums;
    }

    public void setNums(int nums) {
        this.nums = nums;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FurnitureShopEntity that = (FurnitureShopEntity) o;
        return getId() == that.getId() &&
                getFurnitureId() == that.getFurnitureId() &&
                getNums() == that.getNums() &&
                Objects.equals(getUid(), that.getUid());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getUid(), getFurnitureId(), getNums());
    }

    @Override
    public String toString() {
        return "FurnitureShopEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", furnitureId=" + furnitureId +
                ", nums=" + nums +
                '}';
    }
}
