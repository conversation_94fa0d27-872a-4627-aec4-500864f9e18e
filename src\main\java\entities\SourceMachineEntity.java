package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "sourcemachine", schema = "", catalog = "super_star_fruit")
public class SourceMachineEntity {
    private int id;
    private String uid;
    private int type;
    private int itemId;
    private String countdown;

    @Id
    @Column(name = "id")
    public int getId() {

        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "countdown")
    public String getCountdown() {
        return countdown;
    }

    public void setCountdown(String countdown) {
        this.countdown = countdown;
    }


    @Basic
    @Column(name = "itemId")
    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    @Override
    public String toString() {
        return "SourceMachineEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", type=" + type +
                ", itemId=" + itemId +
                ", countdown='" + countdown + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SourceMachineEntity that = (SourceMachineEntity) o;
        return getId() == that.getId() &&
                getType() == that.getType() &&
                getItemId() == that.getItemId() &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getCountdown(), that.getCountdown());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getUid(), getType(), getItemId(), getCountdown());
    }

}
