package module.mission;

import com.googlecode.protobuf.format.JsonFormat;
import common.SuperConfig;
import entities.*;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import model.*;
import module.friend.FriendDao;
import module.friend.IFriend;
import module.item.IItem;
import module.item.ItemDao;
import module.login.ILogin;
import module.login.LoginDao;
import module.robot.ranName;
import module.task.ITask;
import module.task.TaskDao;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import protocol.*;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import server.SuperProtocol;
import server.SuperServer;
import server.SuperServerHandler;
import utils.MyUtils;

import javax.persistence.criteria.CriteriaBuilder;
import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Created by nara on 2017/11/30.
 */
public class MissionDao implements IMission {
    private static Logger log = LoggerFactory.getLogger(MissionDao.class);
    private static MissionDao inst = null;
    public static AtomicInteger roomIndex=new AtomicInteger();
    public static int roomNums;
    public static Map<Integer, String> battleRoomMap ;//匹配队列
    public static  List<String>  userMacthPool;
    public static MissionDao getInstance() {
        if (inst == null) {
            inst = new MissionDao();
        }
        return inst;
    }

    public static List<Integer> endlessList = null;


//    static {
//        Redis jedis = Redis.getInstance();
//        MissionDao.endlessList = new ArrayList<Integer>();
 //   Iterator<String> assortIter = jedis.keys(SuperConfig.REDIS_EXCEL_ASSORT + "*").iterator();
//        while (assortIter.hasNext()) {
//            String key = assortIter.next();
//            int endless = Integer.parseInt(jedis.hget(key, "endless"));
//            if (endless == 1) {
//                int id = Integer.parseInt(key.split(":")[1]);
//                MissionDao.endlessList.add(id);
//            }
//        }
//    }
//    static {
//        Redis jedis = Redis.getInstance();
//        MissionDao.endlessList = new ArrayList<Integer>();
//        Iterator<String> assortIter = jedis.keys(SuperConfig.REDIS_EXCEL_ASSORT + "*").iterator();
//        while (assortIter.hasNext()) {
//            String key = assortIter.next();
//            int endless = Integer.parseInt(jedis.hget(key, "endless"));
//            if (endless == 1) {
//                int id = Integer.parseInt(key.split(":")[1]);
//                MissionDao.endlessList.add(id);
//            }
//        }
//    }
    static {
        try {
            Redis jedis = Redis.getInstance();
            MissionDao.endlessList = new ArrayList<Integer>();
            Iterator<String> assortIter = jedis.keys(SuperConfig.REDIS_EXCEL_ASSORT + "*").iterator();
            while (assortIter.hasNext()) {
                String key = assortIter.next();
                int endless = Integer.parseInt(jedis.hget(key, "endless"));
                if (endless == 1) {
                    int id = Integer.parseInt(key.split(":")[1]);
                    MissionDao.endlessList.add(id);
                }
            }
        }catch(Exception e) {
//            /// System.out.println("MissionDao Initialization Block Exception");
        }
    }


    public synchronized static void initMap() {
//        Redis jedis = Redis.getInstance();
//        Iterator<String> iterator = jedis.keys("missioninfo*").iterator();
//        while (iterator.hasNext()){
//            String key = iterator.next();
//            jedis.del(key);
//        }
        for (int i = 1; i <= 80; i++) {
            for (int j = 1; j <= SuperConfig.INITMISSIONNUM; j++) {
//                if (TimerHandler.mapType == 1){
                createMap(i, j + "");
//                }else {
//                    createMap(i, (j+SuperConfig.INITMISSIONNUM)+"");
//                }
            }
        }
//        for (int i = 200001 ; i<=200002 ; i++){
//            for (int j = 1 ; j<=SuperConfig.INITMISSIONNUM ;j++){
//                createMap(i,j+"");
//            }
//        }
        try {
            Thread.sleep(5000);
        } catch (Exception e) {
//            /// System.out.println(e.getMessage());
        }
    }

    public static void initOneMap() {
        Scanner sc = new Scanner(System.in);
//        /// System.out.println("请输入初始化MissionId，以“|”分割多个：");
        String missionIds = sc.next();
//        /// System.out.println("请确认是否为>>>" + missionIds);
        Scanner sc2 = new Scanner(System.in);
//        /// System.out.println("确认请按 1");
        int val = sc2.nextInt();
        try {
            if (val == 1) {
                Redis jedis = Redis.getInstance();
                String[] missionIdStrs = missionIds.split("\\|");
                for (int i = 0; i < missionIdStrs.length; i++) {
                    int missionId = Integer.parseInt(missionIdStrs[i]);
                    Iterator<String> iterator = jedis.keys("missioninfo:" + missionId + "&*").iterator();
                    while (iterator.hasNext()) {
                        String key = iterator.next();
                        jedis.del(key);
                    }
                    for (int j = 1; j <= SuperConfig.INITMISSIONNUM; j++) {
                        createMap(missionId, j + "");
                    }
                }
//                /// System.out.println("all ok!");
            }
        } catch (Exception e) {
            e.printStackTrace();
//            /// System.out.println("策划你干了啥？？！错了！错了！错了！");
        } finally {
            try {
                Thread.sleep(5000);
            } catch (Exception e) {

            }
        }
    }

    private static void createMap(int missionId, String uid) {
        List<MissionData.BigBall> ballList = new ArrayList<MissionData.BigBall>();
        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MISSION, missionId);
        int mode = Integer.parseInt(missionMap.get("mode"));
        int totalLen = (int) (Float.parseFloat(missionMap.get("length")) * 100);
        int ballNum = Integer.parseInt(missionMap.get("ball_num"));
        String[] ballIds = missionMap.get("big_ball").split("\\|");
        String[] integrals = missionMap.get("diff_integral").split("\\|");
//        int totalIntegral = Integer.parseInt(missionMap.get("total_score"));
        int buffBallId = Integer.parseInt(missionMap.get("buff_ball"));
        int buffBallNum = Integer.parseInt(missionMap.get("buff_ball_num"));
        String[] skillIds = missionMap.get("buff_skill").split("\\|");
        int spacing = Integer.parseInt(missionMap.get("spacing"));//技能球间距
        String[] posYStrs = SuperConfig.getInitBallPosYRange();
        int posYLow = (int) (Float.parseFloat(posYStrs[0]) * 100);
        int posYMax = (int) (Float.parseFloat(posYStrs[1]) * 100);
        int surplusMin = (int) (Float.parseFloat(missionMap.get("surplus_min")) * 100);
        int surplusMax = (int) (Float.parseFloat(missionMap.get("surplus_max")) * 100);
        int totalBall = ballNum + buffBallNum;
        int minRange = (int) (Float.parseFloat(missionMap.get("min_range")) * 100);
        int rangeNum = Integer.parseInt(missionMap.get("range_num"));
        int rangeTime = Integer.parseInt(missionMap.get("range_time"));
        int rangeNeed = rangeNum * rangeTime;
        if (rangeNeed > totalBall) {
//            /// System.out.println("rangeNeed error !!! missionId:" + missionId);
            return;
        }
        int otherLen = totalLen - minRange * rangeNeed - 800;
        int initX = 0;
        List<PointInfo> addPointList = new LinkedList<PointInfo>();
        for (int i = 0; i < totalBall - rangeNeed - 1; i++) {
            int addX = 0;

            if ((totalBall - i + 1) * surplusMin >= (otherLen - initX)) {
                initX += surplusMin;
                addX = surplusMin;
            } else {
                int randX = (int) (surplusMin + Math.random() * (surplusMax - surplusMin + 1));
                if (initX + surplusMax > otherLen) {
                    initX = otherLen;
                    addX = otherLen - surplusMax;
                } else {
                    initX += randX;
                    addX = randX;
                }
            }

            int randY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
            PointInfo pointInfo = new PointInfo();
            pointInfo.setX(addX);
            pointInfo.setY(randY);
            addPointList.add(pointInfo);
        }

        List<BigBallInfo> bigBallInfoList = null;
        if (mode == 1 || mode == 3) {
            bigBallInfoList = createMissionBallMode1(totalBall, ballIds, posYMax, posYLow, minRange, rangeNeed, rangeNum,
                    ballNum, buffBallNum, integrals, skillIds, buffBallId, spacing, addPointList);
        } else if (mode == 2) {
            String[] proportion = missionMap.get("proportion").split("\\|");
            String[] diff_integral = missionMap.get("diff_integral2").split("\\|");
            bigBallInfoList = createMissionBallMode2(totalBall, ballIds, posYMax, posYLow, minRange, rangeNeed, rangeNum,
                    ballNum, buffBallNum, integrals, skillIds, buffBallId, spacing, addPointList, proportion, diff_integral);
        }
        if (bigBallInfoList == null) {
//            /// System.out.println("create bigBallList error !!! missionId:" + missionId);
            return;
        }


        //最后加一个球防止时间到没球
        if (true) {
            BigBallInfo bigBallInfo = new BigBallInfo();
            int pointX = bigBallInfoList.get(bigBallInfoList.size() - 1).getPoint().getX() + surplusMax;
            int randY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
            int ballId_Index = (int) (1 + Math.random() * (ballIds.length - 1 + 1));
            bigBallInfo.setBigBallId(Integer.parseInt(ballIds[ballId_Index - 1]));
            bigBallInfo.getPoint().setX(pointX);
            bigBallInfo.getPoint().setY(randY);
            bigBallInfo.setIsSpeed(false);
            bigBallInfo.setIntegral(Integer.parseInt(integrals[0]));
            bigBallInfoList.add(bigBallInfo);
        }

        int spNum = Integer.parseInt(missionMap.get("sp_num"));
        if (spNum > 0) {
            String[] spSkillIds = missionMap.get("speed2_skill").split("\\|");
            if (spNum < spSkillIds.length) {
//                /// System.out.println("spNum error !!! missionId:" + missionId);
                return;
            }
            int spIntergral = Integer.parseInt(missionMap.get("sp_score"));
            List<Integer> skillList2 = new LinkedList<Integer>();
            for (int i = 0; i < spSkillIds.length; i++) {
                skillList2.add(Integer.parseInt(spSkillIds[i]));
            }
            if (spNum > skillList2.size()) {
                for (int i = skillList2.size(); i < spNum; i++) {
                    skillList2.add(0);
                }
            }
            int spStartX = totalLen * 1 / 3;
            int interval = (totalLen - spStartX) / spNum;
            for (int i = 0; i < spNum; i++) {
                BigBallInfo bigBallInfo = new BigBallInfo();
                int randX = (int) (spStartX + Math.random() * ((totalLen - (spNum - i - 1) * interval) - spStartX + 1));
                spStartX += randX;
                int randY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                int ballId_Index = (int) (1 + Math.random() * (ballIds.length - 1 + 1));
                bigBallInfo.setBigBallId(Integer.parseInt(ballIds[ballId_Index - 1]));
                bigBallInfo.getPoint().setX(randX);
                bigBallInfo.getPoint().setY(randY);
                bigBallInfo.setIsSpeed(true);
                bigBallInfo.setIntegral(spIntergral);
                int inRand = (int) (1 + Math.random() * (skillList2.size() - 1 + 1));
                int val = skillList2.remove(inRand - 1);
                bigBallInfo.setSkillId(val);
                if (val == 1011) {//阻碍球在下半区域
                    inRand = (int) (posYLow + Math.random() * ((posYMax - posYLow) / 2 - posYLow + 1));
                    bigBallInfo.getPoint().setY(inRand);
                }
                bigBallInfoList.add(bigBallInfo);
            }
        }

        for (int i = 0; i < bigBallInfoList.size(); i++) {
            BigBallInfo bigBallInfo = bigBallInfoList.get(i);
            MissionData.BigBall.Builder bigBallBu = MissionData.BigBall.newBuilder();
            MissionData.Buff.Builder buffBu = MissionData.Buff.newBuilder();
            MissionData.Body.Builder ballBu = MissionData.Body.newBuilder();
            MissionData.Point.Builder pointBu = MissionData.Point.newBuilder();
            pointBu.setX(bigBallInfo.getPoint().getX());
            pointBu.setY(bigBallInfo.getPoint().getY());
            ballBu.setP(pointBu);
            ballBu.setId(bigBallInfo.getBigBallId());
            buffBu.setBall(ballBu);
            buffBu.setIsSpeed(bigBallInfo.isSpeed());
            bigBallBu.setBall(buffBu);
            bigBallBu.setIntegral(bigBallInfo.getIntegral());
            bigBallBu.setSkillId(bigBallInfo.getSkillId());
            ballList.add(bigBallBu.build());
        }
        missionToRedis(missionId, uid, ballList);
    }

    private static List<BigBallInfo> createMissionBallMode1(int totalBall, String[] ballIds, int posYMax, int posYLow, int minRange, int rangeNeed, int rangeNum, int ballNum,
                                                            int buffBallNum, String[] integrals, String[] skillIds, int buffBallId, int spacing, List<PointInfo> addPointList) {
        List<BigBallInfo> bigBallInfoList = new ArrayList<BigBallInfo>();
        int initX = 0;
        int nowRange = 0;
        for (int i = 0; i < totalBall; i++) {
            int ballId_Index = (int) (1 + Math.random() * (ballIds.length - 1 + 1));
            BigBallInfo bigBallInfo = new BigBallInfo();
            int pointY = 0;
            if (i == 0) {
                initX += 800;
                pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
            } else {
                if (nowRange > 0) {
                    initX += minRange;
                    pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                    nowRange--;
                    rangeNeed--;
                } else if (rangeNeed == (totalBall - i)) {
                    initX += minRange;
                    pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                    rangeNeed--;
                } else if (rangeNeed > 0) {
                    int rand = (int) (1 + Math.random() * (3 - 1 + 1));
                    if (rand == 1) {
                        nowRange = rangeNum;
                        initX += minRange;
                        pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                        nowRange--;
                        rangeNeed--;
                    } else {
                        int index = (int) (1 + Math.random() * (addPointList.size() - 1 + 1));
                        PointInfo pointInfo = addPointList.remove(index - 1);
                        initX += pointInfo.getX();
                        pointY = pointInfo.getY();
                    }
                } else {
                    int index = (int) (1 + Math.random() * (addPointList.size() - 1 + 1));
                    PointInfo pointInfo = addPointList.remove(index - 1);
                    initX += pointInfo.getX();
                    pointY = pointInfo.getY();
                }
            }
            bigBallInfo.setBigBallId(Integer.parseInt(ballIds[ballId_Index - 1]));
            bigBallInfo.getPoint().setX(initX);
            bigBallInfo.getPoint().setY(pointY);
            bigBallInfo.setIsSpeed(false);
            bigBallInfoList.add(i, bigBallInfo);
        }

        int length = integrals.length;
        List<Integer> integralList = new LinkedList<Integer>();
        if (length == 1) {
            int num = Integer.parseInt(integrals[0]);
            for (int i = 0; i < ballNum; i++) {
                integralList.add(num);
            }
        } else {
            int bi = 0;
            for (int i = 0; i < ballNum; i++) {
                int num = Integer.parseInt(integrals[bi]);
                integralList.add(num);
                bi++;
                if (bi >= length) {
                    bi = 0;
                }
            }
        }

        if (buffBallNum != 0 && buffBallNum < skillIds.length) {
//            /// System.out.println("buffBallNum OR skillIds.length error !!!");
            return null;
        }
        List<Integer> skillList = new LinkedList<Integer>();
        for (int i = 0; i < skillIds.length; i++) {
            int n = Integer.parseInt(skillIds[i]);
            if (n != 0) {
                skillList.add(n);
            }
        }
        if (buffBallNum > skillList.size()) {
            for (int i = skillList.size(); i < buffBallNum; i++) {
                skillList.add(0);
            }
        }

        if (spacing != 0) {
            int skillMax = (bigBallInfoList.size() / spacing);
            if (skillMax < skillList.size()) {
//                /// System.out.println("spacing error !!!");
                return null;
            }
        }

        if ((bigBallInfoList.size() - buffBallNum) != integralList.size()) {
//            /// System.out.println("????????????");
        }

        int wi = 0;
        for (int i = 0; i < bigBallInfoList.size(); i++) {
            BigBallInfo bigBallInfo = bigBallInfoList.get(i);
            if (skillList.size() > 0 && wi <= 0) {
                int rand = 30;
                if (((bigBallInfoList.size() - i) / spacing) > skillList.size()) {
                    rand = (int) (1 + Math.random() * (100 - 1 + 1));
                }

                if (rand <= 30) {
                    wi = spacing;
                    int inRand = (int) (1 + Math.random() * (skillList.size() - 1 + 1));
                    int val = skillList.remove(inRand - 1);
                    bigBallInfo.setSkillId(val);
                    if (val == 1011) {//阻碍球在下半区域
                        inRand = (int) (posYLow + Math.random() * ((posYMax + posYLow) / 2 - posYLow + 1));
                        bigBallInfo.getPoint().setY(inRand);
                    }
                    if (buffBallNum > 0) {
                        bigBallInfo.setIsBuffBall(true);
                        bigBallInfo.setBigBallId(buffBallId);
                    }
                } else {
                    wi--;
                }

                if (buffBallNum <= 0) {
                    int inRand = (int) (1 + Math.random() * (integralList.size() - 1 + 1));
                    int val = integralList.remove(inRand - 1);
                    bigBallInfo.setIntegral(val);
                }
            } else {
                wi--;
                int inRand = (int) (1 + Math.random() * (integralList.size() - 1 + 1));
                int val = integralList.remove(inRand - 1);
                bigBallInfo.setIntegral(val);
            }
        }
        return bigBallInfoList;
    }

    private static List<BigBallInfo> createMissionBallMode2(int totalBall, String[] ballIds, int posYMax, int posYLow, int minRange, int rangeNeed, int rangeNum, int ballNum,
                                                            int buffBallNum, String[] integrals, String[] skillIds, int buffBallId, int spacing, List<PointInfo> addPointList,
                                                            String[] proportion, String[] diff_integral) {
        List<BigBallInfo> bigBallInfoList = new ArrayList<BigBallInfo>();
        int initX = 0;
        int nowRange = 0;
        int firstPro = Integer.parseInt(proportion[0]);
        int secondPro = Integer.parseInt(proportion[1]);
        int firstBall = totalBall * firstPro / 100;
        int secondBall = totalBall * secondPro / 100;
        int secondBallId = Integer.parseInt(ballIds[1]);
        for (int i = 0; i < totalBall; i++) {
            BigBallInfo bigBallInfo = new BigBallInfo();

            int ballId_Index = 0;
            if (firstBall <= 0) {
                ballId_Index = 2;
            } else if (secondBall <= 0) {
                ballId_Index = 1;
            } else {
                ballId_Index = (int) (1 + Math.random() * (ballIds.length - 1 + 1));
            }
            if (ballId_Index == 1) {
                firstBall--;
            } else {
                secondBall--;
            }

            int pointY = 0;
            if (i == 0) {
                initX += 800;
                pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
            } else {
                if (nowRange > 0) {
                    initX += minRange;
                    pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                    nowRange--;
                    rangeNeed--;
                } else if (rangeNeed == (totalBall - i)) {
                    initX += minRange;
                    pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                    rangeNeed--;
                } else if (rangeNeed > 0) {
                    int rand = (int) (1 + Math.random() * (3 - 1 + 1));
                    if (rand == 1) {
                        nowRange = rangeNum;
                        initX += minRange;
                        pointY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
                        nowRange--;
                        rangeNeed--;
                    } else {
                        int index = (int) (1 + Math.random() * (addPointList.size() - 1 + 1));
                        PointInfo pointInfo = addPointList.remove(index - 1);
                        initX += pointInfo.getX();
                        pointY = pointInfo.getY();
                    }
                } else {
                    int index = (int) (1 + Math.random() * (addPointList.size() - 1 + 1));
                    PointInfo pointInfo = addPointList.remove(index - 1);
                    initX += pointInfo.getX();
                    pointY = pointInfo.getY();
                }
            }
            bigBallInfo.setBigBallId(Integer.parseInt(ballIds[ballId_Index - 1]));
            bigBallInfo.getPoint().setX(initX);
            bigBallInfo.getPoint().setY(pointY);
            bigBallInfo.setIsSpeed(false);
            bigBallInfoList.add(i, bigBallInfo);
        }
        firstBall = totalBall * firstPro / 100;
        int length = integrals.length;
        List<Integer> integralList = new LinkedList<Integer>();
        int bi = 0;
        for (int i = 0; i < firstBall; i++) {
            int num = Integer.parseInt(integrals[bi]);
            integralList.add(num);
            bi++;
            if (bi >= length) {
                bi = 0;
            }
        }

        if (buffBallNum != 0 && buffBallNum < skillIds.length) {
//            /// System.out.println("buffBallNum OR skillIds.length error !!!");
            return null;
        }
        List<Integer> skillList = new LinkedList<Integer>();
        for (int i = 0; i < skillIds.length; i++) {
            int n = Integer.parseInt(skillIds[i]);
            if (n != 0) {
                skillList.add(n);
            }
        }
        if (buffBallNum > skillList.size()) {
            for (int i = skillList.size(); i < buffBallNum; i++) {
                skillList.add(0);
            }
        }

        if (spacing != 0) {
            int skillMax = (bigBallInfoList.size() / spacing);
            if (skillMax < skillList.size()) {
//                /// System.out.println("spacing error !!!");
                return null;
            }
        }

        if ((bigBallInfoList.size() - buffBallNum) != integralList.size()) {
//            /// System.out.println("????????????");
        }

        int wi = 0;
        for (int i = 0; i < bigBallInfoList.size(); i++) {
            BigBallInfo bigBallInfo = bigBallInfoList.get(i);
            if (skillList.size() > 0 && wi <= 0) {
                int rand = 30;
                if (((bigBallInfoList.size() - i) / spacing) > skillList.size()) {
                    rand = (int) (1 + Math.random() * (100 - 1 + 1));
                }

                if (rand <= 30) {
                    wi = spacing;
                    int inRand = (int) (1 + Math.random() * (skillList.size() - 1 + 1));
                    int val = skillList.remove(inRand - 1);
                    bigBallInfo.setSkillId(val);
                    if (val == 1011) {//阻碍球在下半区域
                        inRand = (int) (posYLow + Math.random() * ((posYMax + posYLow) / 2 - posYLow + 1));
                        bigBallInfo.getPoint().setY(inRand);
                    }
                    if (buffBallNum > 0) {
                        bigBallInfo.setIsBuffBall(true);
                        bigBallInfo.setBigBallId(buffBallId);
                    }
                } else {
                    wi--;
                }

                if (buffBallNum <= 0) {
                    int inRand = (int) (1 + Math.random() * (integralList.size() - 1 + 1));
                    int val = integralList.remove(inRand - 1);
                    bigBallInfo.setIntegral(val);
                }
            } else {
                wi--;
                if (bigBallInfo.getBigBallId() == secondBallId) {
                    int inRand = (int) (1 + Math.random() * (2 - 1 + 1));
                    bigBallInfo.setIntegral(Integer.parseInt(diff_integral[inRand - 1]));
                } else {
                    int inRand = (int) (1 + Math.random() * (integralList.size() - 1 + 1));
                    int val = integralList.remove(inRand - 1);
                    bigBallInfo.setIntegral(val);
                }

            }
        }
        return bigBallInfoList;
    }

    private static List<BigBallInfo> createMissionBallMode13(int totalBall, String[] ballIds, int posYMax, int posYLow, int minRange, int rangeNeed, int rangeNum, int ballNum,
                                                             int buffBallNum, String[] integrals, String[] skillIds, int buffBallId, int spacing, List<PointInfo> addPointList) {
        List<BigBallInfo> bigBallInfoList = new ArrayList<BigBallInfo>();

        return bigBallInfoList;
    }

    private List<MissionData.BigBall> createEndlessMap(int missionId) {
        List<MissionData.BigBall> ballList = new ArrayList<MissionData.BigBall>();
        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ENDLESS, missionId);
        int totalLen = (int) (Float.parseFloat(missionMap.get("length")) * 100);
        int ballNum = Integer.parseInt(missionMap.get("ball_num"));
        int integral = Integer.parseInt(missionMap.get("ball_score"));
        String[] posYStrs = SuperConfig.getInitBallPosYRange();
        int posYLow = (int) (Float.parseFloat(posYStrs[0]) * 100);
        int posYMax = (int) (Float.parseFloat(posYStrs[1]) * 100);
        int initX = 0;
        List<BigBallInfo> bigBallInfoList = new ArrayList<BigBallInfo>();
        int space = 5;
        boolean isTime = false;
        for (int i = 0; i < ballNum; i++) {
            int index = (int) (1 + Math.random() * (endlessList.size() - 1 + 1));
            int ballId = endlessList.get(index - 1);
            BigBallInfo bigBallInfo = new BigBallInfo();
            int randX = (int) (400 + Math.random() * (800 - 400 + 1));
            int randY = (int) (posYLow + Math.random() * (posYMax - posYLow + 1));
            if (initX + 800 > totalLen) {
                initX = totalLen;
            } else {
                initX += randX;
            }
            bigBallInfo.setBigBallId(ballId);
            bigBallInfo.getPoint().setX(initX);
            bigBallInfo.getPoint().setY(randY);
            bigBallInfo.setIsSpeed(false);
            int skillId = 0;
            if (space > 1) {
                if (isTime == false) {
                    int rand = (int) (1 + Math.random() * (3 - 1 + 1));
                    if (rand == 1) {
                        skillId = 999;
                        isTime = true;
                        bigBallInfo.setBigBallId(20000);
                        bigBallInfo.setIntegral(0);
                    } else {
                        bigBallInfo.setIntegral(integral);
                    }
                } else {
                    bigBallInfo.setIntegral(integral);
                }
                space--;
            } else if (space == 1) {
                if (isTime == false) {
                    skillId = 999;
                    bigBallInfo.setBigBallId(20000);
                    bigBallInfo.setIntegral(0);
                } else {
                    bigBallInfo.setIntegral(integral);
                }
                isTime = false;
                space = 10;
            }
            bigBallInfo.setSkillId(skillId);
            bigBallInfoList.add(i, bigBallInfo);
        }

        for (int i = 0; i < bigBallInfoList.size(); i++) {
            BigBallInfo bigBallInfo = bigBallInfoList.get(i);
            MissionData.BigBall.Builder bigBallBu = MissionData.BigBall.newBuilder();
            MissionData.Buff.Builder buffBu = MissionData.Buff.newBuilder();
            MissionData.Body.Builder ballBu = MissionData.Body.newBuilder();
            MissionData.Point.Builder pointBu = MissionData.Point.newBuilder();
            pointBu.setX(bigBallInfo.getPoint().getX());
            pointBu.setY(bigBallInfo.getPoint().getY());
            ballBu.setP(pointBu);
            ballBu.setId(bigBallInfo.getBigBallId());
            buffBu.setBall(ballBu);
            buffBu.setIsSpeed(bigBallInfo.isSpeed());
            bigBallBu.setBall(buffBu);
            bigBallBu.setIntegral(bigBallInfo.getIntegral());
            bigBallBu.setSkillId(bigBallInfo.getSkillId());
            ballList.add(bigBallBu.build());
        }
        return ballList;
    }

    private static String missionToRedis(int missionId, String uid, List<MissionData.BigBall> ballList/*,List<MissionData.Buff> skillList,List<MissionData.Buff> smallBallList,List<MissionData.Buff> debuffList*/) {
        Jedis jedis = Redis.getJedis(-1);
        Pipeline p = jedis.pipelined();
        String key = "missioninfo:" + uid + "&" + missionId;
        try {
            for (int i = 0; i < ballList.size(); i++) {
                byte[] bytes = ballList.get(i).toByteArray();
                MissionData.BigBall bigBall1 = MissionData.BigBall.parseFrom(bytes);
                String string = JsonFormat.printToString(bigBall1);
                p.hset(key, "bigball_" + i, string);
            }
            p.hset(key, "bigball_size", ballList.size() + "");
//            for (int i = 0 ; i < skillList.size() ; i++){
//                byte[] bytes = skillList.get(i).toByteArray();
//                MissionData.Buff skill = MissionData.Buff.parseFrom(bytes);
//                String string = JsonFormat.printToString(skill);
//                p.hset(key, "skill_"+i,string);
//            }
//            p.hset(key, "skill_size",skillList.size()+"");
//            for (int i = 0 ; i < smallBallList.size() ; i++){
//                byte[] bytes = smallBallList.get(i).toByteArray();
//                MissionData.Buff smallBall = MissionData.Buff.parseFrom(bytes);
//                String string = JsonFormat.printToString(smallBall);
//                p.hset(key, "smallball_"+i,string);
//            }
//            p.hset(key, "smallball_size",smallBallList.size()+"");
//            for (int i = 0 ; i < debuffList.size() ; i++){
//                byte[] bytes = debuffList.get(i).toByteArray();
//                MissionData.Buff debuff = MissionData.Buff.parseFrom(bytes);
//                String string = JsonFormat.printToString(debuff);
//                p.hset(key, "debuff_"+i,string);
//            }
//            p.hset(key, "debuff_size",debuffList.size()+"");
            p.sync();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            Redis.destory(jedis);
        }
        return key;
    }

    public int judgeMission(int missionId, String uid) {
//        Redis jedis = Redis.getInstance();
        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MISSION, missionId);
//        int chapter = Integer.parseInt(missionMap.get("chapter"));
//        int nowChapter = Integer.parseInt(jedis.hget("role:"+uid,"chapter"));
//        if (chapter < nowChapter){
//            return 0;
//        }

//        String front = missionMap.get("front_client");
////        if (chapter == nowChapter){
//            if (!front.equals("0")){
//                int frontId = Integer.parseInt(front);
//                int missionMax = getMissionMax(uid);
//                if (missionMax < frontId){
//                    return ProtoData.ErrorCode.MISSIONIDERROR_VALUE;
//                }
//            }
//        }else if (chapter == nowChapter+1){
//            Iterator<String> iterator = jedis.keys("missionconfig*").iterator();
//            while (iterator.hasNext()){
//                String key = iterator.next();
//                String thisId = key.split(":")[1];
//                int c = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_MISSION,thisId,"chapter"));
//                if (c == nowChapter){
//                    int thisType = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_MISSION,thisId+"","type"));
//                    MissionInfo missionInfo = getMissionInfo(roleMissionMap,thisType);
//                    if (missionInfo == null){
//                        return ProtoData.ErrorCode.MISSIONIDERROR_VALUE;
//                    }
//                    if (missionInfo.getList().contains(Integer.parseInt(thisId)) == false){
//                        return ProtoData.ErrorCode.MISSIONIDERROR_VALUE;
//                    }
//                }
//            }
//        }else {
//            return ProtoData.ErrorCode.MISSIONIDERROR_VALUE;
//        }
        return 0;
    }

    private List<MissionData.BigBall> getBigBallList(String key) {
        List<MissionData.BigBall> ballList = null;
        try {
            Redis jedis = Redis.getInstance();
            Map<String, String> map = jedis.hgetAll(key);
            if (map.size() == 0) {
                return null;
            }
            int size = Integer.parseInt(map.get("bigball_size"));
            ballList = new ArrayList<MissionData.BigBall>();
            for (int i = 0; i < size; i++) {
                MissionData.BigBall.Builder builder = MissionData.BigBall.newBuilder();
                JsonFormat.merge(map.get("bigball_" + i), builder);
                ballList.add(builder.build());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return ballList;
    }

    private List<MissionData.Buff> getSkillList(String key) {
        List<MissionData.Buff> skillList = null;
        try {
            Redis jedis = Redis.getInstance();
            Map<String, String> map = jedis.hgetAll(key);
            int size = Integer.parseInt(map.get("skill_size"));
            skillList = new ArrayList<MissionData.Buff>();
            for (int i = 0; i < size; i++) {
                MissionData.Buff.Builder builder = MissionData.Buff.newBuilder();
                JsonFormat.merge(map.get("skill_" + i), builder);
                skillList.add(builder.build());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return skillList;
    }

    private List<MissionData.Buff> getSmallBallList(String key) {
        List<MissionData.Buff> smallBallList = null;
        try {
            Redis jedis = Redis.getInstance();
            Map<String, String> map = jedis.hgetAll(key);
            int size = Integer.parseInt(map.get("smallball_size"));
            smallBallList = new ArrayList<MissionData.Buff>();
            for (int i = 0; i < size; i++) {
                MissionData.Buff.Builder builder = MissionData.Buff.newBuilder();
                JsonFormat.merge(map.get("smallball_" + i), builder);
                smallBallList.add(builder.build());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return smallBallList;
    }

    private List<MissionData.Buff> getDebuffList(String key) {
        List<MissionData.Buff> debuffList = null;
        try {
            Redis jedis = Redis.getInstance();
            Map<String, String> map = jedis.hgetAll(key);
            int size = Integer.parseInt(map.get("debuff_size"));
            debuffList = new ArrayList<MissionData.Buff>();
            for (int i = 0; i < size; i++) {
                MissionData.Buff.Builder builder = MissionData.Buff.newBuilder();
                JsonFormat.merge(map.get("debuff_" + i), builder);
                debuffList.add(builder.build());
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return debuffList;
    }

    public MissionData.ResponseBeginMission.Builder getMissionMap(int missionId, String uid) {
        MissionData.ResponseBeginMission.Builder builder = MissionData.ResponseBeginMission.newBuilder();
//        int rand = 0;
//        if (TimerHandler.mapType == 1){
//            rand = (int)(1 + Math.random() * (SuperConfig.INITMISSIONNUM - 1 + 1));
//        }else {
//            rand = (int)(SuperConfig.INITMISSIONNUM+1 + Math.random() * (SuperConfig.INITMISSIONNUM*2 - (SuperConfig.INITMISSIONNUM+1) + 1));
//        }

        createMap(missionId, uid);
        String key = "missioninfo:" + uid + "&" + missionId;
        List<MissionData.BigBall> bigBallList = getBigBallList(key);
        if (bigBallList != null) {
            builder.addAllBigBall(bigBallList);
            nowMissionToRedis(uid, key, missionId);
        } else {
            builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
        }
//        builder.addAllSkill(getSkillList(key));
//        builder.addAllDebuff(getDebuffList(key));
//        builder.addAllSmallBall(getSmallBallList(key));
        return builder;
    }

    public MissionData.ResponseBeginGold.Builder createList(Integer missionid,String uid,Integer types){
        Redis jedis = Redis.getInstance();
        Integer class_id=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARMISSION, missionid+ "", "class_id"));

        List<String> clazzids=new ArrayList<String>();
        List<String> ball_sec=new ArrayList<String>();
        List<String> bianyi=new ArrayList<String>();
        List<String> ball_num=new ArrayList<String>();

        List<String> du=null;
        Set<String> clazz = jedis.keys(SuperConfig.REDIS_EXCEL_STARCLASS+"*");

        for(int i=0;i<clazz.size();i++){
            if(Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  (i+1)+ "", "file"))==class_id){
                ball_num.add(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  (i+1)+ "", "ball_num"));
            }
        }
        ball_num=new ArrayList(new HashSet(ball_num));


        Integer type=0;
        Integer index=0;
        Integer downup=0;
        Integer star_classid=0;

        List<MissionData.BigBall> bigBallList = new ArrayList<MissionData.BigBall>();
        MissionData.BigBall.Builder ball=null;
        List<ItemData.Item> itemList = null;
        ItemData.Item.Builder item=null;

        for (int j=0;j<ball_num.size();j++){
            ball_sec=new ArrayList<String>();
            clazzids=new ArrayList<String>();

            for(int i=0;i<clazz.size();i++){
            if(Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  (i+1)+ "", "file"))==class_id){
                    type=Integer.valueOf(ball_num.get(j));
                    if(type==Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  (i+1)+ "", "ball_num"))){
                        ball_sec.add(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  (i+1)+ "", "ball_sec"));
                        clazzids.add(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  (i+1)+ "", "id"));
                    }
                }
            }

            index=ranName.ranGift(ball_sec);
            star_classid=Integer.valueOf(clazzids.get(index));

            itemList=new ArrayList<ItemData.Item>();

            du=new ArrayList<String>();
            du.add(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  star_classid+ "", "sec_down"));
            du.add(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  star_classid+ "", "sec_up"));

            downup=ranName.ranGift(du);
            Integer price=Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "price"));
            StringBuffer qujian=new StringBuffer("");
            StringBuffer qujian2=new StringBuffer("");
            Integer down=Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "down"));
            Integer up=Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "up"));
            qujian.append(price-down+","+(price+up));


            if(downup==0){
                qujian2.append(price-down+","+price);
            }else if(downup==1){
                qujian2.append(price+","+(price+up));
            }

            List<String> qiu=new ArrayList<String>();
            String a=qujian2.toString();
            for(int aa=Integer.valueOf(a.split(",")[0]);aa<=Integer.valueOf(a.split(",")[1]);aa++){
                if(aa%10==0){
                    qiu.add(aa+"");
                }
            }

            List<String> one=new ArrayList<String>();
            List<String> two=new ArrayList<String>();
            List<String> three=new ArrayList<String>();
            Integer san=0;
            Integer yi=1;
            for(int bb=0;bb<qiu.size();bb++){
                if(yi==1){
                    one.add(qiu.get(bb));
                }else if(yi==2){
                    two.add(qiu.get(bb));
                }else{
                    three.add(qiu.get(bb));
                }
                san++;
                if(san==qiu.size()/3){
                    yi++;
                    san=0;
                }
            }
            Integer lastGold=0;
            switch (ranName.ranGift(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  1+ "", "three_probability").split("\\|"))){
                case 1:
                    lastGold=Integer.valueOf(two.get(ranName.ranGift(two)));
                    break;
                case 2:
                    lastGold=Integer.valueOf(three.get(ranName.ranGift(three)));
                    break;
                case 0:
                    lastGold=Integer.valueOf(one.get(ranName.ranGift(one)));
                    break;
            }
            //金币
            item=ItemData.Item.newBuilder();
            item.setNum(lastGold);
            item.setId(1);
            itemList.add(item.build());

            Integer diff_item=Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "diff_item"));
            bianyi.add(diff_item+"");
            bianyi.add(100-diff_item+"");
            if(ranName.ranGift(bianyi)==0){//变异
                item=ItemData.Item.newBuilder();
                item.setId(Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "item_id").split(",")[0]));
                item.setNum(Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "item_id").split(",")[1]));
                itemList.add(item.build());
            }

            ball = MissionData.BigBall.newBuilder();
            ball.addAllItem(itemList);
            ball.setSkillId(0);
            ball.setIntegral(Integer.valueOf(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARCLASS,  clazzids.get(index)+ "", "id")));
            ball.setSection(qujian.toString());
            bigBallList.add(ball.build());
        }
        MissionData.ResponseBeginGold.Builder builder = MissionData.ResponseBeginGold.newBuilder();
        builder.addAllBigBall(bigBallList);
        return builder;
    }

    public MissionData.ResponseBeginEndless.Builder getEndlessMap(int type, int missionId, String uid) {
        MissionData.ResponseBeginEndless.Builder builder = MissionData.ResponseBeginEndless.newBuilder();
        builder.setType(type);
        List<MissionData.BigBall> ballList = createEndlessMap(missionId);
//        String key = "missioninfo:"+uid+"&"+missionId;
        builder.addAllBigBall(ballList);

//        nowMissionToRedis(uid, key, missionId);

        ITask iTask = TaskDao.getInstance();
        iTask.updateOneTask(uid, 10010, 1);
        return builder;
    }

    private void nowMissionToRedis(String uid, String missionKey, int missionId) {
        Redis jedis = Redis.getInstance();
        Map<String, String> missionMap = new HashMap<String, String>();
        missionMap.put("uid", uid);
        missionMap.put("missionkey", missionKey);
        missionMap.put("missionid", missionId + "");
        jedis.hmset("nowmission:" + uid, missionMap);
//        int time = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_MISSION, missionId + "", "target"))+10;
//        jedis.expire("rolenowmission:"+uid,time);
    }

    private Map<String, String> getNowMissionFromRedis(String uid) {
        Map<String, String> map = null;
        Redis jedis = Redis.getInstance();
        map = jedis.hgetAll("nowmission:" + uid);
        return map;
    }

    public MissionData.ResponseCountMission.Builder countMission(int missionId, String uid, boolean isTimeOut, List<Integer> ballIdList) {
        MissionData.ResponseCountMission.Builder builder = MissionData.ResponseCountMission.newBuilder();
       boolean isLuckyTime= isLuckyTime();
        boolean isSuccess = true;
        Map<String, String> roleMissionMap = getNowMissionFromRedis(uid);
        if (roleMissionMap.size() == 0) {
            isSuccess = false;
            builder.setIsSuccess(isSuccess);
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid + ":[countMission] error 1");
            return builder;
        }
        int roleMissionId = Integer.parseInt(roleMissionMap.get("missionid"));
        if (roleMissionId != missionId) {
            isSuccess = false;
            builder.setIsSuccess(isSuccess);
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid + ":[countMission] error 2");
            return builder;
        }
        if (isTimeOut == true) {
            isSuccess = false;
        }
        List<MissionData.BigBall> bigBallList = getBigBallList(roleMissionMap.get("missionkey"));
        int allIntegral = 0;
        int nowCombo = 0;
        int maxCombo = 0;
        List<Integer> skillList = new ArrayList<Integer>();
        for (int i = 0; i < ballIdList.size(); i++) {
            int ballId = ballIdList.get(i);
            if (ballId == -1) {
                nowCombo = 0;
            } else if (ballId < bigBallList.size()) {
                MissionData.BigBall bigBall = bigBallList.get(ballId);
                allIntegral += bigBall.getIntegral();
                if (bigBall.getSkillId() > 0) {
                    skillList.add(bigBall.getSkillId());
                }
                nowCombo++;
                //
                if (nowCombo > maxCombo) {
                    maxCombo = nowCombo;
                }
            }
        }
        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MISSION, missionId);
        int mode = Integer.parseInt(missionMap.get("mode"));
        IItem iItem = ItemDao.getInstance();
        ILogin iLogin = LoginDao.getInstance();
        int target = Integer.parseInt(missionMap.get("target_score"));
        int combo = Integer.parseInt(missionMap.get("target_combo"));
        if (allIntegral < target || maxCombo < combo) {
            isSuccess = false;
        } else if (mode == 3) {
            String[] targetBalls = missionMap.get("target_ball").split("\\|");
            for (int i = 0; i < targetBalls.length; i++) {
                int needId = Integer.parseInt(targetBalls[i].split(",")[0]);
                int needNum = Integer.parseInt(targetBalls[i].split(",")[1]);
                int val = 0;
                int j = 0;
                for (; j < ballIdList.size(); j++) {
                    int ballId = ballIdList.get(j);
                    if (ballId == -1) {
                        continue;
                    }
                    MissionData.BigBall bigBall = bigBallList.get(ballId);
                    if (bigBall.getBall().getBall().getId() == needId) {
                        val += 1;
                        if (val == needNum) {
                            j++;
                            break;
                        }
                    } else {
                        val = 0;
                    }
                }
                if (j == 0 || (val < needNum && j == ballIdList.size())) {
                    isSuccess = false;
                    break;
                }
                if (ballIdList.size() >= j) {
                    ballIdList = ballIdList.subList(j, ballIdList.size());
                }
            }
        }

        ITask iTask = TaskDao.getInstance();
        List<RewardInfo> rewardInfoList=new ArrayList<RewardInfo>();
        if (isSuccess == true) {
//            int type = Integer.parseInt(missionMap.get("type"));
            boolean bo = updateMissionProgress(uid, missionId);
            int val = bo == true ? 2 : 1;
            if (missionMap.get("item") != null && !missionMap.get("item").equals("0")) {
                String[] items = missionMap.get("item").split("\\|");
                for (int i = 0; i < items.length; i++) {
                    if (items[i].equals("")) {
                        continue;
                    }
                    int rate = Integer.parseInt(items[i].split(",")[2]);
                    int rand = (int) (1 + Math.random() * (100 - 1 + 1));
                    if (rand <= rate) {
                        int id = Integer.parseInt(items[i].split(",")[0]);
                        int num = Integer.parseInt(items[i].split(",")[1]) * val;
                        if(isLuckyTime){
                            num*=2;
                        }
                        double totalNum = iItem.updateItemInfo(uid, id, num);
                        if (totalNum > 0) {
                            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                            itemBu.setId(id);
                            itemBu.setNum(totalNum);
                            builder.addItem(itemBu);
                            RewardInfo rewardInfo =new RewardInfo();
                            rewardInfo.setItemid(id);
                            rewardInfo.setItemnums(num);
                            rewardInfo.setItemtotal(new Double(totalNum).intValue());
                            rewardInfoList.add(rewardInfo);
                            ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                            showBu.setId(id);
                            showBu.setNum(num);
                            builder.addShowItem(showBu);
                        }
                    }
                }
            }

            if (bo == true) {
                String firstItem=missionMap.get("first_item");
                if (firstItem!=null&&!"0".equals(firstItem)) {
                    String[] itemConfigArrays=firstItem.split("\\|");
                   for(int i=0;i<itemConfigArrays.length;i++){
                       String itemConfig=itemConfigArrays[i];
                    int id = Integer.parseInt(itemConfig.split(",")[0]);
                    int num = Integer.parseInt(itemConfig.split(",")[1]);
                    double totalNum = iItem.updateItemInfo(uid, id, num);
                    if (totalNum > 60) {
                        totalNum = 60;
                    }
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    itemBu.setId(id);
                    itemBu.setNum(totalNum);
                    builder.addItem(itemBu);
                    RewardInfo rewardInfo = new RewardInfo();
                    rewardInfo.setItemid(id);
                    rewardInfo.setItemnums(num);
                    rewardInfo.setItemtotal(new Double(totalNum).intValue());
                    rewardInfoList.add(rewardInfo);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(id);
                    showBu.setNum(num);
                    builder.addShowItem(showBu);

                }
/*                    Map<String, String> itemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, id);
                    int dress_type = Integer.parseInt(itemMap.get("dress_type"));
                    if (dress_type == 1) {
                        ItemData.Item.Builder showItemitemClothing = ItemData.Item.newBuilder();
                        showItemitemClothing.setId(id);
                        showItemitemClothing.setNum(1);
                        builder.addShowItem(showItemitemClothing);

                        String get_id = itemMap.get("get_id");
                        String[] strArray = get_id.split("\\|");
                        int dressId = Integer.parseInt(strArray[0]);
                        int cType = Integer.parseInt(strArray[1]);
                        List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
                        awardClothing(uid, dressId, cType, utilityList);
                        builder.addAllClothingList(utilityList);
                    }*/


                }
            }

            int exp = Integer.parseInt(missionMap.get("exp"));
            if (exp > 0) {
                if(isLuckyTime){
                    exp*=2;
                }
                List<Integer> result = iLogin.updateRoleExp(uid, exp);
                builder.setLv(result.get(0));
                builder.setExp(result.get(1));
                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                RewardInfo rewardInfo =new RewardInfo();
                rewardInfo.setItemid(25);
                rewardInfo.setItemnums(exp);
                rewardInfo.setItemtotal(result.get(1));
                rewardInfoList.add(rewardInfo);
                showBu.setId(25);
                showBu.setNum(exp);
                builder.addShowItem(showBu);
            }

           /* iTask.updateOneTask(uid, 10001, 1);*/

            int debuffNum = 0;
            for (int i = 0; i < skillList.size(); i++) {
                int skillId = skillList.get(i);
                int skillType = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_SKILL, skillId + "", "type"));
                if (skillType == 0) {
                    debuffNum += 1;
                }
            }
            if (debuffNum > 0) {
                iTask.updateOneTask(uid, 10007, debuffNum);
            }
            iTask.updateAchievement(uid, 8, 1);
         //   iTask.updateAchievement(uid, 29, 1);

            StringBuffer hql = new StringBuffer(" from MissionEntity where type=2 and uid='").append(uid).append("'").append("and missionid=").append(missionId);
            List<Object> list = MySql.queryForList(hql.toString());
            if (list.size() == 0) {
                MissionEntity mission = new MissionEntity();
                mission.setUid(uid);
                mission.setMissionid(missionId);
                mission.setNums(1);
                mission.setType(2);
                MySql.insert(mission);
                Redis jedis = Redis.getInstance();
                jedis.hset("rolemission:" + uid + "#" + 2, missionId + "", "1");

            } else {
                hql = new StringBuffer(" update MissionEntity set nums=(nums+1)").append("where type=2 and uid='").append(uid).append("' and missionid=").append(missionId);
                MySql.updateSomes(hql.toString());
                Redis jedis = Redis.getInstance();
                String missionStr = jedis.hget("rolemission:" + uid + "#" + 2, missionId+ "");
                jedis.hset("rolemission:" + uid + "#" + 2, missionId + "", Integer.parseInt(missionStr) + 1 + "");
            }
        } else {
            int itemLose = Integer.parseInt(missionMap.get("item_lose"));
            if(isLuckyTime){
                itemLose*=2;
            }
            double totalNum = iItem.updateItemInfo(uid, 1, itemLose);
            if (totalNum > 0) {
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(1);
                itemBu.setNum(totalNum);
                builder.addItem(itemBu);
                RewardInfo rewardInfo =new RewardInfo();
                rewardInfo.setItemid(1);
                rewardInfo.setItemnums(itemLose);
                rewardInfo.setItemtotal(new Double(totalNum).intValue());
                rewardInfoList.add(rewardInfo);
                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(1);
                showBu.setNum(itemLose);
                builder.addShowItem(showBu);
            }
            int exp = Integer.parseInt(missionMap.get("exp_lose"));
            if (exp > 0) {
                if(isLuckyTime){
                    exp*=2;
                }
                List<Integer> result = iLogin.updateRoleExp(uid, exp);
                builder.setLv(result.get(0));
                builder.setExp(result.get(1));
                RewardInfo rewardInfo =new RewardInfo();
                rewardInfo.setItemid(25);
                rewardInfo.setItemnums(exp);
                rewardInfo.setItemtotal(result.get(1));
                rewardInfoList.add(rewardInfo);
                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(25);
                showBu.setNum(exp);
                builder.addShowItem(showBu);
            }

            iTask.updateAchievement(uid, 25, 1);
        }
        builder.setIsSuccess(isSuccess);
        Redis jedis = Redis.getInstance();
        int missionIdMax = Integer.parseInt(jedis.hget("role:" + uid, "missionid"));
        builder.setMissionId(missionIdMax);
        jedis.del("nowmission:" + uid);
        jedis.del("missioninfo:" + uid + "&" + missionId);

        iTask.updateAchievement(uid, 21, maxCombo - 1);
        iTask.updateAchievement(uid, 32, 1);
        GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录关卡结算奖励
        gameRecordEntity.setGamepattern(2);
        gameRecordEntity.setMissionid(missionId);
        gameRecordEntity.setType(isSuccess?1:2);
        gameRecordEntity.setUid(uid);
        StringBuffer rewardString=new StringBuffer("");
        for(int i=0;i<rewardInfoList.size();i++){
            rewardString.append(MyUtils.objectToJson(rewardInfoList.get(i)));
            if(i!=(rewardInfoList.size()-1)){
                rewardString.append(",");
            }
        }
        gameRecordEntity.setReward(rewardString.toString());
        MySql.insert(gameRecordEntity);
        return builder;
    }

    boolean awardClothing(String uid, int dressId, int cType, List<CommonData.Utility> utilityList) {
        synchronized (uid) {
            ILogin iLogin = LoginDao.getInstance();

            Redis jedis = Redis.getInstance();

            Map<String, String> dressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_DRESS, dressId);
            //int nowRoleId = Integer.parseInt(dressMap.get("mod"));
            String roleId = dressMap.get("mod");

            String key = "roledress:" + uid + "#" + roleId;
            RoleDressInfo roleDressInfo = iLogin.getRoleDressFromRedis(key);
            if (roleDressInfo == null) return false;

            List<UtilityInfo> dressList = roleDressInfo.getDressList();
            //List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
            // Map<Integer,ItemInfo> itemMap = new HashMap<Integer, ItemInfo>();
            List<Object> hqlObj = new ArrayList<Object>();


            // List<UserData.Clothing>  clothingList = new ArrayList<UserData.Clothing>();
            // UserData.Clothing clothing;
            // clothing.get

            long addTime = getAddTime(cType);

            UtilityInfo utilityInfo = null;
            for (int j = 0; j < dressList.size(); j++) {
                if (dressList.get(j).getId() == dressId) {
                    utilityInfo = dressList.get(j);
                    break;
                }
            }
            if (dressMap.size() == 0) {
                log.error(uid + ":[awardClothing] error 2 >>>dressId:" + dressId);
                return false;
            }

            if (utilityInfo == null) {
                utilityInfo = new UtilityInfo();
                utilityInfo.setId(dressId);
                long overTime = addTime == 0 ? 0 : (TimerHandler.nowTimeStamp + addTime);
                utilityInfo.setOverStamp(overTime + "");
                dressList.add(utilityInfo);
                DressEntity dressEntity = new DressEntity();
                dressEntity.setUid(uid);
                dressEntity.setRoleid(Integer.parseInt(roleId));
                dressEntity.setDressid(dressId);
                dressEntity.setOverstamp(overTime + "");
                hqlObj.add(dressEntity);

            } else {
                if (utilityInfo.getOverStamp().equals("0")) {
                    log.error(uid + ":[awardClothing] error 4 >>>dressId:" + utilityInfo.getId());
                    return false;
                }
                long overTime = addTime == 0 ? 0 : (Long.parseLong(utilityInfo.getOverStamp()) + addTime);
                utilityInfo.setOverStamp(overTime + "");

                StringBuffer hql = new StringBuffer("update DressEntity set overstamp = '").append(overTime).append("' where uid = '")
                        .append(uid).append("' and roleid = ").append(Integer.parseInt(roleId)).append(" and dressid = ").append(dressId);
                hqlObj.add(hql.toString());
            }
            CommonData.Utility.Builder utilityBu = CommonData.Utility.newBuilder();
            utilityBu.setId(utilityInfo.getId());
            utilityBu.setTimeStamp(utilityInfo.getOverStamp());
            utilityList.add(utilityBu.build());

            //builder.addAllClothingList(utilityList);
            //衣物sql
            for (int i = 0; i < hqlObj.size(); i++) {
                Object object = hqlObj.get(i);
                if (object instanceof DressEntity) {
                    MySql.insert(object);
                } else if (object instanceof String) {
                    MySql.updateSomes(object.toString());
                } else {
//                    /// System.out.println("???????");
                }
            }

            jedis.hset("roledress:" + uid + "#" + roleId, "dresslist", MyUtils.objectToJson(dressList));

        }
        return true;
    }

    private long getAddTime(int type) {
        long addTime = -1;
        if (type == 1) {
            addTime = 7 * 24 * 60 * 60 * 1000;
        } else if (type == 2) {
            addTime = (long) 30 * 24 * 60 * 60 * 1000;
        } else if (type == 3) {
            addTime = 0;
        }
        return addTime;
    }


    public MissionData.ResponseCountEndless.Builder countEndless(String uid, int endlessId, int totalTime, int totalIntegral, int maxCombo) {
        MissionData.ResponseCountEndless.Builder builder = MissionData.ResponseCountEndless.newBuilder();
        builder.setErrorId(0);
        builder.setEndlessId(endlessId);
        Map<String, String> endlessMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ENDLESS, endlessId);
        float exp = Float.parseFloat(endlessMap.get("exp"));
        float gold = Float.parseFloat(endlessMap.get("gold"));
        String itemIds = endlessMap.get("itemID");
        int itemNum = Integer.parseInt(endlessMap.get("item_num"));
            boolean isLuckyTime=isLuckyTime();
        IItem iItem = ItemDao.getInstance();
        int val = (totalIntegral / 200) - 1;
        if (val < 0) {
            val = 0;
        }
        if (val > 10) {
            val = 10;
        }
        List<RewardInfo> rewardInfoList=new ArrayList<RewardInfo>();//获得的奖励
        if (exp > 0 && totalIntegral > 0) {
            int addExp = (int) Math.round((1 + val * 0.5) * exp * totalIntegral);
            if(isLuckyTime){
               addExp*=2;
            }
            ILogin iLogin = LoginDao.getInstance();
            List<Integer> result = iLogin.updateRoleExp(uid, addExp);
            builder.setLv(result.get(0));
            builder.setExp(result.get(1));
            RewardInfo rewardInfo =new RewardInfo();
            rewardInfo.setItemid(25);
            rewardInfo.setItemnums(addExp);
            rewardInfo.setItemtotal(result.get(1));
            rewardInfoList.add(rewardInfo);
            ItemData.Item.Builder showExpBu = ItemData.Item.newBuilder();
            showExpBu.setId(25);
            showExpBu.setNum(addExp);
            builder.addShowItem(showExpBu);
        }
        if (gold > 0 && totalIntegral > 0) {
            int addGold = (int) Math.round((1 + val * 0.5) * gold * totalIntegral);
            if(isLuckyTime){
                addGold*=2;
            }
            double totalGold = iItem.updateItemInfo(uid, 1, addGold);
            ItemData.Item.Builder addGoldBu = ItemData.Item.newBuilder();
            addGoldBu.setId(1);
            addGoldBu.setNum(totalGold);
            builder.addItem(addGoldBu);
            RewardInfo rewardInfo =new RewardInfo();
            rewardInfo.setItemid(1);
            rewardInfo.setItemnums(addGold);
            rewardInfo.setItemtotal(new Double(totalGold).intValue());
            rewardInfoList.add(rewardInfo);

            ItemData.Item.Builder showGoldBu = ItemData.Item.newBuilder();
            showGoldBu.setId(1);
            showGoldBu.setNum(addGold);
            builder.addShowItem(showGoldBu);
        }
        if (itemIds != null && totalIntegral > 0) {
            String[] strings = itemIds.split("\\|");
            for (int i = 0; i < val + 1; i++) {
                int index = 0;
                if (strings.length > 1) {
                    index = (int) (1 + Math.random() * (strings.length - 1 + 1)) - 1;
                }
                int itemId = Integer.parseInt(strings[index]);
                int adItemNum = itemNum;
                if(isLuckyTime){
                    adItemNum*=2;
                }
                double totalNum = iItem.updateItemInfo(uid, itemId, adItemNum);
                if (totalNum > 0) {
                    ItemData.Item.Builder addBu = ItemData.Item.newBuilder();
                    addBu.setId(itemId);
                    addBu.setNum(totalNum);
                    builder.addItem(addBu);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid( itemId);
                    rewardInfo.setItemnums(adItemNum);
                    rewardInfo.setItemtotal(new Double(totalNum).intValue());
                    rewardInfoList.add(rewardInfo);

                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(itemId);
                    showBu.setNum(adItemNum);
                    builder.addShowItem(showBu);
                }
            }
        }
        long total = updateEndlessIntegral(uid, totalIntegral);
        builder.setEndlessIntegral(total);
        Redis jedis = Redis.getInstance();
        int endless = Integer.parseInt(jedis.hget("role:" + uid, "endless"));
        builder.setMaxIntegral(endless);
        ITask iTask = TaskDao.getInstance();
        iTask.updateAchievement(uid, 21, maxCombo);
        StringBuffer hql = new StringBuffer(" from MissionEntity where type=3 and uid='").append(uid).append("'").append("and missionid=").append(endlessId);
        List<Object> list = MySql.queryForList(hql.toString());
        if (list.size() == 0) {
            MissionEntity mission = new MissionEntity();
            mission.setUid(uid);
            mission.setMissionid(endlessId);
            mission.setNums(1);
            mission.setType(3);
            MySql.insert(mission);
            jedis.hset("rolemission:" + uid + "#" + 3, endlessId + "", "1");
        } else {
            hql = new StringBuffer(" update MissionEntity set nums=(nums+1)").append("where type=3 and uid='").append(uid).append("' and missionid=").append(endlessId);
            MySql.updateSomes(hql.toString());
            String missionStr = jedis.hget("rolemission:" + uid + "#" + 3, endlessId+ "");
            jedis.hset("rolemission:" + uid + "#" + 3, endlessId + "", Integer.parseInt(missionStr) + 1 + "");
        }

        GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录关卡结算奖励
        gameRecordEntity.setGamepattern(3);
        gameRecordEntity.setMissionid(endlessId);
        gameRecordEntity.setType(totalIntegral);
        gameRecordEntity.setUid(uid);
        StringBuffer rewardString=new StringBuffer("");
        for(int i=0;i<rewardInfoList.size();i++){
            rewardString.append(MyUtils.objectToJson(rewardInfoList.get(i)));
            if(i!=(rewardInfoList.size()-1)){
                rewardString.append(",");
            }
        }
        gameRecordEntity.setReward(rewardString.toString());
        MySql.insert(gameRecordEntity);
        return builder;
    }

    //判断是否是首通
    public boolean isFirst(String uid, int missionid) {
        Redis jedis = Redis.getInstance();
        Map<String, String> map = jedis.hgetAll("headball:" + uid);
        if (map.size() == 0 || map == null) {
            StringBuffer sql = new StringBuffer("from HeadBallEntity where user_id='").append(uid).append("'");
            List<Object> list = MySql.queryForList(sql.toString());
            if (list.size() == 0) {
                HeadBallEntity headBall = new HeadBallEntity();
                headBall.setJump_num(0);
                headBall.setJump_missionid(0);
                headBall.setIs_first(0);
                headBall.setBattle_num(0);
                headBall.setUser_id(uid);
                headBall.setIs_threestar(0);
                setHeadBallToRedis(headBall);
                return true;
            }
            if (list.size() == 1) {
                HeadBallEntity headBall = (HeadBallEntity) list.get(0);
                if (missionid > headBall.getJump_missionid()) {
                    return true;
                } else if (missionid <= headBall.getJump_missionid()) {
                    return false;
                }
            }
            return true;
        } else {
            String jump_missionid = map.get("jump_missionid");
            if (missionid > Integer.parseInt(jump_missionid)) {
                //说明是首通
                return true;
            } else {
                //说明不是首通
                return false;
            }
        }
    }

    //顶球结算返回
    public MissionData.ResponseCountHeadBall.Builder countHeadBallMission(HeadBallEntity headBallEntity, boolean isSuccess, int exp, List<ItemData.Item> itemList) {
        MissionData.ResponseCountHeadBall.Builder builder = MissionData.ResponseCountHeadBall.newBuilder();
        IItem iItem = ItemDao.getInstance();
        ILogin iLogin = LoginDao.getInstance();
        ITask iTask = TaskDao.getInstance();
        Redis jedis = Redis.getInstance();
        String uid = headBallEntity.getUser_id();

        try {
            //验证前台传来的奖励数据是否合法
            if (isSuccess == true) 
            {

                String mailjump_missionid = jedis.hget("headball:" + headBallEntity.getUser_id(), "jump_missionid");
                if (Integer.parseInt(mailjump_missionid) < 2 && headBallEntity.getJump_missionid() == 2) {
                    IFriend iFriend = FriendDao.getInstance();
                    String mid = MyUtils.setRandom();
                    MessageInfo info = iFriend.addNewMessage(uid, 30, "", mid, TimerHandler.nowTimeStamp);
                    if (info != null) 
                    {
                        iFriend.reportMessage(mid, 30, "", uid);
                    }
                }
                //判断是不是首通
                Map<String, String> map = jedis.hgetAll("headball:" + headBallEntity.getUser_id());
                if (headBallEntity.getJump_missionid() > Integer.parseInt(map.get("jump_missionid"))) {
                    //首通
                    Map<String, String> jumpMissionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_JUMPMISSION, headBallEntity.getJump_missionid());
                    String itemStr = jumpMissionMap.get("first_item");
                    String[] items = itemStr.split("\\|");
                    //24,3,100|25,200,100|1,200,100
                    for (int i = 0; i < items.length; i++) 
                    {
                        int id = Integer.parseInt(items[i].split(",")[0]);
                        int num = Integer.parseInt(items[i].split(",")[1]);
                        Map<String, String> itemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, id);
                        int dress_type = Integer.parseInt(itemMap.get("dress_type"));
                        if (dress_type == 1) 
                        {
                            String get_id = itemMap.get("get_id");
                            String[] strArray = get_id.split("\\|");
                            int dressId = Integer.parseInt(strArray[0]);
                            int cType = Integer.parseInt(strArray[1]);

                            List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
                            awardClothing(uid, dressId, cType, utilityList);
                            builder.addAllClothingList(utilityList);

                            continue;
                        }
                        if (id == 25) 
                        {
                            List<Integer> result = iLogin.updateRoleExp(headBallEntity.getUser_id(), num);
                            builder.setLv(result.get(0));
                            builder.setExp(result.get(1));
                        } 
                        else 
                        {
                            iItem.updateItemInfo(headBallEntity.getUser_id(), id, num);
                        }
                    }
                    //首通成功之后,插入数据库
                    map.put("is_first", "1");
                    map.put("battle_num", "1");
                    map.put("jump_num", headBallEntity.getJump_num() + "");
                    map.put("jump_missionid", headBallEntity.getJump_missionid() + "");
                    map.put("user_id", headBallEntity.getUser_id());
                    map.put("is_threestar", headBallEntity.getIs_threestar() + "");
                    jedis.hmset("headball:" + headBallEntity.getUser_id(), map);

                    //插入数据库
                    HeadBallEntity ballEntity = new HeadBallEntity();
                    ballEntity.setIs_first(1);
                    ballEntity.setBattle_num(1);
                    ballEntity.setJump_num(headBallEntity.getJump_num());
                    ballEntity.setJump_missionid(headBallEntity.getJump_missionid());
                    ballEntity.setUser_id(headBallEntity.getUser_id());
                    ballEntity.setIs_threestar(headBallEntity.getIs_threestar());
                    MySql.insert(ballEntity);

                } else {
                    //非首通
                    for (int i = 0; i < itemList.size(); i++) {
                        ItemData.Item item = itemList.get(i);
                        int id = item.getId();
                        Double num = item.getNum();
                        if (id == 25) {
                            List<Integer> result = iLogin.updateRoleExp(headBallEntity.getUser_id(), num.intValue());
                            builder.setLv(result.get(0));
                            builder.setExp(result.get(1));
                        } else {
                            iItem.updateItemInfo(headBallEntity.getUser_id(), id, num.intValue());
                        }
                    }
                    //也要更新数据库
                    //获得最大的关卡id
                    String jump_missionid = jedis.hget("headball:" + headBallEntity.getUser_id(), "jump_missionid");

                    StringBuffer hql = new StringBuffer();
                    hql.append("update HeadBallEntity set battle_num=").append(Integer.parseInt(map.get("battle_num")) + 1)
                            .append(",").append("jump_num=").append((Integer.parseInt(map.get("jump_num")) + headBallEntity.getJump_num()));

                    String threestar = jedis.hget("headball:" + headBallEntity.getUser_id(), "is_threestar");
                    if (Integer.parseInt(threestar) < headBallEntity.getIs_threestar()) {
                        hql.append(",is_threestar=").append(headBallEntity.getIs_threestar());
                    }

                    String missionid = jedis.hget("headball:" + headBallEntity.getUser_id(), "jump_missionid");
                    if (Integer.parseInt(missionid) < headBallEntity.getJump_missionid()) {
                        hql.append(",jump_missionid=").append(headBallEntity.getJump_missionid());
                    }
                    hql.append(" where user_id='").append(headBallEntity.getUser_id()).append("'");
                    MySql.updateSomes(hql.toString());

                    //更新redis
                    if (Integer.parseInt(jump_missionid) < headBallEntity.getJump_missionid()) {
                        map.put("jump_missionid", headBallEntity.getJump_missionid() + "");
                    } else {
                        map.put("jump_missionid", jump_missionid);
                    }
                    map.put("is_first", "1");
                    map.put("battle_num", (Integer.parseInt(map.get("battle_num")) + 1) + "");
                    map.put("jump_num", (Integer.parseInt(map.get("jump_num")) + headBallEntity.getJump_num()) + "");
                    map.put("user_id", headBallEntity.getUser_id());
                    String is_threestar = jedis.hget("headball:" + headBallEntity.getUser_id(), "is_threestar");
                    if (Integer.parseInt(is_threestar) < headBallEntity.getIs_threestar()) {
                        map.put("is_threestar", headBallEntity.getIs_threestar() + "");
                    } else {
                        map.put("is_threestar", is_threestar);
                    }

                    jedis.hmset("headball:" + headBallEntity.getUser_id(), map);

                }
               /* iTask.updateOneTask(headBallEntity.getUser_id(), 10001, 1);*/
                iTask.updateAchievement(headBallEntity.getUser_id(), 8, 1);

            } else {
                //失败只有失败金币和失败经验
                List<Integer> result = iLogin.updateRoleExp(headBallEntity.getUser_id(), exp);
                builder.setLv(result.get(0));
                builder.setExp(result.get(1));
                ItemData.Item item = itemList.get(0);
                int itemLose = new Double(item.getNum()).intValue();
                iItem.updateItemInfo(headBallEntity.getUser_id(), 1, itemLose);
                iTask.updateAchievement(headBallEntity.getUser_id(), 25, 1);
            }
            String maxjump_missionid = jedis.hget("headball:" + headBallEntity.getUser_id(), "jump_missionid");
            jedis.hset("role:" + headBallEntity.getUser_id(), "headballmissionid", maxjump_missionid);
            builder.setMissionId(Integer.parseInt(maxjump_missionid));
            builder.setIsSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
           builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
        }
        return builder;
    }

    //新请求结算返回
    public MissionData.ResponseCountHeadBall.Builder countJumpHeadBallMission(int missionId, String uid, int jumpnum, int starNum, Boolean isSuccess) {
        MissionData.ResponseCountHeadBall.Builder builder = MissionData.ResponseCountHeadBall.newBuilder();
        IMission iMission = MissionDao.getInstance();
        IItem iItem=ItemDao.getInstance();
        ITask iTask=TaskDao.getInstance();
        Map<String, String> jumpMissionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_JUMPMISSION, missionId);
        Redis jedis=Redis.getInstance();
        ILogin iLogin = LoginDao.getInstance();
        builder.setIsSuccess(isSuccess);
        builder.setStarLevel(starNum);
        if(isSuccess)
        {
            //成功
            //通关1-2发送邮件
            String mailjump_missionid = jedis.hget("headball:" + uid, "jump_missionid");
            if(mailjump_missionid!=null){
                if (Integer.parseInt(mailjump_missionid) < 2 && missionId == 2) {
                    IFriend iFriend = FriendDao.getInstance();
                    String mid = MyUtils.setRandom();
                    MessageInfo info = iFriend.addNewMessage(uid, 30, "", mid, TimerHandler.nowTimeStamp);
                    if (info != null) {
                        iFriend.reportMessage(mid, 30, "", uid);
                    }
                }
            }


            //判断是不是首通
            boolean first = iMission.isFirst(uid, missionId);

            String ss = jedis.hget("headball:" + uid, "jump_missionid");
            if(ss!=null){
                ///// System.out.println("不是首冲"+uid+"====="+missionId);
                if(missionId>=Integer.parseInt(ss)){
                   // /// System.out.println("执行修改"+uid);
                    StringBuffer hql = new StringBuffer();
                    hql.append(" update RoleEntity set headballmissionid = ").append(missionId).append(" where uid = '").append(uid).append("' ");
                    MySql.updateSomes(hql.toString());
                }
            }


            Map<String, String> map = jedis.hgetAll("headball:" + uid);
            if(first)
            {
                if (jumpMissionMap.get("first_item") != null && !jumpMissionMap.get("first_item").equals("0")) 
                {
                    String[] items = jumpMissionMap.get("first_item").split("\\|");
                    for (int i = 0; i < items.length; i++) 
                    {
                        if (items[i].equals("")) 
                        {
                            continue;
                        }

                        double rate = Double.parseDouble(items[i].split(",")[2]);
                        double rand = Math.random();
                        if (rand <= rate) 
                        {
                            int id = Integer.parseInt(items[i].split(",")[0]);
                            int num = Integer.parseInt(items[i].split(",")[1]);
                            Map<String, String> itemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, id);
                            int dress_type = Integer.parseInt(itemMap.get("dress_type"));
                            if (dress_type == 1) 
                            {
                                String get_id = itemMap.get("get_id");
                                String[] strArray = get_id.split("\\|");
                                int dressId = Integer.parseInt(strArray[0]);
                                int cType = Integer.parseInt(strArray[1]);

                                ItemData.Item.Builder showItemitemClothing = ItemData.Item.newBuilder();
                                showItemitemClothing.setId(id);
                                showItemitemClothing.setNum(1);
                                builder.addShowItem(showItemitemClothing);
                                                                
                                List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
                                awardClothing(uid, dressId, cType, utilityList);
                                builder.addAllClothingList(utilityList);
                                continue;
                            }
                            if (id == 25) 
                            {
                                List<Integer> result = iLogin.updateRoleExp(uid, num);
                                builder.setLv(result.get(0));
                                builder.setExp(result.get(1));

                                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                                showBu.setId(25);
                                showBu.setNum(num);
                                builder.addShowItem(showBu);
                            } 
                            else 
                            {
                                double totalNum = iItem.updateItemInfo(uid, id, num);
                                if (totalNum > 0) 
                                {
                                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                                    itemBu.setId(id);
                                    itemBu.setNum(totalNum);
                                    builder.addItem(itemBu);
    
                                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                                    showBu.setId(id);
                                    showBu.setNum(num);
                                    builder.addShowItem(showBu);
                                }
                            }
                        }

                    }
                }

                //首通成功之后,插入数据库
                map.put("is_first", "1");
                map.put("battle_num", "1");
                map.put("jump_num", jumpnum + "");
                map.put("jump_missionid", missionId + "");
                map.put("user_id", uid);
                map.put("is_threestar", starNum + "");
                jedis.hmset("headball:" + uid, map);
                //插入数据库
                HeadBallEntity ballEntity = new HeadBallEntity();
                ballEntity.setIs_first(1);
                ballEntity.setBattle_num(1);
                ballEntity.setJump_num(jumpnum);
                ballEntity.setJump_missionid(missionId);
                ballEntity.setUser_id(uid);
                ballEntity.setIs_threestar(starNum);
                MySql.insert(ballEntity);                
            }
            else 
            {
                //非首通
                //判断是1星,2星,3星,给不同的奖励
                String StarAware = "";
                switch (starNum)
                {
                    case 1:
                       // 1星奖励
                       StarAware = jumpMissionMap.get("stars_3");
                        break;
                    case 2:
                        //2星奖励
                        StarAware = jumpMissionMap.get("stars_2");
                     break;
                    case 3:
                        //3星奖励
                        StarAware = jumpMissionMap.get("stars_1");
                     break;
                }

                String[] items = StarAware.split("\\|");
                for (int i = 0; i < items.length; i++) 
                {
                   String itemString =items[i];
                //    if(itemString.isEmpty())
                    if(StringUtils.isEmpty(itemString))
                    {
                        continue;
                    }
                    double rate = Double.parseDouble(items[i].split(",")[2]);
                    double rand = Math.random();
                    if (rand <= rate) 
                    {
                        int id = Integer.parseInt(items[i].split(",")[0]);
                        int num = Integer.parseInt(items[i].split(",")[1]);

                        if(id==25)
                        {
                            List<Integer> result = iLogin.updateRoleExp(uid, num);
                            builder.setLv(result.get(0));
                            builder.setExp(result.get(1));
                            ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                            showBu.setId(25);
                            showBu.setNum(num);
                            builder.addShowItem(showBu);
                        }   
                        else
                        {                 
                            double totalNum = iItem.updateItemInfo(uid, id, num);
                            if (totalNum > 0) 
                            {
                                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                                itemBu.setId(id);
                                itemBu.setNum(totalNum);
                                builder.addItem(itemBu);

                                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                                showBu.setId(id);
                                showBu.setNum(num);
                                builder.addShowItem(showBu);
                            }
                        }
                    }
                }                
                //也要更新数据库
                //获得最大的关卡id
                String jump_missionid = jedis.hget("headball:" + uid, "jump_missionid");

                StringBuffer hql = new StringBuffer();
                hql.append("update HeadBallEntity set battle_num=").append(Integer.parseInt(map.get("battle_num")) + 1)
                        .append(",").append("jump_num=").append((Integer.parseInt(map.get("jump_num")) + jumpnum));

                String threestar = jedis.hget("headball:" + uid, "is_threestar");
                if (Integer.parseInt(threestar) < starNum) {
                    hql.append(",is_threestar=").append(starNum);
                }

                String missionid = jedis.hget("headball:" + uid, "jump_missionid");
                if (Integer.parseInt(missionid) < missionId) {
                    hql.append(",jump_missionid=").append(missionId);
                }
                hql.append(" where user_id='").append(uid).append("'");
                MySql.updateSomes(hql.toString());

                //更新redis
                if (Integer.parseInt(jump_missionid) < missionId) {
                    map.put("jump_missionid", missionId + "");
                } else {
                    map.put("jump_missionid", jump_missionid);
                }
                map.put("is_first", "1");
                map.put("battle_num", (Integer.parseInt(map.get("battle_num")) + 1) + "");
                map.put("jump_num", (Integer.parseInt(map.get("jump_num")) + jumpnum) + "");
                map.put("user_id", uid);
                String is_threestar = jedis.hget("headball:" + uid, "is_threestar");
                if (Integer.parseInt(is_threestar) < starNum) {
                    map.put("is_threestar", starNum + "");
                } else {
                    map.put("is_threestar", is_threestar);
                }
                jedis.hmset("headball:" + uid, map);
                jedis.hmset("headball:" + uid, map);
                /*iTask.updateOneTask(uid, 10001, 1);*/
                iTask.updateAchievement(uid, 8, 1);

            }
        }
        else 
        {
            //失败
            String strItem = jumpMissionMap.get("item_lose");
            String[] strItemArray = strItem.split(",");
            int itemLose = Integer.parseInt(strItemArray[1]);
            double totalNum = iItem.updateItemInfo(uid, 1, itemLose);
            if (totalNum > 0) {
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(1);
                itemBu.setNum(totalNum);
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(1);
                showBu.setNum(itemLose);
                builder.addShowItem(showBu);
            }
            strItem = jumpMissionMap.get("exp_lose");
            strItemArray = strItem.split(",");
            int exp = Integer.parseInt(strItemArray[0]);
            if (exp > 0) {
                List<Integer> result = iLogin.updateRoleExp(uid, exp);
                builder.setLv(result.get(0));
                builder.setExp(result.get(1));
                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(25);
                showBu.setNum(exp);
                builder.addShowItem(showBu);
            }
            iTask.updateAchievement(uid, 25, 1);
        }
        String maxjump_missionid = jedis.hget("headball:" + uid, "jump_missionid");
        jedis.hset("role:" + uid, "headballmissionid", maxjump_missionid);
        builder.setMissionId(Integer.parseInt(maxjump_missionid));
        return builder;
    }


    //金币模式
    public MissionData.ResponseCountGold.Builder countGold(List<ItemData.Item> itemList,String uid){
        Redis jedis = Redis.getInstance();
        MissionData.ResponseCountGold.Builder builder = MissionData.ResponseCountGold.newBuilder();
        builder.setIsOk(true);
        ItemDao itemdao=ItemDao.getInstance();
        for(ItemData.Item obj:itemList){
            itemdao.updateItemInfo(uid,obj.getId(),(int)obj.getNum());
        }
        List<ItemData.Item> items= itemdao.getBag(uid);
        List<Object> itemsList=MySql.queryForList(" FROM ItemEntity where uid='"+uid+"'");
        ItemData.Item.Builder item=null;
        if(itemsList.size()==0){
            for(ItemData.Item obj:itemList){
                if(obj.getId()==1){
                    continue;
                }
                item=ItemData.Item.newBuilder();
                item.setId(obj.getId());
                item.setNum(obj.getNum());
                items.add(item.build());
            }
        }else {
            for(ItemData.Item obj:itemList){
                if(obj.getId()==1){
                    continue;
                }
                item=ItemData.Item.newBuilder();
                item.setId(obj.getId());
                StringBuffer sql=new StringBuffer(" FROM ItemEntity where uid = '").append(uid).append("' and itemid= ").append(obj.getId()).append(" ");
                ItemEntity dress=(ItemEntity)MySql.queryForOne(sql.toString());
                if(dress==null){
                    item.setNum(obj.getNum());
                }else {
                    item.setNum(dress.getItemnum()+obj.getNum());
                }
                items.add(item.build());
            }
        }
        builder.addAllHeadItem(items);
        return builder;
    }
    public MissionData.ResponseCountStack.Builder countStack(MissionData.RequestCountStack request,String uid){
        MissionData.ResponseCountStack.Builder builder = MissionData.ResponseCountStack.newBuilder();
        ItemDao itemdao=ItemDao.getInstance();
        //ILogin iLogin = LoginDao.getInstance();
        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_STACKBALL, request.getMissionId());
        if(request.getGamePattern()==1){ //1顶球模式  其他为测试模式
            String[] arrayReward = missionMap.get("get_probability").split("\\|");
        /*   int exp = Integer.parseInt(missionMap.get("exp"));
            int cion = Integer.parseInt(missionMap.get("coin").split(",")[1]);*/
            int exp=100;
            int coin=100;
            int itemNum = request.getItemNum();

            String item = missionMap.get("item_id");

            List<Integer> listBallCount = request.getBallCountList();
            int BallCount = 0;
            int BallID = 0;
            for (int i = 0; i < listBallCount.size(); i++) {
                BallCount = listBallCount.get(i);

                BallID = Integer.parseInt(arrayReward[i]);
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(BallID);
                itemBu.setNum(itemdao.updateItemInfo(uid, BallID, BallCount));
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(BallID);
                showBu.setNum(BallCount);
                builder.addShowItem(showBu);
            }

            if (itemNum != 0) {
                Integer id = Integer.valueOf(item.split(",")[0]);
                Integer number = Integer.valueOf(item.split(",")[1]);
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(id);
                itemBu.setNum(itemdao.updateItemInfo(uid, id, number));
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(id);
                showBu.setNum(number);
                builder.addShowItem(showBu);
            }
            if (coin > 0) {
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(1);
                itemBu.setNum(itemdao.updateItemInfo(uid, 1, coin));
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(1);
                showBu.setNum(coin);
                builder.addShowItem(showBu);
            }
            if (exp > 0) {
                ILogin iLogin = LoginDao.getInstance();
                List<Integer> result = iLogin.updateRoleExp(uid, exp);
                builder.setLv(result.get(0));
                builder.setExp(result.get(1));
                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(25);
                showBu.setNum(exp);
                builder.addShowItem(showBu);
            }
        }else { //测试游戏模式
            long nowTotalCount=Long.parseLong(missionMap.get("exp"));//当前总目标数
            //是否双倍奖励活动时间
            boolean isLuckyTime=isLuckyTime();
            if (request.getIsSuccess() == true) {   //表示游戏成功
              /*  String[] arrayReward = missionMap.get("item_id").split("\\|");
                int exp = Integer.parseInt(missionMap.get("exp").split(",")[1]);
                int cion = Integer.parseInt(missionMap.get("coin").split(",")[1]);*/
              /*  成功公式
                100*（人物当前等级*0.1）*（当前局的目标总数/10）*（当前完成的目标/10）*5

                人物等级需要读取当前角色等级
                        当前总目标数=读取表里面exp的字段
                当前完成的目标数=读取当前关卡完成的进度（成功的话是和总目标数一样，失败的话是不一样的）*/
                int itemNum = request.getItemNum();
                String item = missionMap.get("item_id");
                RoleEntity roleEntity = null;
                StringBuffer sql = new StringBuffer(" from RoleEntity where uid='").append(uid).append("' and serverid =0 ");
                Object object = MySql.queryForOne(sql.toString());
                if (object != null) {
                    roleEntity = (RoleEntity) object;
                }
                int lv = roleEntity.getLv();
                int coin = Math.round((long) (100 * (lv * 0.1) * (nowTotalCount * 0.1) * (nowTotalCount * 0.1) * 5));
                int exp = Math.round((long) (100 * (lv * 0.1) * (nowTotalCount * 0.1) * (nowTotalCount * 0.1) * 5));

                // if (itemNum != 0)
                // {
                //     Integer id = Integer.valueOf(item.split(",")[0]);
                //     Integer number = Integer.valueOf(item.split(",")[1]);
                //     ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                //     itemBu.setId(id);
                //     itemBu.setNum(itemdao.updateItemInfo(uid, id, number));
                //     builder.addItem(itemBu);

                //     ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                //     showBu.setId(id);
                //     showBu.setNum(number);
                //     builder.addShowItem(showBu);

                List<RewardInfo> rewardInfoList=new ArrayList<RewardInfo>();//获得的奖励
                // }
                if (coin > 0) {   //金币奖励
                    if(isLuckyTime){
                        coin*=2;
                    }
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    itemBu.setId(1);
                    itemBu.setNum(itemdao.updateItemInfo(uid, 1, coin));
                    builder.addItem(itemBu);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(1);
                    showBu.setNum(coin);
                    builder.addShowItem(showBu);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(1);
                    rewardInfo.setItemnums(coin);
                    rewardInfo.setItemtotal(new Double(itemBu.getNum()).intValue());
                    rewardInfoList.add(rewardInfo);
                }
                if (exp > 0) {   // 经验奖励
                    if (isLuckyTime){
                        exp*=2;
                    }
                    ILogin iLogin = LoginDao.getInstance();
                    List<Integer> result = iLogin.updateRoleExp(uid, exp);
                    builder.setLv(result.get(0));
                    builder.setExp(result.get(1));
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(25);
                    showBu.setNum(exp);
                    builder.addShowItem(showBu);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(25);
                    rewardInfo.setItemnums(exp);
                    rewardInfo.setItemtotal(result.get(1));
                    rewardInfoList.add(rewardInfo);
                }

                String Reward = missionMap.get("item_id");
                String allReward = Reward + "|" + missionMap.get("other_item");
                String[] levelReward = allReward.split("\\|");

                String concreteLevelReward = levelReward[0];
                int levelRewardId = 0;
                int levelRewardNumber = 0;

                levelRewardId = Integer.parseInt(concreteLevelReward.split(",")[0]);
                levelRewardNumber = Integer.parseInt(concreteLevelReward.split(",")[1]);

                if (levelRewardId != 0) {
                    ItemData.Item.Builder itemBu2 = ItemData.Item.newBuilder();
                    itemBu2.setId(levelRewardId);
                    if(isLuckyTime){
                        levelRewardNumber*=2;
                    }
                    itemBu2.setNum(itemdao.updateItemInfo(uid, levelRewardId, levelRewardNumber));
                    builder.addItem(itemBu2);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(levelRewardId);
                    rewardInfo.setItemnums(levelRewardNumber);
                    rewardInfo.setItemtotal(new Double(itemBu2.getNum()).intValue());
                    rewardInfoList.add(rewardInfo);
                    ItemData.Item.Builder showBu2 = ItemData.Item.newBuilder();
                    showBu2.setId(levelRewardId);
                    showBu2.setNum(levelRewardNumber);
                    builder.addShowItem(showBu2);
                }
                int total = Integer.parseInt(missionMap.get("progress_add"));
                int base = total / levelReward.length;

                int index = request.getFinishLevel() / base;
                if (index > levelReward.length - 1)
                    index = levelReward.length - 1;

                levelRewardId = 0;
                levelRewardNumber = 0;
                Random random = new Random();
                int rate = random.nextInt(100);
                int condition = Integer.parseInt(missionMap.get("percentage"));
                if (index != 0 && rate < condition) {

                    concreteLevelReward = levelReward[index];
                    levelRewardId = Integer.parseInt(concreteLevelReward.split(",")[0]);
                    levelRewardNumber = Integer.parseInt(concreteLevelReward.split(",")[1]);

                    if (levelRewardId != 0) {
                        if(isLuckyTime){
                            levelRewardNumber*=2;
                        }
                        ItemData.Item.Builder itemBu2 = ItemData.Item.newBuilder();
                        itemBu2.setId(levelRewardId);
                        itemBu2.setNum(itemdao.updateItemInfo(uid, levelRewardId, levelRewardNumber));
                        builder.addItem(itemBu2);
                        RewardInfo rewardInfo =new RewardInfo();
                        rewardInfo.setItemid(levelRewardId);
                        rewardInfo.setItemnums(levelRewardNumber);
                        rewardInfo.setItemtotal(new Double(itemBu2.getNum()).intValue());
                        rewardInfoList.add(rewardInfo);
                        ItemData.Item.Builder showBu2 = ItemData.Item.newBuilder();
                        showBu2.setId(levelRewardId);
                        showBu2.setNum(levelRewardNumber);
                        builder.addShowItem(showBu2);
                    }
                }
                GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录关卡结算奖励
                gameRecordEntity.setGamepattern(1);
                gameRecordEntity.setMissionid(request.getMissionId());
                gameRecordEntity.setType(1);
                gameRecordEntity.setUid(uid);
                StringBuffer rewardString=new StringBuffer("");
                for(int i=0;i<rewardInfoList.size();i++){
                    rewardString.append(MyUtils.objectToJson(rewardInfoList.get(i)));
                    if(i!=(rewardInfoList.size()-1)){
                        rewardString.append(",");
                    }
                }
                gameRecordEntity.setReward(rewardString.toString());
                MySql.insert(gameRecordEntity);
                Redis jedis = Redis.getInstance();
                //记录关卡通关情况
                StringBuffer hql = new StringBuffer(" from MissionEntity where type=6 and uid='").append(uid).append("'").append("and missionid=").append(request.getMissionId());
                List<Object> list = MySql.queryForList(hql.toString());
                if (list.size() == 0) {
                    MissionEntity mission = new MissionEntity();
                    mission.setUid(uid);
                    mission.setMissionid(request.getMissionId());
                    mission.setNums(1);
                    mission.setType(1);
                    MySql.insert(mission);
                    jedis.hset("rolemission:" + uid + "#" + 6, request.getMissionId() + "", "1");
                } else {
                    hql = new StringBuffer(" update MissionEntity set nums=(nums+1)").append("where type=6 and uid='").append(uid).append("' and missionid=").append(request.getMissionId());
                    MySql.updateSomes(hql.toString());
                    String missionStr = jedis.hget("rolemission:" + uid + "#" + 6, request.getMissionId() + "");
                    jedis.hset("rolemission:" + uid + "#" + 6, request.getMissionId() + "", Integer.parseInt(missionStr) + 1 + "");
                }




                //判断关卡完成进度
                String unlock= missionMap.get("Unlock");
                int advance=Integer.parseInt(unlock.split("\\|")[0]);
                Redis  redis= Redis.getInstance();
                Map<String,String> mission = redis.hgetAll("rolemission:" + uid+"#"+6);
                String  headballadvance=redis.hget("role:"+uid,"headballadvance");
                if(Integer.parseInt(headballadvance)==advance) {
                    StringBuffer update = new StringBuffer("update RoleEntity set headballadvance=").append(advance + 1)
                            .append("where uid='").append(uid).append("'");
                    MySql.updateSomes(update.toString());
                    redis.hset("role:" + uid, "headballadvance", advance + 1 + "");
                    builder.setAdvance(advance + 1);
                }else{
                    builder.setAdvance(Integer.parseInt(headballadvance));
                }
                String totalCount= missionMap.get("add_item");//获取成功是消除的个数
                String[] totalCountArray= totalCount.split("\\|");
                int  finishCount=0;
                for(int i=0;i<totalCountArray.length;i++){
                    finishCount=Integer.parseInt(totalCountArray[i].split(",")[1])+finishCount;
                }

            }
            else
            {
             /*   int exp = Integer.parseInt(missionMap.get("exp_lose").split(",")[1]);
                int cion = Integer.parseInt(missionMap.get("coin_lose").split(",")[1]);*/
              /*  失败的公式：
                100*（当前总目标数/10）*（当前完成的目标数/10）*/
                int itemNum = request.getItemNum();
                int exp =Math.round((long)(100*(nowTotalCount*0.1)*(request.getNowFinishAmount()*0.1)));
                int coin=Math.round((long)(100*(nowTotalCount*0.1)*(request.getNowFinishAmount()*0.1)));
                // String item = missionMap.get("item_id");
                // if (itemNum != 0) {
                //     Integer id = Integer.valueOf(item.split(",")[0]);
                //     Integer number = Integer.valueOf(item.split(",")[1]);
                //     ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                //     itemBu.setId(id);
                //     itemBu.setNum(itemdao.updateItemInfo(uid, id, number));
                //     builder.addItem(itemBu);

                //     ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                //     showBu.setId(id);
                //     showBu.setNum(number);
                //     builder.addShowItem(showBu);
                // }
                List<RewardInfo> rewardInfoList=new ArrayList<RewardInfo>();//获得的奖励
                if (coin >= 0)
                {
                    if(isLuckyTime){
                        coin*=2;
                    }
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    itemBu.setId(1);
                    itemBu.setNum(itemdao.updateItemInfo(uid, 1, coin));
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(1);
                    rewardInfo.setItemnums(coin);
                    rewardInfo.setItemtotal(new Double(itemBu.getNum()).intValue());
                    rewardInfoList.add(rewardInfo);
                    if(coin==0){
                        Redis redis= Redis.getInstance();
                        String key="roleitem:"+uid+"#0";
                        String gold= redis.hget(key,1+"");
                        itemBu.setNum(Double.parseDouble(gold));
                    }
                    builder.addItem(itemBu);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(1);
                    showBu.setNum(coin);
                    builder.addShowItem(showBu);
                }
                if (exp >=0)
                {
                    if(isLuckyTime){
                        exp*=2;
                    }
                    ILogin iLogin = LoginDao.getInstance();
                    List<Integer> result = iLogin.updateRoleExp(uid, exp);
                    builder.setLv(result.get(0));
                    builder.setExp(result.get(1));
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(25);
                    rewardInfo.setItemnums(exp);
                    rewardInfo.setItemtotal(result.get(1));
                    rewardInfoList.add(rewardInfo);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(25);
                    showBu.setNum(exp);
                    builder.addShowItem(showBu);
                }
                GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录关卡结算奖励
                gameRecordEntity.setGamepattern(1);
                gameRecordEntity.setMissionid(request.getMissionId());
                gameRecordEntity.setType(2);
                gameRecordEntity.setUid(uid);
                StringBuffer rewardString=new StringBuffer("");
                for(int i=0;i<rewardInfoList.size();i++){
                    rewardString.append(MyUtils.objectToJson(rewardInfoList.get(i)));
                    if(i!=(rewardInfoList.size()-1)){
                        rewardString.append(",");
                    }
                }
                gameRecordEntity.setReward(rewardString.toString());
                MySql.insert(gameRecordEntity);
            }
            Redis  redis =Redis.getInstance();
            String  headballadvance=redis.hget("role:"+uid,"headballadvance");
            builder.setAdvance(Integer.parseInt(headballadvance));
        }
        builder.setIsOk(true);
      //  /// System.out.println("stackball");
        return builder;

    }
    //落球模式
    public MissionData.ResponseCountDown.Builder countDown(MissionData.RequestCountDown request,String uid){ /*Integer itemNum,String uid,Integer missionId*/
        MissionData.ResponseCountDown.Builder builder = MissionData.ResponseCountDown.newBuilder();
        ItemDao itemdao=ItemDao.getInstance();
        //ILogin iLogin = LoginDao.getInstance();
        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_FULLINGBALL, request.getMissionId());
        if(request.getGamePattern()==1){ //1顶球模式  其他为测试模式
            String[] arrayReward = missionMap.get("get_probability").split("\\|");
        /*   int exp = Integer.parseInt(missionMap.get("exp"));
            int cion = Integer.parseInt(missionMap.get("coin").split(",")[1]);*/
             int exp=100;
             int coin=100;
            int itemNum = request.getItemNum();

            String item = missionMap.get("item_id");

            List<Integer> listBallCount = request.getBallCountList();
            int BallCount = 0;
            int BallID = 0;
            for (int i = 0; i < listBallCount.size(); i++) {
                BallCount = listBallCount.get(i);

                BallID = Integer.parseInt(arrayReward[i]);
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(BallID);
                itemBu.setNum(itemdao.updateItemInfo(uid, BallID, BallCount));
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(BallID);
                showBu.setNum(BallCount);
                builder.addShowItem(showBu);
            }

            if (itemNum != 0) {
                Integer id = Integer.valueOf(item.split(",")[0]);
                Integer number = Integer.valueOf(item.split(",")[1]);
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(id);
                itemBu.setNum(itemdao.updateItemInfo(uid, id, number));
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(id);
                showBu.setNum(number);
                builder.addShowItem(showBu);
            }
            if (coin > 0) {
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(1);
                itemBu.setNum(itemdao.updateItemInfo(uid, 1, coin));
                builder.addItem(itemBu);

                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(1);
                showBu.setNum(coin);
                builder.addShowItem(showBu);
            }
            if (exp > 0) {
                ILogin iLogin = LoginDao.getInstance();
                List<Integer> result = iLogin.updateRoleExp(uid, exp);
                builder.setLv(result.get(0));
                builder.setExp(result.get(1));
                ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                showBu.setId(25);
                showBu.setNum(exp);
                builder.addShowItem(showBu);
            }
        }else { //测试游戏模式
            long nowTotalCount=Long.parseLong(missionMap.get("exp"));//当前总目标数
          boolean  isLuckyTime=isLuckyTime();
            if (request.getIsSuccess() == true) {   //表示游戏成功
              /*  String[] arrayReward = missionMap.get("item_id").split("\\|");
                int exp = Integer.parseInt(missionMap.get("exp").split(",")[1]);
                int cion = Integer.parseInt(missionMap.get("coin").split(",")[1]);*/
              /*  成功公式
                100*（人物当前等级*0.1）*（当前局的目标总数/10）*（当前完成的目标/10）*5

                人物等级需要读取当前角色等级
                        当前总目标数=读取表里面exp的字段
                当前完成的目标数=读取当前关卡完成的进度（成功的话是和总目标数一样，失败的话是不一样的）*/
                int itemNum = request.getItemNum();
                String item = missionMap.get("item_id");
                RoleEntity roleEntity = null;
                StringBuffer sql = new StringBuffer(" from RoleEntity where uid='").append(uid).append("' and serverid =0 ");
                Object object = MySql.queryForOne(sql.toString());
                if (object != null) {
                    roleEntity = (RoleEntity) object;
                }
                int lv = roleEntity.getLv();
                int coin = Math.round((long) (100 * (lv * 0.1) * (nowTotalCount * 0.1) * (nowTotalCount * 0.1) * 5));
                int exp = Math.round((long) (100 * (lv * 0.1) * (nowTotalCount * 0.1) * (nowTotalCount * 0.1) * 5));

                // if (itemNum != 0)
                // {
                //     Integer id = Integer.valueOf(item.split(",")[0]);
                //     Integer number = Integer.valueOf(item.split(",")[1]);
                //     ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                //     itemBu.setId(id);
                //     itemBu.setNum(itemdao.updateItemInfo(uid, id, number));
                //     builder.addItem(itemBu);

                //     ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                //     showBu.setId(id);
                //     showBu.setNum(number);
                //     builder.addShowItem(showBu);

              List<RewardInfo> rewardInfoList=new ArrayList<RewardInfo>();//获得的奖励
                // }
                if (coin > 0) {   //金币奖励
                    if(isLuckyTime){
                        coin*=2;
                    }
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    itemBu.setId(1);
                    itemBu.setNum(itemdao.updateItemInfo(uid, 1, coin));
                    builder.addItem(itemBu);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(1);
                    showBu.setNum(coin);
                    builder.addShowItem(showBu);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(1);
                    rewardInfo.setItemnums(coin);
                    rewardInfo.setItemtotal(new Double(itemBu.getNum()).intValue());
                    rewardInfoList.add(rewardInfo);
                }
                if (exp > 0) {   // 经验奖励
                    if(isLuckyTime){
                        exp*=2;
                    }
                    ILogin iLogin = LoginDao.getInstance();
                    List<Integer> result = iLogin.updateRoleExp(uid, exp);
                    builder.setLv(result.get(0));
                    builder.setExp(result.get(1));
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(25);
                    showBu.setNum(exp);
                    builder.addShowItem(showBu);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(25);
                    rewardInfo.setItemnums(exp);
                    rewardInfo.setItemtotal(result.get(1));
                    rewardInfoList.add(rewardInfo);
                }

                String Reward = missionMap.get("item_id");
                String allReward = Reward + "|" + missionMap.get("other_item");
                String[] levelReward = allReward.split("\\|");

                String concreteLevelReward = levelReward[0];
                int levelRewardId = 0;
                int levelRewardNumber = 0;

                levelRewardId = Integer.parseInt(concreteLevelReward.split(",")[0]);
                levelRewardNumber = Integer.parseInt(concreteLevelReward.split(",")[1]);

                if (levelRewardId != 0) {
                    ItemData.Item.Builder itemBu2 = ItemData.Item.newBuilder();
                    itemBu2.setId(levelRewardId);
                    if (isLuckyTime){
                        levelRewardNumber*=2;
                    }
                    itemBu2.setNum(itemdao.updateItemInfo(uid, levelRewardId, levelRewardNumber));
                    builder.addItem(itemBu2);
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(levelRewardId);
                    rewardInfo.setItemnums(levelRewardNumber);
                    rewardInfo.setItemtotal(new Double(itemBu2.getNum()).intValue());
                    rewardInfoList.add(rewardInfo);
                    ItemData.Item.Builder showBu2 = ItemData.Item.newBuilder();
                    showBu2.setId(levelRewardId);
                    showBu2.setNum(levelRewardNumber);
                    builder.addShowItem(showBu2);
                }
                int total = Integer.parseInt(missionMap.get("progress_add"));
                int base = total / levelReward.length;

                int index = request.getFinishLevel() / base;
                if (index > levelReward.length - 1)
                    index = levelReward.length - 1;

                levelRewardId = 0;
                levelRewardNumber = 0;
                Random random = new Random();
                int rate = random.nextInt(100);
                int condition = Integer.parseInt(missionMap.get("percentage"));
                if (index != 0 && rate < condition) {

                    concreteLevelReward = levelReward[index];
                    levelRewardId = Integer.parseInt(concreteLevelReward.split(",")[0]);
                    levelRewardNumber = Integer.parseInt(concreteLevelReward.split(",")[1]);

                    if (levelRewardId != 0) {
                        if(isLuckyTime){
                            levelRewardNumber*=2;
                        }
                        ItemData.Item.Builder itemBu2 = ItemData.Item.newBuilder();
                        itemBu2.setId(levelRewardId);
                        itemBu2.setNum(itemdao.updateItemInfo(uid, levelRewardId, levelRewardNumber));
                        builder.addItem(itemBu2);
                        RewardInfo rewardInfo =new RewardInfo();
                        rewardInfo.setItemid(levelRewardId);
                        rewardInfo.setItemnums(levelRewardNumber);
                        rewardInfo.setItemtotal(new Double(itemBu2.getNum()).intValue());
                        rewardInfoList.add(rewardInfo);
                        ItemData.Item.Builder showBu2 = ItemData.Item.newBuilder();
                        showBu2.setId(levelRewardId);
                        showBu2.setNum(levelRewardNumber);
                        builder.addShowItem(showBu2);
                    }
                }
                GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录关卡结算奖励
                gameRecordEntity.setGamepattern(1);
                gameRecordEntity.setMissionid(request.getMissionId());
                gameRecordEntity.setType(1);
                gameRecordEntity.setUid(uid);
                  StringBuffer rewardString=new StringBuffer("");
               for(int i=0;i<rewardInfoList.size();i++){
                   rewardString.append(MyUtils.objectToJson(rewardInfoList.get(i)));
                   if(i!=(rewardInfoList.size()-1)){
                       rewardString.append(",");
                   }
               }
                gameRecordEntity.setReward(rewardString.toString());
                MySql.insert(gameRecordEntity);
                Redis jedis = Redis.getInstance();
                //判断是否是新手引导结算
                if(request.getMissionId()==1) {
                    String advance = jedis.hget("role:" + uid, "advance");
                    if (Integer.parseInt(advance) <=5) {
                        builder.setIsOk(true);
                        builder.setAdvance(1);
                      return builder;
                    }
                }
                //记录关卡通关情况
                StringBuffer hql = new StringBuffer(" from MissionEntity where type=1 and uid='").append(uid).append("'").append("and missionid=").append(request.getMissionId());
                List<Object> list = MySql.queryForList(hql.toString());
                if (list.size() == 0) {
                    MissionEntity mission = new MissionEntity();
                    mission.setUid(uid);
                    mission.setMissionid(request.getMissionId());
                    mission.setNums(1);
                    mission.setType(1);
                    MySql.insert(mission);
                    jedis.hset("rolemission:" + uid + "#" + 1, request.getMissionId() + "", "1");
                } else {
                    hql = new StringBuffer(" update MissionEntity set nums=(nums+1)").append("where type=1 and uid='").append(uid).append("' and missionid=").append(request.getMissionId());
                    MySql.updateSomes(hql.toString());
                    String missionStr = jedis.hget("rolemission:" + uid + "#" + 1, request.getMissionId() + "");
                    jedis.hset("rolemission:" + uid + "#" + 1, request.getMissionId() + "", Integer.parseInt(missionStr) + 1 + "");
                }

                ITask task = TaskDao.getInstance();
                task.updateOneTask(uid, 10012, 1);
                //stage为30的成就
                int firstTaskId=20106;
               String  nowTask =jedis.hget("roletask:"+uid,20106-1+request.getMissionId()+"");
               if(nowTask!=null){
                       task.updateAchievement(uid,30,1);
               }
                task.updateAchievement(uid, 29, 1);
               //主线成就2
                String missionStr = jedis.hget("rolemission:" + uid + "#" + 1, request.getMissionId() + "");
                int missionId= request.getMissionId();
                if(missionId==3){
                    task.updateMainAchievement(uid,3,1);
                }
                //主线成就5
                String  json= jedis.hget("roletask:"+uid,"30006");
                if(json!=null&&json.length()>0){
                    TaskInfo taskInfo=(TaskInfo) MyUtils.jsonToBean(json,TaskInfo.class);
                    long dressNum=Long.parseLong(Long.toBinaryString(taskInfo.getTaskNowNum()));
                    if(missionId==4&&(dressNum&4)==0){
                        task.updateMainAchievement(uid,6,4);
                    }
                    if(missionId==5&&(dressNum&2)==0){
                        task.updateMainAchievement(uid,6,2);
                    }
                    if(missionId==6&&(dressNum&1)==0){
                        task.updateMainAchievement(uid,6,1);
                    }
                }
                //判断关卡完成进度
               String unlock= missionMap.get("Unlock");
               int advance=Integer.parseInt(unlock.split("\\|")[0]);
                Redis  redis= Redis.getInstance();
                //Map<String,String> mission = redis.hgetAll("rolemission:" + uid+"#"+1);
                String  headballadvance=redis.hget("role:"+uid,"headballadvance");
                if(Integer.parseInt(headballadvance)==advance) {
                        StringBuffer update = new StringBuffer("update RoleEntity set headballadvance=").append(advance + 1)
                                .append("where uid='").append(uid).append("'");
                        MySql.updateSomes(update.toString());
                        redis.hset("role:" + uid, "headballadvance", advance + 1 + "");
                        builder.setAdvance(advance + 1);
                        //每次更新关卡后收集箱更新数据
                   LoginDao.getInstance().waitItem(uid);


                }else{
                    builder.setAdvance(Integer.parseInt(headballadvance));
                }
                String totalCount= missionMap.get("add_item");//获取成功是消除的个数
               String[] totalCountArray= totalCount.split("\\|");
              int  finishCount=0;
               for(int i=0;i<totalCountArray.length;i++){
                   finishCount=Integer.parseInt(totalCountArray[i].split(",")[1])+finishCount;
               }
                task.updateAchievement(uid,31,finishCount);
             //  /// System.out.println("~~~~~"+finishCount);
            }
            else 
            {
             /*   int exp = Integer.parseInt(missionMap.get("exp_lose").split(",")[1]);
                int cion = Integer.parseInt(missionMap.get("coin_lose").split(",")[1]);*/
              /*  失败的公式：
                100*（当前总目标数/10）*（当前完成的目标数/10）*/
                int itemNum = request.getItemNum();
                int exp =Math.round((long)(100*(nowTotalCount*0.1)*(request.getNowFinishAmount()*0.1)));
                int coin=Math.round((long)(100*(nowTotalCount*0.1)*(request.getNowFinishAmount()*0.1)));
                // String item = missionMap.get("item_id");
                // if (itemNum != 0) {
                //     Integer id = Integer.valueOf(item.split(",")[0]);
                //     Integer number = Integer.valueOf(item.split(",")[1]);
                //     ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                //     itemBu.setId(id);
                //     itemBu.setNum(itemdao.updateItemInfo(uid, id, number));
                //     builder.addItem(itemBu);

                //     ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                //     showBu.setId(id);
                //     showBu.setNum(number);
                //     builder.addShowItem(showBu);
                // }
                List<RewardInfo> rewardInfoList=new ArrayList<RewardInfo>();//获得的奖励
                if (coin >= 0)
                {
                    if(isLuckyTime){
                        coin*=2;
                    }
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    itemBu.setId(1);
                    itemBu.setNum(itemdao.updateItemInfo(uid, 1, coin));
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(1);
                    rewardInfo.setItemnums(coin);
                    rewardInfo.setItemtotal(new Double(itemBu.getNum()).intValue());
                    rewardInfoList.add(rewardInfo);
                    if(coin==0){
                        Redis redis= Redis.getInstance();
                        String key="roleitem:"+uid+"#0";
                        String gold= redis.hget(key,1+"");
                        itemBu.setNum(Double.parseDouble(gold));
                    }
                    builder.addItem(itemBu);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(1);
                    showBu.setNum(coin);
                    builder.addShowItem(showBu);
                }
                if (exp >=0)
                {
                    if(isLuckyTime){
                        exp*=2;
                    }
                    ILogin iLogin = LoginDao.getInstance();
                    List<Integer> result = iLogin.updateRoleExp(uid, exp);
                    builder.setLv(result.get(0));
                    builder.setExp(result.get(1));
                    RewardInfo rewardInfo =new RewardInfo();
                    rewardInfo.setItemid(25);
                    rewardInfo.setItemnums(exp);
                    rewardInfo.setItemtotal(result.get(1));
                    rewardInfoList.add(rewardInfo);
                    ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
                    showBu.setId(25);
                    showBu.setNum(exp);
                    builder.addShowItem(showBu);
                }
                GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录关卡结算奖励
                gameRecordEntity.setGamepattern(1);
                gameRecordEntity.setMissionid(request.getMissionId());
                gameRecordEntity.setType(2);
                gameRecordEntity.setUid(uid);
                StringBuffer rewardString=new StringBuffer("");
                for(int i=0;i<rewardInfoList.size();i++){
                    rewardString.append(MyUtils.objectToJson(rewardInfoList.get(i)));
                    if(i!=(rewardInfoList.size()-1)){
                        rewardString.append(",");
                    }
                }
                gameRecordEntity.setReward(rewardString.toString());
                MySql.insert(gameRecordEntity);
                ITask taskDao=TaskDao.getInstance();
                taskDao.updateAchievement(uid,31,request.getNowFinishAmount());
               // /// System.out.println(request.getNowFinishAmount()+"!!!!");
            }
            Redis  redis =Redis.getInstance();
            String  headballadvance=redis.hget("role:"+uid,"headballadvance");
            builder.setAdvance(Integer.parseInt(headballadvance));
            ITask taskDaoObject=TaskDao.getInstance();
          /*  switch (request.getMissionId()){
                case 1:
                    taskDaoObject.updateActivities(uid,7,1);
                    break;
                case 2:
                    taskDaoObject.updateActivities(uid,8,1);
                    break;
                case 3:
                    taskDaoObject.updateActivities(uid,9,1);
                    break;
            }
        }
*/
            int missionId=request.getMissionId();
          if(missionId<=3){
              taskDaoObject.updateActivities(uid,missionId+6,1);
          }else{
              taskDaoObject.updateActivities(uid,missionId+9,1);//先写死。。
          }
        }
        builder.setIsOk(true);
        return builder;
    }

    //@Override
    public MissionData.ResposeSourceMachine.Builder sourceMachine(ChannelHandlerContext ctx,String uid) {
        MissionData.ResposeSourceMachine.Builder builder=MissionData.ResposeSourceMachine.newBuilder();
        Redis redis = Redis.getInstance();
        Map<String,String> mission = redis.hgetAll("rolemission:" + uid+"#"+1);
        MissionData.Mission.Builder missionBuilder= MissionData.Mission.newBuilder();
        for(Map.Entry<String,String> entry: mission.entrySet()){
              missionBuilder.setType(1);
              int id=Integer.parseInt(entry.getKey());
              missionBuilder.setId (id);
              missionBuilder.setNums(Integer.parseInt(entry.getValue()));
             builder.addMissioninfo(missionBuilder);
        }

        Map<String,String> map=redis.hgetAll("rolesourcemachine:"+uid+"#1");
        MissionData.Product.Builder proBuilder=MissionData.Product.newBuilder();
        MissionData.ProduceItem.Builder PItemBuilder=MissionData.ProduceItem.newBuilder();
        Map<String,String> productMap=redis.hgetAll("rolesourcemachine:"+uid+"#2");
        for(Map.Entry<String,String> entry:productMap.entrySet()) {
            int key= Integer.parseInt(entry.getKey());
            int value= Integer.parseInt(entry.getValue());
            for(int i=0;i<value;i++){
                proBuilder.setItemID(key);
                builder.addProduct(proBuilder);
            }
        }
        for(Map.Entry<String,String> entry:map.entrySet()){
            SourceMachineEntity entity=(SourceMachineEntity)MyUtils.jsonToBean(entry.getValue(),SourceMachineEntity.class);
            double time=System.currentTimeMillis()/1000;
            int now=(int)Math.round(time);
           int end=Integer.parseInt(entity.getCountdown());
            if(end<=now){
                proBuilder.setItemID(entity.getItemId());
                builder.addProduct(proBuilder);
                redis.hdel("rolesourcemachine:"+uid+"#1",entity.getItemId()+"");
               StringBuffer hql= new StringBuffer("delete from SourceMachineEntity where uid='").append(uid)
                        .append("' and type=1 and itemId=").append(entity.getItemId());
                MySql.updateSomes(hql.toString());
                String productNums=redis.hget("rolesourcemachine:"+uid+"#2",entity.getItemId()+"");
                int nowNums=Integer.parseInt(productNums);
                redis.hset("rolesourcemachine:"+uid+"#2",entity.getItemId()+"",nowNums+1+"");
                StringBuffer sql= new StringBuffer("update SourceMachineEntity set countdown='").append(nowNums+1).append("'where uid='")
                        .append(uid).append("'and itemId=").append(entity.getItemId()).append("and type=2");
                MySql.updateSomes(sql.toString());
            }else{
                PItemBuilder.setCountDown(end-now);
                PItemBuilder.setItemId(entity.getItemId());
                builder.addItemList(PItemBuilder);
            }
        }
        byte[] bytes = builder.build().toByteArray();
        SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSESOURCESMACHINE_VALUE,bytes.length, bytes);
        ctx.writeAndFlush(response);

        return builder;
    }

    //@Override
    public MissionData.ResposeMakeItem.Builder makeItem(int id,int nums, String uid) {
        MissionData.ResposeMakeItem.Builder builder=MissionData.ResposeMakeItem.newBuilder();
        ItemData.Item.Builder itemBu=ItemData.Item.newBuilder();
        double time=System.currentTimeMillis()/1000;
        int now=(int)Math.round(time);
        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ENERGY, id);
        Redis jedis=Redis.getInstance();
        String finishNums =jedis.hget("rolesourcemachine:"+uid+"#2",id+"");

        if(finishNums==null){
            SourceMachineEntity entity= new SourceMachineEntity();
            entity.setItemId(id);
            entity.setType(2);
            entity.setUid(uid);
            entity.setCountdown("0");
            MySql.insert(entity);
            jedis.hset("rolesourcemachine:"+uid+"#2",id+"","0");
        }

        int need=Integer.parseInt(map.get("time_minute"))*60+Integer.parseInt(map.get("second"));
        int end=now+need;
        String iteminfo=map.get("use_id");
        String[] item=iteminfo.split("\\|");
        for(int i=0;i<item.length;i++){
            int itemId=Integer.parseInt(item[i].split(",")[0]);
            int itemNums=Integer.parseInt(item[i].split(",")[1]);
            IItem itemDao=ItemDao.getInstance();
            itemBu.setId(itemId);
            itemBu.setNum(itemDao.updateItemInfo(uid,itemId,-itemNums));
            builder.addItemList(itemBu);
        }
           SourceMachineEntity entity=new SourceMachineEntity();
           entity.setUid(uid);
           entity.setType(1);
           entity.setItemId(id);
           entity.setCountdown(end+"");
           MySql.insert(entity);
         jedis.hset("rolesourcemachine:"+uid+"#1",id+"",MyUtils.objectToJson(entity));
           builder.setIsSuccess(1);
           return builder;
    }

    //@Override
    public MissionData.ResposeProduct.Builder getProduct(int id, String uid) {
        MissionData.ResposeProduct.Builder builder=MissionData.ResposeProduct.newBuilder();
        ItemData.Item.Builder itemBuilder =ItemData.Item.newBuilder();
        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ENERGY, id);
         int itemId=Integer.parseInt(map.get("get_item"));
        int down=Integer.parseInt(map.get("item_num").split("\\|")[0]);
        int up=Integer.parseInt(map.get("item_num").split("\\|")[1]);
        Random ran=new Random();
        int nums= ran.nextInt(up+1-down)+down;
        itemBuilder.setId(itemId);
        IItem itemDao=ItemDao.getInstance();
        double addnums=itemDao.updateItemInfo(uid,itemId,nums);
        RewardInfo rewardInfo=new RewardInfo();
        rewardInfo.setItemnums(nums);
        rewardInfo.setItemtotal(new Double(addnums).intValue());
        rewardInfo.setItemid(itemId);
        itemBuilder.setNum(addnums);
        builder.setItem(itemBuilder);
        Redis jedis=Redis.getInstance();
        String productNums=jedis.hget("rolesourcemachine:"+uid+"#2",id+"");
       int nowNums=Integer.parseInt(productNums);
        if(nowNums>=1){
            jedis.hset("rolesourcemachine:"+uid+"#2",id+"",nowNums-1+"");
            StringBuffer sql= new StringBuffer("update SourceMachineEntity set countdown='").append(nowNums-1).append("'where uid='")
                    .append(uid).append("'and itemId=").append(id).append("and type=2");
            MySql.updateSomes(sql.toString());
        }else{
       StringBuffer sql= new StringBuffer("delete from SourceMachineEntity where uid='").append(uid).append("'and itemId=").append(id).append("and type=1");
        MySql.updateSomes(sql.toString());
        jedis.hdel("rolesourcemachine:"+uid+"#1",id+"");
        }
        StringBuffer sql  =new StringBuffer("from SourceMachineEntity where uid='").append(uid).append("'and itemId=").append(id).append("and type=3");
        SourceMachineEntity entity=(SourceMachineEntity)MySql.queryForOne(sql.toString());
        if(entity==null){
           entity=new SourceMachineEntity();
           entity.setCountdown("1");
           entity.setUid(uid);
           entity.setType(3);
           entity.setItemId(id);
            MySql.insert(entity);
        }else{
            entity.setCountdown(Integer.parseInt(entity.getCountdown())+1+"");
            MySql.update(entity);
        }
        ITask iTask=TaskDao.getInstance();
        iTask.updateMainAchievement(uid,5,1);
        ProductrecordEntity precordEntity  =new ProductrecordEntity();
        precordEntity.setItemid(id);
        precordEntity.setUid(uid);
        precordEntity.setGetitem(MyUtils.objectToJson(rewardInfo));
        MySql.insert(precordEntity);
        return builder;
    }

    //@Override
    public void initSourceMachine(String uid) {
         Redis jedis= Redis.getInstance();
         String key="rolesourcemachine:"+uid+"#2";
         Set set=jedis.keys(SuperConfig.REDIS_EXCEL_ENERGY+"*");
          for (int i=1;i<=set.size();i++){
              SourceMachineEntity entity= new SourceMachineEntity();
              entity.setItemId(i);
              entity.setType(2);
              entity.setUid(uid);
              entity.setCountdown("0");
              MySql.insert(entity);
              jedis.hset(key,i+"","0");
          }

    }

    public MissionData.ResposeStartGameCopy.Builder startGameCopy(String uid, int id) {
        MissionData.ResposeStartGameCopy.Builder builder=MissionData.ResposeStartGameCopy.newBuilder();
        StringBuffer sql=null;
        String key="role:"+uid;
        Redis jedis=Redis.getInstance();
        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GAMECOPY, id);
        int limit = Integer.parseInt(map.get("frequency"));
        if(id==1) {
            int nums=Integer.parseInt(jedis.hget(key,"gamecopy1"));
             if(nums>=limit){
                 builder.setErrorId(1);
                 return builder;
             }
            sql=new StringBuffer("update RoleEntity set gamecopy1=gamecopy1+1 where uid='").append(uid).append("'");
            jedis.hset(key,"gamecopy1",nums+1+"");
        }else{
            sql=new StringBuffer("update RoleEntity set gamecopy2=gamecopy2+1 where uid='").append(uid).append("'");
            int nums=Integer.parseInt(jedis.hget(key,"gamecopy2"));
            if(nums>=limit){
                builder.setErrorId(1);
                return builder;
            }
            jedis.hset(key,"gamecopy2",nums+1+"");
        }
        MySql.updateSomes(sql.toString());
        builder.setErrorId(0);
        return builder;
    }

    public MissionData.ResposeCountGameCopy.Builder countGameCopy(String uid, int id, int integral) {
        MissionData.ResposeCountGameCopy.Builder builder=MissionData.ResposeCountGameCopy.newBuilder();
        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GAMECOPY, id);
        int base=Integer.parseInt(map.get("base"));
        int itemId=0;
        int nums=base*integral;
        boolean isLuckyTime=isLuckyTime();
        if(isLuckyTime){
            nums*=2;
        }
        if(id==1){
            itemId=25;
            ILogin iLogin = LoginDao.getInstance();
            List<Integer> result = iLogin.updateRoleExp(uid, nums);
           // /// System.err.println(result.get(0)+"!!!"+result.get(1));
            builder.setLv(result.get(0));
            builder.setExp(result.get(1));
        }else if(id==2){
            Redis redis = Redis.getInstance();
            itemId=1;
            IItem itemDao=ItemDao.getInstance();
            ItemData.Item.Builder itemBu= ItemData.Item.newBuilder();
            if(nums==0) {
                String key = "roleitem:" + uid + "#0";
                String gold = redis.hget(key, 1 + "");
                itemBu.setNum(Double.parseDouble(gold));
            } else{
            double possess =itemDao.updateItemInfo(uid,itemId,nums);
            itemBu.setNum(possess);
            }
            itemBu.setId(itemId);
            builder.addItem(itemBu);//同步用户物品数据
            // 查询玩家等级
            int oldLv = Integer.parseInt(redis.hget("role:" + uid, "lv"));
            String expStr = redis.hget("roleitem:" + uid + "#0", "25");
            int exp = expStr == null ? 0 : (int) Double.parseDouble(expStr);
            builder.setLv(oldLv);
            builder.setExp(exp);
        }
        ItemData.Item.Builder showBu= ItemData.Item.newBuilder();
        showBu.setId(itemId);
        showBu.setNum(nums);
        builder.addShowItem(showBu);//展示层
        int achieve=Integer.parseInt(map.get("integral_added"));
        if(integral>=achieve){
            String reward=map.get("item_add");
            String[] rewards=reward.split("\\|");
            for(int i=0;i<rewards.length ;i++){
                String[] itemInfo=rewards[i].split(",");
                itemId=Integer.parseInt(itemInfo[0]);
                nums=Integer.parseInt(itemInfo[1]);
                if(isLuckyTime){
                    nums*=2;
                }
                ItemData.Item.Builder rewardShowBu= ItemData.Item.newBuilder();
                rewardShowBu.setNum(nums);
                rewardShowBu.setId(itemId);
                builder.addShowItem(rewardShowBu);//展示层
                IItem itemDao=ItemDao.getInstance();
                ItemData.Item.Builder itemBu= ItemData.Item.newBuilder();
               double possess =itemDao.updateItemInfo(uid,itemId,nums);
                itemBu.setNum(possess);
                itemBu.setId(itemId);
                builder.addItem(itemBu);
            }
        }
        builder.setErrorId(0);
        return builder;
    }

    private void setHeadBallToRedis(HeadBallEntity headBall) {
        Redis jedis = Redis.getInstance();
        Map<String, String> headBallMap = new HashMap<String, String>();
        headBallMap.put("is_first", headBall.getIs_first() + "");
        headBallMap.put("battle_num", headBall.getBattle_num() + "");
        headBallMap.put("jump_num", headBall.getJump_num() + "");
        headBallMap.put("jump_missionid", headBall.getJump_missionid() + "");
        headBallMap.put("user_id", headBall.getUser_id());
        headBallMap.put("is_threestar", headBall.getIs_threestar() + "");
        jedis.hmset("headball:" + headBall.getUser_id(), headBallMap);
    }




    private int countEndlessIntegral(List<Integer> ballIdList) {
        int total = 0;
        int combo = 0;
        for (int i = 0; i < ballIdList.size(); i++) {
            int id = ballIdList.get(i);
            if (id == 0) {
                if (combo > 0) {

                }
                combo = 0;
            }
        }
        return total;
    }

    public MissionInfo getMissionInfo(Map<String, String> roleMissionMap, int type) {
        MissionInfo missionInfo = null;
        if (roleMissionMap != null && roleMissionMap.get(type + "") != null && !roleMissionMap.get(type + "").equals("")) {
            missionInfo = new MissionInfo();
            missionInfo.setType(type);
            List<Integer> list = new ArrayList<Integer>();
            JSONArray jsonArray = JSONArray.fromObject(roleMissionMap.get(type + ""));
            for (int j = 0; j < jsonArray.size(); j++) {
                list.add(jsonArray.getInt(j));
            }
         //   missionInfo.setList(list);
        }
        return missionInfo;
    }

    private boolean updateMissionProgress(String uid, int missionId) {
        Redis jedis = Redis.getInstance();
        int missionMax = getMissionMax(uid);
        int frontId = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_MISSION, missionId + "", "front_client"));
        if (missionMax == frontId) {
            jedis.hset("role:" + uid, "missionid", missionId + "");

            StringBuffer hql = new StringBuffer("update RoleEntity set missionid = ").append(missionId).append(",missionstamp = '").append(System.currentTimeMillis())
                    .append("' where uid = '").append(uid).append("'");
            MySql.updateSomes(hql.toString());

            if (missionId < 200000) {
                ITask iTask = TaskDao.getInstance();
                iTask.updateAchievement(uid, 2, missionId);
            }
            return true;
        }
        return false;
    }

    private long updateEndlessIntegral(String uid, int integral) {
        Redis jedis = Redis.getInstance();
        long totalEndless = Long.parseLong(jedis.hget("role:" + uid, "totalendless"));
        totalEndless += integral;
        jedis.hset("role:" + uid, "totalendless", totalEndless + "");
        ReportManager.reportUpdateFriendEndless(uid, totalEndless);
        StringBuffer hql = new StringBuffer("update RoleEntity set totalendless = ").append(totalEndless);

        int endless = Integer.parseInt(jedis.hget("role:" + uid, "endless"));
        if (integral > endless) {
            jedis.hset("role:" + uid, "endless", integral + "");

            hql.append(",endless = ").append(integral).append(",endlessstamp = '").append(System.currentTimeMillis()).append("'");
        }

        hql.append(" where uid = '").append(uid).append("'");
        MySql.updateSomes(hql.toString());

        ITask iTask = TaskDao.getInstance();
      //  iTask.updateAchievement(uid, 23, integral);
        iTask.updateAchievement(uid, 23, totalEndless);
        iTask.updateAchievement(uid, 24, integral);
        return totalEndless;
    }

    /**
     * type1 主线，type2 支线
     */
    public int getMissionMax(String uid) {
        Redis jedis = Redis.getInstance();
        String missionId = jedis.hget("role:" + uid, "missionid");
        return missionId == null ? 0 : Integer.parseInt(missionId);
//        Map<String,String> map = jedis.hgetAll("rolemission:" + uid);
//        IMission iMission = MissionDao.getInstance();
//        MissionInfo missionInfo = iMission.getMissionInfo(map, type);
//        if (missionInfo == null){
//            return 0;
//        }
//        return missionInfo.getList().get(missionInfo.getList().size()-1);
    }

    public int getEndlessMax(String uid) {
        Redis jedis = Redis.getInstance();
        String endless = jedis.hget("role:" + uid, "endless");
        return endless == null ? 0 : Integer.parseInt(endless);
    }

    public void setFight() {

    }

    public synchronized void syncMission() {

    }


    //双人匹配
  /*  public synchronized void doubleMatch(String uid){
        int roomSize=2;
          int roomId=-1;
        for (Map.Entry<Integer, List<StationInfo>> entry : allocateRoomMap.entrySet()) {
            if (entry.getValue().size() < roomSize) {
                roomId = entry.getKey();
                if (entry.getValue().size()==(roomSize-1)){
                    //匹配成功
                    //通知战斗服分配房间
                    *//*SuperProtocol response = new SuperProtocol(ProtoData.SToC.RESPONSESTAMP_VALUE,bytes.length,
                            bytes);
                    SuperServer.BattelServer.writeAndFlush(response);*//*
                  battleRoomMap.put(roomId,allocateRoomMap.remove(roomId));
                }
                Redis jedis=Redis.getInstance();
                jedis.hset("role:"+uid,"BattleRoom",roomId+"");
                break;
            }
        }


    }*/
    public synchronized void doubleMatch(String uid){


    }
    //判断双倍奖励活动期间
    private boolean isLuckyTime(){
        long now=System.currentTimeMillis();
        Map<String,String> map=Redis.getExcelMap(SuperConfig.REDIS_EXCEL_LUCKYTIME,1);
        String startTime=map.get("oepn_time");
        String endTime=map.get("close_time");
        long start=MyUtils.dateStringToStamp(startTime);
        long end=MyUtils.dateStringToStamp(endTime);
        return  now>start&&end>now;
    }
}