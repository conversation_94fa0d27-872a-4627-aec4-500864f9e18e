package module.activity.LimitedTime;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.LimitedTimeRewardCompleteTaskEntity;
import entities.LimitedTimeRewardTaskEntity;
import io.netty.util.CharsetUtil;
import manager.ReportManager;
import module.mail.mail_tool.MailGetRewards;
import protocol.LimitedTimeRewardData;
import protocol.MailData;
import protocol.ProtoData;
import table.equitment_attribute.EquipmentAttributeLine;
import table.equitment_attribute.EquipmentAttributeTable;
import table.limitedtime_reward.LimitedTimeRewardLine;
import table.limitedtime_reward.LimitedTimeRewardTable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


public class LimitedTimeRewardService {
    private static LimitedTimeRewardService inst = null;
    public static LimitedTimeRewardService getInstance() {
        if (inst == null) {
            inst = new LimitedTimeRewardService();
        }
        return inst;
    }

    public byte[] RequestLimitedTimeReward(byte[] inBytes, String uid) {
        System.err.format("当前的Uid是：" + uid,CharsetUtil.UTF_8);
        LimitedTimeRewardData.ResponseLimitedTimeReward.Builder builder = LimitedTimeRewardData.ResponseLimitedTimeReward.newBuilder();
        LimitedTimeRewardData.RequestLimitedTimeReward data = null;
        try {
            data = LimitedTimeRewardData.RequestLimitedTimeReward.parseFrom(inBytes);

            LimitedTimeRewardDao.getInstance().Update(uid, data.getData());

            builder.setChargeReward(data.getData().getChargeReward());
            builder.setExp(data.getData().getExp());
            builder.setOrdinaryRewardId(data.getData().getOrdinaryRewardId());
            builder.setChargeRewardId(data.getData().getChargeRewardId());
            System.err.println("当前的奖励类型是：" + data.getData().getChargeReward());
            System.err.println("当前的奖励ID是：" + data.getData().getOrdinaryRewardId());
            GetReward(uid,builder.build());

        } catch (InvalidProtocolBufferException e) {

        }
        return builder.build().toByteArray();
    }

    public void GetReward(String uid,LimitedTimeRewardData.ResponseLimitedTimeReward data){
        MailData.Attachment.Builder attacment = MailData.Attachment.newBuilder();
        int rewardId = -1;
        LimitedTimeRewardLine targetLine = null;
        ArrayList<LimitedTimeRewardLine> lines = LimitedTimeRewardTable.getInstance().GetAllItem();

        if(!data.getChargeReward()){
            rewardId = data.getOrdinaryRewardId();
        }
        else{
            rewardId = data.getChargeRewardId();
        }
        if(rewardId == -1) throw new RuntimeException("请求的Task Id异常");
        targetLine = LimitedTimeRewardTable.getInstance().GetItem(rewardId);

        if(targetLine != null){
            List<Integer> targetReward = null;
            if(!data.getChargeReward()){
                targetReward = targetLine.leftReward;
            }
            else{
                targetReward = targetLine.rightReward;
            }
            attacment.setType(targetReward.get(0));
            switch (targetReward.get(0)){
                //1表示获取的奖励是物品
                case 1:
                    protocol.ItemData.Item.Builder item = protocol.ItemData.Item.newBuilder();
                    item.setId(targetReward.get(1));
                    item.setNum(targetReward.get(2));
                    attacment.addItem(item);
                    break;
                    //2表示装备
                case 2:
                    EquipmentAttributeLine equipmentAttributeLine = EquipmentAttributeTable.getInstance().GetItem(targetReward.get(1));
                    attacment.addEquip(EquipmentAttributeTable.getInstance().GenerateEquip(equipmentAttributeLine.type,equipmentAttributeLine.level_required));
                    break;
                    //3表示宠物
                case 3:
                    protocol.MailData.DefaultPet.Builder pet = protocol.MailData.DefaultPet.newBuilder();
                    pet.setPetId(targetReward.get(1));
                    pet.setIsEgg(0);
                    attacment.addDefaultPet(pet);
                    break;
            }


            System.err.format("当前的奖励类型：" + targetReward.get(0) + "  当前的奖励ID：" + targetReward.get(1) + "  当前的奖励数量：" + targetReward.get(1),CharsetUtil.UTF_8);


            MailGetRewards.getInstance().GetRewards(uid, attacment.build());
        }


    }

    // 任务数量
    public void RequestLimitedTimeRewardType(byte[] inBytes, String uid) {
        try {
            LimitedTimeRewardData.RequestLimitedTimeRewardType requestData  = LimitedTimeRewardData.
                    RequestLimitedTimeRewardType.parseFrom(inBytes);
            if (requestData != null){
                LimitedTimeRewardTaskEntity dailyTaskEntity = LimitedTimeRewardTaskDao.getInstance().GetTaskEntity(
                        uid,
                        String.valueOf(requestData.getType())
                );
                if (dailyTaskEntity != null){
                    // 数据库里面有相同类型的每日任务
                    dailyTaskEntity.setNumber(dailyTaskEntity.getNumber() + requestData.getNum());
                    LimitedTimeRewardTaskDao.getInstance().update(dailyTaskEntity);
                }else{
                    // 没有此任务类型，插入一个
                    LimitedTimeRewardTaskEntity entity = new LimitedTimeRewardTaskEntity();
                    entity.setUid(uid);
                    entity.setTime(new Date(System.currentTimeMillis()));
                    entity.setType(requestData.getType());
                    entity.setNumber(requestData.getNum());
                    LimitedTimeRewardTaskDao.getInstance().insert(entity);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    // 任务完成
    public void RequestLimitedTimeRewardFinish(byte[] inBytes, String uid) {
        try {
            LimitedTimeRewardData.RequestLimitedTimeRewardFinish requestData  = LimitedTimeRewardData.
                    RequestLimitedTimeRewardFinish.parseFrom(inBytes);
            if (requestData !=  null){
                LimitedTimeRewardCompleteTaskEntity dailyTaskEntity = LimitedTimeRewardCompleteTaskDao.getInstance().GetCompleteDailyTaskEntity(
                        uid,
                        String.valueOf(requestData.getId())
                );
                if (dailyTaskEntity != null){
                    dailyTaskEntity.setComplete(requestData.getIsComplete());
                    LimitedTimeRewardCompleteTaskDao.getInstance().update(dailyTaskEntity);
                }else{
                    LimitedTimeRewardCompleteTaskEntity savaData = new LimitedTimeRewardCompleteTaskEntity();
                    savaData.setUid(uid);
                    savaData.setTime(new Date(System.currentTimeMillis()));
                    savaData.setTaskId(requestData.getId());
                    savaData.setComplete(requestData.getIsComplete());
                    LimitedTimeRewardCompleteTaskDao.getInstance().insert(savaData);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    // 全部任务
    public static void GetAllLimitedTimeRewardTask(String uid) {
        LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.Builder builder =
                LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.newBuilder();
        try {
            List<Object> dailyTaskEntity = LimitedTimeRewardTaskDao.getInstance().GetAll(uid);
            List<Object> completeDailyTaskEntity = LimitedTimeRewardCompleteTaskDao.getInstance().GetAll(uid);

            for (Object item :
                    dailyTaskEntity) {
                LimitedTimeRewardTaskEntity data = (LimitedTimeRewardTaskEntity)item;
                LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder dataBuilder =
                        LimitedTimeRewardData.RequestLimitedTimeRewardType.newBuilder();
                dataBuilder.setNum(data.getNumber());
                dataBuilder.setType(data.getType());

                builder.addTaskType(dataBuilder);
            }

            for (Object item :
                    completeDailyTaskEntity) {
                LimitedTimeRewardCompleteTaskEntity data = (LimitedTimeRewardCompleteTaskEntity)item;
                LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder dataBuilder =
                        LimitedTimeRewardData.RequestLimitedTimeRewardFinish.newBuilder();
                dataBuilder.setId(data.getTaskId());
                dataBuilder.setIsComplete(data.isComplete());

                builder.addTaskState(dataBuilder);
            }

            ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSELIMITEDTIMEREWARDTASKDATA_VALUE, builder.build().toByteArray());
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    // 获取时间
    public byte[] RequestLimitedTimeRewardGetRemainTime(byte[] inBytes, String uid) {
        LimitedTimeRewardData.ResponseRemainTime.Builder builder = LimitedTimeRewardData.ResponseRemainTime.newBuilder();
        builder.setTimeSecond(LimitedTimeRewardTimer.getInstance().getCurTime());
        return builder.build().toByteArray();
    }
}
