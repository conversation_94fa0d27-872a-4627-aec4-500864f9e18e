package common;

import java.util.List;

@ExcelConfigObject(key = "breedMatchration")
public class BreedPetRarityMappingConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "N", isList = true)
    private List<Integer> rarity1;
    @ExcelColumn(name = "R", isList = true)
    private List<Integer> rarity2;
    @ExcelColumn(name = "SR", isList = true)
    private List<Integer> rarity3;
    @ExcelColumn(name = "SSR", isList = true)
    private List<Integer> rarity4;
    @ExcelColumn(name = "UR", isList = true)
    private List<Integer> rarity5;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public List<Integer> getRarity1() {
        return rarity1;
    }

    public void setRarity1(List<Integer> rarity1) {
        this.rarity1 = rarity1;
    }

    public List<Integer> getRarity2() {
        return rarity2;
    }

    public void setRarity2(List<Integer> rarity2) {
        this.rarity2 = rarity2;
    }

    public List<Integer> getRarity3() {
        return rarity3;
    }

    public void setRarity3(List<Integer> rarity3) {
        this.rarity3 = rarity3;
    }

    public List<Integer> getRarity4() {
        return rarity4;
    }

    public void setRarity4(List<Integer> rarity4) {
        this.rarity4 = rarity4;
    }

    public List<Integer> getRarity5() {
        return rarity5;
    }

    public void setRarity5(List<Integer> rarity5) {
        this.rarity5 = rarity5;
    }

    @Override
    public String toString() {
        return "BreedPetRarityMappingConfig{" +
                "id=" + id +
                ", rarity1=" + rarity1 +
                ", rarity2=" + rarity2 +
                ", rarity3=" + rarity3 +
                ", rarity4=" + rarity4 +
                ", rarity5=" + rarity5 +
                '}';
    }
}
