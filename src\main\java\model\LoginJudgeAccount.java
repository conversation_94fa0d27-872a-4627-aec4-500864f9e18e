package model;

import entities.UserEntity;

/**
 * Created by nara on 2018/8/13.
 */
public class LoginJudgeAccount {
    private RoleInfo roleInfo;
    private UserEntity userEntity;
    private int errorId;

    public LoginJudgeAccount() {
        this.roleInfo = null;
        this.userEntity = null;
        this.errorId = 0;
    }

    public UserEntity getUserEntity() {
        return userEntity;
    }

    public void setUserEntity(UserEntity userEntity) {
        this.userEntity = userEntity;
    }

    public int getErrorId() {
        return errorId;
    }

    public void setErrorId(int errorId) {
        this.errorId = errorId;
    }

    public RoleInfo getRoleInfo() {
        return roleInfo;
    }

    public void setRoleInfo(RoleInfo roleInfo) {
        this.roleInfo = roleInfo;
    }
}
