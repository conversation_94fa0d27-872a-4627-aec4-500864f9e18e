package Json;

import java.util.List;

/**
 * Created by nara on 2018/3/5.
 */
public class MailJson {
    private String sender;
    private String title;
    private String content;
    private String itemStr;
    private List<CommonJson> items;

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public List<CommonJson> getItems() {
        return items;
    }

    public void setItems(List<CommonJson> items) {
        this.items = items;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getItemStr() {
        return itemStr;
    }

    public void setItemStr(String itemStr) {
        this.itemStr = itemStr;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

}