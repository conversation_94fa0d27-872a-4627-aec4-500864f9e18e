package server;

import common.SuperConfig;
import entities.ItemEntity;
import entities.MailEntity;
import entities.RoleEntity;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.logging.LogLevel;
import io.netty.handler.logging.LoggingHandler;
import io.netty.handler.timeout.IdleStateHandler;
import manager.MySql;
import manager.ReportManager;
import module.item.ItemDao;
import module.mail.MailDao;
import module.role.RoleDao;

import org.apache.http.HttpEntity;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.PropertyConfigurator;

import com.google.api.client.http.HttpResponse;

import protocol.*;
import utils.MyUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Properties;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created by nara on 2017/11/15.
 */
public class SuperServer {
    public static ChannelHandlerContext BattelServer = null; //战斗服的连接
    public static SuperServer inst = null;
    private static int port;
    private static int pingSecond;
    private static boolean isProxy;
    public static EventLoopGroup bossGroup;
    public static EventLoopGroup workerGroup;

    public static SuperServer getInstance() {
        if (inst == null) {
            inst = new SuperServer();
            MyUtils.clearAllUserCache();
            try {
                Properties prop = new Properties();
                InputStream in = null;
                in = Properties.class.getResourceAsStream("/config.properties");
                prop.load(in);
                if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MAIN || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_PLOTTER) {
                    port = Integer.parseInt(prop.getProperty("server.port"));
                    PropertyConfigurator.configure(Properties.class.getResource("/log4j.properties"));//log4j配置
                } else if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_TEST) {
                    port = Integer.parseInt(prop.getProperty("server.test.port"));
                } else if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION_PLOTTER) {
                    port = Integer.parseInt(prop.getProperty("server.mission.port"));
                    PropertyConfigurator.configure(Properties.class.getResource("/log4j_mission.properties"));//log4j配置
                }
                //体力刷新
                //physicalStrength();
                //温度层刷新
                //physicalTemper();
                /// System.out.println("=================SuperStar port=============" + port);
                 System.out.println("//\n" +
                        "//                       _oo0oo_\n" +
                        "//                     o888888888o\n" +
                        "//                     88 \" . \" 88\n" +
                        "//                     (| Q a Q |)\n" +
                        "//                      0\\  =  /0\n" +
                        "//                    ___/`---'\\___\n" +
                        "//                  .' \\\\|     |// '.\n" +
                        "//                 / \\\\|||  :  |||// \\\n" +
                        "//                / _||||| -:- |||||- \\\n" +
                        "//               |   | \\\\\\  -  /// |   |\n" +
                        "//               | \\_|  ''\\---/''  |_/ |\n" +
                        "//               \\  .-\\__  '-'  ___/-. /\n" +
                        "//             ___'. .'  /--.--\\  `. .'___\n" +
                        "//          .\"\" '<  `.___\\_<|>_/___.' >' \"\".\n" +
                        "//         | | :  `- \\`.;`\\ _ /`;.`/ - ` : | |\n" +
                        "//         \\  \\ `_.   \\_ __\\ /__ _/   .-` /  /\n" +
                        "//     =====`-.____`.___ \\_____/___.-`___.-'=====\n" +
                        "//                       `=---='\n" +
                        "//\n" +
                        "//\n" +
                        "//     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~\n" +
                        "//\n" +
                        "//               佛祖保佑         永无BUG\n" +
                        "//");
                pingSecond = Integer.parseInt(prop.getProperty("server.pingSecond"));
                if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_PLOTTER) {
                    pingSecond = 60;
                }

                in.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return inst;
    }

    public void start(Boolean isProxy) {

        if( isProxy )
        {
            Properties systemProperties = System.getProperties();
            systemProperties.setProperty("http.proxyHost", "127.0.0.1");
            systemProperties.setProperty("http.proxyPort", "7890");
            systemProperties.setProperty("https.proxyHost", "127.0.0.1");
            systemProperties.setProperty("https.proxyPort", "7890");
            systemProperties.setProperty("socksProxyHost", "127.0.0.1");
            systemProperties.setProperty("socksProxyPort", "7890");
            systemProperties.setProperty("http.nonProxyHosts","localhost");
            systemProperties.setProperty("https.nonProxyHosts","localhost");
            System.setProperty("sun.net.client.defaultConnectTimeout", String.valueOf(8000));// （单位：毫秒）
            System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,SSLv3");    
                     
        }

        bossGroup = new NioEventLoopGroup();
        workerGroup = new NioEventLoopGroup();
        try {
            ServerBootstrap b = new ServerBootstrap();
            b.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .handler(new LoggingHandler(LogLevel.INFO))
                    .childHandler(new ChildChannelHandler())//
                    .option(ChannelOption.SO_BACKLOG, 1024) // 设置tcp缓冲区
                    .childOption(ChannelOption.SO_KEEPALIVE, true);
            ChannelFuture f = b.bind(port).sync(); // (7)
            f.channel().closeFuture().sync();

        } catch (Exception e) {
            System.out.println("Netty server is error , message:" + e.getMessage());
            e.printStackTrace();
        } finally {
            workerGroup.shutdownGracefully();
            bossGroup.shutdownGracefully();
        }
    }

    /**
     * 网络事件处理器
     */
    private class ChildChannelHandler extends ChannelInitializer<SocketChannel> {
        @Override
        protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast("ping", new IdleStateHandler(pingSecond, 0, 0));
            ch.pipeline().addLast(new SuperEncoder());
            ch.pipeline().addLast(new SuperDecoder());
//            ch.pipeline().addLast(new SuperServerHandlerTest());
            ch.pipeline().addLast(new SuperServerHandler());
        }
    }

    /*
    *
    * 体力刷新
    *
    * */
    private static void physicalStrength(){
        ItemData.Item.Builder builder=ItemData.Item.newBuilder();

        Runnable runnable=new Runnable() {
            @Override
            public void run() {
                RoleDao roleDao = new RoleDao();
                List<Object> list = roleDao.queryUid();
                String uid = null;
                for (int i = 0; i < list.size(); i++) {
                    Object o = list.get(i);
                    uid = (String) o;
                    RoleEntity roleEntity = roleDao.queryRole(uid);
                    RoleData.PlainRole role = RoleEntity.entityToPb(roleEntity);
                    int LV = role.getRoleCurrentLV();
                    int stamina = 0;
                    for (int k = 0; k <= 20; k++) {
                        stamina = LV + 20;
                    }
                    ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, 3);
                    if (itemEntity == null){
                        itemEntity = new ItemEntity();
                    //    itemEntity.setUid(uid);
                        itemEntity.setUid(uid);
                        itemEntity.setItemid(3);
                        itemEntity.setItemnum((double) stamina);
                        MySql.insert(itemEntity);
                        double itemnum = itemEntity.getItemnum();
                        itemnum += 20;
                        if (itemnum > (double) stamina || itemnum < 0) {
                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                            itemEntity.setItemnum((double) stamina);
                            MySql.update(itemEntity);
                            builder.setId(itemEntity.getItemid());
                            builder.setNum(itemEntity.getItemnum());
                            reportBuilder.addItem(builder);
                            ReportManager.reportInfo(itemEntity.getUid(), ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
                        } else {
                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                            itemEntity.setItemnum(itemnum);
                            MySql.update(itemEntity);
                            builder.setId(itemEntity.getItemid());
                            builder.setNum(itemEntity.getItemnum());
                            reportBuilder.addItem(builder);
                            ReportManager.reportInfo(itemEntity.getUid(), ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
                        }
                    }else {
                        double itemnum = itemEntity.getItemnum();
                        itemnum += 1;
                        if (itemnum > (double) stamina || itemnum < 0) {
                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                            itemEntity.setItemnum((double) stamina);
                            MySql.update(itemEntity);
                            builder.setId(itemEntity.getItemid());
                            builder.setNum(itemEntity.getItemnum());
                            reportBuilder.addItem(builder);
                            ReportManager.reportInfo(itemEntity.getUid(), ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
                        } else {
                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                            itemEntity.setItemnum(itemnum);
                            MySql.update(itemEntity);
                            builder.setId(itemEntity.getItemid());
                            builder.setNum(itemEntity.getItemnum());
                            reportBuilder.addItem(builder);
                            ReportManager.reportInfo(itemEntity.getUid(), ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
                        }
                    }
                }
            }
        };
        ScheduledExecutorService service= Executors.newSingleThreadScheduledExecutor();
        service.scheduleAtFixedRate(runnable,1,60,TimeUnit.MINUTES);
    }
    private static void physicalTemper(){
        MailEntity entity = new MailEntity();
        Runnable runnable = new Runnable() {
            @Override
            public void run() {
                RoleDao roleDao = new RoleDao();
                List<Object> list = roleDao.queryUid();
                String uid = null;
                for (int i = 0; i < list.size(); i++) {
                    Object o = list.get(i);
                    uid = (String) o;
                    if (SuperServerHandler.getCtxFromUid(uid) != null) {
                        TemperData.ResponseTemper.Builder attachment = TemperData.ResponseTemper.newBuilder();
                        ReportManager.reportInfo(uid,ProtoData.SToC.RESPONSETemper_VALUE,attachment.build().toByteArray());
                    }
                }
            }
        };
        ScheduledExecutorService service = Executors.newSingleThreadScheduledExecutor();
        service.scheduleAtFixedRate(runnable, 1, 10, TimeUnit.MINUTES);
    }
}
