package module.synchronization.match;

/**
 * Created by nara on 2018/6/4.
 */
public class RoomShowInfo {
    private int id;
    private String name;
    private int isSecret;
    private int playerNum;
    private boolean isPlaying;
    private int type;
   private  boolean isRobotRoom;
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    public void setIsPlaying(boolean isPlaying) {
        this.isPlaying = isPlaying;
    }

    public int getPlayerNum() {
        return playerNum;
    }

    public void setPlayerNum(int playerNum) {
        this.playerNum = playerNum;
    }

    public int getIsSecret() {
        return isSecret;
    }

    public void setIsSecret(int isSecret) {
        this.isSecret = isSecret;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public boolean isRobotRoom() {
        return isRobotRoom;
    }

    public void setRobotRoom(boolean robotRoom) {
        isRobotRoom = robotRoom;
    }
}
