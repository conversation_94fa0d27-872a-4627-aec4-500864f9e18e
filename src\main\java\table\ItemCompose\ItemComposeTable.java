package table.ItemCompose;

import table.TableManager;

import java.util.Comparator;

public class ItemComposeTable extends TableManager<ItemComposeLine> {
    private static  ItemComposeTable instance;
    public static ItemComposeTable getInstance(){
        if (instance != null){
            return instance;
        }
        instance = new ItemComposeTable();
        return instance;
    }
    private ItemComposeTable(){

    }

    @Override
    public void Parse() {
        this.GetAllItem().sort(Comparator.comparingInt(a -> a.id));


    }
    @Override
    public String TableName() {
        return "ItemCompose";
    }

    @Override
    public String LinePath() {
        return "table.ItemCompose.ItemComposeLine";
    }
}