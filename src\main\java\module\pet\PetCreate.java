package module.pet;

import common.PetAttributeConfig;
import common.PetConfig;
import common.SuperConfig;
import entities.PetEntity;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import protocol.PetData;
import protocol.ProtoData;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

public class PetCreate {
    private static PetCreate inst = null;
    public static PetCreate getInstance() {
        if (inst == null) {
            inst = new PetCreate();
        }
        return inst;
    }

    public void DefaultCreatePet(String uid, int petId, boolean isEgg){
        PetData.ResponseOperatePet.Builder petbuilder = PetData.ResponseOperatePet.newBuilder();
        petbuilder.setErrorId(0);
        petbuilder.setType(1);
        try {
            PetEntity petEntity = PetCreate.getInstance().CreatePet(uid, petId, isEgg);
            petbuilder.addPet(PetEntity.entityToPb(petEntity));
        }catch (Exception e){
            petbuilder.setErrorId(1);
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, petbuilder.build().toByteArray());
    }

    public PetEntity CreatePet(String uid, int petId, boolean isEgg){
        PetEntity petEntity = new PetEntity();
        petEntity.setPetCharacter(1); // 废弃 宠物性格
        petEntity.setCurrentExp(0);
        petEntity.setCurrentLevel(1);
        petEntity.setFriendId(uid);
        petEntity.setPetType(petId);
        petEntity.setStrongLevel(0);
        petEntity.setSpSkillLv(1);
        petEntity.setNormalSkillLv(1);
        petEntity.setBreakLV(0);
        petEntity.setAccessType(2); // 废弃 好像是卡池
        petEntity.setName("");

        petEntity.setLockStatus(isEgg ? 0 : 1 << PetConfig.eggFormLockIndex);
        petEntity.setMode(isEgg ? 1 : 2);
        petEntity.setGrowing(!isEgg);

        petEntity.setGetTime(TimerHandler.nowTimeStamp);

        //获取宠物的稀有度
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("petconfig:*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> mailMap = jedis.hgetAll(key);
            int keys = Integer.parseInt(mailMap.get("id"));
            if (keys == petEntity.getPetType()) {
                petEntity.setRarity(Integer.parseInt(String.valueOf(mailMap.get("rare")))); // 固定的 稀有度
            }
        }

        try {
            //星级 决定宠物属性六维的系数
            int starLv = PetUtils.getPetRarity();
            petEntity.setStarLevel(starLv);
            PetAttributeConfig petAttributeConfig = (PetAttributeConfig) SuperConfig.getCongifObject(SuperConfig.petAttributeConfig, starLv);
            //hp
            List<Float> hpFactorList = petAttributeConfig.getHpCoefficient();
            int hpFactorindex = (int) (Math.random() * hpFactorList.size());
            float hpFactor = hpFactorList.get(hpFactorindex);
            petEntity.setHpFactor(hpFactor);
            //atk
            List<Float> AtkFactorList = petAttributeConfig.getAtkcoefficient();
            int AtkFactorIndex = (int) (Math.random() * AtkFactorList.size());
            float AtkFactor = AtkFactorList.get(AtkFactorIndex);
            petEntity.setAtkFactor(AtkFactor);
            //def
            List<Float> defFactorList = petAttributeConfig.getDefCoefficient();
            int defFactorIndex = (int) (Math.random() * defFactorList.size());
            float defFactor = defFactorList.get(defFactorIndex);
            petEntity.setDefFactor(defFactor);
            //satk
            List<Float> satkFactorList = petAttributeConfig.getSatkCoefficient();
            int satkFactorIndex = (int) (Math.random() * satkFactorList.size());
            float satkFactor = satkFactorList.get(satkFactorIndex);
            petEntity.setSatkFactor(satkFactor);
            //sdef
            List<Float> sdefFactorList = petAttributeConfig.getSdefCoefficient();
            int sdefFactorIndex = (int) (Math.random() * sdefFactorList.size());
            float sdefFactor = sdefFactorList.get(sdefFactorIndex);
            petEntity.setSdefFactor(sdefFactor);
            //speed
            List<Float> speedFactorList = petAttributeConfig.getSpeedCoefficient();
            int speedFactorIndex = (int) (Math.random() * speedFactorList.size());
            float speedFactor = speedFactorList.get(speedFactorIndex);
            petEntity.setSpeedFactor(speedFactor);
            PetConfig petConfig = (PetConfig) SuperConfig.getCongifObject(SuperConfig.petConfig, petId);
            petEntity.setMainAttribute(petConfig.getTypeone());
            petEntity.setSubAttribute(petConfig.getTypetwo());
            petEntity.setNormalSkillId(petConfig.getNormalskillid());
            petEntity.setSpSkillId(petConfig.getSpskillid());
        } catch (Exception e) {
            e.printStackTrace();
        }

        int petUid = PetUtils.createPetId(petEntity.getPetCharacter(), uid, petId);
        petEntity.setPetUId(petUid);

        MySql.insert(petEntity);
        return petEntity;
    }
}
