package model;

public class ActivitiesInfo {
    private int activitiesId;
    private long activitiesNowNum;
    private int status;
    private long timeStamp;
    private int type;
    private int overdue;

    public int getActivitiesId() {
        return activitiesId;
    }

    public void setActivitiesId(int activitiesId) {
        this.activitiesId = activitiesId;
    }

    public long getActivitiesNowNum() {
        return activitiesNowNum;
    }

    public void setActivitiesNowNum(long activitiesNowNum) {
        this.activitiesNowNum = activitiesNowNum;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getTimeStamp() {
        return timeStamp;
    }

    public void setTimeStamp(long timeStamp) {
        this.timeStamp = timeStamp;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getOverdue() {
        return overdue;
    }

    public void setOverdue(int overdue) {
        this.overdue = overdue;
    }

    @Override
    public String toString() {
        return "ActivitiesInfo{" +
                "activitiesId=" + activitiesId +
                ", activitiesNowNum=" + activitiesNowNum +
                ", status=" + status +
                ", timeStamp=" + timeStamp +
                ", type=" + type +
                ", overdue=" + overdue +
                '}';
    }
}
