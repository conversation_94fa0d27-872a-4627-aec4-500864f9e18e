import entities.InformationEntity;
import entities.RoleEntity;
import manager.MySql;
import protocol.FriendData;

import java.util.*;
import java.util.stream.Collectors;

public class Test3 {
    public static void main(String[] args) {
        StringBuilder stringBuilder = new StringBuilder("from InformationEntity where uid1='").append("20220907155840JJw0874ydCO").append("' group by uid2");
        List<Object> informationList = MySql.queryForList(stringBuilder.toString());
        FriendData.ResponseChatRecord.Builder responseChatRecord = FriendData.ResponseChatRecord.newBuilder();
        FriendData.ResponseChatRecord.Builder responseChatRecord1 = FriendData.ResponseChatRecord.newBuilder();
        FriendData.ResponseChatRecord.Builder responseChatRecord2 = FriendData.ResponseChatRecord.newBuilder();
        FriendData.Information.Builder responseInformation = FriendData.Information.newBuilder();
        FriendData.InformationList.Builder responseInformationList1 = null;
        FriendData.InformationList.Builder responseInformationList2 = null;
        //查出发送的消息
        for (int i = 0; i < informationList.size(); i++) {
            responseInformationList1 = FriendData.InformationList.newBuilder();
            InformationEntity informationEntity = (InformationEntity) informationList.get(i);
            stringBuilder = new StringBuilder("from InformationEntity where uid1='").append("20220907155840JJw0874ydCO").append("' and uid2='").append(informationEntity.getUid2()).append("'");
            List<Object> informationLists = MySql.queryForList(stringBuilder.toString());
            stringBuilder = new StringBuilder("from RoleEntity where uid='").append(informationEntity.getUid2()).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            responseInformationList1.setId(roleEntity.getId());
            int status = 0;
            for (int j = 0; j < informationLists.size(); j++) {
                InformationEntity informationEntitys = (InformationEntity) informationLists.get(j);
                responseInformation.setInformationId(informationEntitys.getId());
                responseInformation.setContent(informationEntitys.getContent());
                responseInformation.setDate(informationEntitys.getDate());
                responseInformation.setIsReceive(0);
                responseInformationList1.addInformation(responseInformation);
                if (informationEntitys.getStatus() == 1) {
                    status = 1;
                }
                responseInformationList1.setStatus(status);
            }
            responseChatRecord1.addInformationList(responseInformationList1);
        }
        StringBuilder stringBuilder1 = new StringBuilder("from InformationEntity where uid2='").append("20220907155840JJw0874ydCO").append("' group by uid1");
        List<Object> informationList1 = MySql.queryForList(stringBuilder1.toString());
        //查出接收的消息
        for (int i = 0; i < informationList1.size(); i++) {
            responseInformationList2 = FriendData.InformationList.newBuilder();
            InformationEntity informationEntity = (InformationEntity) informationList1.get(i);
            stringBuilder = new StringBuilder("from InformationEntity where uid2='").append("20220907155840JJw0874ydCO").append("' and uid1='").append(informationEntity.getUid1()).append("'");
            List<Object> informationLists = MySql.queryForList(stringBuilder.toString());
            stringBuilder = new StringBuilder("from RoleEntity where uid='").append(informationEntity.getUid1()).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            responseInformationList2.setId(roleEntity.getId());
            int status = 0;
            for (int j = 0; j < informationLists.size(); j++) {
                InformationEntity informationEntitys = (InformationEntity) informationLists.get(j);
                responseInformation.setInformationId(informationEntitys.getId());
                responseInformation.setContent(informationEntitys.getContent());
                responseInformation.setDate(informationEntitys.getDate());
                responseInformation.setIsReceive(1);
                responseInformationList2.addInformation(responseInformation);
                if (informationEntitys.getStatus() == 1) {
                    status = 1;
                }
                responseInformationList2.setStatus(status);
            }
            responseChatRecord1.addInformationList(responseInformationList2);
        }

        Set set = new HashSet();
        Map<Integer, FriendData.InformationList.Builder> map = new HashMap<>();
        for (int i = 0; i < responseChatRecord1.getInformationListList().size(); i++) {
            boolean success = set.add(responseChatRecord1.getInformationListList().get(i).getId());
            if (!success) {
                if (map.get(responseChatRecord1.getInformationListList().get(i).getId()) != null) {
                    responseInformationList1 = FriendData.InformationList.newBuilder();
                    responseInformationList1.setStatus(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getStatus());
                    responseInformationList1.setId(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getId());
                    for (int j = 0; j < map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().size(); j++) {
                        responseInformation.setContent(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getContent());
                        responseInformation.setDate(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getDate());
                        responseInformation.setInformationId(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getInformationId());
                        responseInformation.setIsReceive(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getIsReceive());
                        responseInformationList1.addInformation(responseInformation);
                    }
                    responseInformationList1.setStatus(responseChatRecord1.getInformationList(i).getStatus());
                    responseInformationList1.setId(responseChatRecord1.getInformationListList().get(i).getId());
                    System.err.println("6666666666666666=" + responseChatRecord1.getInformationListList().get(i).getInformationList().size());
                    for (int j = 0; j < responseChatRecord1.getInformationListList().get(i).getInformationList().size(); j++) {
                        responseInformation.setContent(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getContent());
                        responseInformation.setDate(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getDate());
                        responseInformation.setInformationId(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getInformationId());
                        responseInformation.setIsReceive(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getIsReceive());
                        responseInformationList1.addInformation(responseInformation);
                    }

                    map.put(responseChatRecord1.getInformationListList().get(i).getId(), responseInformationList1);
                }
            } else {
                responseInformationList1 = FriendData.InformationList.newBuilder();
                responseInformationList1.setStatus(responseChatRecord1.getInformationList(i).getStatus());
                responseInformationList1.setId(responseChatRecord1.getInformationListList().get(i).getId());
                for (int j = 0; j < responseChatRecord1.getInformationListList().get(i).getInformationList().size(); j++) {
                    responseInformation.setContent(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getContent());
                    responseInformation.setDate(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getDate());
                    responseInformation.setInformationId(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getInformationId());
                    responseInformation.setIsReceive(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getIsReceive());
                    System.err.println("36666666666666666=" + responseChatRecord1.getInformationListList().get(i).getInformationList().size());

                    responseInformationList1.addInformation(responseInformation);
                }
                map.put(responseChatRecord1.getInformationList(i).getId(), responseInformationList1);
            }
        }
        for (Integer key : map.keySet()) {
            responseInformationList2 = FriendData.InformationList.newBuilder();
            for (int i = 0; i < map.get(key).getInformationList().size(); i++) {
                responseInformationList2.setId(map.get(key).getId());
                responseInformationList2.setStatus(map.get(key).getStatus());
                List<FriendData.Information> information = map.get(key).getInformationList().stream().sorted(Comparator.comparing(FriendData.Information::getInformationId)).collect(Collectors.toList());
                for (int j = 0; j < information.size(); j++) {
                    responseInformation.setIsReceive(information.get(i).getIsReceive());
                    responseInformation.setContent(information.get(i).getContent());
                    responseInformation.setDate(information.get(i).getDate());
                    responseInformation.setInformationId(information.get(i).getInformationId());
                }
                responseInformationList2.addInformation(responseInformation);
            }
            responseChatRecord.addInformationList(responseInformationList2);
        }
        for (int i = 0; i < responseChatRecord.getInformationListList().size(); i++) {
            System.err.println("111111111111=id=" + responseChatRecord.getInformationListList().get(i).getId());
            for (int j = 0; j < responseChatRecord.getInformationListList().get(i).getInformationList().size(); j++) {
                System.err.println("000000000000=getinformationid=" + responseChatRecord.getInformationListList().get(i).getInformationList().get(j).getInformationId());
            }
        }
    }
}
