<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.PVPBaseDataEntity" table="productrecord" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="roleUid" column="roleUid"/>
        <property name="rank" column="rank"/>
        <property name="score" column="score"/>
        <property name="victory" column="victory"/>
        <property name="fail" column="fail"/>
    </class>
</hibernate-mapping>