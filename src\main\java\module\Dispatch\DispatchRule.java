package module.Dispatch;

public class DispatchRule {
    private int type;//类型
    private String content;//具体细节

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "DispatchRule{" +
                "type=" + type +
                ", content='" + content + '\'' +
                '}';
    }
}
