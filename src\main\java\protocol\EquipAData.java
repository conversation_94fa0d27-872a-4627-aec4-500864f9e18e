// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: equipA.proto

package protocol;

public final class EquipAData {
  private EquipAData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface EquipAOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 Eid = 1;
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    boolean hasEid();
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    int getEid();

    // optional int32 ad = 2;
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    boolean hasAd();
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    int getAd();

    // optional int32 ap = 3;
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    boolean hasAp();
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    int getAp();

    // optional int32 arm = 4;
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    boolean hasArm();
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    int getArm();

    // optional int32 mdf = 5;
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    boolean hasMdf();
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    int getMdf();

    // optional int32 speed = 6;
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    boolean hasSpeed();
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    int getSpeed();

    // optional int32 hp = 7;
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    boolean hasHp();
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    int getHp();

    // optional int32 ead = 8;
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    boolean hasEad();
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    int getEad();

    // optional int32 emdf = 9;
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    boolean hasEmdf();
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    int getEmdf();

    // optional int32 esp = 10;
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    boolean hasEsp();
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    int getEsp();

    // optional int32 ehp = 11;
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    boolean hasEhp();
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    int getEhp();

    // optional int32 eap = 12;
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    boolean hasEap();
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    int getEap();

    // optional int32 earm = 13;
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    boolean hasEarm();
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    int getEarm();
  }
  /**
   * Protobuf type {@code protocol.EquipA}
   *
   * <pre>
   *装备
   * </pre>
   */
  public static final class EquipA extends
      com.google.protobuf.GeneratedMessage
      implements EquipAOrBuilder {
    // Use EquipA.newBuilder() to construct.
    private EquipA(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private EquipA(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final EquipA defaultInstance;
    public static EquipA getDefaultInstance() {
      return defaultInstance;
    }

    public EquipA getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private EquipA(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              eid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ad_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              ap_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              arm_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              mdf_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              speed_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              hp_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              ead_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              emdf_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              esp_ = input.readInt32();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              ehp_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              eap_ = input.readInt32();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              earm_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipAData.internal_static_protocol_EquipA_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipAData.internal_static_protocol_EquipA_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipAData.EquipA.class, protocol.EquipAData.EquipA.Builder.class);
    }

    public static com.google.protobuf.Parser<EquipA> PARSER =
        new com.google.protobuf.AbstractParser<EquipA>() {
      public EquipA parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EquipA(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<EquipA> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 Eid = 1;
    public static final int EID_FIELD_NUMBER = 1;
    private int eid_;
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    public boolean hasEid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    public int getEid() {
      return eid_;
    }

    // optional int32 ad = 2;
    public static final int AD_FIELD_NUMBER = 2;
    private int ad_;
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    public boolean hasAd() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    public int getAd() {
      return ad_;
    }

    // optional int32 ap = 3;
    public static final int AP_FIELD_NUMBER = 3;
    private int ap_;
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    public boolean hasAp() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    public int getAp() {
      return ap_;
    }

    // optional int32 arm = 4;
    public static final int ARM_FIELD_NUMBER = 4;
    private int arm_;
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    public boolean hasArm() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    public int getArm() {
      return arm_;
    }

    // optional int32 mdf = 5;
    public static final int MDF_FIELD_NUMBER = 5;
    private int mdf_;
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    public boolean hasMdf() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    public int getMdf() {
      return mdf_;
    }

    // optional int32 speed = 6;
    public static final int SPEED_FIELD_NUMBER = 6;
    private int speed_;
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    public int getSpeed() {
      return speed_;
    }

    // optional int32 hp = 7;
    public static final int HP_FIELD_NUMBER = 7;
    private int hp_;
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    public boolean hasHp() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    public int getHp() {
      return hp_;
    }

    // optional int32 ead = 8;
    public static final int EAD_FIELD_NUMBER = 8;
    private int ead_;
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    public boolean hasEad() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    public int getEad() {
      return ead_;
    }

    // optional int32 emdf = 9;
    public static final int EMDF_FIELD_NUMBER = 9;
    private int emdf_;
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    public boolean hasEmdf() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    public int getEmdf() {
      return emdf_;
    }

    // optional int32 esp = 10;
    public static final int ESP_FIELD_NUMBER = 10;
    private int esp_;
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    public boolean hasEsp() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    public int getEsp() {
      return esp_;
    }

    // optional int32 ehp = 11;
    public static final int EHP_FIELD_NUMBER = 11;
    private int ehp_;
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    public boolean hasEhp() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    public int getEhp() {
      return ehp_;
    }

    // optional int32 eap = 12;
    public static final int EAP_FIELD_NUMBER = 12;
    private int eap_;
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    public boolean hasEap() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    public int getEap() {
      return eap_;
    }

    // optional int32 earm = 13;
    public static final int EARM_FIELD_NUMBER = 13;
    private int earm_;
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    public boolean hasEarm() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    public int getEarm() {
      return earm_;
    }

    private void initFields() {
      eid_ = 0;
      ad_ = 0;
      ap_ = 0;
      arm_ = 0;
      mdf_ = 0;
      speed_ = 0;
      hp_ = 0;
      ead_ = 0;
      emdf_ = 0;
      esp_ = 0;
      ehp_ = 0;
      eap_ = 0;
      earm_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, eid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, ad_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, ap_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, arm_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, mdf_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, speed_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, hp_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(8, ead_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeInt32(9, emdf_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeInt32(10, esp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeInt32(11, ehp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeInt32(12, eap_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeInt32(13, earm_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, eid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, ad_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, ap_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, arm_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, mdf_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, speed_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, hp_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, ead_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, emdf_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, esp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, ehp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, eap_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, earm_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipAData.EquipA parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.EquipA parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.EquipA parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.EquipA parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.EquipA parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.EquipA parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.EquipA parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipAData.EquipA parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.EquipA parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.EquipA parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipAData.EquipA prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.EquipA}
     *
     * <pre>
     *装备
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipAData.EquipAOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipAData.internal_static_protocol_EquipA_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipAData.internal_static_protocol_EquipA_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipAData.EquipA.class, protocol.EquipAData.EquipA.Builder.class);
      }

      // Construct using protocol.EquipAData.EquipA.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        eid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        ad_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        ap_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        arm_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        mdf_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        speed_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        hp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        ead_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        emdf_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        esp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        ehp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        eap_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        earm_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipAData.internal_static_protocol_EquipA_descriptor;
      }

      public protocol.EquipAData.EquipA getDefaultInstanceForType() {
        return protocol.EquipAData.EquipA.getDefaultInstance();
      }

      public protocol.EquipAData.EquipA build() {
        protocol.EquipAData.EquipA result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipAData.EquipA buildPartial() {
        protocol.EquipAData.EquipA result = new protocol.EquipAData.EquipA(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.eid_ = eid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.ad_ = ad_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.ap_ = ap_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.arm_ = arm_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.mdf_ = mdf_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.speed_ = speed_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.hp_ = hp_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.ead_ = ead_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.emdf_ = emdf_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.esp_ = esp_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.ehp_ = ehp_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.eap_ = eap_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.earm_ = earm_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipAData.EquipA) {
          return mergeFrom((protocol.EquipAData.EquipA)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipAData.EquipA other) {
        if (other == protocol.EquipAData.EquipA.getDefaultInstance()) return this;
        if (other.hasEid()) {
          setEid(other.getEid());
        }
        if (other.hasAd()) {
          setAd(other.getAd());
        }
        if (other.hasAp()) {
          setAp(other.getAp());
        }
        if (other.hasArm()) {
          setArm(other.getArm());
        }
        if (other.hasMdf()) {
          setMdf(other.getMdf());
        }
        if (other.hasSpeed()) {
          setSpeed(other.getSpeed());
        }
        if (other.hasHp()) {
          setHp(other.getHp());
        }
        if (other.hasEad()) {
          setEad(other.getEad());
        }
        if (other.hasEmdf()) {
          setEmdf(other.getEmdf());
        }
        if (other.hasEsp()) {
          setEsp(other.getEsp());
        }
        if (other.hasEhp()) {
          setEhp(other.getEhp());
        }
        if (other.hasEap()) {
          setEap(other.getEap());
        }
        if (other.hasEarm()) {
          setEarm(other.getEarm());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipAData.EquipA parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipAData.EquipA) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 Eid = 1;
      private int eid_ ;
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public boolean hasEid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public int getEid() {
        return eid_;
      }
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public Builder setEid(int value) {
        bitField0_ |= 0x00000001;
        eid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public Builder clearEid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        eid_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ad = 2;
      private int ad_ ;
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public boolean hasAd() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public int getAd() {
        return ad_;
      }
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public Builder setAd(int value) {
        bitField0_ |= 0x00000002;
        ad_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public Builder clearAd() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ad_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ap = 3;
      private int ap_ ;
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public boolean hasAp() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public int getAp() {
        return ap_;
      }
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public Builder setAp(int value) {
        bitField0_ |= 0x00000004;
        ap_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public Builder clearAp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ap_ = 0;
        onChanged();
        return this;
      }

      // optional int32 arm = 4;
      private int arm_ ;
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public boolean hasArm() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public int getArm() {
        return arm_;
      }
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public Builder setArm(int value) {
        bitField0_ |= 0x00000008;
        arm_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public Builder clearArm() {
        bitField0_ = (bitField0_ & ~0x00000008);
        arm_ = 0;
        onChanged();
        return this;
      }

      // optional int32 mdf = 5;
      private int mdf_ ;
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public boolean hasMdf() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public int getMdf() {
        return mdf_;
      }
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public Builder setMdf(int value) {
        bitField0_ |= 0x00000010;
        mdf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public Builder clearMdf() {
        bitField0_ = (bitField0_ & ~0x00000010);
        mdf_ = 0;
        onChanged();
        return this;
      }

      // optional int32 speed = 6;
      private int speed_ ;
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public int getSpeed() {
        return speed_;
      }
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public Builder setSpeed(int value) {
        bitField0_ |= 0x00000020;
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000020);
        speed_ = 0;
        onChanged();
        return this;
      }

      // optional int32 hp = 7;
      private int hp_ ;
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public boolean hasHp() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public int getHp() {
        return hp_;
      }
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public Builder setHp(int value) {
        bitField0_ |= 0x00000040;
        hp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public Builder clearHp() {
        bitField0_ = (bitField0_ & ~0x00000040);
        hp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ead = 8;
      private int ead_ ;
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public boolean hasEad() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public int getEad() {
        return ead_;
      }
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public Builder setEad(int value) {
        bitField0_ |= 0x00000080;
        ead_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public Builder clearEad() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ead_ = 0;
        onChanged();
        return this;
      }

      // optional int32 emdf = 9;
      private int emdf_ ;
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public boolean hasEmdf() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public int getEmdf() {
        return emdf_;
      }
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public Builder setEmdf(int value) {
        bitField0_ |= 0x00000100;
        emdf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public Builder clearEmdf() {
        bitField0_ = (bitField0_ & ~0x00000100);
        emdf_ = 0;
        onChanged();
        return this;
      }

      // optional int32 esp = 10;
      private int esp_ ;
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public boolean hasEsp() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public int getEsp() {
        return esp_;
      }
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public Builder setEsp(int value) {
        bitField0_ |= 0x00000200;
        esp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public Builder clearEsp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        esp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ehp = 11;
      private int ehp_ ;
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public boolean hasEhp() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public int getEhp() {
        return ehp_;
      }
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public Builder setEhp(int value) {
        bitField0_ |= 0x00000400;
        ehp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public Builder clearEhp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        ehp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 eap = 12;
      private int eap_ ;
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public boolean hasEap() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public int getEap() {
        return eap_;
      }
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public Builder setEap(int value) {
        bitField0_ |= 0x00000800;
        eap_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public Builder clearEap() {
        bitField0_ = (bitField0_ & ~0x00000800);
        eap_ = 0;
        onChanged();
        return this;
      }

      // optional int32 earm = 13;
      private int earm_ ;
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public boolean hasEarm() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public int getEarm() {
        return earm_;
      }
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public Builder setEarm(int value) {
        bitField0_ |= 0x00001000;
        earm_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public Builder clearEarm() {
        bitField0_ = (bitField0_ & ~0x00001000);
        earm_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.EquipA)
    }

    static {
      defaultInstance = new EquipA(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.EquipA)
  }

  public interface RequestEquipFOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 Eid = 1;
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    boolean hasEid();
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    int getEid();

    // optional int32 ad = 2;
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    boolean hasAd();
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    int getAd();

    // optional int32 ap = 3;
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    boolean hasAp();
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    int getAp();

    // optional int32 arm = 4;
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    boolean hasArm();
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    int getArm();

    // optional int32 mdf = 5;
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    boolean hasMdf();
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    int getMdf();

    // optional int32 speed = 6;
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    boolean hasSpeed();
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    int getSpeed();

    // optional int32 hp = 7;
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    boolean hasHp();
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    int getHp();

    // optional int32 ead = 8;
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    boolean hasEad();
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    int getEad();

    // optional int32 emdf = 9;
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    boolean hasEmdf();
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    int getEmdf();

    // optional int32 esp = 10;
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    boolean hasEsp();
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    int getEsp();

    // optional int32 ehp = 11;
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    boolean hasEhp();
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    int getEhp();

    // optional int32 eap = 12;
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    boolean hasEap();
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    int getEap();

    // optional int32 earm = 13;
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    boolean hasEarm();
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    int getEarm();
  }
  /**
   * Protobuf type {@code protocol.RequestEquipF}
   *
   * <pre>
   *1324
   * </pre>
   */
  public static final class RequestEquipF extends
      com.google.protobuf.GeneratedMessage
      implements RequestEquipFOrBuilder {
    // Use RequestEquipF.newBuilder() to construct.
    private RequestEquipF(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestEquipF(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestEquipF defaultInstance;
    public static RequestEquipF getDefaultInstance() {
      return defaultInstance;
    }

    public RequestEquipF getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestEquipF(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              eid_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ad_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              ap_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              arm_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              mdf_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              speed_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              hp_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              ead_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              emdf_ = input.readInt32();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              esp_ = input.readInt32();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              ehp_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              eap_ = input.readInt32();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              earm_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipAData.internal_static_protocol_RequestEquipF_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipAData.internal_static_protocol_RequestEquipF_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipAData.RequestEquipF.class, protocol.EquipAData.RequestEquipF.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestEquipF> PARSER =
        new com.google.protobuf.AbstractParser<RequestEquipF>() {
      public RequestEquipF parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestEquipF(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestEquipF> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 Eid = 1;
    public static final int EID_FIELD_NUMBER = 1;
    private int eid_;
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    public boolean hasEid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 Eid = 1;</code>
     *
     * <pre>
     *唯一编号
     * </pre>
     */
    public int getEid() {
      return eid_;
    }

    // optional int32 ad = 2;
    public static final int AD_FIELD_NUMBER = 2;
    private int ad_;
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    public boolean hasAd() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 ad = 2;</code>
     *
     * <pre>
     *物攻
     * </pre>
     */
    public int getAd() {
      return ad_;
    }

    // optional int32 ap = 3;
    public static final int AP_FIELD_NUMBER = 3;
    private int ap_;
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    public boolean hasAp() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 ap = 3;</code>
     *
     * <pre>
     *法攻
     * </pre>
     */
    public int getAp() {
      return ap_;
    }

    // optional int32 arm = 4;
    public static final int ARM_FIELD_NUMBER = 4;
    private int arm_;
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    public boolean hasArm() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 arm = 4;</code>
     *
     * <pre>
     *物防
     * </pre>
     */
    public int getArm() {
      return arm_;
    }

    // optional int32 mdf = 5;
    public static final int MDF_FIELD_NUMBER = 5;
    private int mdf_;
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    public boolean hasMdf() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 mdf = 5;</code>
     *
     * <pre>
     *法防
     * </pre>
     */
    public int getMdf() {
      return mdf_;
    }

    // optional int32 speed = 6;
    public static final int SPEED_FIELD_NUMBER = 6;
    private int speed_;
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    public boolean hasSpeed() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int32 speed = 6;</code>
     *
     * <pre>
     *速度
     * </pre>
     */
    public int getSpeed() {
      return speed_;
    }

    // optional int32 hp = 7;
    public static final int HP_FIELD_NUMBER = 7;
    private int hp_;
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    public boolean hasHp() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 hp = 7;</code>
     *
     * <pre>
     *体力
     * </pre>
     */
    public int getHp() {
      return hp_;
    }

    // optional int32 ead = 8;
    public static final int EAD_FIELD_NUMBER = 8;
    private int ead_;
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    public boolean hasEad() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>optional int32 ead = 8;</code>
     *
     * <pre>
     *额外攻击
     * </pre>
     */
    public int getEad() {
      return ead_;
    }

    // optional int32 emdf = 9;
    public static final int EMDF_FIELD_NUMBER = 9;
    private int emdf_;
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    public boolean hasEmdf() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>optional int32 emdf = 9;</code>
     *
     * <pre>
     *额外法防
     * </pre>
     */
    public int getEmdf() {
      return emdf_;
    }

    // optional int32 esp = 10;
    public static final int ESP_FIELD_NUMBER = 10;
    private int esp_;
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    public boolean hasEsp() {
      return ((bitField0_ & 0x00000200) == 0x00000200);
    }
    /**
     * <code>optional int32 esp = 10;</code>
     *
     * <pre>
     *额外速度
     * </pre>
     */
    public int getEsp() {
      return esp_;
    }

    // optional int32 ehp = 11;
    public static final int EHP_FIELD_NUMBER = 11;
    private int ehp_;
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    public boolean hasEhp() {
      return ((bitField0_ & 0x00000400) == 0x00000400);
    }
    /**
     * <code>optional int32 ehp = 11;</code>
     *
     * <pre>
     *额外体力
     * </pre>
     */
    public int getEhp() {
      return ehp_;
    }

    // optional int32 eap = 12;
    public static final int EAP_FIELD_NUMBER = 12;
    private int eap_;
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    public boolean hasEap() {
      return ((bitField0_ & 0x00000800) == 0x00000800);
    }
    /**
     * <code>optional int32 eap = 12;</code>
     *
     * <pre>
     *额外法攻
     * </pre>
     */
    public int getEap() {
      return eap_;
    }

    // optional int32 earm = 13;
    public static final int EARM_FIELD_NUMBER = 13;
    private int earm_;
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    public boolean hasEarm() {
      return ((bitField0_ & 0x00001000) == 0x00001000);
    }
    /**
     * <code>optional int32 earm = 13;</code>
     *
     * <pre>
     *额外物防
     * </pre>
     */
    public int getEarm() {
      return earm_;
    }

    private void initFields() {
      eid_ = 0;
      ad_ = 0;
      ap_ = 0;
      arm_ = 0;
      mdf_ = 0;
      speed_ = 0;
      hp_ = 0;
      ead_ = 0;
      emdf_ = 0;
      esp_ = 0;
      ehp_ = 0;
      eap_ = 0;
      earm_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, eid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, ad_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, ap_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, arm_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, mdf_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, speed_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, hp_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeInt32(8, ead_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeInt32(9, emdf_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        output.writeInt32(10, esp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        output.writeInt32(11, ehp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        output.writeInt32(12, eap_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        output.writeInt32(13, earm_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, eid_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, ad_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, ap_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, arm_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, mdf_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, speed_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, hp_);
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, ead_);
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, emdf_);
      }
      if (((bitField0_ & 0x00000200) == 0x00000200)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(10, esp_);
      }
      if (((bitField0_ & 0x00000400) == 0x00000400)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, ehp_);
      }
      if (((bitField0_ & 0x00000800) == 0x00000800)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, eap_);
      }
      if (((bitField0_ & 0x00001000) == 0x00001000)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, earm_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipAData.RequestEquipF parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.RequestEquipF parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipAData.RequestEquipF parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.RequestEquipF parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipAData.RequestEquipF prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestEquipF}
     *
     * <pre>
     *1324
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipAData.RequestEquipFOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipAData.internal_static_protocol_RequestEquipF_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipAData.internal_static_protocol_RequestEquipF_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipAData.RequestEquipF.class, protocol.EquipAData.RequestEquipF.Builder.class);
      }

      // Construct using protocol.EquipAData.RequestEquipF.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        eid_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        ad_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        ap_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        arm_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        mdf_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        speed_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        hp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        ead_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        emdf_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        esp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        ehp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        eap_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        earm_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipAData.internal_static_protocol_RequestEquipF_descriptor;
      }

      public protocol.EquipAData.RequestEquipF getDefaultInstanceForType() {
        return protocol.EquipAData.RequestEquipF.getDefaultInstance();
      }

      public protocol.EquipAData.RequestEquipF build() {
        protocol.EquipAData.RequestEquipF result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipAData.RequestEquipF buildPartial() {
        protocol.EquipAData.RequestEquipF result = new protocol.EquipAData.RequestEquipF(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.eid_ = eid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.ad_ = ad_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.ap_ = ap_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.arm_ = arm_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.mdf_ = mdf_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.speed_ = speed_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.hp_ = hp_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.ead_ = ead_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.emdf_ = emdf_;
        if (((from_bitField0_ & 0x00000200) == 0x00000200)) {
          to_bitField0_ |= 0x00000200;
        }
        result.esp_ = esp_;
        if (((from_bitField0_ & 0x00000400) == 0x00000400)) {
          to_bitField0_ |= 0x00000400;
        }
        result.ehp_ = ehp_;
        if (((from_bitField0_ & 0x00000800) == 0x00000800)) {
          to_bitField0_ |= 0x00000800;
        }
        result.eap_ = eap_;
        if (((from_bitField0_ & 0x00001000) == 0x00001000)) {
          to_bitField0_ |= 0x00001000;
        }
        result.earm_ = earm_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipAData.RequestEquipF) {
          return mergeFrom((protocol.EquipAData.RequestEquipF)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipAData.RequestEquipF other) {
        if (other == protocol.EquipAData.RequestEquipF.getDefaultInstance()) return this;
        if (other.hasEid()) {
          setEid(other.getEid());
        }
        if (other.hasAd()) {
          setAd(other.getAd());
        }
        if (other.hasAp()) {
          setAp(other.getAp());
        }
        if (other.hasArm()) {
          setArm(other.getArm());
        }
        if (other.hasMdf()) {
          setMdf(other.getMdf());
        }
        if (other.hasSpeed()) {
          setSpeed(other.getSpeed());
        }
        if (other.hasHp()) {
          setHp(other.getHp());
        }
        if (other.hasEad()) {
          setEad(other.getEad());
        }
        if (other.hasEmdf()) {
          setEmdf(other.getEmdf());
        }
        if (other.hasEsp()) {
          setEsp(other.getEsp());
        }
        if (other.hasEhp()) {
          setEhp(other.getEhp());
        }
        if (other.hasEap()) {
          setEap(other.getEap());
        }
        if (other.hasEarm()) {
          setEarm(other.getEarm());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEid()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipAData.RequestEquipF parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipAData.RequestEquipF) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 Eid = 1;
      private int eid_ ;
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public boolean hasEid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public int getEid() {
        return eid_;
      }
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public Builder setEid(int value) {
        bitField0_ |= 0x00000001;
        eid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 Eid = 1;</code>
       *
       * <pre>
       *唯一编号
       * </pre>
       */
      public Builder clearEid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        eid_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ad = 2;
      private int ad_ ;
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public boolean hasAd() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public int getAd() {
        return ad_;
      }
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public Builder setAd(int value) {
        bitField0_ |= 0x00000002;
        ad_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ad = 2;</code>
       *
       * <pre>
       *物攻
       * </pre>
       */
      public Builder clearAd() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ad_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ap = 3;
      private int ap_ ;
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public boolean hasAp() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public int getAp() {
        return ap_;
      }
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public Builder setAp(int value) {
        bitField0_ |= 0x00000004;
        ap_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ap = 3;</code>
       *
       * <pre>
       *法攻
       * </pre>
       */
      public Builder clearAp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ap_ = 0;
        onChanged();
        return this;
      }

      // optional int32 arm = 4;
      private int arm_ ;
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public boolean hasArm() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public int getArm() {
        return arm_;
      }
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public Builder setArm(int value) {
        bitField0_ |= 0x00000008;
        arm_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 arm = 4;</code>
       *
       * <pre>
       *物防
       * </pre>
       */
      public Builder clearArm() {
        bitField0_ = (bitField0_ & ~0x00000008);
        arm_ = 0;
        onChanged();
        return this;
      }

      // optional int32 mdf = 5;
      private int mdf_ ;
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public boolean hasMdf() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public int getMdf() {
        return mdf_;
      }
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public Builder setMdf(int value) {
        bitField0_ |= 0x00000010;
        mdf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 mdf = 5;</code>
       *
       * <pre>
       *法防
       * </pre>
       */
      public Builder clearMdf() {
        bitField0_ = (bitField0_ & ~0x00000010);
        mdf_ = 0;
        onChanged();
        return this;
      }

      // optional int32 speed = 6;
      private int speed_ ;
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public boolean hasSpeed() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public int getSpeed() {
        return speed_;
      }
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public Builder setSpeed(int value) {
        bitField0_ |= 0x00000020;
        speed_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 speed = 6;</code>
       *
       * <pre>
       *速度
       * </pre>
       */
      public Builder clearSpeed() {
        bitField0_ = (bitField0_ & ~0x00000020);
        speed_ = 0;
        onChanged();
        return this;
      }

      // optional int32 hp = 7;
      private int hp_ ;
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public boolean hasHp() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public int getHp() {
        return hp_;
      }
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public Builder setHp(int value) {
        bitField0_ |= 0x00000040;
        hp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 hp = 7;</code>
       *
       * <pre>
       *体力
       * </pre>
       */
      public Builder clearHp() {
        bitField0_ = (bitField0_ & ~0x00000040);
        hp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ead = 8;
      private int ead_ ;
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public boolean hasEad() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public int getEad() {
        return ead_;
      }
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public Builder setEad(int value) {
        bitField0_ |= 0x00000080;
        ead_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ead = 8;</code>
       *
       * <pre>
       *额外攻击
       * </pre>
       */
      public Builder clearEad() {
        bitField0_ = (bitField0_ & ~0x00000080);
        ead_ = 0;
        onChanged();
        return this;
      }

      // optional int32 emdf = 9;
      private int emdf_ ;
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public boolean hasEmdf() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public int getEmdf() {
        return emdf_;
      }
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public Builder setEmdf(int value) {
        bitField0_ |= 0x00000100;
        emdf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 emdf = 9;</code>
       *
       * <pre>
       *额外法防
       * </pre>
       */
      public Builder clearEmdf() {
        bitField0_ = (bitField0_ & ~0x00000100);
        emdf_ = 0;
        onChanged();
        return this;
      }

      // optional int32 esp = 10;
      private int esp_ ;
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public boolean hasEsp() {
        return ((bitField0_ & 0x00000200) == 0x00000200);
      }
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public int getEsp() {
        return esp_;
      }
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public Builder setEsp(int value) {
        bitField0_ |= 0x00000200;
        esp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 esp = 10;</code>
       *
       * <pre>
       *额外速度
       * </pre>
       */
      public Builder clearEsp() {
        bitField0_ = (bitField0_ & ~0x00000200);
        esp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 ehp = 11;
      private int ehp_ ;
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public boolean hasEhp() {
        return ((bitField0_ & 0x00000400) == 0x00000400);
      }
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public int getEhp() {
        return ehp_;
      }
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public Builder setEhp(int value) {
        bitField0_ |= 0x00000400;
        ehp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 ehp = 11;</code>
       *
       * <pre>
       *额外体力
       * </pre>
       */
      public Builder clearEhp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        ehp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 eap = 12;
      private int eap_ ;
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public boolean hasEap() {
        return ((bitField0_ & 0x00000800) == 0x00000800);
      }
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public int getEap() {
        return eap_;
      }
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public Builder setEap(int value) {
        bitField0_ |= 0x00000800;
        eap_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 eap = 12;</code>
       *
       * <pre>
       *额外法攻
       * </pre>
       */
      public Builder clearEap() {
        bitField0_ = (bitField0_ & ~0x00000800);
        eap_ = 0;
        onChanged();
        return this;
      }

      // optional int32 earm = 13;
      private int earm_ ;
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public boolean hasEarm() {
        return ((bitField0_ & 0x00001000) == 0x00001000);
      }
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public int getEarm() {
        return earm_;
      }
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public Builder setEarm(int value) {
        bitField0_ |= 0x00001000;
        earm_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 earm = 13;</code>
       *
       * <pre>
       *额外物防
       * </pre>
       */
      public Builder clearEarm() {
        bitField0_ = (bitField0_ & ~0x00001000);
        earm_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestEquipF)
    }

    static {
      defaultInstance = new RequestEquipF(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestEquipF)
  }

  public interface RequestGetEquipAOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetEquipA}
   *
   * <pre>
   *1322
   * </pre>
   */
  public static final class RequestGetEquipA extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetEquipAOrBuilder {
    // Use RequestGetEquipA.newBuilder() to construct.
    private RequestGetEquipA(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetEquipA(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetEquipA defaultInstance;
    public static RequestGetEquipA getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetEquipA getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetEquipA(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipAData.internal_static_protocol_RequestGetEquipA_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipAData.internal_static_protocol_RequestGetEquipA_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipAData.RequestGetEquipA.class, protocol.EquipAData.RequestGetEquipA.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetEquipA> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetEquipA>() {
      public RequestGetEquipA parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetEquipA(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetEquipA> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipAData.RequestGetEquipA parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.RequestGetEquipA parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipAData.RequestGetEquipA parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.RequestGetEquipA parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipAData.RequestGetEquipA prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetEquipA}
     *
     * <pre>
     *1322
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipAData.RequestGetEquipAOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipAData.internal_static_protocol_RequestGetEquipA_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipAData.internal_static_protocol_RequestGetEquipA_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipAData.RequestGetEquipA.class, protocol.EquipAData.RequestGetEquipA.Builder.class);
      }

      // Construct using protocol.EquipAData.RequestGetEquipA.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipAData.internal_static_protocol_RequestGetEquipA_descriptor;
      }

      public protocol.EquipAData.RequestGetEquipA getDefaultInstanceForType() {
        return protocol.EquipAData.RequestGetEquipA.getDefaultInstance();
      }

      public protocol.EquipAData.RequestGetEquipA build() {
        protocol.EquipAData.RequestGetEquipA result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipAData.RequestGetEquipA buildPartial() {
        protocol.EquipAData.RequestGetEquipA result = new protocol.EquipAData.RequestGetEquipA(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipAData.RequestGetEquipA) {
          return mergeFrom((protocol.EquipAData.RequestGetEquipA)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipAData.RequestGetEquipA other) {
        if (other == protocol.EquipAData.RequestGetEquipA.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipAData.RequestGetEquipA parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipAData.RequestGetEquipA) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetEquipA)
    }

    static {
      defaultInstance = new RequestGetEquipA(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetEquipA)
  }

  public interface ResponseGetEquipAOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.EquipA equipA = 1;
    /**
     * <code>required .protocol.EquipA equipA = 1;</code>
     */
    boolean hasEquipA();
    /**
     * <code>required .protocol.EquipA equipA = 1;</code>
     */
    protocol.EquipAData.EquipA getEquipA();
    /**
     * <code>required .protocol.EquipA equipA = 1;</code>
     */
    protocol.EquipAData.EquipAOrBuilder getEquipAOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseGetEquipA}
   *
   * <pre>
   *2322
   * </pre>
   */
  public static final class ResponseGetEquipA extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetEquipAOrBuilder {
    // Use ResponseGetEquipA.newBuilder() to construct.
    private ResponseGetEquipA(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetEquipA(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetEquipA defaultInstance;
    public static ResponseGetEquipA getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetEquipA getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetEquipA(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              protocol.EquipAData.EquipA.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = equipA_.toBuilder();
              }
              equipA_ = input.readMessage(protocol.EquipAData.EquipA.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(equipA_);
                equipA_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipAData.internal_static_protocol_ResponseGetEquipA_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipAData.internal_static_protocol_ResponseGetEquipA_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipAData.ResponseGetEquipA.class, protocol.EquipAData.ResponseGetEquipA.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetEquipA> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetEquipA>() {
      public ResponseGetEquipA parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetEquipA(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetEquipA> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.EquipA equipA = 1;
    public static final int EQUIPA_FIELD_NUMBER = 1;
    private protocol.EquipAData.EquipA equipA_;
    /**
     * <code>required .protocol.EquipA equipA = 1;</code>
     */
    public boolean hasEquipA() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.EquipA equipA = 1;</code>
     */
    public protocol.EquipAData.EquipA getEquipA() {
      return equipA_;
    }
    /**
     * <code>required .protocol.EquipA equipA = 1;</code>
     */
    public protocol.EquipAData.EquipAOrBuilder getEquipAOrBuilder() {
      return equipA_;
    }

    private void initFields() {
      equipA_ = protocol.EquipAData.EquipA.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasEquipA()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getEquipA().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, equipA_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, equipA_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipAData.ResponseGetEquipA parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.ResponseGetEquipA parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipAData.ResponseGetEquipA prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetEquipA}
     *
     * <pre>
     *2322
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipAData.ResponseGetEquipAOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipAData.internal_static_protocol_ResponseGetEquipA_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipAData.internal_static_protocol_ResponseGetEquipA_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipAData.ResponseGetEquipA.class, protocol.EquipAData.ResponseGetEquipA.Builder.class);
      }

      // Construct using protocol.EquipAData.ResponseGetEquipA.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEquipAFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (equipABuilder_ == null) {
          equipA_ = protocol.EquipAData.EquipA.getDefaultInstance();
        } else {
          equipABuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipAData.internal_static_protocol_ResponseGetEquipA_descriptor;
      }

      public protocol.EquipAData.ResponseGetEquipA getDefaultInstanceForType() {
        return protocol.EquipAData.ResponseGetEquipA.getDefaultInstance();
      }

      public protocol.EquipAData.ResponseGetEquipA build() {
        protocol.EquipAData.ResponseGetEquipA result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipAData.ResponseGetEquipA buildPartial() {
        protocol.EquipAData.ResponseGetEquipA result = new protocol.EquipAData.ResponseGetEquipA(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (equipABuilder_ == null) {
          result.equipA_ = equipA_;
        } else {
          result.equipA_ = equipABuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipAData.ResponseGetEquipA) {
          return mergeFrom((protocol.EquipAData.ResponseGetEquipA)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipAData.ResponseGetEquipA other) {
        if (other == protocol.EquipAData.ResponseGetEquipA.getDefaultInstance()) return this;
        if (other.hasEquipA()) {
          mergeEquipA(other.getEquipA());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasEquipA()) {
          
          return false;
        }
        if (!getEquipA().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipAData.ResponseGetEquipA parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipAData.ResponseGetEquipA) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.EquipA equipA = 1;
      private protocol.EquipAData.EquipA equipA_ = protocol.EquipAData.EquipA.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipAData.EquipA, protocol.EquipAData.EquipA.Builder, protocol.EquipAData.EquipAOrBuilder> equipABuilder_;
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public boolean hasEquipA() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public protocol.EquipAData.EquipA getEquipA() {
        if (equipABuilder_ == null) {
          return equipA_;
        } else {
          return equipABuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public Builder setEquipA(protocol.EquipAData.EquipA value) {
        if (equipABuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          equipA_ = value;
          onChanged();
        } else {
          equipABuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public Builder setEquipA(
          protocol.EquipAData.EquipA.Builder builderForValue) {
        if (equipABuilder_ == null) {
          equipA_ = builderForValue.build();
          onChanged();
        } else {
          equipABuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public Builder mergeEquipA(protocol.EquipAData.EquipA value) {
        if (equipABuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              equipA_ != protocol.EquipAData.EquipA.getDefaultInstance()) {
            equipA_ =
              protocol.EquipAData.EquipA.newBuilder(equipA_).mergeFrom(value).buildPartial();
          } else {
            equipA_ = value;
          }
          onChanged();
        } else {
          equipABuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public Builder clearEquipA() {
        if (equipABuilder_ == null) {
          equipA_ = protocol.EquipAData.EquipA.getDefaultInstance();
          onChanged();
        } else {
          equipABuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public protocol.EquipAData.EquipA.Builder getEquipABuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getEquipAFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      public protocol.EquipAData.EquipAOrBuilder getEquipAOrBuilder() {
        if (equipABuilder_ != null) {
          return equipABuilder_.getMessageOrBuilder();
        } else {
          return equipA_;
        }
      }
      /**
       * <code>required .protocol.EquipA equipA = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.EquipAData.EquipA, protocol.EquipAData.EquipA.Builder, protocol.EquipAData.EquipAOrBuilder> 
          getEquipAFieldBuilder() {
        if (equipABuilder_ == null) {
          equipABuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.EquipAData.EquipA, protocol.EquipAData.EquipA.Builder, protocol.EquipAData.EquipAOrBuilder>(
                  equipA_,
                  getParentForChildren(),
                  isClean());
          equipA_ = null;
        }
        return equipABuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetEquipA)
    }

    static {
      defaultInstance = new ResponseGetEquipA(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetEquipA)
  }

  public interface RequestDDDEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestDDDEquip}
   *
   * <pre>
   *1323
   * </pre>
   */
  public static final class RequestDDDEquip extends
      com.google.protobuf.GeneratedMessage
      implements RequestDDDEquipOrBuilder {
    // Use RequestDDDEquip.newBuilder() to construct.
    private RequestDDDEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestDDDEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestDDDEquip defaultInstance;
    public static RequestDDDEquip getDefaultInstance() {
      return defaultInstance;
    }

    public RequestDDDEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestDDDEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipAData.internal_static_protocol_RequestDDDEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipAData.internal_static_protocol_RequestDDDEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipAData.RequestDDDEquip.class, protocol.EquipAData.RequestDDDEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestDDDEquip> PARSER =
        new com.google.protobuf.AbstractParser<RequestDDDEquip>() {
      public RequestDDDEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestDDDEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestDDDEquip> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipAData.RequestDDDEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.RequestDDDEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipAData.RequestDDDEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.RequestDDDEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipAData.RequestDDDEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestDDDEquip}
     *
     * <pre>
     *1323
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipAData.RequestDDDEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipAData.internal_static_protocol_RequestDDDEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipAData.internal_static_protocol_RequestDDDEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipAData.RequestDDDEquip.class, protocol.EquipAData.RequestDDDEquip.Builder.class);
      }

      // Construct using protocol.EquipAData.RequestDDDEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipAData.internal_static_protocol_RequestDDDEquip_descriptor;
      }

      public protocol.EquipAData.RequestDDDEquip getDefaultInstanceForType() {
        return protocol.EquipAData.RequestDDDEquip.getDefaultInstance();
      }

      public protocol.EquipAData.RequestDDDEquip build() {
        protocol.EquipAData.RequestDDDEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipAData.RequestDDDEquip buildPartial() {
        protocol.EquipAData.RequestDDDEquip result = new protocol.EquipAData.RequestDDDEquip(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipAData.RequestDDDEquip) {
          return mergeFrom((protocol.EquipAData.RequestDDDEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipAData.RequestDDDEquip other) {
        if (other == protocol.EquipAData.RequestDDDEquip.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipAData.RequestDDDEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipAData.RequestDDDEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestDDDEquip)
    }

    static {
      defaultInstance = new RequestDDDEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestDDDEquip)
  }

  public interface ResponseDDDEquipOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.EquipA equipA = 1;
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    java.util.List<protocol.EquipAData.EquipA> 
        getEquipAList();
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    protocol.EquipAData.EquipA getEquipA(int index);
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    int getEquipACount();
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    java.util.List<? extends protocol.EquipAData.EquipAOrBuilder> 
        getEquipAOrBuilderList();
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    protocol.EquipAData.EquipAOrBuilder getEquipAOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseDDDEquip}
   *
   * <pre>
   *2323
   * </pre>
   */
  public static final class ResponseDDDEquip extends
      com.google.protobuf.GeneratedMessage
      implements ResponseDDDEquipOrBuilder {
    // Use ResponseDDDEquip.newBuilder() to construct.
    private ResponseDDDEquip(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseDDDEquip(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseDDDEquip defaultInstance;
    public static ResponseDDDEquip getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseDDDEquip getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseDDDEquip(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                equipA_ = new java.util.ArrayList<protocol.EquipAData.EquipA>();
                mutable_bitField0_ |= 0x00000001;
              }
              equipA_.add(input.readMessage(protocol.EquipAData.EquipA.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          equipA_ = java.util.Collections.unmodifiableList(equipA_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.EquipAData.internal_static_protocol_ResponseDDDEquip_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.EquipAData.internal_static_protocol_ResponseDDDEquip_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.EquipAData.ResponseDDDEquip.class, protocol.EquipAData.ResponseDDDEquip.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseDDDEquip> PARSER =
        new com.google.protobuf.AbstractParser<ResponseDDDEquip>() {
      public ResponseDDDEquip parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseDDDEquip(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseDDDEquip> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.EquipA equipA = 1;
    public static final int EQUIPA_FIELD_NUMBER = 1;
    private java.util.List<protocol.EquipAData.EquipA> equipA_;
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public java.util.List<protocol.EquipAData.EquipA> getEquipAList() {
      return equipA_;
    }
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public java.util.List<? extends protocol.EquipAData.EquipAOrBuilder> 
        getEquipAOrBuilderList() {
      return equipA_;
    }
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public int getEquipACount() {
      return equipA_.size();
    }
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public protocol.EquipAData.EquipA getEquipA(int index) {
      return equipA_.get(index);
    }
    /**
     * <code>repeated .protocol.EquipA equipA = 1;</code>
     *
     * <pre>
     * 装备信息
     * </pre>
     */
    public protocol.EquipAData.EquipAOrBuilder getEquipAOrBuilder(
        int index) {
      return equipA_.get(index);
    }

    private void initFields() {
      equipA_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getEquipACount(); i++) {
        if (!getEquipA(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < equipA_.size(); i++) {
        output.writeMessage(1, equipA_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < equipA_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, equipA_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.EquipAData.ResponseDDDEquip parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.EquipAData.ResponseDDDEquip parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.EquipAData.ResponseDDDEquip prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseDDDEquip}
     *
     * <pre>
     *2323
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.EquipAData.ResponseDDDEquipOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.EquipAData.internal_static_protocol_ResponseDDDEquip_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.EquipAData.internal_static_protocol_ResponseDDDEquip_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.EquipAData.ResponseDDDEquip.class, protocol.EquipAData.ResponseDDDEquip.Builder.class);
      }

      // Construct using protocol.EquipAData.ResponseDDDEquip.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getEquipAFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (equipABuilder_ == null) {
          equipA_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          equipABuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.EquipAData.internal_static_protocol_ResponseDDDEquip_descriptor;
      }

      public protocol.EquipAData.ResponseDDDEquip getDefaultInstanceForType() {
        return protocol.EquipAData.ResponseDDDEquip.getDefaultInstance();
      }

      public protocol.EquipAData.ResponseDDDEquip build() {
        protocol.EquipAData.ResponseDDDEquip result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.EquipAData.ResponseDDDEquip buildPartial() {
        protocol.EquipAData.ResponseDDDEquip result = new protocol.EquipAData.ResponseDDDEquip(this);
        int from_bitField0_ = bitField0_;
        if (equipABuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            equipA_ = java.util.Collections.unmodifiableList(equipA_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.equipA_ = equipA_;
        } else {
          result.equipA_ = equipABuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.EquipAData.ResponseDDDEquip) {
          return mergeFrom((protocol.EquipAData.ResponseDDDEquip)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.EquipAData.ResponseDDDEquip other) {
        if (other == protocol.EquipAData.ResponseDDDEquip.getDefaultInstance()) return this;
        if (equipABuilder_ == null) {
          if (!other.equipA_.isEmpty()) {
            if (equipA_.isEmpty()) {
              equipA_ = other.equipA_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureEquipAIsMutable();
              equipA_.addAll(other.equipA_);
            }
            onChanged();
          }
        } else {
          if (!other.equipA_.isEmpty()) {
            if (equipABuilder_.isEmpty()) {
              equipABuilder_.dispose();
              equipABuilder_ = null;
              equipA_ = other.equipA_;
              bitField0_ = (bitField0_ & ~0x00000001);
              equipABuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getEquipAFieldBuilder() : null;
            } else {
              equipABuilder_.addAllMessages(other.equipA_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getEquipACount(); i++) {
          if (!getEquipA(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.EquipAData.ResponseDDDEquip parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.EquipAData.ResponseDDDEquip) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.EquipA equipA = 1;
      private java.util.List<protocol.EquipAData.EquipA> equipA_ =
        java.util.Collections.emptyList();
      private void ensureEquipAIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          equipA_ = new java.util.ArrayList<protocol.EquipAData.EquipA>(equipA_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.EquipAData.EquipA, protocol.EquipAData.EquipA.Builder, protocol.EquipAData.EquipAOrBuilder> equipABuilder_;

      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public java.util.List<protocol.EquipAData.EquipA> getEquipAList() {
        if (equipABuilder_ == null) {
          return java.util.Collections.unmodifiableList(equipA_);
        } else {
          return equipABuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public int getEquipACount() {
        if (equipABuilder_ == null) {
          return equipA_.size();
        } else {
          return equipABuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipAData.EquipA getEquipA(int index) {
        if (equipABuilder_ == null) {
          return equipA_.get(index);
        } else {
          return equipABuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder setEquipA(
          int index, protocol.EquipAData.EquipA value) {
        if (equipABuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipAIsMutable();
          equipA_.set(index, value);
          onChanged();
        } else {
          equipABuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder setEquipA(
          int index, protocol.EquipAData.EquipA.Builder builderForValue) {
        if (equipABuilder_ == null) {
          ensureEquipAIsMutable();
          equipA_.set(index, builderForValue.build());
          onChanged();
        } else {
          equipABuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder addEquipA(protocol.EquipAData.EquipA value) {
        if (equipABuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipAIsMutable();
          equipA_.add(value);
          onChanged();
        } else {
          equipABuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder addEquipA(
          int index, protocol.EquipAData.EquipA value) {
        if (equipABuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEquipAIsMutable();
          equipA_.add(index, value);
          onChanged();
        } else {
          equipABuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder addEquipA(
          protocol.EquipAData.EquipA.Builder builderForValue) {
        if (equipABuilder_ == null) {
          ensureEquipAIsMutable();
          equipA_.add(builderForValue.build());
          onChanged();
        } else {
          equipABuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder addEquipA(
          int index, protocol.EquipAData.EquipA.Builder builderForValue) {
        if (equipABuilder_ == null) {
          ensureEquipAIsMutable();
          equipA_.add(index, builderForValue.build());
          onChanged();
        } else {
          equipABuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder addAllEquipA(
          java.lang.Iterable<? extends protocol.EquipAData.EquipA> values) {
        if (equipABuilder_ == null) {
          ensureEquipAIsMutable();
          super.addAll(values, equipA_);
          onChanged();
        } else {
          equipABuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder clearEquipA() {
        if (equipABuilder_ == null) {
          equipA_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          equipABuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public Builder removeEquipA(int index) {
        if (equipABuilder_ == null) {
          ensureEquipAIsMutable();
          equipA_.remove(index);
          onChanged();
        } else {
          equipABuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipAData.EquipA.Builder getEquipABuilder(
          int index) {
        return getEquipAFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipAData.EquipAOrBuilder getEquipAOrBuilder(
          int index) {
        if (equipABuilder_ == null) {
          return equipA_.get(index);  } else {
          return equipABuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public java.util.List<? extends protocol.EquipAData.EquipAOrBuilder> 
           getEquipAOrBuilderList() {
        if (equipABuilder_ != null) {
          return equipABuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(equipA_);
        }
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipAData.EquipA.Builder addEquipABuilder() {
        return getEquipAFieldBuilder().addBuilder(
            protocol.EquipAData.EquipA.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public protocol.EquipAData.EquipA.Builder addEquipABuilder(
          int index) {
        return getEquipAFieldBuilder().addBuilder(
            index, protocol.EquipAData.EquipA.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.EquipA equipA = 1;</code>
       *
       * <pre>
       * 装备信息
       * </pre>
       */
      public java.util.List<protocol.EquipAData.EquipA.Builder> 
           getEquipABuilderList() {
        return getEquipAFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.EquipAData.EquipA, protocol.EquipAData.EquipA.Builder, protocol.EquipAData.EquipAOrBuilder> 
          getEquipAFieldBuilder() {
        if (equipABuilder_ == null) {
          equipABuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.EquipAData.EquipA, protocol.EquipAData.EquipA.Builder, protocol.EquipAData.EquipAOrBuilder>(
                  equipA_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          equipA_ = null;
        }
        return equipABuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseDDDEquip)
    }

    static {
      defaultInstance = new ResponseDDDEquip(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseDDDEquip)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_EquipA_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_EquipA_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestEquipF_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestEquipF_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetEquipA_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetEquipA_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetEquipA_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetEquipA_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestDDDEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestDDDEquip_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseDDDEquip_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseDDDEquip_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014equipA.proto\022\010protocol\032\013proto.proto\"\262\001" +
      "\n\006EquipA\022\013\n\003Eid\030\001 \002(\005\022\n\n\002ad\030\002 \001(\005\022\n\n\002ap\030" +
      "\003 \001(\005\022\013\n\003arm\030\004 \001(\005\022\013\n\003mdf\030\005 \001(\005\022\r\n\005speed" +
      "\030\006 \001(\005\022\n\n\002hp\030\007 \001(\005\022\013\n\003ead\030\010 \001(\005\022\014\n\004emdf\030" +
      "\t \001(\005\022\013\n\003esp\030\n \001(\005\022\013\n\003ehp\030\013 \001(\005\022\013\n\003eap\030\014" +
      " \001(\005\022\014\n\004earm\030\r \001(\005\"\271\001\n\rRequestEquipF\022\013\n\003" +
      "Eid\030\001 \002(\005\022\n\n\002ad\030\002 \001(\005\022\n\n\002ap\030\003 \001(\005\022\013\n\003arm" +
      "\030\004 \001(\005\022\013\n\003mdf\030\005 \001(\005\022\r\n\005speed\030\006 \001(\005\022\n\n\002hp" +
      "\030\007 \001(\005\022\013\n\003ead\030\010 \001(\005\022\014\n\004emdf\030\t \001(\005\022\013\n\003esp" +
      "\030\n \001(\005\022\013\n\003ehp\030\013 \001(\005\022\013\n\003eap\030\014 \001(\005\022\014\n\004earm",
      "\030\r \001(\005\"\022\n\020RequestGetEquipA\"5\n\021ResponseGe" +
      "tEquipA\022 \n\006equipA\030\001 \002(\0132\020.protocol.Equip" +
      "A\"\021\n\017RequestDDDEquip\"4\n\020ResponseDDDEquip" +
      "\022 \n\006equipA\030\001 \003(\0132\020.protocol.EquipAB\014B\nEq" +
      "uipAData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_EquipA_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_EquipA_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_EquipA_descriptor,
              new java.lang.String[] { "Eid", "Ad", "Ap", "Arm", "Mdf", "Speed", "Hp", "Ead", "Emdf", "Esp", "Ehp", "Eap", "Earm", });
          internal_static_protocol_RequestEquipF_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestEquipF_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestEquipF_descriptor,
              new java.lang.String[] { "Eid", "Ad", "Ap", "Arm", "Mdf", "Speed", "Hp", "Ead", "Emdf", "Esp", "Ehp", "Eap", "Earm", });
          internal_static_protocol_RequestGetEquipA_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestGetEquipA_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetEquipA_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetEquipA_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseGetEquipA_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetEquipA_descriptor,
              new java.lang.String[] { "EquipA", });
          internal_static_protocol_RequestDDDEquip_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_RequestDDDEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestDDDEquip_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseDDDEquip_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_ResponseDDDEquip_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseDDDEquip_descriptor,
              new java.lang.String[] { "EquipA", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
