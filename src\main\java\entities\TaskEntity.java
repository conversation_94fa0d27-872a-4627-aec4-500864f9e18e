package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/1/11.
 */
@Entity
@Table(name = "task", schema = "", catalog = "super_star_fruit")
public class TaskEntity {
    private int id;
    private int version;
    private int type;
    private String uid;
    private int taskid;
    private Integer num;
    private Integer status;
    private String timestamp;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Version
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "taskid")
    public int getTaskid() {
        return taskid;
    }

    public void setTaskid(int taskid) {
        this.taskid = taskid;
    }

    @Basic
    @Column(name = "num")
    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    @Basic
    @Column(name = "status")
    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Basic
    @Column(name = "timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        TaskEntity that = (TaskEntity) o;

        if (id != that.id) return false;
        if (version != that.version) return false;
        if (type != that.type) return false;
        if (taskid != that.taskid) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (num != null ? !num.equals(that.num) : that.num != null) return false;
        if (status != null ? !status.equals(that.status) : that.status != null) return false;
        if (timestamp != null ? !timestamp.equals(that.timestamp) : that.timestamp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + version;
        result = 31 * result + type;
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + taskid;
        result = 31 * result + (num != null ? num.hashCode() : 0);
        result = 31 * result + (status != null ? status.hashCode() : 0);
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "TaskEntity{" +
                "id=" + id +
                ", version=" + version +
                ", type=" + type +
                ", uid='" + uid + '\'' +
                ", taskid=" + taskid +
                ", num=" + num +
                ", status=" + status +
                ", timestamp='" + timestamp + '\'' +
                '}';
    }
}
