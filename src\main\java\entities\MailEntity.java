package entities;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Objects;

@Entity
@Table(name = "mail", schema = "", catalog = "super_star_fruit")
public class MailEntity {
    private int id; //id
    private int mid; //邮件id
    private String subjectType; //主题类型
    private String sender;   //发送者
    private String owner;   //接收方
    private String title;   // 标题
    private String content; //内容
    private int status; //0未读 1已读 2已经处理
    private long timestamp; //发送时间
    private long overdueTime;   //过期时间
    private byte[] attchment;   // 附件
    private int mailId; //邮件类型

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "mid")
    public int getMid() {
        return mid;
    }

    public void setMid(int mid) {
        this.mid = mid;
    }

    @Basic
    @Column(name = "subjectType")
    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    @Basic
    @Column(name = "sender")
    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    @Basic
    @Column(name = "owner")
    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    @Basic
    @Column(name = "title")

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    @Basic
    @Column(name = "content")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Basic
    @Column(name = "status")
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Basic
    @Column(name = "timestamp")
    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    @Basic
    @Column(name = "overdueTime")
    public long getOverdueTime() {
        return overdueTime;
    }

    public void setOverdueTime(long overdueTime) {
        this.overdueTime = overdueTime;
    }

    @Basic
    @Column(name = "attchment")
    public byte[] getAttchment() {
        return attchment;
    }

    public void setAttchment(byte[] attchment) {
        this.attchment = attchment;
    }

    @Basic
    @Column(name = "mailId")
    public int getMailId() {
        return mailId;
    }

    public void setMailId(int mailId) {
        this.mailId = mailId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MailEntity that = (MailEntity) o;
        return getId() == that.getId() &&
                getMid() == that.getMid() &&
                getMailId() == that.getMailId() &&
                getSubjectType() == that.getSubjectType() &&
                getStatus() == that.getStatus() &&
                getTimestamp() == that.getTimestamp() &&
                getOverdueTime() == that.getOverdueTime() &&
                Objects.equals(getSender(), that.getSender()) &&
                Objects.equals(getOwner(), that.getOwner()) &&
                Objects.equals(getTitle(), that.getTitle()) &&
                Objects.equals(getContent(), that.getContent()) &&
                Arrays.equals(getAttchment(), that.getAttchment());
    }

    @Override
    public int hashCode() {

        int result = Objects.hash(getId(), getMid(), getMailId(), getSubjectType(), getSender(), getOwner(), getTitle(), getContent(), getStatus(), getTimestamp(), getOverdueTime());
        result = 31 * result + Arrays.hashCode(getAttchment());
        return result;
    }

    @Override
    public String toString() {
        return "MailEntity{" +
                "id=" + id +
                ", mid=" + mid +
                ", subjectType=" + subjectType +
                ", sender='" + sender + '\'' +
                ", owner='" + owner + '\'' +
                ", mailId='" + mailId + '\'' +
                ", title='" + title + '\'' +
                ", content='" + content + '\'' +
                ", status=" + status +
                ", timestamp=" + timestamp +
                ", overdueTime=" + overdueTime +
                ", attchment=" + Arrays.toString(attchment) +
                '}';
    }
}
