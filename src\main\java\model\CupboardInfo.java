package model;

import entities.PartEntity;
import protocol.UserData;

/**
 * Created by nara on 2018/3/12.
 */
public class CupboardInfo {
    private int roleId;
    private int id;
    private int part1;
    private int part2;
    private int part3;
    private int part4;
    private int part5;
    private int part6;
    private int part7;
    private int part8;
    private int part9;

    public CupboardInfo() {
        this.part1 = 0;
        this.part2 = 0;
        this.part3 = 0;
        this.part4 = 0;
        this.part5 = 0;
        this.part6 = 0;
        this.part7 = 0;
        this.part8 = 0;
        this.part9 = 0;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPart9() {
        return part9;
    }

    public void setPart9(int part9) {
        this.part9 = part9;
    }

    public int getPart8() {
        return part8;
    }

    public void setPart8(int part8) {
        this.part8 = part8;
    }

    public int getPart7() {
        return part7;
    }

    public void setPart7(int part7) {
        this.part7 = part7;
    }

    public int getPart6() {
        return part6;
    }

    public void setPart6(int part6) {
        this.part6 = part6;
    }

    public int getPart5() {
        return part5;
    }

    public void setPart5(int part5) {
        this.part5 = part5;
    }

    public int getPart4() {
        return part4;
    }

    public void setPart4(int part4) {
        this.part4 = part4;
    }

    public int getPart3() {
        return part3;
    }

    public void setPart3(int part3) {
        this.part3 = part3;
    }

    public int getPart2() {
        return part2;
    }

    public void setPart2(int part2) {
        this.part2 = part2;
    }

    public int getPart1() {
        return part1;
    }

    public void setPart1(int part1) {
        this.part1 = part1;
    }

    public static UserData.Part setCupboardToPartData(CupboardInfo cupboardInfo) {
        UserData.Part.Builder builder = UserData.Part.newBuilder();
        builder.setPart1(cupboardInfo.getPart1());
        builder.setPart2(cupboardInfo.getPart2());
        builder.setPart3(cupboardInfo.getPart3());
        builder.setPart4(cupboardInfo.getPart4());
        builder.setPart5(cupboardInfo.getPart5());
        builder.setPart6(cupboardInfo.getPart6());
        builder.setPart7(cupboardInfo.getPart7());
        builder.setPart8(cupboardInfo.getPart8());
        builder.setPart9(cupboardInfo.getPart9());
        return builder.build();
    }

    public static CupboardInfo setPartEntityToCupboard(PartEntity partEntity) {
        CupboardInfo cupboardInfo = new CupboardInfo();
        cupboardInfo.setId(partEntity.getCupboard());
        if (partEntity.getPart1() != null) {
            cupboardInfo.setPart1(partEntity.getPart1());
        }
        if (partEntity.getPart2() != null) {
            cupboardInfo.setPart2(partEntity.getPart2());
        }
        if (partEntity.getPart3() != null) {
            cupboardInfo.setPart3(partEntity.getPart3());
        }
        if (partEntity.getPart4() != null) {
            cupboardInfo.setPart4(partEntity.getPart4());
        }
        if (partEntity.getPart5() != null) {
            cupboardInfo.setPart5(partEntity.getPart5());
        }
        if (partEntity.getPart6() != null) {
            cupboardInfo.setPart6(partEntity.getPart6());
        }
        if (partEntity.getPart7() != null) {
            cupboardInfo.setPart7(partEntity.getPart7());
        }
        if (partEntity.getPart8() != null) {
            cupboardInfo.setPart8(partEntity.getPart8());
        }
        if (partEntity.getPart9() != null) {
            cupboardInfo.setPart9(partEntity.getPart9());
        }
        return cupboardInfo;
    }
}
