package module.achievement;

import entities.CompleteAchievementEntity;
import manager.MySql;
import org.hibernate.Session;
import java.util.List;

public class CompleteAchievementServiceDao {
    private static CompleteAchievementServiceDao inst = null;
    public static CompleteAchievementServiceDao getInstance() {
        if (inst == null) {
            inst = new CompleteAchievementServiceDao();
        }
        return inst;
    }


    public void insert(CompleteAchievementEntity entity){
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public void update(CompleteAchievementEntity entity) {
        Session session = MySql.getSession();
        session.update(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public CompleteAchievementEntity GetOne(String uid, String achievementId) {
        StringBuffer stringBuffer = new StringBuffer("from CompleteAchievementEntity where uid='").append(uid).append("'")
                .append(" and achievement_id='").append(achievementId).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (CompleteAchievementEntity) entity;
    }

    public List<Object> GetAll(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from CompleteAchievementEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());
        return list;
    }
}
