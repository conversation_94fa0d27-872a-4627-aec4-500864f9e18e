package module.pay;

import entities.PayorderEntity;
import manager.Redis;
import manager.ReportManager;
import module.callback.CallBackManager;
import module.callback.CallBackOrder;
import module.login.ILogin;
import module.login.LoginDao;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import common.SuperConfig;
import protocol.ItemData;
import protocol.PayData;
import protocol.ProtoData;

import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2019/4/2.
 */
public class PayCallBack extends CallBackManager {
    private static Logger log = LoggerFactory.getLogger(PayCallBack.class);
    private static Logger paylog = LoggerFactory.getLogger("paylog");

    public PayCallBack(int callBackId){
        super(callBackId);
    }

    public void execute(Object object) {
        switch (callBackId) {
            case CallBackOrder.PAYFINDBACK:
                payFindBack(object);
                break;
            case CallBackOrder.GOOGLEPAYFINDBACK:
                googlePayFindBack(object);
                break;
   

        }
    }

    private void payFindBack(Object object){
        PayData.ResponseSubmitPayBack.Builder builder = PayData.ResponseSubmitPayBack.newBuilder();
        String uid = (String)parameterList.get(0);
        String transaction_id = (String)parameterList.get(1);
        String product_id = (String)parameterList.get(2);
        String cp_order = (String)parameterList.get(3);
        builder.setTransactionID(cp_order);
        PayorderEntity payorderEntity = (PayorderEntity)object;
        if (payorderEntity == null){
            int payId = -1;
            Redis jedis = Redis.getInstance();
            Iterator<String> iterator = jedis.keys("payconfig:*").iterator();
            while (iterator.hasNext()){
                String key = iterator.next();
                Map<String,String> payMap = jedis.hgetAll(key);
                String productId_excel = payMap.get("Product_ID");
                if (productId_excel.equals(product_id)){
                    payId = Integer.parseInt(payMap.get("ID"));
                    break;
                }
            }
            if (payId == -1){
                log.info("===========payBack=========product_id not exist!!!>>>uid:"+uid+",transaction_id:"+transaction_id+",product_id:"+product_id);
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            }else {
                builder.setErrorId(0);
                IPay iPay = PayDao.getInstance();
                List<ItemData.Item.Builder> listItem = iPay.paySuccess(uid,payId,product_id,transaction_id,0);
                for(int i = 0; i < listItem.size();i++)
                    builder.addItem(listItem.get(i));

                builder.setSuccessId(payId);

                Map<String, String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_PAY, payId);

                if (mapPay.get("firstRecharge").equals("1"))
                {
                    ILogin iLogin = LoginDao.getInstance();
                    builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                }                 
                else
                    builder.setFirstRecharge(0);                   
            }
        }else {
            log.info("===========payBack=========has been paied!>>>uid:"+uid+",transaction_id:"+transaction_id+",product_id:"+product_id);
            builder.setErrorId(ProtoData.ErrorCode.PAYEXIST_VALUE);
        }

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSESUBMITPAYBACK_VALUE, builder.build().toByteArray());
    }

    private void googlePayFindBack(Object object){
        PayData.ResponseGooglePay.Builder builder = PayData.ResponseGooglePay.newBuilder();
        String uid = (String)parameterList.get(0);
        String transaction_id = (String)parameterList.get(1);
        String product_id = (String)parameterList.get(2);
        String cp_order = (String)parameterList.get(3);
        builder.setTransactionID(cp_order);
        PayorderEntity payorderEntity = (PayorderEntity)object;
        if (payorderEntity == null){
            int payId = -1;
            Redis jedis = Redis.getInstance();
            Iterator<String> iterator = jedis.keys("googlepay:*").iterator();
            while (iterator.hasNext()){
                String key = iterator.next();
                Map<String,String> payMap = jedis.hgetAll(key);
                String productId_excel = payMap.get("Product_ID");
                if (productId_excel.equals(product_id)){
                    payId = Integer.parseInt(payMap.get("ID"));
                    break;
                }
            }
            if (payId == -1){
                log.info("===========payBack=========product_id not exist!!!>>>uid:"+uid+",transaction_id:"+transaction_id+",product_id:"+product_id);
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            }else {
                builder.setErrorId(0);
                IPay iPay = PayDao.getInstance();
                List<ItemData.Item.Builder> listItem = iPay.paySuccess(uid,payId,product_id,transaction_id,1);
                for(int i = 0; i < listItem.size();i++)
                    builder.addItem(listItem.get(i));
                builder.setSuccessId(payId);

                Map<String, String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payId);

                if (mapPay.get("firstRecharge").equals("1"))
                {
                    ILogin iLogin = LoginDao.getInstance();
                    builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                }                 
                else
                    builder.setFirstRecharge(0);                  
            }
        }else {
            log.info("===========payBack=========has been paied!>>>uid:"+uid+",transaction_id:"+transaction_id+",product_id:"+product_id);
            builder.setErrorId(ProtoData.ErrorCode.PAYEXIST_VALUE);
        }

        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEGOOGLEPAY_VALUE, builder.build().toByteArray());
    }    
}
