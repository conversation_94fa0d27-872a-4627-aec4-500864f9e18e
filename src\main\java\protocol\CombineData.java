// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: combine.proto

package protocol;

public final class CombineData {
  private CombineData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestPetCombineOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool free = 1;
    /**
     * <code>required bool free = 1;</code>
     */
    boolean hasFree();
    /**
     * <code>required bool free = 1;</code>
     */
    boolean getFree();

    // repeated int32 petId = 2;
    /**
     * <code>repeated int32 petId = 2;</code>
     */
    java.util.List<java.lang.Integer> getPetIdList();
    /**
     * <code>repeated int32 petId = 2;</code>
     */
    int getPetIdCount();
    /**
     * <code>repeated int32 petId = 2;</code>
     */
    int getPetId(int index);

    // required int32 gold = 3;
    /**
     * <code>required int32 gold = 3;</code>
     */
    boolean hasGold();
    /**
     * <code>required int32 gold = 3;</code>
     */
    int getGold();
  }
  /**
   * Protobuf type {@code protocol.RequestPetCombine}
   *
   * <pre>
   *1265
   * </pre>
   */
  public static final class RequestPetCombine extends
      com.google.protobuf.GeneratedMessage
      implements RequestPetCombineOrBuilder {
    // Use RequestPetCombine.newBuilder() to construct.
    private RequestPetCombine(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPetCombine(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPetCombine defaultInstance;
    public static RequestPetCombine getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPetCombine getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPetCombine(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              free_ = input.readBool();
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                petId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              petId_.add(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002) && input.getBytesUntilLimit() > 0) {
                petId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                petId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              gold_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          petId_ = java.util.Collections.unmodifiableList(petId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.CombineData.internal_static_protocol_RequestPetCombine_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.CombineData.internal_static_protocol_RequestPetCombine_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.CombineData.RequestPetCombine.class, protocol.CombineData.RequestPetCombine.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPetCombine> PARSER =
        new com.google.protobuf.AbstractParser<RequestPetCombine>() {
      public RequestPetCombine parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPetCombine(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPetCombine> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool free = 1;
    public static final int FREE_FIELD_NUMBER = 1;
    private boolean free_;
    /**
     * <code>required bool free = 1;</code>
     */
    public boolean hasFree() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool free = 1;</code>
     */
    public boolean getFree() {
      return free_;
    }

    // repeated int32 petId = 2;
    public static final int PETID_FIELD_NUMBER = 2;
    private java.util.List<java.lang.Integer> petId_;
    /**
     * <code>repeated int32 petId = 2;</code>
     */
    public java.util.List<java.lang.Integer>
        getPetIdList() {
      return petId_;
    }
    /**
     * <code>repeated int32 petId = 2;</code>
     */
    public int getPetIdCount() {
      return petId_.size();
    }
    /**
     * <code>repeated int32 petId = 2;</code>
     */
    public int getPetId(int index) {
      return petId_.get(index);
    }

    // required int32 gold = 3;
    public static final int GOLD_FIELD_NUMBER = 3;
    private int gold_;
    /**
     * <code>required int32 gold = 3;</code>
     */
    public boolean hasGold() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 gold = 3;</code>
     */
    public int getGold() {
      return gold_;
    }

    private void initFields() {
      free_ = false;
      petId_ = java.util.Collections.emptyList();
      gold_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasFree()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGold()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, free_);
      }
      for (int i = 0; i < petId_.size(); i++) {
        output.writeInt32(2, petId_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(3, gold_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, free_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < petId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(petId_.get(i));
        }
        size += dataSize;
        size += 1 * getPetIdList().size();
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, gold_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.CombineData.RequestPetCombine parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.CombineData.RequestPetCombine parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.CombineData.RequestPetCombine parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.CombineData.RequestPetCombine parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.CombineData.RequestPetCombine prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPetCombine}
     *
     * <pre>
     *1265
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.CombineData.RequestPetCombineOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.CombineData.internal_static_protocol_RequestPetCombine_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.CombineData.internal_static_protocol_RequestPetCombine_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.CombineData.RequestPetCombine.class, protocol.CombineData.RequestPetCombine.Builder.class);
      }

      // Construct using protocol.CombineData.RequestPetCombine.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        free_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        petId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        gold_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.CombineData.internal_static_protocol_RequestPetCombine_descriptor;
      }

      public protocol.CombineData.RequestPetCombine getDefaultInstanceForType() {
        return protocol.CombineData.RequestPetCombine.getDefaultInstance();
      }

      public protocol.CombineData.RequestPetCombine build() {
        protocol.CombineData.RequestPetCombine result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.CombineData.RequestPetCombine buildPartial() {
        protocol.CombineData.RequestPetCombine result = new protocol.CombineData.RequestPetCombine(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.free_ = free_;
        if (((bitField0_ & 0x00000002) == 0x00000002)) {
          petId_ = java.util.Collections.unmodifiableList(petId_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.petId_ = petId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000002;
        }
        result.gold_ = gold_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.CombineData.RequestPetCombine) {
          return mergeFrom((protocol.CombineData.RequestPetCombine)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.CombineData.RequestPetCombine other) {
        if (other == protocol.CombineData.RequestPetCombine.getDefaultInstance()) return this;
        if (other.hasFree()) {
          setFree(other.getFree());
        }
        if (!other.petId_.isEmpty()) {
          if (petId_.isEmpty()) {
            petId_ = other.petId_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensurePetIdIsMutable();
            petId_.addAll(other.petId_);
          }
          onChanged();
        }
        if (other.hasGold()) {
          setGold(other.getGold());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasFree()) {
          
          return false;
        }
        if (!hasGold()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.CombineData.RequestPetCombine parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.CombineData.RequestPetCombine) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool free = 1;
      private boolean free_ ;
      /**
       * <code>required bool free = 1;</code>
       */
      public boolean hasFree() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool free = 1;</code>
       */
      public boolean getFree() {
        return free_;
      }
      /**
       * <code>required bool free = 1;</code>
       */
      public Builder setFree(boolean value) {
        bitField0_ |= 0x00000001;
        free_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool free = 1;</code>
       */
      public Builder clearFree() {
        bitField0_ = (bitField0_ & ~0x00000001);
        free_ = false;
        onChanged();
        return this;
      }

      // repeated int32 petId = 2;
      private java.util.List<java.lang.Integer> petId_ = java.util.Collections.emptyList();
      private void ensurePetIdIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          petId_ = new java.util.ArrayList<java.lang.Integer>(petId_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public java.util.List<java.lang.Integer>
          getPetIdList() {
        return java.util.Collections.unmodifiableList(petId_);
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public int getPetIdCount() {
        return petId_.size();
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public int getPetId(int index) {
        return petId_.get(index);
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public Builder setPetId(
          int index, int value) {
        ensurePetIdIsMutable();
        petId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public Builder addPetId(int value) {
        ensurePetIdIsMutable();
        petId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public Builder addAllPetId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensurePetIdIsMutable();
        super.addAll(values, petId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 2;</code>
       */
      public Builder clearPetId() {
        petId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // required int32 gold = 3;
      private int gold_ ;
      /**
       * <code>required int32 gold = 3;</code>
       */
      public boolean hasGold() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 gold = 3;</code>
       */
      public int getGold() {
        return gold_;
      }
      /**
       * <code>required int32 gold = 3;</code>
       */
      public Builder setGold(int value) {
        bitField0_ |= 0x00000004;
        gold_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 gold = 3;</code>
       */
      public Builder clearGold() {
        bitField0_ = (bitField0_ & ~0x00000004);
        gold_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPetCombine)
    }

    static {
      defaultInstance = new RequestPetCombine(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPetCombine)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPetCombine_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPetCombine_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rcombine.proto\022\010protocol\">\n\021RequestPetC" +
      "ombine\022\014\n\004free\030\001 \002(\010\022\r\n\005petId\030\002 \003(\005\022\014\n\004g" +
      "old\030\003 \002(\005B\rB\013CombineData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestPetCombine_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestPetCombine_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPetCombine_descriptor,
              new java.lang.String[] { "Free", "PetId", "Gold", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
