package module.room;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MissionData;
import protocol.ProtoData;

/**
 * Created by nara on 2018/6/1.
 */
public class RoomService {
    private static Logger log = LoggerFactory.getLogger(RoomService.class);
    private static RoomService inst = null;
    public static RoomService getInstance() {
        if (inst == null) {
            inst = new RoomService();
        }
        return inst;
    }

    public byte[] joinRoom(byte[] bytes,String uid){
        MissionData.RequestJoinRoom requestJoinRoom = null;
        MissionData.ResponseJoinRoom.Builder builder = MissionData.ResponseJoinRoom.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestJoinRoom = MissionData.RequestJoinRoom.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestJoinRoom == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[joinRoom] error");
            } else {
                IRoom iRoom = RoomDao.getInstance();
                builder = iRoom.joinRoom(uid, requestJoinRoom.getType(),requestJoinRoom.getId(),requestJoinRoom.getPwd());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] createRoom(byte[] bytes,String uid){
        MissionData.RequestCreateRoom requestCreateRoom = null;
        MissionData.ResponseCreateRoom.Builder builder = MissionData.ResponseCreateRoom.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestCreateRoom = MissionData.RequestCreateRoom.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestCreateRoom == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[createRoom] error");
            } else {
                IRoom iRoom = RoomDao.getInstance();
                builder = iRoom.createRoom(uid, requestCreateRoom.getType(),requestCreateRoom.getName(),requestCreateRoom.getPwd());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] leaveRoom(String uid){
        MissionData.ResponseLeaveRoom.Builder builder = MissionData.ResponseLeaveRoom.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            IRoom iRoom = RoomDao.getInstance();
            builder = iRoom.leaveRoom(uid);
        }
        return builder.build().toByteArray();
    }

    public byte[] updateRoomInfo(byte[] bytes,String uid){
        MissionData.RequestUpdateRoomInfo requestUpdateRoomInfo = null;
        MissionData.ResponseUpdateRoomInfo.Builder builder = MissionData.ResponseUpdateRoomInfo.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestUpdateRoomInfo = MissionData.RequestUpdateRoomInfo.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestUpdateRoomInfo == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[updateRoomInfo] error");
            } else {
                IRoom iRoom = RoomDao.getInstance();
                builder = iRoom.updateRoomInfo(uid, requestUpdateRoomInfo.getType(), requestUpdateRoomInfo.getName(), requestUpdateRoomInfo.getPwd());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] updateRoomRole(byte[] bytes,String uid){
        MissionData.RequestUpdateRoomRole requestUpdateRoomRole = null;
        MissionData.ResponseUpdateRoomRole.Builder builder = MissionData.ResponseUpdateRoomRole.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestUpdateRoomRole = MissionData.RequestUpdateRoomRole.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestUpdateRoomRole == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[updateRoomRole] error");
            } else {
                IRoom iRoom = RoomDao.getInstance();
                builder = iRoom.updateRoomRole(uid, requestUpdateRoomRole.getStatus(), requestUpdateRoomRole.getQueue(),requestUpdateRoomRole.getPosition());
            }
        }
        return  builder==null?null:builder.build().toByteArray();
    }

    public byte[] joinFightHall(byte[] bytes,String uid){
        MissionData.RequestJoinFightHall requestJoinFightHall = null;
        MissionData.ResponseJoinFightHall.Builder builder = MissionData.ResponseJoinFightHall.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestJoinFightHall = MissionData.RequestJoinFightHall.parseFrom(bytes);
              //  /// System.out.println(requestJoinFightHall.getHall()+"hall"+requestJoinFightHall.getType()+"type");
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestJoinFightHall == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[joinRoom] error");
            } else {
                IRoom iRoom = RoomDao.getInstance();
                builder = iRoom.joinFightHall(uid, requestJoinFightHall.getType(),requestJoinFightHall.getHall());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] startInRoom(String uid){
        MissionData.ResponseStartInRoom.Builder builder = MissionData.ResponseStartInRoom.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            IRoom iRoom = RoomDao.getInstance();
            int val = iRoom.startInRoom(uid);
            builder.setErrorId(val);
        }
        return builder.build().toByteArray();
    }

    public byte[] overMission(byte[] bytes){
        MissionData.MRequestOverMission mRequestOverMission = null;
        MissionData.SResponseOverMission.Builder builder = MissionData.SResponseOverMission.newBuilder();
        try {
            mRequestOverMission = MissionData.MRequestOverMission.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        if (mRequestOverMission == null) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            log.error("[overMission] error");
        } else {
            IRoom iRoom = RoomDao.getInstance();
            builder = iRoom.overMission(mRequestOverMission.getType(),mRequestOverMission.getHall(), mRequestOverMission.getRoomId(),mRequestOverMission.getUidsList());
        }
        return builder.build().toByteArray();
    }
}
