package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/1/17.
 */
@Entity
@Table(name = "item", schema = "", catalog = "super_star_fruit")
public class ItemEntity {
    private int id;
    private int version;
    private String uid;
    private Integer type;
    private Integer itemid;
    private double itemnum;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Version
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Basic
    @Column(name = "itemid")
    public Integer getItemid() {
        return itemid;
    }

    public void setItemid(Integer itemid) {
        this.itemid = itemid;
    }

    @Basic
    @Column(name = "itemnum")
    public double getItemnum() {
        return itemnum;
    }

    public void setItemnum(double itemnum) {
        this.itemnum = itemnum;
    }

    @Override
    public String toString() {
        return "ItemEntity{" +
                "id=" + id +
                ", version=" + version +
                ", uid='" + uid + '\'' +
                ", type=" + type +
                ", itemid=" + itemid +
                ", itemnum=" + itemnum +
                '}';
    }
}
