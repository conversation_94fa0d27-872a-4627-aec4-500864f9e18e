package module.Dispatch;

import Json.ExperienceJson;
import com.google.protobuf.InvalidProtocolBufferException;
import com.googlecode.protobuf.format.JsonFormat;
import common.DispatchConfig;
import common.PetConfig;
import common.SuperConfig;
import entities.*;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import model.CommonInfo;
import module.item.ItemDao;
import module.item.ItemUtils;
import module.pet.PetDao;
import protocol.*;
import utils.MyUtils;

import java.text.SimpleDateFormat;
import java.util.*;

public class DispatchService {
    private static DispatchService inst = null;

    public static DispatchService getInstance() {
        if (inst == null) {
            inst = new DispatchService();
        }
        return inst;
    }

    private static DispatchDao dispatchDao = DispatchDao.getInstance();

    public byte[] getExperiences(byte[] bytes, String uid) {
        ExploreData.ResponseGetExperiences.Builder builder = ExploreData.ResponseGetExperiences.newBuilder();

        try {
            DispatchEntity dispatchEntity = dispatchDao.getAllExperience(uid);
            int nums = dispatchEntity.getFlushNums();
            builder.setErrorId(0);
            builder.setFlushNums(nums);

            List<ExperienceJson> experienceJsonList = dispatchEntity.getExperienceJsons();
            for (ExperienceJson experienceJson : experienceJsonList) {
                builder.addExperience(Dispatch.objectToPB(experienceJson));
            }
        } catch (Exception e) {
            e.printStackTrace();

        }
        ///  /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }


    public byte[] flushExperiences(byte[] bytes, String uid) {
        ExploreData.ResponseFlushExperience.Builder builder = ExploreData.ResponseFlushExperience.newBuilder();
        builder.setErroeId(0);
        ///  /// System.err.println("收到刷新探险请求");
        try {
            DispatchEntity dispatchEntity = dispatchDao.getAllExperience(uid);
            int nums = dispatchEntity.getFlushNums();
            nums--;
            if (nums < 0) {
                builder.setErroeId(1);
            } else {

                ItemUtils.updatePlayerItems(uid, 2, 30, false);
                List<ExperienceJson> newList = new ArrayList<ExperienceJson>();
                List<ExperienceJson> preList = dispatchEntity.getExperienceJsons();
                List<Integer> filterList = new ArrayList<Integer>();
                for (ExperienceJson experienceJson : preList) {
                    int status = experienceJson.getStatus();
                    if (status != 1) {
                        ///           /// System.err.println(experienceJson + "bushuaix ");
                        filterList.add(experienceJson.getKey());
                        newList.add(experienceJson);
                        builder.addExperience(Dispatch.objectToPB(experienceJson));
                    }

                }
                int flushNums = 10 - newList.size();
                List<ExperienceJson> flushList = Dispatch.getNewExperience(filterList, flushNums);
                for (ExperienceJson experienceJson : flushList) {
                    newList.add(experienceJson);
                    builder.addExperience(Dispatch.objectToPB(experienceJson));
                }
                dispatchEntity.setExperienceJsons(newList);
                ///      /// System.err.println("dispatchEntity" + dispatchEntity + dispatchEntity.getExperienceJsons().size());
                dispatchDao.removeDispatchEntity(uid);
                dispatchDao.updateDispatch(uid, dispatchEntity);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        ///  /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }


    //操作探险
    public byte[] operateExperience(byte[] bytes, String uid) {
        ExploreData.ResponseOperateExperience.Builder builder = ExploreData.ResponseOperateExperience.newBuilder();
        ExploreData.RequestOperateExperience requestOperateExperience = null;
        builder.setErrorId(0);
        try {
            requestOperateExperience = ExploreData.RequestOperateExperience.parseFrom(bytes);
            int operateType = requestOperateExperience.getType();
            builder.setType(operateType);
            int key = requestOperateExperience.getExperienceKey();
            List<PetEntity> petEntityList = new ArrayList<PetEntity>();
            PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
            builder.setExperienceKey(key);
            ExperienceJson experienceJson = DispatchDao.getExperience(uid, key + "");
            List<Integer> petIds = experienceJson.getPetId();
            if (petIds == null) {
                petIds = requestOperateExperience.getPetIdList();
                int petNums = petIds.size();
                if (petNums > 5 || petNums < 1) {
                    builder.setErrorId(5);
                    return builder.build().toByteArray();
                }
            }

            DispatchConfig dispatchConfig = (DispatchConfig) SuperConfig.getCongifObject(SuperConfig.dispatchConfig, key);
            if (petIds != null && operateType != 4) {
                for (Integer petId : petIds) {
                    PetEntity petEntity = (PetEntity) PetDao.getInstance().queryPet(uid, petId);
                    ////LockStatus锁定状态（标记位从右往左第1位是通用状态锁定，第2位是编队锁， 第3位是孵化锁 4派遣）
                    if (operateType == 1) {
                        petEntity.setLockStatus((1 << PetConfig.dispatchLockIndex) + petEntity.getLockStatus());
                    } else {
                        petEntity.setLockStatus(petEntity.getLockStatus() - (1 << PetConfig.dispatchLockIndex));
                    }
                    MySql.update(petEntity);
                    petEntityList.add(petEntity);
                    ///         /// System.err.println(petEntity);
                    updatePetbuild.addPet(PetEntity.entityToPb(petEntity));
                }
            }

            if (experienceJson == null) {
                builder.setErrorId(1);
            } else {
                int status = experienceJson.getStatus();
                switch (operateType) {
                    case 1:  //进行探险
                        if (status != 1) {
                            builder.setErrorId(2);
                        } else {
                            experienceJson.setStatus(2);
                            experienceJson.setPetId(petIds);
                            int neddTime = dispatchConfig.getFinishTime();
                            experienceJson.setFinishTime(TimerHandler.nowTimeStamp + neddTime * 1000);
                            builder.setExperience(Dispatch.objectToPB(experienceJson));
                            DispatchDao.updateExperience(uid, experienceJson);

                            //更改寵物状态

                        }
                        break;
                    case 2:
                        if (status != 2) {
                            builder.setErrorId(3);
                        } else {
                            //  =(TimerHandler.nowTimeStamp-experienceJson.getFinishTime())/1000+;
                            int loseRate = dispatchConfig.getLoseRate();
                            int needtime = dispatchConfig.getFinishTime();
                            int startTime = (int) (experienceJson.getFinishTime() / 1000) - needtime;
                            int halfCd = ((int) (loseRate * needtime / 100) + startTime) - (int) (TimerHandler.nowTimeStamp / 1000);
                            ///              /// System.err.println("半程时间" + halfCd);
                            if (halfCd <= 0) {//半程获取基础奖励
                                CommonInfo commonInfo = dispatchConfig.getBasicAward();
                                ItemUtils.updatePlayerItems(uid, commonInfo.getKey(), commonInfo.getValue(), true);
                                builder.addItem(ItemUtils.getItemData(commonInfo.getKey(), commonInfo.getValue()));
                            }
                            experienceJson.setFinishTime(0);
                            ;
                            experienceJson.setPetId(null);
                            experienceJson.setStatus(1);
                            builder.setExperience(Dispatch.objectToPB(experienceJson));
                            DispatchDao.updateExperience(uid, experienceJson);
                        }
                        break;
                    case 3:
                        if (status != 2) {
                            builder.setErrorId(3);
                        } else {
                            long finishTime = experienceJson.getFinishTime();
                            int cd = MyUtils.getCountdown(finishTime);
                            if (cd > 0) {
                                builder.setErrorId(4);
                            } else {

                                DispatchEntity dispatchEntity = dispatchDao.getAllExperience(uid);
                                List<ExperienceJson> experienceJsonList = dispatchEntity.getExperienceJsons();
                                ArrayList<Integer> nowExperienceKeyPool = new ArrayList<Integer>();
                                for (ExperienceJson experience : experienceJsonList) {
                                    if (experience.getKey() == key) {
                                        continue;
                                    }
                                    nowExperienceKeyPool.add(experience.getKey());
                                }
                                dispatchDao.removeExperience(uid, key + "");
                                List<ExperienceJson> newExperience = Dispatch.getNewExperience(nowExperienceKeyPool, 1);
                                DispatchDao.updateExperience(uid, newExperience.get(0));
                                builder.setExperience(Dispatch.objectToPB(newExperience.get(0)));
                              /*    experienceJson.setStatus(3);
                                  builder.setExperience(Dispatch.objectToPB(experienceJson));
                                  */
                                List<CommonInfo> commonInfos = dispatchConfig.getNormalAWard();
                                for (CommonInfo commonInfo : commonInfos) {
                                    int itemId = commonInfo.getKey();
                                    int nums = commonInfo.getValue();
                                    ItemUtils.updatePlayerItems(uid, itemId, nums, true);
                                    builder.addItem(ItemUtils.getItemData(itemId, nums));
                                }
                                List<DispatchRule> ruleList = experienceJson.getRules();
                                int upToRules = 0;
                                for (DispatchRule rule : ruleList) {
                                    for (PetEntity pet : petEntityList) {
                                        if (Dispatch.judgeRule(pet, rule)) {
                                            upToRules++;
                                            break;
                                        }
                                    }
                                }
                                int success = (int) (upToRules * 100 / ruleList.size());
                                if (MyUtils.isSuccessRandom(success)) {  //获取额外奖励

                                    List<Integer> itemIdList = dispatchConfig.getExtraAWardId();
                                    List<CommonInfo> commonInfoList = dispatchConfig.getExtraAwardNumsRange();
                                    for (int i = 0; i < itemIdList.size(); i++) {
                                        int itemId = itemIdList.get(i);
                                        CommonInfo commonInfo = commonInfoList.get(i);
                                        int min = commonInfo.getKey();
                                        int max = commonInfo.getValue();
                                        int nums = (int) (Math.random() * (max - min + 1) + min);
                                        ItemUtils.updatePlayerItems(uid, itemId, nums, true);
                                        builder.addItem(ItemUtils.getItemData(itemId, nums));
                                    }

                                }


                            }

                        }
                        break;
                    case 4:
                        if (status != 2) {
                            builder.setErrorId(3);
                        } else {
                            //  DispatchEntity dispatchEntity =dispatchDao.getAllExperience(uid);
                            //  List<ExperienceJson> experienceJsonList=dispatchEntity.getExperienceJsons();
                            long finishTime = experienceJson.getFinishTime();
                            int cd = MyUtils.getCountdown(finishTime);
                            ///               /// System.err.println(cd + "cd");
                            if (cd > 0) {
                                int needHour = (int) Math.ceil(cd / 3600f);
                                ///               /// System.err.println(needHour + "needHour");
                                int nums = needHour * SuperConfig.commonConfigObject.getDispatchAccelerateNeedNums();
                                ItemUtils.updatePlayerItems(uid, 2, 10, false);
                                experienceJson.setFinishTime(0);
                                builder.setExperience(Dispatch.objectToPB(experienceJson));
                                DispatchDao.updateExperience(uid, experienceJson);
                            }
                        }

                        break;
                    default:
                        builder.setErrorId(1);
                }
                updatePetbuild.setType(2);
                updatePetbuild.setErrorId(0);
                ///   /// System.err.println(JsonFormat.printToString(updatePetbuild.build()));
                ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        ///  /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    //进行任务
//    public byte[] requestdailytansks(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
//        TaskData.responsedailytansks.Builder responseBuilder = new TaskData.responsedailytansks.Builder();
//        TaskData.Dailytansks.Builder builder = new TaskData.Dailytansks.Builder();
//        TaskData.requestdailytansks requesttansks = TaskData.requestdailytansks.parseFrom(inBytes);
//        //判断数据库是否有uid
//        StringBuilder sql = new StringBuilder("from ConductTaskEntity where uid='").append(uid).append("'");
//        ConductTaskEntity tasksEntity = (ConductTaskEntity) MySql.queryForOne(sql.toString());
//        if (tasksEntity != null) {
//            if (tasksEntity.getUid().equals(uid)) {
//                //判断字段是否为空
//                if (tasksEntity.getConductTask() != null || !tasksEntity.getConductTask().equals("")) {
//                    //判断数据库中是否已有此类型
//                    TaskData.responsedailytansks responsedailytansks = TaskData.responsedailytansks.parseFrom(tasksEntity.getConductTask());
//                    for (int i = 0; i < responsedailytansks.getDailytansksList().size(); i++) {
//                        if (requesttansks.getType() == (i + 1)) {
//                            builder.setType(requesttansks.getType());
//                            builder.setNum((responsedailytansks.getDailytansksList().get(i).getNum() + requesttansks.getNum()));
//                            responseBuilder.addDailytansks(i, builder);
//                        } else {
//                            builder.setNum(responsedailytansks.getDailytansksList().get(i).getNum());
//                            builder.setType(responsedailytansks.getDailytansksList().get(i).getType());
//                            responseBuilder.addDailytansks(i, builder);
//                        }
//                    }
//                    tasksEntity.setConductTask(responseBuilder.build().toByteArray());
//                    MySql.update(tasksEntity);
//                    return responseBuilder.build().toByteArray();
//                } else {
//                    Date date = new Date();
//                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                    for (int i = 0; i < 4; i++) {
//                        tasksEntity = new ConductTaskEntity();
//                        tasksEntity.setId(0);
//                        tasksEntity.setUid(uid);
//                        tasksEntity.setTime(simpleDateFormat.format(date).trim());
//                        if (requesttansks.getType() == (i + 1)) {
//                            builder.setNum(requesttansks.getNum());
//                            builder.setType(requesttansks.getType());
//                            responseBuilder.addDailytansks(i, builder);
//                        } else {
//                            builder.setNum(0);
//                            builder.setType((i + 1));
//                            responseBuilder.addDailytansks(i, builder);
//                        }
//                    }
//                    tasksEntity.setConductTask(responseBuilder.build().toByteArray());
//                    MySql.update(tasksEntity);
//                    return responseBuilder.build().toByteArray();
//                }
//            } else {
//                Date date = new Date();
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                tasksEntity = new ConductTaskEntity();
//                tasksEntity.setId(0);
//                tasksEntity.setUid(uid);
//                tasksEntity.setTime(simpleDateFormat.format(date).trim());
//                builder.setNum(requesttansks.getNum());
//                builder.setType(requesttansks.getType());
//                responseBuilder.addDailytansks(builder);
//                tasksEntity.setConductTask(responseBuilder.build().toByteArray());
//                MySql.insert(tasksEntity);
//                return responseBuilder.build().toByteArray();
//            }
//        }
//        Date date = new Date();
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        for (int i = 0; i < 4; i++) {
//            tasksEntity = new ConductTaskEntity();
//            tasksEntity.setId(0);
//            tasksEntity.setUid(uid);
//            tasksEntity.setTime(simpleDateFormat.format(date).trim());
//            if (requesttansks.getType() == (i + 1)) {
//                builder.setNum(requesttansks.getNum());
//                builder.setType(requesttansks.getType());
//                responseBuilder.addDailytansks(i, builder);
//            } else {
//                builder.setNum(0);
//                builder.setType((i + 1));
//                responseBuilder.addDailytansks(i, builder);
//            }
//        }
//        tasksEntity.setConductTask(responseBuilder.build().toByteArray());
//        MySql.insert(tasksEntity);
//        return responseBuilder.build().toByteArray();
//    }

    //完成任务
//    public byte[] requestcompletedailytasks(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
//        synchronized (uid) {
//            TaskData.responsecompletedailytasks.Builder responseBuilder = new TaskData.responsecompletedailytasks.Builder();
//            TaskData.Completedailytasks.Builder builder = new TaskData.Completedailytasks.Builder();
//            TaskData.requestcompletedailytasks requesttansks = TaskData.requestcompletedailytasks.parseFrom(inBytes);
//            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//            ItemData.Item.Builder item=ItemData.Item.newBuilder();
//            StringBuilder sql = new StringBuilder("from CompleteTaskEntity where uid='").append(uid).append("'");
//            CompleteTaskEntity completeTaskEntity = (CompleteTaskEntity) MySql.queryForOne(sql.toString());
//            StringBuilder sqlCount = new StringBuilder("select count(0) from CompleteTaskEntity where uid='").append(uid).append("'");
//            long completeTaskEntityCount = (long) MySql.queryForOne(sqlCount.toString());
//            if (completeTaskEntityCount <= 0) {
//                //通过下标存入
//                for (int i = 0; i < 7; i++) {
//                    if (requesttansks.getId() == (i + 1)) {
//                        Redis jedis = Redis.getInstance();
//                        Iterator<String> iterator = jedis.keys("dailyconfig:*").iterator();
//                        while (iterator.hasNext()) {
//                            String key = iterator.next();
//                            Map<String, String> mailMap = jedis.hgetAll(key);
//                            if (requesttansks.getId() == Integer.parseInt(mailMap.get("id"))) {
//                                String str = mailMap.get("item");
//                                ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, Integer.parseInt(str.substring(0, str.indexOf(","))));
//                                itemEntity.setItemnum(Integer.parseInt(str.substring(str.indexOf(",") + 1, str.length())) + itemEntity.getItemnum());
//                                MySql.update(itemEntity);
//
//                                reportBuilder.addItem(ItemUtils.getItemData(itemEntity.getId(), itemEntity.getItemnum()));
//                                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//                            }
//                        }
//                        builder.setId(requesttansks.getId());
//                        builder.setIsComplete(requesttansks.getIsComplete());
//                        responseBuilder.addCompletedailytasks(i, builder);
//                    } else {
//                        builder.setId((i + 1));
//                        builder.setIsComplete(false);
//                        responseBuilder.addCompletedailytasks(i, builder);
//                    }
//                }
//
//                completeTaskEntity = new CompleteTaskEntity();
//                Date date = new Date();
//                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//                completeTaskEntity.setUid(uid);
//                completeTaskEntity.setTime(simpleDateFormat.format(date).trim());
//                completeTaskEntity.setCompleteTask(responseBuilder.build().toByteArray());
//                MySql.mustInsert(completeTaskEntity);
//
//            } else {
//                TaskData.responsecompletedailytasks responsecompletedailytasks = TaskData.responsecompletedailytasks.parseFrom(completeTaskEntity.getCompleteTask());
//                //通过下标存入
//                for (int i = 0; i < responsecompletedailytasks.getCompletedailytasksList().size(); i++) {
//                    if (requesttansks.getId() == (i + 1)) {
//                        Redis jedis = Redis.getInstance();
//                        Iterator<String> iterator = jedis.keys("dailyconfig:*").iterator();
//                        while (iterator.hasNext()) {
//                            String key = iterator.next();
//                            Map<String, String> mailMap = jedis.hgetAll(key);
//                            if (requesttansks.getId() == Integer.parseInt(mailMap.get("id"))) {
//                                String str = mailMap.get("item");
//                                ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, Integer.parseInt(str.substring(0, str.indexOf(","))));
////                            if (itemEntity == null) {
////                                itemEntity = new ItemEntity();
////                                itemEntity.setUid(uid);
//////                                    itemEntity.setType(itemEntity.getType());
////                                itemEntity.setItemnum(Integer.parseInt(str.substring(str.indexOf(",") + 1, str.length())) + itemEntity.getItemnum());
////                                MySql.insert(itemEntity);
////                                ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
////                                reportBuilder.addItem(ItemUtils.getItemData(22, itemEntity.getItemnum()));
////                            }
//                                itemEntity.setItemnum(Integer.parseInt(str.substring(str.indexOf(",") + 1, str.length())) + itemEntity.getItemnum());
//                                MySql.update(itemEntity);
//                                item.setNum(itemEntity.getItemnum());
//                                item.setId(itemEntity.getItemid());
//                                reportBuilder.addItem(item);
//                                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//                            }
//                        }
//                        builder.setId(requesttansks.getId());
//                        builder.setIsComplete(requesttansks.getIsComplete());
//                        responseBuilder.addCompletedailytasks(i, builder);
//                    } else {
//                        builder.setId(responsecompletedailytasks.getCompletedailytasksList().get(i).getId());
//                        builder.setIsComplete(responsecompletedailytasks.getCompletedailytasksList().get(i).getIsComplete());
//                        responseBuilder.addCompletedailytasks(i, builder);
//                    }
//                }
//                completeTaskEntity.setCompleteTask(responseBuilder.build().toByteArray());
//                MySql.update(completeTaskEntity);
//            }
//            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//            return responseBuilder.build().toByteArray();
//        }
//    }
//
//    public byte[] requestTask(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
//        TaskData.responseTask.Builder builder = new TaskData.responseTask.Builder();
//        //进行任务
//        TaskData.responsedailytansks.Builder responsedailytansksBuilder = new TaskData.responsedailytansks.Builder();
//        TaskData.Dailytansks.Builder dailytansksBuilder = new TaskData.Dailytansks.Builder();
//        //完成任务
//        TaskData.responsecompletedailytasks.Builder responsecompletedailytasksBuilder = new TaskData.responsecompletedailytasks.Builder();
//        TaskData.Completedailytasks.Builder completedailytasksBuilder = new TaskData.Completedailytasks.Builder();
//        //获取数据库中的数据
//        StringBuilder sqlCon = new StringBuilder("select count(0) from ConductTaskEntity where uid='").append(uid).append("'");
//        long conductTaskEntityCount = (long) MySql.queryForOne(sqlCon.toString());
//        StringBuilder sqlCom = new StringBuilder("select count(0) from CompleteTaskEntity where uid='").append(uid).append("'");
//        long completeTaskEntityCount = (long) MySql.queryForOne(sqlCom.toString());
//        if (conductTaskEntityCount <= 0) {
////            /// System.out.println("1====conductTaskEntityCount <= 0====true");
//            for (int i = 0; i < 4; i++) {
//                dailytansksBuilder.setNum(0);
//                dailytansksBuilder.setType(i + 1);
//                responsedailytansksBuilder.addDailytansks(i, dailytansksBuilder);
//                builder.addDailytansks(responsedailytansksBuilder.getDailytansksList().get(i));
//            }
//        }
//
//        if (completeTaskEntityCount <= 0) {
////            /// System.out.println("2===completeTaskEntityCount <= 0====true");
//            for (int i = 0; i < 7; i++) {
//                completedailytasksBuilder.setId(i + 1);
//                completedailytasksBuilder.setIsComplete(false);
//                responsecompletedailytasksBuilder.addCompletedailytasks(completedailytasksBuilder);
//                builder.addCompletedailytasks(responsecompletedailytasksBuilder.getCompletedailytasksList().get(i));
//            }
//        }
//        StringBuilder sql = new StringBuilder("from ConductTaskEntity where uid='").append(uid).append("'");
//        ConductTaskEntity conductTaskEntity = (ConductTaskEntity) MySql.queryForOne(sql.toString());
//        StringBuilder sqlS = new StringBuilder("from CompleteTaskEntity where uid='").append(uid).append("'");
//        CompleteTaskEntity completeTaskEntity = (CompleteTaskEntity) MySql.queryForOne(sqlS.toString());
//        Date date = new Date();
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        try {
//            if (!simpleDateFormat.format(date).trim().equals(conductTaskEntity.getTime()) && !simpleDateFormat.format(date).trim().equals(completeTaskEntity.getTime())) {
//                if (conductTaskEntity.getConductTask() != null) {
//                    TaskData.responsedailytansks responsedailytansksParseFrom = TaskData.responsedailytansks.parseFrom(conductTaskEntity.getConductTask());
//                    for (int i = 0; i < responsedailytansksParseFrom.getDailytansksList().size(); i++) {
//                        dailytansksBuilder.setNum(0);
//                        dailytansksBuilder.setType(responsedailytansksParseFrom.getDailytansksList().get(i).getType());
//                        responsedailytansksBuilder.addDailytansks(i, dailytansksBuilder);
//                    }
//                } else {
//                    for (int i = 0; i < 4; i++) {
//                        dailytansksBuilder.setNum(0);
//                        dailytansksBuilder.setType(i + 1);
//                        responsedailytansksBuilder.addDailytansks(i, dailytansksBuilder);
//                        builder.addDailytansks(responsedailytansksBuilder.getDailytansksList().get(i));
//                    }
//                }
//                if (completeTaskEntity.getCompleteTask() != null) {
//                    TaskData.responsecompletedailytasks responsecompletedailytasks = TaskData.responsecompletedailytasks.parseFrom(completeTaskEntity.getCompleteTask());
//                    for (int i = 0; i < responsecompletedailytasks.getCompletedailytasksList().size(); i++) {
//                        completedailytasksBuilder.setId(responsecompletedailytasks.getCompletedailytasksList().get(i).getId());
//                        completedailytasksBuilder.setIsComplete(false);
//                        responsecompletedailytasksBuilder.addCompletedailytasks(completedailytasksBuilder);
//                    }
//                } else {
//                    for (int i = 0; i < 7; i++) {
//                        completedailytasksBuilder.setId(i + 1);
//                        completedailytasksBuilder.setIsComplete(false);
//                        responsecompletedailytasksBuilder.addCompletedailytasks(completedailytasksBuilder);
//                        builder.addCompletedailytasks(responsecompletedailytasksBuilder.getCompletedailytasksList().get(i));
//                    }
//                }
//                ConductTaskEntity conductTaskEntity1 = new ConductTaskEntity();
//                conductTaskEntity1.setId(conductTaskEntity.getId());
//                conductTaskEntity1.setUid(conductTaskEntity.getUid());
//                conductTaskEntity1.setTime(simpleDateFormat.format(date).trim());
//                conductTaskEntity1.setConductTask(responsedailytansksBuilder.build().toByteArray());
//                CompleteTaskEntity completeTaskEntity1 = new CompleteTaskEntity(completeTaskEntity.getId(), completeTaskEntity.getUid(), simpleDateFormat.format(date).trim(), responsecompletedailytasksBuilder.build().toByteArray());
//                MySql.update(conductTaskEntity1);
//                MySql.update(completeTaskEntity1);
//                return responsecompletedailytasksBuilder.build().toByteArray();
//            }
//        } catch (NullPointerException e) {
//            ///     /// System.err.println("空指针" + e.getMessage());
//        }
//        if (conductTaskEntityCount > 0) {
//            TaskData.responsedailytansks responsedailytansks = TaskData.responsedailytansks.parseFrom(conductTaskEntity.getConductTask());
//            for (int i = 0; i < responsedailytansks.getDailytansksList().size(); i++) {
//                builder.addDailytansks(responsedailytansks.getDailytansksList().get(i));
//            }
//        }
//        if (completeTaskEntityCount > 0) {
//            TaskData.responsecompletedailytasks responsecompletedailytasks = TaskData.responsecompletedailytasks.parseFrom(completeTaskEntity.getCompleteTask());
//            for (int i = 0; i < responsecompletedailytasks.getCompletedailytasksList().size(); i++) {
//                builder.addCompletedailytasks(responsecompletedailytasks.getCompletedailytasksList().get(i));
//            }
//        }
//        return builder.build().toByteArray();
//    }
//
//    public byte[] RequestAchievementType(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
//        TaskData.ResponseAchievementType.Builder responseBuilder = new TaskData.ResponseAchievementType.Builder();
//        TaskData.Dailytansks.Builder builder = new TaskData.Dailytansks.Builder();
//        TaskData.RequestAchievementType requesttansks = TaskData.RequestAchievementType.parseFrom(inBytes);
//        //判断数据库是否有uid
//        StringBuilder sql = new StringBuilder("from AchievementEntity where uid='").append(uid).append("'");
//        AchievementEntity tasksEntity = (AchievementEntity) MySql.queryForOne(sql.toString());
//        if (tasksEntity != null) {
//            if (tasksEntity.getUid().equals(uid)) {
//                //判断字段是否为空
//                if (tasksEntity.getAchievement_type() != null) {
//                    //判断数据库中是否已有此类型
//                    TaskData.ResponseAchievementType responsedailytansks = TaskData.ResponseAchievementType.parseFrom(tasksEntity.getAchievement_type());
//                    for (int i = 0; i < responsedailytansks.getDailytansksList().size(); i++) {
//                        if (requesttansks.getType() == responsedailytansks.getDailytansksList().get(i).getType()) {
//                            builder.setType(requesttansks.getType());
//                            builder.setNum((responsedailytansks.getDailytansksList().get(i).getNum() + requesttansks.getNum()));
//                            responseBuilder.addDailytansks(i, builder);
//                        } else {
//                            builder.setNum(responsedailytansks.getDailytansksList().get(i).getNum());
//                            builder.setType(responsedailytansks.getDailytansksList().get(i).getType());
//                            responseBuilder.addDailytansks(i, builder);
//                        }
//                    }
//                    tasksEntity.setAchievement_type(responseBuilder.build().toByteArray());
//                    MySql.update(tasksEntity);
//                    return responseBuilder.build().toByteArray();
//                } else {
//                    for (int i = 0; i < 7; i++) {
//                        Redis jedis = Redis.getInstance();
//                        Iterator<String> iterator = jedis.keys("achivementconfig:*").iterator();
//                        if (requesttansks.getType() == (i + 1)) {
//                            builder.setNum(requesttansks.getNum());
//                            builder.setType(requesttansks.getType());
//                            responseBuilder.addDailytansks(builder);
//                        }
//                        while (iterator.hasNext()) {
//                            String key = iterator.next();
//                            Map<String, String> mailMap = jedis.hgetAll(key);
//                            if (Integer.parseInt(mailMap.get("type")) == (i + 1) && Integer.parseInt(mailMap.get("type")) != requesttansks.getType()) {
//                                builder.setNum(0);
//                                builder.setType(Integer.parseInt(mailMap.get("type")));
//                                responseBuilder.addDailytansks(builder);
//                                break;
//                            }
//                        }
//                    }
//                    tasksEntity.setAchievement_type(responseBuilder.build().toByteArray());
//                    MySql.update(tasksEntity);
//                    return responseBuilder.build().toByteArray();
//                }
//            }
//        }
//        for (int i = 0; i < 7; i++) {
//            tasksEntity = new AchievementEntity();
//            tasksEntity.setId(0);
//            tasksEntity.setUid(uid);
//            Redis jedis = Redis.getInstance();
//            Iterator<String> iterator = jedis.keys("achivementconfig:*").iterator();
//            if (requesttansks.getType() == (i + 1)) {
//                builder.setNum(requesttansks.getNum());
//                builder.setType(requesttansks.getType());
//                responseBuilder.addDailytansks(builder);
//            }
//            while (iterator.hasNext()) {
//                String key = iterator.next();
//                Map<String, String> mailMap = jedis.hgetAll(key);
//                if (Integer.parseInt(mailMap.get("type")) == (i + 1) && Integer.parseInt(mailMap.get("type")) != requesttansks.getType()) {
//                    builder.setNum(0);
//                    builder.setType(Integer.parseInt(mailMap.get("type")));
//                    responseBuilder.addDailytansks(builder);
//                    break;
//                }
//            }
//        }
//        tasksEntity.setAchievement_type(responseBuilder.build().toByteArray());
//        MySql.insert(tasksEntity);
//        return responseBuilder.build().toByteArray();
//    }
//
//    public byte[] RequestAchievementIs(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
//        TaskData.ResponseAchievementIs.Builder responseBuilder = new TaskData.ResponseAchievementIs.Builder();
//        TaskData.Completedailytasks.Builder builder = new TaskData.Completedailytasks.Builder();
//        TaskData.RequestAchievementIs requesttansks = TaskData.RequestAchievementIs.parseFrom(inBytes);
//        StringBuilder sql = new StringBuilder("from AchievementEntity where uid='").append(uid).append("'");
//        AchievementEntity tasksEntity = (AchievementEntity) MySql.queryForOne(sql.toString());
//        if (tasksEntity.getAchievement_is() != null) {
//            TaskData.ResponseAchievementIs responsecompletedailytasks = TaskData.ResponseAchievementIs.parseFrom(tasksEntity.getAchievement_is());
//            //通过下标存入
//            for (int i = 0; i < responsecompletedailytasks.getCompletedailytasksList().size(); i++) {
//                if (requesttansks.getId() == (i + 1)) {
//                    Redis jedis = Redis.getInstance();
//                    Iterator<String> iterator = jedis.keys("achivementconfig:*").iterator();
//                    while (iterator.hasNext()) {
//                        String key = iterator.next();
//                        Map<String, String> mailMap = jedis.hgetAll(key);
//                        if (requesttansks.getId() == Integer.parseInt(mailMap.get("id"))) {
//                            String str = mailMap.get("item");
//                            ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, Integer.parseInt(str.substring(0, str.indexOf(","))));
//                            itemEntity.setItemnum(Integer.parseInt(str.substring(str.indexOf(",") + 1, str.length())) + itemEntity.getItemnum());
//                            MySql.update(itemEntity);
//                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//                            reportBuilder.addItem(ItemUtils.getItemData(1, itemEntity.getItemnum()));
//                            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//                        }
//                    }
//
//                    builder.setId(requesttansks.getId());
//                    builder.setIsComplete(requesttansks.getIsComplete());
//                    responseBuilder.addCompletedailytasks(i, builder);
//                } else {
//                    builder.setId(responsecompletedailytasks.getCompletedailytasksList().get(i).getId());
//                    builder.setIsComplete(responsecompletedailytasks.getCompletedailytasksList().get(i).getIsComplete());
//                    responseBuilder.addCompletedailytasks(i, builder);
//                }
//            }
//            tasksEntity.setAchievement_is(responseBuilder.build().toByteArray());
//            MySql.update(tasksEntity);
//            return responseBuilder.build().toByteArray();
//        } else {
//            //通过下标存入
//            for (int i = 0; i < 8; i++) {
//                if (requesttansks.getId() == (i + 1)) {
//                    Redis jedis = Redis.getInstance();
//                    Iterator<String> iterator = jedis.keys("achivementconfig:*").iterator();
//                    while (iterator.hasNext()) {
//                        String key = iterator.next();
//                        Map<String, String> mailMap = jedis.hgetAll(key);
//                        if (requesttansks.getId() == Integer.parseInt(mailMap.get("id"))) {
//                            String str = mailMap.get("item");
//                            ItemEntity itemEntity = ItemDao.getInstance().queryItemGildCoins(uid, Integer.parseInt(str.substring(0, str.indexOf(","))));
//                            itemEntity.setItemnum(Integer.parseInt(str.substring(str.indexOf(",") + 1, str.length())) + itemEntity.getItemnum());
//                            MySql.update(itemEntity);
//                            ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
//                            reportBuilder.addItem(ItemUtils.getItemData(1, itemEntity.getItemnum()));
//                            ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
//                        }
//                    }
//                    builder.setId(requesttansks.getId());
//                    builder.setIsComplete(requesttansks.getIsComplete());
//                    responseBuilder.addCompletedailytasks(i, builder);
//                } else {
//                    builder.setId((i + 1));
//                    builder.setIsComplete(false);
//                    responseBuilder.addCompletedailytasks(i, builder);
//                }
//            }
//            tasksEntity.setAchievement_is(responseBuilder.build().toByteArray());
//            MySql.update(tasksEntity);
//        }
//        return responseBuilder.build().toByteArray();
//    }
//
//    public byte[] RequestAchievement(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
//        TaskData.ResponseAchievement.Builder builders = new TaskData.ResponseAchievement.Builder();
//        //进行任务
//        TaskData.Dailytansks.Builder dailytansksBuilder = new TaskData.Dailytansks.Builder();
//        //完成任务
//        TaskData.ResponseAchievementIs.Builder responsecompletedailytasksBuilder = new TaskData.ResponseAchievementIs.Builder();
//        TaskData.Completedailytasks.Builder completedailytasksBuilder = new TaskData.Completedailytasks.Builder();
//        //获取数据库中的数据
//        StringBuilder sql;
////        ConductTaskEntity conductTaskEntity = (ConductTaskEntity) MySql.queryForOne(sql.toString());
//        sql = new StringBuilder("from AchievementEntity where uid='").append(uid).append("'");
//        AchievementEntity achievementEntity = (AchievementEntity) MySql.queryForOne(sql.toString());
//        if (achievementEntity == null) {
//            for (int i = 0; i < 8; i++) {
//                completedailytasksBuilder.setId(i + 1);
//                completedailytasksBuilder.setIsComplete(false);
//                responsecompletedailytasksBuilder.addCompletedailytasks(completedailytasksBuilder);
//                builders.addCompletedailytasks(responsecompletedailytasksBuilder.getCompletedailytasksList().get(i));
//            }
//            for (int i = 0; i < 7; i++) {
//                Redis jedis = Redis.getInstance();
//                Iterator<String> iterator = jedis.keys("achivementconfig:*").iterator();
//                while (iterator.hasNext()) {
//                    String key = iterator.next();
//                    Map<String, String> mailMap = jedis.hgetAll(key);
//                    if (Integer.parseInt(mailMap.get("type")) == (i + 1)) {
//                        dailytansksBuilder.setNum(0);
//                        dailytansksBuilder.setType(Integer.parseInt(mailMap.get("type")));
//                        builders.addDailytansks(dailytansksBuilder);
//                        break;
//                    }
//                }
//            }
//            return builders.build().toByteArray();
//        }
//        if (achievementEntity.getAchievement_type() != null) {
//            TaskData.ResponseAchievementType responsedailytansks = TaskData.ResponseAchievementType.parseFrom(achievementEntity.getAchievement_type());
//            for (int i = 0; i < responsedailytansks.getDailytansksList().size(); i++) {
//                builders.addDailytansks(responsedailytansks.getDailytansksList().get(i));
//            }
//        } else {
//            for (int i = 0; i < 7; i++) {
//                Redis jedis = Redis.getInstance();
//                Iterator<String> iterator = jedis.keys("achivementconfig:*").iterator();
//                while (iterator.hasNext()) {
//                    String key = iterator.next();
//                    Map<String, String> mailMap = jedis.hgetAll(key);
//                    if (Integer.parseInt(mailMap.get("type")) == (i + 1)) {
//                        dailytansksBuilder.setNum(0);
//                        dailytansksBuilder.setType(Integer.parseInt(mailMap.get("type")));
//                        builders.addDailytansks(dailytansksBuilder);
//                        break;
//                    }
//                }
//            }
//        }
//        if (achievementEntity.getAchievement_is() != null) {
//            TaskData.ResponseAchievementIs responsecompletedailytasks = TaskData.ResponseAchievementIs.parseFrom(achievementEntity.getAchievement_is());
//            for (int i = 0; i < responsecompletedailytasks.getCompletedailytasksList().size(); i++) {
//                builders.addCompletedailytasks(responsecompletedailytasks.getCompletedailytasksList().get(i));
//            }
//        } else {
//            for (int i = 0; i < 8; i++) {
//                completedailytasksBuilder.setId(i + 1);
//                completedailytasksBuilder.setIsComplete(false);
//                responsecompletedailytasksBuilder.addCompletedailytasks(completedailytasksBuilder);
//                builders.addCompletedailytasks(responsecompletedailytasksBuilder.getCompletedailytasksList().get(i));
//            }
//        }
//        return builders.build().toByteArray();
//    }
}
