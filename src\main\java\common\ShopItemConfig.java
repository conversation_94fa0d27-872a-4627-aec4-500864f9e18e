package common;

@ExcelConfigObject(key = "shopItem")
public class ShopItemConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "type")
    private int type;
    @ExcelColumn(name = "item")
    private int linkItemId;
    @ExcelColumn(name = "item_num")
    private int getNums;
    @ExcelColumn(name = "coin")
    private int costItemId;
    @ExcelColumn(name = "coin_num")
    private int cotsNums;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getLinkItemId() {
        return linkItemId;
    }

    public void setLinkItemId(int linkItemId) {
        this.linkItemId = linkItemId;
    }

    public int getGetNums() {
        return getNums;
    }

    public void setGetNums(int getNums) {
        this.getNums = getNums;
    }

    public int getCostItemId() {
        return costItemId;
    }

    public void setCostItemId(int costItemId) {
        this.costItemId = costItemId;
    }

    public int getCotsNums() {
        return cotsNums;
    }

    public void setCotsNums(int cotsNums) {
        this.cotsNums = cotsNums;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }
}
