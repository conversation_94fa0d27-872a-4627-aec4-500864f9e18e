package module.callback;

/**
 * Created by nara on 2018/5/8.
 */
public class CallBackOrder {
    public final static int FINDFRIENDBACK = 1;
    public final static int ADDFRIENDBACK = 2;
    public final static int SENDCHATTOSTRANGER = 3;
    public final static int PUBLISHMAILBACK = 4;
    public final static int SETPLAYERTYPEBACK = 5;
    public final static int AGREEWITHFRIENFBACK = 6;
    public final static int JUDGEFRIENGVALUEBACK = 7;
    public final static int JUDGENAMEFORCHANGEBACK = 8;
    public final static int RANKFORLVBACK = 9;
    public final static int RANKFORMISSIONBACK = 10;
    public final static int RANKFORHEADBALLMISSIONBACK = 17;
    public final static int RANKFORENDLESSBACK = 11;
    public final static int RANKFORDAILYPKNUMBACK = 16;
    public final static int RANKFORDreesValueBACK = 18;
    public final static int RANKFORPETNUMBER = 19;
    public final static int RANKFORSCORE = 20;


    public final static int FRIENDRECOMMEND = 12;
    public final static int CLOTHINGRECOMMEND = 13;
    public final static int RELATIVESHIPVALUEBACK = 14;
    public final static int RELATIVESHIPGETNAMEBACK = 15;


    public final static int ENTERMISSION = 100;
    public final static int OVERMISSION = 101;

    public final static int PAYFINDBACK = 150;
    public final static int GOOGLEPAYFINDBACK = 151;
    public final static int HUAWEIPAY = 152;
}
