package table.new_shop_item;

import table.TableManager;

public class new_shop_itemTable extends TableManager<new_shop_itemLine> {
    private static new_shop_itemTable instance;
    public static new_shop_itemTable getInstance(){
        if (instance != null){
            return instance;
        }
        instance = new new_shop_itemTable();
        return instance;
    }
    private new_shop_itemTable(){

    }


    @Override
    public String TableName() {
        return "newShopItem";
    }

    @Override
    public String LinePath() {
        return "table.new_shop_item.new_shop_itemLine";
    }
}
