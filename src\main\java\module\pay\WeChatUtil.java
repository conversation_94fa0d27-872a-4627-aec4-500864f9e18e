package module.pay;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import net.sf.json.JSONObject;
import com.thoughtworks.xstream.XStream;

import manager.Redis;
import manager.TimerHandler;
import utils.MyUtils;
import utils.XMLUtil;

public class WeChatUtil {
    private static class TrustAnyTrustManager implements X509TrustManager {

        public void checkClientTrusted(final X509Certificate[] chain, final String authType)
                throws CertificateException {
        }

        public void checkServerTrusted(final X509Certificate[] chain, final String authType)
                throws CertificateException {
        }

        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[] {};
        }
    }

    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        public boolean verify(final String hostname, final SSLSession session) {
            return true;
        }
    }

    public static String callbackUrl = "*************:15000/WeChatCallback";
    public static String MCH_ID = "1482441592";
    public static String MCH_KEY = "Qiujaja2TangWangLiSu0302Youjgame";
    public static String AppID = "wxf43ac15bc6a2322d";

    public static Map<String, String> unifiedOrder(final String uid, final String orderId, final String ProductID,
            final int money, String body, final String spBillIp, final StringBuffer key) {
        final UnifiedOrder unifiedOrder = new UnifiedOrder();
        unifiedOrder.setAppid(AppID);
        unifiedOrder.setAttach(key.toString());

        // unifiedOrder.setProduct_id(ProductID);

        try {
            body = new String(body.getBytes("ISO-8859-1"), "UTF-8");
        } catch (final Exception e) {
            return null;
        }

        unifiedOrder.setBody(body);
        unifiedOrder.setMch_id(MCH_ID);

        final String nonce = MyUtils.setRandom();
        unifiedOrder.setNonce_str(nonce);
        unifiedOrder.setNotify_url(callbackUrl);

        unifiedOrder.setOut_trade_no(orderId);

        unifiedOrder.setSpbill_create_ip(spBillIp);
        unifiedOrder.setTotal_fee(money);
        unifiedOrder.setTrade_type("APP");

        final String sign = createUnifiedOrderSign(unifiedOrder);
        if (sign == null) {
            return null;
        }
        unifiedOrder.setSign(sign);
        /**
         * 转成XML格式
         */
        final XStream stream = new XStream();
        stream.alias("xml", unifiedOrder.getClass());
        final String xml = stream.toXML(unifiedOrder).replace("__", "_");

        /// System.out.println("===========xml: " + xml);

        Map<String, String> result = null;

        final String resContent = doHttpPost("https://api.mch.weixin.qq.com/pay/unifiedorder", xml);
        if (resContent == null)
            return null;
        try {
            final byte[] utf8Bytes = resContent.getBytes();

            final String utf8Str = new String(utf8Bytes, "UTF-8");
            /// System.out.println("weixinPayid: " + utf8Str);

        } catch (final Exception e) {
            return null;
        }

        if (resContent.indexOf("prepay_id") != -1) {
            try {
                final Map<String, String> responseMap = XMLUtil.doXMLParse(resContent);

                result = new HashMap<String, String>();
                result.put("prepayid", responseMap.get("prepay_id"));
            } catch (final Exception e) {
                /// System.out.println("get prepayid error!");

            }
        }
        // this.setDebugInfo(this.getDebugInfo() + "\r\n" + "resContent:"
        // + resContent);

        return result;
    }

    public static String createUnifiedOrderSign(final UnifiedOrder unifiedOrder) {
        final StringBuffer sign = new StringBuffer();
        sign.append("appid=").append(unifiedOrder.getAppid());
        sign.append("&attach=").append(unifiedOrder.getAttach());
        sign.append("&body=").append(unifiedOrder.getBody());
        // sign.append("&device_info=").append(unifiedOrder.getDevice_info());
        sign.append("&mch_id=").append(unifiedOrder.getMch_id());
        sign.append("&nonce_str=").append(unifiedOrder.getNonce_str());
        sign.append("&notify_url=").append(unifiedOrder.getNotify_url());
        sign.append("&out_trade_no=").append(unifiedOrder.getOut_trade_no());
        // sign.append("&product_id=").append(unifiedOrder.getProduct_id());
        sign.append("&spbill_create_ip=").append(unifiedOrder.getSpbill_create_ip());
        sign.append("&total_fee=").append(unifiedOrder.getTotal_fee());
        sign.append("&trade_type=").append(unifiedOrder.getTrade_type());
        sign.append("&key=").append(MCH_KEY);
        // /// System.out.println("sign=========" + sign.toString());
        return MyUtils.stringToMD5(sign.toString()).toUpperCase();
    }

    public static String createClientSign(final String noncestr, final String prepayid, final String timeitamp) {

        final StringBuffer sign = new StringBuffer();
        sign.append("appid=").append(AppID);
        sign.append("&noncestr=").append(noncestr);
        sign.append("&package=").append("Sign=WXPay");
        sign.append("&partnerid=").append(MCH_ID);
        sign.append("&prepayid=").append(prepayid);
        sign.append("&timestamp=").append(timeitamp);
        sign.append("&key=").append(MCH_KEY);
        /// System.out.println("sign=========" + sign.toString());
        return MyUtils.stringToMD5(sign.toString()).toUpperCase();
    }

    public static String doHttpPost(final String url, final String content) 
    {
        try {
            final SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[] { new TrustAnyTrustManager() }, new java.security.SecureRandom());
            final URL console = new URL(url);
            final HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            conn.setRequestMethod("POST");
            conn.setRequestProperty("content-type", "text/json");
            conn.setRequestProperty("Proxy-Connection", "Keep-Alive");
            conn.setDoInput(true);
            conn.setDoOutput(true);
            final BufferedOutputStream hurlBufOus = new BufferedOutputStream(conn.getOutputStream());

            hurlBufOus.write(content.getBytes());
            hurlBufOus.flush();

            final InputStream is = conn.getInputStream();
            final BufferedReader reader = new BufferedReader(new InputStreamReader(is));
            String line = null;
            final StringBuffer sb = new StringBuffer();
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            reader.close();
            return sb.toString();
        } catch (final Exception ex) {
            /// System.out.println("服务器异常");
            ex.printStackTrace();
        }
        return null;
    }

    public static JSONObject doHttpsGet(String url){
		String response = "";
		HttpsURLConnection conn = null;
		try {
			SSLContext sc = SSLContext.getInstance("SSL");
			sc.init(null, new TrustManager[] { new TrustAnyTrustManager() },
					new java.security.SecureRandom());
			URL console = new URL(url);
			conn = (HttpsURLConnection) console.openConnection();
			conn.setSSLSocketFactory(sc.getSocketFactory());
			conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
			conn.connect();
			InputStream is = conn.getInputStream();

			BufferedReader reader = new BufferedReader(new InputStreamReader(is));
			String ret = "";
            while (ret != null) 
            {
				ret = reader.readLine();
                if (ret != null && !ret.trim().equals("")) 
                {
					response = response + new String(ret.getBytes("ISO-8859-1"), "utf-8");
				}
            }
            reader.close();

		} catch (Exception e) {
            e.printStackTrace();
            return null;
		} finally {
			conn.disconnect();
        }
        
        /// System.out.println("https get: "+response);

        JSONObject responseJson = JSONObject.fromObject(response);

		return responseJson;
	}    
    
   
}