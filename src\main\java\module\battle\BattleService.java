package module.battle;

import com.googlecode.protobuf.format.JsonFormat;
import common.MapConfig;
import common.SuperConfig;
import entities.PetEntity;
import manager.MySql;
import manager.ReportManager;
import model.CommonInfo;
import model.ItemInfo;
import module.item.ItemDao;
import module.item.ItemUtils;
import module.login.LoginDao;
import module.pet.PetDao;
import module.pet.PetUtils;
import protocol.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class BattleService {
    private static BattleService inst = null;
    private static BattleUtils battleUtils = null;

    public static BattleService getInstance() {
        if (inst == null) {
            inst = new BattleService();
            battleUtils = BattleUtils.getInstance();
        }
        return inst;
    }

    public byte[] countBattle(byte[] bytes, String uid) {
        BattleData.RequestCountBattle requestCountBattle = null;
        BattleData.ResponseCountBattle.Builder builder = BattleData.ResponseCountBattle.newBuilder();
        try {
            requestCountBattle = BattleData.RequestCountBattle.parseFrom(bytes);
            boolean isSuccess = requestCountBattle.getSuccess();
            builder.setErrorId(0);
            builder.setSuccess(isSuccess);
            //成功才有獎勵
            if (isSuccess) {
                MapConfig mapConfig = (MapConfig) SuperConfig.getCongifObject(SuperConfig.mapConfig, requestCountBattle.getMapId());
                //物品奖励s
                List<ItemInfo> itemsInfoList = new ArrayList<ItemInfo>();
                List<Integer> monsterIdList = requestCountBattle.getMonsterIdList();
                for (Integer monsterId : monsterIdList) {
                    List<ItemInfo> itemInfoList = battleUtils.getMonsterAward(monsterId);
                    itemsInfoList.addAll(itemInfoList);
                }
                Map<Integer, ItemInfo> itemsMap = ItemUtils.mergeItem(itemsInfoList);
                ItemData.ReportItem.Builder reportItemBuilder = ItemData.ReportItem.newBuilder();
                for (Map.Entry<Integer, ItemInfo> entry : itemsMap.entrySet()) {
                    ItemInfo itemInfo = entry.getValue();
                    builder.addItem(ItemUtils.getItemData(itemInfo.getId(), itemInfo.getNum()));
                    ItemDao itemDao = ItemDao.getInstance();
                    double value = itemDao.updateItemInfo(uid, itemInfo.getId(), (long) itemInfo.getNum());
                    reportItemBuilder.addItem(ItemUtils.getItemData(itemInfo.getId(), value));
                }
                ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, reportItemBuilder.build().toByteArray());
                ///人物奖励待定
                int roleEXpAward = mapConfig.getOfflineExp();
                try {
                    CommonInfo commonInfo = LoginDao.getInstance().roleAddExp(uid, roleEXpAward);
                    builder.setPalyerLv(commonInfo.getKey());
                    builder.setPlayerExp(commonInfo.getValue());
                } catch (NullPointerException e) {
                    /// System.err.println("空指针"+e.getMessage());
                }
                // 宠物奖励
                int petAddExp = mapConfig.getOfflineExp();
                builder.setAddExp(petAddExp);
                List<PetData.PetFormation> formationList = PetUtils.getPetFormation(uid);
                PetData.ResponseOperatePet.Builder updatePetbuild = PetData.ResponseOperatePet.newBuilder();
                updatePetbuild.setType(2);
                updatePetbuild.setErrorId(0);
                for (PetData.PetFormation pet : formationList) {
                    int petId = pet.getPetId();
                    PetEntity petEntity = PetDao.getInstance().queryPet(uid, petId);
                    petEntity = PetUtils.addPetExp(petEntity, petAddExp);
                    PetData.PlainPet.Builder petBu = PetData.PlainPet.newBuilder();
                    petBu.setPetId(petEntity.getPetUId());
                    petBu.setPetCurrentLV(petEntity.getCurrentLevel());
                    petBu.setPetExp(petEntity.getCurrentExp());
                    builder.addPet(petBu);
                    MySql.update(petEntity);
                    updatePetbuild.addPet(PetEntity.entityToPb(petEntity));
                }
                ReportManager.reportInfo(uid, ProtoData.SToC.RESPOSEOPERATEPET_VALUE, updatePetbuild.build().toByteArray());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ///   /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }
}
