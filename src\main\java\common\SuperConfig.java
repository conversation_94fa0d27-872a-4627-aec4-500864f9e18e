package common;

import manager.Redis;
import model.CommonInfo;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * Created by nara on 2017/11/22.
 */
public class SuperConfig {
    public static int GAMEMODE;

    public static int getGAMEMODE() {
        return GAMEMODE;
    }

    public static void setGAMEMODE(int GAMEMODE) {
        SuperConfig.GAMEMODE = GAMEMODE;
    }

    public static final String STARTSERVERSTAMP = "1525708800000";
    public static final double Need_AUTHENTICATION_TIME = 60 * 60 * 1000;
    public static final int GAMEMODE_MAIN = 1;
    public static final int GAMEMODE_PLOTTER = 2;
    public static final int GAMEMODE_MISSION = 3;
    public static final int GAMEMODE_MISSION_PLOTTER = 4;
    public static final int GAMEMODE_TEST = 5;

    public static final int DB_INSERT = 1;
    public static final int DB_UPDATE = 2;
    public static final int DB_UPDATESOMES = 3;
    public static final int DB_DELETE = 4;
    public static final int DB_QUERYFORONELCALLBACK = 5;
    public static final int DB_QUERYFORLISTLCALLBACK = 6;

    /**
     * 表
     */
    public static final String REDIS_EXCEL_STARPRICE = "starpriceconfig";
    public static final String REDIS_EXCEL_STARCLASS = "starclassconfig";
    public static final String REDIS_EXCEL_STARMISSION = "starmissionconfig";
    public static final String REDIS_EXCEL_LOTTO = "lottoconfig";
    public static final String REDIS_EXCEL_LOTTOCLASS = "lottoclassconfig";
    public static final String REDIS_EXCEL_FULLINGBALL = "fullingballconfig";
    public static final String REDIS_EXCEL_ENERGY = "energyconfig";
    public static final String REDIS_EXCEL_STACKBALL = "stackballconfig";
    public static final String REDIS_EXCEL_RANNAME = "randomnameconfig";
    public static final String REDIS_EXCEL_MISSION = "missionconfig";
    public static final String REDIS_EXCEL_DECISION = "decisionconfig";
    public static final String REDIS_EXCEL_ASSORT = "assortconfig";
    public static final String REDIS_EXCEL_DRESS = "dressconfig";
    public static final String REDIS_EXCEL_SHOPITEM = "shopitemconfig";
    public static final String REDIS_EXCEL_TASK = "taskconfig";
    public static final String REDIS_EXCEL_STORYTASK = "storytaskconfig";
    public static final String REDIS_EXCEL_ITEM = "itemconfig";
    public static final String REDIS_EXCEL_BAGUSE = "baguseconfig";
    public static final String REDIS_EXCEL_SIGN = "signconfig";
    public static final String REDIS_EXCEL_EXP = "expconfig";
    public static final String REDIS_EXCEL_COMPOSE = "composeconfig";
    public static final String REDIS_EXCEL_SKILL = "skillconfig";
    public static final String REDIS_EXCEL_MESSAGE = "messageconfig";
    public static final String REDIS_EXCEL_UTILITY = "utilityconfig";
    public static final String REDIS_EXCEL_INTERACTION = "interactionconfig";
    public static final String REDIS_EXCEL_ENDLESS = "endlessconfig";
    public static final String REDIS_EXCEL_ROLE = "playerroleconfig";
    public static final String REDIS_EXCEL_ENDLESSREWARD = "endlessrewardconfig";
    public static final String REDIS_EXCEL_PVPREWARD = "pvprewardconfig";
    public static final String REDIS_EXCEL_PARTNERREWARD = "partnerrewardconfig";
    public static final String REDIS_EXCEL_RANKING = "rankingconfig";
    public static final String REDIS_EXCEL_PAY = "payconfig";
    public static final String REDIS_EXCEL_GOOGLE_PAY = "googlepayconfig";
    public static final String REDIS_EXCEL_JUMPMISSION = "jumpmissionconfig";
    public static final String REDIS_EXCEL_GAMECOPY = "gamecopyConfig";
    public static final String REDIS_EXCEL_TIMING = "timingconfig";
    public static final String REDIS_EXCEL_LIMITED = "Limitedconfig";
    public static final String REDIS_EXCEL_ACTIVITIES = "activitiesconfig";
    public static final String REDIS_EXCEL_TICKRT = "ticketconfig";
    public static final String REDIS_EXCEL_CHANGETICKRT = "changeTicketconfig";
    public static final String REDIS_EXCEL_LUCKYTIME = "luckyTimeconfig";
    public static final String REDIS_EXCEL_WAITITEM = "waitItemconfig";
    public static final String REDIS_EXCEL_FURNITURE = "furnitureconfig";
    public static final int INITMISSIONNUM = 1;
    public static final int EXPIRETIMESECOND = 60 * 60 * 1;
    public static final int SIGNINALL = 7;
    public static final int DAILYTASKLAST = 10004;
    public static final int INITBAGNUM = 8;
    public static final int OFFLINEMESSAGE = 999;
    /*
     * type = 1001自定义邮件
     * */
    public static final int SPECIALMESSAGE = 1001;
    /**
     * 邮件过期时间
     */
    public static final int MAILEXPIRE = 7 * 24 * 60 * 60 * 1000;
    public static final int USEACTION_ACHIEVEMENT = 20034;

    public static final int RELATIVESHIP_FRIEND = 1;
    public static final int RANKSIZE = 50;

    public static final int FIGHTHALLMISSIONID = 51;

    public static final int STATIONTOTALROLE = 5;

    /**
     * baguse表类型字段
     */

    public static float getBallRadius() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "radius");
        return string == null ? null : Float.parseFloat(string);
    }

    public static int getSmallBallPosNum() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "outside");
        return string == null ? null : Integer.parseInt(string);
    }

    public static String[] getInitBallPosYRange() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "height");
        return string == null ? null : string.split("\\|");
    }

    public static int getBallFloatMax() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "float");
        return string == null ? null : Integer.parseInt(string.split("\\|")[1]);
    }

    public static String[] getSkillYRange() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "skill_h");
        return string == null ? null : string.split("\\|");
    }

    public static String[] getDebuffYRange() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "debuff_h");
        return string == null ? null : string.split("\\|");
    }

    public static String getSkillStartX() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "skill_start");
        return string == null ? null : string;
    }

    public static String getDownY() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "down_h");
        return string == null ? null : string;
    }

    public static int getMoneyCell3() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell3");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell4() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell4");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell5() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell5");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell6() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell6");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell7() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell7");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell8() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell8");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell9() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell9");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell10() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell10");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell11() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell11");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell12() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell12");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getMoneyCell13() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "cell13");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getActionLimit() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "physical_max");
        return string == null ? null : Integer.parseInt(string);
//        return 60;
    }

    public static int getMissionCostAction() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "reduce");
        return string == null ? null : Integer.parseInt(string);
//        return 5;
    }

    public static int getEndlessCostAction() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "reduce_endless");
        return string == null ? null : Integer.parseInt(string);
//        return 5;
    }

    public static int getRecoverActionSec() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "recovery_time");
        return string == null ? null : Integer.parseInt(string);
//        return 300;
    }

    public static int getDailyMoneyNum() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "change_num");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getDailyPresentScript() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "friend_word");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getDailyPresentItem() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "friend_num");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getFriendValueMax() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "friend_max");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getFriendValueMin() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "friend_min");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getLvApproval() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "thumbs_up_lv");
        return string == null ? null : Integer.parseInt(string);
    }

    //    public static int getLv(){
//        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1" , "thumbs_up_lv");
//        return string == null ? null:Integer.parseInt(string);
//    }
//    public static int getRankNumber(){
//        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1" , "thumbs_up_ranknumber");
//        return string == null ? null:Integer.parseInt(string);
//    }
    public static int getMissionApproval() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "thumbs_up_star");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getEndlessApproval() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "thumbs_up_endless");
        return string == null ? null : Integer.parseInt(string);
    }

    public static int getDressApproval() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "thumbs_up_dress");
        return string == null ? null : Integer.parseInt(string);
    }

    public static List<Integer> getMoneyChanges() {
        List<Integer> list = new ArrayList<Integer>();
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "change_rate");
        String[] strings = string.split("\\|");
        if (strings.length == 2) {
            for (int i = 0; i < strings.length; i++) {
                int val = Integer.parseInt(strings[i]);
                list.add(i, val);
            }
        }
        return list;
    }

    public static int getChangeName() {
        String string = Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "name");
        return string == null ? null : Integer.parseInt(string);
    }

    public static String getBagLimite(int baglevel) {
        return Redis.getExcelInfo(REDIS_EXCEL_DECISION, "1", "add_boxes" + baglevel);
    }

    //  public  static final  String  configKey=null;
   /* public Map<Integer,Map<String,String>> configMap =new HashMap<Integer,Map<String,String>>();
    public void batchConfigDataToMap(){
        Set<String> raffleConfigSet=Redis.keys(configKey+"*");
        Iterator<String> iterator=raffleConfigSet.iterator();
        Redis jedis=Redis.getInstance();
        while(iterator.hasNext()){
            String key=iterator.next();
            Map<String,String> dataMap=jedis.hgetAll(key);
            configMap.put(Integer.parseInt(key.split(":")[1]),dataMap);

        }

    }*/
    public static final String itemInfoSeparator = ","; //id "," num表示表格配置物品信息中同级并列的分隔符，前面表示物品id，后面是数量
    public static final String andSeparator = "\\|";  // 表示表格配置信息中同级并列的分隔符
    public static final String keyPrefix = "config";  // 表示表格配置信息中同级并列的分隔符
    public static Map<String, Map<Integer, Object>> configMap;

    //初始化配置map
    public static void initconfigMap() {
        configMap = new HashMap<String, Map<Integer, Object>>();
        Class<SuperConfig> zlass = SuperConfig.class;
        Field[] fields = zlass.getDeclaredFields();
        for (Field field : fields) {
            Annotation[] annotations = field.getDeclaredAnnotations();
            if (annotations.length == 0) {
                continue;
            } else {
                //获取标记了mapping注解的字段
                for (Annotation annotation : annotations) {
                    if (annotation instanceof Mapping) {
                        Map<Integer, Object> configInfos = new HashMap<Integer, Object>();
                        String className = ((Mapping) annotation).value();
                        try {
                            //mapping注解name值为映射的javaObject
                            Class configObject = Class.forName(className);
                            //获取类上在redis的key
                            ExcelConfigObject excelConfigObject = (ExcelConfigObject) (configObject.getDeclaredAnnotation(ExcelConfigObject.class));
                            String configKey = excelConfigObject.key();
                            Set<String> configSet = Redis.scan(configKey + SuperConfig.keyPrefix + "*");
                            for (String key : configSet) {
                                try {
                                    Object object = configMapToObject(key, configObject);
                                    Integer id = Integer.parseInt(key.split(":")[1]);
                                    configInfos.put(id, object);
                                } catch (Exception e) {
                                    System.out.println("????????");
                                    System.err.println("当前的键值为" + key);
                                }
                            }
                            //  /// System.out.println(configInfos);
                         /*   if(excelConfigObject.isUnique()){
                               for(Map.Entry<Integer,Object> entry:configInfos.entrySet()){
                                   Map<Integer,Object >  uniqueMap=new HashMap<Integer,Object>();
                                   uniqueMap.put(1,entry.getValue());
                                   /// System.err.println(entry+"~~~~~~~~~~~~~~~");
                                   configMap.put((String) field.get(null),uniqueMap);
                                   break;
                               }
                            }else{
                            }
                            }*/
                            configMap.put((String) field.get(null), configInfos);


                        } catch (Exception e) {
                            e.printStackTrace();
                            /// System.out.println(field.getName());
                        }
                    }

                }
            }
        }
        commonConfigObject = (CommonConfig) SuperConfig.configMap.get(SuperConfig.commonConfig).get(60);
    }

    @Mapping(value = "common.CommonConfig")
    public final static String commonConfig = "commonConfig";
    @Mapping(value = "common.PetCharaterConfig")
    public final static String petCharaterConfig = "petCharaterconfig";
    @Mapping(value = "common.ItemConfig")
    public final static String itemConfig = "itemConfig";
    @Mapping(value = "common.PetExpConfig")
    public final static String petExpConfig = "petExpConfig";
    @Mapping(value = "common.PetConfig")
    public final static String petConfig = "petconfig";
    @Mapping(value = "common.EquipmentConfig")
    public final static String equipConfig = "equipmentconfig";
    @Mapping(value = "common.PetSkillConfig")
    public final static String petSkillConfig = "petSkillConfig";
    @Mapping(value = "common.ShopItemConfig")
    public final static String shopItemConfig = "shopItemConfig";
    @Mapping(value = "common.PowerupConfig")
    public final static String powerUpConfig = "powerupconfig";
    @Mapping(value = "common.PetHatchConfig")
    public final static String petHatchConfig = "pethatchConfig";
    @Mapping(value = "common.ComposeConfig")
    public final static String petComposeConfig = "composeConfig";
    @Mapping(value = "common.RoleExpConfig")
    public final static String roleExpConfig = "rexpconfig";
    @Mapping(value = "common.MapConfig")
    public final static String mapConfig = "mapConfig";
    @Mapping(value = "common.PetCultivateConfig")
    public final static String petCultivateConfig = "petCultivateConfig";
    @Mapping(value = "common.MonsterConfig")
    public final static String monsterConfig = "monsterConfig";
    @Mapping(value = "common.PetAttributeConfig")
    public final static String petAttributeConfig = "petAttributeConfig";
    @Mapping(value = "common.PetStarConfig")
    public final static String petStarConfig = "petStarConfig";
    @Mapping(value = "common.SignConfig")
    public final static String signConfig = "signConfig";
    @Mapping(value = "common.BreedPetRarityMappingConfig")
    public final static String breedPetRarityMappingConfig = "breedPetRarityMappingConfig";
    @Mapping(value = "common.PetBreakConfig")
    public final static String petBreakConfig = "petBreakConfig";
    @Mapping(value = "common.DispatchConfig")
    public final static String dispatchConfig = "DispatchConfig";
    //  ItemComposeConfig
    @Mapping(value = "common.ItemComposeConfig")
    public final static String itemComposeConfig = "ItemComposeConfig";

    private static Object configMapToObject(String configKey, Class zlass) {
        ExcelConfigObject excelConfigObject = (ExcelConfigObject) (zlass.getDeclaredAnnotation(ExcelConfigObject.class));
        String key = excelConfigObject.key();
        key += keyPrefix;
        Object object = null;
        try {
            object = zlass.newInstance();
            if (!key.equals(configKey.split(":")[0])) {
                /// System.err.println(configKey.split(":")[0]+"@@@@"+"key"+key);
                return null;
            }
            Redis jedis = Redis.getInstance();
            Map<String, String> map = jedis.hgetAll(configKey);
            //System.out.println(configKey+"map"+map);
            //System.out.println("configKey:"+configKey);
            if (map.size() == 0) {
                return null;
            }
            Field[] configObjectFields = zlass.getDeclaredFields();
            for (Field configObjectfield : configObjectFields) {

                if (Modifier.isStatic(configObjectfield.getModifiers())) {
                    continue;
                }
                configObjectfield.setAccessible(true);
                ExcelColumn objectAnnotations = configObjectfield.getDeclaredAnnotation(ExcelColumn.class);
                String name = null;
                String configInfo = null;
                try {
                    name = objectAnnotations.name();
                } catch (Exception e) {
                    /// System.err.println(configObjectfield+"@@@@@@@@@@@"+configKey);
                }

                configInfo = map.get(name);

                //     /// System.err.println(configInfo+"~~~~~~"+objectAnnotations);
                if (objectAnnotations.isList() && objectAnnotations.isCommonInfo()) {
                    List<CommonInfo> list = new ArrayList<CommonInfo>();
                    String[] configInfos = configInfo.split(andSeparator);
                    for (String eachConfigInfo : configInfos) {
                        try {
                            CommonInfo commonInfo = new CommonInfo();
                            //  /// System.err.println(eachConfigInfo+"~~~"+configInfo);
                            commonInfo.setKey(Integer.parseInt(eachConfigInfo.split(itemInfoSeparator)[0]));
                            commonInfo.setValue(Integer.parseInt(eachConfigInfo.split(itemInfoSeparator)[1]));
                            list.add(commonInfo);
                        } catch (Exception e) {
                            /// System.err.println(eachConfigInfo+"~~~~~~"+configInfo);
                            e.printStackTrace();
                        }

                    }
                    configObjectfield.set(object, list);
                } else if (objectAnnotations.isList() && !objectAnnotations.isCommonInfo()) {
                    List<Number> list = new ArrayList<Number>();
                    String[] configInfos = configInfo.split(andSeparator);
                    for (String eachConfigInfo : configInfos) {
                        Number value = 0;
                        if (objectAnnotations.isFloat()) {
                            value = Float.parseFloat(eachConfigInfo);
                        } else {
                            value = Integer.parseInt(eachConfigInfo);
                        }
                        list.add(value);
                        configObjectfield.set(object, list);
                    }
                } else if (!objectAnnotations.isList() && objectAnnotations.isCommonInfo()) {
                    CommonInfo commonInfo = new CommonInfo();
                    commonInfo.setKey(Integer.parseInt(configInfo.split(itemInfoSeparator)[0]));
                    commonInfo.setValue(Integer.parseInt(configInfo.split(itemInfoSeparator)[1]));
                    configObjectfield.set(object, commonInfo);
                } else if (!objectAnnotations.isList() && !objectAnnotations.isCommonInfo()) {
                    try {
                        if (objectAnnotations.isFloat()) {
                            configObjectfield.set(object, Float.parseFloat(configInfo));
                        } else {
                            configObjectfield.set(object, Integer.parseInt(configInfo));
                        }
                    } catch (Exception e) {
                        System.err.println("error   =   " + map + "\n" +
                                "name   =   " + name + "\n" +
                                "configKey   =   " + configKey + "\n" +
                                "configinfo   =   " + configInfo + "\n" +
                                "zclass   =   " + zlass + "\n" +
                                "excelConfigObject = " + excelConfigObject + "\n" +
                                "key = " + key + "\n" +
                                "object = " + object + "\n" +
                                "map = " + map + "\n" +
                                "configObjectFields = " + configObjectFields + "\n" +
                                "configObjectfield = " + configObjectfield + "\n" +
                                "objectAnnotations = " + objectAnnotations
                        );
                        e.printStackTrace();
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return object;
    }

    public static CommonConfig commonConfigObject = null;

    // 根据表格配置名找到配置总表
    public static Map<Integer, Object> getCongifMap(String key) throws ResourceNotFound {
        Map<Integer, Object> config = configMap.get(key);
        if (config == null) {
            throw new ResourceNotFound("传入的键值为" + key);
        }
        return config;
    }

    public static Object getCongifObject(String key, Integer id) throws ResourceNotFound {
        Map<Integer, Object> config = getCongifMap(key);
        Object object = config.get(id);
        if (object == null) {
            throw new ResourceNotFound("传入的键值为" + key + "~~~" + "传入的id为" + id);
        }
        return object;
    }

    //field 为对象的字段名，不是表格里的名称！！！！（反射+循环遍历，可能没有直接去redis查找快？？？？？）
    public static Object getCongifValue(String key, Integer id, String field) throws ResourceNotFound {
        Object object = getCongifObject(key, id);
        Object value = null;
        Class zlass = object.getClass();
        Field[] fields = zlass.getDeclaredFields();
        for (Field fieldObject : fields) {
            if (fieldObject.getName().equals(field)) {
                fieldObject.setAccessible(true);
                try {
                    value = fieldObject.get(object);
                } catch (IllegalAccessException e) {
                    e.printStackTrace();
                }
                break;
            }
        }
        if (value == null) {
            throw new ResourceNotFound("传入的键值为" + key + "~~~" + "传入的id为" + id + "~~~~" + "传入的字段为" + field);
        }
        return value;
    }

}