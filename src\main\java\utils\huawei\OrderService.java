/*
 * Copyright 2020. Huawei Technologies Co., Ltd. All rights reserved.
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 *
 */

package utils.huawei;

import com.alibaba.fastjson.JSONObject;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2019-12-27
 */
public class OrderService {
    // TODO: replace the (ip:port) to the real one, and if the protocol is https, you should deal with the license
    // yourself.
    public static final String TOC_SITE_URL = "https://orders-drcn.iap.hicloud.com";

    // site for telecom carrier
    public static final String TOBTOC_SITE_URL = "https://orders-at-dre.iap.dbankcloud.com";

    public static String getRootUrl() {
        return TOC_SITE_URL;
    }

    public static boolean verifyToken(String purchaseToken, String productId ) throws Exception {
        // fetch the App Level AccessToken
        String appAt = HuaWeiHelper.getAppAT();
        // construct the Authorization in Header
        Map<String, String> headers = HuaWeiHelper.buildAuthorization(appAt);

        // pack the request body
        Map<String, String> bodyMap = new HashMap<String, String>();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String msgBody = JSONObject.toJSONString(bodyMap);

        String response = HuaWeiHelper.httpPost(getRootUrl() + "/applications/purchases/tokens/verify",
            "application/json; charset=UTF-8", msgBody, 5000, 5000, headers);
        // TODO: display the response as string in console, you can replace it with your business logic.
        /// System.out.println(response);

        JSONObject jsonObject = JSONObject.parseObject(response);
        String responseCode = jsonObject.getString("responseCode");
        if(!responseCode.equals("0"))
        {
            return false;
        }
        return true;
    }
//5 向API提供的参数无效。此错误也可能说明应用未在IAP中针对应用内购买结算正确签署或设置，或者在清单中没有所需的权限。
//6 API操作期间发生致命错误。
//8 由于未拥有该商品，消耗或者确认失败。
//9 商品已经消耗或者确认，不能再次消耗或者确认。
//11 用户账号异常，比如已经销户。
    public static void cancelledListPurchase(Long endAt, Long startAt, Integer maxRows, Integer type,
        String continuationToken, Integer accountFlag) throws Exception {
        // fetch the App Level AccessToken
        String appAt = HuaWeiHelper.getAppAT();
        // construct the Authorization in Header
        Map<String, String> headers = HuaWeiHelper.buildAuthorization(appAt);

        // pack the request body
        Map<String, Object> bodyMap = new HashMap<String, Object>();
        bodyMap.put("endAt", endAt);
        bodyMap.put("startAt", startAt);
        bodyMap.put("maxRows", maxRows);
        bodyMap.put("type", type);
        bodyMap.put("continuationToken", continuationToken);

        String msgBody = JSONObject.toJSONString(bodyMap);

        String response = HuaWeiHelper.httpPost(getRootUrl() + "/applications/v2/purchases/cancelledList",
            "application/json; charset=UTF-8", msgBody, 5000, 5000, headers);
        // TODO: display the response as string in console, you can replace it with your business logic.
        /// System.out.println(response);
    }

    public static boolean confirmPurchase(String purchaseToken, String productId) throws Exception {
        // fetch the App Level AccessToken
        String appAt = HuaWeiHelper.getAppAT();
        // construct the Authorization in Header
        Map<String, String> headers = HuaWeiHelper.buildAuthorization(appAt);

        // pack the request body
        Map<String, Object> bodyMap = new HashMap<String, Object>();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String msgBody = JSONObject.toJSONString(bodyMap);

        String response = HuaWeiHelper.httpPost(getRootUrl() + "/applications/v2/purchases/confirm",
                "application/json; charset=UTF-8", msgBody, 5000, 5000, headers);
        // TODO: display the response as string in console, you can replace it with your business logic.
        JSONObject jsonObject = JSONObject.parseObject(response);
        String responseCode = jsonObject.getString("responseCode");
        if(!responseCode.equals("0"))
        {
            return false;
        }
        /// System.out.println(response);
        return true;
    }
}
