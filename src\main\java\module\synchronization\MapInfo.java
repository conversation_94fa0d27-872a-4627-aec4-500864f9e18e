package module.synchronization;

import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2018/6/11.
 */
public class MapInfo {
    private int ballNum;
    private List<BallInfo> ballList;
    private List<SyncMissionInfo> syncMap;
    private Map<Integer,Integer> playerOnBall;

    public Map<Integer, Integer> getPlayerOnBall() {
        return playerOnBall;
    }

    public void setPlayerOnBall(Map<Integer, Integer> playerOnBall) {
        this.playerOnBall = playerOnBall;
    }

    public int getBallNum() {
        return ballNum;
    }

    public void setBallNum(int ballNum) {
        this.ballNum = ballNum;
    }

    public List<SyncMissionInfo> getSyncMap() {
        return syncMap;
    }

    public void setSyncMap(List<SyncMissionInfo> syncMap) {
        this.syncMap = syncMap;
    }

    public List<BallInfo> getBallList() {
        return ballList;
    }

    public void setBallList(List<BallInfo> ballList) {
        this.ballList = ballList;
    }
}
