package module.level;

import entities.LevelEntity;
import manager.MySql;

import java.util.List;

public class LevelDao {
    private static LevelDao inst = null;

    public static LevelDao getInstance() {
        if (inst == null) {
            inst = new LevelDao();
        }
        return inst;
    }
    public LevelEntity queryUid(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from LevelEntity where uid='").append(uid).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (LevelEntity) entity;
    }
    public  LevelEntity insertId(int levelid,int layerid,String uid){
        StringBuffer stringBuffer = new StringBuffer("INSERT INTO LEVEL (levelid ,layerid,uid) VALUES (")
                .append(levelid).append(",").append(layerid).append(",'").append(uid).append("')");
        Object entity = MySql.queryForOne(stringBuffer.toString());

        return (LevelEntity)entity;
    }
}
