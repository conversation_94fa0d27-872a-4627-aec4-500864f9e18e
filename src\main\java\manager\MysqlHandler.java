package manager;
import java.util.ArrayList;
import common.SuperConfig;
import module.callback.CallBack;
import module.task.TaskService;

import org.hibernate.Session;
import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import utils.MyUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2017/11/22.
 */
public class MysqlHandler implements Runnable {
    private static Logger sqlLog = LoggerFactory.getLogger("sqlLog");
    private int type;
    private Object entity;
    private String hql;
    private Object sex;
    private CallBack callBack = null;

    public void setCallBack(CallBack callBack) {
        this.callBack = callBack;
    }

    public void setHql(String hql) {
        this.hql = hql;
    }
    public void setSex(Object sex){
        this.sex =sex;
    }
    public void setType(int type) {
        this.type = type;
    }

    public void setEntity(Object entity) {
        this.entity = entity;
    }

    public void run() {
        switch (type) {
            case SuperConfig.DB_INSERT:
                insert();
                break;
            case SuperConfig.DB_UPDATE:
                    update();
                break;
            case SuperConfig.DB_UPDATESOMES:
                updateSomes();
                break;
            case SuperConfig.DB_DELETE:
                delete();
                break;
            case SuperConfig.DB_QUERYFORONELCALLBACK:
                queryForOne();
                break;
            case SuperConfig.DB_QUERYFORLISTLCALLBACK:
                queryForList();
                break;
        }
    }

    private void insert() {
        Session session = MySql.getSession();
        try {

            session.save(entity);
            session.beginTransaction().commit();
        } catch (Exception e) {
            ///      /// System.err.println(entity);
            session.beginTransaction().rollback();
            e.printStackTrace();
            sqlLog.error(MyUtils.objectToJson(entity));
        } finally {
            session.close();
            MySql.reduceNowSize();
            ///// System.out.println("nowSize over>>>>>>>"+MySql.getNowSize());
        }
    }

    private void update() {
        Session session = MySql.getSession();
        try {
            session.update(entity);
            try {
                session.beginTransaction().commit();
            } catch (Exception e) {
            }
        } catch (Exception e) {
            e.printStackTrace();
            ///    /// System.err.println(entity+"exception");
        }
        session.close();
    }

    private void updateSomes() {
        ///// System.out.println("nowSize begin>>>>>>>" + MySql.getNowSize());
        Session session = MySql.getSession();
        try {
            session.beginTransaction();
            Query query = session.createQuery(hql);
            query.executeUpdate();
            session.getTransaction().commit();
        } catch (Exception e) {
            //  session.beginTransaction().rollback();
            session.getTransaction().rollback();
            e.printStackTrace();
            ///        /// System.err.println(hql+"error!!!!!!!!!!!!");
            sqlLog.error(hql);
        } finally {
            session.close();
            MySql.reduceNowSize();
            ///// System.out.println("nowSize over>>>>>>>"+MySql.getNowSize());
        }
    }

    private void delete() {
        try {
            Session session = MySql.getSession();
            session.delete(entity);
            session.beginTransaction().commit();
            session.close();
        } catch (Exception e) {
            ///      /// System.err.println(entity);
        }

    }

    private void queryForOne() {
        ///// System.out.println("nowSize begin>>>>>>>" + MySql.getNowSize());
        Session session = MySql.getSession();
        try {
            Object object = session.createQuery(hql).uniqueResult();
            if (callBack != null) {
                callBack.execute(object);
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlLog.error(e.getMessage(), e);
        } finally {
            session.close();
            MySql.reduceNowSize();
            ///// System.out.println("nowSize over>>>>>>>"+MySql.getNowSize());
        }
    }

    private void queryForList() {
        // /// System.out.println("nowSize begin>>>>>>>" + MySql.getNowSize());
        Session session = MySql.getSession();
        try {
            //  /// System.out.println(hql);
            List<Object> objectList = session.createQuery(hql).list();
            if (callBack != null) {
                callBack.execute(objectList);
            }
        } catch (Exception e) {
            e.printStackTrace();
            sqlLog.error(e.getMessage(), e);
//            /// System.out.println("errorSql"+hql);
        } finally {
            session.close();
            MySql.reduceNowSize();
            //    /// System.out.println("nowSize over>>>>>>>"+MySql.getNowSize());
        }
    }
}
