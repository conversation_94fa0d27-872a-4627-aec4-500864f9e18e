package entities;


import javax.persistence.*;

@Entity
@Table(name = "petegg", schema = "", catalog = "super_star_fruit")
public class PetEggEntity {
    private int id;
    private String uid; // 玩家
    private int petUid; // 宠物唯一编号
    private int exp;
    private int lv;
    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }
    @Basic
    @Column(name = "petUid")
    public int getPetUid() {
        return petUid;
    }

    public void setPetUid(int petUId) {
        this.petUid = petUId;
    }
    @Basic
    @Column(name = "exp")
    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }
    @Basic
    @Column(name = "lv")
    public int getLv() {
        return lv;
    }

    public void setLv(int lv) {
        this.lv = lv;
    }
}
