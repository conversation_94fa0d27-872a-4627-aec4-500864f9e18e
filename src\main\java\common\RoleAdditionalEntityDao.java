package common;

import entities.RoleAdditionalEntity;
import manager.MySql;

import java.util.List;

public class RoleAdditionalEntityDao {
    private static RoleAdditionalEntityDao inst = null;

    public static RoleAdditionalEntityDao getInstance() {
        if (inst == null) {
            inst = new RoleAdditionalEntityDao();
        }
        return inst;
    }

    public Object getROleBlackMarket(String uid) {
        StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
        RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
        return entity;

    }

    public List<Object> getROleBlackMarkets(String uid) {
        StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
        List<Object> entity = MySql.queryForList(sbu.toString());
        return entity;

    }

    public int getBlackMarketFlushNums(String uid) {
        StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(uid).append("'");
        RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
        if (entity == null) {
            return -1;
        } else {
            return entity.getMarketFlushNums();
        }

    }
}
