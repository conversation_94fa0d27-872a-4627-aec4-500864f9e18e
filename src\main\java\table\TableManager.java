package table;

import manager.Redis;
import table.ItemCompose.ItemComposeLine;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public abstract class TableManager<ITEM extends LineKey> implements TableName, LinePath{
    private ArrayList<ITEM> allItems = new ArrayList<>();
    private HashMap<Integer, ITEM> itemHashMap = new HashMap<>();
    protected TableManager(){
//        System.out.println("启动");

        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys(TableName() + "config:*").iterator();

        while (iterator.hasNext()) {
            String key = iterator.next();
//            System.out.println(key);

            Map<String, String> mailMap = jedis.hgetAll(key);
            int index = Integer.parseInt(key.split(":")[1]);
            ITEM data = CreatITEM(index, mailMap);
            allItems.add(data);
            itemHashMap.put(data.Key(), data);
        }
        Parse();
    }
    public void Parse(){

    }
    private ITEM CreatITEM(int index, Map<String, String> data){
        Object object = null;
        try {
            Class itemClass = Class.forName(LinePath());
            object = itemClass.newInstance();
            Field[] fields = itemClass.getFields();
            for (Field f :
                    fields) {
                String curData = data.get(f.getName());
                if (curData == null){
                    continue;
                }
              //System.err.println("curData :" + curData);
                switch (f.getAnnotatedType().getType().getTypeName()){
                    case "java.lang.String":
                        f.set(object, curData);
                        break;
                    case "int":
                        f.setInt(object, Integer.parseInt(curData));
                        break;
                    case "long":
                        f.setLong(object, Long.parseLong(curData));
                        break;
                    default:
                        System.err.println(f.getAnnotatedType().getType().getTypeName());
                        break;
                }


                System.out.println("Type:  " + f.getAnnotatedType().getType().getTypeName());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return (ITEM) object;
    }

    public ArrayList<ITEM> GetAllItem(){
        return allItems;
    }

    public ITEM GetItem(int key){
        return itemHashMap.get(key);
    }
}
