<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.GameBattleEntity" table="gamebattle" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <timestamp name="time" column="time"/>
        <property name="player1" column="player1"/>
        <property name="player2" column="player2"/>
        <property name="time" column="time" type="timestamp"/>
        <property name="type" column="type"/>
    </class>
</hibernate-mapping>