package module.event;

import com.googlecode.protobuf.format.JsonFormat;
import entities.EventEntity;
import manager.MySql;
import manager.TimerHandler;
import protocol.EventData;

import java.util.List;

public class EventService {

    public static final int EVENTNUMS = 6;
    private static EventService inst = null;
    public static EventDao eventDao = EventDao.getInstance();

    public static EventService getInstance() {
        if (inst == null) {
            inst = new EventService();
        }
        return inst;
    }

    public byte[] getEventInfo(byte[] bytes, String uid) {
        EventData.ResponseGetEventInfo.Builder builder = EventData.ResponseGetEventInfo.newBuilder();
        try {
            List<Object> eventInfos = eventDao.getEventInfos(uid);
            if (eventInfos == null || eventInfos.size() == 0) {
                for (int i = 0; i < EVENTNUMS; i++) {
                    EventEntity eventEntity = EventUtils.createEvent(uid, true);
                    MySql.insert(eventEntity);
                    builder.addEvents(EventUtils.objectToPb(eventEntity));
                }
            } else {
                for (int i = 0; i < eventInfos.size(); i++) {
                    EventEntity eventEntity = (EventEntity) eventInfos.get(i);
                    builder.addEvents(EventUtils.objectToPb(eventEntity));
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        ///  /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder.build().toByteArray();
    }

    public byte[] flushEvent(byte[] bytes, String uid) {
        EventData.RequestFlushEvent flushEvent = null;
        EventData.ResponseFlushEvent.Builder responseFlushEvent = EventData.ResponseFlushEvent.newBuilder();
        responseFlushEvent.setErrorId(0);
        try {
            flushEvent = EventData.RequestFlushEvent.parseFrom(bytes);
            int eventId = flushEvent.getEventId();
            EventEntity eventEntity = eventDao.getEventInfo(uid, eventId);
            if (eventEntity == null) {
                responseFlushEvent.setErrorId(1);
            } else if (eventEntity.getCountdown() > TimerHandler.nowTimeStamp) {
                responseFlushEvent.setErrorId(2);
            } else {
                EventEntity newEntity = EventUtils.createEvent(uid, false);
                if (newEntity == null) {
                    responseFlushEvent.setErrorId(3);
                } else {
                    newEntity.setId(eventEntity.getId());
                    MySql.update(newEntity);
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseFlushEvent.build().toByteArray();

    }
}
