package module.synchronization;

import io.netty.channel.ChannelHandlerContext;

/**
 * Created by nara on 2018/6/15.
 */
public class FightPlayerInfo {
    private String uid;
    private int status;
    private int queue;
    private ChannelHandlerContext ctx;

    public FightPlayerInfo(){
        this.status = 0;
        this.ctx = null;
    }

    public int getQueue() {
        return queue;
    }

    public void setQueue(int queue) {
        this.queue = queue;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }
}
