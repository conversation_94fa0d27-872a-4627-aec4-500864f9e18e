package module.ad;

import entities.AdLogEntity;
import entities.ClickNumEntity;
import manager.MySql;
import module.login.LoginDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2019-06-03 19:24
 */
public class IAdDao implements IAd {
    private static Logger log = LoggerFactory.getLogger(LoginDao.class);
    private static IAdDao inst = null;

    public static IAdDao getInstance() {
        if (inst == null) {
            inst = new IAdDao();
        }
        return inst;
    }

    public ClickNumEntity getClickNumEntity(String user_id) {
        StringBuffer hql = new StringBuffer("from ClickNumEntity where user_id='").append(user_id).append("'");
        Object o = MySql.queryForOne(hql.toString());
        if (o != null) {
            return (ClickNumEntity) o;
        }
        return null;
    }

    public List<Object> getClickNumList(String event_id) {
        StringBuffer hql = new StringBuffer("from AdLogEntity where event_id='").append(event_id).append("'");
        List<Object> list = MySql.queryForList(hql.toString());
        return list;
    }

    public void saveClickNum(ClickNumEntity clickNumEntity) {
        MySql.insert(clickNumEntity);
    }

    public void saveAdLog(AdLogEntity adLogEntity) {
        MySql.insert(adLogEntity);
    }

    public void updateClickNum(int num, String user_id, Date date) {
        String dateStr = dateToString(date);
        StringBuffer hql = new StringBuffer("update from ClickNumEntity set click_num=").append(num).append(", click_time='").append(dateStr).append("' where user_id='").append(user_id).append("'");
        MySql.updateSomes(hql.toString());
    }


    public String dateToString(Date date) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = df.format(date);
        return format;
    }
}
