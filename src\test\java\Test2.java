import entities.InformationEntity;
import manager.MySql;

import java.util.List;

public class Test2 {
    public static void main(String[] args) {
        StringBuilder stringBuilder=new StringBuilder("from InformationEntity");
        List<Object> informationList=MySql.queryForList(stringBuilder.toString());
        for (int i=0;i<informationList.size();i++){
            InformationEntity informationEntity= (InformationEntity) informationList.get(i);
            System.out.println(informationEntity.toString());
        }
    }
}
