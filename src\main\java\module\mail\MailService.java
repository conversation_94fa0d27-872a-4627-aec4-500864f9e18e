package module.mail;


import com.google.protobuf.InvalidProtocolBufferException;

import Json.MailJson;
import entities.MailEntity;
import entities.RoleEntity;
import manager.MySql;
import manager.ReportManager;
import module.friend.FriendDao;
import protocol.ItemData;
import protocol.MailData;
import protocol.ProtoData;
import table.pvp_reward.pvp_rewardTable;
import utils.MyUtils;



public class MailService {
    private static MailService inst = null;
    private MailService() {
    }
    public static MailService getInstance() {
        if (inst == null) {
            inst = new MailService();
        }
        return inst;
    }

    public void RequestCreateNewMail(byte[] bytes, String uid){
        try {
            MailData.RequestCreateNewMail requestCreateNewMail = MailData.RequestCreateNewMail.parseFrom(bytes);
            MailData.Mail newMail = requestCreateNewMail.getNewMail();
            String query = "from RoleEntity where uid='" + uid + "'";
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(query);
            MailJson mailJson = new MailJson();
            mailJson.setContent(newMail.getContent());
            ItemData.Item item = newMail.getAttchment().getItemList().get(0);
            mailJson.setItemStr(newMail.getAttchment().getType() + "#id:" + item.getId() + ",num:" + item.getNum());
            FriendDao.getInstance().SendMail(newMail.getSubjectType(), roleEntity.getId(), newMail.getMailId(),newMail.getSender(),newMail.getTitle(),MyUtils.objectToJson(mailJson),newMail.getStatus(),System.currentTimeMillis(),System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000 ,newMail.getAttchment());
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
    }

    public void RequestNewMailTest(byte[] bytes, String uid) {
//        MailData.ReportNewMail.Builder builder = MailData.ReportNewMail.newBuilder();
//
//        MailData.Mail.Builder mail = MailData.Mail.newBuilder();
//        mail.setMid(1232);
//        mail.setMailId(1001);
//        mail.setSubjectType("1");
//        mail.setContent("测试测试");
//        mail.setStatus(1);
//        mail.setTimeStamp(21);
//        mail.setOverdueTime(1);
//
//        builder.setNewMail(mail);
        //ReportManager.reportInfo(uid, ProtoData.SToC.REPORTNEWMAIL_VALUE, builder.build().toByteArray());
        SentRankReward(45, uid);
    }

    //                             排名         id
    public void SentRankReward(int rank, String roleUid) {
        if (rank <= 0 || rank >= 51){
            return;
        }

        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
        attachment.setType(1);
        int index = 1;
        if (rank < 4){
            index = rank;
        }else if(rank < 11){
            index = 4;
        }else if(rank < 21){
            index = 5;
        }else if(rank < 51){
            index = 6;
        }
        for (String[] data :
                pvp_rewardTable.getInstance().GetItem(index).items) {
            ItemData.Item.Builder item = ItemData.Item.newBuilder();
            item.setId(Integer.parseInt(data[0]));
            item.setNum(Double.parseDouble(data[1]));
            attachment.addItem(item);
        }
        SendMail(roleUid, attachment);
    }

    private void SendMail(String roleUid, MailData.Attachment.Builder attachment){
        //邮件全新版本ReportNewMail2111
        MailEntity entity = new MailEntity();
        entity.setOwner(roleUid);
        entity.setMid(MyUtils.setRandom().hashCode());

        entity.setSender("Sender");
        entity.setSubjectType("SubjectType");
        entity.setTitle("Title");
        entity.setContent("Content");

        entity.setMailId(1001);
        entity.setStatus(0);

        String str1 = MyUtils.stampToDate2(System.currentTimeMillis());
        str1 = str1.substring(0, 4) + str1.substring(5, 7) + str1.substring(8, str1.length());
        long timestamp = Long.parseLong(str1);
        String str2 = MyUtils.stampToDate2(System.currentTimeMillis() + 1000*60*60*24*7);
        str2 = str2.substring(0, 4) + str2.substring(5, 7) + str2.substring(8, str2.length());
        long overdueTime = Long.parseLong(str2);
        entity.setTimestamp(timestamp);
        entity.setOverdueTime(overdueTime);

        if (attachment != null){
            entity.setAttchment(attachment.build().toByteArray());
        }
        MySql.insert(entity);

        MailData.ReportNewMail.Builder reportNewMail = MailData.ReportNewMail.newBuilder();
        reportNewMail.setNewMail(MailDao.getInstance().entityToPb(entity));
        ReportManager.reportInfo(roleUid, ProtoData.SToC.REPORTNEWMAIL_VALUE, reportNewMail.build().toByteArray());
    }
}
