package entities;

import javax.persistence.*;
import java.util.Arrays;

@Entity
@Table(name = "roleadditional", schema = "super_star_fruit", catalog = "super_star_fruit")
public class RoleAdditionalEntity {
    private int id;
    private byte[] blackMarket;
    private String uid;
    private int marketFlushNums;
    private byte[] petFormation;
    private byte[] petBreed;
    private byte[] petEvolution;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "blackMarket")
    public byte[] getBlackMarket() {
        return blackMarket;
    }

    public void setBlackMarket(byte[] blackMarket) {
        this.blackMarket = blackMarket;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "marketFlushNums")
    public int getMarketFlushNums() {
        return marketFlushNums;
    }

    public void setMarketFlushNums(int marketFlushNums) {
        this.marketFlushNums = marketFlushNums;
    }

    @Basic
    @Column(name = "petFormation")
    public byte[] getPetFormation() {
        return petFormation;
    }

    public void setPetFormation(byte[] petFormation) {
        this.petFormation = petFormation;
    }

    @Basic
    @Column(name = "petBreed")
    public byte[] getPetBreed() {
        return petBreed;
    }

    public void setPetBreed(byte[] petBreed) {
        this.petBreed = petBreed;
    }

    @Basic
    @Column(name = "petEvolution")
    public byte[] getPetEvolution() {
        return petEvolution;
    }

    public void setPetEvolution(byte[] petEvolution) {
        this.petEvolution = petEvolution;
    }

    @Override
    public String toString() {
        return "RoleAdditionalEntity{" +
                "id=" + id +
                ", blackMarket=" + Arrays.toString(blackMarket) +
                ", uid='" + uid + '\'' +
                ", marketFlushNums=" + marketFlushNums +
                ", petFormation=" + Arrays.toString(petFormation) +
                ", petBreed=" + Arrays.toString(petBreed) +
                ", petEvolution=" + Arrays.toString(petEvolution) +
                '}';
    }
}
