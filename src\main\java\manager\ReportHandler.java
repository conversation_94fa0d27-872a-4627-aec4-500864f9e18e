package manager;

import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import server.SuperProtocol;
import server.SuperServerHandler;

import java.util.Arrays;
import java.util.Map;

/**
 * Created by nara on 2018/1/15.
 */
public class ReportHandler implements Runnable {
    private String roleId;
    private int msgId;
    private byte[] reportMsg;
    private ChannelHandlerContext ctx = null;
    private boolean needClose = false;

    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }

    public void setNeedClose(boolean needClose) {
        this.needClose = needClose;
    }

    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    public void setReportMsg(byte[] reportMsg) {
        this.reportMsg = reportMsg;
    }

    public void setMsgId(int msgId) {
        this.msgId = msgId;
    }

    public void run() {
        try{
        /*   if (ctx == null){
              ctx = SuperServerHandler.getServerIdCtxFromUid(roleId);
              //  ctx  =SuperServerHandler.serverRoleMap.get(0).get(roleId);
            }*/
            if(ctx==null){
                ctx=SuperServerHandler.rolectx.get(roleId);
            }
            if (ctx != null && reportMsg != null){
                SuperProtocol response = new SuperProtocol(msgId,reportMsg.length,
                        reportMsg);
                if (needClose == true){
                    ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
                }else {
                    ctx.writeAndFlush(response);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    @Override
    public String toString() {
        return "ReportHandler{" +
                "roleId='" + roleId + '\'' +
                ", msgId=" + msgId +
                ", reportMsg=" + Arrays.toString(reportMsg) +
                ", ctx=" + ctx +
                ", needClose=" + needClose +
                '}';
    }
}
