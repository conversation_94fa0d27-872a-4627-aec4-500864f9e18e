package entities;

import javax.persistence.*;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * Created by nara on 2018/4/26.
 */
@Entity
@Table(name = "friend_application", schema = "", catalog = "super_star_fruit")
public class FriendApplicationEntity {
    private int id;
    private String roleuid1;
    private String roleuid2;
    private String date;
    private int apply;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "date")
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Basic
    @Column(name = "roleuid1")
    public String getRoleuid1() {
        return roleuid1;
    }

    public void setRoleuid1(String roleuid1) {
        this.roleuid1 = roleuid1;
    }

    @Basic
    @Column(name = "roleuid2")
    public String getRoleuid2() {
        return roleuid2;
    }

    public void setRoleuid2(String roleuid2) {
        this.roleuid2 = roleuid2;
    }

    @Basic
    @Column(name = "apply")
    public int getApply() {
        return apply;
    }

    public void setApply(int apply) {
        this.apply = apply;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        FriendApplicationEntity that = (FriendApplicationEntity) o;
        return getId() == that.getId() &&
                getApply() == that.getApply() &&
                Objects.equals(getRoleuid1(), that.getRoleuid1()) &&
                Objects.equals(getRoleuid2(), that.getRoleuid2()) &&
                Objects.equals(getDate(), that.getDate());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getRoleuid1(), getRoleuid2(), getDate(), getApply());
    }

    @Override
    public String toString() {
        return "FriendApplicationEntity{" +
                "id=" + id +
                ", roleuid1='" + roleuid1 + '\'' +
                ", roleuid2='" + roleuid2 + '\'' +
                ", date='" + date + '\'' +
                ", apply=" + apply +
                '}';
    }
}
