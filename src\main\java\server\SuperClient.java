package server;

import io.netty.bootstrap.Bootstrap;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import io.netty.handler.codec.LengthFieldPrepender;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.util.CharsetUtil;
import manager.MissionHeartHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by nara on 2017/12/8.
 */
public class SuperClient {
    private static final Logger logger = LoggerFactory.getLogger(SuperClient.class);
    public static String HOST = "localhost";
    public static int PORT = 8567;

    public static Bootstrap bootstrap = getBootstrap();
    public static Channel channel = getChannel(HOST, PORT);

    /**
     * 初始化Bootstrap
     *
     * @return
     */
    public static final Bootstrap getBootstrap() {
        EventLoopGroup group = new NioEventLoopGroup();
        Bootstrap b = new Bootstrap();
        b.group(group).channel(NioSocketChannel.class);
        b.handler(new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel ch) throws Exception {
                ChannelPipeline pipeline = ch.pipeline();
//                pipeline.addLast("frameDecoder",new LengthFieldBasedFrameDecoder(Integer.MAX_VALUE, 0,4, 0, 4));
//                pipeline.addLast("frameEncoder", new LengthFieldPrepender(4));
                pipeline.addLast("decoder",new SuperDecoder());
                pipeline.addLast("encoder",new SuperEncoder());
                pipeline.addLast("handler", new SuperClientHandler());
            }
        });
        b.option(ChannelOption.SO_KEEPALIVE, true);
        return b;
    }

    public static final Channel getChannel(String host, int port) {
        Channel channel = null;
        try {
            channel = bootstrap.connect(host, port).sync().channel();
        } catch (Exception e) {
            logger.error(
                    String.format("连接Server(IP[%s],PORT[%s])失败", host, port), e);
            return null;
        }
        MissionHeartHandler missionHeartHandler = new MissionHeartHandler();
        Thread thread = new Thread(missionHeartHandler);
        thread.start();
        return channel;
    }

    public static final void reConnect(){
        try {
            channel = bootstrap.connect(HOST, PORT).sync().channel();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public static void sendMsg(int msgId,byte[] bytes) throws Exception {
        if (channel != null) {
            if (!channel.isActive()){
                SuperClient.reConnect();
            }
            SuperProtocol response = new SuperProtocol(msgId,bytes.length,
                    bytes);
            channel.writeAndFlush(response).sync();
        } else {
            logger.warn("消息发送失败,连接尚未建立!");
        }
    }
}