import entities.MailEntity;
import manager.MySql;
import utils.MyUtils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Timer;
import java.util.TimerTask;

public class DateTest {
   /* public static class TestDateTimer extends TimerTask {
        @Override
        public void run() {
            /// System.out.println("指定的日期时间已被触发");
        }
    }
    public static void TimeTest(int yue,int ri,int shi,int fen, int miao){
        Calendar calendar=Calendar.getInstance();
        calendar.set(Calendar.MONTH,(yue-1));//月份
        calendar.set(Calendar.DAY_OF_MONTH,ri);//日
        calendar.set(Calendar.HOUR_OF_DAY,shi);//小时
        calendar.set(Calendar.MINUTE,fen);//分钟
        calendar.set(Calendar.SECOND,miao);//秒
        Date time=calendar.getTime();
        Timer timer=new Timer();
        timer.schedule(new DateTest.TestDateTimer(),time);
    }*/
    public static void main(String[] args) {
     /*   {
            TimeTest(8,25,19,20,30);
        }*/
        Date date=new Date();
//        /// System.out.println(date);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String str=simpleDateFormat.format(date);
//        /// System.out.println(str);
        str=str.substring(0,4)+str.substring(5,7)+str.substring(8,str.length());
//        /// System.out.println(str+"\n");
//        /// System.out.println(Integer.parseInt(str));
/*        StringBuilder hql=new StringBuilder("from MailEntity where mid=").append(1630334892);
        MailEntity mailEntity =(MailEntity)MySql.queryForOne(hql.toString());
        /// System.out.println(mailEntity.toString());*/

       /* Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long dates=Long.parseLong(MyUtils.dateToStampComplete(simpleDateFormat.format(date)));
        /// System.out.println(dates/1000);*/
    }
}
