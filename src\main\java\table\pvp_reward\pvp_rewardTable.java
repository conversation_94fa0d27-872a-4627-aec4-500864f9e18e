package table.pvp_reward;

import table.TableManager;
import table.pvp_robot.pvp_robotLine;

import java.util.Comparator;

public class pvp_rewardTable extends TableManager<pvp_rewardLine> {

    private static pvp_rewardTable instance;
    public static pvp_rewardTable getInstance(){
        if (instance != null){
            return instance;
        }
        instance = new pvp_rewardTable();
        return instance;
    }
    private pvp_rewardTable(){

    }

    @Override
    public void Parse() {
        this.GetAllItem().sort(Comparator.comparingInt(a -> a.id));
        for (pvp_rewardLine data :
                GetAllItem()) {
            data.Parse();
        }
    }

    @Override
    public String TableName() {
        return "pvpReward";
    }

    @Override
    public String LinePath() {
        return "table.pvp_reward.pvp_rewardLine";
    }

}
