package table.pvp_reward;

import table.LineKey;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class pvp_rewardLine implements LineKey {
    public int id;
    public String des;
    public String  reward;

    public List<String[]> items;
    public void Parse() {
        items  = new ArrayList<>();
        System.out.println(reward);
        String[] rewards = reward.split("[|]");
        for (String reward : rewards) {
            String[] item = reward.split("[,]");
            items.add(item);
        }
    }

    @Override
    public String toString() {
        return "pvp_rewardLine{" +
                "id=" + id +
                ", des='" + des + '\'' +
                ", reward='" + reward + '\'' +
                ", items=" + items +
                '}';
    }

    @Override
    public int Key() {
        return id;
    }
}
