package table.peteggexp;

import table.LineKey;

public class peteggexpLine implements LineKey {
    public int petlv;
    public String exp;
    public String design;


    @Override
    public String toString() {
        return "peteggexpLine{" +
                "petlv=" + petlv +
                ", exp=" + exp +
                ", design=" + design +
                '}';
    }

    @Override
    public int Key() {
        return petlv;
    }
}
