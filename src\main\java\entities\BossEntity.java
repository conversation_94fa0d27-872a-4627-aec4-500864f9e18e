package entities;
import protocol.BossData;
import javax.persistence.*;
@Entity
@Table(name = "boss", schema = "", catalog = "super_star_fruit")
public class BossEntity {
    private int id;
    private float blood;
    private float SH;
    private String uid;
    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }
    public void setId(int id) {
        this.id = id;
    }


    public float getSH() {
        return SH;
    }

    public void setSH(float SH) {
        this.SH = SH;
    }

    public String getUid() {

        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic

    @Column(name = "blood")
    public float getBlood() {
        return blood;
    }
    public void setBlood(float blood) {
        this.blood = blood;
    }

    @Override
    public String toString() {
        return "BossEntity{" +
                "id=" + id +
                ", blood=" + blood +
                ", SH=" + SH +
                ", uid='" + uid + '\'' +
                '}';
    }
}
