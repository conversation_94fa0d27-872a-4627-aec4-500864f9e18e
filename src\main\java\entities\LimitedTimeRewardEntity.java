package entities;

import javax.persistence.*;

@Entity
@Table(name = "limited_time_reward", schema = "", catalog = "super_star_fruit")
public class LimitedTimeRewardEntity {
    private int id;
    private String uid;

    private boolean charge_reward;
    private int exp;
    private int ordinary_reward_id;
    private int charge_reward_id;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "charge_reward")
    public boolean isCharge_reward() {
        return charge_reward;
    }

    public void setCharge_reward(boolean charge_reward) {
        this.charge_reward = charge_reward;
    }

    @Basic
    @Column(name = "exp")
    public int getExp() {
        return exp;
    }

    public void setExp(int exp) {
        this.exp = exp;
    }

    @Basic
    @Column(name = "ordinary_reward_id")
    public int getOrdinary_reward_id() {
        return ordinary_reward_id;
    }

    public void setOrdinary_reward_id(int ordinary_reward_id) {
        this.ordinary_reward_id = ordinary_reward_id;
    }

    @Basic
    @Column(name = "charge_reward_id")
    public int getCharge_reward_id() {
        return charge_reward_id;
    }

    public void setCharge_reward_id(int charge_reward_id) {
        this.charge_reward_id = charge_reward_id;
    }
}
