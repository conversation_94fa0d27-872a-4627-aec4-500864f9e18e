package entities;

import javax.persistence.*;

@Entity
@Table(name = "consumerecord", schema = "", catalog = "super_star_fruit")
public class ConsumeRecordEntity {
    private int id;
    private String uid;
    private int itemid;
    private int type;
    private int nums;
    private String record;
    private int nownums;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }


    @Basic
    @Column(name = "itemid")
    public int getItemid() {
        return itemid;
    }

    public void setItemid(int itemid) {
        this.itemid = itemid;
    }


    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "nums")
    public int getNums() {
        return nums;
    }

    public void setNums(int nums) {
        this.nums = nums;
    }


    @Basic
    @Column(name = "record")
    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }

    @Basic
    @Column(name = "nownums")

    public int getNownums() {
        return nownums;
    }

    public void setNownums(int nownums) {
        this.nownums = nownums;
    }

    @Override
    public String toString() {
        return "ConsumeRecordEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", itemid=" + itemid +
                ", type=" + type +
                ", nums=" + nums +
                ", record='" + record + '\'' +
                '}';
    }
}
