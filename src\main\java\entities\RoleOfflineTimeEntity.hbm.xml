<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.RoleOfflineTimeEntity" table="role_offline_time" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="roleUid" column="roleUid"/>
        <property name="offlineTime" column="offlineTime"/>
    </class>
</hibernate-mapping>