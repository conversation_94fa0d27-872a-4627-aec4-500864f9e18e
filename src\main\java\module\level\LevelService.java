package module.level;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.LevelEntity;
import manager.MySql;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.levelData;

public class LevelService {
    private static Logger log = LoggerFactory.getLogger(LevelService.class);
    private static LevelService inst = null;

    public static LevelService getInstance() {
        if (inst == null) {
            inst = new LevelService();
        }
        return inst;
    }

    public byte[] RequestLevelid(byte[] bytes, String uid) {
        levelData.RequestLevelNum requestLevelNum = null;
        levelData.ResponseLevelPage.Builder builder = levelData.ResponseLevelPage.newBuilder();
        try {
            requestLevelNum = levelData.RequestLevelNum.parseFrom(bytes);
        } catch (InvalidProtocolBufferException e) {
            log.error(e.getMessage(), e);
        }
        if (requestLevelNum != null) {
            LevelEntity levelEntity = LevelDao.getInstance().queryUid(uid);
            if (levelEntity == null) {
                LevelEntity ab =new LevelEntity();
                ab.setLevelid(requestLevelNum.getPageNumber());
                ab.setLayerid(requestLevelNum.getLeveLnum());
                ab.setUid(uid);
                MySql.insert(ab);
            } else {
                levelEntity.setLevelid(requestLevelNum.getPageNumber());
                levelEntity.setLayerid(requestLevelNum.getLeveLnum());

                MySql.update(levelEntity);
            }

        } else {
            log.error("error");
        }
        return builder.build().toByteArray();
    }
    public byte[] SelectLevelid( String uid) {
        levelData.RequestLevelNum requestLevelNum = null;
        levelData.ResponseLevelD.Builder builder = levelData.ResponseLevelD.newBuilder();

            LevelEntity levelEntity = LevelDao.getInstance().queryUid(uid);
            if (levelEntity==null){
                builder.setNumber(1);
                builder.setRoad(1);
            }else { builder.setNumber(levelEntity.getLevelid());
                builder.setRoad(levelEntity.getLayerid());}

        return builder.build().toByteArray();
    }
}
