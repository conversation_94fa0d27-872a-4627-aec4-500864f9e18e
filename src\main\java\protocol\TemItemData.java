// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: temItem.proto

package protocol;

public final class TemItemData {
  private TemItemData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestTemItemOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // optional int32 wood = 1;
    /**
     * <code>optional int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasWood();
    /**
     * <code>optional int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    int getWood();

    // optional int32 leaf = 2;
    /**
     * <code>optional int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasLeaf();
    /**
     * <code>optional int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    int getLeaf();

    // optional int32 water = 3;
    /**
     * <code>optional int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasWater();
    /**
     * <code>optional int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    int getWater();

    // optional int32 soil = 4;
    /**
     * <code>optional int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasSoil();
    /**
     * <code>optional int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    int getSoil();

    // optional int32 battle = 5;
    /**
     * <code>optional int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasBattle();
    /**
     * <code>optional int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    int getBattle();

    // optional int32 run = 6;
    /**
     * <code>optional int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasRun();
    /**
     * <code>optional int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    int getRun();

    // optional int32 puzzle = 7;
    /**
     * <code>optional int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPuzzle();
    /**
     * <code>optional int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    int getPuzzle();
  }
  /**
   * Protobuf type {@code protocol.RequestTemItem}
   *
   * <pre>
   * 1380  温度层获得道具限制次数
   * </pre>
   */
  public static final class RequestTemItem extends
      com.google.protobuf.GeneratedMessage
      implements RequestTemItemOrBuilder {
    // Use RequestTemItem.newBuilder() to construct.
    private RequestTemItem(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestTemItem(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestTemItem defaultInstance;
    public static RequestTemItem getDefaultInstance() {
      return defaultInstance;
    }

    public RequestTemItem getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestTemItem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              wood_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              leaf_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              water_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              soil_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              battle_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              run_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              puzzle_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.TemItemData.internal_static_protocol_RequestTemItem_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.TemItemData.internal_static_protocol_RequestTemItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.TemItemData.RequestTemItem.class, protocol.TemItemData.RequestTemItem.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestTemItem> PARSER =
        new com.google.protobuf.AbstractParser<RequestTemItem>() {
      public RequestTemItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestTemItem(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestTemItem> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // optional int32 wood = 1;
    public static final int WOOD_FIELD_NUMBER = 1;
    private int wood_;
    /**
     * <code>optional int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasWood() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>optional int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public int getWood() {
      return wood_;
    }

    // optional int32 leaf = 2;
    public static final int LEAF_FIELD_NUMBER = 2;
    private int leaf_;
    /**
     * <code>optional int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasLeaf() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public int getLeaf() {
      return leaf_;
    }

    // optional int32 water = 3;
    public static final int WATER_FIELD_NUMBER = 3;
    private int water_;
    /**
     * <code>optional int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasWater() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public int getWater() {
      return water_;
    }

    // optional int32 soil = 4;
    public static final int SOIL_FIELD_NUMBER = 4;
    private int soil_;
    /**
     * <code>optional int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasSoil() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public int getSoil() {
      return soil_;
    }

    // optional int32 battle = 5;
    public static final int BATTLE_FIELD_NUMBER = 5;
    private int battle_;
    /**
     * <code>optional int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public int getBattle() {
      return battle_;
    }

    // optional int32 run = 6;
    public static final int RUN_FIELD_NUMBER = 6;
    private int run_;
    /**
     * <code>optional int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasRun() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>optional int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public int getRun() {
      return run_;
    }

    // optional int32 puzzle = 7;
    public static final int PUZZLE_FIELD_NUMBER = 7;
    private int puzzle_;
    /**
     * <code>optional int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPuzzle() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>optional int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public int getPuzzle() {
      return puzzle_;
    }

    private void initFields() {
      wood_ = 0;
      leaf_ = 0;
      water_ = 0;
      soil_ = 0;
      battle_ = 0;
      run_ = 0;
      puzzle_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, wood_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, leaf_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, water_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, soil_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, battle_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, run_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, puzzle_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, wood_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, leaf_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, water_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, soil_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, battle_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, run_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, puzzle_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.TemItemData.RequestTemItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.TemItemData.RequestTemItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.TemItemData.RequestTemItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.TemItemData.RequestTemItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.TemItemData.RequestTemItem prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestTemItem}
     *
     * <pre>
     * 1380  温度层获得道具限制次数
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.TemItemData.RequestTemItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.TemItemData.internal_static_protocol_RequestTemItem_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.TemItemData.internal_static_protocol_RequestTemItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.TemItemData.RequestTemItem.class, protocol.TemItemData.RequestTemItem.Builder.class);
      }

      // Construct using protocol.TemItemData.RequestTemItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        wood_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        leaf_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        water_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        soil_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        battle_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        run_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        puzzle_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.TemItemData.internal_static_protocol_RequestTemItem_descriptor;
      }

      public protocol.TemItemData.RequestTemItem getDefaultInstanceForType() {
        return protocol.TemItemData.RequestTemItem.getDefaultInstance();
      }

      public protocol.TemItemData.RequestTemItem build() {
        protocol.TemItemData.RequestTemItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.TemItemData.RequestTemItem buildPartial() {
        protocol.TemItemData.RequestTemItem result = new protocol.TemItemData.RequestTemItem(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.wood_ = wood_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.leaf_ = leaf_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.water_ = water_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.soil_ = soil_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.battle_ = battle_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.run_ = run_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.puzzle_ = puzzle_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.TemItemData.RequestTemItem) {
          return mergeFrom((protocol.TemItemData.RequestTemItem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.TemItemData.RequestTemItem other) {
        if (other == protocol.TemItemData.RequestTemItem.getDefaultInstance()) return this;
        if (other.hasWood()) {
          setWood(other.getWood());
        }
        if (other.hasLeaf()) {
          setLeaf(other.getLeaf());
        }
        if (other.hasWater()) {
          setWater(other.getWater());
        }
        if (other.hasSoil()) {
          setSoil(other.getSoil());
        }
        if (other.hasBattle()) {
          setBattle(other.getBattle());
        }
        if (other.hasRun()) {
          setRun(other.getRun());
        }
        if (other.hasPuzzle()) {
          setPuzzle(other.getPuzzle());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.TemItemData.RequestTemItem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.TemItemData.RequestTemItem) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // optional int32 wood = 1;
      private int wood_ ;
      /**
       * <code>optional int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasWood() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>optional int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public int getWood() {
        return wood_;
      }
      /**
       * <code>optional int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setWood(int value) {
        bitField0_ |= 0x00000001;
        wood_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearWood() {
        bitField0_ = (bitField0_ & ~0x00000001);
        wood_ = 0;
        onChanged();
        return this;
      }

      // optional int32 leaf = 2;
      private int leaf_ ;
      /**
       * <code>optional int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasLeaf() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public int getLeaf() {
        return leaf_;
      }
      /**
       * <code>optional int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setLeaf(int value) {
        bitField0_ |= 0x00000002;
        leaf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearLeaf() {
        bitField0_ = (bitField0_ & ~0x00000002);
        leaf_ = 0;
        onChanged();
        return this;
      }

      // optional int32 water = 3;
      private int water_ ;
      /**
       * <code>optional int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasWater() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public int getWater() {
        return water_;
      }
      /**
       * <code>optional int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setWater(int value) {
        bitField0_ |= 0x00000004;
        water_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearWater() {
        bitField0_ = (bitField0_ & ~0x00000004);
        water_ = 0;
        onChanged();
        return this;
      }

      // optional int32 soil = 4;
      private int soil_ ;
      /**
       * <code>optional int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasSoil() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public int getSoil() {
        return soil_;
      }
      /**
       * <code>optional int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setSoil(int value) {
        bitField0_ |= 0x00000008;
        soil_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearSoil() {
        bitField0_ = (bitField0_ & ~0x00000008);
        soil_ = 0;
        onChanged();
        return this;
      }

      // optional int32 battle = 5;
      private int battle_ ;
      /**
       * <code>optional int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public int getBattle() {
        return battle_;
      }
      /**
       * <code>optional int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setBattle(int value) {
        bitField0_ |= 0x00000010;
        battle_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearBattle() {
        bitField0_ = (bitField0_ & ~0x00000010);
        battle_ = 0;
        onChanged();
        return this;
      }

      // optional int32 run = 6;
      private int run_ ;
      /**
       * <code>optional int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasRun() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public int getRun() {
        return run_;
      }
      /**
       * <code>optional int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setRun(int value) {
        bitField0_ |= 0x00000020;
        run_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearRun() {
        bitField0_ = (bitField0_ & ~0x00000020);
        run_ = 0;
        onChanged();
        return this;
      }

      // optional int32 puzzle = 7;
      private int puzzle_ ;
      /**
       * <code>optional int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPuzzle() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public int getPuzzle() {
        return puzzle_;
      }
      /**
       * <code>optional int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPuzzle(int value) {
        bitField0_ |= 0x00000040;
        puzzle_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPuzzle() {
        bitField0_ = (bitField0_ & ~0x00000040);
        puzzle_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestTemItem)
    }

    static {
      defaultInstance = new RequestTemItem(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestTemItem)
  }

  public interface ResponseTemItemOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 wood = 1;
    /**
     * <code>required int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasWood();
    /**
     * <code>required int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    int getWood();

    // required int32 leaf = 2;
    /**
     * <code>required int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasLeaf();
    /**
     * <code>required int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    int getLeaf();

    // required int32 water = 3;
    /**
     * <code>required int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasWater();
    /**
     * <code>required int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    int getWater();

    // required int32 soil = 4;
    /**
     * <code>required int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasSoil();
    /**
     * <code>required int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    int getSoil();

    // required int32 battle = 5;
    /**
     * <code>required int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasBattle();
    /**
     * <code>required int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    int getBattle();

    // required int32 run = 6;
    /**
     * <code>required int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasRun();
    /**
     * <code>required int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    int getRun();

    // required int32 puzzle = 7;
    /**
     * <code>required int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPuzzle();
    /**
     * <code>required int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    int getPuzzle();
  }
  /**
   * Protobuf type {@code protocol.ResponseTemItem}
   *
   * <pre>
   * 2380
   * </pre>
   */
  public static final class ResponseTemItem extends
      com.google.protobuf.GeneratedMessage
      implements ResponseTemItemOrBuilder {
    // Use ResponseTemItem.newBuilder() to construct.
    private ResponseTemItem(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseTemItem(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseTemItem defaultInstance;
    public static ResponseTemItem getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseTemItem getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseTemItem(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              wood_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              leaf_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              water_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              soil_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              battle_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              run_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              puzzle_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.TemItemData.internal_static_protocol_ResponseTemItem_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.TemItemData.internal_static_protocol_ResponseTemItem_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.TemItemData.ResponseTemItem.class, protocol.TemItemData.ResponseTemItem.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseTemItem> PARSER =
        new com.google.protobuf.AbstractParser<ResponseTemItem>() {
      public ResponseTemItem parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseTemItem(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseTemItem> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 wood = 1;
    public static final int WOOD_FIELD_NUMBER = 1;
    private int wood_;
    /**
     * <code>required int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasWood() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 wood = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public int getWood() {
      return wood_;
    }

    // required int32 leaf = 2;
    public static final int LEAF_FIELD_NUMBER = 2;
    private int leaf_;
    /**
     * <code>required int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasLeaf() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 leaf = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public int getLeaf() {
      return leaf_;
    }

    // required int32 water = 3;
    public static final int WATER_FIELD_NUMBER = 3;
    private int water_;
    /**
     * <code>required int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasWater() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 water = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public int getWater() {
      return water_;
    }

    // required int32 soil = 4;
    public static final int SOIL_FIELD_NUMBER = 4;
    private int soil_;
    /**
     * <code>required int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasSoil() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 soil = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public int getSoil() {
      return soil_;
    }

    // required int32 battle = 5;
    public static final int BATTLE_FIELD_NUMBER = 5;
    private int battle_;
    /**
     * <code>required int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required int32 battle = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public int getBattle() {
      return battle_;
    }

    // required int32 run = 6;
    public static final int RUN_FIELD_NUMBER = 6;
    private int run_;
    /**
     * <code>required int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasRun() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required int32 run = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public int getRun() {
      return run_;
    }

    // required int32 puzzle = 7;
    public static final int PUZZLE_FIELD_NUMBER = 7;
    private int puzzle_;
    /**
     * <code>required int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPuzzle() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required int32 puzzle = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public int getPuzzle() {
      return puzzle_;
    }

    private void initFields() {
      wood_ = 0;
      leaf_ = 0;
      water_ = 0;
      soil_ = 0;
      battle_ = 0;
      run_ = 0;
      puzzle_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasWood()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasLeaf()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasWater()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSoil()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasBattle()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasRun()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPuzzle()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, wood_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, leaf_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, water_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, soil_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, battle_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(6, run_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeInt32(7, puzzle_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, wood_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, leaf_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, water_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, soil_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, battle_);
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, run_);
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, puzzle_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.TemItemData.ResponseTemItem parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.TemItemData.ResponseTemItem parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.TemItemData.ResponseTemItem parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.TemItemData.ResponseTemItem parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.TemItemData.ResponseTemItem prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseTemItem}
     *
     * <pre>
     * 2380
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.TemItemData.ResponseTemItemOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.TemItemData.internal_static_protocol_ResponseTemItem_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.TemItemData.internal_static_protocol_ResponseTemItem_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.TemItemData.ResponseTemItem.class, protocol.TemItemData.ResponseTemItem.Builder.class);
      }

      // Construct using protocol.TemItemData.ResponseTemItem.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        wood_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        leaf_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        water_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        soil_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        battle_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        run_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        puzzle_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.TemItemData.internal_static_protocol_ResponseTemItem_descriptor;
      }

      public protocol.TemItemData.ResponseTemItem getDefaultInstanceForType() {
        return protocol.TemItemData.ResponseTemItem.getDefaultInstance();
      }

      public protocol.TemItemData.ResponseTemItem build() {
        protocol.TemItemData.ResponseTemItem result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.TemItemData.ResponseTemItem buildPartial() {
        protocol.TemItemData.ResponseTemItem result = new protocol.TemItemData.ResponseTemItem(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.wood_ = wood_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.leaf_ = leaf_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.water_ = water_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.soil_ = soil_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.battle_ = battle_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.run_ = run_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.puzzle_ = puzzle_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.TemItemData.ResponseTemItem) {
          return mergeFrom((protocol.TemItemData.ResponseTemItem)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.TemItemData.ResponseTemItem other) {
        if (other == protocol.TemItemData.ResponseTemItem.getDefaultInstance()) return this;
        if (other.hasWood()) {
          setWood(other.getWood());
        }
        if (other.hasLeaf()) {
          setLeaf(other.getLeaf());
        }
        if (other.hasWater()) {
          setWater(other.getWater());
        }
        if (other.hasSoil()) {
          setSoil(other.getSoil());
        }
        if (other.hasBattle()) {
          setBattle(other.getBattle());
        }
        if (other.hasRun()) {
          setRun(other.getRun());
        }
        if (other.hasPuzzle()) {
          setPuzzle(other.getPuzzle());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasWood()) {
          
          return false;
        }
        if (!hasLeaf()) {
          
          return false;
        }
        if (!hasWater()) {
          
          return false;
        }
        if (!hasSoil()) {
          
          return false;
        }
        if (!hasBattle()) {
          
          return false;
        }
        if (!hasRun()) {
          
          return false;
        }
        if (!hasPuzzle()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.TemItemData.ResponseTemItem parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.TemItemData.ResponseTemItem) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 wood = 1;
      private int wood_ ;
      /**
       * <code>required int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasWood() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public int getWood() {
        return wood_;
      }
      /**
       * <code>required int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setWood(int value) {
        bitField0_ |= 0x00000001;
        wood_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 wood = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearWood() {
        bitField0_ = (bitField0_ & ~0x00000001);
        wood_ = 0;
        onChanged();
        return this;
      }

      // required int32 leaf = 2;
      private int leaf_ ;
      /**
       * <code>required int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasLeaf() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public int getLeaf() {
        return leaf_;
      }
      /**
       * <code>required int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setLeaf(int value) {
        bitField0_ |= 0x00000002;
        leaf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 leaf = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearLeaf() {
        bitField0_ = (bitField0_ & ~0x00000002);
        leaf_ = 0;
        onChanged();
        return this;
      }

      // required int32 water = 3;
      private int water_ ;
      /**
       * <code>required int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasWater() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public int getWater() {
        return water_;
      }
      /**
       * <code>required int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setWater(int value) {
        bitField0_ |= 0x00000004;
        water_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 water = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearWater() {
        bitField0_ = (bitField0_ & ~0x00000004);
        water_ = 0;
        onChanged();
        return this;
      }

      // required int32 soil = 4;
      private int soil_ ;
      /**
       * <code>required int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasSoil() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public int getSoil() {
        return soil_;
      }
      /**
       * <code>required int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setSoil(int value) {
        bitField0_ |= 0x00000008;
        soil_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 soil = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearSoil() {
        bitField0_ = (bitField0_ & ~0x00000008);
        soil_ = 0;
        onChanged();
        return this;
      }

      // required int32 battle = 5;
      private int battle_ ;
      /**
       * <code>required int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public int getBattle() {
        return battle_;
      }
      /**
       * <code>required int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setBattle(int value) {
        bitField0_ |= 0x00000010;
        battle_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 battle = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearBattle() {
        bitField0_ = (bitField0_ & ~0x00000010);
        battle_ = 0;
        onChanged();
        return this;
      }

      // required int32 run = 6;
      private int run_ ;
      /**
       * <code>required int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasRun() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public int getRun() {
        return run_;
      }
      /**
       * <code>required int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setRun(int value) {
        bitField0_ |= 0x00000020;
        run_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 run = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearRun() {
        bitField0_ = (bitField0_ & ~0x00000020);
        run_ = 0;
        onChanged();
        return this;
      }

      // required int32 puzzle = 7;
      private int puzzle_ ;
      /**
       * <code>required int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPuzzle() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public int getPuzzle() {
        return puzzle_;
      }
      /**
       * <code>required int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPuzzle(int value) {
        bitField0_ |= 0x00000040;
        puzzle_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 puzzle = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPuzzle() {
        bitField0_ = (bitField0_ & ~0x00000040);
        puzzle_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseTemItem)
    }

    static {
      defaultInstance = new ResponseTemItem(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseTemItem)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestTemItem_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestTemItem_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseTemItem_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseTemItem_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rtemItem.proto\022\010protocol\032\013proto.proto\"v" +
      "\n\016RequestTemItem\022\014\n\004wood\030\001 \001(\005\022\014\n\004leaf\030\002" +
      " \001(\005\022\r\n\005water\030\003 \001(\005\022\014\n\004soil\030\004 \001(\005\022\016\n\006bat" +
      "tle\030\005 \001(\005\022\013\n\003run\030\006 \001(\005\022\016\n\006puzzle\030\007 \001(\005\"w" +
      "\n\017ResponseTemItem\022\014\n\004wood\030\001 \002(\005\022\014\n\004leaf\030" +
      "\002 \002(\005\022\r\n\005water\030\003 \002(\005\022\014\n\004soil\030\004 \002(\005\022\016\n\006ba" +
      "ttle\030\005 \002(\005\022\013\n\003run\030\006 \002(\005\022\016\n\006puzzle\030\007 \002(\005B" +
      "\rB\013TemItemData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestTemItem_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestTemItem_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestTemItem_descriptor,
              new java.lang.String[] { "Wood", "Leaf", "Water", "Soil", "Battle", "Run", "Puzzle", });
          internal_static_protocol_ResponseTemItem_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseTemItem_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseTemItem_descriptor,
              new java.lang.String[] { "Wood", "Leaf", "Water", "Soil", "Battle", "Run", "Puzzle", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
