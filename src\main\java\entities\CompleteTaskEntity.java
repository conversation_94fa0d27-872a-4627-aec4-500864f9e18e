package entities;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Objects;

/**
 * Created by nara on 2018/4/26.
 */
@Entity
@Table(name = "complete_task", schema = "", catalog = "super_star_fruit")
public class CompleteTaskEntity {
    public CompleteTaskEntity() {
    }

    public CompleteTaskEntity(int id, String uid, String time, byte[] completeTask) {

        this.id = id;
        this.uid = uid;
        this.time = time;
        this.completeTask = completeTask;
    }

    private int id;
    private String uid;
    private String time;
    private byte[] completeTask;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "time")
    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    @Basic
    @Column(name = "complete_task")
    public byte[] getCompleteTask() {
        return completeTask;
    }

    public void setCompleteTask(byte[] completeTask) {
        this.completeTask = completeTask;
    }

    @Override
    public String toString() {
        return "CompleteTaskEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", time='" + time + '\'' +
                ", completeTask=" + Arrays.toString(completeTask) +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CompleteTaskEntity that = (CompleteTaskEntity) o;
        return getId() == that.getId() &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getTime(), that.getTime()) &&
                Arrays.equals(getCompleteTask(), that.getCompleteTask());
    }

    @Override
    public int hashCode() {

        int result = Objects.hash(getId(), getUid(), getTime());
        result = 31 * result + Arrays.hashCode(getCompleteTask());
        return result;
    }
}
