package server;

import java.security.cert.CertificateException;

import javax.net.ssl.SSLException;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.ChannelFuture;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslContextBuilder;
import io.netty.handler.ssl.util.SelfSignedCertificate;

/**
 * 
 * Title: NettyServer Description: Netty服务端 Http测试 Version:1.0.0
 */
public class ADServer implements Runnable {
	private static final int port = 10010; // 设置服务端端口
	private static EventLoopGroup group = new NioEventLoopGroup(); // 通过nio方式来接收连接和处理连接
	private static ServerBootstrap b = new ServerBootstrap();

	public void run() {
		try {
			final SslContext sslCtx;
			SelfSignedCertificate ssc = new SelfSignedCertificate();
			sslCtx = SslContextBuilder.forServer(ssc.certificate(), ssc.privateKey()).build();

			b.group(group);
			b.channel(NioServerSocketChannel.class);
			b.childHandler(new HttpServerFilter(sslCtx)); // 设置过滤器
			// 服务器绑定端口监听
			ChannelFuture f = null;
			try {
				f = b.bind(port).sync();
				System.out.println("服务端启动成功,端口是:" + port);
				// 监听服务器关闭监听
				f.channel().closeFuture().sync();
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		} catch (CertificateException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} catch (SSLException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		} finally {
			group.shutdownGracefully(); //关闭EventLoopGroup，释放掉所有资源包括创建的线程
		}
	}
}
