package module.mission;

import com.google.protobuf.InvalidProtocolBufferException;
import common.SuperConfig;
import entities.*;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.TimerHandler;
import model.MessageInfo;
import model.RewardInfo;
import module.friend.FriendDao;
import module.friend.IFriend;
import module.item.IItem;
import module.item.ItemDao;
import module.login.ILogin;
import module.login.LoginDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ItemData;
import protocol.MissionData;
import protocol.ProtoData;
import protocol.UserData;
import utils.MyUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2017/11/30.
 */
public class MissionService {
    private static Logger log = LoggerFactory.getLogger(MissionService.class);

    private static MissionService inst = null;
    public static MissionService getInstance() {
        if (inst == null) {
            inst = new MissionService();
        }
        return inst;
    }

    public byte[] beginMission(byte[] bytes,String uid){
        MissionData.RequestBeginMission requestBeginMission = null;
        MissionData.ResponseBeginMission.Builder builder = MissionData.ResponseBeginMission.newBuilder();
        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestBeginMission = MissionData.RequestBeginMission.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestBeginMission == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[beginMission] error 1");
            } else {
                IMission iMission = MissionDao.getInstance();
                int val = iMission.judgeMission(requestBeginMission.getMissionId(),uid);
                if (val > 0) {
                    builder.setErrorId(val);
                    log.error(uid+":[beginMission] error 2");
                } else {
                    ILogin iLogin = LoginDao.getInstance();
                   // List<String> actionList = iLogin.useAction(uid, SuperConfig.getMissionCostAction());
                    List<String> actionList = iLogin.useAction(uid,0);
                    if (actionList == null){
                        builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        log.error(uid+":[beginMission] error 3");
                    }else {
                  builder = iMission.getMissionMap(requestBeginMission.getMissionId(), uid);
                      //  builder.setAction(Integer.parseInt(actionList.get(0)));
                        IItem itemDao =ItemDao.getInstance();
                        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MISSION, requestBeginMission.getMissionId());
                        int consume = Integer.parseInt(missionMap.get("consume"));
                        Redis jedis=Redis.getInstance();
                        String gold=jedis.hget("roleitem:"+uid+"#0","1");
                        if(gold==null){
                            StringBuffer sql =new StringBuffer("from ItemEntity where uid='").append(uid).append("' and itemid=1");
                            ItemEntity itemEntity=(ItemEntity)MySql.queryForOne(sql.toString());
                            gold=itemEntity.getItemnum()+"";
                        }
                        if(consume>Double.parseDouble(gold)){
                            builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        }else{
                        double total=itemDao.updateItemInfo(uid,1,-consume);
                        builder.setActionStamp(actionList.get(1));
                        builder.setAction(new Double(total).intValue());
                        GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录进入关卡操作
                        gameRecordEntity.setGamepattern(2);
                        gameRecordEntity.setMissionid(requestBeginMission.getMissionId());
                        gameRecordEntity.setType(0);
                        gameRecordEntity.setUid(uid);
                        RewardInfo rewardInfo=new RewardInfo();
                        rewardInfo.setItemtotal(new Double(total).intValue());
                        rewardInfo.setItemid(1);
                        rewardInfo.setItemnums(-consume);
                        gameRecordEntity.setReward(MyUtils.objectToJson(rewardInfo));
                        MySql.insert(gameRecordEntity);
                        }
                    }
                }
            }
        }

        return builder.build().toByteArray();
    }
    public byte[] beginMission1(byte[] bytes,String uid){
        MissionData.RequestBeginMission requestBeginMission = null;
        MissionData.ResponseBeginMission.Builder builder = MissionData.ResponseBeginMission.newBuilder();
        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestBeginMission = MissionData.RequestBeginMission.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestBeginMission == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[beginMission] error 1");
            } else {
                IMission iMission = MissionDao.getInstance();
                int val = iMission.judgeMission(requestBeginMission.getMissionId(),uid);
                if (val > 0) {
                    builder.setErrorId(val);
                    log.error(uid+":[beginMission] error 2");
                } else {
                    ILogin iLogin = LoginDao.getInstance();
                    List<String> actionList = iLogin.useAction(uid, SuperConfig.getMissionCostAction());
                    if (actionList == null){
                        builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        log.error(uid+":[beginMission] error 3");
                    }else {
                       builder = iMission.getMissionMap(requestBeginMission.getMissionId(), uid);
                        builder.setAction(Integer.parseInt(actionList.get(0)));
                        builder.setActionStamp(actionList.get(1));
                       /* IItem itemDao =ItemDao.getInstance();
                        Map<String, String> missionMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MISSION, requestBeginMission.getMissionId());
                        int consume = Integer.parseInt(missionMap.get("consume"));
                        Redis jedis=Redis.getInstance();
                        String gold=jedis.hget("roleitem:"+uid+"#0","1");
                        if(consume>Integer.parseInt(gold)){
                            builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        }else{
                            double total=itemDao.updateItemInfo(uid,1,consume);
                            builder.setActionStamp(actionList.get(1));
                            builder.setAction(new Double(total).intValue());
                        }*/
                    }
                }
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] beginGold(byte[] bytes,String uid){
        MissionData.RequestBeginGold requestBeginGold = null;
        MissionData.ResponseBeginGold.Builder builder = MissionData.ResponseBeginGold.newBuilder();
        Redis jedis = Redis.getInstance();
        Map<String, String> stringMap = jedis.hgetAll("roleitem:"+uid+"#0");
        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
            builder.setIsOk(false);
        }else {
            MyUtils.sendEmailNew(uid);
            try {
                requestBeginGold = MissionData.RequestBeginGold.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            Integer type=requestBeginGold.getType();
            Integer missionid=requestBeginGold.getMissionId();
            Integer goldnum=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARMISSION, missionid+ "", "ticket"));
            ItemDao itemDao=ItemDao.getInstance();
            if(type==1){
                if(Integer.valueOf(stringMap.get("1").substring(0,stringMap.get("1").length()-2))<goldnum){
                        builder.setIsOk(false);
                }else {
                        itemDao.updateItemInfo(uid,1,-goldnum);//扣金币
                        builder.setIsOk(true);
                }
            }else if(type==2){
                builder.setIsOk(true);
            }else {
                Integer tili=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STARMISSION, missionid+ "", "step_hp"));
                if(Integer.valueOf(stringMap.get("24").substring(0,stringMap.get("24").length()-2))<tili){
                    builder.setIsOk(false);
                }else {
                    itemDao.updateItemInfo(uid,24,-tili);//扣体力
                    builder.setIsOk(true);
                }
            }
            IMission iMission = MissionDao.getInstance();
            builder = iMission.createList(missionid,uid,type);

            Map<String,String> map = jedis.hgetAll("role:"+uid);//获取role
            map.put("actionstamp",System.currentTimeMillis()+"");
            jedis.hmset("role:"+uid,map);
        }
        return builder.build().toByteArray();
    }

    public byte[] beginStack(byte[] bytes,String uid){
        MissionData.RequestBeginStack requestBeginStack=null;
        MissionData.ResponseBeginStack.Builder builder=MissionData.ResponseBeginStack.newBuilder();
        try {
            requestBeginStack = MissionData.RequestBeginStack.parseFrom(bytes);
            Integer missId=requestBeginStack.getMissionId();
            log.info(missId+"");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        // Integer needId=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DECISION, 1+ "", "reduce"));
        // ItemDao itemdao=ItemDao.getInstance();
        // Double tile= itemdao.getItemNum(uid,24);
        // if (tile < needId){
        //     log.error(uid+":[compose] error >>>num:"+tile+",needNum:"+needId);
        //     builder.setErrorId(ProtoData.ErrorCode.ACTIONLACK_VALUE);
        // }else 
        // {
        //     MyUtils.sendEmailNew(uid);

        //     itemdao.updateItemInfo(uid,24,-needId);
        //     builder.setErrorId(0);
        // }

        ILogin iLogin = LoginDao.getInstance();
        List<String> actionList = iLogin.useAction(uid, SuperConfig.getMissionCostAction());
        if (actionList == null)
        {
            builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
            log.error(uid+":[RequestBeginStack] error 3");
        }
        else 
        {
            builder.setErrorId(0);         
            builder.setAction(Integer.parseInt(actionList.get(0)));
            builder.setActionStamp(actionList.get(1));
            GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录进入关卡操作
            gameRecordEntity.setGamepattern(6);
            gameRecordEntity.setMissionid(requestBeginStack.getMissionId());
            gameRecordEntity.setType(0);
            gameRecordEntity.setUid(uid);
            RewardInfo rewardInfo=new RewardInfo();
            rewardInfo.setItemtotal(Integer.parseInt(actionList.get(0)));
            rewardInfo.setItemid(24);
            rewardInfo.setItemnums(-SuperConfig.getMissionCostAction());
            gameRecordEntity.setReward(MyUtils.objectToJson(rewardInfo));
            MySql.insert(gameRecordEntity);
        }        
        return builder.build().toByteArray();

    }
    public byte[] beginDown(byte[] bytes,String uid){
        MissionData.RequestBeginDown requestBeginDown=null;
        MissionData.ResponseBeginDown.Builder builder=MissionData.ResponseBeginDown.newBuilder();
        try {
            requestBeginDown = MissionData.RequestBeginDown.parseFrom(bytes);
            Integer missId=requestBeginDown.getMissionId();
            log.info(missId+"");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        // Integer needId=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DECISION, 1+ "", "reduce"));
        // ItemDao itemdao=ItemDao.getInstance();
        // Double tile= itemdao.getItemNum(uid,24);
        // if (tile < needId){
        //     log.error(uid+":[compose] error >>>num:"+tile+",needNum:"+needId);
        //     builder.setErrorId(ProtoData.ErrorCode.ACTIONLACK_VALUE);
        // }else
        // {
        //     MyUtils.sendEmailNew(uid);

        //     itemdao.updateItemInfo(uid,24,-needId);
        //     builder.setErrorId(0);
        // }

        ILogin iLogin = LoginDao.getInstance();
        List<String> actionList = iLogin.useAction(uid, SuperConfig.getMissionCostAction());
        if (actionList == null)
        {
            builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
            log.error(uid+":[RequestBeginDown] error 3");
        }
        else
        {
            MyUtils.sendEmailNew(uid);

            builder.setErrorId(0);
            builder.setAction(Integer.parseInt(actionList.get(0)));
            builder.setActionStamp(actionList.get(1));
            GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录进入关卡操作
            gameRecordEntity.setGamepattern(1);
            gameRecordEntity.setMissionid(requestBeginDown.getMissionId());
            gameRecordEntity.setType(0);
            gameRecordEntity.setUid(uid);
            RewardInfo rewardInfo=new RewardInfo();
            rewardInfo.setItemtotal(Integer.parseInt(actionList.get(0)));
            rewardInfo.setItemid(24);
            rewardInfo.setItemnums(-SuperConfig.getMissionCostAction());
            gameRecordEntity.setReward(MyUtils.objectToJson(rewardInfo));
            MySql.insert(gameRecordEntity);
        }
        return builder.build().toByteArray();

    }

    public byte[] countMission(byte[] bytes,String uid){
        MissionData.RequestCountMission requestCountMission = null;
        MissionData.ResponseCountMission.Builder builder = MissionData.ResponseCountMission.newBuilder();
        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestCountMission = MissionData.RequestCountMission.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestCountMission == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid + ":[countMission] error");
            } else {
                IMission iMission = MissionDao.getInstance();
                builder = iMission.countMission(requestCountMission.getMissionId(), uid ,requestCountMission.getIsTimeOut(), requestCountMission.getBallIdsList());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] beginEndless(byte[] bytes,String uid){
        MissionData.RequestBeginEndless requestBeginEndless = null;
        MissionData.ResponseBeginEndless.Builder builder = MissionData.ResponseBeginEndless.newBuilder();
        builder.setType(0);
        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestBeginEndless = MissionData.RequestBeginEndless.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestBeginEndless == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[beginEndless] error 1");
            } else {
                if (requestBeginEndless.getType() == 1){
                    ILogin iLogin = LoginDao.getInstance();
                    List<String> actionList = iLogin.useAction(uid, SuperConfig.getEndlessCostAction());
                    if (actionList == null){
                        builder.setType(requestBeginEndless.getType());
                        builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        log.error(uid+":[beginEndless] error 2");
                    }else {
                        IMission iMission = MissionDao.getInstance();
                        builder = iMission.getEndlessMap(requestBeginEndless.getType(),requestBeginEndless.getEndlessId(), uid);
                        builder.setAction(Integer.parseInt(actionList.get(0)));
                        builder.setActionStamp(actionList.get(1));
                        GameRecordEntity gameRecordEntity =new GameRecordEntity();//记录进入关卡操作
                        gameRecordEntity.setGamepattern(3);
                        gameRecordEntity.setMissionid(requestBeginEndless.getEndlessId());
                        gameRecordEntity.setType(0);
                        gameRecordEntity.setUid(uid);
                        RewardInfo rewardInfo=new RewardInfo();
                        rewardInfo.setItemtotal(Integer.parseInt(actionList.get(0)));
                        rewardInfo.setItemid(24);
                        rewardInfo.setItemnums(-SuperConfig.getMissionCostAction());
                        gameRecordEntity.setReward(MyUtils.objectToJson(rewardInfo));
                        MySql.insert(gameRecordEntity);
                    }
                }else {
                    IMission iMission = MissionDao.getInstance();
                    builder = iMission.getEndlessMap(requestBeginEndless.getType(),requestBeginEndless.getEndlessId(), uid);
                }
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] countEndless(byte[] bytes,String uid){
        MissionData.RequestCountEndless requestCountEndless = null;
        MissionData.ResponseCountEndless.Builder builder = MissionData.ResponseCountEndless.newBuilder();
        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestCountEndless = MissionData.RequestCountEndless.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestCountEndless == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[countEndless] error");
            } else {
                IMission iMission = MissionDao.getInstance();
                builder = iMission.countEndless(uid,requestCountEndless.getEndlessId() ,requestCountEndless.getTotalTime(), requestCountEndless.getTotalIntegral(),requestCountEndless.getMaxCombo());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] beginHeadBallMission(byte[] bytes, String uid) {
        MissionData.RequestBeginHeadBall requestBeginHeadBall=null;
        MissionData.ResponseBeginHeadBall.Builder builder=MissionData.ResponseBeginHeadBall.newBuilder();
        IMission iMission = MissionDao.getInstance();

        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestBeginHeadBall = MissionData.RequestBeginHeadBall.parseFrom(bytes);
                requestBeginHeadBall.getMissionid();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestBeginHeadBall == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[beginMission] error 1");
            }else {
                    ILogin iLogin = LoginDao.getInstance();
                    List<String> actionList = iLogin.useAction(uid, SuperConfig.getMissionCostAction());
                    if (actionList == null){
                        builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        log.error(uid+":[beginMission] error 3");
                    }else {
                        int missionid = requestBeginHeadBall.getMissionid();
                        boolean flag=iMission.isFirst(uid,missionid);
                        builder.setIsFirst(flag);
                        builder.setAction(Integer.parseInt(actionList.get(0)));
                        builder.setActionStamp(actionList.get(1));
                    }
                }
            }
        return builder.build().toByteArray();
    }

    //结算顶球
    public byte[] countHeadBallMission(byte[] bytes, String uid) {
        MissionData.RequestCountHeadBall requestCountHeadBall=null;
        MissionData.ResponseCountHeadBall.Builder builder=MissionData.ResponseCountHeadBall.newBuilder();

        if (uid == null){
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        }else {
            try {
                requestCountHeadBall = MissionData.RequestCountHeadBall.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestCountHeadBall == null) {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[beginMission] error 1");
            } else {
                IMission iMission = MissionDao.getInstance();
                boolean isSuccess = requestCountHeadBall.getIsSuccess();
                int isThreeStar = requestCountHeadBall.getIsThreeStar();
                int exp = requestCountHeadBall.getExp();
                int jumpnum = requestCountHeadBall.getJumpnum();
                int missionId = requestCountHeadBall.getMissionId();

                // HeadBallEntity headBallEntity = new HeadBallEntity();
                // headBallEntity.setIs_threestar(isThreeStar);
                // headBallEntity.setUser_id(uid);
                // headBallEntity.setJump_missionid(missionId);
                // headBallEntity.setJump_num(jumpnum);

                //builder
                //  = iMission.countHeadBallMission(headBallEntity,isSuccess,exp,requestCountHeadBall.getItemList());
                builder = iMission.countJumpHeadBallMission(missionId,uid,jumpnum,isThreeStar,isSuccess);

            }
        }
        return builder.build().toByteArray();
    }

    //结算金币
    public byte[] countGold(byte[] bytes, String uid){
        MissionData.RequestCountGold requestCountHeadBall=null;
        MissionData.ResponseCountGold.Builder builder=MissionData.ResponseCountGold.newBuilder();
        try {
            requestCountHeadBall=MissionData.RequestCountGold.parseFrom(bytes);;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        List<ItemData.Item> itemList=requestCountHeadBall.getItemList();
        IMission iMission = MissionDao.getInstance();
        builder = iMission.countGold(itemList,uid);


        return builder.build().toByteArray();
    }

    //落球结算
    public byte[] countDown(byte[] bytes, String uid){
        MissionData.ResponseCountDown.Builder builder=MissionData.ResponseCountDown.newBuilder();
        MissionData.RequestCountDown requestCountDown=null;
        try {
            requestCountDown=MissionData.RequestCountDown.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        IMission iMission = MissionDao.getInstance();
        builder = iMission.countDown(requestCountDown,uid);
        return builder.build().toByteArray();
    }
    public byte[] countStack(byte[] bytes, String uid){
        MissionData.ResponseCountStack.Builder builder=MissionData.ResponseCountStack.newBuilder();
        MissionData.RequestCountStack requestCountStack=null;
        try {
            requestCountStack=MissionData.RequestCountStack.parseFrom(bytes);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        IMission iMission = MissionDao.getInstance();
        builder = iMission.countStack(requestCountStack,uid);
        return builder.build().toByteArray();

    }


    public byte[] sourceMachine(ChannelHandlerContext ctx,String uid){
        MissionData.ResposeSourceMachine.Builder builder=MissionData.ResposeSourceMachine.newBuilder();
        IMission iMission = MissionDao.getInstance();
        builder =iMission.sourceMachine(ctx,uid);
        return null;
    }
    public byte[]  makeItem(byte[] bytes,String uid) {
         MissionData.ResposeMakeItem.Builder builder=MissionData.ResposeMakeItem.newBuilder();
        MissionData.RequestMakeItem requestMakeItem=null;
        try{
            requestMakeItem  =MissionData.RequestMakeItem.parseFrom(bytes);
        }catch(Exception e){
            e.printStackTrace();
        }
         IMission iMission = MissionDao.getInstance();
        builder=iMission.makeItem(requestMakeItem.getItemId(),requestMakeItem.getItemNums(), uid);
        return builder.build().toByteArray();
    }
    public byte[]  getProduct(byte[] bytes,String uid){
         MissionData.RequestProduct requestProduct =null;
         MissionData.ResposeProduct.Builder buillder=null;
       try{
           requestProduct=MissionData.RequestProduct.parseFrom(bytes);
       }catch(Exception e){
           e.printStackTrace();
       }
        IMission iMission = MissionDao.getInstance();
       buillder=iMission.getProduct(requestProduct.getItemId(),uid);
        return  buillder.build().toByteArray();
    }

    // //新顶球模式
    // public byte[] beginJumpHeadBallMission(byte[] bytes, String uid) {
    //     MissionData.RequestBeginHeadBallMission requestBeginMission = null;
    //     MissionData.ResponseBeginHeadBallMission.Builder builder = MissionData.ResponseBeginHeadBallMission.newBuilder();
    //     if (uid == null){
    //         builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
    //     }else {
    //         try {
    //             requestBeginMission = MissionData.RequestBeginHeadBallMission.parseFrom(bytes);
    //         } catch (Exception e) {
    //             log.error(e.getMessage(), e);
    //         }
    //         if (requestBeginMission == null) {
    //             builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
    //             log.error(uid+":[beginMission] error 1");
    //         } else {
    //                 ILogin iLogin = LoginDao.getInstance();
    //                 List<String> actionList = iLogin.useAction(uid, SuperConfig.getMissionCostAction());
    //                 if (actionList == null){
    //                     builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
    //                     log.error(uid+":[beginMission] error 3");
    //                 }else {
    //                     builder.setAction(Integer.parseInt(actionList.get(0)));
    //                     builder.setActionStamp(actionList.get(1));
    //                 }
    //             }
    //         }
    //     return builder.build().toByteArray();
    // }

    // public byte[] countJumpHeadBallMission(byte[] bytes, String uid) {
    //     MissionData.RequestCountHeadBall requestCountMission = null;
    //     MissionData.ResponseCountHeadBall.Builder builder = MissionData.ResponseCountHeadBall.newBuilder();
    //     if (uid == null){
    //         builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
    //     }else {
    //         try {
    //             requestCountMission = MissionData.RequestCountHeadBall.parseFrom(bytes);
    //         } catch (Exception e) {
    //             log.error(e.getMessage(), e);
    //         }
    //         if (requestCountMission == null) {
    //             builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
    //             log.error(uid + ":[countMission] error");
    //         } else {
    //             IMission iMission = MissionDao.getInstance();
    //             builder = iMission.countJumpHeadBallMission(requestCountMission.getMissionId(), uid ,requestCountMission.getJumpnum(), requestCountMission.getStarNum(),requestCountMission.getIsSuccess());
    //         }
    //     }
    //     return builder.build().toByteArray();
    // }

    public byte[]  startGameCopy(byte[] inbytes,String uid){
        MissionData.ResposeStartGameCopy.Builder builder=MissionData.ResposeStartGameCopy.newBuilder();
        MissionData.RequestStartGameCopy startGameCopy=null;
        try{
            startGameCopy=MissionData.RequestStartGameCopy.parseFrom(inbytes);
        }catch (InvalidProtocolBufferException e){
            e.printStackTrace();
        }
       IMission missionDao =MissionDao.getInstance();
        builder=missionDao.startGameCopy(uid, startGameCopy.getMissionId());
        return  builder.build().toByteArray();
    }

    public byte[]  countGameCopy(byte[] inbytes,String uid){
        MissionData.ResposeCountGameCopy.Builder builder=MissionData.ResposeCountGameCopy.newBuilder();
        MissionData.RequestCountGameCopy requestCountGameCopy=null;
        try{
            requestCountGameCopy=MissionData.RequestCountGameCopy.parseFrom(inbytes);
        }catch (InvalidProtocolBufferException e){
            e.printStackTrace();
        }
        IMission missionDao =MissionDao.getInstance();
        builder=missionDao.countGameCopy(uid, requestCountGameCopy.getMissionId(),requestCountGameCopy.getIntegral());
        return  builder.build().toByteArray();
    }
}
