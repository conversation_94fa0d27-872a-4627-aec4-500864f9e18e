// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: limitedTimeReward.proto

package protocol;

public final class LimitedTimeRewardData {
  private LimitedTimeRewardData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestLimitedTimeRewardOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.ResponseLimitedTimeReward data = 1;
    /**
     * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
     */
    boolean hasData();
    /**
     * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
     */
    protocol.LimitedTimeRewardData.ResponseLimitedTimeReward getData();
    /**
     * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
     */
    protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder getDataOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.RequestLimitedTimeReward}
   *
   * <pre>
   * 1350
   * </pre>
   */
  public static final class RequestLimitedTimeReward extends
      com.google.protobuf.GeneratedMessage
      implements RequestLimitedTimeRewardOrBuilder {
    // Use RequestLimitedTimeReward.newBuilder() to construct.
    private RequestLimitedTimeReward(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestLimitedTimeReward(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestLimitedTimeReward defaultInstance;
    public static RequestLimitedTimeReward getDefaultInstance() {
      return defaultInstance;
    }

    public RequestLimitedTimeReward getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestLimitedTimeReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = data_.toBuilder();
              }
              data_ = input.readMessage(protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(data_);
                data_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeReward_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.RequestLimitedTimeReward.class, protocol.LimitedTimeRewardData.RequestLimitedTimeReward.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestLimitedTimeReward> PARSER =
        new com.google.protobuf.AbstractParser<RequestLimitedTimeReward>() {
      public RequestLimitedTimeReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestLimitedTimeReward(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestLimitedTimeReward> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.ResponseLimitedTimeReward data = 1;
    public static final int DATA_FIELD_NUMBER = 1;
    private protocol.LimitedTimeRewardData.ResponseLimitedTimeReward data_;
    /**
     * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
     */
    public boolean hasData() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
     */
    public protocol.LimitedTimeRewardData.ResponseLimitedTimeReward getData() {
      return data_;
    }
    /**
     * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
     */
    public protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder getDataOrBuilder() {
      return data_;
    }

    private void initFields() {
      data_ = protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasData()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getData().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, data_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, data_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.RequestLimitedTimeReward prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestLimitedTimeReward}
     *
     * <pre>
     * 1350
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.RequestLimitedTimeRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeReward_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.RequestLimitedTimeReward.class, protocol.LimitedTimeRewardData.RequestLimitedTimeReward.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.RequestLimitedTimeReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getDataFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (dataBuilder_ == null) {
          data_ = protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance();
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeReward_descriptor;
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeReward getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.RequestLimitedTimeReward.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeReward build() {
        protocol.LimitedTimeRewardData.RequestLimitedTimeReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeReward buildPartial() {
        protocol.LimitedTimeRewardData.RequestLimitedTimeReward result = new protocol.LimitedTimeRewardData.RequestLimitedTimeReward(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (dataBuilder_ == null) {
          result.data_ = data_;
        } else {
          result.data_ = dataBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.RequestLimitedTimeReward) {
          return mergeFrom((protocol.LimitedTimeRewardData.RequestLimitedTimeReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.RequestLimitedTimeReward other) {
        if (other == protocol.LimitedTimeRewardData.RequestLimitedTimeReward.getDefaultInstance()) return this;
        if (other.hasData()) {
          mergeData(other.getData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasData()) {
          
          return false;
        }
        if (!getData().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.RequestLimitedTimeReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.RequestLimitedTimeReward) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.ResponseLimitedTimeReward data = 1;
      private protocol.LimitedTimeRewardData.ResponseLimitedTimeReward data_ = protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.LimitedTimeRewardData.ResponseLimitedTimeReward, protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder, protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder> dataBuilder_;
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public boolean hasData() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public protocol.LimitedTimeRewardData.ResponseLimitedTimeReward getData() {
        if (dataBuilder_ == null) {
          return data_;
        } else {
          return dataBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public Builder setData(protocol.LimitedTimeRewardData.ResponseLimitedTimeReward value) {
        if (dataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          data_ = value;
          onChanged();
        } else {
          dataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public Builder setData(
          protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder builderForValue) {
        if (dataBuilder_ == null) {
          data_ = builderForValue.build();
          onChanged();
        } else {
          dataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public Builder mergeData(protocol.LimitedTimeRewardData.ResponseLimitedTimeReward value) {
        if (dataBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              data_ != protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance()) {
            data_ =
              protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.newBuilder(data_).mergeFrom(value).buildPartial();
          } else {
            data_ = value;
          }
          onChanged();
        } else {
          dataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public Builder clearData() {
        if (dataBuilder_ == null) {
          data_ = protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance();
          onChanged();
        } else {
          dataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder getDataBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDataFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      public protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder getDataOrBuilder() {
        if (dataBuilder_ != null) {
          return dataBuilder_.getMessageOrBuilder();
        } else {
          return data_;
        }
      }
      /**
       * <code>required .protocol.ResponseLimitedTimeReward data = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.LimitedTimeRewardData.ResponseLimitedTimeReward, protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder, protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder> 
          getDataFieldBuilder() {
        if (dataBuilder_ == null) {
          dataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.LimitedTimeRewardData.ResponseLimitedTimeReward, protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder, protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder>(
                  data_,
                  getParentForChildren(),
                  isClean());
          data_ = null;
        }
        return dataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestLimitedTimeReward)
    }

    static {
      defaultInstance = new RequestLimitedTimeReward(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestLimitedTimeReward)
  }

  public interface ResponseLimitedTimeRewardOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool charge_reward = 1;
    /**
     * <code>required bool charge_reward = 1;</code>
     */
    boolean hasChargeReward();
    /**
     * <code>required bool charge_reward = 1;</code>
     */
    boolean getChargeReward();

    // required int32 exp = 2;
    /**
     * <code>required int32 exp = 2;</code>
     */
    boolean hasExp();
    /**
     * <code>required int32 exp = 2;</code>
     */
    int getExp();

    // required int32 ordinary_reward_id = 3;
    /**
     * <code>required int32 ordinary_reward_id = 3;</code>
     */
    boolean hasOrdinaryRewardId();
    /**
     * <code>required int32 ordinary_reward_id = 3;</code>
     */
    int getOrdinaryRewardId();

    // required int32 charge_reward_id = 4;
    /**
     * <code>required int32 charge_reward_id = 4;</code>
     */
    boolean hasChargeRewardId();
    /**
     * <code>required int32 charge_reward_id = 4;</code>
     */
    int getChargeRewardId();
  }
  /**
   * Protobuf type {@code protocol.ResponseLimitedTimeReward}
   *
   * <pre>
   * 2350 返回显示奖励的信息
   * </pre>
   */
  public static final class ResponseLimitedTimeReward extends
      com.google.protobuf.GeneratedMessage
      implements ResponseLimitedTimeRewardOrBuilder {
    // Use ResponseLimitedTimeReward.newBuilder() to construct.
    private ResponseLimitedTimeReward(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseLimitedTimeReward(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseLimitedTimeReward defaultInstance;
    public static ResponseLimitedTimeReward getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseLimitedTimeReward getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseLimitedTimeReward(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              chargeReward_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              exp_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              ordinaryRewardId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              chargeRewardId_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeReward_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeReward_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.class, protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseLimitedTimeReward> PARSER =
        new com.google.protobuf.AbstractParser<ResponseLimitedTimeReward>() {
      public ResponseLimitedTimeReward parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseLimitedTimeReward(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseLimitedTimeReward> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool charge_reward = 1;
    public static final int CHARGE_REWARD_FIELD_NUMBER = 1;
    private boolean chargeReward_;
    /**
     * <code>required bool charge_reward = 1;</code>
     */
    public boolean hasChargeReward() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool charge_reward = 1;</code>
     */
    public boolean getChargeReward() {
      return chargeReward_;
    }

    // required int32 exp = 2;
    public static final int EXP_FIELD_NUMBER = 2;
    private int exp_;
    /**
     * <code>required int32 exp = 2;</code>
     */
    public boolean hasExp() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 exp = 2;</code>
     */
    public int getExp() {
      return exp_;
    }

    // required int32 ordinary_reward_id = 3;
    public static final int ORDINARY_REWARD_ID_FIELD_NUMBER = 3;
    private int ordinaryRewardId_;
    /**
     * <code>required int32 ordinary_reward_id = 3;</code>
     */
    public boolean hasOrdinaryRewardId() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 ordinary_reward_id = 3;</code>
     */
    public int getOrdinaryRewardId() {
      return ordinaryRewardId_;
    }

    // required int32 charge_reward_id = 4;
    public static final int CHARGE_REWARD_ID_FIELD_NUMBER = 4;
    private int chargeRewardId_;
    /**
     * <code>required int32 charge_reward_id = 4;</code>
     */
    public boolean hasChargeRewardId() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 charge_reward_id = 4;</code>
     */
    public int getChargeRewardId() {
      return chargeRewardId_;
    }

    private void initFields() {
      chargeReward_ = false;
      exp_ = 0;
      ordinaryRewardId_ = 0;
      chargeRewardId_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasChargeReward()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasExp()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasOrdinaryRewardId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasChargeRewardId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, chargeReward_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, exp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, ordinaryRewardId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, chargeRewardId_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, chargeReward_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, exp_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, ordinaryRewardId_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, chargeRewardId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.ResponseLimitedTimeReward prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseLimitedTimeReward}
     *
     * <pre>
     * 2350 返回显示奖励的信息
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeReward_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeReward_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.class, protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        chargeReward_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        exp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        ordinaryRewardId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        chargeRewardId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeReward_descriptor;
      }

      public protocol.LimitedTimeRewardData.ResponseLimitedTimeReward getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.ResponseLimitedTimeReward build() {
        protocol.LimitedTimeRewardData.ResponseLimitedTimeReward result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.ResponseLimitedTimeReward buildPartial() {
        protocol.LimitedTimeRewardData.ResponseLimitedTimeReward result = new protocol.LimitedTimeRewardData.ResponseLimitedTimeReward(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.chargeReward_ = chargeReward_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.exp_ = exp_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.ordinaryRewardId_ = ordinaryRewardId_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.chargeRewardId_ = chargeRewardId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.ResponseLimitedTimeReward) {
          return mergeFrom((protocol.LimitedTimeRewardData.ResponseLimitedTimeReward)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.ResponseLimitedTimeReward other) {
        if (other == protocol.LimitedTimeRewardData.ResponseLimitedTimeReward.getDefaultInstance()) return this;
        if (other.hasChargeReward()) {
          setChargeReward(other.getChargeReward());
        }
        if (other.hasExp()) {
          setExp(other.getExp());
        }
        if (other.hasOrdinaryRewardId()) {
          setOrdinaryRewardId(other.getOrdinaryRewardId());
        }
        if (other.hasChargeRewardId()) {
          setChargeRewardId(other.getChargeRewardId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasChargeReward()) {
          
          return false;
        }
        if (!hasExp()) {
          
          return false;
        }
        if (!hasOrdinaryRewardId()) {
          
          return false;
        }
        if (!hasChargeRewardId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.ResponseLimitedTimeReward parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.ResponseLimitedTimeReward) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool charge_reward = 1;
      private boolean chargeReward_ ;
      /**
       * <code>required bool charge_reward = 1;</code>
       */
      public boolean hasChargeReward() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool charge_reward = 1;</code>
       */
      public boolean getChargeReward() {
        return chargeReward_;
      }
      /**
       * <code>required bool charge_reward = 1;</code>
       */
      public Builder setChargeReward(boolean value) {
        bitField0_ |= 0x00000001;
        chargeReward_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool charge_reward = 1;</code>
       */
      public Builder clearChargeReward() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeReward_ = false;
        onChanged();
        return this;
      }

      // required int32 exp = 2;
      private int exp_ ;
      /**
       * <code>required int32 exp = 2;</code>
       */
      public boolean hasExp() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 exp = 2;</code>
       */
      public int getExp() {
        return exp_;
      }
      /**
       * <code>required int32 exp = 2;</code>
       */
      public Builder setExp(int value) {
        bitField0_ |= 0x00000002;
        exp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 exp = 2;</code>
       */
      public Builder clearExp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        exp_ = 0;
        onChanged();
        return this;
      }

      // required int32 ordinary_reward_id = 3;
      private int ordinaryRewardId_ ;
      /**
       * <code>required int32 ordinary_reward_id = 3;</code>
       */
      public boolean hasOrdinaryRewardId() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 ordinary_reward_id = 3;</code>
       */
      public int getOrdinaryRewardId() {
        return ordinaryRewardId_;
      }
      /**
       * <code>required int32 ordinary_reward_id = 3;</code>
       */
      public Builder setOrdinaryRewardId(int value) {
        bitField0_ |= 0x00000004;
        ordinaryRewardId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 ordinary_reward_id = 3;</code>
       */
      public Builder clearOrdinaryRewardId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ordinaryRewardId_ = 0;
        onChanged();
        return this;
      }

      // required int32 charge_reward_id = 4;
      private int chargeRewardId_ ;
      /**
       * <code>required int32 charge_reward_id = 4;</code>
       */
      public boolean hasChargeRewardId() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 charge_reward_id = 4;</code>
       */
      public int getChargeRewardId() {
        return chargeRewardId_;
      }
      /**
       * <code>required int32 charge_reward_id = 4;</code>
       */
      public Builder setChargeRewardId(int value) {
        bitField0_ |= 0x00000008;
        chargeRewardId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 charge_reward_id = 4;</code>
       */
      public Builder clearChargeRewardId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        chargeRewardId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseLimitedTimeReward)
    }

    static {
      defaultInstance = new ResponseLimitedTimeReward(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseLimitedTimeReward)
  }

  public interface RequestLimitedTimeRewardTypeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    int getType();

    // required int32 num = 2;
    /**
     * <code>required int32 num = 2;</code>
     *
     * <pre>
     *数量
     * </pre>
     */
    boolean hasNum();
    /**
     * <code>required int32 num = 2;</code>
     *
     * <pre>
     *数量
     * </pre>
     */
    int getNum();
  }
  /**
   * Protobuf type {@code protocol.RequestLimitedTimeRewardType}
   *
   * <pre>
   *1351
   * </pre>
   */
  public static final class RequestLimitedTimeRewardType extends
      com.google.protobuf.GeneratedMessage
      implements RequestLimitedTimeRewardTypeOrBuilder {
    // Use RequestLimitedTimeRewardType.newBuilder() to construct.
    private RequestLimitedTimeRewardType(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestLimitedTimeRewardType(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestLimitedTimeRewardType defaultInstance;
    public static RequestLimitedTimeRewardType getDefaultInstance() {
      return defaultInstance;
    }

    public RequestLimitedTimeRewardType getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestLimitedTimeRewardType(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardType_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardType_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.class, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestLimitedTimeRewardType> PARSER =
        new com.google.protobuf.AbstractParser<RequestLimitedTimeRewardType>() {
      public RequestLimitedTimeRewardType parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestLimitedTimeRewardType(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestLimitedTimeRewardType> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *类型
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // required int32 num = 2;
    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <code>required int32 num = 2;</code>
     *
     * <pre>
     *数量
     * </pre>
     */
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 num = 2;</code>
     *
     * <pre>
     *数量
     * </pre>
     */
    public int getNum() {
      return num_;
    }

    private void initFields() {
      type_ = 0;
      num_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasNum()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, num_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestLimitedTimeRewardType}
     *
     * <pre>
     *1351
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardType_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardType_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.class, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardType_descriptor;
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType build() {
        protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType buildPartial() {
        protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType result = new protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.num_ = num_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType) {
          return mergeFrom((protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType other) {
        if (other == protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (!hasNum()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *类型
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // required int32 num = 2;
      private int num_ ;
      /**
       * <code>required int32 num = 2;</code>
       *
       * <pre>
       *数量
       * </pre>
       */
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 num = 2;</code>
       *
       * <pre>
       *数量
       * </pre>
       */
      public int getNum() {
        return num_;
      }
      /**
       * <code>required int32 num = 2;</code>
       *
       * <pre>
       *数量
       * </pre>
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 num = 2;</code>
       *
       * <pre>
       *数量
       * </pre>
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestLimitedTimeRewardType)
    }

    static {
      defaultInstance = new RequestLimitedTimeRewardType(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestLimitedTimeRewardType)
  }

  public interface RequestLimitedTimeRewardFinishOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 id = 1;
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *id
     * </pre>
     */
    boolean hasId();
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *id
     * </pre>
     */
    int getId();

    // required bool isComplete = 2;
    /**
     * <code>required bool isComplete = 2;</code>
     *
     * <pre>
     *完成
     * </pre>
     */
    boolean hasIsComplete();
    /**
     * <code>required bool isComplete = 2;</code>
     *
     * <pre>
     *完成
     * </pre>
     */
    boolean getIsComplete();
  }
  /**
   * Protobuf type {@code protocol.RequestLimitedTimeRewardFinish}
   *
   * <pre>
   *1352
   * </pre>
   */
  public static final class RequestLimitedTimeRewardFinish extends
      com.google.protobuf.GeneratedMessage
      implements RequestLimitedTimeRewardFinishOrBuilder {
    // Use RequestLimitedTimeRewardFinish.newBuilder() to construct.
    private RequestLimitedTimeRewardFinish(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestLimitedTimeRewardFinish(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestLimitedTimeRewardFinish defaultInstance;
    public static RequestLimitedTimeRewardFinish getDefaultInstance() {
      return defaultInstance;
    }

    public RequestLimitedTimeRewardFinish getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestLimitedTimeRewardFinish(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              isComplete_ = input.readBool();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardFinish_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardFinish_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.class, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestLimitedTimeRewardFinish> PARSER =
        new com.google.protobuf.AbstractParser<RequestLimitedTimeRewardFinish>() {
      public RequestLimitedTimeRewardFinish parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestLimitedTimeRewardFinish(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestLimitedTimeRewardFinish> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *id
     * </pre>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 id = 1;</code>
     *
     * <pre>
     *id
     * </pre>
     */
    public int getId() {
      return id_;
    }

    // required bool isComplete = 2;
    public static final int ISCOMPLETE_FIELD_NUMBER = 2;
    private boolean isComplete_;
    /**
     * <code>required bool isComplete = 2;</code>
     *
     * <pre>
     *完成
     * </pre>
     */
    public boolean hasIsComplete() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required bool isComplete = 2;</code>
     *
     * <pre>
     *完成
     * </pre>
     */
    public boolean getIsComplete() {
      return isComplete_;
    }

    private void initFields() {
      id_ = 0;
      isComplete_ = false;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasIsComplete()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBool(2, isComplete_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isComplete_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestLimitedTimeRewardFinish}
     *
     * <pre>
     *1352
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardFinish_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardFinish_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.class, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        isComplete_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestLimitedTimeRewardFinish_descriptor;
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish build() {
        protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish buildPartial() {
        protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish result = new protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.isComplete_ = isComplete_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish) {
          return mergeFrom((protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish other) {
        if (other == protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasIsComplete()) {
          setIsComplete(other.getIsComplete());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasId()) {
          
          return false;
        }
        if (!hasIsComplete()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 id = 1;
      private int id_ ;
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *id
       * </pre>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *id
       * </pre>
       */
      public int getId() {
        return id_;
      }
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *id
       * </pre>
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 id = 1;</code>
       *
       * <pre>
       *id
       * </pre>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      // required bool isComplete = 2;
      private boolean isComplete_ ;
      /**
       * <code>required bool isComplete = 2;</code>
       *
       * <pre>
       *完成
       * </pre>
       */
      public boolean hasIsComplete() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required bool isComplete = 2;</code>
       *
       * <pre>
       *完成
       * </pre>
       */
      public boolean getIsComplete() {
        return isComplete_;
      }
      /**
       * <code>required bool isComplete = 2;</code>
       *
       * <pre>
       *完成
       * </pre>
       */
      public Builder setIsComplete(boolean value) {
        bitField0_ |= 0x00000002;
        isComplete_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool isComplete = 2;</code>
       *
       * <pre>
       *完成
       * </pre>
       */
      public Builder clearIsComplete() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isComplete_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestLimitedTimeRewardFinish)
    }

    static {
      defaultInstance = new RequestLimitedTimeRewardFinish(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestLimitedTimeRewardFinish)
  }

  public interface ResponseLimitedTimeRewardTaskDataOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.RequestLimitedTimeRewardType task_type = 1;
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType> 
        getTaskTypeList();
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType getTaskType(int index);
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    int getTaskTypeCount();
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    java.util.List<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder> 
        getTaskTypeOrBuilderList();
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder getTaskTypeOrBuilder(
        int index);

    // repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish> 
        getTaskStateList();
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish getTaskState(int index);
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    int getTaskStateCount();
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    java.util.List<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder> 
        getTaskStateOrBuilderList();
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder getTaskStateOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseLimitedTimeRewardTaskData}
   *
   * <pre>
   * 2353 返回全部数据
   * </pre>
   */
  public static final class ResponseLimitedTimeRewardTaskData extends
      com.google.protobuf.GeneratedMessage
      implements ResponseLimitedTimeRewardTaskDataOrBuilder {
    // Use ResponseLimitedTimeRewardTaskData.newBuilder() to construct.
    private ResponseLimitedTimeRewardTaskData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseLimitedTimeRewardTaskData(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseLimitedTimeRewardTaskData defaultInstance;
    public static ResponseLimitedTimeRewardTaskData getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseLimitedTimeRewardTaskData getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseLimitedTimeRewardTaskData(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                taskType_ = new java.util.ArrayList<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType>();
                mutable_bitField0_ |= 0x00000001;
              }
              taskType_.add(input.readMessage(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.PARSER, extensionRegistry));
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                taskState_ = new java.util.ArrayList<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish>();
                mutable_bitField0_ |= 0x00000002;
              }
              taskState_.add(input.readMessage(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          taskType_ = java.util.Collections.unmodifiableList(taskType_);
        }
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          taskState_ = java.util.Collections.unmodifiableList(taskState_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeRewardTaskData_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeRewardTaskData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.class, protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseLimitedTimeRewardTaskData> PARSER =
        new com.google.protobuf.AbstractParser<ResponseLimitedTimeRewardTaskData>() {
      public ResponseLimitedTimeRewardTaskData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseLimitedTimeRewardTaskData(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseLimitedTimeRewardTaskData> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.RequestLimitedTimeRewardType task_type = 1;
    public static final int TASK_TYPE_FIELD_NUMBER = 1;
    private java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType> taskType_;
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    public java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType> getTaskTypeList() {
      return taskType_;
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    public java.util.List<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder> 
        getTaskTypeOrBuilderList() {
      return taskType_;
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    public int getTaskTypeCount() {
      return taskType_.size();
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType getTaskType(int index) {
      return taskType_.get(index);
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
     */
    public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder getTaskTypeOrBuilder(
        int index) {
      return taskType_.get(index);
    }

    // repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;
    public static final int TASK_STATE_FIELD_NUMBER = 2;
    private java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish> taskState_;
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    public java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish> getTaskStateList() {
      return taskState_;
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    public java.util.List<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder> 
        getTaskStateOrBuilderList() {
      return taskState_;
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    public int getTaskStateCount() {
      return taskState_.size();
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish getTaskState(int index) {
      return taskState_.get(index);
    }
    /**
     * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
     */
    public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder getTaskStateOrBuilder(
        int index) {
      return taskState_.get(index);
    }

    private void initFields() {
      taskType_ = java.util.Collections.emptyList();
      taskState_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getTaskTypeCount(); i++) {
        if (!getTaskType(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getTaskStateCount(); i++) {
        if (!getTaskState(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < taskType_.size(); i++) {
        output.writeMessage(1, taskType_.get(i));
      }
      for (int i = 0; i < taskState_.size(); i++) {
        output.writeMessage(2, taskState_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < taskType_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, taskType_.get(i));
      }
      for (int i = 0; i < taskState_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, taskState_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseLimitedTimeRewardTaskData}
     *
     * <pre>
     * 2353 返回全部数据
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeRewardTaskData_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeRewardTaskData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.class, protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getTaskTypeFieldBuilder();
          getTaskStateFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (taskTypeBuilder_ == null) {
          taskType_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          taskTypeBuilder_.clear();
        }
        if (taskStateBuilder_ == null) {
          taskState_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          taskStateBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseLimitedTimeRewardTaskData_descriptor;
      }

      public protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData build() {
        protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData buildPartial() {
        protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData result = new protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData(this);
        int from_bitField0_ = bitField0_;
        if (taskTypeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            taskType_ = java.util.Collections.unmodifiableList(taskType_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.taskType_ = taskType_;
        } else {
          result.taskType_ = taskTypeBuilder_.build();
        }
        if (taskStateBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            taskState_ = java.util.Collections.unmodifiableList(taskState_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.taskState_ = taskState_;
        } else {
          result.taskState_ = taskStateBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData) {
          return mergeFrom((protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData other) {
        if (other == protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.getDefaultInstance()) return this;
        if (taskTypeBuilder_ == null) {
          if (!other.taskType_.isEmpty()) {
            if (taskType_.isEmpty()) {
              taskType_ = other.taskType_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTaskTypeIsMutable();
              taskType_.addAll(other.taskType_);
            }
            onChanged();
          }
        } else {
          if (!other.taskType_.isEmpty()) {
            if (taskTypeBuilder_.isEmpty()) {
              taskTypeBuilder_.dispose();
              taskTypeBuilder_ = null;
              taskType_ = other.taskType_;
              bitField0_ = (bitField0_ & ~0x00000001);
              taskTypeBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getTaskTypeFieldBuilder() : null;
            } else {
              taskTypeBuilder_.addAllMessages(other.taskType_);
            }
          }
        }
        if (taskStateBuilder_ == null) {
          if (!other.taskState_.isEmpty()) {
            if (taskState_.isEmpty()) {
              taskState_ = other.taskState_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTaskStateIsMutable();
              taskState_.addAll(other.taskState_);
            }
            onChanged();
          }
        } else {
          if (!other.taskState_.isEmpty()) {
            if (taskStateBuilder_.isEmpty()) {
              taskStateBuilder_.dispose();
              taskStateBuilder_ = null;
              taskState_ = other.taskState_;
              bitField0_ = (bitField0_ & ~0x00000002);
              taskStateBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getTaskStateFieldBuilder() : null;
            } else {
              taskStateBuilder_.addAllMessages(other.taskState_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getTaskTypeCount(); i++) {
          if (!getTaskType(i).isInitialized()) {
            
            return false;
          }
        }
        for (int i = 0; i < getTaskStateCount(); i++) {
          if (!getTaskState(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.RequestLimitedTimeRewardType task_type = 1;
      private java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType> taskType_ =
        java.util.Collections.emptyList();
      private void ensureTaskTypeIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          taskType_ = new java.util.ArrayList<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType>(taskType_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder> taskTypeBuilder_;

      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType> getTaskTypeList() {
        if (taskTypeBuilder_ == null) {
          return java.util.Collections.unmodifiableList(taskType_);
        } else {
          return taskTypeBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public int getTaskTypeCount() {
        if (taskTypeBuilder_ == null) {
          return taskType_.size();
        } else {
          return taskTypeBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType getTaskType(int index) {
        if (taskTypeBuilder_ == null) {
          return taskType_.get(index);
        } else {
          return taskTypeBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder setTaskType(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType value) {
        if (taskTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskTypeIsMutable();
          taskType_.set(index, value);
          onChanged();
        } else {
          taskTypeBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder setTaskType(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder builderForValue) {
        if (taskTypeBuilder_ == null) {
          ensureTaskTypeIsMutable();
          taskType_.set(index, builderForValue.build());
          onChanged();
        } else {
          taskTypeBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder addTaskType(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType value) {
        if (taskTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskTypeIsMutable();
          taskType_.add(value);
          onChanged();
        } else {
          taskTypeBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder addTaskType(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType value) {
        if (taskTypeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskTypeIsMutable();
          taskType_.add(index, value);
          onChanged();
        } else {
          taskTypeBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder addTaskType(
          protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder builderForValue) {
        if (taskTypeBuilder_ == null) {
          ensureTaskTypeIsMutable();
          taskType_.add(builderForValue.build());
          onChanged();
        } else {
          taskTypeBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder addTaskType(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder builderForValue) {
        if (taskTypeBuilder_ == null) {
          ensureTaskTypeIsMutable();
          taskType_.add(index, builderForValue.build());
          onChanged();
        } else {
          taskTypeBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder addAllTaskType(
          java.lang.Iterable<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType> values) {
        if (taskTypeBuilder_ == null) {
          ensureTaskTypeIsMutable();
          super.addAll(values, taskType_);
          onChanged();
        } else {
          taskTypeBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder clearTaskType() {
        if (taskTypeBuilder_ == null) {
          taskType_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          taskTypeBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public Builder removeTaskType(int index) {
        if (taskTypeBuilder_ == null) {
          ensureTaskTypeIsMutable();
          taskType_.remove(index);
          onChanged();
        } else {
          taskTypeBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder getTaskTypeBuilder(
          int index) {
        return getTaskTypeFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder getTaskTypeOrBuilder(
          int index) {
        if (taskTypeBuilder_ == null) {
          return taskType_.get(index);  } else {
          return taskTypeBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public java.util.List<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder> 
           getTaskTypeOrBuilderList() {
        if (taskTypeBuilder_ != null) {
          return taskTypeBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(taskType_);
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder addTaskTypeBuilder() {
        return getTaskTypeFieldBuilder().addBuilder(
            protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder addTaskTypeBuilder(
          int index) {
        return getTaskTypeFieldBuilder().addBuilder(
            index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardType task_type = 1;</code>
       */
      public java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder> 
           getTaskTypeBuilderList() {
        return getTaskTypeFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder> 
          getTaskTypeFieldBuilder() {
        if (taskTypeBuilder_ == null) {
          taskTypeBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardType.Builder, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardTypeOrBuilder>(
                  taskType_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          taskType_ = null;
        }
        return taskTypeBuilder_;
      }

      // repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;
      private java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish> taskState_ =
        java.util.Collections.emptyList();
      private void ensureTaskStateIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          taskState_ = new java.util.ArrayList<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish>(taskState_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder> taskStateBuilder_;

      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish> getTaskStateList() {
        if (taskStateBuilder_ == null) {
          return java.util.Collections.unmodifiableList(taskState_);
        } else {
          return taskStateBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public int getTaskStateCount() {
        if (taskStateBuilder_ == null) {
          return taskState_.size();
        } else {
          return taskStateBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish getTaskState(int index) {
        if (taskStateBuilder_ == null) {
          return taskState_.get(index);
        } else {
          return taskStateBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder setTaskState(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish value) {
        if (taskStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskStateIsMutable();
          taskState_.set(index, value);
          onChanged();
        } else {
          taskStateBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder setTaskState(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder builderForValue) {
        if (taskStateBuilder_ == null) {
          ensureTaskStateIsMutable();
          taskState_.set(index, builderForValue.build());
          onChanged();
        } else {
          taskStateBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder addTaskState(protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish value) {
        if (taskStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskStateIsMutable();
          taskState_.add(value);
          onChanged();
        } else {
          taskStateBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder addTaskState(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish value) {
        if (taskStateBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskStateIsMutable();
          taskState_.add(index, value);
          onChanged();
        } else {
          taskStateBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder addTaskState(
          protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder builderForValue) {
        if (taskStateBuilder_ == null) {
          ensureTaskStateIsMutable();
          taskState_.add(builderForValue.build());
          onChanged();
        } else {
          taskStateBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder addTaskState(
          int index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder builderForValue) {
        if (taskStateBuilder_ == null) {
          ensureTaskStateIsMutable();
          taskState_.add(index, builderForValue.build());
          onChanged();
        } else {
          taskStateBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder addAllTaskState(
          java.lang.Iterable<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish> values) {
        if (taskStateBuilder_ == null) {
          ensureTaskStateIsMutable();
          super.addAll(values, taskState_);
          onChanged();
        } else {
          taskStateBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder clearTaskState() {
        if (taskStateBuilder_ == null) {
          taskState_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          taskStateBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public Builder removeTaskState(int index) {
        if (taskStateBuilder_ == null) {
          ensureTaskStateIsMutable();
          taskState_.remove(index);
          onChanged();
        } else {
          taskStateBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder getTaskStateBuilder(
          int index) {
        return getTaskStateFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder getTaskStateOrBuilder(
          int index) {
        if (taskStateBuilder_ == null) {
          return taskState_.get(index);  } else {
          return taskStateBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public java.util.List<? extends protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder> 
           getTaskStateOrBuilderList() {
        if (taskStateBuilder_ != null) {
          return taskStateBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(taskState_);
        }
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder addTaskStateBuilder() {
        return getTaskStateFieldBuilder().addBuilder(
            protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder addTaskStateBuilder(
          int index) {
        return getTaskStateFieldBuilder().addBuilder(
            index, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.RequestLimitedTimeRewardFinish task_state = 2;</code>
       */
      public java.util.List<protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder> 
           getTaskStateBuilderList() {
        return getTaskStateFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder> 
          getTaskStateFieldBuilder() {
        if (taskStateBuilder_ == null) {
          taskStateBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinish.Builder, protocol.LimitedTimeRewardData.RequestLimitedTimeRewardFinishOrBuilder>(
                  taskState_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          taskState_ = null;
        }
        return taskStateBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseLimitedTimeRewardTaskData)
    }

    static {
      defaultInstance = new ResponseLimitedTimeRewardTaskData(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseLimitedTimeRewardTaskData)
  }

  public interface RequestRemainTimeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestRemainTime}
   *
   * <pre>
   * 1354 获取剩余时间
   * </pre>
   */
  public static final class RequestRemainTime extends
      com.google.protobuf.GeneratedMessage
      implements RequestRemainTimeOrBuilder {
    // Use RequestRemainTime.newBuilder() to construct.
    private RequestRemainTime(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestRemainTime(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestRemainTime defaultInstance;
    public static RequestRemainTime getDefaultInstance() {
      return defaultInstance;
    }

    public RequestRemainTime getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestRemainTime(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestRemainTime_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_RequestRemainTime_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.RequestRemainTime.class, protocol.LimitedTimeRewardData.RequestRemainTime.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestRemainTime> PARSER =
        new com.google.protobuf.AbstractParser<RequestRemainTime>() {
      public RequestRemainTime parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestRemainTime(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestRemainTime> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.RequestRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.RequestRemainTime prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestRemainTime}
     *
     * <pre>
     * 1354 获取剩余时间
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.RequestRemainTimeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestRemainTime_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestRemainTime_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.RequestRemainTime.class, protocol.LimitedTimeRewardData.RequestRemainTime.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.RequestRemainTime.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_RequestRemainTime_descriptor;
      }

      public protocol.LimitedTimeRewardData.RequestRemainTime getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.RequestRemainTime.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.RequestRemainTime build() {
        protocol.LimitedTimeRewardData.RequestRemainTime result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.RequestRemainTime buildPartial() {
        protocol.LimitedTimeRewardData.RequestRemainTime result = new protocol.LimitedTimeRewardData.RequestRemainTime(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.RequestRemainTime) {
          return mergeFrom((protocol.LimitedTimeRewardData.RequestRemainTime)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.RequestRemainTime other) {
        if (other == protocol.LimitedTimeRewardData.RequestRemainTime.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.RequestRemainTime parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.RequestRemainTime) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestRemainTime)
    }

    static {
      defaultInstance = new RequestRemainTime(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestRemainTime)
  }

  public interface ResponseRemainTimeOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int64 time_second = 1;
    /**
     * <code>required int64 time_second = 1;</code>
     */
    boolean hasTimeSecond();
    /**
     * <code>required int64 time_second = 1;</code>
     */
    long getTimeSecond();
  }
  /**
   * Protobuf type {@code protocol.ResponseRemainTime}
   *
   * <pre>
   * 2354 
   * </pre>
   */
  public static final class ResponseRemainTime extends
      com.google.protobuf.GeneratedMessage
      implements ResponseRemainTimeOrBuilder {
    // Use ResponseRemainTime.newBuilder() to construct.
    private ResponseRemainTime(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseRemainTime(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseRemainTime defaultInstance;
    public static ResponseRemainTime getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseRemainTime getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseRemainTime(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              timeSecond_ = input.readInt64();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseRemainTime_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseRemainTime_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.LimitedTimeRewardData.ResponseRemainTime.class, protocol.LimitedTimeRewardData.ResponseRemainTime.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseRemainTime> PARSER =
        new com.google.protobuf.AbstractParser<ResponseRemainTime>() {
      public ResponseRemainTime parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseRemainTime(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseRemainTime> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int64 time_second = 1;
    public static final int TIME_SECOND_FIELD_NUMBER = 1;
    private long timeSecond_;
    /**
     * <code>required int64 time_second = 1;</code>
     */
    public boolean hasTimeSecond() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int64 time_second = 1;</code>
     */
    public long getTimeSecond() {
      return timeSecond_;
    }

    private void initFields() {
      timeSecond_ = 0L;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasTimeSecond()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt64(1, timeSecond_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, timeSecond_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.LimitedTimeRewardData.ResponseRemainTime parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.LimitedTimeRewardData.ResponseRemainTime prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseRemainTime}
     *
     * <pre>
     * 2354 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.LimitedTimeRewardData.ResponseRemainTimeOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseRemainTime_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseRemainTime_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.LimitedTimeRewardData.ResponseRemainTime.class, protocol.LimitedTimeRewardData.ResponseRemainTime.Builder.class);
      }

      // Construct using protocol.LimitedTimeRewardData.ResponseRemainTime.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        timeSecond_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.LimitedTimeRewardData.internal_static_protocol_ResponseRemainTime_descriptor;
      }

      public protocol.LimitedTimeRewardData.ResponseRemainTime getDefaultInstanceForType() {
        return protocol.LimitedTimeRewardData.ResponseRemainTime.getDefaultInstance();
      }

      public protocol.LimitedTimeRewardData.ResponseRemainTime build() {
        protocol.LimitedTimeRewardData.ResponseRemainTime result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.LimitedTimeRewardData.ResponseRemainTime buildPartial() {
        protocol.LimitedTimeRewardData.ResponseRemainTime result = new protocol.LimitedTimeRewardData.ResponseRemainTime(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.timeSecond_ = timeSecond_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.LimitedTimeRewardData.ResponseRemainTime) {
          return mergeFrom((protocol.LimitedTimeRewardData.ResponseRemainTime)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.LimitedTimeRewardData.ResponseRemainTime other) {
        if (other == protocol.LimitedTimeRewardData.ResponseRemainTime.getDefaultInstance()) return this;
        if (other.hasTimeSecond()) {
          setTimeSecond(other.getTimeSecond());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasTimeSecond()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.LimitedTimeRewardData.ResponseRemainTime parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.LimitedTimeRewardData.ResponseRemainTime) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int64 time_second = 1;
      private long timeSecond_ ;
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public boolean hasTimeSecond() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public long getTimeSecond() {
        return timeSecond_;
      }
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public Builder setTimeSecond(long value) {
        bitField0_ |= 0x00000001;
        timeSecond_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int64 time_second = 1;</code>
       */
      public Builder clearTimeSecond() {
        bitField0_ = (bitField0_ & ~0x00000001);
        timeSecond_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseRemainTime)
    }

    static {
      defaultInstance = new ResponseRemainTime(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseRemainTime)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestLimitedTimeReward_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestLimitedTimeReward_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseLimitedTimeReward_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseLimitedTimeReward_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestLimitedTimeRewardType_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestLimitedTimeRewardType_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestLimitedTimeRewardFinish_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestLimitedTimeRewardFinish_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseLimitedTimeRewardTaskData_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseLimitedTimeRewardTaskData_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestRemainTime_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestRemainTime_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseRemainTime_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseRemainTime_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\027limitedTimeReward.proto\022\010protocol\"M\n\030R" +
      "equestLimitedTimeReward\0221\n\004data\030\001 \002(\0132#." +
      "protocol.ResponseLimitedTimeReward\"u\n\031Re" +
      "sponseLimitedTimeReward\022\025\n\rcharge_reward" +
      "\030\001 \002(\010\022\013\n\003exp\030\002 \002(\005\022\032\n\022ordinary_reward_i" +
      "d\030\003 \002(\005\022\030\n\020charge_reward_id\030\004 \002(\005\"9\n\034Req" +
      "uestLimitedTimeRewardType\022\014\n\004type\030\001 \002(\005\022" +
      "\013\n\003num\030\002 \002(\005\"@\n\036RequestLimitedTimeReward" +
      "Finish\022\n\n\002id\030\001 \002(\005\022\022\n\nisComplete\030\002 \002(\010\"\234" +
      "\001\n!ResponseLimitedTimeRewardTaskData\0229\n\t",
      "task_type\030\001 \003(\0132&.protocol.RequestLimite" +
      "dTimeRewardType\022<\n\ntask_state\030\002 \003(\0132(.pr" +
      "otocol.RequestLimitedTimeRewardFinish\"\023\n" +
      "\021RequestRemainTime\")\n\022ResponseRemainTime" +
      "\022\023\n\013time_second\030\001 \002(\003B\027B\025LimitedTimeRewa" +
      "rdData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestLimitedTimeReward_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestLimitedTimeReward_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestLimitedTimeReward_descriptor,
              new java.lang.String[] { "Data", });
          internal_static_protocol_ResponseLimitedTimeReward_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseLimitedTimeReward_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseLimitedTimeReward_descriptor,
              new java.lang.String[] { "ChargeReward", "Exp", "OrdinaryRewardId", "ChargeRewardId", });
          internal_static_protocol_RequestLimitedTimeRewardType_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestLimitedTimeRewardType_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestLimitedTimeRewardType_descriptor,
              new java.lang.String[] { "Type", "Num", });
          internal_static_protocol_RequestLimitedTimeRewardFinish_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestLimitedTimeRewardFinish_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestLimitedTimeRewardFinish_descriptor,
              new java.lang.String[] { "Id", "IsComplete", });
          internal_static_protocol_ResponseLimitedTimeRewardTaskData_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponseLimitedTimeRewardTaskData_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseLimitedTimeRewardTaskData_descriptor,
              new java.lang.String[] { "TaskType", "TaskState", });
          internal_static_protocol_RequestRemainTime_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_RequestRemainTime_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestRemainTime_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseRemainTime_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_protocol_ResponseRemainTime_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseRemainTime_descriptor,
              new java.lang.String[] { "TimeSecond", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
