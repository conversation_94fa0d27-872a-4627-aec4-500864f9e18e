package module.pvp;

import com.google.gson.Gson;
import protocol.PVPData;

public class PVPPetData {
    public int uid;
    public int id;
    public String nameItem;
    public int currentLevel;

    public float hp;

    public float sp;

    public float attack;

    public float defense;

    public float specialAttack;

    public float specialDefense;

    public float speed;

    // 回避
    public int avoid;

    // 命中
    public int hid;

    // 暴击
    public int crt;

    // 属性 金木水火土
    public int typeone;

    public PVPPetData SetInfo(PVPData.PetData petData) {
        if (petData != null){
            this.uid = petData.getUid();
            this.id = petData.getId();
            this.nameItem = petData.getNameItem();
            this.currentLevel = petData.getCurrentLevel();

            this.hp = petData.getHp();
            this.sp = petData.getSp();
            this.attack = petData.getAttack();
            this.defense = petData.getDefense();
            this.specialAttack = petData.getSpecialAttack();
            this.specialDefense = petData.getSpecialDefense();
            this.speed = petData.getSpeed();
            this.avoid = petData.getAvoid();
            this.hid = petData.getHid();
            this.crt = petData.getCrt();

            this.typeone = petData.getTypeone();
        }else {
            this.uid = 0;
        }
        return this;
    }

    public String toJson(){
        Gson gson = new Gson();
        return gson.toJson(this);
    }

    public PVPData.PetData.Builder GetPetData(String pet1) {
        Gson gson = new Gson();
        PVPPetData pvpPetData1 = gson.fromJson(pet1, PVPPetData.class);
        protocol.PVPData.PetData.Builder value = PVPData.PetData.newBuilder();

        value.setUid(pvpPetData1.uid);
        value.setId(pvpPetData1.id);
        value.setNameItem(pvpPetData1.nameItem);
        value.setCurrentLevel(pvpPetData1.currentLevel);

        value.setHp(pvpPetData1.hp);
        value.setSp(pvpPetData1.sp);
        value.setAttack(pvpPetData1.attack);
        value.setDefense(pvpPetData1.defense);
        value.setSpecialAttack(pvpPetData1.specialAttack);
        value.setSpecialDefense(pvpPetData1.specialDefense);
        value.setSpeed(pvpPetData1.speed);
        value.setAvoid(pvpPetData1.avoid);
        value.setHid(pvpPetData1.hid);
        value.setCrt(pvpPetData1.crt);

        value.setTypeone(pvpPetData1.typeone);
        return value;
    }
}
