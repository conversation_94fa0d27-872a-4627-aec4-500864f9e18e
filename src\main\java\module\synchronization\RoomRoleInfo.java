package module.synchronization;

import model.CupboardInfo;
import model.PointDoubleInfo;
import protocol.FriendData;
import protocol.MissionData;
import protocol.UserData;

/**
 * Created by nara on 2018/5/31.
 */
public class RoomRoleInfo {
    private int type;//1房主 2成员
    private int status;//0未准备 1已准备
    private int queue;//1队伍 2队伍
    private int position;//队伍位置
    private String uid;
    private int playerId;
    private String name;
    private int lv;
    private int head;
    private int roleId;
    private CupboardInfo cupboard;
    private PointDoubleInfo point;
    private boolean robot=false;

    public PointDoubleInfo getPoint() {
        return point;
    }

    public void setPoint(PointDoubleInfo point) {
        this.point = point;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public int getRoleId() {
        return roleId;
    }

    public void setRoleId(int roleId) {
        this.roleId = roleId;
    }

    public int getHead() {
        return head;
    }

    public void setHead(int head) {
        this.head = head;
    }

    public int getLv() {
        return lv;
    }

    public void setLv(int lv) {
        this.lv = lv;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getQueue() {
        return queue;
    }

    public void setQueue(int queue) {
        this.queue = queue;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public CupboardInfo getCupboard() {
        return cupboard;
    }

    public void setCupboard(CupboardInfo cupboard) {
        this.cupboard = cupboard;
    }

    public int getPlayerId() {
        return playerId;
    }

    public void setPlayerId(int playerId) {
        this.playerId = playerId;
    }

    public boolean isRobot() {
        return robot;
    }

    public void setRobot(boolean robot) {
        this.robot = robot;
    }

    public static MissionData.RoomRole toRoomRoleData(RoomRoleInfo roomRoleInfo){
        MissionData.RoomRole.Builder roomRoleBu = MissionData.RoomRole.newBuilder();
        roomRoleBu.setType(roomRoleInfo.getType());
        roomRoleBu.setStatus(roomRoleInfo.getStatus());
        roomRoleBu.setQueue(roomRoleInfo.getQueue());
        roomRoleBu.setPosition(roomRoleInfo.getPosition());
        roomRoleBu.setRoleId(roomRoleInfo.getRoleId());
        FriendData.Player.Builder playerBu = FriendData.Player.newBuilder();
        playerBu.setId(roomRoleInfo.getPlayerId());
        playerBu.setName(roomRoleInfo.getName());
        playerBu.setLv(roomRoleInfo.getLv());
        playerBu.setHead(roomRoleInfo.getHead());
//        playerBu.setStation(0);
        roomRoleBu.setPlayer(playerBu);
        UserData.Part part = CupboardInfo.setCupboardToPartData(roomRoleInfo.getCupboard());
        roomRoleBu.setPart(part);
        UserData.PointDouble pointDouble = PointDoubleInfo.toPointDoubleData(roomRoleInfo.getPoint());
        roomRoleBu.setPoint(pointDouble);
        return roomRoleBu.build();
    }

    @Override
    public String toString() {
        return "RoomRoleInfo{" +
                "type=" + type +
                ", status=" + status +
                ", queue=" + queue +
                ", position=" + position +
                ", uid='" + uid + '\'' +
                ", playerId=" + playerId +
                ", name='" + name + '\'' +
                ", lv=" + lv +
                ", head=" + head +
                ", roleId=" + roleId +
                ", cupboard=" + cupboard +
                ", point=" + point +
                ", robot=" + robot +
                '}';
    }
}
