import manager.Redis;

import java.util.*;

public class JedisTest {
    public static Map<String, String> itemsMap(String str) {
        Map<String, String> map = new HashMap<>();
        String[] keys = str.split("\\|");
        for (int i = 0; i < keys.length; i++) {
            String[] strs = keys[i].split(",");
            map.put(strs[0], strs[1]);
        }
        return map;
    }
//拿到数据库中的id及数据,添加到MySQL数据库中返回是否合成成功并刷新
    public static void main(String[] args) {
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("ItemComposeconfig:*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> mailMap = jedis.hgetAll(key);
            Map<String, String> map = itemsMap(mailMap.get("CostItem"));
        }
    }
}
