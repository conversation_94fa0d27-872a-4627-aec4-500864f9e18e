package module.map;

import entities.MapEntity;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import protocol.MapData;
import protocol.ProtoData;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

public class MapUtils {
    private static MapUtils inst = null;
    public static MapUtils getInstance() {
        if (inst == null) {
            inst = new MapUtils();
        }
        return  inst;
    }
    public static MapData.Map getMapData(int count, int num) {
        MapData.Map.Builder map =  MapData.Map.newBuilder();
        map.setMapCount(count);
        map.setMapCurrent(num);
        return map.build();
    }
    public static MapData.Map entityToMapData(MapEntity entity) {
        return getMapData(entity.getMapcount(), entity.getMapnumnow());
    }
}
