package manager;

import model.LoginInfo;
import module.friend.FriendDao;
import module.login.LoginService;
import module.pay.WeChatUtil;



import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by nara on 2018/5/10.
 */
public class LoginHandler implements Runnable {
    private static Logger log = LoggerFactory.getLogger(LoginHandler.class);
    private static LoginHandler inst;
    public static LoginHandler getInstance(){
        if (inst == null) {
            inst = new LoginHandler();
        }
        return inst;
    }

    public void run() {
        while (true){
            try {
                login();
                Thread.sleep(1);
            }catch (Exception e){
                e.printStackTrace();
                log.error(e.getMessage(),e);
            }
        }
    }

    public static void login(){
        while (MySql.loginList.size() > 0)
        {
          //  /// System.out.println("MySql.loginList.size()>>>>>>>"+MySql.loginList.size());//？？？？？？？？？？？？？？？
            if (MySql.getLoginNowSize() < MySql.loginMaxSize)
            {
                LoginInfo loginInfo = null;
                try
                 {
                    loginInfo = MySql.loginList.remove(0);
                }catch (Exception e)
                {
                    MySql.loginList.remove(null);
                }

                if (loginInfo == null)
                {
//                    /// System.out.println("loginInfo null???");
                }
                else 
                {
                    try 
                    {
                        MySql.addLoginNowSize();
//                        /// System.out.println("loginNowSize begin>>>>>>>"+MySql.getLoginNowSize());
                        if (loginInfo.getLoginType() == 1)
                        {
                            LoginService.getInstance().getLoginUserInfo(loginInfo);
                        }
                        else if (loginInfo.getLoginType() == 2)
                        {
                            LoginService.getInstance().createRole(loginInfo);

                        }else if (loginInfo.getLoginType() == 3)
                        {
                            LoginService.getInstance().register(loginInfo);
                        }
                        else if( loginInfo.getLoginType() == 4 )//wechat login
                        {
                           LoginService.getInstance().WeChatLogin(loginInfo);
                        }
                        else if( loginInfo.getLoginType() == 5 )//apple login
                        {
                            LoginService.getInstance().AppleLogin(loginInfo);
                        }   
                        else if( loginInfo.getLoginType() == 6 )//QQ login
                        {
                            LoginService.getInstance().QQLogin(loginInfo);
                        }                          
                        else if( loginInfo.getLoginType() == 7 )//YSDK login
                        {
                            LoginService.getInstance().YSDKLogin(loginInfo);
                        }   
                        else if( loginInfo.getLoginType() == 8 )//huawei login
                        {
                            LoginService.getInstance().HuaWeiLogin(loginInfo);
                        }   
                        else if( loginInfo.getLoginType() == 9 )//google login
                        {
                            LoginService.getInstance().GoogleLogin(loginInfo);
                        }                                                                                              
                    }
                    catch (Exception e)
                    {
                        e.printStackTrace();
                        log.error(e.getMessage(),e);
                    }
                    finally 
                    {
                        MySql.reduceLoginNowSize();
                 //       /// System.out.println("loginNowSize over>>>>>>>"+MySql.getLoginNowSize());
                    }
                }
            }
        }
    }
}
