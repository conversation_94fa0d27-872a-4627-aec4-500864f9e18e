import manager.Redis;
import table.RechargeRebate.RechargeRebateLine;
import table.RechargeRebate.RechargeRebateTable;
import table.TableManager;
import table.character_xp.character_xpLine;
import table.character_xp.character_xpTable;
import table.limitedtime_reward.LimitedTimeRewardLine;
import table.limitedtime_reward.LimitedTimeRewardTable;

import java.util.ArrayList;
import java.util.List;

public class ReadTableTest {
    public static void main(String[] args) {
        ArrayList<RechargeRebateLine> lines = RechargeRebateTable.getInstance().GetAllItem();
        for (RechargeRebateLine line : lines) {
            System.out.println(line.rewards);
        }
    }
}
