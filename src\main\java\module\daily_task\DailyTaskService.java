package module.daily_task;

import com.google.api.client.util.DateTime;
import com.google.protobuf.InvalidProtocolBufferException;
import entities.CompleteDailyTaskEntity;
import entities.DailyTaskEntity;
import module.activity.LimitedTime.LimitedTimeRewardService;
import module.task.TaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.TaskData;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

public class DailyTaskService {
    private static Logger log = LoggerFactory.getLogger(DailyTaskService.class);
    private static DailyTaskService inst = null;
    public static DailyTaskService getInstance() {
        if (inst == null) {
            inst = new DailyTaskService();
        }
        return inst;
    }
    // 每日任务的任务数量 ++
    public byte[] NumberIncreaseDailyTask(String uid,byte[] bytes){
        try {
            TaskData.requestdailytansks requestData  = TaskData.requestdailytansks.parseFrom(bytes);
            if (requestData != null){
                DailyTaskEntity dailyTaskEntity = DailtTaskDao.getInstance().GetDailyTaskEntity(
                        uid,
                        String.valueOf(requestData.getType())
                );
                if (dailyTaskEntity != null){
                    // 数据库里面有相同类型的每日任务
                    dailyTaskEntity.setNumber(dailyTaskEntity.getNumber() + requestData.getNum());
                    DailtTaskDao.getInstance().update(dailyTaskEntity);
                }else{
                    // 没有此任务类型，插入一个
                    DailyTaskEntity entity = new DailyTaskEntity();
                    entity.setUid(uid);
                    entity.setTime(new Date(System.currentTimeMillis()));
                    entity.setType(requestData.getType());
                    entity.setNumber(requestData.getNum());
                    DailtTaskDao.getInstance().insert(entity);
                }

                // 限时任务
                //LimitedTimeRewardService.getInstance().TaskNumberIncrease(uid, requestData.getType(), requestData.getNum());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        // 返回奖励

        // 不需要返回值
        return null;
    }

    // 修改任务状态
    public byte[] FinishDailyTask(String uid,byte[] bytes){
        try {
            TaskData.requestcompletedailytasks requestData  = TaskData.requestcompletedailytasks.parseFrom(bytes);
//            System.err.println("id = " + requestData.getId()+"\n"+
//            "isComplete = " + requestData.getIsComplete());
            if (requestData !=  null){
                CompleteDailyTaskEntity dailyTaskEntity = CompleteDailtTaskDao.getInstance().GetCompleteDailyTaskEntity(
                        uid,
                        String.valueOf(requestData.getId())
                );
                if (dailyTaskEntity != null){
//                    System.err.println("测试成功\n" + dailyTaskEntity.isComplete() + dailyTaskEntity.getTime());
                    dailyTaskEntity.setComplete(requestData.getIsComplete());
                    CompleteDailtTaskDao.getInstance().update(dailyTaskEntity);
                }else{
//                    System.err.println("测试失败");
                    CompleteDailyTaskEntity savaData = new CompleteDailyTaskEntity();
                    savaData.setUid(uid);
                    savaData.setTime(new Date(System.currentTimeMillis()));
                    savaData.setDailyTaskId(requestData.getId());
                    savaData.setComplete(requestData.getIsComplete());
                    CompleteDailtTaskDao.getInstance().insert(savaData);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
        // 返回奖励

        // 不需要返回值
        return null;
    }

    // 获取全部的任务
    public byte[] GetAllDailyTask(String uid, byte[] bytes){
//        System.err.println("任务 获取全部");
        TaskData.responseTask.Builder builder = TaskData.responseTask.newBuilder();
        try {
            List<Object> dailyTaskEntity = DailtTaskDao.getInstance().GetAllDailyTaskEntity(uid);
            List<Object> completeDailyTaskEntity = CompleteDailtTaskDao.getInstance().GetAllCompleteDailyTaskEntity(uid);

            for (Object item :
                    dailyTaskEntity) {
                DailyTaskEntity data = (DailyTaskEntity)item;
                TaskData.Dailytansks.Builder dataBuilder = TaskData.Dailytansks.newBuilder();
                dataBuilder.setNum(data.getNumber());
                dataBuilder.setType(data.getType());

                builder.addDailytansks(dataBuilder);
            }

            for (Object item :
                    completeDailyTaskEntity) {
                CompleteDailyTaskEntity data = (CompleteDailyTaskEntity)item;
                TaskData.Completedailytasks.Builder dataBuilder = TaskData.Completedailytasks.newBuilder();
                dataBuilder.setId(data.getDailyTaskId());
                dataBuilder.setIsComplete(data.isComplete());

                builder.addCompletedailytasks(dataBuilder);
            }

            builder.setErrorId(1);
        }catch (Exception e){
            builder.setErrorId(0);
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }


    // 没写协议，自动领取每日任务
    public byte[] AutoFinishDailyTask(String uid, byte[] bytes){
        return  null;
    }
}
