package table.RechargeRebate;

import table.TableManager;

import java.util.Comparator;

public class RechargeRebateTable extends TableManager<RechargeRebateLine> {
    private static RechargeRebateTable inst = null;
    public static RechargeRebateTable getInstance() {
        if (inst == null) {
            inst = new RechargeRebateTable();
        }
        return inst;
    }
    @Override
    public void Parse() {
        this.GetAllItem().sort(Comparator.comparingInt(a -> a.id));
        for (RechargeRebateLine data :
                  GetAllItem()) {
            data.Parse();
        }
    }
    @Override
    public String LinePath() {
        return "table.RechargeRebate.RechargeRebateLine";
    }

    @Override
    public String TableName() {
        return "rechargeRebate";
    }
}
