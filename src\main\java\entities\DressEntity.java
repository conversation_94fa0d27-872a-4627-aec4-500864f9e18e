package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/3/29.
 */
@Entity
@Table(name = "dress", schema = "", catalog = "super_star_fruit")
public class DressEntity {
    private int id;
    private int version;
    private String uid;
    private int roleid;
    private Integer dressid;
    private String overstamp;
    private int type;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "roleid")
    public int getRoleid() {
        return roleid;
    }

    public void setRoleid(int roleid) {
        this.roleid = roleid;
    }

    @Basic
    @Column(name = "dressid")
    public Integer getDressid() {
        return dressid;
    }

    public void setDressid(Integer dressid) {
        this.dressid = dressid;
    }

    @Basic
    @Column(name = "overstamp")
    public String getOverstamp() {
        return overstamp;
    }

    public void setOverstamp(String overstamp) {
        this.overstamp = overstamp;
    }


    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        DressEntity that = (DressEntity) o;

        if (id != that.id) return false;
        if (version != that.version) return false;
        if (roleid != that.roleid) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (dressid != null ? !dressid.equals(that.dressid) : that.dressid != null) return false;
        if (overstamp != null ? !overstamp.equals(that.overstamp) : that.overstamp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + version;
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + roleid;
        result = 31 * result + (dressid != null ? dressid.hashCode() : 0);
        result = 31 * result + (overstamp != null ? overstamp.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "DressEntity{" +
                "id=" + id +
                ", version=" + version +
                ", uid='" + uid + '\'' +
                ", roleid=" + roleid +
                ", dressid=" + dressid +
                ", overstamp='" + overstamp + '\'' +
                '}';
    }
}
