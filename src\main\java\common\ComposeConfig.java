package common;

@ExcelConfigObject(key = "compose")
public class ComposeConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "composeid")
    private int composeId;
    @ExcelColumn(name = "dimprobability")
    private int dimprobability;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getComposeId() {
        return composeId;
    }

    public void setComposeId(int composeId) {
        this.composeId = composeId;
    }

    public int getDimprobability() {
        return dimprobability;
    }

    public void setDimprobability(int dimprobability) {
        this.dimprobability = dimprobability;
    }
}
