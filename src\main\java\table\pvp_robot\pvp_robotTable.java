package table.pvp_robot;

import table.TableManager;

import java.util.Comparator;
import java.util.Random;

public class pvp_robotTable extends TableManager<pvp_robotLine> {
    private static pvp_robotTable instance;
    public static pvp_robotTable getInstance(){
        if (instance != null){
            return instance;
        }
        instance = new pvp_robotTable();
        return instance;
    }
    private pvp_robotTable(){

    }
    
    @Override
    public void Parse() {
        this.GetAllItem().sort(Comparator.comparingInt(a -> a.id));
        for (pvp_robotLine data :
                GetAllItem()) {
            data.Parse();
        }
    }

    @Override
    public String TableName() {
        return "pvpRobot";
    }

    @Override
    public String LinePath() {
        return "table.pvp_robot.pvp_robotLine";
    }


    public pvp_robotLine GetRandom(){
        Random random = new Random();
        return GetAllItem().get(random.nextInt(GetAllItem().size()));
    }
}
