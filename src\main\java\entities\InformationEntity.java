package entities;

import javax.persistence.*;
import java.util.Objects;

/**
 * Created by nara on 2018/2/11.
 */
@Entity
@Table(name = "information", schema = "", catalog = "super_star_fruit")
public class InformationEntity {
    private int id;
    private String uid1;
    private String uid2;
    private String content;
    private String date;
    private int status;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid1")
    public String getUid1() {
        return uid1;
    }

    public void setUid1(String uid1) {
        this.uid1 = uid1;
    }

    @Basic
    @Column(name = "uid2")
    public String getUid2() {
        return uid2;
    }

    public void setUid2(String uid2) {
        this.uid2 = uid2;
    }

    @Basic
    @Column(name = "content")
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Basic
    @Column(name = "date")
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Basic
    @Column(name = "status")
    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "InformationEntity{" +
                "id=" + id +
                ", uid1='" + uid1 + '\'' +
                ", uid2='" + uid2 + '\'' +
                ", content='" + content + '\'' +
                ", date='" + date + '\'' +
                ", status=" + status +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        InformationEntity that = (InformationEntity) o;
        return getId() == that.getId() &&
                getStatus() == that.getStatus() &&
                Objects.equals(getUid1(), that.getUid1()) &&
                Objects.equals(getUid2(), that.getUid2()) &&
                Objects.equals(getContent(), that.getContent()) &&
                Objects.equals(getDate(), that.getDate());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getUid1(), getUid2(), getContent(), getDate(), getStatus());
    }
}
