package manager;

import model.FriendInfo;
import module.friend.FriendDao;
import module.friend.IFriend;
import module.login.ILogin;
import module.login.LoginDao;
import protocol.FriendData;
import protocol.ProtoData;
import server.SuperServerHandler;

import java.util.List;

/**
 * Created by nara on 2018/2/7.
 */
public class UpdateFriendHandler implements Runnable {
    private int type = 1;//1好友更新提示 2好友上下线系统消息
    private String uid;
    private String name = null;			//姓名
    private int lv = -1;				//等级
    private int head = -1;			//头像
    private int status = -1;			//在线状态 0不在线1在线
    private int station = -1;			//车站
    private String signature = null;		//签名
    private long endless = -1;		//无尽

    private int id = -1;
    private String content = null;

    public void setEndless(long endless) {
        this.endless = endless;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setStation(int station) {
        this.station = station;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public void setHead(int head) {
        this.head = head;
    }

    public void setLv(int lv) {
        this.lv = lv;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public void run() {
        ILogin iLogin = LoginDao.getInstance();
        List<FriendInfo> friendList = iLogin.getFriendInfoFromRedis(uid);
        if (friendList.size() == 0) {
            return;
        }
        IFriend iFriend = FriendDao.getInstance();
        byte[] bytes = null;
        int msgId = 0;
        if (type == 1){
            FriendData.ReportUpdateFriend reportFriend = iFriend.setUpdateFriend(uid,name,lv,head,status,station,signature,endless);
            bytes = reportFriend.toByteArray();
            msgId = ProtoData.SToC.REPORTUPDATEFRIEND_VALUE;
        }else if (type == 2){
            FriendData.ReportBroadcast reportBroadcast = iFriend.setReportBroadcast(FriendData.BroadcastType.SYSTEM_VALUE,id,content);
            bytes = reportBroadcast.toByteArray();
            msgId = ProtoData.SToC.REPORTSYSTEM_VALUE;
        }
        int server = Redis.getRoleServer(uid);
        for (int i = 0 ; i < friendList.size() ; i++){
            FriendInfo friendInfo = friendList.get(i);
            if (SuperServerHandler.getServerIdCtxFromUid(server,friendInfo.getUid()) != null){
                ReportManager.reportInfo(friendInfo.getUid(), msgId,bytes);
            }
        }
    }
}