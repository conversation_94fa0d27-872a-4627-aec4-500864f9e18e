package module.playerstatus;

import Json.MailJson;
import com.google.protobuf.InvalidProtocolBufferException;
import entities.RoleEntity;
import manager.MySql;
import module.friend.FriendDao;
import module.item.ItemDao;
import protocol.ItemData;
import protocol.MailData;
import utils.MyUtils;

import java.util.Date;

public class PlayerCreateRole {
    private static PlayerCreateRole inst = null;
    public static PlayerCreateRole getInstance() {
        if (inst == null) {
            inst = new PlayerCreateRole();
        }
        return inst;
    }
    public void SendMessage(String uid) {
        System.err.println("New Player Res:" + uid);
//        ItemDao.getInstance().updateItemInfo(uid, 1, 10000000);
//        ItemDao.getInstance().updateItemInfo(uid, 11, -5);
        //ItemDao.getInstance().updateItemInfo(uid, 11, 2);
        ItemDao.getInstance().updateItemInfo(uid, 1, 1000);
        ItemDao.getInstance().updateItemInfo(uid, 29, 15);
        //PlayerOnline.getInstance().SendMessage(uid);
        //FreshmanMail(uid, 2, 3000, "亲爱的玩家：\n今天是您踏上果大陆的第一天，我们为您准备了一份小小的礼物~\n请在附件中，收取「《Fresh Mania》测试奖励」的所含内容。愿您旅途愉快。", "「测试奖励」领取");
        //FreshmanMail(uid, 2, 2000, "亲爱的玩家：\n为了纪念您与《Fresh Mania》的第一次相识，我们为这次邂逅准备了一份礼物~\n请在附件中，收取「新手引导完成奖励」的所含内容。愿您旅途愉快。", "「新手引导完成奖励」领取");
//        ItemDao.getInstance().updateItemInfo(uid, 11, 100000);
//        ItemDao.getInstance().updateItemInfo(uid, 25, 100000);
//        ItemDao.getInstance().updateItemInfo(uid, 26, 100000);
//        ItemDao.getInstance().updateItemInfo(uid, 27, 100000);
//        ItemDao.getInstance().updateItemInfo(uid, 28, 100000);
//        ItemDao.getInstance().updateItemInfo(uid, 29, 100000);
    }

    private void FreshmanMail(String uid, int itemID, int itemNum, String content, String title){
        System.err.println("Send Freshman Mail!!!!!!!!!!!!!!!!!");
        try {
            MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
            attachment.setType(1);
            ItemData.Item.Builder itemBuild = ItemData.Item.newBuilder();
            itemBuild.setId(itemID);
            itemBuild.setNum(itemNum);
            attachment.addItem(itemBuild.build());
            StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            MailJson mailJson = new MailJson();
            mailJson.setContent(content);
            ItemData.Item item = attachment.getItemList().get(0);
            mailJson.setItemStr(attachment.getType() + "#id:" + item.getId() + ",num:" + item.getNum());
            FriendDao.getInstance().SendMail("part", roleEntity.getId(), 1001, "Admin",title,MyUtils.objectToJson(mailJson), 0, new Date().getTime(),new Date().getTime() + 1000*60*60*24*7,attachment.build());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
