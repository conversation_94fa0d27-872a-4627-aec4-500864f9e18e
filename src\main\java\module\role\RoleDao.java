package module.role;

import entities.RoleEntity;
import manager.MySql;

import java.util.List;

public class RoleDao {
    private static RoleDao inst = null;

    public static RoleDao getInstance() {
        if (inst == null) {
            inst = new RoleDao();
        }
        return inst;
    }
    public RoleEntity queryRole(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (RoleEntity) entity;
    }

    public  List<Object> query(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());
        return list;
    }
    public  List<Object> queryOne(String uid){
        StringBuffer stringBuffer = new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());

        return list;
    }
    public  List<Object> BOSSUid(int type){
        StringBuffer stringBuffer = new StringBuffer(" select uid from RoleEntity where type='").append(type).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());

        return list;
    }
    public List<Object> queryUserid( String userid){
        StringBuffer stringBuffer = new StringBuffer("SELECT uid FROM RoleEntity where userid ='").append(userid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());

        return list;
    }
    public  List<Object> queryUid(){
        StringBuffer stringBuffer = new StringBuffer("SELECT uid FROM RoleEntity");
        List<Object> list = MySql.queryForList(stringBuffer.toString());

        return list;
    }
    public  List<Object> queryId(){
        StringBuffer stringBuffer = new StringBuffer("SELECT id FROM RoleEntity");
        List<Object> list = MySql.queryForList(stringBuffer.toString());

        return list;
    }

}
