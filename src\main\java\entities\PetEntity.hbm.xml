<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.PetEntity" table="pet" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="petUId" column="petUId"/>
        <property name="petType" column="petType"/>
        <property name="friendId" column="friendId"/>
        <property name="currentLevel" column="currentLevel"/>
        <property name="currentExp" column="currentExp"/>
        <property name="starLevel" column="starLevel"/>
        <property name="strongLevel" column="strongLevel"/>
        <property name="petCharacter" column="petCharacter"/>
        <property name="mainAttribute" column="mainAttribute"/>
        <property name="subAttribute" column="subAttribute"/>
        <property name="hp" column="hp"/>
        <property name="atk" column="atk"/>
        <property name="def" column="def"/>
        <property name="satk" column="satk"/>
        <property name="sdef" column="sdef"/>
        <property name="speed" column="speed"/>
        <property name="normalSkillId" column="normalSkillId"/>
        <property name="spSkillId" column="spSkillId"/>
        <property name="normalSkillLv" column="normalSkillLv"/>
        <property name="spSkillLv" column="spSkillLv"/>
        <property name="name" column="name"/>
        <property name="lockStatus" column="lockStatus"/>
        <property name="breakLV" column="breakLV"/>
        <property name="hpFactor" column="hpFactor"/>
        <property name="atkFactor" column="atkFactor"/>
        <property name="defFactor" column="defFactor"/>
        <property name="satkFactor" column="satkFactor"/>
        <property name="sdefFactor" column="sdefFactor"/>
        <property name="speedFactor" column="speedFactor"/>
        <property name="rarity" column="rarity"/>
        <property name="accessType" column="accessType"/>
        <property name="growing" column="growing"/>
        <property name="mode" column="petMode"/>
        <property name="getTime" column="getTime"/>
    </class>
</hibernate-mapping>