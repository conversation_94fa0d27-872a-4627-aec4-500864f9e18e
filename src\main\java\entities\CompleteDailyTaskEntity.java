package entities;

import com.google.api.client.util.DateTime;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "complete_daily_task", schema = "", catalog = "super_star_fruit")
public class CompleteDailyTaskEntity {
    private int id;
    private String uid;
    private Date time;
    private int dailyTaskId;
    private boolean isComplete;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "time")
    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    @Basic
    @Column(name = "daily_task_id")
    public int getDailyTaskId() {
        return dailyTaskId;
    }

    public void setDailyTaskId(int dailyTaskId) {
        this.dailyTaskId = dailyTaskId;
    }

    @Basic
    @Column(name = "is_complete")
    public boolean isComplete() {
        return isComplete;
    }

    public void setComplete(boolean complete) {
        isComplete = complete;
    }
}
