package entities;

import com.fasterxml.jackson.annotation.JsonInclude;
import protocol.RoleData;
import protocol.SexData;
import protocol.UserData;

import javax.persistence.*;
import java.util.List;
import java.util.Objects;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@Table(name = "role", schema = "super_star_fruit", catalog = "super_star_fruit")
public class RoleEntity {
    private int id;
    private int type;
    private int roleid;
    private int sex;
    private Integer score;
    private String uid;
    private String userid;
    //private int advance;
    private Integer head;
    private Integer ranknumber;
    //    private Integer rankNumbers;
    private String name;
    private Integer action;
    private String actionstamp;
    private Integer lv;
    private Integer exp;
    private Integer bagmax;
    //  private boolean sign;
    // private Integer signin;
    //private String signstamp;
    private Integer robot;
    private long dailyGameTime;
    private int dailyLoginflag;
    private int dailyLoginLoop;
    private String signaTure;
    private int lvApprovalnums;
    private int MissionApprovalnums;
    private Integer missionid;
    private int endlesspprovalnums;
    private Integer endless;
    private Integer totalmoney;
    private Integer firstRecharge;


    @Basic
    @Column(name = "score")

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }
    @Basic
    @Column(name = "roleId")
    public int getRoleid() {
        return roleid;
    }

    public void setRoleid(int roleid) {
        this.roleid = roleid;
    }




    @Basic
    @Column(name = "ranknumber")
    public Integer getRanknumber() {
        return ranknumber;
    }

    public void setRanknumber(Integer ranknumber) {
        this.ranknumber = ranknumber;
    }
    @Basic
    @Column(name = "endless")
    public Integer getEndless() {
        return endless;
    }

    public void setEndless(Integer endless) {
        this.endless = endless;
    }



    @Basic
    @Column(name = "sex")
    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }
    @Basic
    @Column(name = "endlesspprovalnums")
    public int getEndlesspprovalnums() {
        return endlesspprovalnums;
    }

    public void setEndlesspprovalnums(int endlesspprovalnums) {
        this.endlesspprovalnums = endlesspprovalnums;
    }

    @Basic
    @Column(name = "missionid")

    public Integer getMissionid() {
        return missionid;
    }

    public void setMissionid(Integer missionid) {
        this.missionid = missionid;
    }

    @Basic
    @Column(name = "missionApprovalnums")
    public int getMissionApprovalnums() {
        return MissionApprovalnums;
    }

    public void setMissionApprovalnums(int missionApprovalnums) {
        MissionApprovalnums = missionApprovalnums;
    }

    @Basic
    @Column(name = "lvApprovalnums")
    public int getLvApprovalnums() {
        return lvApprovalnums;
    }

    public void setLvApprovalnums(int lvApprovalnums) {
        this.lvApprovalnums = lvApprovalnums;
    }

    @Basic
    @Column(name = "signaTure")
    public String getSignaTure() {
        return signaTure;
    }

    public void setSignaTure(String signaTure) {
        this.signaTure = signaTure;
    }

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "userid")
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    /*@Basic
    @Column(name = "advance")
    public int getAdvance() {
        return advance;
    }

    public void setAdvance(int advance) {
        this.advance = advance;
    }*/

    @Basic
    @Column(name = "head")
    public Integer getHead() {
        return head;
    }

    public void setHead(Integer head) {
        this.head = head;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "action")
    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    @Basic
    @Column(name = "actionstamp")
    public String getActionstamp() {
        return actionstamp;
    }

    public void setActionstamp(String actionstamp) {
        this.actionstamp = actionstamp;
    }

    @Basic
    @Column(name = "lv")
    public Integer getLv() {
        return lv;
    }

    public void setLv(Integer lv) {
        this.lv = lv;
    }

    @Basic
    @Column(name = "exp")
    public Integer getExp() {
        return exp;
    }

    public void setExp(Integer exp) {
        this.exp = exp;
    }

    @Basic
    @Column(name = "bagmax")
    public Integer getBagmax() {
        return bagmax;
    }

    public void setBagmax(Integer bagmax) {
        this.bagmax = bagmax;
    }

    @Basic
    @Column(name = "robot")
    public Integer getRobot() {
        return robot;
    }

    public void setRobot(Integer robot) {
        this.robot = robot;
    }

    @Basic
    @Column(name = "dailygametime")
    public long getDailyGameTime() {
        return dailyGameTime;
    }

    public void setDailyGameTime(long dailyGameTime) {
        this.dailyGameTime = dailyGameTime;
    }

    @Basic
    @Column(name = "dailyLoginflag")
    public int getDailyLoginflag() {
        return dailyLoginflag;
    }

    public void setDailyLoginflag(int dailyLoginflag) {
        this.dailyLoginflag = dailyLoginflag;
    }

    @Basic
    @Column(name = "dailyLoginLoop")
    public int getDailyLoginLoop() {
        return dailyLoginLoop;
    }

    public void setDailyLoginLoop(int dailyLoginLoop) {
        this.dailyLoginLoop = dailyLoginLoop;
    }

    @Basic
    @Column(name = "totalmoney")
    public Integer getTotalmoney() {
        return totalmoney;
    }

    public void setTotalmoney(Integer totalmoney) {
        this.totalmoney = totalmoney;
    }  
    
    @Basic
    @Column(name = "firstRecharge")
    public Integer getFirstRecharge() {
        return firstRecharge;
    }

    public void setFirstRecharge(Integer firstRecharge) {
        this.firstRecharge = firstRecharge;
    }    
/*
    @Basic
    @Column(name = "sign")
    public boolean isSign() {
        return sign;
    }

    public void setSign(boolean sign) {
        this.sign = sign;
    }
*/

    @Override
    public String toString() {
        return "RoleEntity{" +
                "id=" + id +
                ", type=" + type +
                ", roleid=" + roleid +
                ", sex=" + sex +
                ", score=" + score +
                ", uid='" + uid + '\'' +
                ", userid='" + userid + '\'' +
                ", head=" + head +
                ", ranknumber=" + ranknumber +
                ", name='" + name + '\'' +
                ", action=" + action +
                ", actionstamp='" + actionstamp + '\'' +
                ", lv=" + lv +
                ", exp=" + exp +
                ", bagmax=" + bagmax +
                ", robot=" + robot +
                ", dailyGameTime=" + dailyGameTime +
                ", dailyLoginflag=" + dailyLoginflag +
                ", dailyLoginLoop=" + dailyLoginLoop +
                ", signaTure='" + signaTure + '\'' +
                ", lvApprovalnums=" + lvApprovalnums +
                ", MissionApprovalnums=" + MissionApprovalnums +
                ", missionid=" + missionid +
                ", endlesspprovalnums=" + endlesspprovalnums +
                ", endless=" + endless +
                ", totalmoney=" + totalmoney +
                ", firstRecharge=" + firstRecharge +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RoleEntity that = (RoleEntity) o;
        return getId() == that.getId() &&
                getType() == that.getType() &&
                getRoleid() == that.getRoleid() &&
                getSex() == that.getSex() &&
                getDailyGameTime() == that.getDailyGameTime() &&
                getDailyLoginflag() == that.getDailyLoginflag() &&
                getDailyLoginLoop() == that.getDailyLoginLoop() &&
                getLvApprovalnums() == that.getLvApprovalnums() &&
                getMissionApprovalnums() == that.getMissionApprovalnums() &&
                getEndlesspprovalnums() == that.getEndlesspprovalnums() &&
                Objects.equals(getScore(), that.getScore()) &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getUserid(), that.getUserid()) &&
                Objects.equals(getHead(), that.getHead()) &&
                Objects.equals(getRanknumber(), that.getRanknumber()) &&
                Objects.equals(getName(), that.getName()) &&
                Objects.equals(getAction(), that.getAction()) &&
                Objects.equals(getActionstamp(), that.getActionstamp()) &&
                Objects.equals(getLv(), that.getLv()) &&
                Objects.equals(getExp(), that.getExp()) &&
                Objects.equals(getBagmax(), that.getBagmax()) &&
                Objects.equals(getRobot(), that.getRobot()) &&
                Objects.equals(getSignaTure(), that.getSignaTure()) &&
                Objects.equals(getMissionid(), that.getMissionid()) &&
                Objects.equals(getEndless(), that.getEndless()) &&
                Objects.equals(getTotalmoney(), that.getTotalmoney()) &&
                Objects.equals(getFirstRecharge(), that.getFirstRecharge());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getType(), getRoleid(), getSex(), getScore(), getUid(), getUserid(), getHead(), getRanknumber(), getName(), getAction(), getActionstamp(), getLv(), getExp(), getBagmax(), getRobot(), getDailyGameTime(), getDailyLoginflag(), getDailyLoginLoop(), getSignaTure(), getLvApprovalnums(), getMissionApprovalnums(), getMissionid(), getEndlesspprovalnums(), getEndless(), getTotalmoney(), getFirstRecharge());
    }
    public static SexData.RoleSex entity(RoleEntity entity) {
        SexData.RoleSex sex = null;
        if (entity != null) {
            SexData.RoleSex.Builder builder = SexData.RoleSex.newBuilder();
            builder.setSex(entity.getSex());
            sex = builder.build();
        }
        return sex;
    }
    public static RoleData.PlainRole entityToPb(RoleEntity entity) {
        RoleData.PlainRole role = null;
        if (entity != null) {
            RoleData.PlainRole.Builder builder = RoleData.PlainRole.newBuilder();
            builder.setRoleCurrentLV(entity.getLv());

            builder.setRoleExp(entity.getExp());
            builder.setRoleNextNeedExp(entity.getExp());
            role = builder.build();
        }
        return role;
    }
}
