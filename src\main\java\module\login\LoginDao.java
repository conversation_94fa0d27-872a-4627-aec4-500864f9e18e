package module.login;

import com.googlecode.protobuf.format.JsonFormat;
import common.ResourceNotFound;
//import common.RoleExpConfig;
import common.RoleExpConfig;
import common.SignConfig;
import common.SuperConfig;
import entities.*;
import io.netty.channel.ChannelHandlerContext;
import manager.*;
import model.*;
import module.callback.CallBack;
import module.callback.CallBackOrder;
import module.friend.FriendDao;
import module.friend.IFriend;
import module.item.IItem;
import module.item.ItemDao;
import module.item.ItemService;
import module.item.ItemUtils;
import module.mail.MailDao;
import module.robot.Factory;
import module.task.ITask;
import module.task.TaskDao;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.*;
import server.SuperServerHandler;
import utils.MyUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by nara on 2017/11/17.
 */
public class LoginDao implements ILogin {
    private static Logger log = LoggerFactory.getLogger(LoginDao.class);
    private static LoginDao inst = null;
    public static HashMap<String, String> shopItemMap = new HashMap();

    //类初始化加载，避免用户请求时多次计算；
    //因为在处理用户充值礼包是否为首次时表格里物品的id不是从1开始，所以维护一个映射shopItemMap；
    static {
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys(SuperConfig.REDIS_EXCEL_SHOPITEM + "*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> itemMap = jedis.hgetAll(key);
            String type = itemMap.get("shop");
            if (!"3".equals(type)) { //3表示金錢充值禮包
                continue;
            }
            String itemId = itemMap.get("ID");
            String realItemId = itemMap.get("double");
            shopItemMap.put(itemId, realItemId);          //礼包id与在礼包序列次序映射
            //在用户登录时判断计算返回给前端
        }
    }

    public static LoginDao getInstance() {
        if (inst == null) {
            inst = new LoginDao();
        }
        return inst;
    }

    public String getAnnouncement() {
        String filePath = null;
        filePath = "C:/Java/text.txt";
        StringBuffer txt = new StringBuffer();
        try {
            String encoding = "GBK";
            File file = new File(filePath);
            if (file.isFile() && file.exists()) {
                InputStreamReader read = new InputStreamReader(
                        new FileInputStream(file), encoding);
                BufferedReader bufferedReader = new BufferedReader(read);
                String lineTxt = null;
                while ((lineTxt = bufferedReader.readLine()) != null) {
                    txt.append(lineTxt).append("\r");
                }
                read.close();
            } else {
//                /// System.out.println("找不到指定的文件");
            }
        } catch (Exception e) {
//            /// System.out.println("读取文件内容出错");
            e.printStackTrace();
            log.error(e.getMessage(), e);
        }
        return txt.toString();
    }

    public List<UserData.Server> getServerList(int type) {
        List<UserData.Server> serverList = new ArrayList<UserData.Server>();
        Redis jedis = Redis.getInstance();
        if (type == 0) {
            Iterator<String> iterator = jedis.keys("serverinfo*").iterator();
            while (iterator.hasNext()) {
                String key = iterator.next();
                Map<String, String> map = jedis.hgetAll(key);
                UserData.Server.Builder builder = UserData.Server.newBuilder();
                builder.setId(Integer.parseInt(map.get("id")));
                builder.setStatus(Integer.parseInt(map.get("status")));
//                long timeStamp = getOpenTimeStamp(builder.getId());
                long timeStamp = System.currentTimeMillis();
                builder.setOpenStamp(timeStamp + "");
                serverList.add(builder.build());
            }
        }
        return serverList;
    }

    private UserEntity setInitUserInfo(int type, String param, String pwd, String phone, String idfa) {
        UserEntity userEntity = new UserEntity();
//        userEntity.setServerid(serverId);
        String uid = MyUtils.GenerateID();
        userEntity.setUserid(uid);
        userEntity.setOpenid(param);
        userEntity.setRoleuid(uid);
        userEntity.setType(type);
        userEntity.setPwd(pwd);
        userEntity.setPhone(phone);
        if (idfa != null) {
            userEntity.setIdfa(idfa);
        }
        long now = System.currentTimeMillis();
        userEntity.setRegistertime(now);
        int id = MySql.insertForCreateUser(userEntity);
        userEntity.setId(id);
        try {
            Map<String, String> userMap = MyUtils.convertBean(userEntity);
            userEntityToRedis(uid, userMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        UserdataEntity entity = MyUtils.getUserdata(now);
        entity.setRegisternums(entity.getRegisternums() + 1);
        MySql.update(entity);
        return userEntity;
    }

    public int judgeName(String name) {
        if (name.length() > 10) {
            return ProtoData.ErrorCode.NAMEERROR_VALUE;
        }
        StringBuffer sql = new StringBuffer("select uid from RoleEntity where name='").append(name).append("'");
        List<Object> uid = MySql.queryForList(sql.toString());
        if (uid.size() > 0) {
            return ProtoData.ErrorCode.NAMECREATED_VALUE;
        }
        return 0;
    }
    private ArrayList<UtilityInfo> setInitDress(String uid, int roleId) {
        ArrayList<UtilityInfo> dressList = new ArrayList<UtilityInfo>();
        for (int i = 4; i <= 6; i++) {
            DressEntity dressEntity = new DressEntity();
            dressEntity.setRoleid(roleId);
            dressEntity.setDressid(i);
            dressEntity.setUid(uid);
            dressEntity.setOverstamp("0");
            MySql.insert(dressEntity);
            UtilityInfo utilityInfo = new UtilityInfo();
            utilityInfo.setId(i);
            utilityInfo.setOverStamp("0");
            dressList.add(utilityInfo);
        }
        return dressList;
    }

    private void setCupboardToDB(String uid, int roleId, CupboardInfo cupboard) {
        PartEntity partEntity = new PartEntity();
        partEntity.setUid(uid);
        partEntity.setRoleid(roleId);
        partEntity.setCupboard(cupboard.getId());
        partEntity.setPart1(cupboard.getPart1());
        partEntity.setPart2(cupboard.getPart2());
        partEntity.setPart3(cupboard.getPart3());
        partEntity.setPart4(cupboard.getPart4());
        partEntity.setPart5(cupboard.getPart5());
        partEntity.setPart6(cupboard.getPart6());
        partEntity.setPart7(cupboard.getPart7());
        partEntity.setPart8(cupboard.getPart8());
        partEntity.setPart9(cupboard.getPart9());
        MySql.insert(partEntity);
    }

    public RoleInfo setInitRobotRoleInfo(String userId, String name) {
        RoleEntity roleEntity = new RoleEntity();
        String uid = MyUtils.GenerateID();
        roleEntity.setType(0);
        roleEntity.setUserid(userId);
        roleEntity.setUid(uid);
        //roleEntity.setAdvance(0);
        roleEntity.setDailyGameTime(0);
        roleEntity.setHead(0);
        roleEntity.setName(name);
        roleEntity.setLv((int) (Math.random() * 20) + 1);
        roleEntity.setExp(0);
        roleEntity.setBagmax(SuperConfig.INITBAGNUM);
        roleEntity.setAction(SuperConfig.getActionLimit());
        roleEntity.setActionstamp("0");
        roleEntity.setFirstRecharge(0);

        int id = MySql.insertForCreateRole(roleEntity);
        roleEntity.setId(id);
        RoleInfo roleInfo = new RoleInfo();
        roleInfo.setId(roleEntity.getId());
        roleInfo.setType(roleEntity.getType());
        roleInfo.setSex(roleEntity.getSex());
        roleInfo.setUserid(roleEntity.getUserid());
        roleInfo.setId(roleEntity.getId());
        roleInfo.setUid(roleEntity.getUid());
        //roleInfo.setAdvance(roleEntity.getAdvance());
        roleInfo.setDailyGameTime(roleEntity.getDailyGameTime());
        roleInfo.setHead(roleEntity.getHead());
        roleInfo.setName(roleEntity.getName());
        roleInfo.setLv(roleEntity.getLv());
        roleInfo.setBagmax(roleEntity.getBagmax());
        roleInfo.setActionstamp(roleEntity.getActionstamp());
        roleInfo.setRoleid(1);
        roleInfo.setFirstChargeRecord(roleEntity.getFirstRecharge());
        try {
            Map<String, String> roleMap = MyUtils.convertBean(roleEntity);
            roleEntityToRedis(roleInfo.getUid(), roleMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        IItem iItem = ItemDao.getInstance();
        Map<Integer, List<ItemInfo>> itemMap = iItem.initItemData(uid);
        roleInfo.setItemList(itemMap);

        List<RoleDressInfo> roleDressInfoList = new ArrayList<RoleDressInfo>();
        RoleDressInfo roleDressInfo = new RoleDressInfo();
        roleDressInfo.setUid(roleEntity.getUid());
        ArrayList<UtilityInfo> dressList = setInitDress(uid, 1);
        roleDressInfo.setDressList(dressList);
        List<CupboardInfo> cupboardInfoList = new ArrayList<CupboardInfo>();
       /* CupboardInfo cupboardInfo0 = new CupboardInfo();
        cupboardInfo0.setId(0);
        cupboardInfo0.setPart4(4);
        cupboardInfo0.setPart5(5);
        cupboardInfo0.setPart6(6);
        setCupboardToDB(uid, 1, cupboardInfo0);
        cupboardInfoList.add(cupboardInfo0);
        roleDressInfo.setCupboardList(cupboardInfoList);
        roleDressInfoList.add(roleDressInfo);
        roleInfo.setRoleList(roleDressInfoList);
        userDressToRedis(roleDressInfoList);*/

        List<MissionInfo> missionList = new ArrayList<MissionInfo>();
        /*roleInfo.setMissionList(missionList);*/
        List<MessageInfo> messageList = new ArrayList<MessageInfo>();
        IFriend iFriend = FriendDao.getInstance();
        String mid = MyUtils.setRandom();
        MessageInfo messageInfo = iFriend.addNewMessage(uid, 10, "", mid, TimerHandler.nowTimeStamp);
        messageList.add(messageInfo);
        roleInfo.setMessageList(messageList);
        List<FriendInfo> friendList = new ArrayList<FriendInfo>();
        roleInfo.setFriendList(friendList);
        Map<String, FriendInStationInfo> friendInStationInfoMap = new HashMap<String, FriendInStationInfo>();
        roleInfo.setFriendInStationList(friendInStationInfoMap);
        List<UtilityInfo> utilityList = new ArrayList<UtilityInfo>();
        roleInfo.setUtilityList(utilityList);
        ITask iTask = TaskDao.getInstance();
        List<TaskInfo> dailyTaskList = iTask.initDailyTask(uid);
        List<TaskInfo> achievementList = iTask.initAchievement(uid);
        List<TaskInfo> MainAchievementList = iTask.initMainAchievement(uid);
        List<ActivitiesInfo> activitiesList = iTask.initActivities(uid);
        roleInfo.setDailyTask(dailyTaskList);
        roleInfo.setAchievement(achievementList);
        roleInfo.setMainAchievement(MainAchievementList);
        roleInfo.setActivitiesList(activitiesList);
        tasksToRedis(roleEntity.getUid(), dailyTaskList);
        tasksToRedis(roleEntity.getUid(), achievementList);
        activitiesToRedis(roleEntity.getUid(), activitiesList);
        judgePublicMail(roleInfo);
        UserDataManager.setNewUserToRedis(id);
        return roleInfo;
    }

    public RoleInfo setInitRoleInfo(String userId, String name) {
        RoleEntity roleEntity = new RoleEntity();
        String uid = MyUtils.GenerateID();
        roleEntity.setType(0);
        roleEntity.setUserid(userId);
        roleEntity.setUid(uid);
        //roleEntity.setAdvance(0);
        roleEntity.setDailyGameTime(0);
        roleEntity.setHead(0);
        roleEntity.setName(name);
        roleEntity.setLv(1);
        roleEntity.setExp(0);
        roleEntity.setAction(0);
        roleEntity.setActionstamp("0");
        roleEntity.setBagmax(50);
        roleEntity.setTotalmoney(0);
        roleEntity.setFirstRecharge(0);

        int id = MySql.insertForCreateRole(roleEntity);
        roleEntity.setId(id);

        RoleInfo roleInfo = new RoleInfo();
        roleInfo.setId(roleEntity.getId());
        roleInfo.setType(roleEntity.getType());
        roleInfo.setUserid(roleEntity.getUserid());
        roleInfo.setId(roleEntity.getId());
        roleInfo.setUid(roleEntity.getUid());
        //roleInfo.setAdvance(roleEntity.getAdvance());
        roleInfo.setDailyGameTime(roleEntity.getDailyGameTime());

        roleInfo.setHead(roleEntity.getHead());
        roleInfo.setName(roleEntity.getName());
        roleInfo.setLv(roleEntity.getLv());
        roleInfo.setBagmax(roleEntity.getBagmax());
        roleInfo.setActionstamp(roleEntity.getActionstamp());
        roleInfo.setFirstChargeRecord(roleEntity.getFirstRecharge());

        IItem iItem = ItemDao.getInstance();
      /*  List<MessageInfo> messageList = new ArrayList<MessageInfo>();
        IFriend iFriend = FriendDao.getInstance();
        String mid = MyUtils.setRandom();
        MessageInfo messageInfo = iFriend.addNewMessage(uid, 10, "", mid, TimerHandler.nowTimeStamp);
        messageList.add(messageInfo);
        roleInfo.setMessageList(messageList);*/
/*
        List<FriendInfo> friendList = new ArrayList<FriendInfo>();
        roleInfo.setFriendList(friendList);
        Map<String, FriendInStationInfo> friendInStationInfoMap = new HashMap<String, FriendInStationInfo>();
        roleInfo.setFriendInStationList(friendInStationInfoMap);
        judgePublicMail(roleInfo);*/
        UserDataManager.setNewUserToRedis(id);


        //
        RoleAdditionalEntity entity = new RoleAdditionalEntity();
        entity.setUid(roleEntity.getUid());
        entity.setMarketFlushNums(0);
        MySql.mustInsert(entity);
        judgePublicMail(roleInfo);
        //新注册官方赠送宠物
//        PetUtils.createPet(uid,60,PetConfig.PetFromOfficial,5,2);
        return roleInfo;
    }

    public void setRoleToUser(RoleInfo roleInfo) {
        UserEntity userEntity = getUserInfoFromRedis(roleInfo.getUserid());
        if (userEntity.getRoleuid().equals("")) {
            userEntity.setRoleuid(roleInfo.getUid());
            Redis jedis = Redis.getInstance();
            jedis.hset("user:" + userEntity.getUserid(), "roleuid", roleInfo.getUid());
            MySql.update(userEntity);
        }
    }

    private String getUserIdFromRedis(int serverId, int type, String param, String pwd) {
        Redis jedis = Redis.getInstance();
        Iterator<String> iter = jedis.keys("user:*").iterator();
        String theOpenId = "";
        if (type == 0) {
            theOpenId = "openid";
        }
        while (iter.hasNext()) {
            String key = iter.next();

            String open = jedis.hget(key, theOpenId);
            if (open == null) {
                continue;
            }
            if (param.equals(open)) {
                String serverid1 = jedis.hget(key, "serverid");
                int serverid;
                if (serverid1 == null) {
                    continue;
                } else {
                    serverid = Integer.parseInt(serverid1);
                }
                if (type == 0 && !pwd.equals("@@")) {
                    String pwdSql = jedis.hget(key, "pwd");
                    if (pwdSql.equals("")) {
                        return "-2";
                    } else if (!pwdSql.equals(pwd)) {
                        return "-1";
                    }
                }
                if (serverid == serverId) {
                    String userId = jedis.hget(key, "userid");
                    return userId;
                }
            }
        }
        return null;
    }

    private UserEntity getUserInfoFromRedis(String uid) {
        Redis jedis = Redis.getInstance();
        Map<String, String> userMap = jedis.hgetAll("user:" + uid);
        UserEntity userEntity = null;
        if (userMap.size() > 0) {
            userEntity = new UserEntity();
            userEntity.setId(Integer.parseInt(userMap.get("id")));
            userEntity.setServerid(Integer.parseInt(userMap.get("serverid")));
            userEntity.setUserid(userMap.get("userid"));
            userEntity.setOpenid(userMap.get("openid"));
            userEntity.setRoleuid(userMap.get("roleuid"));
            userEntity.setPwd(userMap.get("pwd"));
            userEntity.setPhone(userMap.get("phone"));
            userEntity.setRegistertime(Long.parseLong(userMap.get("registertime")));
            userEntity.setLastlogin(Long.parseLong(userMap.get("lastlogin")));
        }
        return userEntity;
    }

    public RoleEntity getRoleEntityFromRedis(String uid) {
        RoleEntity roleEntity = null;
        try {
            Redis jedis = Redis.getInstance();
            Map<String, String> roleMap = jedis.hgetAll("role:" + uid);
            if (roleMap.size() > 0) {
                roleEntity = new RoleEntity();
                roleEntity.setId(Integer.parseInt(roleMap.get("id")));
                roleEntity.setType(Integer.parseInt(roleMap.get("type")));
                roleEntity.setUid(roleMap.get("uid"));
                roleEntity.setLv(Integer.parseInt(roleMap.get("lv")));
                roleEntity.setExp(Integer.parseInt(roleMap.get("exp")));
                roleEntity.setBagmax(Integer.parseInt(roleMap.get("bagmax")));
                roleEntity.setAction(Integer.parseInt(roleMap.get("action")));
                roleEntity.setActionstamp(roleMap.get("actionstamp"));
                roleEntity.setHead(Integer.parseInt(roleMap.get("head")));
                roleEntity.setName(roleMap.get("name"));
                //roleEntity.setAdvance(Integer.parseInt(roleMap.get("advance")));
                roleEntity.setDailyGameTime(Integer.parseInt(roleMap.get("dailyGameTime")));
                roleEntity.setUserid(roleMap.get("userid"));
                roleEntity.setTotalmoney(Integer.parseInt(roleMap.get("totalmoney")));
                roleEntity.setFirstRecharge(Integer.parseInt(roleMap.get("firstRecharge")));
            }
        } catch (Exception e) {
            e.printStackTrace();
            StringBuffer roleSql = new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
            roleEntity = (RoleEntity) MySql.queryForOne(roleSql.toString());
        }
        return roleEntity;
    }

    private Map<Integer, List<ItemInfo>> getItemInfoFromRedis(String uid) {
        Redis jedis = Redis.getInstance();
        Map<Integer, List<ItemInfo>> itemMap = new HashMap<Integer, List<ItemInfo>>();
        Iterator<String> iterator = jedis.keys("roleitem:" + uid + "*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            int type = Integer.parseInt(key.split("#")[1]);
            Map<String, String> map = jedis.hgetAll(key);
            List<ItemInfo> itemList = new ArrayList<ItemInfo>();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (!entry.getKey().equals("nowcell")) {
                    ItemInfo item = new ItemInfo();
                    item.setId(Integer.parseInt(entry.getKey()));
                    Map itemInfo = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, Integer.parseInt(entry.getKey()));
                    if (itemInfo == null || itemInfo.size() == 0) {
                        StringBuffer hql = new StringBuffer("delete ItemEntity where uid='").append(uid).append("' and itemid=")
                                .append(entry.getKey());
                        MySql.updateSomes(hql.toString());
                        continue;
                    }
                    item.setNum(Double.parseDouble(entry.getValue()));
                    itemList.add(item);
                }
            }
            itemMap.put(type, itemList);
        }
        return itemMap;
    }

    /* private List<MissionInfo> getMissionInfoFromRedis(String uid) {
         List<MissionInfo> missionList = new ArrayList<MissionInfo>();
         Redis jedis = Redis.getInstance();
         Map<String, String> map = jedis.hgetAll("rolemission:" + uid);
         IMission iMission = MissionDao.getInstance();
         for (int i = 1; i <= missionList.size(); i++) {
             MissionInfo missionInfo = iMission.getMissionInfo(map, i);
             if (missionInfo != null) {
                 missionList.add(missionInfo);
             }
         }
         return missionList;
     }
 */
    public CupboardInfo getCupboardInfo(List<CupboardInfo> cupboardList, int id) {
        CupboardInfo cupboardInfo = null;
        for (int i = 0; i < cupboardList.size(); i++) {
            CupboardInfo tmp = cupboardList.get(i);
            if (tmp.getId() == id) {
                cupboardInfo = tmp;
                break;
            }
        }
        return cupboardInfo;
    }

    //key格式：roledress:uid#roleid
    public RoleDressInfo getRoleDressFromRedis(String key) {
        Redis jedis = Redis.getInstance();
        Map<String, String> map = jedis.hgetAll(key);
        if (map.size() <= 0) {
            return null;
        }
        RoleDressInfo roleDressInfo = new RoleDressInfo();
        roleDressInfo.setId(Integer.parseInt(map.get("id")));
        roleDressInfo.setUid(map.get("uid"));
        roleDressInfo.setCupboardNum(Integer.parseInt(map.get("cupboardnum")));

        List<UtilityInfo> dressList = new ArrayList<UtilityInfo>();
        if (map.get("dresslist") != null && !map.get("dresslist").equals("") && !map.get("dresslist").equals("[]")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("dresslist"));
            int flag = 0;
            for (int i = 0; i < jsonArray.size(); i++) {
                UtilityInfo dress = (UtilityInfo) MyUtils.jsonToBean(jsonArray.getString(i), UtilityInfo.class);
                if (dress.getId() == 368) {
                    flag += 1;
                }
                if (dress.getId() == 368) {
                    flag += 2;
                }
                if (dress.getId() == 380) {
                    flag += 4;
                }

                dressList.add(dress);
            }
        }
        dressList = judgeDressList(roleDressInfo.getUid(), roleDressInfo.getId(), dressList);
        roleDressInfo.setDressList(dressList);

        List<CupboardInfo> cupboardList = new ArrayList<CupboardInfo>();
        if (map.get("cupboardlist") != null && !map.get("cupboardlist").equals("") && !map.get("cupboardlist").equals("[]")) {
            JSONArray jsonArray = JSONArray.fromObject(map.get("cupboardlist"));
            for (int i = 0; i < jsonArray.size(); i++) {
                CupboardInfo part = (CupboardInfo) MyUtils.jsonToBean(jsonArray.getString(i), CupboardInfo.class);
                part = judgeCupboardInDressList(roleDressInfo.getUid(), roleDressInfo.getId(), part, dressList);
                cupboardList.add(part);
            }
        }
        roleDressInfo.setCupboardList(cupboardList);

//        if (map.get("cupboardlist") != null && !map.get("cupboardlist").equals(""))
//        {
//            CupboardInfo lastDress = (CupboardInfo)MyUtils.jsonToBean(map.get("lastDress"),CupboardInfo.class);
//            roleDressInfo.setLastDress(lastDress);
//        }

        jedis.hset(key, "cupboardlist", MyUtils.objectToJson(cupboardList));
        return roleDressInfo;
    }

    private CupboardInfo judgeCupboardInDressList(String uid, int roleId, CupboardInfo cupboardInfo, List<UtilityInfo> dressList) {
        StringBuffer hql = new StringBuffer("update PartEntity set ");
        boolean needUpdate = false;
        int dressId = cupboardInfo.getPart1();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart1(0);
                hql.append("part1 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart2();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart2(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part2 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart3();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart3(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part3 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart4();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart4(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part4 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart5();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart5(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part5 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart6();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart6(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part6 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart7();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart7(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part7 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart8();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart8(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part8 = 0");
                needUpdate = true;
            }
        }
        dressId = cupboardInfo.getPart9();
        if (dressId != 0) {
            boolean bo = judgeDressIdInDressList(dressId, dressList);
            if (bo == false) {
                cupboardInfo.setPart9(0);
                if (needUpdate == true) {
                    hql.append(",");
                }
                hql.append("part9 = 0");
                needUpdate = true;
            }
        }
        if (needUpdate == true) {
            hql.append(" where uid = '").append(uid).append("' and roleid = ").append(roleId).append(" and cupboard = ").append(cupboardInfo.getId());
            MySql.updateSomes(hql.toString());
        }

        return cupboardInfo;
    }

    private boolean judgeDressIdInDressList(int dressId, List<UtilityInfo> dressList) {
        for (int i = 0; i < dressList.size(); i++) {
            if (dressId == dressList.get(i).getId()) {
                return true;
            }
        }
        return false;
    }

    private List<UtilityInfo> judgeDressList(String uid, int roleId, List<UtilityInfo> dressList) {
        for (int i = 0; i < dressList.size(); i++) {
            UtilityInfo dress = dressList.get(i);
            if (!dress.getOverStamp().equals("0") && TimerHandler.nowTimeStamp >= Long.parseLong(dress.getOverStamp())) {
                StringBuffer hql = new StringBuffer("delete from DressEntity")
                        .append(" where uid = '").append(uid).append("' and dressid = ").append(dress.getId());
                MySql.updateSomes(hql.toString());
                dressList.remove(i);
                i--;
            }
        }
        Redis jedis = Redis.getInstance();
        jedis.hset("roledress:" + uid + "#" + roleId, "dresslist", MyUtils.objectToJson(dressList));
        return dressList;
    }

    public List<RoleDressInfo> getUserAllDressFromRedis(String uid) {
        List<RoleDressInfo> roleDressInfos = new ArrayList<RoleDressInfo>();
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("roledress:" + uid + "*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            RoleDressInfo roleDressInfo = getRoleDressFromRedis(key);
//            List<UtilityInfo> dressList = judgeDressList(roleDressInfo.getUid(),roleDressInfo.getDressList());
            roleDressInfo.setDressList(roleDressInfo.getDressList());
            roleDressInfos.add(roleDressInfo);
        }
        return roleDressInfos;
    }

    public CupboardInfo getCurrentDressOnLine(String uid) {
        Redis jedis = Redis.getInstance();
        int roleId = Integer.parseInt(jedis.hget("role:" + uid, "roleid"));
        RoleDressInfo roleDressInfo = getRoleDressFromRedis("roledress:" + uid + "#" + roleId);
        CupboardInfo cupboardInfo = getCupboardInfo(roleDressInfo.getCupboardList(), 0);
        cupboardInfo.setRoleId(roleId);
        return cupboardInfo;
    }


    public List<TaskInfo> getTaskInfoFromRedis(String uid, int type) {
        List<TaskInfo> taskList = new ArrayList<TaskInfo>();
        Redis jedis = Redis.getInstance();
        Map<String, String> map = jedis.hgetAll("roletask:" + uid);
        long nowTimeStamp = 0;
        if (type == 1) {
            nowTimeStamp = System.currentTimeMillis();
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            int key = Integer.parseInt(entry.getKey());
            if (key >= 30000) {
                continue;
            }
            if ((type == 1 && key < 20000) || (type == 2 && key > 20000)) {
                TaskInfo taskInfo = (TaskInfo) MyUtils.jsonToBean(entry.getValue(), TaskInfo.class);
                if (type == 1 || taskInfo.getTaskId() == 20034) {
                    boolean bo = taskInfo.getTimeStamp() == null ? false : MyUtils.isOneDay(Long.parseLong(taskInfo.getTimeStamp()), nowTimeStamp);
                    if (bo == false) {
                        if (taskInfo.getTaskId() != 20034 && taskInfo.getStatus() != 1) {
                            StringBuffer hql = new StringBuffer("update TaskEntity set num = 0,status = 0,timestamp = '").append(nowTimeStamp)
                                    .append("' where uid = '").append(uid).append("' and taskid = ").append(taskInfo.getTaskId());
                            MySql.updateSomes(hql.toString());
                            taskInfo.setTaskNowNum(0);
                            taskInfo.setStatus(0);
                            taskInfo.setTimeStamp(nowTimeStamp + "");
                            jedis.hset("roletask:" + uid, taskInfo.getTaskId() + "", MyUtils.objectToJson(taskInfo));
                        }
                    }
                }
                taskList.add(taskInfo);
            }

        }
        return taskList;
    }

    public List<FriendInfo> getFriendInfoFromRedis(String uid) {
        List<FriendInfo> friendList = new ArrayList<FriendInfo>();
        Redis jedis = Redis.getInstance();
        Map<String, String> friendMap = jedis.hgetAll("rolefriends:" + uid);
        for (Map.Entry<String, String> entry : friendMap.entrySet()) {
            FriendInfo friendInfo = (FriendInfo) MyUtils.jsonToBean(entry.getValue(), FriendInfo.class);
            boolean needUpdate = false;
            if (friendInfo.getDailyScript() != 0) {
                boolean bo = MyUtils.isOneDay(Long.parseLong(friendInfo.getScriptStamp()), TimerHandler.nowTimeStamp);
                if (bo == false) {
                    needUpdate = true;
                    friendInfo.setDailyScript(0);
                    friendInfo.setScriptStamp("0");
                    StringBuffer sql = new StringBuffer("delete from PresentEntity where uid='").append(uid).append("' and friend = '").append(friendInfo.getUid()).append("' and type = 1");
                    MySql.updateSomes(sql.toString());
                }
            }
            if (friendInfo.getDailyItem() != 0) {
                boolean bo = MyUtils.isOneDay(Long.parseLong(friendInfo.getItemStamp()), TimerHandler.nowTimeStamp);
                if (bo == false) {
                    needUpdate = true;
                    friendInfo.setDailyItem(0);
                    friendInfo.setItemStamp("0");
                    StringBuffer sql = new StringBuffer("delete from PresentEntity where uid='").append(uid).append("' and friend = '").append(friendInfo.getUid()).append("' and type = 2");
                    MySql.updateSomes(sql.toString());
                }
            }
            if (needUpdate == true) {
                jedis.hset("rolefriends:" + uid, friendInfo.getId() + "", MyUtils.objectToJson(friendInfo));
            }

            friendInfo.setStatus(getRoleOnlineStatus(friendInfo.getUid()));
            friendList.add(friendInfo);
        }
        return friendList;
    }

    private List<MessageInfo> getMessageInfoFromRedis(String uid) {
        List<MessageInfo> messageList = new ArrayList<MessageInfo>();
        Redis jedis = Redis.getInstance();
        IFriend iFriend = FriendDao.getInstance();
        Map<String, String> messageMap = jedis.hgetAll("rolemessages:" + uid);
        for (Map.Entry<String, String> entry : messageMap.entrySet()) {
            MessageInfo messageInfo = (MessageInfo) MyUtils.jsonToBean(entry.getValue(), MessageInfo.class);
            if (messageInfo.getType() == 2) {
                continue;
            }
            messageInfo.setContent(messageInfo.getContent().replace("\\", ""));
            if (messageInfo.getStatus() != -1) {
                messageList.add(messageInfo);
                if (Long.parseLong(messageInfo.getTimeStamp()) + SuperConfig.MAILEXPIRE < TimerHandler.nowTimeStamp) {
                    iFriend.deleteMessage(uid, messageInfo.getMid());
                }
            }
        }
        return messageList;
    }


    private LoginJudgeAccount getUserAllInfoFromRedisForOpenId(int serverId, int type, String param, String pwd) {
        LoginJudgeAccount account = new LoginJudgeAccount();
        String userId = getUserIdFromRedis(serverId, type, param, pwd);
        if (userId != null) {
            if (userId.equals("-1")) {
                account.setErrorId(ProtoData.ErrorCode.ACCOUNTPWDERROR_VALUE);
            } else if (userId.equals("-2")) {
                account.setErrorId(ProtoData.ErrorCode.ACCOUNTPWDNOTEXIST_VALUE);
            } else {
                try {
                    UserEntity userEntity = getUserInfoFromRedis(userId);
                    RoleEntity roleEntity = getRoleEntityFromRedis(userEntity.getRoleuid());
                    if (roleEntity != null) {
                        //重新登录新的一天直接读取数据库信息，因为有些第二天刷新的功能走缓存很麻烦
                        if (userEntity != null) {
                            boolean isOneDay = MyUtils.isOneDay(userEntity.getLastlogin(), System.currentTimeMillis());
                            if (!isOneDay) {
                                account = getRoleAllInfoFromDB(serverId, type, param, pwd);
                                return account;
                            }
                        }
                        RoleInfo roleInfo = new RoleInfo();
                        roleInfo.setId(roleEntity.getId());
                        roleInfo.setType(roleEntity.getType());
                        roleInfo.setUserid(roleEntity.getUserid());
                        roleInfo.setUid(roleEntity.getUid());
                        //roleInfo.setAdvance(roleEntity.getAdvance());
                        roleInfo.setDailyGameTime(roleEntity.getDailyGameTime());
                        roleInfo.setHead(roleEntity.getHead());
                        roleInfo.setName(roleEntity.getName());
                        roleInfo.setLv(roleEntity.getLv());
                        roleInfo.setBagmax(roleEntity.getBagmax());
                        roleInfo.setActionstamp(roleEntity.getActionstamp());
                        Map<Integer, List<ItemInfo>> itemList = getItemInfoFromRedis(roleInfo.getUid());
                        roleInfo.setItemList(itemList);

                        List<RoleDressInfo> roleDressInfos = getUserAllDressFromRedis(roleInfo.getUid());

                        roleInfo.setRoleList(roleDressInfos);
                        roleInfo.setDailyLoginflag(roleEntity.getDailyLoginflag());
                        roleInfo.setDailyLoginLoop(roleEntity.getDailyLoginLoop());
                        roleInfo.setFirstRecharge(roleEntity.getFirstRecharge());
                     /*  List<MissionInfo> missionList = getMissionInfoFromRedis(roleInfo.getUid());
                     roleInfo.setMissionList(missionList);*/

                        List<TaskInfo> dailyTaskList = getTaskInfoFromRedis(roleInfo.getUid(), 1);
                        List<TaskInfo> achievement = getTaskInfoFromRedis(roleInfo.getUid(), 2);


                        List<FriendInfo> friendList = getFriendInfoFromRedis(roleInfo.getUid());
                        roleInfo.setFriendList(friendList);
                        Map<String, FriendInStationInfo> friendInStationList = getStationFriends(roleInfo.getUid(), roleInfo.getStation(), friendList);
                        roleInfo.setFriendInStationList(friendInStationList);

                        List<MessageInfo> messageList = getMessageInfoFromRedis(roleInfo.getUid());
                        roleInfo.setMessageList(messageList);

                        account.setRoleInfo(roleInfo);
                    }

                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                }
            }
        }

        return account;
    }

    private LoginJudgeAccount getUserInfoFromDBForOpenId(int type, String param, String pwd) {
        LoginJudgeAccount account = new LoginJudgeAccount();
        UserEntity userEntity = null;
        String theOpenId = "";
//        if (type == 0) {
        theOpenId = "openid";
//        }
        StringBuffer sql = new StringBuffer("from UserEntity where ").append(theOpenId).append("='").append(param).append("'");
        Object object = MySql.queryForOne(sql.toString());
        if (object != null) {
            long now = System.currentTimeMillis();
            userEntity = (UserEntity) object;
            long lastlogin = userEntity.getLastlogin();
            userEntity.setLastlogin(now);
            userEntity.setPrelogin(lastlogin);
            MySql.update(userEntity);
            if (type == 0 && !pwd.equals("@@")) {
                if (userEntity.getPwd().equals("")) {
                    account.setErrorId(ProtoData.ErrorCode.ACCOUNTPWDNOTEXIST_VALUE);
                    return account;
                } else if (!userEntity.getPwd().equals(pwd)) {
                    account.setErrorId(ProtoData.ErrorCode.ACCOUNTPWDERROR_VALUE);
                    return account;
                }
            }
            boolean isOneDay;
            if (lastlogin == 0) {
                isOneDay = false;
            } else {
                isOneDay = MyUtils.isOneDay(lastlogin, now);
            }
            if (!isOneDay) {
                UserdataEntity entity = MyUtils.getUserdata(now);
                entity.setLoginnums((entity.getLoginnums() + 1));
                MySql.update(entity);
                int interval;
                if (userEntity.getRegistertime() == 0) {
                    interval = -1;
                } else {
                    interval = MyUtils.getDayInterval(userEntity.getRegistertime(), now);
                }
                if (interval == 1) {
                    entity.setNext(entity.getNext() + 1);
                    userEntity.setWeekflag(1);
                    MySql.update(userEntity);
                    MySql.update(entity);
                } else if (interval > 1 && interval < 7 && userEntity.getWeekflag() == 0) {
                    entity.setWeek(entity.getWeek() + 1);
                    userEntity.setWeekflag(1);
                    MySql.update(userEntity);
                    MySql.update(entity);
                }

            }
//每月
        } else {
            if (type == 0 && !pwd.equals("@@")) {
                account.setErrorId(ProtoData.ErrorCode.ACCOUNTNAMEERROR_VALUE);
                return account;
            } else {
                userEntity = setInitUserInfo(type, param, "", "1", "0");
            }
        }
        try {
            Map<String, String> userMap = MyUtils.convertBean(userEntity);
            userEntityToRedis(userEntity.getUserid(), userMap);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        account.setUserEntity(userEntity);
        return account;
    }

    public void getRoleEntityFromDB(String uid, CallBack callBack, int type) {
        StringBuffer sql = new StringBuffer(" from RoleEntity where uid='").append(uid).append("'");
        MySql.queryInSql(sql.toString(), callBack, type);
    }

    private RoleEntity getRoleEntityFromDB(String userId, int serverId) {
        RoleEntity roleEntity = null;
        StringBuffer sql = new StringBuffer(" from RoleEntity where userid='").append(userId).append("'");
        Object object = MySql.queryForOne(sql.toString());
        if (object != null) {
            roleEntity = (RoleEntity) object;
            StringBuilder sbu = new StringBuilder("from RoleAdditionalEntity where uid='").append(roleEntity.getUid()).append("'");
            RoleAdditionalEntity entity = (RoleAdditionalEntity) MySql.queryForOne(sbu.toString());
            if (entity == null) {
                ///     /// System.err.println("~!#$%"+entity);
                entity = new RoleAdditionalEntity();
                entity.setUid(roleEntity.getUid());
                entity.setMarketFlushNums(0);
                MySql.mustInsert(entity);

            }
        }


        return roleEntity;
    }

    private RoleEntity getRoleEntityFromDB(String uid) {
        RoleEntity roleEntity = null;
        StringBuffer sql = new StringBuffer(" from RoleEntity where uid='").append(uid).append("'");
        Object object = MySql.queryForOne(sql.toString());
        if (object != null) {
            roleEntity = (RoleEntity) object;
        }
        return roleEntity;
    }

    public void getRoleEntityFromDBById(int id, CallBack callBack, int type) {
        StringBuffer sql = new StringBuffer(" from RoleEntity where id=").append(id);
        MySql.queryInSql(sql.toString(), callBack, type);
    }

//    public RoleEntity getRoleEntityFromDBById(int id){
//        RoleEntity roleEntity = null;
//        StringBuffer sql = new StringBuffer(" from RoleEntity where id=").append(id);
//        Object object = MySql.queryForOne(sql.toString());
//        if (object != null) {
//            roleEntity = (RoleEntity) object;
//        }
//        return roleEntity;
//    }

    public LoginJudgeAccount getRoleAllInfoFromDB(int serverId, int type, String param, String pwd) {
        RoleInfo roleInfo = null;
        LoginJudgeAccount account = getUserInfoFromDBForOpenId(type, param, pwd);
        if (account.getErrorId() != 0) {
            return account;
        }
        UserEntity userEntity = account.getUserEntity();
        RoleEntity roleEntity = getRoleEntityFromDB(userEntity.getUserid(), serverId);
        if (roleEntity != null) {
            roleInfo = new RoleInfo();
            roleInfo.setId(roleEntity.getId());
            roleInfo.setType(roleEntity.getType());
            roleInfo.setUserid(roleEntity.getUserid());
            roleInfo.setUid(roleEntity.getUid());
            //roleEntity.setAdvance(0);
            roleInfo.setDailyGameTime(roleEntity.getDailyGameTime());
            roleInfo.setHead(roleEntity.getHead());
            roleInfo.setName(roleEntity.getName());
            roleInfo.setLv(roleEntity.getLv());
            roleInfo.setBagmax(50);
            roleInfo.setDailyLoginLoop(roleEntity.getDailyLoginLoop());
            roleInfo.setDailyLoginflag(roleEntity.getDailyLoginflag());
            roleInfo.setTotalMoney(roleEntity.getTotalmoney());
            roleInfo.setFirstRecharge(roleEntity.getFirstRecharge());
            try {
                Map<String, String> roleMap = MyUtils.convertBean(roleEntity);
                roleEntityToRedis(roleInfo.getUid(), roleMap);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }


            List<MessageInfo> messageList = getMessageInfoFromDB(roleInfo.getUid());
            messageList = messagesToRedis(roleInfo.getUid(), messageList);
            account.setRoleInfo(roleInfo);
        }
        return account;
    }

    private List<MessageInfo> getMessageInfoFromDB(String uid) {
        List<MessageInfo> messageList = new ArrayList<MessageInfo>();
        StringBuffer sql = new StringBuffer(" from MessageEntity where uid='").append(uid).append("' or type=20 or type=23");
        List<Object> objectList = MySql.queryForList(sql.toString());
        if (objectList != null) {
            for (int i = 0; i < objectList.size(); i++) {
                MessageEntity messageEntity = (MessageEntity) objectList.get(i);
                MessageInfo messageInfo = new MessageInfo();
                messageInfo.setId(messageEntity.getId());
                messageInfo.setType(messageEntity.getType());
                messageInfo.setMid(messageEntity.getMid());
                String content = messageEntity.getContent();
                messageInfo.setContent(content);
                messageInfo.setTimeStamp(messageEntity.getTimestamp());
                messageInfo.setStatus(messageEntity.getStatus());
                messageList.add(messageInfo);
            }
        }
        return messageList;
    }


    private List<MessageInfo> messagesToRedis(String uid, List<MessageInfo> messageList) {
        List<MessageInfo> list = new ArrayList<MessageInfo>();
        if (messageList.size() == 0) return list;
        Redis jedis = Redis.getInstance();
        IFriend iFriend = FriendDao.getInstance();
        Map<String, String> map = new HashMap<String, String>();
        for (int i = 0; i < messageList.size(); i++) {
            MessageInfo messageInfo = messageList.get(i);
            //新手引导的那个邮件不能删除
            if (messageInfo.getType() != 30 && Long.parseLong(messageInfo.getTimeStamp()) + SuperConfig.MAILEXPIRE < TimerHandler.nowTimeStamp) {
                iFriend.deleteMessageSql(uid, messageInfo.getMid());
                continue;
            }
            map.put(messageInfo.getMid() + "", MyUtils.objectToJson(messageInfo));
            if (messageInfo.getStatus() != -1) {
                list.add(messageInfo);
            }
        }
        if (map.size() > 0) {
            jedis.hmset("rolemessages:" + uid, map);
        }

        return list;
    }


    public void itemsToRedis(String uid, Map<Integer, List<ItemInfo>> itemMap) {
        Redis jedis = Redis.getInstance();
        for (Map.Entry<Integer, List<ItemInfo>> entry : itemMap.entrySet()) {
            int key = entry.getKey();
            Map<String, String> map = new HashMap<String, String>();
            int cell = 0;
            for (int i = 0; i < entry.getValue().size(); i++) {
                ItemInfo item = entry.getValue().get(i);
                if (key >= 1 && key <= 3) {
                    String itemInfo = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, item.getId() + "", "group_max");
                    if (StringUtils.isEmpty(itemInfo)) {
                        continue;
                    }
                    int oneMax = Integer.parseInt(itemInfo);

                    int num = (int) Math.ceil(item.getNum() / oneMax);
                    cell += num;
                }
                map.put(item.getId() + "", item.getNum() + "");
            }
            if (key >= 1 && key <= 3) {
                map.put("nowcell", cell + "");
            }
            jedis.hmset("roleitem:" + uid + "#" + key, map);
        }
    }

    private void missionsToRedis(String uid, Map<Integer, List<MissionEntity>> missionMap) {
        if (missionMap.size() == 0) return;
        Redis jedis = Redis.getInstance();
        for (Map.Entry<Integer, List<MissionEntity>> entry : missionMap.entrySet()) {
            StringBuffer key = new StringBuffer("rolemission:").append(uid).append("#").append(entry.getKey());
            Map rolemissionMap = new HashMap<String, String>();
            List missionList = entry.getValue();
            for (int i = 0; i < missionList.size(); i++) {
                MissionEntity entity = (MissionEntity) missionList.get(i);
                int missionkey = entity.getMissionid();
                int missionValue = entity.getNums();
                rolemissionMap.put(missionkey + "", missionValue + "");
            }
            jedis.hmset(key.toString(), rolemissionMap);
        }
    }

    private void roleDressToRedis(String uid, int roleId, Map<String, String> map) {
        Redis jedis = Redis.getInstance();
        jedis.hmset("roledress:" + uid + "#" + roleId, map);
    }

    private void userDressToRedis(List<RoleDressInfo> dressList) {
        for (int i = 0; i < dressList.size(); i++) {
            RoleDressInfo roleDressInfo = dressList.get(i);
            Map<String, String> map = new HashMap<String, String>();
            map.put("uid", roleDressInfo.getUid());
            map.put("id", roleDressInfo.getId() + "");
            map.put("cupboardnum", roleDressInfo.getCupboardNum() + "");
            map.put("dresslist", MyUtils.objectToJson(roleDressInfo.getDressList()));
            map.put("cupboardlist", MyUtils.objectToJson(roleDressInfo.getCupboardList()));
            roleDressToRedis(roleDressInfo.getUid(), roleDressInfo.getId(), map);
        }
    }

    private void tasksToRedis(String uid, List<TaskInfo> taskList) {
        if (taskList.size() == 0) return;
        Redis jedis = Redis.getInstance();
        Map<String, String> map = new HashMap<String, String>();
        for (int i = 0; i < taskList.size(); i++) {
            TaskInfo taskInfo = taskList.get(i);
            map.put(taskInfo.getTaskId() + "", MyUtils.objectToJson(taskInfo));
        }
        jedis.hmset("roletask:" + uid, map);

    }

    private void activitiesToRedis(String uid, List<ActivitiesInfo> activitiesList) {
        Redis jedis = Redis.getInstance();
        String key = "roleactivities:" + uid;
        for (int i = 0; i < activitiesList.size(); i++) {
            ActivitiesInfo activitiesInfo = activitiesList.get(i);
            jedis.hset(key, activitiesInfo.getActivitiesId() + "", MyUtils.objectToJson(activitiesInfo));
        }
    }


    private void userEntityToRedis(String uid, Map<String, String> userMap) {
        Redis jedis = Redis.getInstance();
        jedis.hmset("user:" + uid, userMap);
    }

    private void roleEntityToRedis(String uid, Map<String, String> roleMap) {
        Redis jedis = Redis.getInstance();
//        String jump_missionid = jedis.hget("headball:" + uid, "jump_missionid");
//        if(jump_missionid==""||jump_missionid==null){
//            roleMap.put("headballmissionid", "0");
//        }else{
//            roleMap.put("headballmissionid", jump_missionid);
//        }
        jedis.hmset("role:" + uid, roleMap);
    }

    public int register(String account, String pwd, String phone, String idfa) {
        if (pwd.length() > 12) {
            return ProtoData.ErrorCode.LENGTHERROR_VALUE;
        }
        StringBuffer sql = new StringBuffer(" from UserEntity where openid='").append(account).append("'");
        Object object = MySql.queryForOne(sql.toString());
        if (object != null) {
            UserEntity userEntity = (UserEntity) object;
            if (userEntity.getPwd().equals("")) {
                StringBuffer sql2 = new StringBuffer("update UserEntity set pwd = '").append(pwd)
                        .append("' where openid='").append(account).append("'");//
                MySql.updateSomes(sql2.toString());
            } else {
                return ProtoData.ErrorCode.ACCOUNTEXIST_VALUE;
            }

        } else {
            setInitUserInfo(0, account, pwd, phone, idfa);
        }
        return 0;
    }

    public int visitorRegister(String account, String pwd, String phone, String idfa) {
        if (pwd.length() > 12) {
            return ProtoData.ErrorCode.LENGTHERROR_VALUE;
        }
        StringBuffer sql = new StringBuffer(" from UserEntity where openid='").append(account).append("'");
        Object object = MySql.queryForOne(sql.toString());
        if (object != null) {
            UserEntity userEntity = (UserEntity) object;
            if (userEntity.getPwd().equals("")) {
                StringBuffer sql2 = new StringBuffer("update UserEntity set pwd = '").append(pwd)
                        .append("' where openid='").append(account).append("'");//
                MySql.updateSomes(sql2.toString());
            } else {
                return ProtoData.ErrorCode.ACCOUNTEXIST_VALUE;
            }

        } else {
            UserEntity userEntity = new UserEntity();
//        userEntity.setServerid(serverId);
            String uid = MyUtils.GenerateID();
            userEntity.setUserid(uid);
            userEntity.setOpenid(account);
            userEntity.setRoleuid(uid);
            userEntity.setType(0);
            userEntity.setPwd(pwd);
            userEntity.setPhone(phone);
            userEntity.setVisitor(1);
            userEntity.setIdfa(idfa);
            long now = System.currentTimeMillis();
            userEntity.setRegistertime(now);
            int id = MySql.insertForCreateUser(userEntity);
            userEntity.setId(id);
            try {
                Map<String, String> userMap = MyUtils.convertBean(userEntity);
                userEntityToRedis(uid, userMap);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            UserdataEntity entity = MyUtils.getUserdata(now);
            entity.setRegisternums(entity.getRegisternums() + 1);
            MySql.update(entity);
        }
        return 0;
    }


    public LoginJudgeAccount getLoginRoleInfo(int serverId, int type, String param, String pwd) {
        LoginJudgeAccount account = getRoleAllInfoFromDB(serverId, type, param, pwd);
        RoleInfo roleInfo = account.getRoleInfo();
        if (account.getErrorId() != 0) {
            return account;
        }
        if (roleInfo != null) {
            judgePublicMail(roleInfo);
        }
        return account;
    }

    //推送邮件
    private void judgePublicMail(RoleInfo roleInfo) {
        Redis jedis = Redis.getInstance();
        IFriend iFriend = FriendDao.getInstance();
        Iterator<String> iterator = jedis.keys("mailinfo*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> mailMap = jedis.hgetAll(key);
            MailInfo mailInfo = iFriend.mapToMailInfo(mailMap);
            //群发邮件type = all  status = 0 未读
            if (mailInfo.getType().equals("all") && mailInfo.getStatus() == 0) {
                // if (mailInfo.getType().equals("all") && mailInfo.getStatus() == 1) {
                String mail = jedis.hget("rolemessages:" + roleInfo.getUid(), mailInfo.getMid());
                if (mail == null) {
                    MessageInfo messageInfo = new MessageInfo();
                    messageInfo.setId(-1);
                    messageInfo.setType(mailInfo.getMailId());
                    messageInfo.setMid(mailInfo.getMid());
                    messageInfo.setContent(mailInfo.getContent());
                    messageInfo.setTimeStamp(mailInfo.getPublishTime() + "");
                    messageInfo.setStatus(0);
                    //获取消息列表
                    roleInfo.getMessageList().add(messageInfo);
                    iFriend.addNewMessage(roleInfo.getUid(), mailInfo.getMailId(), mailInfo.getContent(), mailInfo.getMid(), mailInfo.getPublishTime());
                }
            }
        }
    }

    private String judgeMonthStamp(String uid) {
        Redis jedis = Redis.getInstance();
        String monthStamp = jedis.hget("role:" + uid, "monthstamp");
        if (!monthStamp.equals("0")) {
            if (Long.parseLong(monthStamp) < TimerHandler.nowTimeStamp) {
                monthStamp = "0";
                jedis.hset("role:" + uid, "monthstamp", monthStamp);
                StringBuffer hql = new StringBuffer("update RoleEntity set monthstamp = '0'")
                        .append(" where uid = '").append(uid).append("'");
                MySql.updateSomes(hql.toString());
            }
        }
        return monthStamp;
    }

    public void setFirstRecharge(String uid) {
        int firstRecharge = getFirstRecharge(uid);
        firstRecharge += 1;

        Redis jedis = Redis.getInstance();

        jedis.hset("role:" + uid, "firstRecharge", firstRecharge + "");
        StringBuffer hql = new StringBuffer("update RoleEntity set firstRecharge = " + firstRecharge)
                .append(" where uid = '").append(uid).append("'");

        // /// System.out.println("setFirstRecharge " + hql );
        MySql.updateSomes(hql.toString());
    }

    public int getFirstRecharge(String uid) {
        Redis jedis = Redis.getInstance();

        String stringfirstRecharge = jedis.hget("role:" + uid, "firstRecharge");
        if (stringfirstRecharge == null) return 0;
        int firstRecharge = Integer.parseInt(stringfirstRecharge);

        return firstRecharge;
    }

    public UserData.Role roleInfoToUserData(RoleInfo roleInfo) {
        if (roleInfo == null) return null;
        UserData.Role.Builder builder = UserData.Role.newBuilder();
        builder.setId(roleInfo.getId());
        builder.setUid(roleInfo.getUid());
        builder.setHead(roleInfo.getHead());
        builder.setName(roleInfo.getName());
        builder.setLv(roleInfo.getLv());
        builder.setBagMax(roleInfo.getBagmax());
        builder.setActionStamp("0");
        builder.setFirstRecharge(roleInfo.getFirstRecharge());

        StringBuffer hql = new StringBuffer(" from UserEntity where userid='").append(roleInfo.getUserid()).append("'");
        UserEntity userEntity = (UserEntity) MySql.queryForOne(hql.toString());
        Redis jedis = Redis.getInstance();
        int signNum = roleInfo.getSignin();
//        builder.setChapter(roleInfo.getChapter());

  /*      for (int i = 0 ; i<roleInfo.getMissionList().size() ; i++){
          MissionInfo missionInfo = roleInfo.getMissionList().get(i);
           MissionData.Mission.Builder missionBu = MissionData.Mission.newBuilder();
          missionBu.setType(missionInfo.getType());
         *//*  missionBu.addAllId(missionInfo.getList());*//*
   //        builder.addMissionList(missionBu);
       }*/
        boolean isOneDay = MyUtils.isOneDay(userEntity.getLastlogin(), userEntity.getPrelogin());
        //   StringBuffer sql = new StringBuffer("from UserEntity where userid='").append(roleInfo.getUserid()).append("'");
        //  UserEntity userEntity =(UserEntity)MySql.queryForOne(sql.toString());
        StringBuffer updateSql = new StringBuffer("update UserEntity set lastlogin=").append(TimerHandler.nowTimeStamp)
                .append(", prelogin=").append(userEntity.getLastlogin()).append(" where userid='").append(userEntity.getUserid()).append("'");
        MySql.updateSomes(updateSql.toString());
        // StringBuffer userSql=new StringBuffer("from UserEntity where userid='").append(roleInfo.getUserid()).append("'");
        //   UserEntity userEntity=(UserEntity)MySql.queryForOne(sql.toString());
        if (userEntity.getAuthentication() == 0) {
            builder.setIsneedauthentication(true);
        } else {
            builder.setIsneedauthentication(false);
        }
        List itemInfoslist = ItemDao.getInstance().getplayerItem(roleInfo.getUid());
        if (!isOneDay) {
            int dailyLoginflag = roleInfo.getDailyLoginflag();
            int dailyLoginLoop = roleInfo.getDailyLoginLoop();
            ++dailyLoginflag;
            int awardItemConfigId = dailyLoginflag;
            if (dailyLoginflag > 30) {
                dailyLoginflag = 1;
                dailyLoginLoop++;
                awardItemConfigId = dailyLoginflag;
            } else if (dailyLoginflag == 30) {
                awardItemConfigId = 30 + dailyLoginLoop;
            }
//             System.err.println(dailyLoginLoop+"第二天"+dailyLoginflag+"awardItemConfigId"+awardItemConfigId);
            try {
                SignConfig signConfig = (SignConfig) SuperConfig.getCongifObject(SuperConfig.signConfig, awardItemConfigId);
                int itemId = signConfig.getAwardItemId();
                int itemNum = signConfig.getAwardItemNums();
                  ItemUtils.updatePlayerItems(roleInfo.getUid(),itemId,itemNum,true);
            } catch (Exception e) {
                e.printStackTrace();
            }
            builder.setDailyLoginflag(dailyLoginflag);
            builder.setDailyLoginLoop(dailyLoginLoop);
            StringBuffer resetApproval = new StringBuffer("update RoleEntity set lvapproval=0, endlessapproval=0,missionapproval=0, dressapproval=0 ,gamecopy1=0 , gamecopy2=0 ,dailyLoginflag=").append(dailyLoginflag).append(",dailyLoginLoop=").append(dailyLoginLoop).append(",dailyGameTime=0 where uid='")
                    .append(roleInfo.getUid()).append("'");
            MySql.updateSomes(resetApproval.toString());
            String key = "role:" + roleInfo.getUid();
            jedis.hset(key, "dailyGameTime", "0");
            builder.setIsSign(true);
            StringBuilder stringBuilder = new StringBuilder("from RoleAdditionalEntity where uid='").append(roleInfo.getUid()).append("'");
            Object object = MySql.queryForOne(stringBuilder.toString());
            if (object != null) {
                RoleAdditionalEntity entity = (RoleAdditionalEntity) object;
                List<Integer> list = ItemService.reflushBlackMarket();
                ItemData.GoodsInfos.Builder goodsInfosBu = ItemData.GoodsInfos.newBuilder();
                for (Integer id : list) {
                    ItemData.GoodsInfo.Builder goodsBu = ItemData.GoodsInfo.newBuilder();
                    goodsBu.setId(id);
                    goodsBu.setIsSellOut(false);
                    goodsInfosBu.addGoodsInfo(goodsBu);
                }
                byte[] goodsInfoByteArray = goodsInfosBu.build().toByteArray();
                entity.setBlackMarket(goodsInfoByteArray);
                entity.setMarketFlushNums(0);
                MySql.update(entity);
            }
        } else {
//            int dailyLoginflag = roleInfo.getDailyLoginflag();
//            int dailyLoginLoop = roleInfo.getDailyLoginLoop();
//            ++dailyLoginflag;
//            int awardItemConfigId = dailyLoginflag;
//            if (dailyLoginflag > 30) {
//                dailyLoginflag = 1;
//                dailyLoginLoop++;
//                awardItemConfigId = dailyLoginflag;
//            } else if (dailyLoginflag == 30) {
//                awardItemConfigId = 30 + dailyLoginLoop;
//            }
//            try {
//                SignConfig  signConfig = (SignConfig) SuperConfig.getCongifObject(SuperConfig.signConfig, awardItemConfigId);
//                int itemId = signConfig.getAwardItemId();
//                int itemNum = signConfig.getAwardItemNums();
//                ItemUtils.updatePlayerItems(roleInfo.getUid(),itemId,itemNum,true);
//                System.out.println(itemNum+"-------------------------------");
//            } catch (ResourceNotFound resourceNotFound) {
//                resourceNotFound.printStackTrace();
//            }

            builder.setDailyLoginflag(roleInfo.getDailyLoginflag());
            builder.setDailyLoginLoop(roleInfo.getDailyLoginLoop());
//            System.err.println(roleInfo.getDailyLoginflag()+"今天"+roleInfo.getDailyLoginLoop());
            builder.setIsSign(false);
        }
        //邮件全新版本
        MailDao mailDao = MailDao.getInstance();
        StringBuilder stringBuilder = new StringBuilder("from MailEntity where owner='").append(roleInfo.getUid()).append("'");
        Date mdate = new Date();
        SimpleDateFormat msimpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        List<Object> mailEntities = MySql.queryForList(stringBuilder.toString());
        for (int i = 0; i < mailEntities.size(); i++) {
            MailEntity mailEntity = (MailEntity) mailEntities.get(i);
            //long current = Long.parseLong(MyUtils.dateToStampCompletes(msimpleDateFormat.format(mdate))) / 1000;
            //long overdue = Long.parseLong(MyUtils.dateToStampCompletes(String.valueOf(mailEntity.getOverdueTime()))) / 1000;
            //System.err.println("overdue:::::::::::::::::::" + overdue);
            //System.err.println("current:::::::::::::::::::" + new Date().getTime());
            //System.err.println("database overdue time:::::::::::::::::::" + mailEntity.getOverdueTime());
            if ((mailEntity.getOverdueTime() - System.currentTimeMillis()) < 0) {
                //System.err.println("Delete:::::::::::::::::::::::::::::::");
                MySql.delete(mailEntity);
            } else {
                //System.err.println("Day::::::::::::::::::::::" + (mailEntity.getOverdueTime() - System.currentTimeMillis()) / 1000 / 60 / 60 / 24);
                mailEntity.setOverdueTime((mailEntity.getOverdueTime() - System.currentTimeMillis()) / 1000 / 60 / 60 / 24);
                builder.addMails(mailDao.entityToPb(mailEntity));
            }
        }
        ItemData.Bag.Builder bagBu = ItemData.Bag.newBuilder();
        bagBu.setType(1);
        ItemData.Bag.Builder bagBu0 = ItemData.Bag.newBuilder();
        bagBu0.setType(0);
        for (int i = 0; i < itemInfoslist.size(); i++) {

            ItemEntity entity = (ItemEntity) itemInfoslist.get(i);
            if (entity.getType() == 1) {
                bagBu.addItem(ItemUtils.entityToPBData(entity));
            } else if (entity.getType() == 0) {
                bagBu0.addItem(ItemUtils.entityToPBData(entity));
            }

        }
        builder.addItemList(bagBu);
        builder.addItemList(bagBu0);
        ///// System.err.println("!!!!!!!!!"+JsonFormat.printToString(builder.build()));
        builder.setIsneedauthentication(false);
        ///临时运行通过
        builder.setAdvance(0);
        return builder.build();
    }


    public Map<String, FriendInStationInfo> getStationFriends(String uid, int stationId, List<FriendInfo> friendInfoList) {
        List<FriendInStationInfo> friendInStationInfoList = new ArrayList<FriendInStationInfo>();
        List<StationInfo> list = SuperServerHandler.stationMap.get(stationId);
        Map<ChannelHandlerContext, FriendInfo> map = new HashMap<ChannelHandlerContext, FriendInfo>();
        for (int i = 0; i < friendInfoList.size(); i++) {
            FriendInfo friendInfo = friendInfoList.get(i);
            ChannelHandlerContext ctx = SuperServerHandler.getCtxFromUid(friendInfo.getUid());
            if (ctx != null) {
                map.put(ctx, friendInfo);
            }
        }
        if (map != null && map.size() != 0 && list != null) {
            for (int i = 0; i < list.size(); i++) {
                if (map.size() == 0) {
                    break;
                }
                StationInfo stationInfo = list.get(i);
                FriendInfo friendInfo = map.remove(stationInfo.getCtx());
                if (friendInfo != null) {
                    FriendInStationInfo friendInStationInfo = new FriendInStationInfo();
                    friendInStationInfo.setFriendUid(friendInfo.getUid());
                    friendInStationInfo.setValue(friendInfo.getValue());
                    friendInStationInfoList.add(friendInStationInfo);
                }
            }
        }
        if (friendInStationInfoList.size() > SuperConfig.STATIONTOTALROLE) {
            Collections.sort(friendInStationInfoList, new Comparator() {
                public int compare(Object o1, Object o2) {
                    FriendInStationInfo f1 = (FriendInStationInfo) o1;
                    FriendInStationInfo f2 = (FriendInStationInfo) o2;
                    return f2.getValue() - f1.getValue();
                }
            });
            while (friendInStationInfoList.size() > SuperConfig.STATIONTOTALROLE) {
                friendInStationInfoList.remove(friendInStationInfoList.size() - 1);
            }
        }
        Redis jedis = Redis.getInstance();
        Map<String, FriendInStationInfo> resultMap = new HashMap<String, FriendInStationInfo>();
        Map<String, String> updateMap = new HashMap<String, String>();
        for (int i = 0; i < friendInStationInfoList.size(); i++) {
            FriendInStationInfo friendInStationInfo = friendInStationInfoList.get(i);
            resultMap.put(friendInStationInfo.getFriendUid(), friendInStationInfo);
            String string = MyUtils.objectToJson(friendInStationInfo);
            updateMap.put(friendInStationInfo.getFriendUid(), string);
        }
        jedis.del("rolefriendinstation:" + uid);
        if (updateMap.size() > 0) {
            jedis.hmset("rolefriendinstation:" + uid, updateMap);
        }
        return resultMap;
    }


    public int getStationId(String uid) {
        int station = -1;
        for (Map.Entry<Integer, List<StationInfo>> entry : SuperServerHandler.stationMap.entrySet()) {
            if (entry.getValue().size() < 5) {
                station = entry.getKey();
                break;
            }
        }
        if (station == -1) {
            station = SuperServerHandler.stationVal;
            SuperServerHandler.stationVal += 1;
            List<StationInfo> list = new ArrayList<StationInfo>();
//            StationInfo stationInfo = new StationInfo();
//            ChannelHandlerContext channelHandlerContext = SuperServerHandler.getCtxFromUid(uid);
//            stationInfo.setCtx(channelHandlerContext);
//            PointDoubleInfo pointDoubleInfo = new PointDoubleInfo();
//            pointDoubleInfo.setX(0);
//            pointDoubleInfo.setY(0);
//            stationInfo.setPoint(pointDoubleInfo);
//            list.add(stationInfo);
            SuperServerHandler.stationMap.put(station, list);
        }
        Redis jedis = Redis.getInstance();
        jedis.hset("role:" + uid, "station", station + "");
        return station;
    }

    private List<UserData.Passenger> getStationPassengers2(int station, String uid) {
        List<UserData.Passenger> passengerList = null;
        List<StationInfo> list = SuperServerHandler.stationMap.get(station);
        if (list == null) {
            return null;
        }

        for (int i = 0; i < list.size(); i++) {
            StationInfo stationInfo = list.get(i);
            String otherUid = SuperServerHandler.linkMap.get(stationInfo.getCtx());
            if (otherUid == null) {
                SuperServerHandler.stationMap.get(station).remove(i);
                i--;
                continue;
            }
            if (otherUid.equals(uid)) {
                continue;
            }
            RoleEntity roleEntity = getRoleEntityFromRedis(otherUid);
            if (roleEntity == null) {
                continue;
            }
            PointDoubleInfo pointDoubleInfo = new PointDoubleInfo();
            pointDoubleInfo.setX(stationInfo.getPoint().getX());
            pointDoubleInfo.setY(stationInfo.getPoint().getY());

            if (passengerList == null) {
                passengerList = new ArrayList<UserData.Passenger>();
            }

        }
        return passengerList;
    }
///第一版机器人
 /*public   List<UserData.Passenger> getStationRobotPassengers(int station){
     List<UserData.Passenger> passengerList = new ArrayList<UserData.Passenger>();
     StringBuffer sql =new StringBuffer("from RoleEntity order by dressnums desc");
     int needRobot= (int)(Math.random()*4+1);
     // int needRobot=1;
     List<Object> robotGroup=MySql.queryForList(sql.toString(),100);
     if(robotGroup==null){
         return null;
     }
     List<Object> robotList=new ArrayList<Object>();
    for(int i=0;i<needRobot;i++){
        int index=(int)(Math.random()*robotGroup.size());
        Object robot=robotGroup.get(index);
         RoleEntity roleEntity=(RoleEntity)robot;
        roleEntity.setName( ranName.name());
        robotList.add(roleEntity);
    }
     for (int i = 0; i < robotList.size(); i++) {
         RoleEntity roleEntity =(RoleEntity)robotList.get(i) ;
         roleEntity.setStation(station);
         PointDoubleInfo pointDoubleInfo = new PointDoubleInfo();
         pointDoubleInfo.setX(0);
         pointDoubleInfo.setY(0);
         UserData.Passenger passengerData = setRobotPassengerData(roleEntity, pointDoubleInfo);
         if(passengerData==null){
             return null;
         }
         passengerList.add(passengerData);
     }
     return passengerList;
 }*/

    public List<UserData.Passenger> getStationRobotPassengers(String uid, int station, int currentPassengers) {
        station %= Factory.robotStationNums;
        List<UserData.Passenger> robotList = Factory.robotPoolMap.get(station);
        List<UserData.Passenger> passengerList = new ArrayList<UserData.Passenger>();
        Calendar calendar = Calendar.getInstance();
        int nowMinute = calendar.get(Calendar.MINUTE);
        int robotNums = (nowMinute % Factory.everyStationRobotNums) + 1;
        int needReomveRobotNums = Factory.everyStationRobotNums - robotNums - currentPassengers + 2;
        needReomveRobotNums = (needReomveRobotNums <= 2 ? 2 : needReomveRobotNums);
        if (needReomveRobotNums >= 4) {
            needReomveRobotNums = 4;
        }
        String key = "rolePassenger" + uid;
        Map<String, String> robotPassenger = new HashMap<String, String>();
        for (int i = 0; robotList != null && i < robotList.size(); i++) {
            if (i < needReomveRobotNums) {
                continue;
            }
            UserData.Passenger passenger = robotList.get(i);
            robotPassenger.put(i + "", passenger.getPlayer().getId() + "");
            passengerList.add(passenger);
        }
        if (robotPassenger.size() > 0) {
            Redis jedis = Redis.getInstance();
            jedis.hmset(key, robotPassenger);
        }
        return passengerList;
    }

    public UserData.ResponseSetInfo.Builder setRoleBaseInfo(String uid, int type, String info) throws NullPointerException {
        UserData.ResponseSetInfo.Builder builder = UserData.ResponseSetInfo.newBuilder();
        ItemData.Item.Builder itemBilder = ItemData.Item.newBuilder();
        builder.setErrorId(0);
        //super_star_fruit,签名,头像
        //type ==1修改名称,==2修改签名,==3修改头像
        if (type == 1) {
            //修改名称
            StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            roleEntity.setName(info);
            MySql.update(roleEntity);
            stringBuilder = new StringBuilder("from ItemEntity where itemId=2 and uid='").append(uid).append("'");
            ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
            itemEntity.setItemnum(itemEntity.getItemnum() - 50);
            MySql.update(itemEntity);
            itemBilder.setNum(itemEntity.getItemnum());
            itemBilder.setId(itemEntity.getItemid());
            builder.setItem(itemBilder);
            builder.setInfo(info);
            builder.setType(type);
        } else if (type == 2) {
            //修改签名
            StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            roleEntity.setSignaTure(info);
            MySql.update(roleEntity);

            stringBuilder = new StringBuilder("from ItemEntity where itemId=2 and uid='").append(uid).append("'");
            ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
            itemEntity.setItemnum(itemEntity.getItemnum() - 50);
            MySql.update(itemEntity);
            itemBilder.setNum(itemEntity.getItemnum());
            itemBilder.setId(itemEntity.getItemid());
            builder.setItem(itemBilder);
            builder.setInfo(info);
            builder.setType(type);
        } else if (type == 3) {
            //修改头像
            StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            roleEntity.setHead(Integer.parseInt(info));
            MySql.update(roleEntity);
            stringBuilder = new StringBuilder("from ItemEntity where itemId=2 and uid='").append(uid).append("'");
            ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuilder.toString());
            itemEntity.setItemnum(itemEntity.getItemnum()-0);
            MySql.update(itemEntity);
            itemBilder.setNum(itemEntity.getItemnum());
            itemBilder.setId(itemEntity.getItemid());
            builder.setItem(itemBilder);
            builder.setInfo(info);
            builder.setType(type);
        }

        return builder;
    }


    /**
     * List0 lv,List1 exp
     */
    public List<Integer> updateRoleExp(String uid, int addExp) {
     /*   ITask iTask = TaskDao.getInstance();
        Redis jedis = Redis.getInstance();
        List<Integer> result = new ArrayList<Integer>();
        int oldLv = Integer.parseInt(jedis.hget("role:" + uid, "lv"));
        String expStr = jedis.hget("roleitem:" + uid + "#0", "25");
        int exp = expStr == null ? 0 : (int) Double.parseDouble(expStr);
        IItem iItem = ItemDao.getInstance();
        int much = iItem.judgeUtility(uid, 25);
        int totalExp = exp + addExp * much;

        int newLv = oldLv;
        int needExp = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_EXP, newLv + "", "exp"));
        while (totalExp >= needExp) {
            newLv += 1;
            totalExp -= needExp;
            needExp = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_EXP, newLv + "", "exp"));
        }
        jedis.hset("role:" + uid, "lv", newLv + "");
        StringBuffer hql = new StringBuffer("update RoleEntity set lv = ").append(newLv)
                .append(" where uid = '").append(uid).append("'");
        MySql.updateSomes(hql.toString());

      //  iItem.updateItemInfo(uid, 25, (totalExp - exp));
        result.add(0, newLv);
        result.add(1, totalExp);
        if (newLv > oldLv) {
            ReportManager.reportUpdateFriendLv(uid, newLv);
            iTask.updateActivities(uid, 1,newLv-oldLv);
        }
        iTask.updateAchievement(uid, 1, newLv);*/
        return null;
    }

    private boolean judgeDressId(int dressId, List<UtilityInfo> dressList) {
        boolean bo = false;
        for (int i = 0; i < dressList.size(); i++) {
            UtilityInfo dress = dressList.get(i);
            if (dress.getId() == dressId) {
                bo = true;
                break;
            }
        }
        return bo;
    }

    private int judgePartIsExit(List<UtilityInfo> dressList, UserData.Part part) {
        int dressId = part.getPart1();
        boolean isExit = false;
        int type = 0;
        if (dressId != 0) {
            isExit = judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 1) return 2;
        }
        dressId = part.getPart2();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 2) return 2;
        }
        dressId = part.getPart3();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 3) return 2;
        }
        dressId = part.getPart4();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 4) return 2;
        }
        dressId = part.getPart5();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 5) return 2;
        }
        dressId = part.getPart6();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 6) return 2;
        }
        dressId = part.getPart7();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 7) return 2;
        }
        dressId = part.getPart8();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 8) return 2;
        }
        dressId = part.getPart9();
        if (dressId != 0) {
            isExit = dressId == 0 ? true : judgeDressId(dressId, dressList);
            if (isExit == false) return 1;
            type = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
            if (type != 9) return 2;
        }
        return 0;
    }

    public UserData.ResponseChooseRole.Builder chooseRole(String uid, int type, int roleId, int payChoice) {
        UserData.ResponseChooseRole.Builder builder = UserData.ResponseChooseRole.newBuilder();
        try {

            builder.setErrorId(0);
            builder.setType(type);
            builder.setRoleId(roleId);
            RoleDressInfo roleDressInfo = getRoleDressFromRedis("roledress:" + uid + "#" + roleId);
            if (type == 1) {
                //第一次花钱获取形象
                int price = 0;
                double total = 0;
                if (roleDressInfo != null) {
                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                    log.error(uid + ":[chooseRole] error 1 >>>roleId:" + roleId);
                } else {
                    IItem iItem = ItemDao.getInstance();
                    if (payChoice == 1) {
                        price = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ROLE, roleId + "", "price"));
                        total = iItem.updateItemInfo(uid, 2, -price);
                    } else {
                        price = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ROLE, roleId + "", "price_coin"));
                        total = iItem.updateItemInfo(uid, 1, -price);
                    }
                }
                if (total < 0) {
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                    log.error(uid + ":[chooseRole] error 2 >>>num:" + (total + price));
                } else {
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    if (payChoice == 1) {
                        itemBu.setId(2);
                    } else {
                        itemBu.setId(1);
                    }

                    itemBu.setNum(total);
                    builder.addItem(itemBu);

                    StringBuffer hql = new StringBuffer("update RoleEntity set roleid = ").append(roleId).append(",role").append(roleId).append(" = 1")
                            .append(" where uid = '").append(uid).append("'");
                    MySql.updateSomes(hql.toString());

                    roleDressInfo = new RoleDressInfo();
                    roleDressInfo.setUid(uid);
                    roleDressInfo.setId(roleId);
                    List<CupboardInfo> cupboardInfoList = new ArrayList<CupboardInfo>();
                    CupboardInfo cupboardInfo0 = new CupboardInfo();
                    cupboardInfo0.setId(0);
                    setCupboardToDB(uid, roleId, cupboardInfo0);
                    cupboardInfoList.add(cupboardInfo0);
                    roleDressInfo.setCupboardList(cupboardInfoList);
                    ArrayList<UtilityInfo> dressList = new ArrayList<UtilityInfo>();
                    StringBuffer query = new StringBuffer("from DressEntity where uid='").append(uid).append("' and roleid=").append(roleId);
                    List<Object> nowDressList = MySql.queryForList(query.toString());
                    for (int i = 0; nowDressList != null && i < nowDressList.size(); i++) {
                        DressEntity entity = (DressEntity) nowDressList.get(i);
                        UtilityInfo utilityInfo = new UtilityInfo();
                        utilityInfo.setOverStamp(entity.getOverstamp());
                        utilityInfo.setId(entity.getDressid());
                        dressList.add(utilityInfo);
                    }
                    roleDressInfo.setDressList(dressList);
                    List<RoleDressInfo> list = new ArrayList<RoleDressInfo>();
                    list.add(roleDressInfo);
                    userDressToRedis(list);

                    Redis jedis = Redis.getInstance();
                    jedis.hset("role:" + uid, "roleid", roleId + "");

                    if (roleId == 2) {
                        ITask iTask = TaskDao.getInstance();
                        iTask.updateAchievement(uid, 14, 1);
                    }
                }
            } else if (type == 2) {
                //切换
                if (roleDressInfo == null) {
                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                    log.error(uid + ":[chooseRole] error 3 >>>roleId:" + roleId);
                } else {
                    StringBuffer hql = new StringBuffer("update RoleEntity set roleid = ").append(roleId)
                            .append(" where uid = '").append(uid).append("'");
                    MySql.updateSomes(hql.toString());
                    Redis jedis = Redis.getInstance();
                    jedis.hset("role:" + uid, "roleid", roleId + "");
                }
            } else {
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                log.error(uid + ":[chooseRole] error 4 >>>type:" + type);
            }


        } catch (Exception e) {
            e.printStackTrace();
            ///   /// System.err.println(JsonFormat.printToString(builder.build()));
        }

        return builder;
    }

    public int changeDress(String uid, UserData.Part part) {
        String roleId = getRoleIdFromUid(uid);
        String key = "roledress:" + uid + "#" + roleId;
        RoleDressInfo roleDressInfo = getRoleDressFromRedis(key);
        List<UtilityInfo> dressList = roleDressInfo.getDressList();
        int val = judgePartIsExit(dressList, part);
        if (val == 1) {
            log.error(uid + ":[changeDress] error 1");
            return ProtoData.ErrorCode.ITEMERROR_VALUE;
        } else if (val == 2) {
            log.error(uid + ":[changeDress] error 2");
            return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
        }
        List<CupboardInfo> cupboardList = roleDressInfo.getCupboardList();
        CupboardInfo cupboardInfo = getCupboardInfo(cupboardList, 0);
        if (cupboardInfo == null) {
            log.error(uid + ":[changeDress] error 3");
            return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
        }

        int last_part1 = cupboardInfo.getPart1();
        int last_part2 = cupboardInfo.getPart2();
        int last_part3 = cupboardInfo.getPart3();
        int last_part4 = cupboardInfo.getPart4();
        int last_part5 = cupboardInfo.getPart5();
        int last_part6 = cupboardInfo.getPart6();
        int last_part7 = cupboardInfo.getPart7();
        int last_part8 = cupboardInfo.getPart8();
        int last_part9 = cupboardInfo.getPart9();
        String strLastDress = MyUtils.objectToJson(cupboardList);

        cupboardInfo.setPart1(part.getPart1());
        cupboardInfo.setPart2(part.getPart2());
        cupboardInfo.setPart3(part.getPart3());
        cupboardInfo.setPart4(part.getPart4());
        cupboardInfo.setPart5(part.getPart5());
        cupboardInfo.setPart6(part.getPart6());
        cupboardInfo.setPart7(part.getPart7());
        cupboardInfo.setPart8(part.getPart8());
        cupboardInfo.setPart9(part.getPart9());
        Map<String, String> map = new HashMap<String, String>();
        map.put("cupboardlist", MyUtils.objectToJson(cupboardList));
        map.put("lastDress", strLastDress);
        roleDressToRedis(uid, Integer.parseInt(roleId), map);

        StringBuffer hql = new StringBuffer("update PartEntity set ");
        hql.append("part1 = ").append(cupboardInfo.getPart1());
        hql.append(",part2 = ").append(cupboardInfo.getPart2());
        hql.append(",part3 = ").append(cupboardInfo.getPart3());
        hql.append(",part4 = ").append(cupboardInfo.getPart4());
        hql.append(",part5 = ").append(cupboardInfo.getPart5());
        hql.append(",part6 = ").append(cupboardInfo.getPart6());
        hql.append(",part7 = ").append(cupboardInfo.getPart7());
        hql.append(",part8 = ").append(cupboardInfo.getPart8());
        hql.append(",part9 = ").append(cupboardInfo.getPart9());
        /*hql.append(",last_part1 = ").append(last_part1);
        hql.append(",last_part2 = ").append(last_part2);
        hql.append(",last_part3 = ").append(last_part3);
        hql.append(",last_part4 = ").append(last_part4);
        hql.append(",last_part5 = ").append(last_part5);
        hql.append(",last_part6 = ").append(last_part6);
        hql.append(",last_part7 = ").append(last_part7);
        hql.append(",last_part8 = ").append(last_part8);
        hql.append(",last_part9 = ").append(last_part9);*/

        hql.append(" where uid = '").append(uid).append("' and roleid = ").append(roleId).append(" and cupboard = 0");
        MySql.updateSomes(hql.toString());
        return 1;
    }

//    public int changeEquip(String uid, EquipData.Equip equip) {
//        String roleId = getRoleIdFromUid(uid);
//        String key = "roleequip:" + uid + "#" + roleId;
//        RoleDressInfo roleDressInfo = getRoleDressFromRedis(key);
//        List<UtilityInfo> dressList = roleDressInfo.getDressList();
//        int val = judgePartIsExit(dressList, part);
//        if (val == 1) {
//            log.error(uid + ":[changeDress] error 1");
//            return ProtoData.ErrorCode.ITEMERROR_VALUE;
//        } else if (val == 2) {
//            log.error(uid + ":[changeDress] error 2");
//            return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
//        }
//        List<CupboardInfo> cupboardList = roleDressInfo.getCupboardList();
//        CupboardInfo cupboardInfo = getCupboardInfo(cupboardList, 0);
//        if (cupboardInfo == null) {
//            log.error(uid + ":[changeDress] error 3");
//            return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
//        }
//
//        int last_part1 = cupboardInfo.getPart1();
//        int last_part2 = cupboardInfo.getPart2();
//        int last_part3 = cupboardInfo.getPart3();
//        int last_part4 = cupboardInfo.getPart4();
//        int last_part5 = cupboardInfo.getPart5();
//        int last_part6 = cupboardInfo.getPart6();
//        int last_part7 = cupboardInfo.getPart7();
//        int last_part8 = cupboardInfo.getPart8();
//        int last_part9 = cupboardInfo.getPart9();
//        String strLastDress = MyUtils.objectToJson(cupboardList);
//
//        cupboardInfo.setPart1(part.getPart1());
//        cupboardInfo.setPart2(part.getPart2());
//        cupboardInfo.setPart3(part.getPart3());
//        cupboardInfo.setPart4(part.getPart4());
//        cupboardInfo.setPart5(part.getPart5());
//        cupboardInfo.setPart6(part.getPart6());
//        cupboardInfo.setPart7(part.getPart7());
//        cupboardInfo.setPart8(part.getPart8());
//        cupboardInfo.setPart9(part.getPart9());
//        Map<String, String> map = new HashMap<String, String>();
//        map.put("cupboardlist", MyUtils.objectToJson(cupboardList));
//        map.put("lastDress", strLastDress);
//        roleDressToRedis(uid, Integer.parseInt(roleId), map);
//
//        StringBuffer hql = new StringBuffer("update PartEntity set ");
//        hql.append("part1 = ").append(cupboardInfo.getPart1());
//        hql.append(",part2 = ").append(cupboardInfo.getPart2());
//        hql.append(",part3 = ").append(cupboardInfo.getPart3());
//        hql.append(",part4 = ").append(cupboardInfo.getPart4());
//        hql.append(",part5 = ").append(cupboardInfo.getPart5());
//        hql.append(",part6 = ").append(cupboardInfo.getPart6());
//        hql.append(",part7 = ").append(cupboardInfo.getPart7());
//        hql.append(",part8 = ").append(cupboardInfo.getPart8());
//        hql.append(",part9 = ").append(cupboardInfo.getPart9());
//        /*hql.append(",last_part1 = ").append(last_part1);
//        hql.append(",last_part2 = ").append(last_part2);
//        hql.append(",last_part3 = ").append(last_part3);
//        hql.append(",last_part4 = ").append(last_part4);
//        hql.append(",last_part5 = ").append(last_part5);
//        hql.append(",last_part6 = ").append(last_part6);
//        hql.append(",last_part7 = ").append(last_part7);
//        hql.append(",last_part8 = ").append(last_part8);
//        hql.append(",last_part9 = ").append(last_part9);*/
//
//        hql.append(" where uid = '").append(uid).append("' and roleid = ").append(roleId).append(" and cupboard = 0");
//        MySql.updateSomes(hql.toString());
//        return 1;
//    }


    public long getAddTime(int type) {
        long addTime = -1;
        if (type == 1) {
            addTime = 7 * 24 * 60 * 60 * 1000;
        } else if (type == 2) {
            addTime = (long) 30 * 24 * 60 * 60 * 1000;
        } else if (type == 3) {
            addTime = 0;
        }
        return addTime;
    }

    private String getPayStr(int type, int cType) {
        StringBuffer str = new StringBuffer();
        if (type == 1) {
            str.append("coin_");
        } else if (type == 2) {
            str.append("ticket_");
        } else {
            return null;
        }
        if (cType == 1) {
            str.append("7");
        } else if (cType == 2) {
            str.append("30");
        } else if (cType == 3) {
            str.append("full");
        } else {
            return null;
        }
        return str.toString();
    }

    public String getNameFromUid(String uid) {
        Redis jedis = Redis.getInstance();
        String name = jedis.hget("role:" + uid, "name");
        return name == null ? "" : name;
    }

    public int getHeadFromUid(String uid) {
        Redis jedis = Redis.getInstance();
        String head = jedis.hget("role:" + uid, "head");
        return head == null ? 0 : Integer.parseInt(head);
    }

    public String getRoleIdFromUid(String uid) {
        Redis jedis = Redis.getInstance();
        String roleid = jedis.hget("role:" + uid, "roleid");
        return roleid;
    }

    public int getIdFromUid(String uid) {
        Redis jedis = Redis.getInstance();
        String id = jedis.hget("role:" + uid, "id");
        return id == null ? -1 : Integer.parseInt(id);
    }

    public int getStationFromUid(String uid) {
        Redis jedis = Redis.getInstance();
        String station = jedis.hget("role:" + uid, "station");
        return station == null ? -1 : Integer.parseInt(station);
    }

    public int getLvFromUid(String uid) {
        Redis jedis = Redis.getInstance();
        String lv = jedis.hget("role:" + uid, "lv");
        return lv == null ? -1 : Integer.parseInt(lv);
    }

    public UserData.ResponseBuyClothing.Builder buyClothing(String uid, int type, List<UserData.Clothing> clothingList) {
        UserData.ResponseBuyClothing.Builder builder = UserData.ResponseBuyClothing.newBuilder();
        builder.setErrorId(0);
        builder.setBuyType(type);
        synchronized (uid) {

            String roleId = getRoleIdFromUid(uid);
            String key = "roledress:" + uid + "#" + roleId;
            RoleDressInfo roleDressInfo = getRoleDressFromRedis(key);
            List<UtilityInfo> dressList = roleDressInfo.getDressList();
            List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
            Map<Integer, ItemInfo> itemMap = new HashMap<Integer, ItemInfo>();
            List<Object> hqlObj = new ArrayList<Object>();
            Redis jedis = Redis.getInstance();
            int nowRoleId = Integer.parseInt(jedis.hget("role:" + uid, "roleid"));
            for (int i = 0; i < clothingList.size(); i++) {
                UserData.Clothing clothing = clothingList.get(i);
                int dressId = clothing.getDressId();
                int cType = clothing.getType();
                long addTime = getAddTime(cType);
                String string = getPayStr(type, cType);
                if (string == null) {
                    log.error(uid + ":[buyClothing] error 1");
                    builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                    return builder;
                }
                UtilityInfo utilityInfo = null;
                for (int j = 0; j < dressList.size(); j++) {
                    if (dressList.get(j).getId() == dressId) {
                        utilityInfo = dressList.get(j);
                        break;
                    }
                }
                Map<String, String> dressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_DRESS, dressId);
                if (dressMap.size() == 0) {
                    log.error(uid + ":[buyClothing] error 2 >>>dressId:" + dressId);
                    builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                    return builder;
                }
                int mod = Integer.parseInt(dressMap.get("mod"));
                if (mod != nowRoleId) {
                    log.error(uid + ":[buyClothing] error 3 >>>dressId:" + dressId + ",nowRoleId:" + nowRoleId);
                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                    return builder;
                }
                if (utilityInfo == null) {
                    utilityInfo = new UtilityInfo();
                    utilityInfo.setId(dressId);
                    long overTime = addTime == 0 ? 0 : (TimerHandler.nowTimeStamp + addTime);
                    utilityInfo.setOverStamp(overTime + "");
                    dressList.add(utilityInfo);
                    DressEntity dressEntity = new DressEntity();
                    dressEntity.setUid(uid);
                    dressEntity.setRoleid(Integer.parseInt(roleId));
                    dressEntity.setDressid(dressId);
                    dressEntity.setOverstamp(overTime + "");
                    dressEntity.setType(1);
                    hqlObj.add(dressEntity);

                } else {
                    if (utilityInfo.getOverStamp().equals("0")) {
                        log.error(uid + ":[buyClothing] error 4 >>>dressId:" + utilityInfo.getId());
                        builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                        return builder;
                    }
                    long overTime = addTime == 0 ? 0 : (Long.parseLong(utilityInfo.getOverStamp()) + addTime);
                    utilityInfo.setOverStamp(overTime + "");

                    StringBuffer hql = new StringBuffer("update DressEntity set overstamp = '").append(overTime).append("' where uid = '")
                            .append(uid).append("' and roleid = ").append(Integer.parseInt(roleId)).append(" and dressid = ").append(dressId);
                    hqlObj.add(hql.toString());
                }

                CommonData.Utility.Builder utilityBu = CommonData.Utility.newBuilder();
                utilityBu.setId(utilityInfo.getId());
                utilityBu.setTimeStamp(utilityInfo.getOverStamp());
                utilityList.add(utilityBu.build());


                String itemStrs = dressMap.get(string);
                int itemId = Integer.parseInt(itemStrs.split(",")[0]);
                int itemNum = Integer.parseInt(itemStrs.split(",")[1]);

                if (itemMap.containsKey(itemId) == true) {
                    ItemInfo itemInfo = itemMap.get(itemId);
                    itemInfo.setNum(itemInfo.getNum() + itemNum);
                } else {
                    ItemInfo itemInfo = new ItemInfo();
                    itemInfo.setId(itemId);
                    itemInfo.setNum(itemNum);
                    itemMap.put(itemId, itemInfo);
                }
            }
            IItem iItem = ItemDao.getInstance();
            for (Map.Entry<Integer, ItemInfo> entry : itemMap.entrySet()) {
                ItemInfo itemInfo = entry.getValue();
                double nowNum = iItem.getItemNum(uid, itemInfo.getId());
                if (nowNum < itemInfo.getNum()) {
                    log.error(uid + ":[buyClothing] error 5 >>>id:" + itemInfo.getId() + ",num:" + nowNum + ",needNum:" + itemInfo.getNum());
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                    return builder;
                }
            }
            for (Map.Entry<Integer, ItemInfo> entry : itemMap.entrySet()) {
                ItemInfo itemInfo = entry.getValue();
                double totalNum = iItem.updateItemInfo(uid, itemInfo.getId(), (long) (-itemInfo.getNum()));
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(itemInfo.getId());
                itemBu.setNum(totalNum);
                builder.addItem(itemBu);
            }
            //衣物sql
            for (int i = 0; i < hqlObj.size(); i++) {
                Object object = hqlObj.get(i);
                if (object instanceof DressEntity) {
                    MySql.insert(object);
                } else if (object instanceof String) {
                    MySql.updateSomes(object.toString());
                } else {
                    /// System.out.println("???????");
                }
            }
            builder.addAllClothingList(utilityList);
            jedis.hset("roledress:" + uid + "#" + roleId, "dresslist", MyUtils.objectToJson(dressList));
            updateDressAchievement(uid, clothingList);

            log.info("[buyClothing]>>>uid:" + uid + ">>>" + MyUtils.objectToJson(clothingList));
        }


        return builder;
    }

    public void updateDressAchievement(String uid, List<UserData.Clothing> clothingList) {
        if (clothingList.size() == 0) {
            return;
        }

        ITask iTask = TaskDao.getInstance();
        Map<Integer, Integer> dressGetMap = new HashMap<Integer, Integer>();
        int dressnums = 0;
        for (int i = 0; i < clothingList.size(); i++) {
            UserData.Clothing clothing = clothingList.get(i);
            //永久服饰才会获得成就
            if (clothing.getType() == 3) {
                dressnums++;
                int dressId = clothing.getDressId();
                int dressType = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "type"));
                if (dressType == 2) {
                    int roleType = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_DRESS, dressId + "", "mod"));
                    if (roleType != 1) {
                        continue;
                    }
                }
                int num = 1;
                if (dressGetMap.containsKey(dressType) == true) {
                    num += dressGetMap.get(dressType);
                }
                dressGetMap.put(dressType, num);
            }
        }
        if (dressnums != 0) {
            StringBuffer sql = new StringBuffer("update RoleEntity set dressnums=dressnums+").append(dressnums).append("where uid='")
                    .append(uid).append("'");
            MySql.updateSomes(sql.toString());
            Redis jedis = Redis.getInstance();
            int nums = Integer.parseInt(jedis.hget("role:" + uid, "dressnums"));
            jedis.hset("role:" + uid, "dressnums", nums + dressnums + "");
        }
        for (Map.Entry<Integer, Integer> entry : dressGetMap.entrySet()) {
            int dressType = entry.getKey();

            int num = entry.getValue();
            int type = 0;
            switch (dressType) {
                case 1:
                    type = 0;
                    break;
                case 2:
                    type = 15;
                    break;
                case 3:
                    type = 0;
                    break;
                case 4:
                    type = 16;
                    break;
                case 5:
                    type = 17;
                    break;
                case 6:
                    type = 18;
                    break;
                case 7:
                    type = 0;
                    break;
                case 8:
                    type = 20;
                    break;
                case 9:
                    type = 19;
                    break;
                default:
                    break;
            }
            if (type != 0) {
                iTask.updateAchievement(uid, type, num);
            }
        }
    }

    private String getNewCupboardNum(String uid, int roleId, int num) {
        Redis jedis = Redis.getInstance();
        StringBuffer result = new StringBuffer();
        switch (roleId) {
            case 1:
                String string1 = jedis.hget("roledress:" + uid + "#2", "cupboardnum");
                String string2 = jedis.hget("roledress:" + uid + "#3", "cupboardnum");
                result = result.append(num).append("|").append(string1).append("|").append(string2);
                break;
            case 2:
                String string3 = jedis.hget("roledress:" + uid + "#1", "cupboardnum");
                String string4 = jedis.hget("roledress:" + uid + "#3", "cupboardnum");
                result = result.append(string3).append("|").append(num).append("|").append(string4);
                break;
            case 3:
                String string5 = jedis.hget("roledress:" + uid + "#1", "cupboardnum");
                String string6 = jedis.hget("roledress:" + uid + "#2", "cupboardnum");
                result = result.append(string5).append("|").append(string6).append("|").append(num);
                break;
        }
        return result.toString();
    }

    public UserData.ResponseChangeCupboard.Builder changeCupboard(String uid, int id, int type, UserData.Part part) {
        UserData.ResponseChangeCupboard.Builder builder = UserData.ResponseChangeCupboard.newBuilder();
        builder.setErrorId(0);
        builder.setId(id);
        builder.setType(type);
        Redis jedis = Redis.getInstance();
        String roleId = getRoleIdFromUid(uid);
        String key = "roledress:" + uid + "#" + roleId;
        RoleDressInfo roleDressInfo = getRoleDressFromRedis(key);
        int cupboardNum = roleDressInfo.getCupboardNum();
        if (type != 0) {
            if (cupboardNum < id) {
                log.error(uid + ":[changeCupboard] error 1 >>>id:" + id + ",cupboardNum:" + cupboardNum);
                builder.setErrorId(ProtoData.ErrorCode.ITEMERROR_VALUE);
                return builder;
            }
        }
        List<CupboardInfo> cupboardList = roleDressInfo.getCupboardList();
        CupboardInfo cupboardInfo = getCupboardInfo(cupboardList, id);

        if (type == 0) {//0解锁 1衣物放入衣橱 2清除衣橱
            cupboardNum++;
            if (cupboardNum > 6) {
                log.error(uid + ":[changeCupboard] error 2 >>>cupboardNum:" + cupboardNum);
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                return builder;
            }
            builder.setId(cupboardNum);
//            cupboardInfo.setId(cupboardNum);
//            cupboardList.add(cupboardInfo);
//            setCupboardToDB(uid,Integer.parseInt(roleId), cupboardInfo);

            jedis.hset(key, "cupboardnum", cupboardNum + "");
            String cupboardNumStr = getNewCupboardNum(uid, Integer.parseInt(roleId), cupboardNum);
            StringBuffer hql = new StringBuffer("update RoleEntity set cupboards = '").append(cupboardNumStr)
                    .append("' where uid = '").append(uid).append("'");
            MySql.updateSomes(hql.toString());
        } else if (type == 1 || type == 2) {
            if (type == 1) {
                int updateType = 2;
                if (cupboardInfo == null) {
                    cupboardInfo = new CupboardInfo();
                    cupboardInfo.setId(id);
                    cupboardList.add(cupboardInfo);
                    updateType = 1;
                }
                int partId1 = part.getPart1();
                int partId2 = part.getPart2();
                int partId3 = part.getPart3();
                int partId4 = part.getPart4();
                int partId5 = part.getPart5();
                int partId6 = part.getPart6();
                int partId7 = part.getPart7();
                int partId8 = part.getPart8();
                int partId9 = part.getPart9();

                cupboardInfo.setPart1(partId1);
                cupboardInfo.setPart2(partId2);
                cupboardInfo.setPart3(partId3);
                cupboardInfo.setPart4(partId4);
                cupboardInfo.setPart5(partId5);
                cupboardInfo.setPart6(partId6);
                cupboardInfo.setPart7(partId7);
                cupboardInfo.setPart8(partId8);
                cupboardInfo.setPart9(partId9);

                builder.setPart(part);
                updatePartEntity(updateType, uid, Integer.parseInt(roleId), cupboardInfo);
            } else {
                if (id == 0) {
                    log.error(uid + ":[changeCupboard] error 3");
                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                    return builder;
                }
                cupboardList.remove(cupboardInfo);
                StringBuffer hql = new StringBuffer("delete from PartEntity where uid = '").append(uid).append("' and roleid = ").append(roleId).append(" and cupboard = ").append(id);
                MySql.updateSomes(hql.toString());
            }

        } else {
            log.error(uid + ":[changeCupboard] error 4 >>>type:" + type);
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            return builder;
        }
        jedis.hset("roledress:" + uid + "#" + roleId, "cupboardlist", MyUtils.objectToJson(cupboardList));
        return builder;
    }

    public int cupboardToBody(String uid, int cupboardId) {
        String roleId = getRoleIdFromUid(uid);
        String key = "roledress:" + uid + "#" + roleId;
        RoleDressInfo roleDressInfo = getRoleDressFromRedis(key);
        List<CupboardInfo> cupboardList = roleDressInfo.getCupboardList();
        CupboardInfo cupboardBody = null;
        CupboardInfo cupboardInfo = null;
        for (int i = 0; i < cupboardList.size(); i++) {
            CupboardInfo tmp = cupboardList.get(i);
            if (tmp.getId() == cupboardId) {
                cupboardInfo = tmp;
            } else if (tmp.getId() == 0) {
                cupboardBody = tmp;
            }
        }
        if (cupboardBody == null || cupboardInfo == null) {
            log.error(uid + ":[cupboardToBody] error 1 >>>cupboardId:" + cupboardId);
            return ProtoData.ErrorCode.UNKNOWERROR_VALUE;
        }
        cupboardBody.setPart1(cupboardInfo.getPart1());
        cupboardBody.setPart2(cupboardInfo.getPart2());
        cupboardBody.setPart3(cupboardInfo.getPart3());
        cupboardBody.setPart4(cupboardInfo.getPart4());
        cupboardBody.setPart5(cupboardInfo.getPart5());
        cupboardBody.setPart6(cupboardInfo.getPart6());
        cupboardBody.setPart7(cupboardInfo.getPart7());
        cupboardBody.setPart8(cupboardInfo.getPart8());
        cupboardBody.setPart9(cupboardInfo.getPart9());
        updatePartEntity(2, uid, Integer.parseInt(roleId), cupboardBody);
        Redis jedis = Redis.getInstance();
        jedis.hset("roledress:" + uid + "#" + roleId, "cupboardlist", MyUtils.objectToJson(cupboardList));
        return 0;
    }

    /**
     * type:1 insert  2 update
     */
    private void updatePartEntity(int type, String uid, int roleId, CupboardInfo cupboardInfo) {
        if (type == 1) {
            setCupboardToDB(uid, roleId, cupboardInfo);
        } else if (type == 2) {
            StringBuffer hql = new StringBuffer("update PartEntity set part1 = ").append(cupboardInfo.getPart1()).append(",part2 = ").append(cupboardInfo.getPart2())
                    .append(",part3 = ").append(cupboardInfo.getPart3()).append(",part4 = ").append(cupboardInfo.getPart4()).append(",part5 = ").append(cupboardInfo.getPart5())
                    .append(",part6 = ").append(cupboardInfo.getPart6()).append(",part7 = ").append(cupboardInfo.getPart7()).append(",part8 = ").append(cupboardInfo.getPart8())
                    .append(",part9 = ").append(cupboardInfo.getPart9())
                    .append(" where uid = '").append(uid).append("' and roleid = ").append(roleId).append(" and cupboard = ").append(cupboardInfo.getId());
            MySql.updateSomes(hql.toString());
        }
    }

    private void updateMoneyNum(String uid, int moneyNum, long moneyStamp) {
        Redis jedis = Redis.getInstance();
        Map<String, String> map = new HashMap<String, String>();
        map.put("moneynum", moneyNum + "");
        map.put("moneystamp", moneyStamp + "");
        jedis.hmset("role:" + uid, map);

        StringBuffer hql = new StringBuffer("update RoleEntity set moneynum = ").append(moneyNum).append(",moneystamp = '").append(moneyStamp).append("'")
                .append(" where uid = '").append(uid).append("'");
        MySql.updateSomes(hql.toString());
    }

    private int judgeDailyMoney(String uid) {
        Redis jedis = Redis.getInstance();
        //每日星币兑换金币
        String moneyStamp = jedis.hget("role:" + uid, "moneystamp");
        int moneyNum = Integer.parseInt(jedis.hget("role:" + uid, "moneynum"));
        if (!moneyStamp.equals("0")) {

//            int dailyMoneyNum = SuperConfig.getDailyMoneyNum();
            boolean bo = MyUtils.isOneDay(TimerHandler.nowTimeStamp, Long.parseLong(moneyStamp));
            if (bo == false) {
                updateMoneyNum(uid, 0, 0);
                return 0;
            }
        }
        return moneyNum;
    }

    public UserData.ResponseStarbiToMoney.Builder starbiToMoney(String uid) {
        UserData.ResponseStarbiToMoney.Builder builder = UserData.ResponseStarbiToMoney.newBuilder();
        builder.setErrorId(0);
        Redis jedis = Redis.getInstance();
        int moneyNum = Integer.parseInt(jedis.hget("role:" + uid, "moneynum"));
        String moneyStamp = jedis.hget("role:" + uid, "moneystamp");
        int dailyMoneyNum = SuperConfig.getDailyMoneyNum();
        boolean bo = MyUtils.isOneDay(TimerHandler.nowTimeStamp, Long.parseLong(moneyStamp));
        if (moneyNum >= dailyMoneyNum && bo == true) {
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
        } else {
            List<Integer> moneyChanges = SuperConfig.getMoneyChanges();
            if (moneyChanges.size() == 0) {
                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            } else {
                IItem iItem = ItemDao.getInstance();
                double starbi = iItem.getItemNum(uid, 2);
                int needStarbi = moneyChanges.get(0);
                if (starbi < needStarbi) {
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                } else {
                    ItemData.Item.Builder starbiBu = ItemData.Item.newBuilder();
                    starbiBu.setId(2);
                    double total = iItem.updateItemInfo(uid, 2, -needStarbi);
                    starbiBu.setNum(total);
                    builder.addItem(starbiBu);

                    ItemData.Item.Builder moneyBu = ItemData.Item.newBuilder();
                    moneyBu.setId(1);
                    total = iItem.updateItemInfo(uid, 1, moneyChanges.get(1));
                    moneyBu.setNum(total);
                    builder.addItem(moneyBu);
                    if (bo == true) {
                        moneyNum += 1;
                    } else {
                        moneyNum = 1;
                    }
                    updateMoneyNum(uid, moneyNum, TimerHandler.nowTimeStamp);
                }
            }
            ITask iTask = TaskDao.getInstance();
            iTask.updateAchievement(uid, 26, 1);
        }
        return builder;
    }

    public void stationLocation(ChannelHandlerContext ctx, String uid, UserData.PointDouble point, UserData.PointDouble direction, double speed) {
        int id = getIdFromUid(uid);
        if (id < 0) {
            return;
        }
        int station = getStationFromUid(uid);
        //  /// System.err.println(station+" public void stationLocation(ChannelHandlerContext ctx, St");
        if (station < 0) {
            return;
        }
        PointDoubleInfo pointInfo = new PointDoubleInfo();
        pointInfo.setX(point.getX());
        pointInfo.setY(point.getY());
        updateLocationInStation(ctx, station, pointInfo);
        UserData.ReportStationLocation.Builder stationBu = UserData.ReportStationLocation.newBuilder();
        stationBu.setPassengerId(id);
        stationBu.setDirection(direction);
        stationBu.setSpeed(speed);
        stationBu.setPoint(point);
        ReportManager.reportStationLocation(uid, station, stationBu.build().toByteArray());
    }

    private boolean updateLocationInStation(ChannelHandlerContext ctx, int station, PointDoubleInfo point) {
        List<StationInfo> list = SuperServerHandler.stationMap.get(station);
        for (int i = 0; i < list.size(); i++) {
            StationInfo stationInfo = list.get(i);
            if (stationInfo.getCtx() == ctx) {
                stationInfo.setPoint(point);
                return true;
            }
        }
        return false;
    }


    public void updateAction(String uid, int addAction, String actionStamp) {
        Map<String, String> map = new HashMap<String, String>();
        map.put("actionstamp", actionStamp);
        roleEntityToRedis(uid, map);
        StringBuffer hql = new StringBuffer("update RoleEntity set actionstamp = '").append(actionStamp)
                .append("' where uid = '").append(uid).append("'");
        MySql.updateSomes(hql.toString());

    }

    /**
     * list0 action,list1 stamp
     */
    private List<String> judgeAction(int action, String actionStamp) {
        int actionLimit = SuperConfig.getActionLimit();
        if (action < actionLimit) {
            long nowStamp = System.currentTimeMillis();
            int recoverActionSecond = SuperConfig.getRecoverActionSec();
            int apartSecond = (int) ((nowStamp - Long.parseLong(actionStamp)) / 1000);
            int addAction = apartSecond / recoverActionSecond;
            if (addAction > 0) {
                action += addAction;

                if (action >= actionLimit) {
                    action = actionLimit;
                    actionStamp = "0";
                } else {
                    long stamp = Long.parseLong(actionStamp) + addAction * recoverActionSecond * 1000;
                    actionStamp = stamp + "";
                }
            }
        } else {
            action = actionLimit;
            actionStamp = "0";
        }
        List<String> result = new ArrayList<String>();
        result.add(action + "");
        result.add(actionStamp);
        return result;
    }

    /**
     * List0 action,List1 actionStamp
     */
    public List<String> useAction(String uid, int useNum) {
        Redis jedis = Redis.getInstance();
        List<String> result = null;
        String actionStr = jedis.hget("roleitem:" + uid + "#0", "24");
        int action = actionStr == null ? 0 : (int) Double.parseDouble(actionStr);
        String actionStamp = jedis.hget("role:" + uid, "actionstamp");
        List<String> actionList = judgeAction(action, actionStamp);
        int newAction = Integer.parseInt(actionList.get(0)) - useNum;
        String newActionStamp = actionList.get(1);

        if (newAction < 0) {
            return result;
        }
        if (newActionStamp.equals("0")) {
            newActionStamp = System.currentTimeMillis() + "";
        }
        result = new ArrayList<String>();
        updateAction(uid, -useNum, newActionStamp);
        result.add(0, newAction + "");
        result.add(1, newActionStamp);
        return result;
    }

    public void operateView(String uid, String views) {
        String target = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK, "10009", "target");
        String target_achievement = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK, "20102", "target");
        String MainAchievement = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STORYTASK, "30008", "target");
        String finishShare = "10016" + Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_STORYTASK, "10016", "target");
        ITask iTask = TaskDao.getInstance();
        if (views.equals(target)) {
            iTask.updateOneTask(uid, 10009, 1);
        } else if (views.equals(target_achievement)) {
            iTask.updateAchievement(uid, 28, 1);
        } else if (views.equals(MainAchievement)) {
            iTask.updateMainAchievement(uid, 8, 1);
            //   iTask.updateMainAchievement(uid,7,1);
        } else if (views.equals(finishShare)) {
            iTask.updateOneTask(uid, 10016, 1);
        }
    }

    //新手引导 task为5时给予固定奖励
    public boolean updateNewUser(String uid, int advance) {
        try {
            Redis redis = Redis.getInstance();
            redis.hset("role:" + uid, "advance", advance + "");
            StringBuffer hql = new StringBuffer("update RoleEntity set advance = '").append(advance).append("' where uid = '").append(uid).append("'");
            MySql.mustUpdateSomes(hql.toString());
            if (advance == 5) {
                DressEntity dressEntity = new DressEntity();
                dressEntity.setUid(uid);
                dressEntity.setRoleid(1);
                dressEntity.setDressid(186);
                dressEntity.setOverstamp("0");
                dressEntity.setType(1);
                MySql.insert(dressEntity);
                ILogin loginDao = LoginDao.getInstance();
                String key = "roledress:" + uid + "#" + 1;
                RoleDressInfo roleDressInfo1 = loginDao.getRoleDressFromRedis(key);
                List<UtilityInfo> dressList = roleDressInfo1.getDressList();
                UtilityInfo utilityInfo = new UtilityInfo();
                utilityInfo.setId(186);
                utilityInfo.setOverStamp("0");
                dressList.add(utilityInfo);
                Redis jedis = Redis.getInstance();
                jedis.hset(key, "dresslist", MyUtils.objectToJson(dressList));
                ItemData.ResponseCompose.Builder builder = ItemData.ResponseCompose.newBuilder();
                builder.setErrorId(0);
                CommonData.Utility.Builder utilityBuilder = CommonData.Utility.newBuilder();
                utilityBuilder.setTimeStamp("0");
                utilityBuilder.setId(186);
                builder.setClothing(utilityBuilder);
                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSECOMPOSE_VALUE, builder.build().toByteArray());
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    public int getRoleOnlineStatus(String uid) {
        if (SuperServerHandler.getCtxFromUid(uid) == null) {
            return 0;
        }
        return 1;
    }

    private void rankFor() {

    }


    /**
     * {
     * "playId":1,
     * "type":2,//0无 1禁止发言 9封号
     * }
     */
    public void setPlayerType(JSONObject jsonObject, ChannelHandlerContext ctx) {
//        String result = "OK";
//        int playId = jsonObject.getInt("playId");
//        RoleEntity roleEntity = getRoleEntityFromDBById(playId);
//        if (roleEntity == null){
//            result = "player is not exit!";
//            return result;
//        }
//        int type = jsonObject.getInt("type");
//        StringBuffer hql = new StringBuffer("update RoleEntity set type = ").append(type).append(" where id = ").append(playId);
//        MySql.updateSomes(hql.toString());
//        String roleId = getRoleIdFromUid(roleEntity.getUid());
//        if (roleId != null){
//            Redis jedis = Redis.getInstance();
//            jedis.hset("role:"+roleEntity.getUid(),"type",type+"");
//        }
//        if (type == 9){//封号
//            ChannelHandlerContext roleCtx = SuperServerHandler.getCtxFromUid(roleEntity.getUid());
//            if (roleCtx != null){
//                ReportManager.reportForceToOffline(roleCtx);
//            }
//        }
//        return result;
        int playId = jsonObject.getInt("playId");
        List<Object> list = new ArrayList<Object>();
        list.add(0, ctx);
        list.add(1, playId);
        list.add(2, jsonObject.getInt("type"));
        CallBack callBack = new LoginCallBack(CallBackOrder.SETPLAYERTYPEBACK);
        callBack.addParameterList(list);
        getRoleEntityFromDBById(playId, callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
    }

    // @Override
//    public UserData.ResponseApproval.Builder approval(String uid, int recognized, int type) {
//        UserData.ResponseApproval.Builder approvalBu = UserData.ResponseApproval.newBuilder();
//        String randType = null;
//        String recognizedtype = null;
//        String rediskey = null;
//        int limit = 0;
//        int nowApprovalNums = 0;
//        Redis redis = Redis.getInstance();
//        switch (type) {
//            case 1:
//                randType = "lvapproval";
//                recognizedtype = "lvApprovalnums";
//                rediskey = "lvApproval";
//                limit = SuperConfig.getLvApproval();
//                break;
//            case 2:
//                randType = "missionapproval";
//                rediskey = "missionApproval";
//                recognizedtype = "missionapprovalnums";
//                limit = SuperConfig.getMissionApproval();
//                break;
//            case 3:
//                randType = "endlessapproval";
//                rediskey = "endlesspproval";
//                recognizedtype = "endlesspprovalnums";
//                limit = SuperConfig.getEndlessApproval();
//                break;
//            case 6:
//                randType = "dressapproval";
//                rediskey = "dressApproval";
//                recognizedtype = "dressapprovalnums";
//                limit = SuperConfig.getDressApproval();
//                break;
//            default:
//                approvalBu.setErrorId(1);
//                return approvalBu;
//        }
//        nowApprovalNums = Integer.parseInt(redis.hget("role:" + uid, rediskey));
//        if (limit <= nowApprovalNums) {
//            approvalBu.setErrorId(1);
//        } else {
//            StringBuffer sql = new StringBuffer("update RoleEntity set ").append(randType).append("=").append(randType)
//                    .append("+1 where uid='").append(uid).append("'");
//            MySql.updateSomes(sql.toString());
//            StringBuffer hql = new StringBuffer("update RoleEntity set ").append(recognizedtype).append("=").append(recognizedtype)
//                    .append("+1 where id=").append(recognized);
//            MySql.updateSomes(hql.toString());
//            redis.hset("role:" + uid, rediskey, nowApprovalNums + 1 + "");
//            approvalBu.setErrorId(0);
//        }
//
//        return approvalBu;
//    }

    public UserData.ResponseGetLuckyItem.Builder getLuckyItem(String uid, int id) {
        UserData.ResponseGetLuckyItem.Builder builder = UserData.ResponseGetLuckyItem.newBuilder();
        Map<String, String> itemInfo = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_TIMING, id);
        String nums = itemInfo.get("num");
        String[] numRange = nums.split("\\|");
        int down = Integer.parseInt(numRange[0]);
        int up = Integer.parseInt(numRange[1]);
        long itemNums = (int) (Math.random() * (up - down + 1)) + down;
        int itemId = Integer.parseInt(itemInfo.get("item"));
        IItem itemDao = ItemDao.getInstance();
        double total = itemDao.updateItemInfo(uid, itemId, itemNums);
        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
        itemBu.setId(itemId);
        itemBu.setNum(total);
        builder.addItem(itemBu);
        ItemData.Item.Builder showBu = ItemData.Item.newBuilder();
        showBu.setId(itemId);
        showBu.setNum(itemNums);
        builder.addShowItem(showBu);
        StringBuffer sql = new StringBuffer("update RoleEntity set luckyItemId=0 where uid='").append(uid).append("'");
        MySql.updateSomes(sql.toString());
        Redis jedis = Redis.getInstance();
        jedis.hset("role:" + uid, "luckyItemId", "0");
        builder.setErrorId(0);
        return builder;
    }

    public TaskData.ResponseFinishActivities.Builder finishActivities(String uid, int id) {

        TaskData.ResponseFinishActivities.Builder builder = TaskData.ResponseFinishActivities.newBuilder();
        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ACTIVITIES, id);
        if (map == null || map.size() == 0) {
            builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            return builder;
        }
    /*    Redis jedis=Redis.getInstance();
        String activities=jedis.hget("roleactivities:"+uid,id+"");
        ActivitiesInfo activitiesInfo=(ActivitiesInfo)MyUtils.jsonToBean(activities,ActivitiesInfo.class);*/
        builder.setErrorId(0);
        String reward = map.get("item_id");

        builder.setActivitiesId(id);
        int change = Integer.parseInt(map.get("change"));
        StringBuffer sql = new StringBuffer("from ActivitiesEntity where uid='").append(uid).append("' and activitiesId=").append(id);
        ActivitiesEntity activitiesEntity = (ActivitiesEntity) MySql.queryForOne(sql.toString());
        if (change != 0) {
            if (change > 0 && activitiesEntity.getNum() >= change) {
                builder.setErrorId(1);
                return builder;
            } else {
                activitiesEntity.setNum(activitiesEntity.getNum() + 1);
            }


            String consume = map.get("target_num");
            String[] consumeItem = consume.split("\\|");
            for (int i = 0; i < consumeItem.length; i++) {
                int itemId = Integer.parseInt(consumeItem[i].split(",")[0]);
                int itemNums = Integer.parseInt(consumeItem[i].split(",")[1]);
                double total = ItemDao.getInstance().updateItemInfo(uid, itemId, -itemNums);
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(itemId);
                itemBu.setNum(total);
                builder.addItem(itemBu);
                //。。。。。。。
            }
              /*activitiesInfo.setActivitiesNowNum(activitiesInfo.getActivitiesNowNum()+1);
              jedis.hset("roleactivities:"+uid,id+"",MyUtils.objectToJson(activitiesInfo));*/
            StringBuffer updateSql = new StringBuffer("update ActivitiesEntity set num= ").append(activitiesEntity.getNum()).append("where uid='").append(uid).append("' and activitiesId=").append(id);
            MySql.updateSomes(updateSql.toString());
        } else {
            if (activitiesEntity.getStatus() == 1) {
                builder.setErrorId(1);
                return builder;
            }
             /* activitiesInfo.setStatus(1);
              jedis.hset("roleactivities:"+uid,id+"",MyUtils.objectToJson(activitiesInfo));*/
            StringBuffer updateSql = new StringBuffer("update ActivitiesEntity set status=1 where uid='").append(uid).append("' and activitiesId=").append(id);
            MySql.updateSomes(updateSql.toString());
        }
        TaskData.ReportUpdateActivities.Builder updateBuilder = TaskData.ReportUpdateActivities.newBuilder();
        TaskData.Activities.Builder activitiesBuilder = TaskData.Activities.newBuilder();
        activitiesBuilder.setType(activitiesEntity.getType());
        StringBuffer queryActivities = new StringBuffer("from ActivitiesEntity where uid='").append(uid).append("' and type=").append(activitiesEntity.getType());
        List<Object> activitiesList = MySql.queryForList(queryActivities.toString());
        for (int i = 0; i < activitiesList.size(); i++) {
            ActivitiesEntity entity = (ActivitiesEntity) activitiesList.get(i);
            if (entity.getActivitiesId() == id) {
                TaskData.ActivitiesDetail.Builder activitiesDetailBU = TaskData.ActivitiesDetail.newBuilder();
                activitiesDetailBU.setId(id);
                activitiesDetailBU.setReceive(true);
                activitiesDetailBU.setNowNum((int) activitiesEntity.getNum());
                activitiesBuilder.addActivitiesInfo(activitiesDetailBU);
            } else {
                TaskData.ActivitiesDetail.Builder activitiesDetailBU = TaskData.ActivitiesDetail.newBuilder();
                activitiesDetailBU.setId(entity.getActivitiesId());
                activitiesDetailBU.setReceive(entity.getStatus() == 1 ? true : false);
                activitiesDetailBU.setNowNum(entity.getNum());
                activitiesBuilder.addActivitiesInfo(activitiesDetailBU);
            }
        }
        updateBuilder.addActivities(activitiesBuilder);
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTUPDATEACTIVITIES_VALUE, updateBuilder.build().toByteArray());
        if (activitiesEntity.getType() == 11) {
            allClothes(uid, id, builder);
            return builder;
        }
        // 发放奖励
        String[] rewards = reward.split("\\|");
        for (int i = 0; i < rewards.length; i++) {
            int itemId = Integer.parseInt(rewards[i].split(",")[0]);
            int itemNums = Integer.parseInt(rewards[i].split(",")[1]);
            double total = ItemDao.getInstance().updateItemInfo(uid, itemId, itemNums);
            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setId(itemId);
            itemBu.setNum(total);
            builder.addItem(itemBu);
        }
        return builder;
    }

    public UserData.ResponseMarketInformation.Builder getMarketInformation(String uid) {
        UserData.ResponseMarketInformation.Builder builder = UserData.ResponseMarketInformation.newBuilder();
        Iterator<String> it = Redis.keys("goodsInfo*").iterator();
        while (it.hasNext()) {
            String key = it.next();
            Redis jedis = Redis.getInstance();
            Map<String, String> goodsConfig = jedis.hgetAll(key);
            UserData.Goods.Builder goodsBuilder = UserData.Goods.newBuilder();
            goodsBuilder.setFluctuate(Integer.parseInt(goodsConfig.get("fluctuate")));
            goodsBuilder.setGoodsId(Integer.parseInt(goodsConfig.get("id")));
            goodsBuilder.setNowPrice(Integer.parseInt(goodsConfig.get("nowPrice")));
            goodsBuilder.setScale(Integer.parseInt(goodsConfig.get("scale")));
            StringBuffer sql = new StringBuffer("from TicketmarketrecordEntity where uid='").append(uid).append("' and type=1 and goodsId=").append(goodsConfig.get("id"));
            List<Object> list = MySql.queryForList(sql.toString());
            int times = list.size();
            int totalNums = 0;
            int averagePrice = 0;
            if (times != 0) {
                int totalPrice = 0;
                for (int i = 0; i < list.size(); i++) {
                    TicketmarketrecordEntity entity = (TicketmarketrecordEntity) list.get(i);
                    totalPrice += (entity.getPriece() * entity.getNums());
                    totalNums += entity.getNums();
                }
                if (totalNums != 0) {
                    averagePrice = totalPrice / totalNums;
                } else {
                    averagePrice = 0;
                }

            }
            goodsBuilder.setNowAveragePrice(averagePrice);
            builder.addGoodsInformation(goodsBuilder);
        }
        StringBuffer sql = new StringBuffer("from TicketmarketrecordEntity where uid='").append(uid).append("' order by  time desc");
        int a = 0;
        List<Object> list = MySql.queryForList(sql.toString());
        for (int i = 0; i < list.size(); i++) {
            TicketmarketrecordEntity entity = (TicketmarketrecordEntity) list.get(i);
            UserData.ConsumeRecord.Builder consumeRecordBu = UserData.ConsumeRecord.newBuilder();
            if (entity.getType() == 1) {
                consumeRecordBu.setType(0);
            } else if (entity.getType() == 2) {
                consumeRecordBu.setType(1);
            } else {
                continue;
            }
            consumeRecordBu.setGoodsId(entity.getGoodsId());
            consumeRecordBu.setNums(entity.getNums());
            consumeRecordBu.setPriece(entity.getPriece());
            consumeRecordBu.setTime(entity.getTime().toString().substring(0, 19));
            builder.addRecord(consumeRecordBu);
            //   /// System.out.println((a++)+"Ticketmarketrecord"+ consumeRecordBu.getTime()+"type"+consumeRecordBu.getType()+"id"+consumeRecordBu.getGoodsId()+"nums"+consumeRecordBu.getNums()+"price"+entity.getPriece());
        }
        //  /// System.out.print(builder.getGoodsInformationCount()+"~~"+builder.getRecordCount()+"~~"+list.size());
        //三分钟倒计时
        builder.setHalfHourCountdown((60 * 3) - TimerHandler.halfHour);
        return builder;
    }

    public UserData.ResponseGoodsOperate.Builder goodsOperate(int type, int goodsId, int nums, String uid) {
        UserData.ResponseGoodsOperate.Builder builder = UserData.ResponseGoodsOperate.newBuilder();
        builder.setErrorId(0);
        builder.setType(type);
        builder.setGoodsID(goodsId);
        Redis jedis = Redis.getInstance();
        IItem itemDao = ItemDao.getInstance();
        String scale = jedis.hget("goodsInfo:" + goodsId, "scale");
        if (type == 1) {
            String nowPrice = jedis.hget("goodsInfo:" + goodsId, "nowPrice");
            String fluctuate = jedis.hget("goodsInfo:" + goodsId, "fluctuate");
            int ticketNums = Integer.parseInt(nowPrice);
            String ticketNumsString = jedis.hget("roleitem:" + uid + "#0", "379");
            if (ticketNumsString == null || (ticketNums * nums) > Double.parseDouble(ticketNumsString)) {
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
            double nowNums = itemDao.updateItemInfo(uid, 379, -ticketNums * nums);
            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setNum(nowNums);
            itemBu.setId(379);
            builder.addItem(itemBu);
            String goodsItemID = jedis.hget(SuperConfig.REDIS_EXCEL_TICKRT + ":" + goodsId, "item_id");
            ItemData.Item.Builder itemBu1 = ItemData.Item.newBuilder();
            double itemNums = itemDao.updateItemInfo(uid, Integer.parseInt(goodsItemID), nums);
            itemBu1.setNum(itemNums);
            itemBu1.setId(Integer.parseInt(goodsItemID));
            builder.addItem(itemBu1);
            TicketmarketrecordEntity entity = new TicketmarketrecordEntity();
            entity.setUid(uid);
            entity.setNums(nums);
            entity.setType(type);
            entity.setPriece(ticketNums);
            entity.setGoodsId(goodsId);
            MySql.insert(entity);
            UserData.Goods.Builder goodsBuilder = UserData.Goods.newBuilder();
            goodsBuilder.setNowPrice(ticketNums);
            goodsBuilder.setGoodsId(goodsId);
            goodsBuilder.setFluctuate(Integer.parseInt(fluctuate));
            goodsBuilder.setScale(Integer.parseInt(scale));


            UserData.ConsumeRecord.Builder consumeRecordBu = UserData.ConsumeRecord.newBuilder();
            consumeRecordBu.setGoodsId(entity.getGoodsId());
            consumeRecordBu.setNums(entity.getNums());
            consumeRecordBu.setPriece(entity.getPriece());
            consumeRecordBu.setType(entity.getType());
            consumeRecordBu.setTime(MyUtils.stampToDate(System.currentTimeMillis()));
            builder.setRecord(consumeRecordBu);
            //更新购入均价
            StringBuffer sql = new StringBuffer("from TicketmarketrecordEntity where uid='").append(uid).append("' and type=1 and goodsId=").append(goodsId);
            List<Object> list = MySql.queryForList(sql.toString());
            int times = list.size();
            int averagePrice = 0;
            int totalPrice = ticketNums * nums;
            int totalNums = 0;
            if (times != 0) {
                for (int i = 0; i < list.size(); i++) {
                    TicketmarketrecordEntity ticketmarketrecordentity = (TicketmarketrecordEntity) list.get(i);
                    totalPrice += (ticketmarketrecordentity.getPriece() * ticketmarketrecordentity.getNums());
                    totalNums += ticketmarketrecordentity.getNums();
                }
            }
            averagePrice = totalPrice / (totalNums + nums);
            //  /// System.out.println("times"+times+"totol"+totalPrice+"average"+averagePrice);
            // /// System.out.println("买入均价"+averagePrice);
            goodsBuilder.setNowAveragePrice(averagePrice);
            builder.setGoodsInformation(goodsBuilder);
            builder.setNowAveragePrice(averagePrice);
        } else if (type == 2) {
            Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_TICKRT, goodsId);
            String id = map.get("item_id");
            StringBuffer sql = new StringBuffer("from ItemEntity where uid='").append(uid).append("' and itemid=").append(id);
            ItemEntity itementity = (ItemEntity) MySql.queryForOne(sql.toString());
            if (itementity == null || itementity.getItemnum() == 0) {
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
            String nowPrice = jedis.hget("goodsInfo:" + goodsId, "nowPrice");
            String fluctuate = jedis.hget("goodsInfo:" + goodsId, "fluctuate");
            int ticketNums = Integer.parseInt(nowPrice);
            double nowNums = itemDao.updateItemInfo(uid, 379, ticketNums * nums);
            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setNum(nowNums);
            itemBu.setId(379);
            builder.addItem(itemBu);
            String goodsItemID = jedis.hget(SuperConfig.REDIS_EXCEL_TICKRT + ":" + goodsId, "item_id");
            ItemData.Item.Builder itemBu1 = ItemData.Item.newBuilder();
            double itemNums = itemDao.updateItemInfo(uid, Integer.parseInt(goodsItemID), -nums);
            itemBu1.setNum(itemNums);
            itemBu1.setId(Integer.parseInt(goodsItemID));
            builder.addItem(itemBu1);

            TicketmarketrecordEntity entity = new TicketmarketrecordEntity();
            entity.setUid(uid);
            entity.setNums(nums);
            entity.setType(type);
            entity.setPriece(ticketNums);
            entity.setGoodsId(goodsId);
            MySql.insert(entity);


            StringBuffer averageQuery = new StringBuffer("from TicketmarketrecordEntity where uid='").append(uid).append("' and type=1 and goodsId=").append(goodsId);
            List<Object> list = MySql.queryForList(averageQuery.toString());
            int times = list.size();
            int averagePrice = 0;
            int totalNums = 0;
            if (times != 0) {
                int totalPrice = 0;
                for (int i = 0; i < list.size(); i++) {
                    TicketmarketrecordEntity ticketmarketrecordentity = (TicketmarketrecordEntity) list.get(i);
                    totalPrice += (ticketmarketrecordentity.getPriece() * ticketmarketrecordentity.getNums());
                    totalNums += ticketmarketrecordentity.getNums();
                }
                averagePrice = totalPrice / (totalNums);
            }

            UserData.Goods.Builder goodsBuilder = UserData.Goods.newBuilder();
            goodsBuilder.setScale(Integer.parseInt(scale));
            goodsBuilder.setNowAveragePrice(averagePrice);
            //  /// System.out.println("卖出均价"+averagePrice);
            goodsBuilder.setNowPrice(ticketNums);
            goodsBuilder.setGoodsId(goodsId);
            goodsBuilder.setFluctuate(Integer.parseInt(fluctuate));

            builder.setGoodsInformation(goodsBuilder);
            UserData.ConsumeRecord.Builder consumeRecordBu = UserData.ConsumeRecord.newBuilder();
            consumeRecordBu.setGoodsId(entity.getGoodsId());
            consumeRecordBu.setNums(entity.getNums());
            consumeRecordBu.setPriece(entity.getPriece());
            consumeRecordBu.setType(entity.getType());
            consumeRecordBu.setTime(MyUtils.stampToDate(System.currentTimeMillis()));
            builder.setRecord(consumeRecordBu);
        }
        return builder;
    }

    public UserData.ResponseTickteExchange.Builder tickteExchange(int id, int nums, String uid) {
        UserData.ResponseTickteExchange.Builder builder = UserData.ResponseTickteExchange.newBuilder();
        Map<String, String> exchangeConfig = Redis.getExcelMap("changeTicketconfig", id);
        String itemId = exchangeConfig.get("item_id");
        String price = exchangeConfig.get("price");
        String exchange = exchangeConfig.get("get_num");
        StringBuffer TicketmarketrecordSql = new StringBuffer(" from TicketmarketrecordEntity where type=0 and uid='").append(uid).append("' and goodsId=").append(id);
        List<Object> ticketExchangeRecord = MySql.queryForList(TicketmarketrecordSql.toString());
        UserData.MarketExchange.Builder exchangeBu = UserData.MarketExchange.newBuilder();
        exchangeBu.setId(id);
        if (Integer.parseInt(exchange) <= ticketExchangeRecord.size()) {
            exchangeBu.setNums(0);
            builder.setExchangeInfo(exchangeBu);
            builder.setErrorId(1);
            //   /// System.out.println("~~~~"+ticketExchangeRecord.size());
            return builder;
        }
        Redis jedis = Redis.getInstance();
        String ticketNums = jedis.hget("roleitem:" + uid + "#0", "379");
        if (ticketNums == null || Double.parseDouble(ticketNums) < (nums * Integer.parseInt(price))) {
            exchangeBu.setNums(Integer.parseInt(exchange) - ticketExchangeRecord.size());
            builder.setExchangeInfo(exchangeBu);
            builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
            //  /// System.out.println(ticketNums+"~~~~"+(nums*Integer.parseInt(price)));
            return builder;
        }
        exchangeBu.setNums(Integer.parseInt(exchange) - ticketExchangeRecord.size() - 1);
        //  /// System.out.println(exchangeBu.getNums()+"nums"+exchange);
        builder.setExchangeInfo(exchangeBu);
        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
        IItem itemDao = ItemDao.getInstance();

        double itemNums = itemDao.updateItemInfo(uid, 379, -nums * Integer.parseInt(price));
        itemBu.setNum(itemNums);
        itemBu.setId(379);
        builder.addItem(itemBu);
        ItemData.Item.Builder itemBu1 = ItemData.Item.newBuilder();
        double itemNums1 = itemDao.updateItemInfo(uid, Integer.parseInt(itemId), nums);
        itemBu1.setNum(itemNums1);
        itemBu1.setId(Integer.parseInt(itemId));
        builder.addItem(itemBu1);
        builder.setErrorId(0);
        TicketmarketrecordEntity entity = new TicketmarketrecordEntity();
        entity.setType(0);
        entity.setGoodsId(id);
        entity.setNums(nums);
        entity.setUid(uid);
        entity.setPriece(Integer.parseInt(price));
        MySql.insert(entity);
        return builder;
    }

    public UserData.ResponseQueryRoleInformation.Builder queryRoleInformation(int roleId) {
        UserData.ResponseQueryRoleInformation.Builder builder = UserData.ResponseQueryRoleInformation.newBuilder();
        StringBuffer sql = new StringBuffer("from RoleEntity where id=").append(roleId);
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(sql.toString());
        FriendData.Player.Builder roleBuilder = FriendData.Player.newBuilder();
        roleBuilder.setHead(roleEntity.getHead());
        roleBuilder.setId(roleId);
        roleBuilder.setLv(roleEntity.getLv());
        roleBuilder.setName(roleEntity.getName());
//        roleBuilder.setStation(-1);
        builder.setPlayer(roleBuilder);
        return builder;
    }

    /*public UserData.ResponseWaitItem.Builder waitItem(String uid) {
        UserData.ResponseWaitItem.Builder builder=UserData.ResponseWaitItem.newBuilder();
        builder.setErrorId(0);
        StringBuffer queryWaitItem=new StringBuffer("from WaitItemEntity where  uid='").append(uid).append("'");
        WaitItemEntity waitItemEntity  =(WaitItemEntity) MySql.queryForOne(queryWaitItem.toString());
        //没有记录就去查询是否关卡通关达到资格
        if(waitItemEntity==null){
            builder.setBagLevel(0);
            Set<String> configSet=Redis.keys("waitItemconfig*");
            int itemTypeNums=configSet.size();
            int latelyWaitItemId=0;
            long latelyWaitItemTime=0;
            long now=System.currentTimeMillis();
         //   /// System.out.println(itemTypeNums);
            for(int i=1;i<=itemTypeNums;i++){
                Map<String,String> map=Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM,i);
                int needNums=Integer.parseInt(map.get("pass_num"));
                StringBuffer queryMission=new StringBuffer("from MissionEntity where  uid='").append(uid).append("' and type=1 and missionId=").append(i);
                MissionEntity missionEntity=(MissionEntity) MySql.queryForOne(queryMission.toString());

                if(missionEntity==null||missionEntity.getNums()<needNums){
                    continue; // 少于要求通关次数忽略
                }else{
              //      /// System.out.println(needNums+"needNums"+missionEntity.getNums());
                    //
                    if(waitItemEntity==null){
                        waitItemEntity=new WaitItemEntity();
                        waitItemEntity.setItemTypeNums(itemTypeNums);
                        waitItemEntity.setLatelyWaitItemId(i);
                        waitItemEntity.setUid(uid);
                        waitItemEntity.setBagLevel(0);
                        latelyWaitItemTime=now+Integer.parseInt(map.get("timing"))*1000;
                        latelyWaitItemId=Integer.parseInt(map.get("id"));
                       // waitItemEntity.setUpToParItemIdCollections((1<<i)+waitItemEntity.getUpToParItemIdCollections());
                        //达到通关次数生成一个相应记录

                        WaitItemInfoEntity waitItemInfoEntity=new WaitItemInfoEntity();
                        waitItemInfoEntity.setReward("0");
                        waitItemInfoEntity.setUid(uid);
                        waitItemInfoEntity.setLatelyFinishTime(latelyWaitItemTime);
                        waitItemInfoEntity.setWaitItemId(latelyWaitItemId);
                        MySql.insert(waitItemInfoEntity);
                    }else{
                       // waitItemEntity.setUpToParItemIdCollections((1<<i)+waitItemEntity.getUpToParItemIdCollections());
                       long waitItemTime=now+Integer.parseInt(map.get("timing"))*1000;
                       int waitItemId=Integer.parseInt(map.get("id"));
                        if(waitItemTime<latelyWaitItemTime){
                            latelyWaitItemTime=waitItemTime;
                            latelyWaitItemId=waitItemId;
                        }

                        WaitItemInfoEntity waitItemInfoEntity=new WaitItemInfoEntity();
                        waitItemInfoEntity.setReward("0");
                        waitItemInfoEntity.setUid(uid);
                        waitItemInfoEntity.setLatelyFinishTime(waitItemTime);
                        waitItemInfoEntity.setWaitItemId(waitItemId);
                        MySql.insert(waitItemInfoEntity);
                    }
                }
            }
            if(waitItemEntity==null){
                  builder.setErrorId(2);
                  builder.setTimeStamp(0);
            }else{
                waitItemEntity.setLatelyWaitItemId(latelyWaitItemId);
                waitItemEntity.setLatelyWaitTime(latelyWaitItemTime);
                MySql.insert(waitItemEntity);
                builder.setErrorId(3);
                builder.setTimeStamp(waitItemEntity.getLatelyWaitTime());
            }
        }else{
          Map<Integer ,ItemData.Item.Builder> itemMap=new HashMap<Integer ,ItemData.Item.Builder>();
            int bagLevel=waitItemEntity.getBagLevel();
            builder.setBagLevel(bagLevel);
            int bagLimite=0;
            if(bagLevel==0){
                bagLimite=30;
            }else {
                String bagInfo=SuperConfig.getBagLimite(bagLevel);
                bagLimite=Integer.parseInt(bagInfo.split("\\|")[1]);
            }
            long now=System.currentTimeMillis();

            StringBuffer queryWaitItemInfo =new StringBuffer("from WaitItemInfoEntity where uid='").append(uid).append("' order by latelyFinishTime asc");
           List<Object>  waitItemInfoList= MySql.queryForList(queryWaitItemInfo.toString());
           ArrayList<Integer> uptoQualificationItemIds =new ArrayList<Integer>();
           //已经生成的奖励记录
           for(int i=0;i<waitItemInfoList.size();i++){
               WaitItemInfoEntity waitItemInfoEntity=(WaitItemInfoEntity)waitItemInfoList.get(i);
               String rewardString=waitItemInfoEntity.getReward();
               uptoQualificationItemIds.add(waitItemInfoEntity.getWaitItemId());
               if("0".equals(rewardString)){
                   continue;
               }else{
                   String[] rewardsString=rewardString.split("\\|"); //1,1|1,1格式
                   int reward1Id=Integer.parseInt(rewardsString[0].split(",")[0]);
                   int reward1Nums=Integer.parseInt(rewardsString[0].split(",")[1]);
                   int reward2Id=Integer.parseInt(rewardsString[1].split(",")[0]);
                   int reward2Nums=Integer.parseInt(rewardsString[1].split(",")[1]);
                   //发送给前端的奖励
                   if(reward1Nums>0){
                       ItemData.Item.Builder itemBu=ItemData.Item.newBuilder();
                       itemBu.setId(reward1Id);
                       itemBu.setNum(reward1Nums);
                       integrateItemBuulder(itemMap,itemBu);

                   }
                   if(reward2Nums>0){
                       ItemData.Item.Builder itemBu=ItemData.Item.newBuilder();
                       itemBu.setId(reward2Id);
                       itemBu.setNum(reward2Nums);
                       integrateItemBuulder(itemMap,itemBu);
                   }
               }
           }
            int rewardTotalNums=waitItemEntity.getRewardTotalNums(); //总共的奖励数
            int latelyWaitItemId=waitItemEntity.getLatelyWaitItemId();
            long latelyWaitItemTime=waitItemEntity.getLatelyWaitTime();
            if(rewardTotalNums>=bagLimite){
                for(Map.Entry<Integer ,ItemData.Item.Builder> entry: itemMap.entrySet()){
                    builder.addItem(entry.getValue());
                }
                builder.setErrorId(1);
                builder.setTimeStamp(latelyWaitItemTime);

                return  builder;
            }
            //查询有达到通关关卡的记录，生成相应奖励
            for(int i=1;i<=waitItemEntity.getItemTypeNums();i++){
                if(uptoQualificationItemIds.contains(i)){
                    continue;
                }else{
                    Map<String,String> map=Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM,i);
                    int needNums=Integer.parseInt(map.get("pass_num"));
                    StringBuffer queryMission=new StringBuffer("from MissionEntity where  uid='").append(uid).append("' and type=1 and missionId=").append(i);
                    MissionEntity missionEntity=(MissionEntity) MySql.queryForOne(queryMission.toString());
                    if(missionEntity==null||missionEntity.getNums()<needNums){
                        continue;
                    }else{
                        if(rewardTotalNums>=bagLimite){
                            now=waitItemEntity.getLastQueryTime();
                        }
                        long waitItemTime=now+Integer.parseInt(map.get("timing"))*1000;
                        int waitItemId=Integer.parseInt(map.get("id"));
                        WaitItemInfoEntity waitItemInfoEntity=new WaitItemInfoEntity();
                        waitItemInfoEntity.setReward("0");
                        waitItemInfoEntity.setUid(uid);
                        waitItemInfoEntity.setLatelyFinishTime(waitItemTime);
                        waitItemInfoEntity.setWaitItemId(waitItemId);
                        MySql.insert(waitItemInfoEntity);
                        if(waitItemTime<waitItemEntity.getLatelyWaitTime()){
                            waitItemEntity.setLatelyWaitItemId(waitItemInfoEntity.getWaitItemId());
                            waitItemEntity.setLatelyWaitTime(waitItemInfoEntity.getLatelyFinishTime());
                        }
                    }
                }
            }
            if(latelyWaitItemTime>now){
                if(rewardTotalNums==0){
                    builder.setErrorId(3);
                }
                for(Map.Entry<Integer ,ItemData.Item.Builder> entry: itemMap.entrySet()){
                    builder.addItem(entry.getValue());
                }
               return builder;
            }

           Set<Integer> needUpdateIdSet=new HashSet<Integer>();  // 奖励记录有更改的才需要数据库更新
            //达到最大生产次数限制或者还没达到最近可生产时间就说明不会生产奖励
           while(rewardTotalNums<bagLimite&&((WaitItemInfoEntity)waitItemInfoList.get(0)).getLatelyFinishTime()<now){

               WaitItemInfoEntity waitItemInfoEntity =(WaitItemInfoEntity)waitItemInfoList.remove(0);
             //  /// System.err.println("开始"+waitItemInfoEntity+(now-waitItemInfoEntity.getLatelyFinishTime()));
             int waitItemId  =waitItemInfoEntity.getWaitItemId();
               needUpdateIdSet.add(waitItemId);
               Map<String,String> map=Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM,waitItemId);

              String probability =map.get("probability");
               List<String> wightList=new ArrayList<String>();
               String[] probabilityArrays=probability.split("\\|");
               for(int i=0;i<probabilityArrays.length;i++){
                   wightList.add(probabilityArrays[i]);
               }
               int rewardIndex=ranName.ranGift(wightList);
               String reward= waitItemInfoEntity.getReward();
             if(rewardIndex==0) {
                 int rewardItemId = Integer.parseInt(map.get("get_item").split("\\|")[rewardIndex]);
                 int rewardItemNum = Integer.parseInt(map.get("item_num").split("\\|")[rewardIndex]);
                 //
                 if(rewardItemNum+rewardTotalNums>=bagLimite){
                     rewardItemNum=bagLimite-rewardTotalNums;
                     rewardTotalNums=bagLimite;
                 }else{
                     rewardTotalNums+=rewardItemNum;
                 }
                 ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                 itemBu.setId(rewardItemId);
                 itemBu.setNum(rewardItemNum);
                 integrateItemBuulder(itemMap,itemBu);

                 if ("0".equals(reward)) {
                     reward = rewardItemId + "," + rewardItemNum + "|0,0";
                 } else {
                     rewardItemNum = Integer.parseInt(reward.split("\\|")[rewardIndex].split(",")[1])+rewardItemNum;
                     reward = rewardItemId + "," + rewardItemNum +"|"+ reward.split("\\|")[1];
                 }
              //   /// System.err.println(reward);
             }else{
                    int  rewardItemId=Integer.parseInt(map.get("get_item").split("\\|")[rewardIndex]);
                    int rewardItemNum=Integer.parseInt(map.get("item_num").split("\\|")[rewardIndex]);
                 if(rewardItemNum+rewardTotalNums>=bagLimite){
                     rewardItemNum=bagLimite-rewardTotalNums;
                     rewardTotalNums=bagLimite;
                 }else{
                     rewardTotalNums+=rewardItemNum;
                 }
                      ItemData.Item.Builder itemBu=ItemData.Item.newBuilder();
                      itemBu.setId(rewardItemId);
                      itemBu.setNum(rewardItemNum);
                 integrateItemBuulder(itemMap,itemBu);

                      if("0".equals(reward)){
                          reward="0,0|"+rewardItemId+","+rewardItemNum;
                      }else{
                          rewardItemNum=Integer.parseInt(reward.split("\\|")[rewardIndex].split(",")[1])+rewardItemNum;
                          reward=reward.split("\\|")[0]+"|"+rewardItemId+","+rewardItemNum;
                      }
             //    /// System.err.println(reward);
              }
               if(rewardTotalNums==bagLimite){
                   waitItemEntity.setLastQueryTime(waitItemInfoEntity.getLatelyFinishTime());
               }
               waitItemInfoEntity.setLatelyFinishTime(waitItemInfoEntity.getLatelyFinishTime()+Integer.parseInt(map.get("timing"))*1000);
             ///// System.out.println(waitItemInfoEntity.getLatelyFinishTime());
               //以最近产生奖励时间升序
               int index=0;
               for(int i=0;i<waitItemInfoList.size();i++){
                   WaitItemInfoEntity  entity  =(WaitItemInfoEntity)waitItemInfoList.get(i);
                   if(entity.getLatelyFinishTime()>waitItemInfoEntity.getLatelyFinishTime()){
                       break;
                   }
                   index++;
               }
               waitItemInfoEntity.setReward(reward);
               waitItemInfoList.add(index,waitItemInfoEntity);
            //   /// System.err.println(""+rewardTotalNums+waitItemInfoEntity);
           }

            WaitItemInfoEntity latelyWaitItemEntity = (WaitItemInfoEntity)waitItemInfoList.get(0);//最近完成的奖励是有序列表的第一个
            waitItemEntity.setLatelyWaitItemId(latelyWaitItemEntity.getWaitItemId());
            waitItemEntity.setLatelyWaitTime(latelyWaitItemEntity.getLatelyFinishTime());
           //更新最新的奖励数据
           for(int i=0;i<needUpdateIdSet.size();i++){
               WaitItemInfoEntity  entity  =(WaitItemInfoEntity)waitItemInfoList.get(i);
               if(needUpdateIdSet.contains(entity.getWaitItemId())){
                //   /// System.err.println(entity+"更新最新的奖励数据");
                   MySql.update(entity);
               }
           }

            waitItemEntity.setRewardTotalNums(rewardTotalNums);
            MySql.update(waitItemEntity);
            for(Map.Entry<Integer ,ItemData.Item.Builder> entry: itemMap.entrySet()){
          // builder.addItem(entry.getValue());
            }
        }
        return builder;
    }*/
    // 第二版收集箱
    public UserData.ResponseWaitItem.Builder waitItem(String uid) {
        UserData.ResponseWaitItem.Builder builder = UserData.ResponseWaitItem.newBuilder();
        builder.setErrorId(0);
        builder.setStatus(0);
        synchronized (uid) {
            StringBuffer queryWaitItem = new StringBuffer("from WaitItemEntity where  uid='").append(uid).append("'");
            WaitItemEntity waitItemEntity = (WaitItemEntity) MySql.queryForOne(queryWaitItem.toString());
            Redis jedis = Redis.getInstance();
            //没有记录就去查询是否关卡通关达到资格
            if (waitItemEntity == null) {
                builder.setBagLevel(0);
                Set<String> configSet = Redis.keys("waitItemconfig*");
                int itemTypeNums = configSet.size();
                int latelyWaitItemId = 0;
                long latelyWaitItemTime = 0;
                long now = System.currentTimeMillis();
                //   /// System.out.println(itemTypeNums);
                for (int i = 1; i <= itemTypeNums; i++) {
                    Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM, i);
                    int needNums = Integer.parseInt(map.get("pass_num"));
                    int rewardId = Integer.parseInt(map.get("get_item"));
              /*StringBuffer queryMission = new StringBuffer("from MissionEntity where  uid='").append(uid).append("' and type=1 and missionId=").append(i);
              MissionEntity missionEntity = (MissionEntity) MySql.queryForOne(queryMission.toString());*/
                    String key = "rolemission:" + uid + "#1";
                    String missionNums = jedis.hget(key, "" + i);
                    if (missionNums == null || Integer.parseInt(missionNums) < needNums) {
                        continue; // 少于要求通关次数忽略
                    } else {
                        //      /// System.out.println(needNums+"needNums"+missionEntity.getNums());
                        //
                        if (waitItemEntity == null) {
                            waitItemEntity = new WaitItemEntity();
                            waitItemEntity.setItemTypeNums(itemTypeNums);
                            waitItemEntity.setLatelyWaitItemId(i);
                            waitItemEntity.setUid(uid);
                            waitItemEntity.setBagLevel(0);
                            latelyWaitItemTime = now + Integer.parseInt(map.get("timing")) * 1000;
                            latelyWaitItemId = Integer.parseInt(map.get("id"));
                            // waitItemEntity.setUpToParItemIdCollections((1<<i)+waitItemEntity.getUpToParItemIdCollections());
                            //达到通关次数生成一个相应记录

                            WaitItemInfoEntity waitItemInfoEntity = new WaitItemInfoEntity();
                            waitItemInfoEntity.setRewardId(rewardId);
                            waitItemInfoEntity.setReward(0);
                            waitItemInfoEntity.setUid(uid);
                            waitItemInfoEntity.setLatelyFinishTime(latelyWaitItemTime);
                            waitItemInfoEntity.setWaitItemId(latelyWaitItemId);
                            MySql.insert(waitItemInfoEntity);

                            UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                            waitItemInfoBu.setMissionId(waitItemInfoEntity.getWaitItemId());
                            waitItemInfoBu.setNextProduceTime(Integer.parseInt(map.get("timing")));
                            waitItemInfoBu.setRewardNums(waitItemInfoEntity.getReward());
                            builder.addMissionWaitItemInfo(waitItemInfoBu);
                        } else {
                            // waitItemEntity.setUpToParItemIdCollections((1<<i)+waitItemEntity.getUpToParItemIdCollections());
                            long waitItemTime = now + Integer.parseInt(map.get("timing")) * 1000;
                            int waitItemId = Integer.parseInt(map.get("id"));

                            if (waitItemTime < latelyWaitItemTime) {
                                latelyWaitItemTime = waitItemTime;
                                latelyWaitItemId = waitItemId;
                            }

                            WaitItemInfoEntity waitItemInfoEntity = new WaitItemInfoEntity();
                            waitItemInfoEntity.setReward(0);
                            waitItemInfoEntity.setUid(uid);
                            waitItemInfoEntity.setLatelyFinishTime(waitItemTime);
                            waitItemInfoEntity.setWaitItemId(waitItemId);
                            waitItemInfoEntity.setRewardId(rewardId);
                            MySql.insert(waitItemInfoEntity);
                            UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                            waitItemInfoBu.setMissionId(waitItemInfoEntity.getWaitItemId());
                            // waitItemInfoBu.setNextProduceTime((int) (waitItemInfoEntity.getLatelyFinishTime() - now) / 1000);
                            waitItemInfoBu.setNextProduceTime((int) Math.ceil((waitItemInfoEntity.getLatelyFinishTime() - now) / 1000f));
                            //  /// System.err.println(Math.ceil((waitItemInfoEntity.getLatelyFinishTime() - now) / 1000f));
                            waitItemInfoBu.setRewardNums(waitItemInfoEntity.getReward());
                            builder.addMissionWaitItemInfo(waitItemInfoBu);
                        }
                    }
                }
                if (waitItemEntity == null) {
                    builder.setErrorId(2);
                } else {
                    waitItemEntity.setLatelyWaitItemId(latelyWaitItemId);
                    waitItemEntity.setLatelyWaitTime(latelyWaitItemTime);
                    MySql.insert(waitItemEntity);
                    builder.setErrorId(3);
                }
            } else {

                //
                int bagLevel = waitItemEntity.getBagLevel();
                builder.setBagLevel(bagLevel);
                int bagLimite = 0;
                if (bagLevel == 0) {
                    bagLimite = 30;
                } else {
                    String bagInfo = SuperConfig.getBagLimite(bagLevel);
                    bagLimite = Integer.parseInt(bagInfo.split("\\|")[1]);
                }
                long now = System.currentTimeMillis();

                StringBuffer queryWaitItemInfo = new StringBuffer("from WaitItemInfoEntity where uid='").append(uid).append("' order by latelyFinishTime asc");
                List<Object> waitItemInfoList = MySql.queryForList(queryWaitItemInfo.toString());
                ArrayList<Integer> uptoQualificationItemIds = new ArrayList<Integer>();
                //已经生成的奖励记录
                for (int i = 0; i < waitItemInfoList.size(); i++) {
                    WaitItemInfoEntity waitItemInfoEntity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                    uptoQualificationItemIds.add(waitItemInfoEntity.getWaitItemId());
                    //发送给前端的奖励
                }
                int rewardTotalNums = waitItemEntity.getRewardTotalNums(); //总共的奖励数
                int latelyWaitItemId = waitItemEntity.getLatelyWaitItemId();
                long latelyWaitItemTime = waitItemEntity.getLatelyWaitTime();
                if (rewardTotalNums >= bagLimite) {
                    builder.setErrorId(1);
                    builder.setStatus(1);
                    for (int i = 0; i < waitItemInfoList.size(); i++) {
                        WaitItemInfoEntity entity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                        UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                        waitItemInfoBu.setMissionId(entity.getWaitItemId());
                        long latelyFinishTime = entity.getLatelyFinishTime();
                        if (latelyFinishTime == -1) {
                            Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM, entity.getWaitItemId());
                            waitItemInfoBu.setNextProduceTime(Integer.parseInt(map.get("timing")));
                        } else {
                            waitItemInfoBu.setNextProduceTime((int) Math.ceil((latelyFinishTime - waitItemEntity.getLastQueryTime()) / 1000f));
                        }
                        waitItemInfoBu.setRewardNums(entity.getReward());
                        builder.addMissionWaitItemInfo(waitItemInfoBu);
                    }

                    //查询有达到通关关卡的记录，生成相应奖励
                    for (int i = 1; i <= waitItemEntity.getItemTypeNums(); i++) {
                        if (uptoQualificationItemIds.contains(i)) {
                            continue;
                        } else {

                            Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM, i);
                            int needNums = Integer.parseInt(map.get("pass_num"));
                            String key = "rolemission:" + uid + "#1";
                            String missionNums = jedis.hget(key, "" + i);
                            if (missionNums == null || Integer.parseInt(missionNums) < needNums) {
                                continue;
                            } else {
                                if (rewardTotalNums >= bagLimite) {
                                    now = waitItemEntity.getLastQueryTime();
                                }
                                int addTime = Integer.parseInt(map.get("timing")) * 1000;
                                long waitItemTime = now + addTime;
                                int waitItemId = Integer.parseInt(map.get("id"));
                                int rewardId = Integer.parseInt(map.get("get_item"));
                                WaitItemInfoEntity waitItemInfoEntity = new WaitItemInfoEntity();
                                waitItemInfoEntity.setReward(0);
                                waitItemInfoEntity.setUid(uid);
                                waitItemInfoEntity.setRewardId(rewardId);

                                if (rewardTotalNums >= bagLimite) {
                                    waitItemInfoEntity.setLatelyFinishTime(-1);
                                } else {
                                    waitItemInfoEntity.setLatelyFinishTime(waitItemTime);
                                }
                                waitItemInfoEntity.setWaitItemId(waitItemId);
                                MySql.insert(waitItemInfoEntity);
                                if (waitItemTime < waitItemEntity.getLatelyWaitTime()) {
                                    waitItemEntity.setLatelyWaitItemId(waitItemInfoEntity.getWaitItemId());
                                    waitItemEntity.setLatelyWaitTime(waitItemInfoEntity.getLatelyFinishTime());
                                }
                                //
                                UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                                waitItemInfoBu.setMissionId(waitItemId);
                                waitItemInfoBu.setNextProduceTime((int) Math.ceil(addTime / 1000f));
                                waitItemInfoBu.setRewardNums(0);
                                builder.addMissionWaitItemInfo(waitItemInfoBu);
                            }

                        }
                    }
                    return builder;
                }

                if (latelyWaitItemTime > now) {
                    if (rewardTotalNums == 0) {
                        builder.setErrorId(3);
                    }
                    for (int i = 0; i < waitItemInfoList.size(); i++) {
                        WaitItemInfoEntity entity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                        UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                        waitItemInfoBu.setMissionId(entity.getWaitItemId());
                        waitItemInfoBu.setNextProduceTime((int) Math.ceil((entity.getLatelyFinishTime() - now) / 1000f));
                        //  /// System.err.println((entity.getLatelyFinishTime() - now) / 1000f);
                        waitItemInfoBu.setRewardNums(entity.getReward());
                        builder.addMissionWaitItemInfo(waitItemInfoBu);
                    }

                    return builder;
                }

                Set<Integer> needUpdateIdSet = new HashSet<Integer>();  // 奖励记录有更改的才需要数据库更新
                //达到最大生产次数限制或者还没达到最近可生产时间就说明不会生产奖励

                while (rewardTotalNums < bagLimite && ((WaitItemInfoEntity) waitItemInfoList.get(0)).getLatelyFinishTime() < now) {
                    WaitItemInfoEntity waitItemInfoEntity = (WaitItemInfoEntity) waitItemInfoList.remove(0);
                    //  /// System.err.println("开始"+waitItemInfoEntity+(now-waitItemInfoEntity.getLatelyFinishTime()));
                    int waitItemId = waitItemInfoEntity.getWaitItemId();
                    needUpdateIdSet.add(waitItemId);
                    Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM, waitItemId);
                    String numString = map.get("item_num");
                    int dwon = Integer.parseInt(numString.split("\\|")[0]);
                    int up = Integer.parseInt(numString.split("\\|")[1]);
                    int rewardNums = (int) (Math.random() * (up - dwon)) + dwon;
                    if (rewardNums + rewardTotalNums >= bagLimite) {
                        rewardNums = bagLimite - rewardTotalNums;
                    }
                    rewardTotalNums += rewardNums;
                    waitItemEntity.setRewardTotalNums(rewardTotalNums);
                    //   /// System.err.println(rewardNums+"rewardNums"+"~~"+rewardTotalNums+"rewardTotalNums");
                    if (rewardTotalNums == bagLimite) {
                        waitItemEntity.setLastQueryTime(waitItemInfoEntity.getLatelyFinishTime());
                    }
                    waitItemInfoEntity.setLatelyFinishTime(waitItemInfoEntity.getLatelyFinishTime() + Integer.parseInt(map.get("timing")) * 1000);
                    ///// System.out.println(waitItemInfoEntity.getLatelyFinishTime());
                    //以最近产生奖励时间升序
                    int index = 0;
                    for (int i = 0; i < waitItemInfoList.size(); i++) {
                        WaitItemInfoEntity entity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                        if (entity.getLatelyFinishTime() > waitItemInfoEntity.getLatelyFinishTime()) {
                            break;
                        }
                        index++;
                    }
                    waitItemInfoEntity.setReward(waitItemInfoEntity.getReward() + rewardNums);

                    waitItemInfoList.add(index, waitItemInfoEntity);

                }

                WaitItemInfoEntity latelyWaitItemEntity = (WaitItemInfoEntity) waitItemInfoList.get(0);//最近完成的奖励是有序列表的第一个
                waitItemEntity.setLatelyWaitItemId(latelyWaitItemEntity.getWaitItemId());
                waitItemEntity.setLatelyWaitTime(latelyWaitItemEntity.getLatelyFinishTime());

                for (int i = 0; i < waitItemInfoList.size(); i++) {
                    WaitItemInfoEntity entity = (WaitItemInfoEntity) waitItemInfoList.get(i);
                    if (needUpdateIdSet.contains(entity.getWaitItemId())) {
                        //更新最新的奖励数据
                        MySql.fastUpdate(entity);
                    }
                    UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                    waitItemInfoBu.setMissionId(entity.getWaitItemId());
                    if (bagLimite == rewardTotalNums) {

                        waitItemInfoBu.setNextProduceTime((int) Math.ceil((entity.getLatelyFinishTime() - waitItemEntity.getLastQueryTime()) / 1000f));
                    } else {

                        waitItemInfoBu.setNextProduceTime((int) Math.ceil((entity.getLatelyFinishTime() - now) / 1000f));

                    }
                    waitItemInfoBu.setRewardNums(entity.getReward());
                    builder.addMissionWaitItemInfo(waitItemInfoBu);
                }
                waitItemEntity.setRewardTotalNums(rewardTotalNums);
                MySql.fastUpdate(waitItemEntity);


                if (bagLimite == rewardTotalNums) {
                    builder.setStatus(1);
                }
                //查询有达到通关关卡的记录，生成相应奖励
                for (int i = 1; i <= waitItemEntity.getItemTypeNums(); i++) {
                    if (uptoQualificationItemIds.contains(i)) {
                        continue;
                    } else {

                        Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_WAITITEM, i);
                        int needNums = Integer.parseInt(map.get("pass_num"));
                        String key = "rolemission:" + uid + "#1";
                        String missionNums = jedis.hget(key, "" + i);
                        if (missionNums == null || Integer.parseInt(missionNums) < needNums) {
                            continue;
                        } else {
                            if (rewardTotalNums >= bagLimite) {
                                now = waitItemEntity.getLastQueryTime();
                            }
                            int addTime = Integer.parseInt(map.get("timing")) * 1000;
                            long waitItemTime = now + addTime;
                            int waitItemId = Integer.parseInt(map.get("id"));
                            int rewardId = Integer.parseInt(map.get("get_item"));
                            WaitItemInfoEntity waitItemInfoEntity = new WaitItemInfoEntity();
                            waitItemInfoEntity.setReward(0);
                            waitItemInfoEntity.setUid(uid);
                            waitItemInfoEntity.setRewardId(rewardId);

                            if (rewardTotalNums >= bagLimite) {
                                waitItemInfoEntity.setLatelyFinishTime(-1);
                            } else {
                                waitItemInfoEntity.setLatelyFinishTime(waitItemTime);
                            }
                            waitItemInfoEntity.setWaitItemId(waitItemId);
                            MySql.insert(waitItemInfoEntity);
                            if (waitItemTime < waitItemEntity.getLatelyWaitTime()) {
                                waitItemEntity.setLatelyWaitItemId(waitItemInfoEntity.getWaitItemId());
                                waitItemEntity.setLatelyWaitTime(waitItemInfoEntity.getLatelyFinishTime());
                            }
                            //
                            UserData.MissionWaitItemInfo.Builder waitItemInfoBu = UserData.MissionWaitItemInfo.newBuilder();
                            waitItemInfoBu.setMissionId(waitItemId);
                            waitItemInfoBu.setNextProduceTime((int) Math.ceil(addTime / 1000f));
                            waitItemInfoBu.setRewardNums(0);
                            builder.addMissionWaitItemInfo(waitItemInfoBu);
                        }

                    }
                }
            }

            return builder;
        }
    }

    @Override
    public RankData.ResponseRank.Builder rank(String uid, int mold) {
        RankData.ResponseRank.Builder rankBu=RankData.ResponseRank.newBuilder();
        String rankMold=null;
        String rediskey=null;
        int limit=0;
        int nowRank=0;
        Redis redis=Redis.getInstance();
//        switch (mold){
//            case 1:
//                rankMold="lv";
//                rediskey="lv";
//                limit=SuperConfig.getLv();
//                break;
//            case 2:
//                rankMold="ranknumber";
//                rediskey="ranknumber";
//                limit=SuperConfig.getRankNumber();
//                break;
//            default:
//                rankBu.setErrorId(1);
//                return  rankBu;
//        }
//        nowRank=Integer.parseInt(redis.hget("role"+uid,rediskey));
//        if(limit<=nowRank){
//            rankBu.setErrorId(1);
//        }else{
//            StringBuffer sql=new StringBuffer("update RoleEntity set ").append(rankMold).append("=").append(rankMold)
//                    .append("+1 where uid='").append(uid).append("'");
//            MySql.updateSomes(sql.toString());
//            redis.hset("role:"+uid,rediskey,nowRank+1+"");
//            rankBu.setErrorId(0);
//        }
        return rankBu;
    }


    private Map<Integer, ItemData.Item.Builder> integrateItemBuulder(Map<Integer, ItemData.Item.Builder> map, ItemData.Item.Builder itemBu) {
        int itemId = itemBu.getId();
        ItemData.Item.Builder item = map.get(itemId);
        if (item == null) {
            map.put(itemId, itemBu);
        } else {
            item.setNum(itemBu.getNum() + item.getNum());
        }
        return map;
    }

    public TaskData.ResponseFinishActivities.Builder allClothes(String uid, int activitiesId, TaskData.ResponseFinishActivities.Builder builder) {
        List<ItemData.Item.Builder> itemList = new ArrayList<ItemData.Item.Builder>();

        Map<String, String> activitiesConfigMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ACTIVITIES, activitiesId);
        String[] dressInfo = activitiesConfigMap.get("item_id").split("\\|");

        Map<Integer, Integer> addClothesMap = new HashMap<Integer, Integer>();
        for (int i = 0; i < dressInfo.length; i++) {
            addClothesMap.put(Integer.parseInt(dressInfo[i].split(",")[0]), Integer.parseInt(dressInfo[i].split(",")[1]));
        }
        LoginDao loginDao = LoginDao.getInstance();
        String key1 = "roledress:" + uid + "#" + 1;
        RoleDressInfo roleDressInfo1 = loginDao.getRoleDressFromRedis(key1);
        List<UtilityInfo> dressList1 = roleDressInfo1.getDressList();
        String key2 = "roledress:" + uid + "#" + 2;
        RoleDressInfo roleDressInfo2 = loginDao.getRoleDressFromRedis(key2);
        List<UtilityInfo> dressList2 = (roleDressInfo2 == null ? new ArrayList<UtilityInfo>() : roleDressInfo2.getDressList());
        String key3 = "roledress:" + uid + "#" + 3;
        RoleDressInfo roleDressInfo3 = loginDao.getRoleDressFromRedis(key3);
        List<UtilityInfo> dressList3 = (roleDressInfo3 == null ? new ArrayList<UtilityInfo>() : roleDressInfo3.getDressList());
        int roleId = 0;
        List<Object> hqlObj = new ArrayList<Object>();
        List<CommonData.Utility> utilityList = new ArrayList<CommonData.Utility>();
        List<UserData.Clothing> clothingList = new ArrayList<UserData.Clothing>();
        for (Map.Entry<Integer, Integer> entry : addClothesMap.entrySet()) {
            int dressId = entry.getKey();
            int type = entry.getValue();
            long addTime = loginDao.getAddTime(type);
            Map<String, String> dressMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_DRESS, dressId);
            roleId = Integer.parseInt(dressMap.get("mod"));
            UtilityInfo utilityInfo = null;
            List<UtilityInfo> dressList = null;
            switch (roleId) {
                case 1:
                    dressList = dressList1;
                    break;
                case 2:
                    dressList = dressList2;
                    break;
                case 3:
                    dressList = dressList3;
                    break;
            }
            for (int j = 0; j < dressList.size(); j++) {
                if (dressList.get(j).getId() == dressId) {
                    utilityInfo = dressList.get(j);
                    break;
                }
            }

            if (utilityInfo == null) {

                utilityInfo = new UtilityInfo();
                utilityInfo.setId(dressId);
                long overTime = addTime == 0 ? 0 : (TimerHandler.nowTimeStamp + addTime);
                utilityInfo.setOverStamp(overTime + "");
                dressList.add(utilityInfo);
                DressEntity dressEntity = new DressEntity();
                dressEntity.setUid(uid);
                dressEntity.setRoleid(roleId);
                dressEntity.setDressid(dressId);
                dressEntity.setOverstamp(overTime + "");
                dressEntity.setType(1);
                hqlObj.add(dressEntity);
                if (addTime == 0) {
                    /// System.err.println(dressId);
                    UserData.Clothing.Builder clothing = UserData.Clothing.newBuilder();
                    clothing.setDressId(dressId);
                    clothing.setType(3);
                    clothingList.add(clothing.build());
                }
            } else {
                if (utilityInfo.getOverStamp().equals("0")) {
                    continue;
                }
                if (addTime == 0) {
                    UserData.Clothing.Builder clothing = UserData.Clothing.newBuilder();
                    clothing.setDressId(dressId);
                    clothing.setType(3);
                    clothingList.add(clothing.build());
                    //  /// System.err.println(dressId+"~~~~");
                }
                long overTime = addTime == 0 ? 0 : (Long.parseLong(utilityInfo.getOverStamp()) + addTime);
                utilityInfo.setOverStamp(overTime + "");
                StringBuffer hql = new StringBuffer("update DressEntity set overstamp = '").append(overTime).append("' where uid = '")
                        .append(uid).append("' and roleid = ").append(roleId).append(" and dressid = ").append(dressId);
                hqlObj.add(hql.toString());
            }
            CommonData.Utility.Builder utilityBu = CommonData.Utility.newBuilder();
            utilityBu.setId(utilityInfo.getId());
            utilityBu.setTimeStamp(utilityInfo.getOverStamp());
            utilityList.add(utilityBu.build());
        }
        //衣物sql
        for (int i = 0; i < hqlObj.size(); i++) {
            Object object = hqlObj.get(i);
            if (object instanceof DressEntity) {
                MySql.insert(object);
            } else if (object instanceof String) {
                MySql.updateSomes(object.toString());
            } else {
                /// System.out.println("???????");
            }
        }
        Redis jedis = Redis.getInstance();
        jedis.hset("roledress:" + uid + "#" + 1, "dresslist", MyUtils.objectToJson(dressList1));
        if (roleDressInfo2 != null) {
            jedis.hset("roledress:" + uid + "#" + 2, "dresslist", MyUtils.objectToJson(dressList2));
        }
        if (roleDressInfo3 != null) {
            jedis.hset("roledress:" + uid + "#" + 3, "dresslist", MyUtils.objectToJson(dressList3));
        }
        builder.addAllClothingList(utilityList);
       /*   TaskData.ResponseFinishActivities.Builder builder=TaskData.ResponseFinishActivities.newBuilder();

          builder.addAllClothingList(utilityList);
          StringBuffer updateSql  =new StringBuffer("update ActivitiesEntity set status=1 where uid='").append(uid).append("' and activitiesId=").append(activitiesId );
          MySql.updateSomes(updateSql.toString());
          builder.setActivitiesId(activitiesId);
          builder.setErrorId(0);*/
        //    ReportManager.reportInfo(uid,uid,ProtoData.SToC.RESPOSEFINISHACTIVITIES,builder.build().toByteArray());
  /*        TaskData.ReportUpdateActivities.Builder updateBuilder=TaskData.ReportUpdateActivities.newBuilder();
          TaskData.Activities.Builder activitiesBuilder=TaskData.Activities.newBuilder();
          int activitiesType=Integer.parseInt(activitiesConfigMap.get("activities_type"));
          activitiesBuilder.setType(activitiesType);
          StringBuffer queryActivities =new StringBuffer("from ActivitiesEntity where uid='").append(uid).append("' and type=").append(activitiesType);
          List<Object> activitiesList=MySql.queryForList(queryActivities.toString());
          for(int i=0;i<activitiesList.size();i++){
              ActivitiesEntity entity  =(ActivitiesEntity)activitiesList.get(i);
              if(entity.getActivitiesId()==activitiesId){
                  TaskData.ActivitiesDetail.Builder activitiesDetailBU=  TaskData.ActivitiesDetail.newBuilder();
                  activitiesDetailBU.setId(activitiesId);
                  activitiesDetailBU.setReceive(true);
                  activitiesDetailBU.setNowNum(1);
                  activitiesBuilder.addActivitiesInfo(activitiesDetailBU);
              }else{
                  TaskData.ActivitiesDetail.Builder activitiesDetailBU=  TaskData.ActivitiesDetail.newBuilder();
                  activitiesDetailBU.setId(entity.getActivitiesId());
                  activitiesDetailBU.setReceive(entity.getStatus()==1?true:false);
                  activitiesDetailBU.setNowNum(entity.getNum());
                  activitiesBuilder.addActivitiesInfo(activitiesDetailBU);
              }
          }
          updateBuilder.addActivities(activitiesBuilder);
          ReportManager.reportInfo(uid,ProtoData.SToC.RESPOSEFINISHACTIVITIES_VALUE,builder.build().toByteArray());
          ReportManager.reportInfo(uid,ProtoData.SToC.REPORTUPDATEACTIVITIES_VALUE,updateBuilder.build().toByteArray());
         /// System.err.println(JsonFormat.printToString(updateBuilder.build()));
       /// System.err.println(JsonFormat.printToString(builder.build()));*/
        loginDao.updateDressAchievement(uid, clothingList);
        // /// System.err.println(JsonFormat.printToString(builder.build()));
        return builder;
    }

    int updateFurniture(String uid, int furnitureId, int nums) {
        int value = 0;
        Redis jedis = Redis.getInstance();
        String key = "roleFurniture:" + uid;
        String furnitureNums = jedis.hget(key, "" + furnitureId);
        ///  /// System.err.println("furnitureNums:"+furnitureNums);
        if (furnitureNums == null) {
            if (Redis.exists(key)) {
                value = nums;
                FurnitureShopEntity furnitureShopEntity = new FurnitureShopEntity();
                furnitureShopEntity.setFurnitureId(furnitureId);
                furnitureShopEntity.setNums(nums);
                furnitureShopEntity.setUid(uid);
                MySql.insert(furnitureShopEntity);
                jedis.hset(key, "" + furnitureId, value + "");
            } else {
                StringBuffer sql = new StringBuffer("from FurnitureShopEntity where uid='").append(uid).append("'");
                List<Object> furnituresList = MySql.queryForList(sql.toString());
                if (furnituresList.size() == 0) {
                    FurnitureShopEntity furnitureShopEntity = new FurnitureShopEntity();
                    furnitureShopEntity.setFurnitureId(furnitureId);
                    furnitureShopEntity.setNums(nums);
                    furnitureShopEntity.setUid(uid);
                    MySql.insert(furnitureShopEntity);
                    value = nums;
                    jedis.hset(key, "" + furnitureId, value + "");
                } else {
                    boolean needInsert = true;
                    Map<String, String> furnituresMap = new HashMap<String, String>();
                    for (int i = 0; i < furnituresList.size(); i++) {
                        FurnitureShopEntity furnitureShopEntity = (FurnitureShopEntity) furnituresList.get(i);
                        int id = furnitureShopEntity.getFurnitureId();
                        if (id == furnitureId) {
                            furnitureShopEntity.setNums(furnitureShopEntity.getNums() + nums);
                            value = furnitureShopEntity.getNums();
                            MySql.update(furnitureShopEntity);
                            needInsert = false;
                        }
                        furnituresMap.put(id + "", value + "");
                    }
                    if (needInsert) {
                        FurnitureShopEntity furnitureShopEntity = new FurnitureShopEntity();
                        furnitureShopEntity.setFurnitureId(furnitureId);
                        furnitureShopEntity.setNums(nums);
                        value = nums;
                        furnitureShopEntity.setUid(uid);
                        MySql.insert(furnitureShopEntity);
                        furnituresMap.put(furnitureId + "", value + "");
                    }
                    jedis.hmset(key, furnituresMap);
                }
            }
        } else {
            value = Integer.parseInt(furnitureNums) + nums;
            StringBuffer sql = new StringBuffer("update FurnitureShopEntity set nums=").append(value).append(" where uid='").append(uid).append("' and furnitureId=").append(furnitureId);
            MySql.updateSomes(sql.toString());
            jedis.hset(key, furnitureId + "", value + "");
        }

        return value;
    }


    public int getRoleLv(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from Role where uid='").append(uid).append("'");
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuffer.toString());
        return roleEntity.getLv();

    }

    public int getRoleExp(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from ItemEntity where uid='").append(uid).append("' and type=").append(4);
        ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(stringBuffer.toString());
        return (int) itemEntity.getItemnum();

    }

    public CommonInfo roleAddExp(String uid, int addExp) {
        StringBuffer stringBuffer = new StringBuffer("from RoleEntity where uid='").append(uid).append("'");
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuffer.toString());
        StringBuffer sb = new StringBuffer("from ItemEntity where uid='").append(uid).append("' and itemid=").append(4);
        ItemEntity itemEntity = (ItemEntity) MySql.queryForOne(sb.toString());
        if (itemEntity == null) {
            /// System.out.println(sb);
        }
        int curLv = roleEntity.getLv();
        int curExp = (int) itemEntity.getItemnum();
        try {
            RoleExpConfig roleExpConfig = (RoleExpConfig) SuperConfig.getCongifObject(SuperConfig.roleExpConfig, curLv);
            int lvUpNeedExp = roleExpConfig.getToLevelNeedExp();
            curExp += addExp;
            int uplv = 0;
            while (curExp >= lvUpNeedExp) {
                curExp -= lvUpNeedExp;
                curLv++;
                uplv++;
                roleExpConfig = (RoleExpConfig) SuperConfig.getCongifObject(SuperConfig.roleExpConfig, curLv);
                if (roleExpConfig == null) {
                    curExp = 0;
                    break;//达到最高级
                }
                lvUpNeedExp = roleExpConfig.getToLevelNeedExp();
            }
            if (uplv != 0) {
                roleEntity.setLv(curLv);
                MySql.update(roleEntity);
            }
            itemEntity.setItemnum(curExp);
            MySql.update(itemEntity);
        } catch (Exception e) {
            e.printStackTrace();
        }
        CommonInfo commonInfo = new CommonInfo();
        commonInfo.setKey(curLv);
        commonInfo.setValue(curExp);
        return commonInfo;

    }
}