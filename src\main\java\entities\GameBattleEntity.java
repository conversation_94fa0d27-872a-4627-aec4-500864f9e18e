package entities;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Table(name = "gamebattle", schema = "", catalog = "super_star_fruit")
public class GameBattleEntity {
    private int id;
    private int type;
    private String player1;
    private String Player2;
    private Timestamp time;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "player1")
    public String getPlayer1() {
        return player1;
    }

    public void setPlayer1(String player1) {
        this.player1 = player1;
    }

    @Basic
    @Column(name = "player2")
    public String getPlayer2() {
        return Player2;
    }

    public void setPlayer2(String player2) {
        Player2 = player2;
    }

    @Basic
    @Column(name = "time")
    public Timestamp getTime() {
        return time;
    }

    public void setTime(Timestamp time) {
        this.time = time;
    }

    @Override
    public String toString() {
        return "GameBattleEntity{" +
                "id=" + id +
                ", type=" + type +
                ", player1='" + player1 + '\'' +
                ", Player2='" + Player2 + '\'' +
                ", time=" + time +
                '}';
    }
}
