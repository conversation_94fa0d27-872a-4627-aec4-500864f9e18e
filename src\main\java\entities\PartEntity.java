package entities;

import protocol.PetData;

import javax.persistence.*;

/**
 * Created by nara on 2018/3/29.
 */
@Entity
@Table(name = "part", schema = "", catalog = "super_star_fruit")
public class PartEntity {
    private int id;
    private int version;
    private String uid;
    private int roleid;
    private int cupboard;
    private Integer part1;
    private Integer part2;
    private Integer part3;
    private Integer part4;
    private Integer part5;
    private Integer part6;
    private Integer part7;
    private Integer part8;
    private Integer part9;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Version
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "roleid")
    public int getRoleid() {
        return roleid;
    }

    public void setRoleid(int roleid) {
        this.roleid = roleid;
    }

    @Basic
    @Column(name = "cupboard")
    public int getCupboard() {
        return cupboard;
    }

    public void setCupboard(int cupboard) {
        this.cupboard = cupboard;
    }

    @Basic
    @Column(name = "part1")
    public Integer getPart1() {
        return part1;
    }

    public void setPart1(Integer part1) {
        this.part1 = part1;
    }

    @Basic
    @Column(name = "part2")
    public Integer getPart2() {
        return part2;
    }

    public void setPart2(Integer part2) {
        this.part2 = part2;
    }

    @Basic
    @Column(name = "part3")
    public Integer getPart3() {
        return part3;
    }

    public void setPart3(Integer part3) {
        this.part3 = part3;
    }

    @Basic
    @Column(name = "part4")
    public Integer getPart4() {
        return part4;
    }

    public void setPart4(Integer part4) {
        this.part4 = part4;
    }

    @Basic
    @Column(name = "part5")
    public Integer getPart5() {
        return part5;
    }

    public void setPart5(Integer part5) {
        this.part5 = part5;
    }

    @Basic
    @Column(name = "part6")
    public Integer getPart6() {
        return part6;
    }

    public void setPart6(Integer part6) {
        this.part6 = part6;
    }

    @Basic
    @Column(name = "part7")
    public Integer getPart7() {
        return part7;
    }

    public void setPart7(Integer part7) {
        this.part7 = part7;
    }

    @Basic
    @Column(name = "part8")
    public Integer getPart8() {
        return part8;
    }

    public void setPart8(Integer part8) {
        this.part8 = part8;
    }

    @Basic
    @Column(name = "part9")
    public Integer getPart9() {
        return part9;
    }

    public void setPart9(Integer part9) {
        this.part9 = part9;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PartEntity that = (PartEntity) o;

        if (id != that.id) return false;
        if (version != that.version) return false;
        if (roleid != that.roleid) return false;
        if (cupboard != that.cupboard) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (part1 != null ? !part1.equals(that.part1) : that.part1 != null) return false;
        if (part2 != null ? !part2.equals(that.part2) : that.part2 != null) return false;
        if (part3 != null ? !part3.equals(that.part3) : that.part3 != null) return false;
        if (part4 != null ? !part4.equals(that.part4) : that.part4 != null) return false;
        if (part5 != null ? !part5.equals(that.part5) : that.part5 != null) return false;
        if (part6 != null ? !part6.equals(that.part6) : that.part6 != null) return false;
        if (part7 != null ? !part7.equals(that.part7) : that.part7 != null) return false;
        if (part8 != null ? !part8.equals(that.part8) : that.part8 != null) return false;
        if (part9 != null ? !part9.equals(that.part9) : that.part9 != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + version;
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + roleid;
        result = 31 * result + cupboard;
        result = 31 * result + (part1 != null ? part1.hashCode() : 0);
        result = 31 * result + (part2 != null ? part2.hashCode() : 0);
        result = 31 * result + (part3 != null ? part3.hashCode() : 0);
        result = 31 * result + (part4 != null ? part4.hashCode() : 0);
        result = 31 * result + (part5 != null ? part5.hashCode() : 0);
        result = 31 * result + (part6 != null ? part6.hashCode() : 0);
        result = 31 * result + (part7 != null ? part7.hashCode() : 0);
        result = 31 * result + (part8 != null ? part8.hashCode() : 0);
        result = 31 * result + (part9 != null ? part9.hashCode() : 0);
        return result;
    }

    @Override
    public String toString() {
        return "PartEntity{" +
                "id=" + id +
                ", version=" + version +
                ", uid='" + uid + '\'' +
                ", roleid=" + roleid +
                ", cupboard=" + cupboard +
                ", part1=" + part1 +
                ", part2=" + part2 +
                ", part3=" + part3 +
                ", part4=" + part4 +
                ", part5=" + part5 +
                ", part6=" + part6 +
                ", part7=" + part7 +
                ", part8=" + part8 +
                ", part9=" + part9 +
                '}';
    }

}
