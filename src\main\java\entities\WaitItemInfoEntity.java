package entities;

import javax.persistence.*;

@Entity
@Table(name = "waitItemInfo", schema = "", catalog = "super_star_fruit")
public class WaitItemInfoEntity {
    private int id;
    private String uid;
    private int waitItemId;
    private int reward;//以|分割，与表格waitItem.xlsx的get_item对应，表示相应的数量;
    private long latelyFinishTime;
    private int rewardId;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "waitItemId")
    public int getWaitItemId() {
        return waitItemId;
    }

    public void setWaitItemId(int waitItemId) {
        this.waitItemId = waitItemId;
    }

    @Basic
    @Column(name = "reward")
    public int getReward() {
        return reward;
    }

    public void setReward(int reward) {
        this.reward = reward;
    }

    @Basic
    @Column(name = "latelyFinishTime")
    public long getLatelyFinishTime() {
        return latelyFinishTime;
    }

    public void setLatelyFinishTime(long latelyFinishTime) {
        this.latelyFinishTime = latelyFinishTime;
    }

    @Basic
    @Column(name = "rewardId")
    public int getRewardId() {
        return rewardId;
    }

    public void setRewardId(int rewardId) {
        this.rewardId = rewardId;
    }

    @Override
    public String toString() {
        return "WaitItemInfoEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", waitItemId=" + waitItemId +
                ", reward=" + reward +
                ", latelyFinishTime=" + latelyFinishTime +
                ", rewardId=" + rewardId +
                '}';
    }
}
