package entities;

import javax.persistence.*;


@Entity
@Table(name = "recharge_rebate_reward", schema = "", catalog = "super_star_fruit")
public class RechargeRebateRewardEntity {
    private int id;
    private String uid;
    private int reward_id;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "reward_id")
    public int getReward_id() {
        return reward_id;
    }

    public void setReward_id(int reward_id) {
        this.reward_id = reward_id;
    }
}
