// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: explore.proto

package protocol;

public final class ExploreData {
  private ExploreData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  /**
   * Protobuf enum {@code protocol.RuleType}
   */
  public enum RuleType
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>ATTRIBUTE = 1;</code>
     */
    ATTRIBUTE(0, 1),
    /**
     * <code>OCCUPATION = 2;</code>
     */
    OCCUPATION(1, 2),
    /**
     * <code>LEVEL = 3;</code>
     */
    LEVEL(2, 3),
    /**
     * <code>STAR = 4;</code>
     *
     * <pre>
     *属性
     *职业
     *等级
     *星级
     * </pre>
     */
    STAR(3, 4),
    ;

    /**
     * <code>ATTRIBUTE = 1;</code>
     */
    public static final int ATTRIBUTE_VALUE = 1;
    /**
     * <code>OCCUPATION = 2;</code>
     */
    public static final int OCCUPATION_VALUE = 2;
    /**
     * <code>LEVEL = 3;</code>
     */
    public static final int LEVEL_VALUE = 3;
    /**
     * <code>STAR = 4;</code>
     *
     * <pre>
     *属性
     *职业
     *等级
     *星级
     * </pre>
     */
    public static final int STAR_VALUE = 4;


    public final int getNumber() { return value; }

    public static RuleType valueOf(int value) {
      switch (value) {
        case 1: return ATTRIBUTE;
        case 2: return OCCUPATION;
        case 3: return LEVEL;
        case 4: return STAR;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<RuleType>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<RuleType>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<RuleType>() {
            public RuleType findValueByNumber(int number) {
              return RuleType.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ExploreData.getDescriptor().getEnumTypes().get(0);
    }

    private static final RuleType[] VALUES = values();

    public static RuleType valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private RuleType(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.RuleType)
  }

  public interface ExperienceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 status = 1;
    /**
     * <code>required int32 status = 1;</code>
     *
     * <pre>
     *1初始状态 2进行状态 3完成状态
     * </pre>
     */
    boolean hasStatus();
    /**
     * <code>required int32 status = 1;</code>
     *
     * <pre>
     *1初始状态 2进行状态 3完成状态
     * </pre>
     */
    int getStatus();

    // required int32 key = 2;
    /**
     * <code>required int32 key = 2;</code>
     *
     * <pre>
     *对应表格里的id
     * </pre>
     */
    boolean hasKey();
    /**
     * <code>required int32 key = 2;</code>
     *
     * <pre>
     *对应表格里的id
     * </pre>
     */
    int getKey();

    // required int32 countdown = 3;
    /**
     * <code>required int32 countdown = 3;</code>
     *
     * <pre>
     *完成倒计时
     * </pre>
     */
    boolean hasCountdown();
    /**
     * <code>required int32 countdown = 3;</code>
     *
     * <pre>
     *完成倒计时
     * </pre>
     */
    int getCountdown();

    // repeated int32 petId = 4;
    /**
     * <code>repeated int32 petId = 4;</code>
     *
     * <pre>
     *进行时宠物id
     * </pre>
     */
    java.util.List<java.lang.Integer> getPetIdList();
    /**
     * <code>repeated int32 petId = 4;</code>
     *
     * <pre>
     *进行时宠物id
     * </pre>
     */
    int getPetIdCount();
    /**
     * <code>repeated int32 petId = 4;</code>
     *
     * <pre>
     *进行时宠物id
     * </pre>
     */
    int getPetId(int index);

    // repeated .protocol.Rule rules = 5;
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    java.util.List<protocol.ExploreData.Rule> 
        getRulesList();
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    protocol.ExploreData.Rule getRules(int index);
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    int getRulesCount();
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    java.util.List<? extends protocol.ExploreData.RuleOrBuilder> 
        getRulesOrBuilderList();
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    protocol.ExploreData.RuleOrBuilder getRulesOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.Experience}
   */
  public static final class Experience extends
      com.google.protobuf.GeneratedMessage
      implements ExperienceOrBuilder {
    // Use Experience.newBuilder() to construct.
    private Experience(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Experience(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Experience defaultInstance;
    public static Experience getDefaultInstance() {
      return defaultInstance;
    }

    public Experience getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Experience(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              status_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              key_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              countdown_ = input.readInt32();
              break;
            }
            case 32: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                petId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000008;
              }
              petId_.add(input.readInt32());
              break;
            }
            case 34: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008) && input.getBytesUntilLimit() > 0) {
                petId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000008;
              }
              while (input.getBytesUntilLimit() > 0) {
                petId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 42: {
              if (!((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
                rules_ = new java.util.ArrayList<protocol.ExploreData.Rule>();
                mutable_bitField0_ |= 0x00000010;
              }
              rules_.add(input.readMessage(protocol.ExploreData.Rule.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          petId_ = java.util.Collections.unmodifiableList(petId_);
        }
        if (((mutable_bitField0_ & 0x00000010) == 0x00000010)) {
          rules_ = java.util.Collections.unmodifiableList(rules_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_Experience_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_Experience_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.Experience.class, protocol.ExploreData.Experience.Builder.class);
    }

    public static com.google.protobuf.Parser<Experience> PARSER =
        new com.google.protobuf.AbstractParser<Experience>() {
      public Experience parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Experience(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Experience> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 status = 1;
    public static final int STATUS_FIELD_NUMBER = 1;
    private int status_;
    /**
     * <code>required int32 status = 1;</code>
     *
     * <pre>
     *1初始状态 2进行状态 3完成状态
     * </pre>
     */
    public boolean hasStatus() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 status = 1;</code>
     *
     * <pre>
     *1初始状态 2进行状态 3完成状态
     * </pre>
     */
    public int getStatus() {
      return status_;
    }

    // required int32 key = 2;
    public static final int KEY_FIELD_NUMBER = 2;
    private int key_;
    /**
     * <code>required int32 key = 2;</code>
     *
     * <pre>
     *对应表格里的id
     * </pre>
     */
    public boolean hasKey() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 key = 2;</code>
     *
     * <pre>
     *对应表格里的id
     * </pre>
     */
    public int getKey() {
      return key_;
    }

    // required int32 countdown = 3;
    public static final int COUNTDOWN_FIELD_NUMBER = 3;
    private int countdown_;
    /**
     * <code>required int32 countdown = 3;</code>
     *
     * <pre>
     *完成倒计时
     * </pre>
     */
    public boolean hasCountdown() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 countdown = 3;</code>
     *
     * <pre>
     *完成倒计时
     * </pre>
     */
    public int getCountdown() {
      return countdown_;
    }

    // repeated int32 petId = 4;
    public static final int PETID_FIELD_NUMBER = 4;
    private java.util.List<java.lang.Integer> petId_;
    /**
     * <code>repeated int32 petId = 4;</code>
     *
     * <pre>
     *进行时宠物id
     * </pre>
     */
    public java.util.List<java.lang.Integer>
        getPetIdList() {
      return petId_;
    }
    /**
     * <code>repeated int32 petId = 4;</code>
     *
     * <pre>
     *进行时宠物id
     * </pre>
     */
    public int getPetIdCount() {
      return petId_.size();
    }
    /**
     * <code>repeated int32 petId = 4;</code>
     *
     * <pre>
     *进行时宠物id
     * </pre>
     */
    public int getPetId(int index) {
      return petId_.get(index);
    }

    // repeated .protocol.Rule rules = 5;
    public static final int RULES_FIELD_NUMBER = 5;
    private java.util.List<protocol.ExploreData.Rule> rules_;
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    public java.util.List<protocol.ExploreData.Rule> getRulesList() {
      return rules_;
    }
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    public java.util.List<? extends protocol.ExploreData.RuleOrBuilder> 
        getRulesOrBuilderList() {
      return rules_;
    }
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    public int getRulesCount() {
      return rules_.size();
    }
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    public protocol.ExploreData.Rule getRules(int index) {
      return rules_.get(index);
    }
    /**
     * <code>repeated .protocol.Rule rules = 5;</code>
     *
     * <pre>
     *具体的宠物规则
     * </pre>
     */
    public protocol.ExploreData.RuleOrBuilder getRulesOrBuilder(
        int index) {
      return rules_.get(index);
    }

    private void initFields() {
      status_ = 0;
      key_ = 0;
      countdown_ = 0;
      petId_ = java.util.Collections.emptyList();
      rules_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasStatus()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCountdown()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getRulesCount(); i++) {
        if (!getRules(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, status_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, key_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, countdown_);
      }
      for (int i = 0; i < petId_.size(); i++) {
        output.writeInt32(4, petId_.get(i));
      }
      for (int i = 0; i < rules_.size(); i++) {
        output.writeMessage(5, rules_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, status_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, key_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, countdown_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < petId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(petId_.get(i));
        }
        size += dataSize;
        size += 1 * getPetIdList().size();
      }
      for (int i = 0; i < rules_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, rules_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.Experience parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.Experience parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.Experience parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.Experience parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.Experience parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.Experience parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.Experience parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.Experience parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.Experience parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.Experience parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.Experience prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Experience}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.ExperienceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_Experience_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_Experience_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.Experience.class, protocol.ExploreData.Experience.Builder.class);
      }

      // Construct using protocol.ExploreData.Experience.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getRulesFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        status_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        key_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        countdown_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        petId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        if (rulesBuilder_ == null) {
          rules_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
        } else {
          rulesBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_Experience_descriptor;
      }

      public protocol.ExploreData.Experience getDefaultInstanceForType() {
        return protocol.ExploreData.Experience.getDefaultInstance();
      }

      public protocol.ExploreData.Experience build() {
        protocol.ExploreData.Experience result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.Experience buildPartial() {
        protocol.ExploreData.Experience result = new protocol.ExploreData.Experience(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.status_ = status_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.key_ = key_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.countdown_ = countdown_;
        if (((bitField0_ & 0x00000008) == 0x00000008)) {
          petId_ = java.util.Collections.unmodifiableList(petId_);
          bitField0_ = (bitField0_ & ~0x00000008);
        }
        result.petId_ = petId_;
        if (rulesBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010)) {
            rules_ = java.util.Collections.unmodifiableList(rules_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.rules_ = rules_;
        } else {
          result.rules_ = rulesBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.Experience) {
          return mergeFrom((protocol.ExploreData.Experience)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.Experience other) {
        if (other == protocol.ExploreData.Experience.getDefaultInstance()) return this;
        if (other.hasStatus()) {
          setStatus(other.getStatus());
        }
        if (other.hasKey()) {
          setKey(other.getKey());
        }
        if (other.hasCountdown()) {
          setCountdown(other.getCountdown());
        }
        if (!other.petId_.isEmpty()) {
          if (petId_.isEmpty()) {
            petId_ = other.petId_;
            bitField0_ = (bitField0_ & ~0x00000008);
          } else {
            ensurePetIdIsMutable();
            petId_.addAll(other.petId_);
          }
          onChanged();
        }
        if (rulesBuilder_ == null) {
          if (!other.rules_.isEmpty()) {
            if (rules_.isEmpty()) {
              rules_ = other.rules_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureRulesIsMutable();
              rules_.addAll(other.rules_);
            }
            onChanged();
          }
        } else {
          if (!other.rules_.isEmpty()) {
            if (rulesBuilder_.isEmpty()) {
              rulesBuilder_.dispose();
              rulesBuilder_ = null;
              rules_ = other.rules_;
              bitField0_ = (bitField0_ & ~0x00000010);
              rulesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRulesFieldBuilder() : null;
            } else {
              rulesBuilder_.addAllMessages(other.rules_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasStatus()) {
          
          return false;
        }
        if (!hasKey()) {
          
          return false;
        }
        if (!hasCountdown()) {
          
          return false;
        }
        for (int i = 0; i < getRulesCount(); i++) {
          if (!getRules(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.Experience parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.Experience) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 status = 1;
      private int status_ ;
      /**
       * <code>required int32 status = 1;</code>
       *
       * <pre>
       *1初始状态 2进行状态 3完成状态
       * </pre>
       */
      public boolean hasStatus() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 status = 1;</code>
       *
       * <pre>
       *1初始状态 2进行状态 3完成状态
       * </pre>
       */
      public int getStatus() {
        return status_;
      }
      /**
       * <code>required int32 status = 1;</code>
       *
       * <pre>
       *1初始状态 2进行状态 3完成状态
       * </pre>
       */
      public Builder setStatus(int value) {
        bitField0_ |= 0x00000001;
        status_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 status = 1;</code>
       *
       * <pre>
       *1初始状态 2进行状态 3完成状态
       * </pre>
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000001);
        status_ = 0;
        onChanged();
        return this;
      }

      // required int32 key = 2;
      private int key_ ;
      /**
       * <code>required int32 key = 2;</code>
       *
       * <pre>
       *对应表格里的id
       * </pre>
       */
      public boolean hasKey() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 key = 2;</code>
       *
       * <pre>
       *对应表格里的id
       * </pre>
       */
      public int getKey() {
        return key_;
      }
      /**
       * <code>required int32 key = 2;</code>
       *
       * <pre>
       *对应表格里的id
       * </pre>
       */
      public Builder setKey(int value) {
        bitField0_ |= 0x00000002;
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 key = 2;</code>
       *
       * <pre>
       *对应表格里的id
       * </pre>
       */
      public Builder clearKey() {
        bitField0_ = (bitField0_ & ~0x00000002);
        key_ = 0;
        onChanged();
        return this;
      }

      // required int32 countdown = 3;
      private int countdown_ ;
      /**
       * <code>required int32 countdown = 3;</code>
       *
       * <pre>
       *完成倒计时
       * </pre>
       */
      public boolean hasCountdown() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 countdown = 3;</code>
       *
       * <pre>
       *完成倒计时
       * </pre>
       */
      public int getCountdown() {
        return countdown_;
      }
      /**
       * <code>required int32 countdown = 3;</code>
       *
       * <pre>
       *完成倒计时
       * </pre>
       */
      public Builder setCountdown(int value) {
        bitField0_ |= 0x00000004;
        countdown_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 countdown = 3;</code>
       *
       * <pre>
       *完成倒计时
       * </pre>
       */
      public Builder clearCountdown() {
        bitField0_ = (bitField0_ & ~0x00000004);
        countdown_ = 0;
        onChanged();
        return this;
      }

      // repeated int32 petId = 4;
      private java.util.List<java.lang.Integer> petId_ = java.util.Collections.emptyList();
      private void ensurePetIdIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          petId_ = new java.util.ArrayList<java.lang.Integer>(petId_);
          bitField0_ |= 0x00000008;
         }
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public java.util.List<java.lang.Integer>
          getPetIdList() {
        return java.util.Collections.unmodifiableList(petId_);
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public int getPetIdCount() {
        return petId_.size();
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public int getPetId(int index) {
        return petId_.get(index);
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public Builder setPetId(
          int index, int value) {
        ensurePetIdIsMutable();
        petId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public Builder addPetId(int value) {
        ensurePetIdIsMutable();
        petId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public Builder addAllPetId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensurePetIdIsMutable();
        super.addAll(values, petId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 4;</code>
       *
       * <pre>
       *进行时宠物id
       * </pre>
       */
      public Builder clearPetId() {
        petId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }

      // repeated .protocol.Rule rules = 5;
      private java.util.List<protocol.ExploreData.Rule> rules_ =
        java.util.Collections.emptyList();
      private void ensureRulesIsMutable() {
        if (!((bitField0_ & 0x00000010) == 0x00000010)) {
          rules_ = new java.util.ArrayList<protocol.ExploreData.Rule>(rules_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ExploreData.Rule, protocol.ExploreData.Rule.Builder, protocol.ExploreData.RuleOrBuilder> rulesBuilder_;

      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public java.util.List<protocol.ExploreData.Rule> getRulesList() {
        if (rulesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rules_);
        } else {
          return rulesBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public int getRulesCount() {
        if (rulesBuilder_ == null) {
          return rules_.size();
        } else {
          return rulesBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public protocol.ExploreData.Rule getRules(int index) {
        if (rulesBuilder_ == null) {
          return rules_.get(index);
        } else {
          return rulesBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder setRules(
          int index, protocol.ExploreData.Rule value) {
        if (rulesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRulesIsMutable();
          rules_.set(index, value);
          onChanged();
        } else {
          rulesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder setRules(
          int index, protocol.ExploreData.Rule.Builder builderForValue) {
        if (rulesBuilder_ == null) {
          ensureRulesIsMutable();
          rules_.set(index, builderForValue.build());
          onChanged();
        } else {
          rulesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder addRules(protocol.ExploreData.Rule value) {
        if (rulesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRulesIsMutable();
          rules_.add(value);
          onChanged();
        } else {
          rulesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder addRules(
          int index, protocol.ExploreData.Rule value) {
        if (rulesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRulesIsMutable();
          rules_.add(index, value);
          onChanged();
        } else {
          rulesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder addRules(
          protocol.ExploreData.Rule.Builder builderForValue) {
        if (rulesBuilder_ == null) {
          ensureRulesIsMutable();
          rules_.add(builderForValue.build());
          onChanged();
        } else {
          rulesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder addRules(
          int index, protocol.ExploreData.Rule.Builder builderForValue) {
        if (rulesBuilder_ == null) {
          ensureRulesIsMutable();
          rules_.add(index, builderForValue.build());
          onChanged();
        } else {
          rulesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder addAllRules(
          java.lang.Iterable<? extends protocol.ExploreData.Rule> values) {
        if (rulesBuilder_ == null) {
          ensureRulesIsMutable();
          super.addAll(values, rules_);
          onChanged();
        } else {
          rulesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder clearRules() {
        if (rulesBuilder_ == null) {
          rules_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          rulesBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public Builder removeRules(int index) {
        if (rulesBuilder_ == null) {
          ensureRulesIsMutable();
          rules_.remove(index);
          onChanged();
        } else {
          rulesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public protocol.ExploreData.Rule.Builder getRulesBuilder(
          int index) {
        return getRulesFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public protocol.ExploreData.RuleOrBuilder getRulesOrBuilder(
          int index) {
        if (rulesBuilder_ == null) {
          return rules_.get(index);  } else {
          return rulesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public java.util.List<? extends protocol.ExploreData.RuleOrBuilder> 
           getRulesOrBuilderList() {
        if (rulesBuilder_ != null) {
          return rulesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rules_);
        }
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public protocol.ExploreData.Rule.Builder addRulesBuilder() {
        return getRulesFieldBuilder().addBuilder(
            protocol.ExploreData.Rule.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public protocol.ExploreData.Rule.Builder addRulesBuilder(
          int index) {
        return getRulesFieldBuilder().addBuilder(
            index, protocol.ExploreData.Rule.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Rule rules = 5;</code>
       *
       * <pre>
       *具体的宠物规则
       * </pre>
       */
      public java.util.List<protocol.ExploreData.Rule.Builder> 
           getRulesBuilderList() {
        return getRulesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ExploreData.Rule, protocol.ExploreData.Rule.Builder, protocol.ExploreData.RuleOrBuilder> 
          getRulesFieldBuilder() {
        if (rulesBuilder_ == null) {
          rulesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ExploreData.Rule, protocol.ExploreData.Rule.Builder, protocol.ExploreData.RuleOrBuilder>(
                  rules_,
                  ((bitField0_ & 0x00000010) == 0x00000010),
                  getParentForChildren(),
                  isClean());
          rules_ = null;
        }
        return rulesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Experience)
    }

    static {
      defaultInstance = new Experience(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Experience)
  }

  public interface RuleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *对应ruleType
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *对应ruleType
     * </pre>
     */
    int getType();

    // required string content = 2;
    /**
     * <code>required string content = 2;</code>
     *
     * <pre>
     *具体细节
     * </pre>
     */
    boolean hasContent();
    /**
     * <code>required string content = 2;</code>
     *
     * <pre>
     *具体细节
     * </pre>
     */
    java.lang.String getContent();
    /**
     * <code>required string content = 2;</code>
     *
     * <pre>
     *具体细节
     * </pre>
     */
    com.google.protobuf.ByteString
        getContentBytes();
  }
  /**
   * Protobuf type {@code protocol.Rule}
   */
  public static final class Rule extends
      com.google.protobuf.GeneratedMessage
      implements RuleOrBuilder {
    // Use Rule.newBuilder() to construct.
    private Rule(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Rule(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Rule defaultInstance;
    public static Rule getDefaultInstance() {
      return defaultInstance;
    }

    public Rule getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Rule(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              content_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_Rule_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_Rule_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.Rule.class, protocol.ExploreData.Rule.Builder.class);
    }

    public static com.google.protobuf.Parser<Rule> PARSER =
        new com.google.protobuf.AbstractParser<Rule>() {
      public Rule parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Rule(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Rule> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *对应ruleType
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *对应ruleType
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // required string content = 2;
    public static final int CONTENT_FIELD_NUMBER = 2;
    private java.lang.Object content_;
    /**
     * <code>required string content = 2;</code>
     *
     * <pre>
     *具体细节
     * </pre>
     */
    public boolean hasContent() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string content = 2;</code>
     *
     * <pre>
     *具体细节
     * </pre>
     */
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          content_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string content = 2;</code>
     *
     * <pre>
     *具体细节
     * </pre>
     */
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      type_ = 0;
      content_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasContent()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getContentBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getContentBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.Rule parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.Rule parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.Rule parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.Rule parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.Rule parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.Rule parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.Rule parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.Rule parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.Rule parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.Rule parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.Rule prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Rule}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.RuleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_Rule_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_Rule_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.Rule.class, protocol.ExploreData.Rule.Builder.class);
      }

      // Construct using protocol.ExploreData.Rule.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        content_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_Rule_descriptor;
      }

      public protocol.ExploreData.Rule getDefaultInstanceForType() {
        return protocol.ExploreData.Rule.getDefaultInstance();
      }

      public protocol.ExploreData.Rule build() {
        protocol.ExploreData.Rule result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.Rule buildPartial() {
        protocol.ExploreData.Rule result = new protocol.ExploreData.Rule(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.content_ = content_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.Rule) {
          return mergeFrom((protocol.ExploreData.Rule)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.Rule other) {
        if (other == protocol.ExploreData.Rule.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasContent()) {
          bitField0_ |= 0x00000002;
          content_ = other.content_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (!hasContent()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.Rule parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.Rule) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *对应ruleType
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *对应ruleType
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *对应ruleType
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *对应ruleType
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // required string content = 2;
      private java.lang.Object content_ = "";
      /**
       * <code>required string content = 2;</code>
       *
       * <pre>
       *具体细节
       * </pre>
       */
      public boolean hasContent() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string content = 2;</code>
       *
       * <pre>
       *具体细节
       * </pre>
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string content = 2;</code>
       *
       * <pre>
       *具体细节
       * </pre>
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string content = 2;</code>
       *
       * <pre>
       *具体细节
       * </pre>
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        content_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string content = 2;</code>
       *
       * <pre>
       *具体细节
       * </pre>
       */
      public Builder clearContent() {
        bitField0_ = (bitField0_ & ~0x00000002);
        content_ = getDefaultInstance().getContent();
        onChanged();
        return this;
      }
      /**
       * <code>required string content = 2;</code>
       *
       * <pre>
       *具体细节
       * </pre>
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        content_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Rule)
    }

    static {
      defaultInstance = new Rule(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Rule)
  }

  public interface RequestGetExperiencesOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetExperiences}
   *
   * <pre>
   *1280
   * </pre>
   */
  public static final class RequestGetExperiences extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetExperiencesOrBuilder {
    // Use RequestGetExperiences.newBuilder() to construct.
    private RequestGetExperiences(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetExperiences(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetExperiences defaultInstance;
    public static RequestGetExperiences getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetExperiences getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetExperiences(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_RequestGetExperiences_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_RequestGetExperiences_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.RequestGetExperiences.class, protocol.ExploreData.RequestGetExperiences.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetExperiences> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetExperiences>() {
      public RequestGetExperiences parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetExperiences(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetExperiences> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.RequestGetExperiences parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.RequestGetExperiences parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.RequestGetExperiences parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.RequestGetExperiences parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.RequestGetExperiences prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetExperiences}
     *
     * <pre>
     *1280
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.RequestGetExperiencesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_RequestGetExperiences_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_RequestGetExperiences_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.RequestGetExperiences.class, protocol.ExploreData.RequestGetExperiences.Builder.class);
      }

      // Construct using protocol.ExploreData.RequestGetExperiences.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_RequestGetExperiences_descriptor;
      }

      public protocol.ExploreData.RequestGetExperiences getDefaultInstanceForType() {
        return protocol.ExploreData.RequestGetExperiences.getDefaultInstance();
      }

      public protocol.ExploreData.RequestGetExperiences build() {
        protocol.ExploreData.RequestGetExperiences result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.RequestGetExperiences buildPartial() {
        protocol.ExploreData.RequestGetExperiences result = new protocol.ExploreData.RequestGetExperiences(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.RequestGetExperiences) {
          return mergeFrom((protocol.ExploreData.RequestGetExperiences)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.RequestGetExperiences other) {
        if (other == protocol.ExploreData.RequestGetExperiences.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.RequestGetExperiences parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.RequestGetExperiences) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetExperiences)
    }

    static {
      defaultInstance = new RequestGetExperiences(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetExperiences)
  }

  public interface ResponseGetExperiencesOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // required int32 flushNums = 2;
    /**
     * <code>required int32 flushNums = 2;</code>
     *
     * <pre>
     *可以刷新的次数
     * </pre>
     */
    boolean hasFlushNums();
    /**
     * <code>required int32 flushNums = 2;</code>
     *
     * <pre>
     *可以刷新的次数
     * </pre>
     */
    int getFlushNums();

    // repeated .protocol.Experience experience = 3;
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    java.util.List<protocol.ExploreData.Experience> 
        getExperienceList();
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    protocol.ExploreData.Experience getExperience(int index);
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    int getExperienceCount();
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    java.util.List<? extends protocol.ExploreData.ExperienceOrBuilder> 
        getExperienceOrBuilderList();
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseGetExperiences}
   *
   * <pre>
   *2280
   * </pre>
   */
  public static final class ResponseGetExperiences extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetExperiencesOrBuilder {
    // Use ResponseGetExperiences.newBuilder() to construct.
    private ResponseGetExperiences(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetExperiences(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetExperiences defaultInstance;
    public static ResponseGetExperiences getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetExperiences getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetExperiences(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              flushNums_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                experience_ = new java.util.ArrayList<protocol.ExploreData.Experience>();
                mutable_bitField0_ |= 0x00000004;
              }
              experience_.add(input.readMessage(protocol.ExploreData.Experience.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          experience_ = java.util.Collections.unmodifiableList(experience_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_ResponseGetExperiences_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_ResponseGetExperiences_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.ResponseGetExperiences.class, protocol.ExploreData.ResponseGetExperiences.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetExperiences> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetExperiences>() {
      public ResponseGetExperiences parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetExperiences(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetExperiences> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 flushNums = 2;
    public static final int FLUSHNUMS_FIELD_NUMBER = 2;
    private int flushNums_;
    /**
     * <code>required int32 flushNums = 2;</code>
     *
     * <pre>
     *可以刷新的次数
     * </pre>
     */
    public boolean hasFlushNums() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 flushNums = 2;</code>
     *
     * <pre>
     *可以刷新的次数
     * </pre>
     */
    public int getFlushNums() {
      return flushNums_;
    }

    // repeated .protocol.Experience experience = 3;
    public static final int EXPERIENCE_FIELD_NUMBER = 3;
    private java.util.List<protocol.ExploreData.Experience> experience_;
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    public java.util.List<protocol.ExploreData.Experience> getExperienceList() {
      return experience_;
    }
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    public java.util.List<? extends protocol.ExploreData.ExperienceOrBuilder> 
        getExperienceOrBuilderList() {
      return experience_;
    }
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    public int getExperienceCount() {
      return experience_.size();
    }
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    public protocol.ExploreData.Experience getExperience(int index) {
      return experience_.get(index);
    }
    /**
     * <code>repeated .protocol.Experience experience = 3;</code>
     *
     * <pre>
     *探险任务
     * </pre>
     */
    public protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder(
        int index) {
      return experience_.get(index);
    }

    private void initFields() {
      errorId_ = 0;
      flushNums_ = 0;
      experience_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFlushNums()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getExperienceCount(); i++) {
        if (!getExperience(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, flushNums_);
      }
      for (int i = 0; i < experience_.size(); i++) {
        output.writeMessage(3, experience_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, flushNums_);
      }
      for (int i = 0; i < experience_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, experience_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.ResponseGetExperiences parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.ResponseGetExperiences parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.ResponseGetExperiences prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetExperiences}
     *
     * <pre>
     *2280
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.ResponseGetExperiencesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_ResponseGetExperiences_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_ResponseGetExperiences_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.ResponseGetExperiences.class, protocol.ExploreData.ResponseGetExperiences.Builder.class);
      }

      // Construct using protocol.ExploreData.ResponseGetExperiences.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getExperienceFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        flushNums_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (experienceBuilder_ == null) {
          experience_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          experienceBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_ResponseGetExperiences_descriptor;
      }

      public protocol.ExploreData.ResponseGetExperiences getDefaultInstanceForType() {
        return protocol.ExploreData.ResponseGetExperiences.getDefaultInstance();
      }

      public protocol.ExploreData.ResponseGetExperiences build() {
        protocol.ExploreData.ResponseGetExperiences result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.ResponseGetExperiences buildPartial() {
        protocol.ExploreData.ResponseGetExperiences result = new protocol.ExploreData.ResponseGetExperiences(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.flushNums_ = flushNums_;
        if (experienceBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            experience_ = java.util.Collections.unmodifiableList(experience_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.experience_ = experience_;
        } else {
          result.experience_ = experienceBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.ResponseGetExperiences) {
          return mergeFrom((protocol.ExploreData.ResponseGetExperiences)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.ResponseGetExperiences other) {
        if (other == protocol.ExploreData.ResponseGetExperiences.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasFlushNums()) {
          setFlushNums(other.getFlushNums());
        }
        if (experienceBuilder_ == null) {
          if (!other.experience_.isEmpty()) {
            if (experience_.isEmpty()) {
              experience_ = other.experience_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureExperienceIsMutable();
              experience_.addAll(other.experience_);
            }
            onChanged();
          }
        } else {
          if (!other.experience_.isEmpty()) {
            if (experienceBuilder_.isEmpty()) {
              experienceBuilder_.dispose();
              experienceBuilder_ = null;
              experience_ = other.experience_;
              bitField0_ = (bitField0_ & ~0x00000004);
              experienceBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getExperienceFieldBuilder() : null;
            } else {
              experienceBuilder_.addAllMessages(other.experience_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasFlushNums()) {
          
          return false;
        }
        for (int i = 0; i < getExperienceCount(); i++) {
          if (!getExperience(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.ResponseGetExperiences parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.ResponseGetExperiences) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 flushNums = 2;
      private int flushNums_ ;
      /**
       * <code>required int32 flushNums = 2;</code>
       *
       * <pre>
       *可以刷新的次数
       * </pre>
       */
      public boolean hasFlushNums() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 flushNums = 2;</code>
       *
       * <pre>
       *可以刷新的次数
       * </pre>
       */
      public int getFlushNums() {
        return flushNums_;
      }
      /**
       * <code>required int32 flushNums = 2;</code>
       *
       * <pre>
       *可以刷新的次数
       * </pre>
       */
      public Builder setFlushNums(int value) {
        bitField0_ |= 0x00000002;
        flushNums_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 flushNums = 2;</code>
       *
       * <pre>
       *可以刷新的次数
       * </pre>
       */
      public Builder clearFlushNums() {
        bitField0_ = (bitField0_ & ~0x00000002);
        flushNums_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Experience experience = 3;
      private java.util.List<protocol.ExploreData.Experience> experience_ =
        java.util.Collections.emptyList();
      private void ensureExperienceIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          experience_ = new java.util.ArrayList<protocol.ExploreData.Experience>(experience_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder> experienceBuilder_;

      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public java.util.List<protocol.ExploreData.Experience> getExperienceList() {
        if (experienceBuilder_ == null) {
          return java.util.Collections.unmodifiableList(experience_);
        } else {
          return experienceBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public int getExperienceCount() {
        if (experienceBuilder_ == null) {
          return experience_.size();
        } else {
          return experienceBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public protocol.ExploreData.Experience getExperience(int index) {
        if (experienceBuilder_ == null) {
          return experience_.get(index);
        } else {
          return experienceBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder setExperience(
          int index, protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExperienceIsMutable();
          experience_.set(index, value);
          onChanged();
        } else {
          experienceBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder setExperience(
          int index, protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.set(index, builderForValue.build());
          onChanged();
        } else {
          experienceBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder addExperience(protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExperienceIsMutable();
          experience_.add(value);
          onChanged();
        } else {
          experienceBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder addExperience(
          int index, protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExperienceIsMutable();
          experience_.add(index, value);
          onChanged();
        } else {
          experienceBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder addExperience(
          protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.add(builderForValue.build());
          onChanged();
        } else {
          experienceBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder addExperience(
          int index, protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.add(index, builderForValue.build());
          onChanged();
        } else {
          experienceBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder addAllExperience(
          java.lang.Iterable<? extends protocol.ExploreData.Experience> values) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          super.addAll(values, experience_);
          onChanged();
        } else {
          experienceBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder clearExperience() {
        if (experienceBuilder_ == null) {
          experience_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          experienceBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public Builder removeExperience(int index) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.remove(index);
          onChanged();
        } else {
          experienceBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public protocol.ExploreData.Experience.Builder getExperienceBuilder(
          int index) {
        return getExperienceFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder(
          int index) {
        if (experienceBuilder_ == null) {
          return experience_.get(index);  } else {
          return experienceBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public java.util.List<? extends protocol.ExploreData.ExperienceOrBuilder> 
           getExperienceOrBuilderList() {
        if (experienceBuilder_ != null) {
          return experienceBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(experience_);
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public protocol.ExploreData.Experience.Builder addExperienceBuilder() {
        return getExperienceFieldBuilder().addBuilder(
            protocol.ExploreData.Experience.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public protocol.ExploreData.Experience.Builder addExperienceBuilder(
          int index) {
        return getExperienceFieldBuilder().addBuilder(
            index, protocol.ExploreData.Experience.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Experience experience = 3;</code>
       *
       * <pre>
       *探险任务
       * </pre>
       */
      public java.util.List<protocol.ExploreData.Experience.Builder> 
           getExperienceBuilderList() {
        return getExperienceFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder> 
          getExperienceFieldBuilder() {
        if (experienceBuilder_ == null) {
          experienceBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder>(
                  experience_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          experience_ = null;
        }
        return experienceBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetExperiences)
    }

    static {
      defaultInstance = new ResponseGetExperiences(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetExperiences)
  }

  public interface RequestFlushExperienceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestFlushExperience}
   *
   * <pre>
   *1281
   * </pre>
   */
  public static final class RequestFlushExperience extends
      com.google.protobuf.GeneratedMessage
      implements RequestFlushExperienceOrBuilder {
    // Use RequestFlushExperience.newBuilder() to construct.
    private RequestFlushExperience(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestFlushExperience(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestFlushExperience defaultInstance;
    public static RequestFlushExperience getDefaultInstance() {
      return defaultInstance;
    }

    public RequestFlushExperience getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestFlushExperience(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_RequestFlushExperience_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_RequestFlushExperience_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.RequestFlushExperience.class, protocol.ExploreData.RequestFlushExperience.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestFlushExperience> PARSER =
        new com.google.protobuf.AbstractParser<RequestFlushExperience>() {
      public RequestFlushExperience parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestFlushExperience(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestFlushExperience> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.RequestFlushExperience parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.RequestFlushExperience parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.RequestFlushExperience parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.RequestFlushExperience parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.RequestFlushExperience prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestFlushExperience}
     *
     * <pre>
     *1281
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.RequestFlushExperienceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_RequestFlushExperience_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_RequestFlushExperience_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.RequestFlushExperience.class, protocol.ExploreData.RequestFlushExperience.Builder.class);
      }

      // Construct using protocol.ExploreData.RequestFlushExperience.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_RequestFlushExperience_descriptor;
      }

      public protocol.ExploreData.RequestFlushExperience getDefaultInstanceForType() {
        return protocol.ExploreData.RequestFlushExperience.getDefaultInstance();
      }

      public protocol.ExploreData.RequestFlushExperience build() {
        protocol.ExploreData.RequestFlushExperience result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.RequestFlushExperience buildPartial() {
        protocol.ExploreData.RequestFlushExperience result = new protocol.ExploreData.RequestFlushExperience(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.RequestFlushExperience) {
          return mergeFrom((protocol.ExploreData.RequestFlushExperience)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.RequestFlushExperience other) {
        if (other == protocol.ExploreData.RequestFlushExperience.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.RequestFlushExperience parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.RequestFlushExperience) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestFlushExperience)
    }

    static {
      defaultInstance = new RequestFlushExperience(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestFlushExperience)
  }

  public interface ResponseFlushExperienceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 erroeId = 1;
    /**
     * <code>required int32 erroeId = 1;</code>
     *
     * <pre>
     *1次数不够
     * </pre>
     */
    boolean hasErroeId();
    /**
     * <code>required int32 erroeId = 1;</code>
     *
     * <pre>
     *1次数不够
     * </pre>
     */
    int getErroeId();

    // repeated .protocol.Experience experience = 2;
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    java.util.List<protocol.ExploreData.Experience> 
        getExperienceList();
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    protocol.ExploreData.Experience getExperience(int index);
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    int getExperienceCount();
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    java.util.List<? extends protocol.ExploreData.ExperienceOrBuilder> 
        getExperienceOrBuilderList();
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseFlushExperience}
   *
   * <pre>
   *2281
   * </pre>
   */
  public static final class ResponseFlushExperience extends
      com.google.protobuf.GeneratedMessage
      implements ResponseFlushExperienceOrBuilder {
    // Use ResponseFlushExperience.newBuilder() to construct.
    private ResponseFlushExperience(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseFlushExperience(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseFlushExperience defaultInstance;
    public static ResponseFlushExperience getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseFlushExperience getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseFlushExperience(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              erroeId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                experience_ = new java.util.ArrayList<protocol.ExploreData.Experience>();
                mutable_bitField0_ |= 0x00000002;
              }
              experience_.add(input.readMessage(protocol.ExploreData.Experience.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          experience_ = java.util.Collections.unmodifiableList(experience_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_ResponseFlushExperience_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_ResponseFlushExperience_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.ResponseFlushExperience.class, protocol.ExploreData.ResponseFlushExperience.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseFlushExperience> PARSER =
        new com.google.protobuf.AbstractParser<ResponseFlushExperience>() {
      public ResponseFlushExperience parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseFlushExperience(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseFlushExperience> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 erroeId = 1;
    public static final int ERROEID_FIELD_NUMBER = 1;
    private int erroeId_;
    /**
     * <code>required int32 erroeId = 1;</code>
     *
     * <pre>
     *1次数不够
     * </pre>
     */
    public boolean hasErroeId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 erroeId = 1;</code>
     *
     * <pre>
     *1次数不够
     * </pre>
     */
    public int getErroeId() {
      return erroeId_;
    }

    // repeated .protocol.Experience experience = 2;
    public static final int EXPERIENCE_FIELD_NUMBER = 2;
    private java.util.List<protocol.ExploreData.Experience> experience_;
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    public java.util.List<protocol.ExploreData.Experience> getExperienceList() {
      return experience_;
    }
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    public java.util.List<? extends protocol.ExploreData.ExperienceOrBuilder> 
        getExperienceOrBuilderList() {
      return experience_;
    }
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    public int getExperienceCount() {
      return experience_.size();
    }
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    public protocol.ExploreData.Experience getExperience(int index) {
      return experience_.get(index);
    }
    /**
     * <code>repeated .protocol.Experience experience = 2;</code>
     */
    public protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder(
        int index) {
      return experience_.get(index);
    }

    private void initFields() {
      erroeId_ = 0;
      experience_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErroeId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getExperienceCount(); i++) {
        if (!getExperience(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, erroeId_);
      }
      for (int i = 0; i < experience_.size(); i++) {
        output.writeMessage(2, experience_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, erroeId_);
      }
      for (int i = 0; i < experience_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, experience_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.ResponseFlushExperience parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.ResponseFlushExperience parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.ResponseFlushExperience prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseFlushExperience}
     *
     * <pre>
     *2281
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.ResponseFlushExperienceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_ResponseFlushExperience_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_ResponseFlushExperience_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.ResponseFlushExperience.class, protocol.ExploreData.ResponseFlushExperience.Builder.class);
      }

      // Construct using protocol.ExploreData.ResponseFlushExperience.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getExperienceFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        erroeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (experienceBuilder_ == null) {
          experience_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          experienceBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_ResponseFlushExperience_descriptor;
      }

      public protocol.ExploreData.ResponseFlushExperience getDefaultInstanceForType() {
        return protocol.ExploreData.ResponseFlushExperience.getDefaultInstance();
      }

      public protocol.ExploreData.ResponseFlushExperience build() {
        protocol.ExploreData.ResponseFlushExperience result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.ResponseFlushExperience buildPartial() {
        protocol.ExploreData.ResponseFlushExperience result = new protocol.ExploreData.ResponseFlushExperience(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.erroeId_ = erroeId_;
        if (experienceBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            experience_ = java.util.Collections.unmodifiableList(experience_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.experience_ = experience_;
        } else {
          result.experience_ = experienceBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.ResponseFlushExperience) {
          return mergeFrom((protocol.ExploreData.ResponseFlushExperience)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.ResponseFlushExperience other) {
        if (other == protocol.ExploreData.ResponseFlushExperience.getDefaultInstance()) return this;
        if (other.hasErroeId()) {
          setErroeId(other.getErroeId());
        }
        if (experienceBuilder_ == null) {
          if (!other.experience_.isEmpty()) {
            if (experience_.isEmpty()) {
              experience_ = other.experience_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureExperienceIsMutable();
              experience_.addAll(other.experience_);
            }
            onChanged();
          }
        } else {
          if (!other.experience_.isEmpty()) {
            if (experienceBuilder_.isEmpty()) {
              experienceBuilder_.dispose();
              experienceBuilder_ = null;
              experience_ = other.experience_;
              bitField0_ = (bitField0_ & ~0x00000002);
              experienceBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getExperienceFieldBuilder() : null;
            } else {
              experienceBuilder_.addAllMessages(other.experience_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErroeId()) {
          
          return false;
        }
        for (int i = 0; i < getExperienceCount(); i++) {
          if (!getExperience(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.ResponseFlushExperience parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.ResponseFlushExperience) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 erroeId = 1;
      private int erroeId_ ;
      /**
       * <code>required int32 erroeId = 1;</code>
       *
       * <pre>
       *1次数不够
       * </pre>
       */
      public boolean hasErroeId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 erroeId = 1;</code>
       *
       * <pre>
       *1次数不够
       * </pre>
       */
      public int getErroeId() {
        return erroeId_;
      }
      /**
       * <code>required int32 erroeId = 1;</code>
       *
       * <pre>
       *1次数不够
       * </pre>
       */
      public Builder setErroeId(int value) {
        bitField0_ |= 0x00000001;
        erroeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 erroeId = 1;</code>
       *
       * <pre>
       *1次数不够
       * </pre>
       */
      public Builder clearErroeId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        erroeId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Experience experience = 2;
      private java.util.List<protocol.ExploreData.Experience> experience_ =
        java.util.Collections.emptyList();
      private void ensureExperienceIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          experience_ = new java.util.ArrayList<protocol.ExploreData.Experience>(experience_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder> experienceBuilder_;

      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public java.util.List<protocol.ExploreData.Experience> getExperienceList() {
        if (experienceBuilder_ == null) {
          return java.util.Collections.unmodifiableList(experience_);
        } else {
          return experienceBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public int getExperienceCount() {
        if (experienceBuilder_ == null) {
          return experience_.size();
        } else {
          return experienceBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public protocol.ExploreData.Experience getExperience(int index) {
        if (experienceBuilder_ == null) {
          return experience_.get(index);
        } else {
          return experienceBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder setExperience(
          int index, protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExperienceIsMutable();
          experience_.set(index, value);
          onChanged();
        } else {
          experienceBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder setExperience(
          int index, protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.set(index, builderForValue.build());
          onChanged();
        } else {
          experienceBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder addExperience(protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExperienceIsMutable();
          experience_.add(value);
          onChanged();
        } else {
          experienceBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder addExperience(
          int index, protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExperienceIsMutable();
          experience_.add(index, value);
          onChanged();
        } else {
          experienceBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder addExperience(
          protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.add(builderForValue.build());
          onChanged();
        } else {
          experienceBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder addExperience(
          int index, protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.add(index, builderForValue.build());
          onChanged();
        } else {
          experienceBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder addAllExperience(
          java.lang.Iterable<? extends protocol.ExploreData.Experience> values) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          super.addAll(values, experience_);
          onChanged();
        } else {
          experienceBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder clearExperience() {
        if (experienceBuilder_ == null) {
          experience_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          experienceBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public Builder removeExperience(int index) {
        if (experienceBuilder_ == null) {
          ensureExperienceIsMutable();
          experience_.remove(index);
          onChanged();
        } else {
          experienceBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public protocol.ExploreData.Experience.Builder getExperienceBuilder(
          int index) {
        return getExperienceFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder(
          int index) {
        if (experienceBuilder_ == null) {
          return experience_.get(index);  } else {
          return experienceBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public java.util.List<? extends protocol.ExploreData.ExperienceOrBuilder> 
           getExperienceOrBuilderList() {
        if (experienceBuilder_ != null) {
          return experienceBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(experience_);
        }
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public protocol.ExploreData.Experience.Builder addExperienceBuilder() {
        return getExperienceFieldBuilder().addBuilder(
            protocol.ExploreData.Experience.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public protocol.ExploreData.Experience.Builder addExperienceBuilder(
          int index) {
        return getExperienceFieldBuilder().addBuilder(
            index, protocol.ExploreData.Experience.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Experience experience = 2;</code>
       */
      public java.util.List<protocol.ExploreData.Experience.Builder> 
           getExperienceBuilderList() {
        return getExperienceFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder> 
          getExperienceFieldBuilder() {
        if (experienceBuilder_ == null) {
          experienceBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder>(
                  experience_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          experience_ = null;
        }
        return experienceBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseFlushExperience)
    }

    static {
      defaultInstance = new ResponseFlushExperience(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseFlushExperience)
  }

  public interface RequestOperateExperienceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 type = 1;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    int getType();

    // required int32 experienceKey = 2;
    /**
     * <code>required int32 experienceKey = 2;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    boolean hasExperienceKey();
    /**
     * <code>required int32 experienceKey = 2;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    int getExperienceKey();

    // repeated int32 petId = 3;
    /**
     * <code>repeated int32 petId = 3;</code>
     */
    java.util.List<java.lang.Integer> getPetIdList();
    /**
     * <code>repeated int32 petId = 3;</code>
     */
    int getPetIdCount();
    /**
     * <code>repeated int32 petId = 3;</code>
     */
    int getPetId(int index);
  }
  /**
   * Protobuf type {@code protocol.RequestOperateExperience}
   *
   * <pre>
   *1282
   * </pre>
   */
  public static final class RequestOperateExperience extends
      com.google.protobuf.GeneratedMessage
      implements RequestOperateExperienceOrBuilder {
    // Use RequestOperateExperience.newBuilder() to construct.
    private RequestOperateExperience(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestOperateExperience(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestOperateExperience defaultInstance;
    public static RequestOperateExperience getDefaultInstance() {
      return defaultInstance;
    }

    public RequestOperateExperience getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestOperateExperience(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              type_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              experienceKey_ = input.readInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                petId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              petId_.add(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                petId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                petId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          petId_ = java.util.Collections.unmodifiableList(petId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_RequestOperateExperience_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_RequestOperateExperience_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.RequestOperateExperience.class, protocol.ExploreData.RequestOperateExperience.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestOperateExperience> PARSER =
        new com.google.protobuf.AbstractParser<RequestOperateExperience>() {
      public RequestOperateExperience parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestOperateExperience(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestOperateExperience> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 type = 1;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 type = 1;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // required int32 experienceKey = 2;
    public static final int EXPERIENCEKEY_FIELD_NUMBER = 2;
    private int experienceKey_;
    /**
     * <code>required int32 experienceKey = 2;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    public boolean hasExperienceKey() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 experienceKey = 2;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    public int getExperienceKey() {
      return experienceKey_;
    }

    // repeated int32 petId = 3;
    public static final int PETID_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Integer> petId_;
    /**
     * <code>repeated int32 petId = 3;</code>
     */
    public java.util.List<java.lang.Integer>
        getPetIdList() {
      return petId_;
    }
    /**
     * <code>repeated int32 petId = 3;</code>
     */
    public int getPetIdCount() {
      return petId_.size();
    }
    /**
     * <code>repeated int32 petId = 3;</code>
     */
    public int getPetId(int index) {
      return petId_.get(index);
    }

    private void initFields() {
      type_ = 0;
      experienceKey_ = 0;
      petId_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasExperienceKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, experienceKey_);
      }
      for (int i = 0; i < petId_.size(); i++) {
        output.writeInt32(3, petId_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, experienceKey_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < petId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(petId_.get(i));
        }
        size += dataSize;
        size += 1 * getPetIdList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.RequestOperateExperience parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.RequestOperateExperience parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.RequestOperateExperience parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.RequestOperateExperience parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.RequestOperateExperience prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestOperateExperience}
     *
     * <pre>
     *1282
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.RequestOperateExperienceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_RequestOperateExperience_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_RequestOperateExperience_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.RequestOperateExperience.class, protocol.ExploreData.RequestOperateExperience.Builder.class);
      }

      // Construct using protocol.ExploreData.RequestOperateExperience.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        experienceKey_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        petId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_RequestOperateExperience_descriptor;
      }

      public protocol.ExploreData.RequestOperateExperience getDefaultInstanceForType() {
        return protocol.ExploreData.RequestOperateExperience.getDefaultInstance();
      }

      public protocol.ExploreData.RequestOperateExperience build() {
        protocol.ExploreData.RequestOperateExperience result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.RequestOperateExperience buildPartial() {
        protocol.ExploreData.RequestOperateExperience result = new protocol.ExploreData.RequestOperateExperience(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.experienceKey_ = experienceKey_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          petId_ = java.util.Collections.unmodifiableList(petId_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.petId_ = petId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.RequestOperateExperience) {
          return mergeFrom((protocol.ExploreData.RequestOperateExperience)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.RequestOperateExperience other) {
        if (other == protocol.ExploreData.RequestOperateExperience.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasExperienceKey()) {
          setExperienceKey(other.getExperienceKey());
        }
        if (!other.petId_.isEmpty()) {
          if (petId_.isEmpty()) {
            petId_ = other.petId_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensurePetIdIsMutable();
            petId_.addAll(other.petId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasType()) {
          
          return false;
        }
        if (!hasExperienceKey()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.RequestOperateExperience parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.RequestOperateExperience) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 type = 1;
      private int type_ ;
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000001;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 1;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      // required int32 experienceKey = 2;
      private int experienceKey_ ;
      /**
       * <code>required int32 experienceKey = 2;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public boolean hasExperienceKey() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 experienceKey = 2;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public int getExperienceKey() {
        return experienceKey_;
      }
      /**
       * <code>required int32 experienceKey = 2;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public Builder setExperienceKey(int value) {
        bitField0_ |= 0x00000002;
        experienceKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 experienceKey = 2;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public Builder clearExperienceKey() {
        bitField0_ = (bitField0_ & ~0x00000002);
        experienceKey_ = 0;
        onChanged();
        return this;
      }

      // repeated int32 petId = 3;
      private java.util.List<java.lang.Integer> petId_ = java.util.Collections.emptyList();
      private void ensurePetIdIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          petId_ = new java.util.ArrayList<java.lang.Integer>(petId_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public java.util.List<java.lang.Integer>
          getPetIdList() {
        return java.util.Collections.unmodifiableList(petId_);
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public int getPetIdCount() {
        return petId_.size();
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public int getPetId(int index) {
        return petId_.get(index);
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public Builder setPetId(
          int index, int value) {
        ensurePetIdIsMutable();
        petId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public Builder addPetId(int value) {
        ensurePetIdIsMutable();
        petId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public Builder addAllPetId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensurePetIdIsMutable();
        super.addAll(values, petId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 petId = 3;</code>
       */
      public Builder clearPetId() {
        petId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestOperateExperience)
    }

    static {
      defaultInstance = new RequestOperateExperience(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestOperateExperience)
  }

  public interface ResponseOperateExperienceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // required int32 type = 2;
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    boolean hasType();
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    int getType();

    // required int32 experienceKey = 3;
    /**
     * <code>required int32 experienceKey = 3;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    boolean hasExperienceKey();
    /**
     * <code>required int32 experienceKey = 3;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    int getExperienceKey();

    // repeated .protocol.Item item = 4;
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // optional .protocol.Experience Experience = 5;
    /**
     * <code>optional .protocol.Experience Experience = 5;</code>
     */
    boolean hasExperience();
    /**
     * <code>optional .protocol.Experience Experience = 5;</code>
     */
    protocol.ExploreData.Experience getExperience();
    /**
     * <code>optional .protocol.Experience Experience = 5;</code>
     */
    protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseOperateExperience}
   *
   * <pre>
   *2281 
   * </pre>
   */
  public static final class ResponseOperateExperience extends
      com.google.protobuf.GeneratedMessage
      implements ResponseOperateExperienceOrBuilder {
    // Use ResponseOperateExperience.newBuilder() to construct.
    private ResponseOperateExperience(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseOperateExperience(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseOperateExperience defaultInstance;
    public static ResponseOperateExperience getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseOperateExperience getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseOperateExperience(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              type_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              experienceKey_ = input.readInt32();
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000008;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 42: {
              protocol.ExploreData.Experience.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) == 0x00000008)) {
                subBuilder = experience_.toBuilder();
              }
              experience_ = input.readMessage(protocol.ExploreData.Experience.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(experience_);
                experience_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.ExploreData.internal_static_protocol_ResponseOperateExperience_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.ExploreData.internal_static_protocol_ResponseOperateExperience_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.ExploreData.ResponseOperateExperience.class, protocol.ExploreData.ResponseOperateExperience.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseOperateExperience> PARSER =
        new com.google.protobuf.AbstractParser<ResponseOperateExperience>() {
      public ResponseOperateExperience parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseOperateExperience(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseOperateExperience> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 type = 2;
    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_;
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    public boolean hasType() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 type = 2;</code>
     *
     * <pre>
     *操作类型 1进行探险2撤销探险3 完成探险
     * </pre>
     */
    public int getType() {
      return type_;
    }

    // required int32 experienceKey = 3;
    public static final int EXPERIENCEKEY_FIELD_NUMBER = 3;
    private int experienceKey_;
    /**
     * <code>required int32 experienceKey = 3;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    public boolean hasExperienceKey() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 experienceKey = 3;</code>
     *
     * <pre>
     *探险任务id
     * </pre>
     */
    public int getExperienceKey() {
      return experienceKey_;
    }

    // repeated .protocol.Item item = 4;
    public static final int ITEM_FIELD_NUMBER = 4;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 4;</code>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // optional .protocol.Experience Experience = 5;
    public static final int EXPERIENCE_FIELD_NUMBER = 5;
    private protocol.ExploreData.Experience experience_;
    /**
     * <code>optional .protocol.Experience Experience = 5;</code>
     */
    public boolean hasExperience() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional .protocol.Experience Experience = 5;</code>
     */
    public protocol.ExploreData.Experience getExperience() {
      return experience_;
    }
    /**
     * <code>optional .protocol.Experience Experience = 5;</code>
     */
    public protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder() {
      return experience_;
    }

    private void initFields() {
      errorId_ = 0;
      type_ = 0;
      experienceKey_ = 0;
      item_ = java.util.Collections.emptyList();
      experience_ = protocol.ExploreData.Experience.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasType()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasExperienceKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      if (hasExperience()) {
        if (!getExperience().isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, type_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, experienceKey_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(4, item_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeMessage(5, experience_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, experienceKey_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, item_.get(i));
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, experience_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.ExploreData.ResponseOperateExperience parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.ExploreData.ResponseOperateExperience parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.ExploreData.ResponseOperateExperience prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseOperateExperience}
     *
     * <pre>
     *2281 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.ExploreData.ResponseOperateExperienceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.ExploreData.internal_static_protocol_ResponseOperateExperience_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.ExploreData.internal_static_protocol_ResponseOperateExperience_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.ExploreData.ResponseOperateExperience.class, protocol.ExploreData.ResponseOperateExperience.Builder.class);
      }

      // Construct using protocol.ExploreData.ResponseOperateExperience.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
          getExperienceFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        experienceKey_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          itemBuilder_.clear();
        }
        if (experienceBuilder_ == null) {
          experience_ = protocol.ExploreData.Experience.getDefaultInstance();
        } else {
          experienceBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.ExploreData.internal_static_protocol_ResponseOperateExperience_descriptor;
      }

      public protocol.ExploreData.ResponseOperateExperience getDefaultInstanceForType() {
        return protocol.ExploreData.ResponseOperateExperience.getDefaultInstance();
      }

      public protocol.ExploreData.ResponseOperateExperience build() {
        protocol.ExploreData.ResponseOperateExperience result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.ExploreData.ResponseOperateExperience buildPartial() {
        protocol.ExploreData.ResponseOperateExperience result = new protocol.ExploreData.ResponseOperateExperience(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.experienceKey_ = experienceKey_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        if (experienceBuilder_ == null) {
          result.experience_ = experience_;
        } else {
          result.experience_ = experienceBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.ExploreData.ResponseOperateExperience) {
          return mergeFrom((protocol.ExploreData.ResponseOperateExperience)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.ExploreData.ResponseOperateExperience other) {
        if (other == protocol.ExploreData.ResponseOperateExperience.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasExperienceKey()) {
          setExperienceKey(other.getExperienceKey());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000008);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasExperience()) {
          mergeExperience(other.getExperience());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasType()) {
          
          return false;
        }
        if (!hasExperienceKey()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        if (hasExperience()) {
          if (!getExperience().isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.ExploreData.ResponseOperateExperience parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.ExploreData.ResponseOperateExperience) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 type = 2;
      private int type_ ;
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public boolean hasType() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public int getType() {
        return type_;
      }
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public Builder setType(int value) {
        bitField0_ |= 0x00000002;
        type_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 type = 2;</code>
       *
       * <pre>
       *操作类型 1进行探险2撤销探险3 完成探险
       * </pre>
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      // required int32 experienceKey = 3;
      private int experienceKey_ ;
      /**
       * <code>required int32 experienceKey = 3;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public boolean hasExperienceKey() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 experienceKey = 3;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public int getExperienceKey() {
        return experienceKey_;
      }
      /**
       * <code>required int32 experienceKey = 3;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public Builder setExperienceKey(int value) {
        bitField0_ |= 0x00000004;
        experienceKey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 experienceKey = 3;</code>
       *
       * <pre>
       *探险任务id
       * </pre>
       */
      public Builder clearExperienceKey() {
        bitField0_ = (bitField0_ & ~0x00000004);
        experienceKey_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 4;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 4;</code>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // optional .protocol.Experience Experience = 5;
      private protocol.ExploreData.Experience experience_ = protocol.ExploreData.Experience.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder> experienceBuilder_;
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public boolean hasExperience() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public protocol.ExploreData.Experience getExperience() {
        if (experienceBuilder_ == null) {
          return experience_;
        } else {
          return experienceBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public Builder setExperience(protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          experience_ = value;
          onChanged();
        } else {
          experienceBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public Builder setExperience(
          protocol.ExploreData.Experience.Builder builderForValue) {
        if (experienceBuilder_ == null) {
          experience_ = builderForValue.build();
          onChanged();
        } else {
          experienceBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public Builder mergeExperience(protocol.ExploreData.Experience value) {
        if (experienceBuilder_ == null) {
          if (((bitField0_ & 0x00000010) == 0x00000010) &&
              experience_ != protocol.ExploreData.Experience.getDefaultInstance()) {
            experience_ =
              protocol.ExploreData.Experience.newBuilder(experience_).mergeFrom(value).buildPartial();
          } else {
            experience_ = value;
          }
          onChanged();
        } else {
          experienceBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public Builder clearExperience() {
        if (experienceBuilder_ == null) {
          experience_ = protocol.ExploreData.Experience.getDefaultInstance();
          onChanged();
        } else {
          experienceBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public protocol.ExploreData.Experience.Builder getExperienceBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getExperienceFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      public protocol.ExploreData.ExperienceOrBuilder getExperienceOrBuilder() {
        if (experienceBuilder_ != null) {
          return experienceBuilder_.getMessageOrBuilder();
        } else {
          return experience_;
        }
      }
      /**
       * <code>optional .protocol.Experience Experience = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder> 
          getExperienceFieldBuilder() {
        if (experienceBuilder_ == null) {
          experienceBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.ExploreData.Experience, protocol.ExploreData.Experience.Builder, protocol.ExploreData.ExperienceOrBuilder>(
                  experience_,
                  getParentForChildren(),
                  isClean());
          experience_ = null;
        }
        return experienceBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseOperateExperience)
    }

    static {
      defaultInstance = new ResponseOperateExperience(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseOperateExperience)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Experience_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Experience_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Rule_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Rule_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetExperiences_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetExperiences_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetExperiences_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetExperiences_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestFlushExperience_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestFlushExperience_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseFlushExperience_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseFlushExperience_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestOperateExperience_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestOperateExperience_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseOperateExperience_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseOperateExperience_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rexplore.proto\022\010protocol\032\tpet.proto\032\nit" +
      "em.proto\"j\n\nExperience\022\016\n\006status\030\001 \002(\005\022\013" +
      "\n\003key\030\002 \002(\005\022\021\n\tcountdown\030\003 \002(\005\022\r\n\005petId\030" +
      "\004 \003(\005\022\035\n\005rules\030\005 \003(\0132\016.protocol.Rule\"%\n\004" +
      "Rule\022\014\n\004type\030\001 \002(\005\022\017\n\007content\030\002 \002(\t\"\027\n\025R" +
      "equestGetExperiences\"f\n\026ResponseGetExper" +
      "iences\022\017\n\007errorId\030\001 \002(\005\022\021\n\tflushNums\030\002 \002" +
      "(\005\022(\n\nexperience\030\003 \003(\0132\024.protocol.Experi" +
      "ence\"\030\n\026RequestFlushExperience\"T\n\027Respon" +
      "seFlushExperience\022\017\n\007erroeId\030\001 \002(\005\022(\n\nex",
      "perience\030\002 \003(\0132\024.protocol.Experience\"N\n\030" +
      "RequestOperateExperience\022\014\n\004type\030\001 \002(\005\022\025" +
      "\n\rexperienceKey\030\002 \002(\005\022\r\n\005petId\030\003 \003(\005\"\231\001\n" +
      "\031ResponseOperateExperience\022\017\n\007errorId\030\001 " +
      "\002(\005\022\014\n\004type\030\002 \002(\005\022\025\n\rexperienceKey\030\003 \002(\005" +
      "\022\034\n\004item\030\004 \003(\0132\016.protocol.Item\022(\n\nExperi" +
      "ence\030\005 \001(\0132\024.protocol.Experience*>\n\010Rule" +
      "Type\022\r\n\tATTRIBUTE\020\001\022\016\n\nOCCUPATION\020\002\022\t\n\005L" +
      "EVEL\020\003\022\010\n\004STAR\020\004B\rB\013ExploreData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_Experience_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_Experience_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Experience_descriptor,
              new java.lang.String[] { "Status", "Key", "Countdown", "PetId", "Rules", });
          internal_static_protocol_Rule_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_Rule_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Rule_descriptor,
              new java.lang.String[] { "Type", "Content", });
          internal_static_protocol_RequestGetExperiences_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestGetExperiences_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetExperiences_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetExperiences_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseGetExperiences_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetExperiences_descriptor,
              new java.lang.String[] { "ErrorId", "FlushNums", "Experience", });
          internal_static_protocol_RequestFlushExperience_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_RequestFlushExperience_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestFlushExperience_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseFlushExperience_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_ResponseFlushExperience_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseFlushExperience_descriptor,
              new java.lang.String[] { "ErroeId", "Experience", });
          internal_static_protocol_RequestOperateExperience_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_protocol_RequestOperateExperience_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestOperateExperience_descriptor,
              new java.lang.String[] { "Type", "ExperienceKey", "PetId", });
          internal_static_protocol_ResponseOperateExperience_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_protocol_ResponseOperateExperience_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseOperateExperience_descriptor,
              new java.lang.String[] { "ErrorId", "Type", "ExperienceKey", "Item", "Experience", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.PetData.getDescriptor(),
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
