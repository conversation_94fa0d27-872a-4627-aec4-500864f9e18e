package module.robot;

import common.SuperConfig;
import entities.*;
import manager.MySql;
import manager.Redis;
import model.CupboardInfo;
import model.LoginInfo;
import module.login.ILogin;
import module.login.LoginDao;
import org.hibernate.Session;
import protocol.FriendData;
import protocol.UserData;
import utils.JdbcUtils;

import java.sql.*;
import java.util.*;

public class Factory {

    private static String names;
    //private static Redis jedis = Redis.getInstance();
    public static int everyStationRobotNums=5;
    public static int robotStationNums=50;
   // public static int headNums=10;
    public static int lv=30;
   // public static int roleNums=3;
    public static Map<Integer, List<UserData.Passenger>> robotPoolMap=new HashMap<Integer, List<UserData.Passenger>>();
  /* private static int getDressId(int type,HashMap<Integer,List<Integer>> map){
       List<Integer> list=map.get(type);
       return list==null?0:list.get((int)(Math.random()*list.size()));
   }*/



    public synchronized static int addRobot(){
        StringBuffer isrobot=new StringBuffer("from RoleEntity where robot = 1");
        List<Object> listRobot = MySql.queryForList(isrobot.toString());

        StringBuffer all=new StringBuffer("from RoleEntity");
        List<Object> listAll = MySql.queryForList(all.toString());
        float robotCount = listRobot.size();
        float allCount = listAll.size();
       if( robotCount/allCount >0.03 ){
            /// System.out.println("人数达到上限");
            return -1;
        }else{
           names=ranName.name();
           ILogin iLogin = LoginDao.getInstance();
           LoginInfo loginInfo = new LoginInfo();
           loginInfo.setLoginType(3);
           loginInfo.setName(names);
           loginInfo.setPwd("123456");
           String uuid=UUID.randomUUID().toString().replace("-","").substring(0,6);
           int val = iLogin.register("start_"+uuid,loginInfo.getPwd(),"null","00");//创建User
           UserEntity user=(UserEntity)MySql.queryForOne(" FROM UserEntity order by id desc ",1);//得到UserId
           ((LoginDao) iLogin).setInitRobotRoleInfo(user.getUserid(),names);//创建Role
           RoleEntity role=(RoleEntity)MySql.queryForOne(" FROM RoleEntity order by id desc ",1);//得到roleid
            /*StringBuffer dsql=new StringBuffer("delete from PartEntity where part4=4 and part5=5 and part6=6 and uid='").append(role.getUid()).append("'");
            MySql.updateSomes(dsql.toString());*/
           StringBuffer hql3=new StringBuffer("update RoleEntity set robot=1 where uid='").append(role.getUid()).append("' ");
           StringBuffer hql1=new StringBuffer("update RoleEntity set newuser= '").append(RobotConfig.newusers).append("' where uid='").append(role.getUid()).append("' ");
           StringBuffer hql2=new StringBuffer("update UserEntity set roleuid= '").append(role.getUid()).append("' where userid='").append(user.getUserid()).append("' ");
           MySql.mustUpdateSomes(hql1.toString());
           MySql.mustUpdateSomes(hql2.toString());
           MySql.mustUpdateSomes(hql3.toString());
           StringBuffer hql4=new StringBuffer("update RoleEntity set role2=1,role3=1 ").append("where uid='").append(role.getUid()).append("' ");;
           MySql.mustUpdateSomes(hql4.toString());
           //服装


         //  CupboardInfo cupboard=new CupboardInfo();
           PartEntity partEntity = new PartEntity();
           partEntity.setUid(role.getUid());
           PartEntity partEntity2 = ranAccount.dress(partEntity);
           MySql.mustInsert(partEntity2);
           DressEntity dressA;
           if(partEntity2.getPart1()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart1());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart2()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart2());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart3()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart3());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart4()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart4());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart5()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart5());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart6()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart6());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart7()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart7());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart8()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart8());
               MySql.mustInsert(dressA);
           }
           if(partEntity2.getPart9()!=0){
               dressA=new DressEntity();
               dressA.setUid(role.getUid());
               dressA.setVersion(0);
               dressA.setOverstamp("0");
               dressA.setRoleid(partEntity2.getRoleid());
               dressA.setDressid(partEntity2.getPart9());
               MySql.mustInsert(dressA);
           }
           StringBuffer hql5=new StringBuffer("update RoleEntity set roleid= '").append(partEntity2.getRoleid()).append("' where uid='").append(role.getUid()).append("' ");;
           MySql.mustUpdateSomes(hql5.toString());

           //背包
           ItemEntity itemEntity;
           int[] nums=new int[2];
           nums[0]=1;
           nums[1]=2;
 /*          nums[2]=24;
           nums[3]=25;*/

           for(int i=0;i<nums.length;i++){
               itemEntity = new ItemEntity();
               itemEntity.setItemid(nums[i]);
               itemEntity.setUid(role.getUid());
               itemEntity.setType(0);
               itemEntity.setItemnum(10.0);
               MySql.mustInsert(itemEntity);
           }
           Redis jedis=Redis.getInstance();
           //成就
           Set<String> tasksize = jedis.keys(SuperConfig.REDIS_EXCEL_TASK+"*");
           int rows = tasksize.size();
           List<String> ids=new ArrayList<String>();
           for(int a=rows-10;a<=rows;a++){
               if(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK, rows + "", "first")!=null){
                   int isFirst=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK, rows + "", "first"));
                   if(1==isFirst){
                       ids.add(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK, rows + "", "ID"));
                   }
               }
           }
           TaskEntity task;
           for(int b=0;b<ids.size();b++){
               task=new TaskEntity();
               task.setType(2);
               task.setUid(role.getUid());
               task.setVersion(0);
               task.setNum(0);
               task.setStatus(0);
               task.setTaskid(Integer.parseInt(ids.get(b).substring(0,ids.get(b).length()-2)));
               /// System.out.println(task);
               MySql.mustInsert(task);
           }
           /* Connection connection=null;
            Statement statement=null;
            try {
                connection = JdbcUtils.getConnection();
            } catch (ClassNotFoundException e) {
                e.printStackTrace();
            } catch (SQLException e) {
                e.printStackTrace();
            }
            try {
                statement=connection.createStatement();
                statement.executeUpdate(dsql.toString());
                StringBuffer  aa    =new StringBuffer(" FROM PartEntity where uid='").append(role.getUid()).append("'");
                /// System.out.println(aa);
                List<Object> bb =MySql.queryForList(aa.toString());
                /// System.out.println(bb.size()+connection.toString());
                statement.close();
                connection.close();
                /// System.out.println(bb.size()+"刪除成功！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！！");
            } catch (SQLException e) {
                e.printStackTrace();
            }*/
           /// System.out.println("创造机器人");

       }

            return  0;
    }


}
