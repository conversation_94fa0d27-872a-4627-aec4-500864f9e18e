package module.synchronization;

import model.PointInfo;

/**
 * Created by nara on 2017/12/5.
 */
public class SyncMissionInfo {
    private int queue;
    private int playerId;
    private float speed;
    private float direction;
    private int combo;
    private PointInfo point;

    public int getCombo() {
        return combo;
    }

    public void setCombo(int combo) {
        this.combo = combo;
    }

    public int getQueue() {
        return queue;
    }

    public void setQueue(int queue) {
        this.queue = queue;
    }

    public int getPlayerId() {
        return playerId;
    }

    public void setPlayerId(int playerId) {
        this.playerId = playerId;
    }

    public float getDirection() {
        return direction;
    }

    public void setDirection(float direction) {
        this.direction = direction;
    }

    public float getSpeed() {
        return speed;
    }

    public void setSpeed(float speed) {
        this.speed = speed;
    }

    public PointInfo getPoint() {
        return point;
    }

    public void setPoint(PointInfo point) {
        this.point = point;
    }
}
