package module.activity.LimitedTime;

import manager.ReportManager;
import protocol.LimitedTimeRewardData;
import protocol.ProtoData;
import server.SuperServerHandler;

public class LimitedTimeRewardTimer {
    private static LimitedTimeRewardTimer inst = null;
    public static LimitedTimeRewardTimer getInstance() {
        if (inst == null) {
            inst = new LimitedTimeRewardTimer();
        }
        return inst;
    }
    // 每秒执行
    public void PerSecondRun() {
        CheckTime();
    }
    // 多长时间重置限时任务
    private final long Time = 604800;
    private long CurTime = 0;
    // 温度层刷新
    private void CheckTime(){
        CurTime++;
        if (CurTime < Time){
            return;
        }
        CurTime = 0;
        System.err.println("限时任务清理");

        LimitedTimeRewardDao.getInstance().Delete();
        LimitedTimeRewardTaskDao.getInstance().Delete();
        LimitedTimeRewardCompleteTaskDao.getInstance().Delete();

        for (String uid :
                SuperServerHandler.linkMap.values()) {
            LimitedTimeRewardData.ResponseLimitedTimeReward.Builder builder = LimitedTimeRewardData.ResponseLimitedTimeReward.newBuilder();

//            data.setCharge_reward(false);
//            data.setExp(0);
//            data.setOrdinary_reward_id(1);
//            data.setCharge_reward_id(1);

            builder.setChargeReward(false);
            builder.setExp(0);
            builder.setOrdinaryRewardId(1);
            builder.setChargeRewardId(1);

            ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSELIMITEDTIMEREWARD_VALUE,
                    builder.build().toByteArray());

            LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.Builder builder2 =
                    LimitedTimeRewardData.ResponseLimitedTimeRewardTaskData.newBuilder();
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSELIMITEDTIMEREWARDTASKDATA_VALUE,
                    builder2.build().toByteArray());
        }

    }

    public long getCurTime() {
        return CurTime;
    }
}
