package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/1/5.
 */
@Entity
@Table(name = "mission", schema = "", catalog = "super_star_fruit")
public class MissionEntity {
    private int id;
    private int version;
    private String uid;
    private int type;
    private Integer missionid;
    private int nums;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Version
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "missionid")
    public Integer getMissionid() {
        return missionid;
    }

    public void setMissionid(Integer missionid) {
        this.missionid = missionid;
    }

    @Basic
    @Column(name = "nums")
    public int getNums() {
        return nums;
    }

    public void setNums(int nums) {
        this.nums = nums;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        MissionEntity that = (MissionEntity) o;

        if (id != that.id) return false;
        if (version != that.version) return false;
        if (type != that.type) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (missionid != null ? !missionid.equals(that.missionid) : that.missionid != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + version;
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + type;
        result = 31 * result + (missionid != null ? missionid.hashCode() : 0);
        result = 31 * result + nums;
        return result;
    }
}
