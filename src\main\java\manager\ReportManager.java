package manager;

import io.netty.channel.ChannelHandlerContext;
import model.StationInfo;
import protocol.ProtoData;
import protocol.UserData;
import server.SuperServerHandler;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by nara on 2018/2/2.
 */
public class ReportManager {
    private static ExecutorService loginServiceThread;
    public static void init(){
      loginServiceThread = Executors.newFixedThreadPool(10);
    }
    public static void reportInfo(String uid,int msgId,byte[] bytes){
        ReportHandler reportHandler = new ReportHandler();
        reportHandler.setRoleId(uid);
        reportHandler.setMsgId(msgId);
        reportHandler.setReportMsg(bytes);
      //  /// System.out.println("fox==========测试ReportManager");
      //  Thread thread = new Thread(reportHandler);
      //  thread.start();
        loginServiceThread.execute(reportHandler);
       // reportHandler.run();
    }

    public static void reportInfo(ChannelHandlerContext ctx,int msgId,byte[] bytes){
        ReportHandler reportHandler = new ReportHandler();
        reportHandler.setCtx(ctx);
        reportHandler.setMsgId(msgId);
        reportHandler.setReportMsg(bytes);
       // Thread thread = new Thread(reportHandler);
      //  thread.start();
    loginServiceThread.execute(reportHandler);
        //  reportHandler.run();
    }

    public static void boardcastInfo(int server,int type,int msgId,byte[] bytes){
        BoardcastHandler boardcastHandler = new BoardcastHandler();
        boardcastHandler.setType(type);
        boardcastHandler.setMsgId(msgId);
        boardcastHandler.setReportMsg(bytes);
        Thread thread = new Thread(boardcastHandler);
        thread.start();
    }


///////////////////////////////////////////////////////////////////////////////////////////////
    public static void reportSystemBroadcastFriend(String uid,int id,String content){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setType(2);
        reportHandler.setUid(uid);
        reportHandler.setId(id);
        reportHandler.setContent(content);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendStatus(String uid,int status){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setStatus(status);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendName(String uid,String name){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setName(name);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendLv(String uid,int lv){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setLv(lv);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendHead(String uid,int head){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setHead(head);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendStation(String uid,int station){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setStation(station);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendSignature(String uid,String signature){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setSignature(signature);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
    public static void reportUpdateFriendEndless(String uid,long endless){
        UpdateFriendHandler reportHandler = new UpdateFriendHandler();
        reportHandler.setUid(uid);
        reportHandler.setEndless(endless);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }
////////////////////////////////////////////////////////////////////////////////////

    public static void reportRepeatLogin(ChannelHandlerContext ctx){
        ReportHandler reportHandler = new ReportHandler();
        reportHandler.setCtx(ctx);
        reportHandler.setMsgId(ProtoData.SToC.REPORTREPEATLOGIN_VALUE);
        reportHandler.setReportMsg(new byte[0]);
        reportHandler.setNeedClose(true);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }


    public static void reportForceToOffline(ChannelHandlerContext ctx){
        ReportHandler reportHandler = new ReportHandler();
        reportHandler.setCtx(ctx);
        reportHandler.setMsgId(ProtoData.SToC.REPORTFORCETOOFFLINE_VALUE);
        reportHandler.setReportMsg(new byte[0]);
        reportHandler.setNeedClose(true);
        Thread thread = new Thread(reportHandler);
        thread.start();
    }

    public static void reportStationInChange(String uid,int station,byte[] bytes){
        List<StationInfo> list = SuperServerHandler.stationMap.get(station);
        if (list != null){
            //~~~~~~~~~~~~~~~~
//            Redis jedis = Redis.getInstance();
  //  int server = Redis.getRoleServer(uid);
   //       ILogin iLogin = LoginDao.getInstance();
  //         List<FriendInfo> friendInfoList = iLogin.getFriendInfoFromRedis(uid);
//            for (int i = 0 ; i < friendInfoList.size() ; i++){
//                FriendInfo friendInfo = friendInfoList.get(i);
//                ChannelHandlerContext ctx = SuperServerHandler.getFriendCtxFromUid(server,friendInfo.getUid());
//                if (ctx != null){
//                    Map<String, String> map = jedis.hgetAll("rolefriendinstation:" + friendInfo.getUid());
//                    FriendInStationInfo friendInStationInfo = new FriendInStationInfo();
//                    friendInStationInfo.setFriendUid(uid);
//                    friendInStationInfo.setValue(friendInfo.getValue());
//                  if (map.size() < SuperConfig.STATIONTOTALROLE) {//加入车站
//                        jedis.hset("rolefriendinstation:" + friendInfo.getUid(),uid, MyUtils.objectToJson(friendInStationInfo));
//                        //通知加入车站
     //                reportInfo(ctx,ProtoData.SToC.REPORTINANDOUTSTATION_VALUE,bytes);
//                    } else {//替换一个出来
//                        List<FriendInStationInfo> friendInStationInfoList = new ArrayList<FriendInStationInfo>();
//                        for (Map.Entry<String,String>entry:map.entrySet()){
//                            FriendInStationInfo inStationInfo = (FriendInStationInfo)MyUtils.jsonToBean(entry.getValue(),FriendInStationInfo.class);
//                            friendInStationInfoList.add(inStationInfo);
//                        }
//                        friendInStationInfoList.add(friendInStationInfo);
//                        Collections.sort(friendInStationInfoList, new Comparator() {
//                            public int compare(Object o1, Object o2) {
//                                FriendInStationInfo f1 = (FriendInStationInfo)o1;
//                                FriendInStationInfo f2 = (FriendInStationInfo)o2;
//                                return f2.getValue() - f1.getValue();
//                            }
//                        });
//                        FriendInStationInfo delFriend = friendInStationInfoList.get(SuperConfig.STATIONTOTALROLE);
//                        if (!delFriend.getFriendUid().equals(uid)){
//                            //in
//                            jedis.hset("rolefriendinstation:" + friendInfo.getUid(), uid, MyUtils.objectToJson(friendInStationInfo));
//                            reportInfo(ctx, ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, bytes);
//                            //out
//                            jedis.hdel("rolefriendinstation:" + friendInfo.getUid(), delFriend.getFriendUid());
//                            UserData.ReportInAndOutStation.Builder stationBu = UserData.ReportInAndOutStation.newBuilder();
//                            stationBu.setType(2);
//                            int roleId = iLogin.getIdFromUid(delFriend.getFriendUid());
//                            stationBu.setPassengerId(roleId);
//                            reportInfo(ctx, ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, stationBu.build().toByteArray());
//                        }
//                    }
//                }
//            }
            //~~~~~~~~~~~~~~
            ChannelHandlerContext uidCtx = SuperServerHandler.getCtxFromUid(uid);
            for (int i = 0 ; i<list.size() ; i++){
                StationInfo stationInfo = list.get(i);
                if (uidCtx != stationInfo.getCtx()){
                    String key="rolePassenger"+stationInfo.getUid();
                    Redis jedis=Redis.getInstance();
                   Map<String,String> map =jedis.hgetAll(key);
                    String robotPassengerId=null;
                    for (Map.Entry<String,String> entry:map.entrySet()){
                         String filed=entry.getKey();
                         robotPassengerId=entry.getValue();
                         Redis.hdel(key,filed);
                         break;
                   }
                    if(robotPassengerId!=null){
                        UserData.ReportInAndOutStation.Builder reportInAndOutStation=UserData.ReportInAndOutStation.newBuilder();
                        reportInAndOutStation.setType(2);
                        reportInAndOutStation.setPassengerId(Integer.parseInt(robotPassengerId));
                        reportInfo(stationInfo.getCtx(), ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, reportInAndOutStation.build().toByteArray());
                    }


                    reportInfo(stationInfo.getCtx(), ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, bytes);
                }
            }
        }
    }


    public static void reportStationLocation(String uid,int station,byte[] bytes){
        List<StationInfo> list = SuperServerHandler.stationMap.get(station);
        if (list != null){
//            Redis jedis = Redis.getInstance();
//            int server = Redis.getRoleServer(uid);
//            ILogin iLogin = LoginDao.getInstance();
//            List<FriendInfo> friendInfoList = iLogin.getFriendInfoFromRedis(uid);
//            for (int i = 0 ; i < friendInfoList.size() ; i++){
//                FriendInfo friendInfo = friendInfoList.get(i);
//                ChannelHandlerContext ctx = SuperServerHandler.getFriendCtxFromUid(server,friendInfo.getUid());
//                if (ctx != null){
//                    Map<String, String> map = jedis.hgetAll("rolefriendinstation:" + friendInfo.getUid());
//                    if (map.containsKey(uid) == true){
//                        reportInfo(friendInfo.getUid(), ProtoData.SToC.REPORTSTATIONLOCATION_VALUE,bytes);
//                    }
//                }
//            }
            ChannelHandlerContext uidCtx = SuperServerHandler.getCtxFromUid(uid);
            for (int i = 0 ; i<list.size() ; i++){
                StationInfo stationInfo = list.get(i);
                if (uidCtx != stationInfo.getCtx()){
                    reportInfo(stationInfo.getCtx(), ProtoData.SToC.REPORTSTATIONLOCATION_VALUE, bytes);
                }
            }
        }
    }
}
