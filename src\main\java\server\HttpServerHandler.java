package server;

import java.net.InetAddress;
import java.net.URLDecoder;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.internal.util.StringUtils;

import common.SuperConfig;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFutureListener;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.codec.http.DefaultFullHttpResponse;
import io.netty.handler.codec.http.FullHttpRequest;
import io.netty.handler.codec.http.FullHttpResponse;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.netty.handler.codec.http.HttpMethod;
import io.netty.handler.codec.http.HttpResponseStatus;
import io.netty.handler.codec.http.HttpVersion;
import io.netty.util.CharsetUtil;
import manager.Redis;
import manager.ReportManager;
import module.item.IItem;
import module.item.ItemDao;
import module.login.ILogin;
import module.login.LoginDao;
import module.pay.AliPaytUtil;
import module.pay.IPay;
import module.pay.PayDao;
import module.pay.WeChatUtil;
import protocol.ItemData;
import protocol.PayData;
import protocol.ProtoData;
import utils.MyUtils;
import utils.XMLUtil;

public class HttpServerHandler extends ChannelInboundHandlerAdapter {
    private static Logger log = LoggerFactory.getLogger(HttpServerHandler.class);
    private String result = "";

    /*
     * 收到消息时，返回信息
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {

       // /// System.out.println("channelRead");
        if (!(msg instanceof FullHttpRequest)) {
            result = "未知请求!";
            send(ctx, result, HttpResponseStatus.BAD_REQUEST);
            return;
        }

        FullHttpRequest httpRequest = (FullHttpRequest) msg;
        try {
            String path = httpRequest.uri(); // 获取路径
            path = URLDecoder.decode(path,"UTF-8");
            System.out.println("ad callback:" + path);
            if (httpRequest.method() == HttpMethod.GET) {
                Map<String, String> map = MyUtils.URLRequest(path);
                    
                if (map == null || map.isEmpty()) {
                    result = "参数有误";
                    send(ctx, result, HttpResponseStatus.BAD_REQUEST);
                    return;
                }                
                // 如果不是这个路径，就直接返回错误
                if (path.contains("/awardUser")) {

                    // String user_id = map.get("user_id");
                    // String transaction_id = map.get("transaction_id");
                    // String rewards = map.get("reward_item");

                    // receiveAD(ctx, user_id, transaction_id);
                }
                else if(path.contains("/toutiaoAD") ) {
                    String user_id = map.get("user_id");
                    String transaction_id = map.get("trans_id");
                    int rewards = Integer.parseInt( map.get("reward_name") );  
                    int reward_amount = Integer.parseInt( map.get("reward_amount") );
                    if( rewards == 24 )
                    {
                        int itemID = rewards;
                        int itemCount = reward_amount;
                        IItem iItem = ItemDao.getInstance();
                        double newNum = iItem.updateItemInfo(user_id, itemID, itemCount);

                        ItemData.ResponseUseItem.Builder builder = ItemData.ResponseUseItem.newBuilder();
                        builder.setType(1);
                        builder.setErrorId(0);
                       
                        ItemData.Item.Builder deItemBu = ItemData.Item.newBuilder();
                        deItemBu.setId(itemID);
                        deItemBu.setNum(newNum);
                        builder.addItem(deItemBu);  
                        ReportManager.reportInfo(user_id, ProtoData.SToC.RESPONSEUSEITEM_VALUE, builder.build().toByteArray());
                      
                    } 
                    else
                    {
                        //receiveAD(ctx, user_id, transaction_id);
                    }                 
                }
                else if(path.contains("/googleAD") ) {
                    System.out.println("googleAD:"+path);
                }
            }

            if (httpRequest.method() == HttpMethod.POST) {
                if (path.contains("/WeChatCallback") ) {
                    ByteBuf buf = httpRequest.content();
                    byte[] bytes = new byte[buf.readableBytes()];
                    buf.readBytes(bytes);
                    String xml = new String(bytes);
                    /// System.out.println(">>>>>>>>>xml:" + xml);
                    send(ctx, receiveWeChatPay(xml), HttpResponseStatus.OK);
                }
                else if(path.contains("/AliCallback") )
                {                    
                    ByteBuf buf = httpRequest.content();
                    byte[] bytes = new byte[buf.readableBytes()];
                    buf.readBytes(bytes);
                    String uri = new String(bytes);

                    Map<String, String> params = MyUtils.URLRequest(uri);

                    for(Map.Entry<String,String> entry : params.entrySet()) {
                        String value = URLDecoder.decode(entry.getValue(),"UTF-8");
                        params.put(entry.getKey(), value);
                    }                    
                    /// System.out.println(">>>>>>>>>params:" + params.toString());

                    boolean flag = AlipaySignature.rsaCheckV1(params, AliPaytUtil.ALIPAY_PUBLIC_KEY, AliPaytUtil.CHARSET, AliPaytUtil.SIGNTYPE);
                    if (!flag)
                    {
                        send(ctx, "fail", HttpResponseStatus.OK);
                        /// System.out.println(flag+"flag" );
                        return;
                    }
                    if(!"TRADE_SUCCESS".equals(params.get("trade_status")))

                    {
                        send(ctx, "fail", HttpResponseStatus.OK);
                        /// System.out.println("TRADE_SUCCESS.equals(params.get(trade_status)");
                        return;                       
                    }

                    String orderCode = params.get("out_trade_no");
                    String appId = params.get("app_id");
                    //订单金额
                    String totalAmount = params.get("total_amount");
                    //回传参数
                    String key = params.get("passback_params");
                    String ali_trade_no = params.get("trade_no");
                    key = URLDecoder.decode(key,"UTF-8");
                    
                    if (StringUtils.isEmpty(orderCode)) {
                        send(ctx, "fail", HttpResponseStatus.OK);
                        /// System.out.println("StringUtils.isEmpty(orderCode)");
                        return;
                    }

                    if (!AliPaytUtil.AppID.equals(appId)) {
                        send(ctx, "fail", HttpResponseStatus.OK);
                        /// System.out.println("!AliPaytUtil.AppID.equals(appId)");
                        return;
                    }

                    String result = receiveAliPay(key, orderCode,ali_trade_no);

                    send(ctx, result, HttpResponseStatus.OK);

                    /// System.out.println("AliCallback  OK"+result);

                }
                else if(path.contains("/WebPageRegister")){
                    Map<String, String> map = MyUtils.URLRequest(path);
                    ILogin iLogin = LoginDao.getInstance();
                    int val = iLogin.register(map.get("username"),map.get("password"),"null","0");
                }

            }

        } catch (Exception e) {
            /// System.out.println("处理请求失败!");
            e.printStackTrace();
        } finally {
            // 释放请求
            httpRequest.release();
        }
    }

    /**
     * 获取body参数
     * 
     * @param request
     * @return
     */
    private String getBody(FullHttpRequest request) {
        ByteBuf buf = request.content();
        return buf.toString(CharsetUtil.UTF_8);
    }

    /**
     * 发送的返回值
     * 
     * @param ctx     返回
     * @param context 消息
     * @param status  状态
     */
    private void send(ChannelHandlerContext ctx, String context, HttpResponseStatus status) {
        FullHttpResponse response = new DefaultFullHttpResponse(HttpVersion.HTTP_1_1, status,
                Unpooled.copiedBuffer(context, CharsetUtil.UTF_8));
        response.headers().set(HttpHeaderNames.CONTENT_TYPE, "application/json; charset=UTF-8");
        ctx.writeAndFlush(response).addListener(ChannelFutureListener.CLOSE);
    }

    /*
     * 建立连接时，返回消息
     */
    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        /// System.out.println("连接的客户端地址:" + ctx.channel().remoteAddress());
        ctx.writeAndFlush("客户端" + InetAddress.getLocalHost().getHostName() + "成功与服务端建立连接！ ");
        super.channelActive(ctx);
    }

    /**
     * 微信支付返回
     */
    public String receiveWeChatPay(String xmlStr) {
        /// System.out.println("pay call back!!!");

        Map<String, String> responseMap;
        try {
            responseMap = XMLUtil.doXMLParse(xmlStr);
        } catch (Exception e) {
            /// System.out.println("pay call back error!!!");
            return "";
        }

        String sign = responseMap.get("sign");
        if (!CheckWeChatPaySign(sign, responseMap)) {

            /// System.out.println("WEIXIN_PAY=========SIGN ERROR！");
            return "<xml>\n" + "  <return_code><![CDATA[FAIL]]></return_code>\n"
                    + "  <return_msg><![CDATA[SIGN ERROR]]></return_msg>\n" + "</xml>";
        }
        String return_code = responseMap.get("return_code");
        if (!return_code.equals("SUCCESS"))
            return "";


        Redis jedis = Redis.getInstance();

        String key = responseMap.get("attach");
        String tradeId = responseMap.get("out_trade_no");
        if (Redis.exists(key)) 
        {
            Map<String, String> map = jedis.hgetAll(key);
            if (map == null || map.isEmpty()) 
            {
                return "<xml>\n" + "  <return_code><![CDATA[SUCCESS]]></return_code>\n"
                        + "  <return_msg><![CDATA[OK]]></return_msg>\n" + "</xml>";
            }
            String payID = map.get("payid");
            String uid = map.get("uid");
            String cp_order = map.get("order");
            String product_id = map.get("productid");
            String transaction_id = map.get("platformOrder");
            if (!cp_order.equals(tradeId)) 
            {
                return "<xml>\n" + "  <return_code><![CDATA[SUCCESS]]></return_code>\n"
                        + "  <return_msg><![CDATA[OK]]></return_msg>\n" + "</xml>";
            }
            if (Integer.parseInt(map.get("status")) == 0) 
            {
                PayData.ResponseWeChatPay.Builder builder = PayData.ResponseWeChatPay.newBuilder();

                setPaySuccess(uid, product_id, transaction_id);
                IPay iPay = PayDao.getInstance();
                int payIndex = Integer.parseInt(payID);
                List<ItemData.Item.Builder> listItem = iPay.paySuccess(uid, payIndex, product_id, transaction_id, 3);
                for (int i = 0; i < listItem.size(); i++)
                    builder.addItem(listItem.get(i));
                builder.setSuccessId(payIndex);
                builder.setErrorId(0);
                builder.setTransactionID(cp_order);
                Map<String,String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payIndex);
        
                if(mapPay.get("firstRecharge").equals("1") )   
                {
                    ILogin iLogin = LoginDao.getInstance();
                    builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                }                 
                else
                    builder.setFirstRecharge(0);                

                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEWECHATPAY_VALUE, builder.build().toByteArray());

            } else {
                /// System.out.println("WEIXIN_PAY already get!");
            }
        }

        return "<xml>\n" + "  <return_code><![CDATA[SUCCESS]]></return_code>\n"
                + "  <return_msg><![CDATA[OK]]></return_msg>\n" + "</xml>";

    }

    public boolean CheckWeChatPaySign(String tenpaySign, Map<String, String> map) {
        StringBuffer sb = new StringBuffer();
        Iterator iterator = map.keySet().iterator();
        SortedMap parameters = new TreeMap();
        while (iterator.hasNext()) {
            String k = (String) iterator.next();
            String v = map.get(k);

            String val = "";
            if (null != v) {
                val = v.trim();
            }
            parameters.put(k, val);
        }
        Set es = parameters.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (!"sign".equals(k) && null != v && !"".equals(v)) {
                sb.append(k + "=" + v + "&");
            }
        }
        /// System.out.println("=======================================================key= + MCH_KEY");
        sb.append("key=" + WeChatUtil.MCH_KEY);

        // 算出摘要
        String sign = MyUtils.MD5Encode(sb.toString(), "UTF-8").toLowerCase();
        tenpaySign = tenpaySign.toLowerCase();

        return tenpaySign.equals(sign);
    }

    public String receiveAliPay(String key,String tradeId,String ali_tradeId ) 
    {
        Redis jedis = Redis.getInstance();

        if (Redis.exists(key)) 
        {
            Map<String, String> map = jedis.hgetAll(key);
            if (map == null || map.isEmpty()) 
            {
                return "fail";
            }

            String payID = map.get("payid");
            String uid = map.get("uid");
            String cp_order = map.get("order");
            String product_id = map.get("productid");
            String transaction_id = ali_tradeId;//map.get("platformOrder");

            if (!cp_order.equals(tradeId)) 
            {
                return "fail";
            }
            if (Integer.parseInt(map.get("status")) == 0) 
            {
                PayData.ResponseAliPay.Builder builder = PayData.ResponseAliPay.newBuilder();

                setPaySuccess(uid, product_id, transaction_id);
                IPay iPay = PayDao.getInstance();

                int payIndex = Integer.parseInt(payID);
                List<ItemData.Item.Builder> listItem = iPay.paySuccess(uid, payIndex, product_id, transaction_id, 4);
                for (int i = 0; i < listItem.size(); i++)
                    builder.addItem(listItem.get(i));

                builder.setSuccessId(payIndex);
                builder.setErrorId(0);
                builder.setTransactionID(cp_order);
                Map<String,String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payIndex);
        
                if(mapPay.get("firstRecharge").equals("1") )   
                {
                    ILogin iLogin = LoginDao.getInstance();
                    builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                }                 
                else
                    builder.setFirstRecharge(0);

                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEALIPAY_VALUE, builder.build().toByteArray());

            } else {
                /// System.out.println("ali_PAY already get!");
            }
            return "success";

        }

        return "fail";

    }    

    private void setPaySuccess(String uid, String productID, String transaction_id) {
        Redis jedis = Redis.getInstance();
        StringBuffer s = new StringBuffer("payinfo:").append(uid).append("$$").append(productID);
        jedis.hset(s.toString(), "status", "1");
        jedis.hset(s.toString(), "transaction_id", transaction_id);
    }

    // private void receiveAD(ChannelHandlerContext ctx,String user_id,Integer ad_id)
    // {
    //     AdService adService = AdService.getInstance();
    //     Redis jedis = Redis.getInstance();

    //     String item_type = jedis.hget("awardAdconfig:" + ad_id, "item");

    //     // 先从redis中查询clicknum中有不有数据
    //     Map<String, String> clickNumMap = jedis.hgetAll("clicknum:" + user_id);
    //     String jsonStr = clickNumMap.get(user_id);
    //     // 先判断event_id是否重复
    //     List<Object> list = adService.getClickNumList(ad_id);
    //     if (list.size() >= 1) {
    //         result = "事件id重复";
    //         send(ctx, result, HttpResponseStatus.OK);
    //         return;
    //     } else {
    //         // 从redis中拿clickNumMap
    //         if (clickNumMap == null || clickNumMap.size() == 0) {
    //             // 第一次点击广告
    //             adService.firstClick(item_type, user_id, ad_id);
    //         } else {
    //             // 判断点击次数是否大于10次
    //             ClickNumEntity clickNumEntity = (ClickNumEntity) MyUtils.jsonToBean(jsonStr,
    //                     ClickNumEntity.class);
    //             Integer click_num = clickNumEntity.getClick_num();
    //             int max = Integer.parseInt(jedis.hget("awardAdconfig:1", "max"));

    //             // 不是第一次点击广告
    //             Date click_time = clickNumEntity.getClick_time();
    //             // 判断是不是同一天
    //             boolean isSameDate = MyUtils.isSameDay(new Date(), click_time);
    //             if (isSameDate) {
    //                 // 是同一天
    //                 if (click_num < max) {
    //                     Date date = new Date();
    //                     adService.sameDayOperation(item_type, user_id, ad_id, date);
    //                 } else {
    //                     result = "点击次数超过10次";
    //                     send(ctx, result, HttpResponseStatus.OK);
    //                     return;
    //                 }
    //             } else {
    //                 // 不是同一天
    //                 Date date = new Date();
    //                 adService.otherDayOperation(item_type, user_id, ad_id, date);
    //             }

    //         }
    //     }
    //     result = "处理请求成功";
    //     send(ctx, result, HttpResponseStatus.OK);
    //     return;    
    // }
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause)
            throws Exception {
        // cause.printStackTrace();
       log.error(cause.getMessage());
        ctx.close();
    }
}
