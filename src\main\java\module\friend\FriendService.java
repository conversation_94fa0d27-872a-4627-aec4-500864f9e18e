package module.friend;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.*;
import manager.MySql;
import manager.ReportManager;
import manager.TimerHandler;
import model.FriendInfo;
import model.MessageInfo;
import model.MissionInfo;
import model.RoleInfo;
import module.item.ItemDao;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.FriendData;
import protocol.ProtoData;
import server.SuperServerHandler;
import utils.MyUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by nara on 2018/2/1.
 */
public class FriendService {
    private static Logger log = LoggerFactory.getLogger(ItemDao.class);
    private static FriendService inst = null;

    public static FriendService getInstance() {
        if (inst == null) {
            inst = new FriendService();
        }
        return inst;
    }

    public byte[] sendChat(String uid, byte[] bytes) {
        FriendData.RequestSendChat requestSendChat = null;
        FriendData.ResponseSendChat.Builder builder = null;
        if (uid == null) {
            return null;
        } else {
            try {
                requestSendChat = FriendData.RequestSendChat.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestSendChat == null) {
                return null;
            } else {
                IFriend iFriend = FriendDao.getInstance();
                builder = iFriend.sendChat(uid, requestSendChat.getChat(), requestSendChat.getId());
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] operateFriend(String uid, byte[] bytes) {
        FriendData.RequestOperateFriend requestOperateFriend = null;
        FriendData.ResponseOperateFriend.Builder builder = FriendData.ResponseOperateFriend.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestOperateFriend = FriendData.RequestOperateFriend.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestOperateFriend == null) {
                log.error(uid + ":[operateFriend] error");
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            } else {
                IFriend iFriend = FriendDao.getInstance();
                builder = iFriend.operateFriend(uid, requestOperateFriend.getType(), requestOperateFriend.getId());
            }

        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] operateMessage(String uid, byte[] bytes) {
        FriendData.RequestOperateMessage requestOperateMessage = null;
        FriendData.ResponseOperateMessage.Builder builder = FriendData.ResponseOperateMessage.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestOperateMessage = FriendData.RequestOperateMessage.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestOperateMessage == null) {
                log.error(uid + ":[operateMessage] error");
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            } else {
                IFriend iFriend = FriendDao.getInstance();
                builder = iFriend.operateMessage(uid, requestOperateMessage.getMid(), requestOperateMessage.getType());
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] givePresent(String uid, byte[] bytes) {
        FriendData.RequestGivePresent requestGivePresent = null;
        FriendData.ResponseGivePresent.Builder builder = FriendData.ResponseGivePresent.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestGivePresent = FriendData.RequestGivePresent.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestGivePresent == null) {
                log.error(uid + ":[givePresent] error");
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            } else {
                IFriend iFriend = FriendDao.getInstance();
                builder = iFriend.givePresent(uid, requestGivePresent.getFriendId(), requestGivePresent.getType(), requestGivePresent.getIdList());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] accusation(String uid, byte[] bytes) {
        FriendData.RequestAccusation requestAccusation = null;
        FriendData.ResponseAccusation.Builder builder = FriendData.ResponseAccusation.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestAccusation = FriendData.RequestAccusation.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestAccusation == null) {
                log.error(uid + ":[accusation] error");
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            } else {
                IFriend iFriend = FriendDao.getInstance();
                int val = iFriend.accusation(uid, requestAccusation.getId());
                builder.setErrorId(val);
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] recommendFriend(String uid, byte[] bytes) {
        FriendData.RequestRecommendFriend requestRecommendFriend = null;
        FriendData.ResponseRecommendFriend.Builder builder = FriendData.ResponseRecommendFriend.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestRecommendFriend = FriendData.RequestRecommendFriend.parseFrom(bytes);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            if (requestRecommendFriend == null) {
                log.error(uid + ":[recommendFriend] error");
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
            } else {
                IFriend iFriend = FriendDao.getInstance();
                iFriend.recommendFriend(uid, requestRecommendFriend.getType());
                builder = null;
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    //工具消息
    public byte[] publishMail(byte[] bytes) {
        String string = null;
        try {
            string = new String(bytes, "UTF-8");
        } catch (Exception e) {
            return "analyze error!".getBytes();
        }
        JSONObject jsonObject = JSONObject.fromObject(string);
        // /// System.out.println(jsonObject.toString());
        IFriend iFriend = FriendDao.getInstance();
        String result = iFriend.publishMail(jsonObject);
        return result.getBytes();
    }

    public byte[] publishNotice(byte[] bytes) {
        String string = null;
        try {
            string = new String(bytes, "UTF-8");
        } catch (Exception e) {
            return "analyze error!".getBytes();
        }
        JSONObject jsonObject = JSONObject.fromObject(string);
        // /// System.out.println(jsonObject.toString());
        IFriend iFriend = FriendDao.getInstance();
        String result = iFriend.publishNotice(jsonObject);
        return result.getBytes();
    }


    public byte[] RequestOperateFriend1(String uid, byte[] inBytes) throws InvalidProtocolBufferException {
        FriendData.RequestOperateFriend1 requestOperateFriend1 = FriendData.RequestOperateFriend1.parseFrom(inBytes);
        FriendData.ResponseOperateFriend1.Builder builder = FriendData.ResponseOperateFriend1.newBuilder();
        FriendData.ResponseFriendsApply.Builder responseFriendsApply = FriendData.ResponseFriendsApply.newBuilder();
        FriendData.FriendsApply.Builder friendsApply = FriendData.FriendsApply.newBuilder();
        FriendData.RoleUser.Builder roleUserBuilder = FriendData.RoleUser.newBuilder();
        StringBuilder stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestOperateFriend1.getUserId());
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
        builder.setErrorId(0);
        builder.setType(requestOperateFriend1.getType());
        if (roleEntity == null || roleEntity.getUid().equals(uid)) {
            builder.setErrorId(1);
            return builder.build().toByteArray();
        }
        switch (requestOperateFriend1.getType()) {
            case 1:
                //申请添加好友
                stringBuilder = new StringBuilder("select count(1) from RelativeshipEntity where roleuid1='").append(roleEntity.getUid()).append("' and roleuid2='").append(uid).append("' or roleuid1='").append(uid).append("' and roleuid2='").append(roleEntity.getUid()).append("'");
                long relativeshipint = (long) MySql.queryForOne(stringBuilder.toString());
                if (relativeshipint >= 1) {
                    //已经是好友了
                    builder.setErrorId(1);
                    return builder.build().toByteArray();
                }
                stringBuilder = new StringBuilder("select count(1) from FriendApplicationEntity where roleuid1='").append(roleEntity.getUid()).append("' and roleuid2='").append(uid).append("' or roleuid1='").append(uid).append("' and roleuid2='").append(roleEntity.getUid()).append("'");
                long isApply = (long) MySql.queryForOne(stringBuilder.toString());
                if (isApply >= 1) {
                    //已经申请过了
                    builder.setErrorId(1);
                    return builder.build().toByteArray();
                }
                FriendApplicationEntity friendApplicationEntity = new FriendApplicationEntity();
                friendApplicationEntity.setRoleuid1(uid);
                friendApplicationEntity.setRoleuid2(roleEntity.getUid());
                friendApplicationEntity.setApply(0);
                MySql.insert(friendApplicationEntity);
                //发送协议
                stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                friendsApply.setId(roleEntity.getId());
                if (roleEntity.getSignaTure() == null) {
                    friendsApply.setSignature("");
                } else {
                    friendsApply.setSignature(roleEntity.getSignaTure());
                }
                friendsApply.setHead(roleEntity.getHead());
                friendsApply.setLv(roleEntity.getLv());
                friendsApply.setName(roleEntity.getName());
                friendsApply.setType(0);
                friendsApply.setStatus(1);
                if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                    friendsApply.setStatus(0);
                }
                responseFriendsApply.addFriendsApply(friendsApply);
                stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestOperateFriend1.getUserId());
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                ReportManager.reportInfo(roleEntity.getUid(), ProtoData.SToC.RESPONSEFRIENDSAPPLY_VALUE, responseFriendsApply.build().toByteArray());
                break;
            case 2:
                FriendData.RoleUser.Builder roleUser = FriendData.RoleUser.newBuilder();
                stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                stringBuilder = new StringBuilder("from RelativeshipEntity where roleuid1='").append(roleEntity.getUid()).append("'or roleuid2='").append(roleEntity.getUid()).append("'");
                List<Object> objects = MySql.queryForList(stringBuilder.toString());
                for (int i = 0; i < objects.size(); i++) {
                    RelativeshipEntity relativeshipEntity = (RelativeshipEntity) objects.get(i);
                    if (relativeshipEntity.getRoleuid1().equals(uid)) {
                        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid2()).append("'");
                        roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                        roleUser.setId(roleEntity.getId());
                        if (roleEntity.getSignaTure() == null) {
                            roleUser.setSignature("");
                        } else {
                            roleUser.setSignature(roleEntity.getSignaTure());
                        }
                        roleUser.setName(roleEntity.getName());
                        roleUser.setLv(roleEntity.getLv());
                        roleUser.setHead(roleEntity.getHead());
                        roleUser.setStatus(1);
                        if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                            roleUser.setStatus(0);
                        }
                        builder.addRoleUser(roleUser);
                    } else {
                        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid1()).append("'");
                        roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                        roleUser.setId(roleEntity.getId());
                        if (roleEntity.getSignaTure() == null) {
                            roleUser.setSignature("");
                        } else {
                            roleUser.setSignature(roleEntity.getSignaTure());
                        }
                        roleUser.setName(roleEntity.getName());
                        roleUser.setLv(roleEntity.getLv());
                        roleUser.setHead(roleEntity.getHead());
                        roleUser.setStatus(1);
                        if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                            roleUser.setStatus(0);
                        }
                        builder.addRoleUser(roleUser);
                    }
                }
                //删除好友
                stringBuilder = new StringBuilder("delete from RelativeshipEntity where roleuid1='").append(uid).append("' and roleuid2='").append(roleEntity.getUid()).append("' or roleuid1='").append(roleEntity.getUid()).append("' and roleuid2='").append(uid).append("'");
                MySql.mustUpdateSomes(stringBuilder.toString());
                FriendData.ResponseOperateFriend1.Builder builders = FriendData.ResponseOperateFriend1.newBuilder();
                builders.setErrorId(0);
                builders.setType(requestOperateFriend1.getType());
                stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                roleUser.setId(roleEntity.getId());
                if (roleEntity.getSignaTure() == null) {
                    roleUser.setSignature("");
                } else {
                    roleUser.setSignature(roleEntity.getSignaTure());
                }
                roleUser.setName(roleEntity.getName());
                roleUser.setLv(roleEntity.getLv());
                roleUser.setHead(roleEntity.getHead());
                roleUser.setStatus(1);
                if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                    roleUser.setStatus(0);
                }
                builders.addRoleUser(roleUser);
                stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestOperateFriend1.getUserId());
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                ReportManager.reportInfo(roleEntity.getUid(), ProtoData.SToC.RESPONSEOPERATEFRIEND1_VALUE, builders.build().toByteArray());
                //删除聊天记录
                stringBuilder = new StringBuilder("delete from InformationEntity where uid1='").append(uid).append("' and uid2='").append(roleEntity.getUid()).append("' or uid1='").append(roleEntity.getUid()).append("' and uid2='").append(uid).append("'");
                MySql.updateSomes(stringBuilder.toString());
                break;
            case 3:
                //查找玩家
                roleUserBuilder.setId(roleEntity.getId());
                if (roleEntity.getSignaTure() == null) {
                    roleUserBuilder.setSignature("");
                } else {
                    roleUserBuilder.setSignature(roleEntity.getSignaTure());
                }
                roleUserBuilder.setName(roleEntity.getName());
                roleUserBuilder.setLv(roleEntity.getLv());
                roleUserBuilder.setHead(roleEntity.getHead());
                roleUserBuilder.setStatus(1);
                if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                    roleUserBuilder.setStatus(0);
                }
                builder.addRoleUser(roleUserBuilder);
                break;
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestPlayerRecommend(String uid, byte[] inBytes) {
        FriendData.ResponsePlayerRecommend.Builder builder = FriendData.ResponsePlayerRecommend.newBuilder();
        FriendData.RoleUser.Builder roleUser = FriendData.RoleUser.newBuilder();
        StringBuilder stringBuilder = new StringBuilder("from RoleEntity ORDER BY RAND()");
        List<Object> rolelist = MySql.queryForList(stringBuilder.toString(), 10);
        for (int i = 0; i < rolelist.size(); i++) {
            RoleEntity roleEntity = (RoleEntity) rolelist.get(i);
            roleUser.setId(roleEntity.getId());
            roleUser.setName(roleEntity.getName());
            roleUser.setLv(roleEntity.getLv());
            roleUser.setHead(roleEntity.getHead());
            roleUser.setStatus(1);
            if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                roleUser.setStatus(0);
            }
            builder.addRoleUser(roleUser);
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestFriendsApply(String uid, byte[] inBytes) {
        FriendData.ResponseFriendsApply.Builder builder=FriendData.ResponseFriendsApply.newBuilder();
        FriendData.FriendsApply.Builder friend= FriendData.FriendsApply.newBuilder();
        RoleEntity roleEntity = new RoleEntity();
//        builder.addFriendsApply()
//        System.out.println(1299);
        return inBytes;
    }

    public byte[] RequestOperationFriends(byte[] inBytes, String uid) throws InvalidProtocolBufferException {
        FriendData.RequestOperationFriends requestOperationFriends = FriendData.RequestOperationFriends.parseFrom(inBytes);
        StringBuilder stringBuilder;
        switch (requestOperationFriends.getType()) {
            case 1:
                stringBuilder = new StringBuilder("update FriendApplicationEntity set apply=1 where roleuid2='").append(uid).append("'");
                MySql.updateSomes(stringBuilder.toString());
                break;
            case 2:
                FriendData.ResponseOperateFriend1.Builder responseOperateFriend = FriendData.ResponseOperateFriend1.newBuilder();
                responseOperateFriend.setType(1);
                responseOperateFriend.setErrorId(0);
                FriendData.RoleUser.Builder roleUser = FriendData.RoleUser.newBuilder();
                stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestOperationFriends.getId());
                RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                if (roleEntity == null) {
                    return responseOperateFriend.build().toByteArray();
                }
                roleUser.setId(roleEntity.getId());
                if (roleEntity.getSignaTure() == null) {
                    roleUser.setSignature("");
                } else {
                    roleUser.setSignature(roleEntity.getSignaTure());
                }
                roleUser.setName(roleEntity.getName());
                roleUser.setLv(roleEntity.getLv());
                roleUser.setHead(roleEntity.getHead());
                roleUser.setStatus(1);
                if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                    roleUser.setStatus(0);
                }
                responseOperateFriend.addRoleUser(roleUser);
                //数据库添加好友
                RelativeshipEntity relativeshipEntity = new RelativeshipEntity();
                relativeshipEntity.setValue(0);
                relativeshipEntity.setType(1);
                relativeshipEntity.setRoleuid1(uid);
                relativeshipEntity.setRoleuid2(roleEntity.getUid());
                MySql.insert(relativeshipEntity);
                //删除好友申请
                stringBuilder = new StringBuilder("delete from FriendApplicationEntity where roleuid1='").append(uid).append("' and roleuid2='").append(roleEntity.getUid()).append("' or roleuid1='").append(roleEntity.getUid()).append("' and roleuid2='").append(uid).append("'");
                MySql.updateSomes(stringBuilder.toString());
                //返回好友列表    2201

                stringBuilder = new StringBuilder("from RelativeshipEntity where roleuid1='").append(uid).append("'or roleuid2='").append(uid).append("'");
                List<Object> objects = MySql.queryForList(stringBuilder.toString());

                for (int i = 0; i < objects.size(); i++) {
                    relativeshipEntity = (RelativeshipEntity) objects.get(i);
                    if (relativeshipEntity.getRoleuid1().equals(uid)) {
                        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid2()).append("'");
                        roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                        roleUser.setId(roleEntity.getId());
                        if (roleEntity.getSignaTure() == null) {
                            roleUser.setSignature("");
                        } else {
                            roleUser.setSignature(roleEntity.getSignaTure());
                        }
                        roleUser.setName(roleEntity.getName());
                        roleUser.setLv(roleEntity.getLv());
                        roleUser.setHead(roleEntity.getHead());
                        roleUser.setStatus(1);
                        if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                            roleUser.setStatus(0);
                        }
                        responseOperateFriend.addRoleUser(roleUser);
                    } else {
                        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid1()).append("'");
                        roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                        roleUser.setId(roleEntity.getId());
                        if (roleEntity.getSignaTure() == null) {
                            roleUser.setSignature("");
                        } else {
                            roleUser.setSignature(roleEntity.getSignaTure());
                        }
                        roleUser.setName(roleEntity.getName());
                        roleUser.setLv(roleEntity.getLv());
                        roleUser.setHead(roleEntity.getHead());
                        roleUser.setStatus(1);
                        if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                            roleUser.setStatus(0);
                        }
                        responseOperateFriend.addRoleUser(roleUser);
                    }
                }
                ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEOPERATEFRIEND1_VALUE, responseOperateFriend.build().toByteArray());
                /*——————————————————————————————————————————————————————————————————————————*/
                FriendData.ResponseOperateFriend1.Builder responseOperateFriends = FriendData.ResponseOperateFriend1.newBuilder();
                responseOperateFriends.setType(1);
                responseOperateFriends.setErrorId(0);
                FriendData.RoleUser.Builder roleUsers = FriendData.RoleUser.newBuilder();

                stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                if (roleEntity == null) {
                    return responseOperateFriends.build().toByteArray();
                }
                roleUsers.setId(roleEntity.getId());
                if (roleEntity.getSignaTure() == null) {
                    roleUsers.setSignature("");
                } else {
                    roleUsers.setSignature(roleEntity.getSignaTure());
                }
                roleUsers.setName(roleEntity.getName());
                roleUsers.setLv(roleEntity.getLv());
                roleUsers.setHead(roleEntity.getHead());
                roleUsers.setStatus(1);
                if (SuperServerHandler.getCtxFromUid(roleEntity.getUid()) == null) {
                    roleUsers.setStatus(0);
                }
                responseOperateFriends.addRoleUser(roleUsers);

                stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestOperationFriends.getId());
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                //查出申请添加好友的用户，并刷新齐好友
                stringBuilder = new StringBuilder("from RelativeshipEntity where roleuid1='").append(roleEntity.getUid()).append("' or roleuid2='").append(roleEntity.getUid()).append("'");
                List<Object> objectList = MySql.queryForList(stringBuilder.toString());
                for (int i = 0; i < objectList.size(); i++) {
                    relativeshipEntity = (RelativeshipEntity) objectList.get(i);
                    if (relativeshipEntity.getRoleuid1().equals(roleEntity.getUid())) {
                        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid2()).append("'");
                        RoleEntity roleEntitys = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                        roleUsers.setId(roleEntitys.getId());
                        if (roleEntitys.getSignaTure() == null) {
                            roleUsers.setSignature("");
                        } else {
                            roleUsers.setSignature(roleEntitys.getSignaTure());
                        }
                        roleUsers.setName(roleEntitys.getName());
                        roleUsers.setLv(roleEntitys.getLv());
                        roleUsers.setHead(roleEntitys.getHead());
                        roleUsers.setStatus(1);
                        if (SuperServerHandler.getCtxFromUid(roleEntitys.getUid()) == null) {
                            roleUsers.setStatus(0);
                        }
                        responseOperateFriends.addRoleUser(roleUsers);
                    } else {
                        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(relativeshipEntity.getRoleuid1()).append("'");
                        RoleEntity roleEntitys = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                        roleUsers.setId(roleEntitys.getId());
                        if (roleEntitys.getSignaTure() == null) {
                            roleUsers.setSignature("");
                        } else {
                            roleUsers.setSignature(roleEntitys.getSignaTure());
                        }
                        roleUsers.setName(roleEntitys.getName());
                        roleUsers.setLv(roleEntitys.getLv());
                        roleUsers.setHead(roleEntitys.getHead());
                        roleUsers.setStatus(1);
                        if (SuperServerHandler.getCtxFromUid(roleEntitys.getUid()) == null) {
                            roleUsers.setStatus(0);
                        }
                        responseOperateFriends.addRoleUser(roleUsers);
                    }
                }
                ReportManager.reportInfo(roleEntity.getUid(), ProtoData.SToC.RESPONSEOPERATEFRIEND1_VALUE, responseOperateFriends.build().toByteArray());
                break;
            case 3:
                stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestOperationFriends.getId());
                roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
                stringBuilder = new StringBuilder("delete from FriendApplicationEntity where roleuid1='")
                        .append(uid)
                        .append("' and roleuid2='")
                        .append(roleEntity.getUid())
                        .append("' or roleuid2='")
                        .append(uid)
                        .append("' and roleuid1='")
                        .append(roleEntity.getUid())
                        .append("'");
                MySql.updateSomes(stringBuilder.toString());
                break;
        }
        return inBytes;
    }

    public byte[] RequestSendOutInformation(String uid, byte[] inBytes) throws InvalidProtocolBufferException {
        FriendData.RequestSendOutInformation requestSendOutInformation = FriendData.RequestSendOutInformation.parseFrom(inBytes);
        FriendData.ResponseSendOutInformation.Builder builder = FriendData.ResponseSendOutInformation.newBuilder();
        FriendData.Information.Builder information = FriendData.Information.newBuilder();
        //通过id查出role
        StringBuilder stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestSendOutInformation.getId());
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
        //首先将数据存在消息表内
        InformationEntity informationEntity = new InformationEntity();
        informationEntity.setUid1(uid);
        informationEntity.setUid2(roleEntity.getUid());
        informationEntity.setContent(requestSendOutInformation.getContent());
        Date date = new Date();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        informationEntity.setDate(simpleDateFormat.format(date).trim());
        MySql.insert(informationEntity);
        //查询出聊天记录并返回
        stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
        RoleEntity roleEntity1 = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
//        System.err.println(roleEntity1.toString());
        builder.setUid(roleEntity1.getId());
        information.setInformationId(informationEntity.getId());
        information.setContent(informationEntity.getContent());
        information.setDate(informationEntity.getDate());
//        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSECHATRECORD_VALUE, responseChatRecord.build().toByteArray());
        if (informationEntity.getUid1().equals(uid)) {
            information.setIsReceive(1);
        } else {
            information.setIsReceive(0);
        }
        builder.addInformation(information);
        return builder.build().toByteArray();
    }

    //    上线获取玩家聊天记录
    public byte[] RequestChatRecord(String uid, byte[] inBytes) {

//        RoleInfo roleInfo=new RoleInfo();
//        List<MissionInfo> missionList = new ArrayList<MissionInfo>();
//        /*roleInfo.setMissionList(missionList);*/
//        List<MessageInfo> messageList = new ArrayList<MessageInfo>();
//        IFriend iFriend = FriendDao.getInstance();
//        String mid = MyUtils.setRandom();
//        MessageInfo messageInfo = iFriend.addNewMessage(uid, 10, "", mid, TimerHandler.nowTimeStamp);
//        messageList.add(messageInfo);
//        roleInfo.setMessageList(messageList);
//
//        List<FriendInfo> friendList = new ArrayList<FriendInfo>();
//        roleInfo.setFriendList(friendList);

        StringBuilder stringBuilder = new StringBuilder("from InformationEntity where uid1='").append(uid).append("' group by uid2");
        List<Object> informationList = MySql.queryForList(stringBuilder.toString());
        FriendData.ResponseChatRecord.Builder responseChatRecord = FriendData.ResponseChatRecord.newBuilder();
        FriendData.ResponseChatRecord.Builder responseChatRecord1 = FriendData.ResponseChatRecord.newBuilder();
        FriendData.ResponseSendOutInformation.Builder builder = FriendData.ResponseSendOutInformation.newBuilder();
        FriendData.Information.Builder responseInformation = FriendData.Information.newBuilder();
        FriendData.InformationList.Builder responseInformationList1 = null;
        FriendData.InformationList.Builder responseInformationList2 = null;
        //查出发送的消息
        for (int i = 0; i < informationList.size(); i++) {
            responseInformationList1 = FriendData.InformationList.newBuilder();
            InformationEntity informationEntity = (InformationEntity) informationList.get(i);
            stringBuilder = new StringBuilder("from InformationEntity where uid1='").append(uid).append("' and uid2='").append(informationEntity.getUid2()).append("'");
            List<Object> informationLists = MySql.queryForList(stringBuilder.toString());
            stringBuilder = new StringBuilder("from RoleEntity where uid='").append(informationEntity.getUid2()).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            responseInformationList1.setId(roleEntity.getId());
            int status = 0;
            for (int j = 0; j < informationLists.size(); j++) {
                InformationEntity informationEntitys = (InformationEntity) informationLists.get(j);
                responseInformation.setInformationId(informationEntitys.getId());
                responseInformation.setContent(informationEntitys.getContent());
                responseInformation.setDate(informationEntitys.getDate());
                responseInformation.setIsReceive(0);
                responseInformationList1.addInformation(responseInformation);
                if (informationEntitys.getStatus() == 1) {
                    status = 1;
                }
            }
            responseInformationList1.setStatus(status);
            responseChatRecord1.addInformationList(responseInformationList1);
        }
        StringBuilder stringBuilder1 = new StringBuilder("from InformationEntity where uid2='").append(uid).append("' group by uid1");
        List<Object> informationList1 = MySql.queryForList(stringBuilder1.toString());
        //查出接收的消息
        for (int i = 0; i < informationList1.size(); i++) {
            responseInformationList2 = FriendData.InformationList.newBuilder();
            InformationEntity informationEntity = (InformationEntity) informationList1.get(i);
            stringBuilder = new StringBuilder("from InformationEntity where uid2='").append(uid).append("' and uid1='").append(informationEntity.getUid1()).append("'");
            List<Object> informationLists = MySql.queryForList(stringBuilder.toString());
            stringBuilder = new StringBuilder("from RoleEntity where uid='").append(informationEntity.getUid1()).append("'");
            RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            responseInformationList2.setId(roleEntity.getId());
            int status = 0;
            for (int j = 0; j < informationLists.size(); j++) {
                InformationEntity informationEntitys = (InformationEntity) informationLists.get(j);
                responseInformation.setInformationId(informationEntitys.getId());
                responseInformation.setContent(informationEntitys.getContent());
                responseInformation.setDate(informationEntitys.getDate());
                responseInformation.setIsReceive(1);
                responseInformationList2.addInformation(responseInformation);
                if (informationEntitys.getStatus() == 1) {
                    status = 1;
                }
            }
            responseInformationList2.setStatus(status);
            responseChatRecord1.addInformationList(responseInformationList2);
        }
        Set set = new HashSet();
        Map<Integer, FriendData.InformationList.Builder> map = new HashMap<>();
        for (int i = 0; i < responseChatRecord1.getInformationListList().size(); i++) {
            boolean success = set.add(responseChatRecord1.getInformationListList().get(i).getId());
            if (!success) {
                if (map.get(responseChatRecord1.getInformationListList().get(i).getId()) != null) {
                    responseInformationList1 = FriendData.InformationList.newBuilder();
                    responseInformationList1.setStatus(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getStatus());
                    responseInformationList1.setId(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getId());
                    for (int j = 0; j < map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().size(); j++) {
                        responseInformation.setContent(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getContent());
                        responseInformation.setDate(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getDate());
                        responseInformation.setInformationId(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getInformationId());
                        responseInformation.setIsReceive(map.get(responseChatRecord1.getInformationListList().get(i).getId()).getInformationList().get(j).getIsReceive());
                        responseInformationList1.addInformation(responseInformation);
                    }
                    responseInformationList1.setStatus(responseChatRecord1.getInformationList(i).getStatus());
                    responseInformationList1.setId(responseChatRecord1.getInformationListList().get(i).getId());
                    for (int j = 0; j < responseChatRecord1.getInformationListList().get(i).getInformationList().size(); j++) {
                        responseInformation.setContent(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getContent());
                        responseInformation.setDate(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getDate());
                        responseInformation.setInformationId(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getInformationId());
                        responseInformation.setIsReceive(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getIsReceive());
                        responseInformationList1.addInformation(responseInformation);
                    }
                    map.put(responseChatRecord1.getInformationListList().get(i).getId(), responseInformationList1);
                }
            } else {
                responseInformationList1 = FriendData.InformationList.newBuilder();
                responseInformationList1.setStatus(responseChatRecord1.getInformationList(i).getStatus());
                responseInformationList1.setId(responseChatRecord1.getInformationListList().get(i).getId());
                for (int j = 0; j < responseChatRecord1.getInformationListList().get(i).getInformationList().size(); j++) {
                    responseInformation.setContent(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getContent());
                    responseInformation.setDate(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getDate());
                    responseInformation.setInformationId(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getInformationId());
                    responseInformation.setIsReceive(responseChatRecord1.getInformationListList().get(i).getInformationList().get(j).getIsReceive());
                    responseInformationList1.addInformation(responseInformation);
                }
                map.put(responseChatRecord1.getInformationList(i).getId(), responseInformationList1);
            }
        }
        for (Integer key : map.keySet()) {
            responseInformationList2 = FriendData.InformationList.newBuilder();
            for (int i = 0; i < map.get(key).getInformationList().size(); i++) {
                responseInformationList2.setId(map.get(key).getId());
                responseInformationList2.setStatus(map.get(key).getStatus());
                List<FriendData.Information> information = map.get(key).getInformationList().stream().sorted(Comparator.comparing(FriendData.Information::getInformationId)).collect(Collectors.toList());
                for (int j = 0; j < information.size(); j++) {
                    responseInformation.setIsReceive(information.get(i).getIsReceive());
                    responseInformation.setContent(information.get(i).getContent());
                    responseInformation.setDate(information.get(i).getDate());
                    responseInformation.setInformationId(information.get(i).getInformationId());

                }
                responseInformationList2.addInformation(responseInformation);
            }
            responseChatRecord.addInformationList(responseInformationList2);
        }

        return responseChatRecord.build().toByteArray();
    }

    //修改状态已读未读删除
    public byte[] RequestUpdateType(String uid, byte[] inBytes) throws InvalidProtocolBufferException {
        FriendData.RequestUpdateType requestUpdateType = FriendData.RequestUpdateType.parseFrom(inBytes);
        FriendData.ResponseUpdateType.Builder responseUpdateType = FriendData.ResponseUpdateType.newBuilder();
        StringBuilder stringBuilder = new StringBuilder("from RoleEntity where id=").append(requestUpdateType.getId());
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
        stringBuilder = new StringBuilder("from InformationEntity where uid2='").append(uid).append("' and uid1='").append(roleEntity.getUid()).append("'");
        List<Object> informationList = MySql.queryForList(stringBuilder.toString());

        for (int i = 0; i < informationList.size(); i++) {
            InformationEntity informationEntity = (InformationEntity) informationList.get(i);
            informationEntity.setStatus(requestUpdateType.getStatus());
            MySql.update(informationEntity);
            responseUpdateType.setError(0);
        }
        responseUpdateType.setId(requestUpdateType.getId());
        responseUpdateType.setStatus(requestUpdateType.getStatus());
        return responseUpdateType.build().toByteArray();
    }
}
