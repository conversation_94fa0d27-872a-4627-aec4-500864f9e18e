package module.activity.RechargeRebate;

import entities.RechargeRebateRewardEntity;
import manager.MySql;

public class RechargeRebateRewardDao {
    private static RechargeRebateRewardDao inst = null;
    public static RechargeRebateRewardDao getInstance() {
        if (inst == null) {
            inst = new RechargeRebateRewardDao();
        }
        return inst;
    }


    public RechargeRebateRewardEntity GetPlayerRechargeRebateReward(String uid){
        RechargeRebateRewardEntity entity = null;
        StringBuffer sql = new StringBuffer("from RechargeRebateRewardEntity where uid='").append(uid).append("'");
        entity = (RechargeRebateRewardEntity)MySql.queryForOne(sql.toString());

        if (entity == null){
            entity = new RechargeRebateRewardEntity();

            entity.setUid(uid);
            entity.setReward_id(1);
            MySql.insert(entity);
        }
        return entity;
    }

    public RechargeRebateRewardEntity ChangePlayerRechargeRebateReward(String uid, int nextRewardId) {
        RechargeRebateRewardEntity entity = null;
        StringBuffer sql = new StringBuffer("from RechargeRebateRewardEntity where uid='").append(uid).append("'");
        entity = (RechargeRebateRewardEntity) MySql.queryForOne(sql.toString());

        if (entity == null){
            entity = new RechargeRebateRewardEntity();

            entity.setUid(uid);
            entity.setReward_id(nextRewardId);

            MySql.insert(entity);
        }else {
            entity.setReward_id(nextRewardId);
            MySql.update(entity);
        }
        return entity;
    }

    public void GetReward(int id){

    }
}
