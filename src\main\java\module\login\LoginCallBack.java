package module.login;

import common.SuperConfig;
import entities.RoleEntity;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import module.callback.CallBackManager;
import module.callback.CallBackOrder;
import module.item.IItem;
import module.item.ItemDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ItemData;
import protocol.ProtoData;
import protocol.UserData;
import server.SuperProtocol;
import server.SuperServerHandler;

import java.util.List;

/**
 * Created by nara on 2018/5/9.
 */
public class LoginCallBack extends CallBackManager {
    private static Logger log = LoggerFactory.getLogger(LoginCallBack.class);

    public LoginCallBack(int callBackId) {
        super(callBackId);
    }

    public void execute(Object object) {
        switch (callBackId) {
            case CallBackOrder.SETPLAYERTYPEBACK:
                setPlayerTypeBack(object);
                break;
            default:
                break;
        }
    }

    public void execute(List<Object> objectList) {
        switch (callBackId) {
            case CallBackOrder.JUDGENAMEFORCHANGEBACK:
                judgeNameForChangeBack(objectList);
                break;
            default:
                break;
        }
    }

    private void setPlayerTypeBack(Object object) {
        ChannelHandlerContext ctx = (ChannelHandlerContext) parameterList.get(0);
        RoleEntity roleEntity = (RoleEntity) object;
        String result = "OK";
        if (roleEntity == null) {
            result = "player is not exit!";
        } else {
            int playId = Integer.parseInt(parameterList.get(1).toString());
            int type = Integer.parseInt(parameterList.get(2).toString());
            StringBuffer hql = new StringBuffer("update RoleEntity set type = ").append(type).append(" where id = ").append(playId);
            MySql.updateSomes(hql.toString());
            ILogin iLogin = LoginDao.getInstance();
            String roleId = iLogin.getRoleIdFromUid(roleEntity.getUid());
            if (roleId != null) {
                Redis jedis = Redis.getInstance();
                jedis.hset("role:" + roleEntity.getUid(), "type", type + "");
            }
            if (type == 9) {//封号
                ChannelHandlerContext roleCtx = SuperServerHandler.getCtxFromUid(roleEntity.getUid());
                if (roleCtx != null) {
                    ReportManager.reportForceToOffline(roleCtx);
                }
            }
        }

        byte[] bytes = result.getBytes();
        SuperProtocol response = new SuperProtocol(8891, bytes.length, bytes);
        ctx.writeAndFlush(response);
    }

    private void judgeNameForChangeBack(List<Object> objectList) {
        String uid = parameterList.get(0).toString();
        String name = parameterList.get(1).toString();
        UserData.ResponseSetInfo.Builder builder = UserData.ResponseSetInfo.newBuilder();
        builder.setErrorId(0);
        if (objectList.size() > 0) {
            builder.setErrorId(ProtoData.ErrorCode.NAMECREATED_VALUE);
            log.error(uid + ":[setRoleBaseInfo] error 1");
        } else {
            IItem iItem = ItemDao.getInstance();
            int cost = SuperConfig.getChangeName();
            double total = iItem.updateItemInfo(uid, 2, (-cost));
            if (total < 0) {
                log.error(uid + ":[setRoleBaseInfo] error 2 >>>total:" + total);
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
            } else {
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(2);
                itemBu.setNum(total);
                builder.setItem(itemBu);
                ReportManager.reportUpdateFriendName(uid, name);

                Redis jedis = Redis.getInstance();
                jedis.hset("role:" + uid, "name", name);
                StringBuffer hql = new StringBuffer("update RoleEntity set name = '").append(name).append("'").append(" where uid = '").append(uid).append("'");
                MySql.updateSomes(hql.toString());
            }
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSESETINFO_VALUE, builder.build().toByteArray());
    }
}
