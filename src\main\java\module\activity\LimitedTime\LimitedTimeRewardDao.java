package module.activity.LimitedTime;

import entities.LimitedTimeRewardEntity;
import io.netty.util.CharsetUtil;
import manager.MySql;
import module.mail.mail_tool.MailGetRewards;
import protocol.LimitedTimeRewardData;
import protocol.MailData;
import table.limitedtime_reward.LimitedTimeRewardLine;
import table.limitedtime_reward.LimitedTimeRewardTable;

import java.util.ArrayList;
import java.util.List;

public class LimitedTimeRewardDao {
    private static LimitedTimeRewardDao inst = null;
    public static LimitedTimeRewardDao getInstance() {
        if (inst == null) {
            inst = new LimitedTimeRewardDao();
        }
        return inst;
    }

    private void InitData(LimitedTimeRewardEntity data, String uid){
        data.setCharge_reward(false);
        data.setExp(0);
        data.setOrdinary_reward_id(1);
        data.setCharge_reward_id(1);

        data.setUid(uid);
        MySql.insert(data);
    }

    private void UpdateData(LimitedTimeRewardEntity data,
                            LimitedTimeRewardData.ResponseLimitedTimeReward updateData){
        data.setCharge_reward(updateData.getChargeReward());
        data.setExp(updateData.getExp());
        data.setOrdinary_reward_id(updateData.getOrdinaryRewardId());
        data.setCharge_reward_id(updateData.getChargeRewardId());

        MySql.update(data);
    }

    public LimitedTimeRewardEntity Get(String uid){
        LimitedTimeRewardEntity data = null;
        StringBuffer sql = new StringBuffer("from LimitedTimeRewardEntity where uid='").append(uid).append("'");
        data = (LimitedTimeRewardEntity) MySql.queryForOne(sql.toString());

        if (data == null){
            data = new LimitedTimeRewardEntity();

            // 初始化数据
            InitData(data, uid);
        }
        return data;
    }

    public LimitedTimeRewardEntity Update(String uid, LimitedTimeRewardData.ResponseLimitedTimeReward updateData) {
        LimitedTimeRewardEntity data = null;
        StringBuffer sql = new StringBuffer("from LimitedTimeRewardEntity where uid='").append(uid).append("'");
        data = (LimitedTimeRewardEntity) MySql.queryForOne(sql.toString());

        if (data == null){
            data = new LimitedTimeRewardEntity();
            // 初始化数据
            InitData(data, uid);
        }else {
            UpdateData(data, updateData);
        }
        return data;
    }

    public void Delete() {
        StringBuffer stringBuffer = new StringBuffer("Delete from LimitedTimeRewardEntity");
        MySql.updateSomes(stringBuffer.toString());
    }


}
