<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.HeadBallEntity" table="headball" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="is_first" column="is_first"/>
        <property name="battle_num" column="battle_num"/>
        <property name="jump_num" column="jump_num"/>
        <property name="jump_missionid" column="jump_missionid"/>
        <property name="user_id" column="user_id"/>
        <property name="is_threestar" column="is_threestar"/>
    </class>
</hibernate-mapping>