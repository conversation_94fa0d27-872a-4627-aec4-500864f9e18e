package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "goldrecord", schema = "", catalog = "super_star_fruit")
public class GoldRecordEntity {
    private int id;
    private String uid;
    private String record;


    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }


    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "record")
    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }

    @Override
    public String toString() {
        return "GoldRecordEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", record='" + record + '\'' +
                '}';
    }
}
