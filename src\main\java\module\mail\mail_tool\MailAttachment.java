package module.mail.mail_tool;

import protocol.EquipAData;
import protocol.ItemData;
import protocol.MailData;
import table.equitment_attribute.EquipmentAttributeTable;

public class MailAttachment {
    private static MailAttachment inst = null;
    private MailAttachment() {
    }
    public static MailAttachment getInstance() {
        if (inst == null) {
            inst = new MailAttachment();
        }
        return inst;
    }

    /*
    type
    1   #    id:1,num:100  !   id:2,num:100    !   id:2,num:100
     */
    public MailData.Attachment.Builder GetAttachment(String itemStr) {
        MailData.Attachment.Builder attachment = null;
        try {
            String[] data = itemStr.split("[#]", 2);
            switch (data[0]){
                case "1":
                    System.out.println("添加一个Item");
                    if (data.length == 1){
                        attachment = GetItem(null);
                    }else {
                        attachment = GetItem(data[1]);
                    }
                    break;
                case "2":
                    System.out.println("添加一个装备");
                    if (data.length == 1){
                        attachment = GetEquip(null);
                    }else {
                        attachment = GetEquip(data[1]);
                    }
                    break;
                case "3":
                    System.out.println("添加一个宠物");
                    if (data.length == 1){
                        attachment = GetPet(null);
                    }else {
                        attachment = GetPet(data[1]);
                    }
                    break;
            }
        }catch (Exception e){
            System.err.println("发送邮件错误！！！");
            e.printStackTrace();
        }
        return attachment;
    }

    private MailData.Attachment.Builder GetItem(String datum) {
        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
        attachment.setType(1);


        for (String field :
                datum.split("!")) {

            ItemData.Item.Builder item = ItemData.Item.newBuilder();
            for (String data:
                    field.split(",")){
                String[] s = data.split(":");
                switch (s[0]){
                    case "id":
                        item.setId(Integer.parseInt(s[1]));
                        break;
                    case "num":
                        item.setNum(Float.parseFloat(s[1]));
                        break;
                }
            }
            attachment.addItem(item);
        }



        return attachment;
    }

    private MailData.Attachment.Builder GetEquip(String datum) {
        String[] data = datum.split("#");
        System.out.println("衣服选项：" + data[0]);
        System.out.println("衣服数据：" + data[1]);
        switch (data[0]){
            case "1":
                return TypeCreatEquip(data[1]);
            case "2":
                return DefaultCreatEquip(data[1]);
        }
        return MailData.Attachment.newBuilder();
    }

    /*
       type
       2   #  1   #   衣服,20  !   鞋子,5   !   id:2,num:100
    */
    private MailData.Attachment.Builder TypeCreatEquip(String datum) {
        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
        attachment.setType(2);


        for (String field :
                datum.split("!")) {

            EquipAData.EquipA.Builder item = null;

            String data[] = field.split(",");
            item =
                    EquipmentAttributeTable.getInstance().
                            GenerateEquip(data[0], Integer.parseInt(data[1]));

            attachment.addEquip(item);
        }

        return attachment;
    }

    private MailData.Attachment.Builder DefaultCreatEquip(String datum) {
        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
        attachment.setType(2);

        for (String field :
                datum.split("!")) {

            EquipAData.EquipA.Builder item = EquipAData.EquipA.newBuilder();
            for (String data:
                    field.split(",")){
                String[] s = data.split(":");
                switch (s[0]){
                    case "Eid":
                        item.setEid(Integer.parseInt(s[1]));
                        break;
                    case "ad":
                        item.setAd(Integer.parseInt(s[1]));
                        break;
                    case "ap":
                        item.setAp(Integer.parseInt(s[1]));
                        break;
                    case "arm":
                        item.setArm(Integer.parseInt(s[1]));
                        break;
                    case "mdf":
                        item.setMdf(Integer.parseInt(s[1]));
                        break;
                    case "speed":
                        item.setSpeed(Integer.parseInt(s[1]));
                        break;
                    case "hp":
                        item.setHp(Integer.parseInt(s[1]));
                        break;
                    case "ead":
                        item.setEad(Integer.parseInt(s[1]));
                        break;
                    case "emdf":
                        item.setEmdf(Integer.parseInt(s[1]));
                        break;
                    case "esp":
                        item.setEsp(Integer.parseInt(s[1]));
                        break;
                    case "ehp":
                        item.setEhp(Integer.parseInt(s[1]));
                        break;
                    case "eap":
                        item.setEap(Integer.parseInt(s[1]));
                        break;
                    case "earm":
                        item.setEarm(Integer.parseInt(s[1]));
                        break;
                }
            }
            attachment.addEquip(item);
        }

        return attachment;
    }

    private MailData.Attachment.Builder GetPet(String datum) {
        System.out.println("出来");
        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
        attachment.setType(3);

        for (String field :
                datum.split("!")) {

            MailData.DefaultPet.Builder item = MailData.DefaultPet.newBuilder();
            for (String data:
                    field.split(",")){
                String[] s = data.split(":");
                switch (s[0]){
                    case "petId":
                        item.setPetId(Integer.parseInt(s[1]));
                        break;
                    case "isEgg":
                        item.setIsEgg(Integer.parseInt(s[1]));
                        break;
                }
            }
            attachment.addDefaultPet(item);
        }

        return attachment;
    }

}
