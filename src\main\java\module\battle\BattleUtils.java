package module.battle;


import manager.Redis;
import model.ItemInfo;
import utils.MyUtils;

import java.util.*;

public class BattleUtils {
    private static BattleUtils inst = null;

    public static BattleUtils getInstance() {
        if (inst == null) {
            inst = new BattleUtils();
        }
        return inst;
    }

    public static final String BATTLE_ACCOUNTS_CONFIG_FILENAME = "mapconfig";//怪物副本结算配置的key
    public static final String BATTLE_MONSTER_CONFIG_FILENAME = "monsterconfig";
    public static Map<Integer, Map<String, String>> battleAccountsMap;
    public static Map<Integer, Map<String, String>> monsterMap;

    static {
        //battleAccountsMap
        Redis jedis = Redis.getInstance();
        battleAccountsMap = new HashMap<Integer, Map<String, String>>();
        Iterator<String> iterator = Redis.scan(BATTLE_ACCOUNTS_CONFIG_FILENAME + "*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            int id = Integer.parseInt(key.split(":")[1]);
            Map<String, String> configMap = jedis.hgetAll(key);
            battleAccountsMap.put(id, configMap);
        }
        monsterMap = new HashMap<Integer, Map<String, String>>();
        Iterator<String> iterator1 = Redis.scan(BATTLE_MONSTER_CONFIG_FILENAME + "*").iterator();
        while (iterator1.hasNext()) {
            String key = iterator1.next();
            // int id=Integer.parseInt(key.split(":")[1]);
            Map<String, String> configMap = jedis.hgetAll(key);
            int id = Integer.parseInt(configMap.get("petid"));
            monsterMap.put(id, configMap);
        }
    }

    public static Map<String, String> getMapConfig(int mapId) throws Exception {
        Map<String, String> map = battleAccountsMap.get(mapId);
        if (map == null) {
            throw new Exception();
        }
        return map;
    }

    public static Map<String, String> getMonsterConfig(int mapId) throws Exception {
        Map<String, String> map = monsterMap.get(mapId);
        if (map == null) {
            ///    /// System.err.println(mapId+"~~~~1214~~~");
            throw new Exception();
        }
        ///  /// System.err.println(map+"~~~~~~~~~");
        return map;
    }

    public static List<ItemInfo> getMonsterAward(int monsterId) {
        List<ItemInfo> itemList = new ArrayList<ItemInfo>();
        try {
            Map<String, String> monsterConfig = getMonsterConfig((monsterId));
            ItemInfo goldInfo = new ItemInfo();
            int gold = Integer.parseInt(monsterConfig.get("havegold"));
            goldInfo.setId(1);
            goldInfo.setNum(gold);
            itemList.add(goldInfo);
            int exp = Integer.parseInt(monsterConfig.get("haveexp"));
            ItemInfo expInfo = new ItemInfo();
            expInfo.setId(4);
            expInfo.setNum(exp);
            itemList.add(expInfo);
            String extraAwardString = monsterConfig.get("item");
            String[] itemConfig = extraAwardString.split("\\|");
            String getItemProbability = monsterConfig.get("item_probability");
            String[] probabilityArray = getItemProbability.split("\\|");
            for (int i = 0; i < probabilityArray.length; i++) {
                int probability = Integer.parseInt(probabilityArray[i]);
                if (MyUtils.isSuccessRandom(probability)) {
                    String awardInfo = itemConfig[i];
                    ItemInfo itemInfo = new ItemInfo();
                    int id = Integer.parseInt(awardInfo.split(",")[0]);
                    //  /// System.err.println(id+"~~~"+monsterId);
                    itemInfo.setId(id);
                    itemInfo.setNum(Integer.parseInt(awardInfo.split(",")[1]));
                    itemList.add(itemInfo);
                    // /// System.err.println(itemInfo);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return itemList;

    }


}
