package entities;

import org.hibernate.annotations.GenericGenerator;
import protocol.UserData;

import javax.persistence.*;
import java.util.Objects;

/**
 * Created by nara on 2018/8/13.
 */
@Entity
@Table(name = "user", schema = "", catalog = "super_star_fruit")
public class UserEntity {
    private int id;
    private int version;
    private int serverid;
    private String userid;
    private String openid;
    private String pwd;
    private String roleuid;
    private Integer type;
    private String phone;
    private long registertime;
    private long prelogin;
    private long lastlogin;
    private int weekflag;
    private int visitor;
    private String name;
    private String idcard;
    private double gametime;
    private int authentication;
    private String idfa;
    private String uuid;

    @Id
    @Column(name = "id")
    @GenericGenerator(name = "autoId", strategy = "native")
    @GeneratedValue(generator = "autoId")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "serverid")
    public int getServerid() {
        return serverid;
    }

    public void setServerid(int serverid) {
        this.serverid = serverid;
    }

    @Basic
    @Column(name = "userid")
    public String  getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    @Basic
    @Column(name = "openid")
    public String getOpenid() {
        return openid;
    }

    public void setOpenid(String openid) {
        this.openid = openid;
    }

    @Basic
    @Column(name = "pwd")
    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    @Basic
    @Column(name = "roleuid")
    public String getRoleuid() {
        return roleuid;
    }

    public void setRoleuid(String roleuid) {
        this.roleuid = roleuid;
    }

    @Basic
    @Column(name = "type")
    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Basic
    @Column(name = "phone")
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Basic
    @Column(name = "registertime")
    public long getRegistertime() {
        return registertime;
    }

    public void setRegistertime(long registertime) {
        this.registertime = registertime;
    }

    @Basic
    @Column(name = "prelogin")
    public long getPrelogin() {
        return prelogin;
    }

    public void setPrelogin(long prelogin) {
        this.prelogin = prelogin;
    }

    @Basic
    @Column(name = "lastlogin")
    public long getLastlogin() {
        return lastlogin;
    }

    public void setLastlogin(long lastlogin) {
        this.lastlogin = lastlogin;
    }

    @Basic
    @Column(name = "weekflag")
    public int getWeekflag() {
        return weekflag;
    }

    public void setWeekflag(int weekflag) {
        this.weekflag = weekflag;
    }

    @Basic
    @Column(name = "visitor")
    public int getVisitor() {
        return visitor;
    }

    public void setVisitor(int visitor) {
        this.visitor = visitor;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Basic
    @Column(name = "idcard")
    public String getIdcard() {
        return idcard;
    }

    public void setIdcard(String idcard) {
        this.idcard = idcard;
    }

    @Basic
    @Column(name = "gametime")
    public double getGametime() {
        return gametime;
    }

    public void setGametime(double gametime) {
        this.gametime = gametime;
    }

    @Basic
    @Column(name = "authentication")
    public int getAuthentication() {
        return authentication;
    }

    public void setAuthentication(int authentication) {
        this.authentication = authentication;
    }

    @Basic
    @Column(name = "idfa")
    public String getIdfa() {
        return idfa;
    }

    public void setIdfa(String idfa) {
        this.idfa = idfa;
    }

    @Basic
    @Column(name = "uuid")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserEntity that = (UserEntity) o;
        return getId() == that.getId() &&
                getVersion() == that.getVersion() &&
                getServerid() == that.getServerid() &&
                getRegistertime() == that.getRegistertime() &&
                getPrelogin() == that.getPrelogin() &&
                getLastlogin() == that.getLastlogin() &&
                getWeekflag() == that.getWeekflag() &&
                Objects.equals(getUserid(), that.getUserid()) &&
                Objects.equals(getOpenid(), that.getOpenid()) &&
                Objects.equals(getPwd(), that.getPwd()) &&
                Objects.equals(getRoleuid(), that.getRoleuid()) &&
                Objects.equals(getType(), that.getType()) &&
                Objects.equals(getPhone(), that.getPhone());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getVersion(), getServerid(), getUserid(), getOpenid(), getPwd(), getRoleuid(), getType(), getPhone(), getRegistertime(), getPrelogin(), getLastlogin(), getWeekflag());
    }

    @Override
    public String toString() {
        return "UserEntity{" +
                "id=" + id +
                ", version=" + version +
                ", serverid=" + serverid +
                ", userid='" + userid + '\'' +
                ", openid='" + openid + '\'' +
                ", pwd='" + pwd + '\'' +
                ", roleuid='" + roleuid + '\'' +
                ", type=" + type +
                ", phone='" + phone + '\'' +
                ", registertime=" + registertime +
                ", prelogin=" + prelogin +
                ", lastlogin=" + lastlogin +
                ", weekflag=" + weekflag +
                '}';
    }
}
