package model;

import io.netty.channel.ChannelHandlerContext;

/**
 * Created by nara on 2018/4/12.
 */
public class StationInfo {
    private PointDoubleInfo point;
    private ChannelHandlerContext ctx;
    private String uid;

    public StationInfo() {
        point = new PointDoubleInfo();
        point.setX(0);
        point.setY(0);
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }

    public PointDoubleInfo getPoint() {
        return point;
    }

    public void setPoint(PointDoubleInfo point) {
        this.point = point;
    }
}
