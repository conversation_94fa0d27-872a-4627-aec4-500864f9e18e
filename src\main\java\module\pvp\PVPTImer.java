package module.pvp;

import entities.PVPBaseDataEntity;
import manager.ReportManager;
import protocol.PVPData;
import protocol.ProtoData;
import server.SuperServerHandler;

public class PVPTImer {
    private static PVPTImer inst = null;
    public static PVPTImer getInstance() {
        if (inst == null) {
            inst = new PVPTImer();
        }
        return inst;
    }
    // 每秒执行
    public void PerSecondRun() {
        CheckTime();
    }
    // 多长时间重置限时任务
    private final long Time = 604800;
    private long CurTime = 0;
    // 温度层刷新
    private void CheckTime(){
        CurTime++;
        if (CurTime < Time){
            return;
        }
        CurTime = 0;
        PVPRank.getInstance().Settlement();

        for (String uid :
                SuperServerHandler.linkMap.values()) {
            // 发送PVP基本信息
            PVPBaseDataEntity pvpPetsEntity = PVPRank.getInstance().GetPVPPlayerBaseData(uid);
            PVPData.ResponsePVPBaseData.Builder builder = PVPData.ResponsePVPBaseData.newBuilder();
            builder.setRank(pvpPetsEntity.getRank());
            builder.setScore(pvpPetsEntity.getScore());
            builder.setFail(pvpPetsEntity.getFail());
            builder.setVictory(pvpPetsEntity.getVictory());
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPVPBaseData_VALUE, builder.build().toByteArray());

            // 发送PVP时间
            PVPData.ResponsePVPBattleRemainTime.Builder builder2 = PVPData.ResponsePVPBattleRemainTime.newBuilder();
            builder2.setTimeSecond(getCurTime());
            ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPVPBATTLEREMAINTIME_VALUE, builder2.build().toByteArray());
        }
    }

    public long getCurTime() {
        return CurTime;
    }
}
