package module.robot;

import common.SuperConfig;
import entities.MessageEntity;
import entities.RoleEntity;
import manager.MySql;
import manager.Redis;

import java.util.*;

public class ranName {

    private static int num1=0;
    private static int num2=0;
    private static String name="";
    private static Redis jedis = Redis.getInstance();

    //除去多余的元素
    public static  List<String> removeMore(List<String> mores){
        List listTemp=new ArrayList();
        for(int i=0;i<mores.size();i++){
            if (!listTemp.contains(mores.get(i))) {
                listTemp.add(mores.get(i));
            }
        }
        return listTemp;
    }



    //发布通告
    public static void fabuNews(){
        StringBuffer sql=new StringBuffer(" FROM MessageEntity where type = 20");
        MessageEntity mess=(MessageEntity)MySql.queryForOne(sql.toString());
        String content=jedis.hget("common:1","region3");
        if(mess==null){
            MessageEntity newmess=new MessageEntity();
            newmess.setVersion(0);
            newmess.setMid("admin");
            newmess.setType(20);
            newmess.setUid("user");
            newmess.setContent(content);
            newmess.setTimestamp(time());
            newmess.setStatus(0);
            MySql.mustInsert(newmess);
        }else{
            StringBuffer hql=new StringBuffer("update MessageEntity set content = '").append(content).append("',timestamp='").append(time()).append("' where type =20");
            MySql.mustUpdateSomes(hql.toString());
        }
    }

    //今天是星期几
    public  static int week(){
        Date today=new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(today);
        int w= cal.get(Calendar.DAY_OF_WEEK);
        if(w==1) w=8;
        return (w-1);
    }

    public static String name(){
        Set<String> ranName = jedis.keys(SuperConfig.REDIS_EXCEL_RANNAME+"*");
        num1=ranNum(1,ranName.size());
        num2=ranNum(1,ranName.size());
        String a=Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_RANNAME, num1 + "", "name_0");
        String b=Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_RANNAME, num2 + "", "name_1");
        name=a+b;
        StringBuffer buff=new StringBuffer("from RoleEntity where name = '");
        buff.append(name).append("'");
        List<Object> list = MySql.queryForList(buff.toString());
        if(list.size()!=0){
            name();
        }
        return name;
    }


    public static int  ranNum(int min,int max){
         return (int)(min+Math.random()*(max-min+min));
       /* int bound = max-min+1;
        if(bound < 1 )bound = 1;
        return new Random().nextInt(bound)+min;*/

    }

    public static  int ranGift(List<String> list){
        if(list.size() == 0) return 0;
        Integer[] shuzu=new Integer[list.size()];
        Integer num=0;
        for(int i=0;i<list.size();i++){
            num+=Integer.valueOf(list.get(i));
            shuzu[i]=num;
        }
        int ran=ranNum(1,shuzu[shuzu.length-1]);
        for(int i=0;i<list.size();i++){
           if(ran<=shuzu[i]){
               return i;
           }
        }
        return 0;
    }

    public static  int ranGift(String[] list){
        Integer[] shuzu=new Integer[list.length];
        Integer num=0;
        for(int i=0;i<list.length;i++){
            num+=Integer.valueOf(list[i]);
            shuzu[i]=num;
        }
        int ran=ranNum(1,shuzu[shuzu.length-1]);
        for(int i=0;i<list.length;i++){
            if(ran<=shuzu[i]){
                return i;
            }
        }
        return 0;
    }

    //随机获得集合中元素
    public static String  ranList(List<String> list){
        ///// System.out.println(list);
        int n=ranNum(0,list.size()-1);
        return list.get(n);
    }

    //获得时间戳
    public static String  time(){
        Long now=System.currentTimeMillis();
        return now+"";
    }

    //添加经验
    public static void addExp(String uid,double exp){
                /// System.out.println("加经验");
                StringBuffer hql=new StringBuffer("update RoleEntity set exp=exp+").append(exp).
                        append(" where uid='").append(uid).append("' ");
                /// System.out.println(hql);
                MySql.mustUpdateSomes(hql.toString());
    }

    //升级
    public static void addlv(String uid){

        StringBuffer sql=new StringBuffer(" FROM RoleEntity where uid = '").append(uid).append("'");
        RoleEntity rloes=(RoleEntity)MySql.queryForOne(sql.toString());
        Set<String> exps = jedis.keys(SuperConfig.REDIS_EXCEL_EXP+"*");
        int rows = exps.size();
        Map<Integer,Integer> map=new HashMap<Integer, Integer>();
        for(int row=1;row<=rows;row++){
            int a=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_EXP, row + "", "ID"));
            int b=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_EXP, row + "", "exp"));
            map.put(a,b);
        }
        Integer expss=map.get(rloes.getLv());
        if(expss<=rloes.getExp() && rloes.getExp()!=0 ){
            StringBuffer hql=new StringBuffer("update RoleEntity set lv=lv+1,exp=").append(rloes.getExp()-expss).append(" where uid='").append(uid).append("' ");
            ///// System.out.println(hql);
            MySql.mustUpdateSomes(hql.toString());
            /// System.out.println("升级");
        }else{
            /// System.out.println("经验不够,无法升级!");
        }
    }


    public static int getExp(int num,int type){
        int exp=0;
        if(type==0){//跳球
            Set<String> misss = jedis.keys(SuperConfig.REDIS_EXCEL_MISSION+"*");
            int rows = misss.size();
            Map<Integer,Integer> map=new HashMap<Integer, Integer>();
            for(int row=1;row<=rows;row++){
                int miss=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_MISSION, row + "", "ID"));
                int a=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_MISSION, row + "", "exp"));
               if(miss==num){
                    exp=a;
                }
            }
        }else if(type==1){
            Set<String> jumps = jedis.keys(SuperConfig.REDIS_EXCEL_JUMPMISSION+"*");
            int rows = jumps.size();
            Map<Integer,Integer> map=new HashMap<Integer, Integer>();
            for(int row=1;row<=rows;row++){
                StringBuffer a=new StringBuffer("");
                switch (ranNum(1,3)){
                    case 1:
                        a.append(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_JUMPMISSION, row + "", "stars_1"));
                        break;
                    case 2:
                        a.append(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_JUMPMISSION, row + "", "stars_2"));
                        break;
                    case 3:
                        a.append(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_JUMPMISSION, row + "", "stars_3"));
                        break;
                }
                int miss=Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_JUMPMISSION, row + "", "ID"));
               if(miss==num){
                   switch (a.length()){
                       case 16:
                           ///// System.out.println("16");
                           exp=Integer.parseInt(a.substring(a.length()-5,a.length()-2));
                           break;
                       case 23:
                           ///// System.out.println("23");
                           exp=Integer.parseInt(a.substring(a.length()-5,a.length()-2));
                           break;
                       case 32:
                           ///// System.out.println("32");
                           exp=Integer.parseInt(a.substring(a.length()-5,a.length()-2));
                           break;
                   }
                }
            }
        }
        return exp;
    }


}
