package module.Dispatch;

import Json.ExperienceJson;
import common.DispatchConfig;
import common.SuperConfig;
import entities.PetEntity;
import model.CommonInfo;
import protocol.ExploreData;
import protocol.UserData;
import utils.MyUtils;

import java.util.*;

/*
  数据使用频率低，不太重要的直接用nosql做持久化
  hash:  key--prekey+separator+uid;
      业务filed采用业务的key
      数据维护信息flied采用_前缀减少与业务key冲突
*/
public class Dispatch {
    public static final String preKey = "playerDispatch";
    public static final String separator = ":";
    public static final String preFlied = "_";
    public static final String FLUSHNUM = "_flushNum";

    public static String getKey(String uid) {
        return preKey + separator + uid;
    }

    public static final int defaultExperienceNums = 10;
    public static List<Integer> experiencePool = new ArrayList<Integer>();

    static {
        try {
            Map<Integer, Object> objectMap = SuperConfig.getCongifMap(SuperConfig.dispatchConfig);
            for (Map.Entry<Integer, Object> entry : objectMap.entrySet()) {
                int key = entry.getKey();
                experiencePool.add(key);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        ///     /// System.err.println(experiencePool.toString());

    }

    //数据转化为javabean
    public static DispatchEntity datatoObject(Map<String, String> data) {
        DispatchEntity dispatchEntity = new DispatchEntity();
        try {
            int flushNum = Integer.parseInt(data.get(FLUSHNUM));
            dispatchEntity.setFlushNums(flushNum);
        } catch (Exception e) {
            e.printStackTrace();
        }
        List<ExperienceJson> list = new ArrayList<ExperienceJson>();
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String key = entry.getKey();
            if (key.startsWith(preFlied)) {
                continue;
            } else {
                String value = entry.getValue();
                try {
                    ExperienceJson bean = (ExperienceJson) MyUtils.jsonToBean(value, ExperienceJson.class);
                    if (bean != null) {
                        list.add(bean);
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
                dispatchEntity.setExperienceJsons(list);
            }
        }
        return dispatchEntity;
    }

    public static ExploreData.Experience objectToPB(ExperienceJson experienceJson) {
        ExploreData.Experience.Builder builder = ExploreData.Experience.newBuilder();
        builder.setCountdown(MyUtils.getCountdown(experienceJson.getFinishTime()));
        int status = experienceJson.getStatus();
        builder.setStatus(status);
        if (status == 2) {//进行中
            List<Integer> petIds = experienceJson.getPetId();
            builder.addAllPetId(petIds);
        }
        builder.setKey(experienceJson.getKey());
        List<DispatchRule> dispatchRuleList = experienceJson.getRules();
        for (DispatchRule rule : dispatchRuleList) {
            ExploreData.Rule.Builder ruleBu = ExploreData.Rule.newBuilder();
            ruleBu.setContent(rule.getContent());
            ruleBu.setType(rule.getType());
            builder.addRules(ruleBu);
        }
        return builder.build();

    }


    public static ExperienceJson initExperience(int id) {
        DispatchConfig dispatchConfig = getExperienConfig(id);
        ExperienceJson experienceJson = new ExperienceJson();
        experienceJson.setKey(id);
        int needTime = dispatchConfig.getFinishTime();
        experienceJson.setFinishTime(System.currentTimeMillis() + needTime * 1000);
        experienceJson.setStatus(1);//1初始
        //  ArrayList<DispatchRule> rulesList=new ArrayList<DispatchRule>();
        List<CommonInfo> ruleConfig = dispatchConfig.getRules();
        ArrayList<DispatchRule> ruleArrayList = new ArrayList<DispatchRule>();
        for (CommonInfo commonInfo : ruleConfig) {
            DispatchRule rule = new DispatchRule();
            rule.setType(commonInfo.getKey());
            rule.setContent(commonInfo.getValue() + "");
            ruleArrayList.add(rule);
            // /// System.err.println("rule"+rule);
        }
        experienceJson.setRules(ruleArrayList);

        return experienceJson;
    }

    public static DispatchEntity init() {
        DispatchEntity dispatchEntity = new DispatchEntity();
        dispatchEntity.setFlushNums(10);
        ArrayList<ExperienceJson> experienceJsonArrayList = new ArrayList<ExperienceJson>();
        int nums = experiencePool.size();
        int[] pool = new int[nums];
        for (int i = 0; i < nums; i++) {
            pool[i] = experiencePool.get(i);
        }
        int experienceNums = 10;
        int temp = -1;
        // int flag=0;
        for (int i = 0; i < experienceNums; i++) {
            int index = (int) (Math.random() * (nums - i));
            temp = pool[index];
            ExperienceJson experienceJson = initExperience(temp);
            experienceJsonArrayList.add(experienceJson);
            pool[index] = pool[nums - i - 1];
            pool[nums - i - 1] = temp;
        }
        experienceJsonArrayList.add(initExperience(1));
        dispatchEntity.setExperienceJsons(experienceJsonArrayList);
        return dispatchEntity;
    }


    public static Map<String, String> objectToMap(DispatchEntity dispatchEntity) {
        Map<String, String> map = new HashMap<String, String>();
        int flushNums = dispatchEntity.getFlushNums();
        map.put(FLUSHNUM, flushNums + "");
        List<ExperienceJson> experienceJsonList = dispatchEntity.getExperienceJsons();
        for (ExperienceJson experienceJson : experienceJsonList) {
            String filed = experienceJson.getKey() + "";
            map.put(filed, MyUtils.objectToJson(experienceJson));
        }
        return map;
    }

    private static DispatchConfig getExperienConfig(int key) {
        DispatchConfig dispatchConfig = null;
        try {
            dispatchConfig = (DispatchConfig) SuperConfig.getCongifObject(SuperConfig.dispatchConfig, key);
        } catch (Exception e) {
            e.printStackTrace();
            return dispatchConfig;
        }
        return dispatchConfig;
    }

    public static boolean judgeRule(PetEntity pet, DispatchRule rule) {
        int type = rule.getType();
        boolean isAdhere = false;
        int goal = Integer.parseInt(rule.getContent());
        switch (type) {
            case 1:
                isAdhere = pet.getProfession() == goal;
                break;
            case 2:
                isAdhere = pet.getMainAttribute() == goal;
                break;
            case 3:
                isAdhere = pet.getCurrentLevel() >= goal;
                break;
            case 4:
                isAdhere = pet.getRarity() >= goal;
                break;
        }
        return isAdhere;
    }


    public static List<ExperienceJson> getNewExperience(List<Integer> filterList, int newExperienceNums) {
        List<ExperienceJson> list = new ArrayList<ExperienceJson>();
        //int totalnums=experiencePool.size();
        int nums = 10 - filterList.size();
        ///  /// System.err.println(nums+"要刷新的數量");
        nums = nums <= 0 ? 1 : nums;
        int flag = 0;
        int[] pool = new int[nums];
        int loop = nums;
        for (int i = 0; i < loop; i++) {
            // /// System.err.println(i+"i"+loop+"loop"+flag+"flag");
            int index = (int) (experiencePool.size() * Math.random());
            int value = experiencePool.get(index);
            if (filterList.contains(value)) {
                loop++;
            } else {
                boolean isRepeate = false;
                for (int j = 0; j < flag; j++) {
                    if (value == pool[j]) {
                        loop++;
                        isRepeate = true;
                        break;
                    }
                }
                if (!isRepeate) {
                    pool[flag] = value;
                    flag++;

                }

            }

        }
        /// /// System.err.println("pool"+ Arrays.toString(pool));
        int temp = -1;
        // int flag=0;
        for (int i = 0; i < newExperienceNums; i++) {
            try {
                int index = (int) (Math.random() * (nums - i));
                temp = pool[index];
                ExperienceJson experienceJson = initExperience(temp);
                list.add(experienceJson);
                pool[index] = pool[nums - i - 1];
                pool[nums - i - 1] = temp;
            } catch (Exception e) {
                e.printStackTrace();
            }

        }
        return list;


    }
}
