package common;

import model.CommonInfo;

import java.util.List;

@ExcelConfigObject(key = "decision", isUnique = true)
public class CommonConfig {
    @ExcelColumn(name = "recovery_time")
    private int recoveryTime; //恢复1点体力需要时间
    @ExcelColumn(name = "name")
    private int changeName;//改名消耗钻石
    @ExcelColumn(name = "star1")
    private int star1LVLimit;//星级1等级上限
    @ExcelColumn(name = "star2")
    private int star2LVLimit;//星级2等级上限
    @ExcelColumn(name = "star3")
    private int star3LVLimit;//星级3等级上限
    @ExcelColumn(name = "star4")
    private int star4LVLimit;//星级4等级上限
    @ExcelColumn(name = "star5")
    private int star5LVLimit;//星级5等级上限
    @ExcelColumn(name = "star2_item", isCommonInfo = true)
    private CommonInfo star2Item;  // 升星消耗道具1-2
    @ExcelColumn(name = "star3_item", isCommonInfo = true)
    private CommonInfo star3Item;  // 升星消耗道具2-3
    @ExcelColumn(name = "star4_item", isCommonInfo = true)
    private CommonInfo star4Item;  // 升星消耗道具3-4
    @ExcelColumn(name = "star5_item", isCommonInfo = true)
    private CommonInfo star5Item;  // 升星消耗道具4-5
    @ExcelColumn(name = "dispatch_price")
    private int dispatchPrice;  // 派遣刷新1次价格
    @ExcelColumn(name = "dispatch_role", isCommonInfo = true, isList = true)
    private List<CommonInfo> dispatchRole; //派遣人数限制
    @ExcelColumn(name = "refresh_num")
    private int marketRefreshNum;
    @ExcelColumn(name = "refresh_num")
    private int refreshPirce;

    @ExcelColumn(name = "success_breed")
    private int breedSuccessProbability;
    @ExcelColumn(name = "consumediamond_speedup")
    private int dispatchAccelerateNeedNums;

    public int getRecoveryTime() {
        return recoveryTime;
    }

    public void setRecoveryTime(int recoveryTime) {
        this.recoveryTime = recoveryTime;
    }

    public int getChangeName() {
        return changeName;
    }

    public void setChangeName(int changeName) {
        this.changeName = changeName;
    }

    public int getStar1LVLimit() {
        return star1LVLimit;
    }

    public void setStar1LVLimit(int star1LVLimit) {
        this.star1LVLimit = star1LVLimit;
    }

    public int getStar2LVLimit() {
        return star2LVLimit;
    }

    public void setStar2LVLimit(int star2LVLimit) {
        this.star2LVLimit = star2LVLimit;
    }

    public int getStar3LVLimit() {
        return star3LVLimit;
    }

    public void setStar3LVLimit(int star3LVLimit) {
        this.star3LVLimit = star3LVLimit;
    }

    public int getStar4LVLimit() {
        return star4LVLimit;
    }

    public void setStar4LVLimit(int star4LVLimit) {
        this.star4LVLimit = star4LVLimit;
    }

    public int getStar5LVLimit() {
        return star5LVLimit;
    }

    public void setStar5LVLimit(int star5LVLimit) {
        this.star5LVLimit = star5LVLimit;
    }

    public CommonInfo getStar2Item() {
        return star2Item;
    }

    public void setStar2Item(CommonInfo star2Item) {
        this.star2Item = star2Item;
    }

    public CommonInfo getStar3Item() {
        return star3Item;
    }

    public void setStar3Item(CommonInfo star3Item) {
        this.star3Item = star3Item;
    }

    public CommonInfo getStar4Item() {
        return star4Item;
    }

    public void setStar4Item(CommonInfo star4Item) {
        this.star4Item = star4Item;
    }

    public CommonInfo getStar5Item() {
        return star5Item;
    }

    public void setStar5Item(CommonInfo star5Item) {
        this.star5Item = star5Item;
    }

    public int getDispatchPrice() {
        return dispatchPrice;
    }

    public void setDispatchPrice(int dispatchPrice) {
        this.dispatchPrice = dispatchPrice;
    }

    public List<CommonInfo> getDispatchRole() {
        return dispatchRole;
    }

    public void setDispatchRole(List<CommonInfo> dispatchRole) {
        this.dispatchRole = dispatchRole;
    }

    public int getMarketRefreshNum() {
        return marketRefreshNum;
    }

    public void setMarketRefreshNum(int marketRefreshNum) {
        this.marketRefreshNum = marketRefreshNum;
    }

    public int getRefreshPirce() {
        return refreshPirce;
    }

    public void setRefreshPirce(int refreshPirce) {
        this.refreshPirce = refreshPirce;
    }

    public int getBreedSuccessProbability() {
        return breedSuccessProbability;
    }

    public void setBreedSuccessProbability(int breedSuccessProbability) {
        this.breedSuccessProbability = breedSuccessProbability;
    }

    public int getDispatchAccelerateNeedNums() {
        return dispatchAccelerateNeedNums;
    }

    public void setDispatchAccelerateNeedNums(int dispatchAccelerateNeedNums) {
        this.dispatchAccelerateNeedNums = dispatchAccelerateNeedNums;
    }

    @Override
    public String toString() {
        return "CommonConfig{" +
                "recoveryTime=" + recoveryTime +
                ", changeName=" + changeName +
                ", star1LVLimit=" + star1LVLimit +
                ", star2LVLimit=" + star2LVLimit +
                ", star3LVLimit=" + star3LVLimit +
                ", star4LVLimit=" + star4LVLimit +
                ", star5LVLimit=" + star5LVLimit +
                ", star2Item=" + star2Item +
                ", star3Item=" + star3Item +
                ", star4Item=" + star4Item +
                ", star5Item=" + star5Item +
                ", dispatchPrice=" + dispatchPrice +
                ", dispatchRole=" + dispatchRole +
                ", marketRefreshNum=" + marketRefreshNum +
                ", refreshPirce=" + refreshPirce +
                ", breedSuccessProbability=" + breedSuccessProbability +
                '}';
    }
}
