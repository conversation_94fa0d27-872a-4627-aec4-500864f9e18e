<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
    "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
    "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="entities.InformationEntity" table="information" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="uid1" column="uid1"/>
        <property name="uid2" column="uid2"/>
        <property name="content" column="content"/>
        <property name="date" column="date"/>
        <property name="status" column="status"/>
    </class>
</hibernate-mapping>