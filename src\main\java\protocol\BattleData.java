// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: battle.proto

package protocol;

public final class BattleData {
  private BattleData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestCountBattleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required bool success = 1;
    /**
     * <code>required bool success = 1;</code>
     *
     * <pre>
     *游戏胜利或者失败
     * </pre>
     */
    boolean hasSuccess();
    /**
     * <code>required bool success = 1;</code>
     *
     * <pre>
     *游戏胜利或者失败
     * </pre>
     */
    boolean getSuccess();

    // required int32 mapId = 2;
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *地图id
     * </pre>
     */
    boolean hasMapId();
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *地图id
     * </pre>
     */
    int getMapId();

    // repeated int32 monsterId = 3;
    /**
     * <code>repeated int32 monsterId = 3;</code>
     *
     * <pre>
     *怪物id
     * </pre>
     */
    java.util.List<java.lang.Integer> getMonsterIdList();
    /**
     * <code>repeated int32 monsterId = 3;</code>
     *
     * <pre>
     *怪物id
     * </pre>
     */
    int getMonsterIdCount();
    /**
     * <code>repeated int32 monsterId = 3;</code>
     *
     * <pre>
     *怪物id
     * </pre>
     */
    int getMonsterId(int index);
  }
  /**
   * Protobuf type {@code protocol.RequestCountBattle}
   *
   * <pre>
   *1170
   * </pre>
   */
  public static final class RequestCountBattle extends
      com.google.protobuf.GeneratedMessage
      implements RequestCountBattleOrBuilder {
    // Use RequestCountBattle.newBuilder() to construct.
    private RequestCountBattle(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestCountBattle(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestCountBattle defaultInstance;
    public static RequestCountBattle getDefaultInstance() {
      return defaultInstance;
    }

    public RequestCountBattle getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestCountBattle(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              success_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mapId_ = input.readInt32();
              break;
            }
            case 24: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                monsterId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              monsterId_.add(input.readInt32());
              break;
            }
            case 26: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004) && input.getBytesUntilLimit() > 0) {
                monsterId_ = new java.util.ArrayList<java.lang.Integer>();
                mutable_bitField0_ |= 0x00000004;
              }
              while (input.getBytesUntilLimit() > 0) {
                monsterId_.add(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          monsterId_ = java.util.Collections.unmodifiableList(monsterId_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BattleData.internal_static_protocol_RequestCountBattle_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BattleData.internal_static_protocol_RequestCountBattle_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BattleData.RequestCountBattle.class, protocol.BattleData.RequestCountBattle.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestCountBattle> PARSER =
        new com.google.protobuf.AbstractParser<RequestCountBattle>() {
      public RequestCountBattle parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestCountBattle(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestCountBattle> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required bool success = 1;
    public static final int SUCCESS_FIELD_NUMBER = 1;
    private boolean success_;
    /**
     * <code>required bool success = 1;</code>
     *
     * <pre>
     *游戏胜利或者失败
     * </pre>
     */
    public boolean hasSuccess() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required bool success = 1;</code>
     *
     * <pre>
     *游戏胜利或者失败
     * </pre>
     */
    public boolean getSuccess() {
      return success_;
    }

    // required int32 mapId = 2;
    public static final int MAPID_FIELD_NUMBER = 2;
    private int mapId_;
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *地图id
     * </pre>
     */
    public boolean hasMapId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 mapId = 2;</code>
     *
     * <pre>
     *地图id
     * </pre>
     */
    public int getMapId() {
      return mapId_;
    }

    // repeated int32 monsterId = 3;
    public static final int MONSTERID_FIELD_NUMBER = 3;
    private java.util.List<java.lang.Integer> monsterId_;
    /**
     * <code>repeated int32 monsterId = 3;</code>
     *
     * <pre>
     *怪物id
     * </pre>
     */
    public java.util.List<java.lang.Integer>
        getMonsterIdList() {
      return monsterId_;
    }
    /**
     * <code>repeated int32 monsterId = 3;</code>
     *
     * <pre>
     *怪物id
     * </pre>
     */
    public int getMonsterIdCount() {
      return monsterId_.size();
    }
    /**
     * <code>repeated int32 monsterId = 3;</code>
     *
     * <pre>
     *怪物id
     * </pre>
     */
    public int getMonsterId(int index) {
      return monsterId_.get(index);
    }

    private void initFields() {
      success_ = false;
      mapId_ = 0;
      monsterId_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasSuccess()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMapId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBool(1, success_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, mapId_);
      }
      for (int i = 0; i < monsterId_.size(); i++) {
        output.writeInt32(3, monsterId_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, success_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mapId_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < monsterId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(monsterId_.get(i));
        }
        size += dataSize;
        size += 1 * getMonsterIdList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BattleData.RequestCountBattle parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BattleData.RequestCountBattle parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BattleData.RequestCountBattle parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BattleData.RequestCountBattle parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BattleData.RequestCountBattle prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestCountBattle}
     *
     * <pre>
     *1170
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BattleData.RequestCountBattleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BattleData.internal_static_protocol_RequestCountBattle_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BattleData.internal_static_protocol_RequestCountBattle_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BattleData.RequestCountBattle.class, protocol.BattleData.RequestCountBattle.Builder.class);
      }

      // Construct using protocol.BattleData.RequestCountBattle.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        success_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        mapId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        monsterId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BattleData.internal_static_protocol_RequestCountBattle_descriptor;
      }

      public protocol.BattleData.RequestCountBattle getDefaultInstanceForType() {
        return protocol.BattleData.RequestCountBattle.getDefaultInstance();
      }

      public protocol.BattleData.RequestCountBattle build() {
        protocol.BattleData.RequestCountBattle result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BattleData.RequestCountBattle buildPartial() {
        protocol.BattleData.RequestCountBattle result = new protocol.BattleData.RequestCountBattle(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.success_ = success_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.mapId_ = mapId_;
        if (((bitField0_ & 0x00000004) == 0x00000004)) {
          monsterId_ = java.util.Collections.unmodifiableList(monsterId_);
          bitField0_ = (bitField0_ & ~0x00000004);
        }
        result.monsterId_ = monsterId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BattleData.RequestCountBattle) {
          return mergeFrom((protocol.BattleData.RequestCountBattle)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BattleData.RequestCountBattle other) {
        if (other == protocol.BattleData.RequestCountBattle.getDefaultInstance()) return this;
        if (other.hasSuccess()) {
          setSuccess(other.getSuccess());
        }
        if (other.hasMapId()) {
          setMapId(other.getMapId());
        }
        if (!other.monsterId_.isEmpty()) {
          if (monsterId_.isEmpty()) {
            monsterId_ = other.monsterId_;
            bitField0_ = (bitField0_ & ~0x00000004);
          } else {
            ensureMonsterIdIsMutable();
            monsterId_.addAll(other.monsterId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasSuccess()) {
          
          return false;
        }
        if (!hasMapId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BattleData.RequestCountBattle parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BattleData.RequestCountBattle) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required bool success = 1;
      private boolean success_ ;
      /**
       * <code>required bool success = 1;</code>
       *
       * <pre>
       *游戏胜利或者失败
       * </pre>
       */
      public boolean hasSuccess() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required bool success = 1;</code>
       *
       * <pre>
       *游戏胜利或者失败
       * </pre>
       */
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <code>required bool success = 1;</code>
       *
       * <pre>
       *游戏胜利或者失败
       * </pre>
       */
      public Builder setSuccess(boolean value) {
        bitField0_ |= 0x00000001;
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool success = 1;</code>
       *
       * <pre>
       *游戏胜利或者失败
       * </pre>
       */
      public Builder clearSuccess() {
        bitField0_ = (bitField0_ & ~0x00000001);
        success_ = false;
        onChanged();
        return this;
      }

      // required int32 mapId = 2;
      private int mapId_ ;
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *地图id
       * </pre>
       */
      public boolean hasMapId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *地图id
       * </pre>
       */
      public int getMapId() {
        return mapId_;
      }
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *地图id
       * </pre>
       */
      public Builder setMapId(int value) {
        bitField0_ |= 0x00000002;
        mapId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mapId = 2;</code>
       *
       * <pre>
       *地图id
       * </pre>
       */
      public Builder clearMapId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mapId_ = 0;
        onChanged();
        return this;
      }

      // repeated int32 monsterId = 3;
      private java.util.List<java.lang.Integer> monsterId_ = java.util.Collections.emptyList();
      private void ensureMonsterIdIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          monsterId_ = new java.util.ArrayList<java.lang.Integer>(monsterId_);
          bitField0_ |= 0x00000004;
         }
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public java.util.List<java.lang.Integer>
          getMonsterIdList() {
        return java.util.Collections.unmodifiableList(monsterId_);
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public int getMonsterIdCount() {
        return monsterId_.size();
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public int getMonsterId(int index) {
        return monsterId_.get(index);
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public Builder setMonsterId(
          int index, int value) {
        ensureMonsterIdIsMutable();
        monsterId_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public Builder addMonsterId(int value) {
        ensureMonsterIdIsMutable();
        monsterId_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public Builder addAllMonsterId(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureMonsterIdIsMutable();
        super.addAll(values, monsterId_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 monsterId = 3;</code>
       *
       * <pre>
       *怪物id
       * </pre>
       */
      public Builder clearMonsterId() {
        monsterId_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestCountBattle)
    }

    static {
      defaultInstance = new RequestCountBattle(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestCountBattle)
  }

  public interface ResponseCountBattleOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // required bool success = 2;
    /**
     * <code>required bool success = 2;</code>
     */
    boolean hasSuccess();
    /**
     * <code>required bool success = 2;</code>
     */
    boolean getSuccess();

    // repeated .protocol.Item item = 3;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // repeated .protocol.PlainPet pet = 4;
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    java.util.List<protocol.PetData.PlainPet> 
        getPetList();
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    protocol.PetData.PlainPet getPet(int index);
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    int getPetCount();
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    java.util.List<? extends protocol.PetData.PlainPetOrBuilder> 
        getPetOrBuilderList();
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    protocol.PetData.PlainPetOrBuilder getPetOrBuilder(
        int index);

    // optional int32 palyerLv = 5;
    /**
     * <code>optional int32 palyerLv = 5;</code>
     */
    boolean hasPalyerLv();
    /**
     * <code>optional int32 palyerLv = 5;</code>
     */
    int getPalyerLv();

    // optional int32 playerExp = 6;
    /**
     * <code>optional int32 playerExp = 6;</code>
     */
    boolean hasPlayerExp();
    /**
     * <code>optional int32 playerExp = 6;</code>
     */
    int getPlayerExp();

    // optional int32 addExp = 7;
    /**
     * <code>optional int32 addExp = 7;</code>
     */
    boolean hasAddExp();
    /**
     * <code>optional int32 addExp = 7;</code>
     */
    int getAddExp();
  }
  /**
   * Protobuf type {@code protocol.ResponseCountBattle}
   *
   * <pre>
   *2170
   * </pre>
   */
  public static final class ResponseCountBattle extends
      com.google.protobuf.GeneratedMessage
      implements ResponseCountBattleOrBuilder {
    // Use ResponseCountBattle.newBuilder() to construct.
    private ResponseCountBattle(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseCountBattle(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseCountBattle defaultInstance;
    public static ResponseCountBattle getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseCountBattle getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseCountBattle(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              success_ = input.readBool();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000004;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              if (!((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
                pet_ = new java.util.ArrayList<protocol.PetData.PlainPet>();
                mutable_bitField0_ |= 0x00000008;
              }
              pet_.add(input.readMessage(protocol.PetData.PlainPet.PARSER, extensionRegistry));
              break;
            }
            case 40: {
              bitField0_ |= 0x00000004;
              palyerLv_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000008;
              playerExp_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000010;
              addExp_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        if (((mutable_bitField0_ & 0x00000008) == 0x00000008)) {
          pet_ = java.util.Collections.unmodifiableList(pet_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.BattleData.internal_static_protocol_ResponseCountBattle_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.BattleData.internal_static_protocol_ResponseCountBattle_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.BattleData.ResponseCountBattle.class, protocol.BattleData.ResponseCountBattle.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseCountBattle> PARSER =
        new com.google.protobuf.AbstractParser<ResponseCountBattle>() {
      public ResponseCountBattle parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseCountBattle(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseCountBattle> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required bool success = 2;
    public static final int SUCCESS_FIELD_NUMBER = 2;
    private boolean success_;
    /**
     * <code>required bool success = 2;</code>
     */
    public boolean hasSuccess() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required bool success = 2;</code>
     */
    public boolean getSuccess() {
      return success_;
    }

    // repeated .protocol.Item item = 3;
    public static final int ITEM_FIELD_NUMBER = 3;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *奖励物品
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // repeated .protocol.PlainPet pet = 4;
    public static final int PET_FIELD_NUMBER = 4;
    private java.util.List<protocol.PetData.PlainPet> pet_;
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    public java.util.List<protocol.PetData.PlainPet> getPetList() {
      return pet_;
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    public java.util.List<? extends protocol.PetData.PlainPetOrBuilder> 
        getPetOrBuilderList() {
      return pet_;
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    public int getPetCount() {
      return pet_.size();
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    public protocol.PetData.PlainPet getPet(int index) {
      return pet_.get(index);
    }
    /**
     * <code>repeated .protocol.PlainPet pet = 4;</code>
     */
    public protocol.PetData.PlainPetOrBuilder getPetOrBuilder(
        int index) {
      return pet_.get(index);
    }

    // optional int32 palyerLv = 5;
    public static final int PALYERLV_FIELD_NUMBER = 5;
    private int palyerLv_;
    /**
     * <code>optional int32 palyerLv = 5;</code>
     */
    public boolean hasPalyerLv() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional int32 palyerLv = 5;</code>
     */
    public int getPalyerLv() {
      return palyerLv_;
    }

    // optional int32 playerExp = 6;
    public static final int PLAYEREXP_FIELD_NUMBER = 6;
    private int playerExp_;
    /**
     * <code>optional int32 playerExp = 6;</code>
     */
    public boolean hasPlayerExp() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 playerExp = 6;</code>
     */
    public int getPlayerExp() {
      return playerExp_;
    }

    // optional int32 addExp = 7;
    public static final int ADDEXP_FIELD_NUMBER = 7;
    private int addExp_;
    /**
     * <code>optional int32 addExp = 7;</code>
     */
    public boolean hasAddExp() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 addExp = 7;</code>
     */
    public int getAddExp() {
      return addExp_;
    }

    private void initFields() {
      errorId_ = 0;
      success_ = false;
      item_ = java.util.Collections.emptyList();
      pet_ = java.util.Collections.emptyList();
      palyerLv_ = 0;
      playerExp_ = 0;
      addExp_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSuccess()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      for (int i = 0; i < getPetCount(); i++) {
        if (!getPet(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBool(2, success_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(3, item_.get(i));
      }
      for (int i = 0; i < pet_.size(); i++) {
        output.writeMessage(4, pet_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(5, palyerLv_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(6, playerExp_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(7, addExp_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, success_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, item_.get(i));
      }
      for (int i = 0; i < pet_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, pet_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, palyerLv_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, playerExp_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, addExp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.BattleData.ResponseCountBattle parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.BattleData.ResponseCountBattle parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.BattleData.ResponseCountBattle parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.BattleData.ResponseCountBattle parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.BattleData.ResponseCountBattle prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseCountBattle}
     *
     * <pre>
     *2170
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.BattleData.ResponseCountBattleOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.BattleData.internal_static_protocol_ResponseCountBattle_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.BattleData.internal_static_protocol_ResponseCountBattle_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.BattleData.ResponseCountBattle.class, protocol.BattleData.ResponseCountBattle.Builder.class);
      }

      // Construct using protocol.BattleData.ResponseCountBattle.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
          getPetFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        success_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          itemBuilder_.clear();
        }
        if (petBuilder_ == null) {
          pet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
        } else {
          petBuilder_.clear();
        }
        palyerLv_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        playerExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        addExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.BattleData.internal_static_protocol_ResponseCountBattle_descriptor;
      }

      public protocol.BattleData.ResponseCountBattle getDefaultInstanceForType() {
        return protocol.BattleData.ResponseCountBattle.getDefaultInstance();
      }

      public protocol.BattleData.ResponseCountBattle build() {
        protocol.BattleData.ResponseCountBattle result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.BattleData.ResponseCountBattle buildPartial() {
        protocol.BattleData.ResponseCountBattle result = new protocol.BattleData.ResponseCountBattle(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.success_ = success_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (petBuilder_ == null) {
          if (((bitField0_ & 0x00000008) == 0x00000008)) {
            pet_ = java.util.Collections.unmodifiableList(pet_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.pet_ = pet_;
        } else {
          result.pet_ = petBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000004;
        }
        result.palyerLv_ = palyerLv_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000008;
        }
        result.playerExp_ = playerExp_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000010;
        }
        result.addExp_ = addExp_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.BattleData.ResponseCountBattle) {
          return mergeFrom((protocol.BattleData.ResponseCountBattle)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.BattleData.ResponseCountBattle other) {
        if (other == protocol.BattleData.ResponseCountBattle.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSuccess()) {
          setSuccess(other.getSuccess());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (petBuilder_ == null) {
          if (!other.pet_.isEmpty()) {
            if (pet_.isEmpty()) {
              pet_ = other.pet_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensurePetIsMutable();
              pet_.addAll(other.pet_);
            }
            onChanged();
          }
        } else {
          if (!other.pet_.isEmpty()) {
            if (petBuilder_.isEmpty()) {
              petBuilder_.dispose();
              petBuilder_ = null;
              pet_ = other.pet_;
              bitField0_ = (bitField0_ & ~0x00000008);
              petBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getPetFieldBuilder() : null;
            } else {
              petBuilder_.addAllMessages(other.pet_);
            }
          }
        }
        if (other.hasPalyerLv()) {
          setPalyerLv(other.getPalyerLv());
        }
        if (other.hasPlayerExp()) {
          setPlayerExp(other.getPlayerExp());
        }
        if (other.hasAddExp()) {
          setAddExp(other.getAddExp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasSuccess()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        for (int i = 0; i < getPetCount(); i++) {
          if (!getPet(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.BattleData.ResponseCountBattle parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.BattleData.ResponseCountBattle) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required bool success = 2;
      private boolean success_ ;
      /**
       * <code>required bool success = 2;</code>
       */
      public boolean hasSuccess() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required bool success = 2;</code>
       */
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <code>required bool success = 2;</code>
       */
      public Builder setSuccess(boolean value) {
        bitField0_ |= 0x00000002;
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required bool success = 2;</code>
       */
      public Builder clearSuccess() {
        bitField0_ = (bitField0_ & ~0x00000002);
        success_ = false;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 3;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *奖励物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // repeated .protocol.PlainPet pet = 4;
      private java.util.List<protocol.PetData.PlainPet> pet_ =
        java.util.Collections.emptyList();
      private void ensurePetIsMutable() {
        if (!((bitField0_ & 0x00000008) == 0x00000008)) {
          pet_ = new java.util.ArrayList<protocol.PetData.PlainPet>(pet_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PetData.PlainPet, protocol.PetData.PlainPet.Builder, protocol.PetData.PlainPetOrBuilder> petBuilder_;

      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public java.util.List<protocol.PetData.PlainPet> getPetList() {
        if (petBuilder_ == null) {
          return java.util.Collections.unmodifiableList(pet_);
        } else {
          return petBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public int getPetCount() {
        if (petBuilder_ == null) {
          return pet_.size();
        } else {
          return petBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public protocol.PetData.PlainPet getPet(int index) {
        if (petBuilder_ == null) {
          return pet_.get(index);
        } else {
          return petBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder setPet(
          int index, protocol.PetData.PlainPet value) {
        if (petBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePetIsMutable();
          pet_.set(index, value);
          onChanged();
        } else {
          petBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder setPet(
          int index, protocol.PetData.PlainPet.Builder builderForValue) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.set(index, builderForValue.build());
          onChanged();
        } else {
          petBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder addPet(protocol.PetData.PlainPet value) {
        if (petBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePetIsMutable();
          pet_.add(value);
          onChanged();
        } else {
          petBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder addPet(
          int index, protocol.PetData.PlainPet value) {
        if (petBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePetIsMutable();
          pet_.add(index, value);
          onChanged();
        } else {
          petBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder addPet(
          protocol.PetData.PlainPet.Builder builderForValue) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.add(builderForValue.build());
          onChanged();
        } else {
          petBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder addPet(
          int index, protocol.PetData.PlainPet.Builder builderForValue) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.add(index, builderForValue.build());
          onChanged();
        } else {
          petBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder addAllPet(
          java.lang.Iterable<? extends protocol.PetData.PlainPet> values) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          super.addAll(values, pet_);
          onChanged();
        } else {
          petBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder clearPet() {
        if (petBuilder_ == null) {
          pet_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          petBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public Builder removePet(int index) {
        if (petBuilder_ == null) {
          ensurePetIsMutable();
          pet_.remove(index);
          onChanged();
        } else {
          petBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public protocol.PetData.PlainPet.Builder getPetBuilder(
          int index) {
        return getPetFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public protocol.PetData.PlainPetOrBuilder getPetOrBuilder(
          int index) {
        if (petBuilder_ == null) {
          return pet_.get(index);  } else {
          return petBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public java.util.List<? extends protocol.PetData.PlainPetOrBuilder> 
           getPetOrBuilderList() {
        if (petBuilder_ != null) {
          return petBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(pet_);
        }
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public protocol.PetData.PlainPet.Builder addPetBuilder() {
        return getPetFieldBuilder().addBuilder(
            protocol.PetData.PlainPet.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public protocol.PetData.PlainPet.Builder addPetBuilder(
          int index) {
        return getPetFieldBuilder().addBuilder(
            index, protocol.PetData.PlainPet.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.PlainPet pet = 4;</code>
       */
      public java.util.List<protocol.PetData.PlainPet.Builder> 
           getPetBuilderList() {
        return getPetFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.PetData.PlainPet, protocol.PetData.PlainPet.Builder, protocol.PetData.PlainPetOrBuilder> 
          getPetFieldBuilder() {
        if (petBuilder_ == null) {
          petBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.PetData.PlainPet, protocol.PetData.PlainPet.Builder, protocol.PetData.PlainPetOrBuilder>(
                  pet_,
                  ((bitField0_ & 0x00000008) == 0x00000008),
                  getParentForChildren(),
                  isClean());
          pet_ = null;
        }
        return petBuilder_;
      }

      // optional int32 palyerLv = 5;
      private int palyerLv_ ;
      /**
       * <code>optional int32 palyerLv = 5;</code>
       */
      public boolean hasPalyerLv() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 palyerLv = 5;</code>
       */
      public int getPalyerLv() {
        return palyerLv_;
      }
      /**
       * <code>optional int32 palyerLv = 5;</code>
       */
      public Builder setPalyerLv(int value) {
        bitField0_ |= 0x00000010;
        palyerLv_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 palyerLv = 5;</code>
       */
      public Builder clearPalyerLv() {
        bitField0_ = (bitField0_ & ~0x00000010);
        palyerLv_ = 0;
        onChanged();
        return this;
      }

      // optional int32 playerExp = 6;
      private int playerExp_ ;
      /**
       * <code>optional int32 playerExp = 6;</code>
       */
      public boolean hasPlayerExp() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>optional int32 playerExp = 6;</code>
       */
      public int getPlayerExp() {
        return playerExp_;
      }
      /**
       * <code>optional int32 playerExp = 6;</code>
       */
      public Builder setPlayerExp(int value) {
        bitField0_ |= 0x00000020;
        playerExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 playerExp = 6;</code>
       */
      public Builder clearPlayerExp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        playerExp_ = 0;
        onChanged();
        return this;
      }

      // optional int32 addExp = 7;
      private int addExp_ ;
      /**
       * <code>optional int32 addExp = 7;</code>
       */
      public boolean hasAddExp() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>optional int32 addExp = 7;</code>
       */
      public int getAddExp() {
        return addExp_;
      }
      /**
       * <code>optional int32 addExp = 7;</code>
       */
      public Builder setAddExp(int value) {
        bitField0_ |= 0x00000040;
        addExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 addExp = 7;</code>
       */
      public Builder clearAddExp() {
        bitField0_ = (bitField0_ & ~0x00000040);
        addExp_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseCountBattle)
    }

    static {
      defaultInstance = new ResponseCountBattle(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseCountBattle)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestCountBattle_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestCountBattle_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseCountBattle_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseCountBattle_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014battle.proto\022\010protocol\032\nitem.proto\032\tpe" +
      "t.proto\"G\n\022RequestCountBattle\022\017\n\007success" +
      "\030\001 \002(\010\022\r\n\005mapId\030\002 \002(\005\022\021\n\tmonsterId\030\003 \003(\005" +
      "\"\253\001\n\023ResponseCountBattle\022\017\n\007errorId\030\001 \002(" +
      "\005\022\017\n\007success\030\002 \002(\010\022\034\n\004item\030\003 \003(\0132\016.proto" +
      "col.Item\022\037\n\003pet\030\004 \003(\0132\022.protocol.PlainPe" +
      "t\022\020\n\010palyerLv\030\005 \001(\005\022\021\n\tplayerExp\030\006 \001(\005\022\016" +
      "\n\006addExp\030\007 \001(\005B\014B\nBattleData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestCountBattle_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestCountBattle_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestCountBattle_descriptor,
              new java.lang.String[] { "Success", "MapId", "MonsterId", });
          internal_static_protocol_ResponseCountBattle_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseCountBattle_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseCountBattle_descriptor,
              new java.lang.String[] { "ErrorId", "Success", "Item", "Pet", "PalyerLv", "PlayerExp", "AddExp", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
          protocol.PetData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
