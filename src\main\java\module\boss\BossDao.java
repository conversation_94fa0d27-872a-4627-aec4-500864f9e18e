package module.boss;

import entities.BossEntity;
import manager.MySql;

import java.util.List;

public class Boss<PERSON>ao {
    private static BossDao inst = null;

    public static BossDao getInstance() {
        if (inst == null) {
            inst = new BossDao();
        }
        return inst;
    }
    public BossEntity queryUid(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from BossEntity where uid='").append(uid).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return null==entity?null:(BossEntity)entity;
    }
    public void BossDelete() {
        StringBuffer stringBuffer = new StringBuffer("Delete from BossEntity  ");
         MySql.updateSomes(stringBuffer.toString());
    }
}
