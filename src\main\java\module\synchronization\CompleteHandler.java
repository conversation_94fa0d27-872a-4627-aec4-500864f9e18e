package module.synchronization;

import java.util.Map;

/**
 * Created by nara on 2018/6/13.
 */
public class CompleteHandler implements Runnable {
    private static String key;

    public static void setKey(String key) {
        CompleteHandler.key = key;
    }

    public void run(){
        while (true){
            try {
                GameInfo gameInfo = SyncManager.getSyncProgressList().get(key);
                boolean bo = true;
                for (Map.Entry<Integer, FightPlayerInfo>entry2:gameInfo.getFightPlayers().entrySet()){

                    if (entry2.getValue().getStatus() == 0){
                        bo = false;
                        break;
                    }
                }
                if (bo == true){
                    SyncManager.startFight(key);
                    return;
                }
                Thread.sleep(1000);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }
}