package module.item;

import entities.ItemEntity;
import model.ItemInfo;
import protocol.ItemData;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by nara on 2018/1/3.
 */
public interface  IItem {
    Map<Integer, List<ItemInfo>> initItemData(String uid);
    double updateItemInfo(String uid,int itemId,long addNum);
    ItemData.ResponseBuyItem.Builder buyItem(String uid,int id,int num);
    ItemData.ResponseUseItem.Builder useItem(String uid,int id,int num,int type);
    ItemData.ReportItem.Builder PhyItem(String uid, int id, double num);
    ItemData.ResponseOpenBagCell.Builder openBagCell(String uid);
    ItemData.ResponseCompose.Builder compose(String uid,int id);
    double getItemNum(String uid, int itemId);
    void deleteUtility(String uid,int id);
    int judgeUtility(String uid,int itemId);
    boolean judgebagCell(String uid,List<ItemInfo> itemList);
    ItemData.ResponseGetTotalEndless.Builder getTotalEndless(String uid,int id);

    Map<String,String> FullSet(Set<String> set,int lottoid);
    List<ItemData.Item> getBag(String uid);
}
