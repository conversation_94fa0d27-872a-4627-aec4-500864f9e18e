package server;

import java.util.Arrays;

/**
 * Created by nara on 2017/11/15.
 */
/**
 * <pre>
 * 自己定义的协议
 *  数据包格式
 * +——----——+——-----——+——----——+
 * |协议开始标志|  长度             |   数据       |
 * +——----——+——-----——+——----——+
 * 1.协议开始标志head_data，为int类型的数据，16进制表示为0X76
 * 2.传输数据的长度contentLength，int类型
 * 3.要传输的数据
 * </pre>
 */
public class SuperProtocol {
    /**
     * 消息的开头的信息标志
     */
    private byte head_data = 0X76;
    /**
     * 协议号
     */
    private int msgId;
    /**
     * 消息的长度
     */
    private int contentLength;
    /**
     * 消息的内容
     */
    private byte[] content;

    /**
     * 用于初始化，SuperProtocol
     */
    public SuperProtocol(int msgId,int contentLength, byte[] content) {
        this.msgId = msgId;
        this.contentLength = contentLength;
        this.content = content;
    }

    public int getHead_data() {
        return head_data;
    }

    public int getContentLength() {
        return contentLength;
    }

    public int getMsgId() {
        return msgId;
    }

    public void setMsgId(int msgId) {
        this.msgId = msgId;
    }

    public void setContentLength(int contentLength) {
        this.contentLength = contentLength;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    @Override
    public String toString() {
        return "SuperProtocol [head_data=" + head_data
                + ", msgId=" + msgId
                + ", contentLength=" + contentLength
                + ", content=" + Arrays.toString(content) + "]";
    }
}
