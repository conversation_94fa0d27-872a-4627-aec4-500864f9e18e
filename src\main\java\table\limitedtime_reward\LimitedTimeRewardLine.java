package table.limitedtime_reward;

import io.netty.util.CharsetUtil;
import table.LineKey;

import java.util.ArrayList;
import java.util.List;

public class LimitedTimeRewardLine implements LineKey {
    public int id;
    public String bpreward_1;
    public String bpreward_2;

    public List<Integer> leftReward;
    public List<Integer> rightReward;
    public void Parse() {
        leftReward  = new ArrayList<>();
        String[] rewards = bpreward_1.split("[,]");
        for (String reward : rewards) {
            leftReward.add(Integer.parseInt(reward));
        }

        rightReward  = new ArrayList<>();
        String[] rewardsR = bpreward_2.split("[,]");
        for (String reward : rewardsR) {
            rightReward.add(Integer.parseInt(reward));
        }
    }
    @Override
    public int Key() {
        return id;
    }

    @Override
    public String toString() {
        return String.format("[id:" + id + "  bpreward_1:" + bpreward_1 + "  bpreward_2:" + bpreward_2 + "\n",CharsetUtil.UTF_8);
    }
}


