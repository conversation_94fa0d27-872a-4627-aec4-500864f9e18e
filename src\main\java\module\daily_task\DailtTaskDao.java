package module.daily_task;

import java.util.List;

import org.hibernate.Session;

import entities.DailyTaskEntity;
import manager.MySql;

public class DailtTaskDao {
    private static DailtTaskDao inst = null;
    public static DailtTaskDao getInstance() {
        if (inst == null) {
            inst = new DailtTaskDao();
        }
        return inst;
    }

    public void insert(DailyTaskEntity entity){
        try (Session session = MySql.getSession()) {
            session.save(entity);
            session.beginTransaction().commit();
        }
    }

    public void update(DailyTaskEntity entity) {
        try (Session session = MySql.getSession()) {
            session.update(entity);
            session.beginTransaction().commit();
        }
    }

    public DailyTaskEntity GetDailyTaskEntity(String uid, String dailyType) {
        String stringBuffer = "from DailyTaskEntity where uid='" + uid + "'" + " and type='" + dailyType + "'";
        Object entity = MySql.queryForOne(stringBuffer);
        return (DailyTaskEntity) entity;
    }

    public List<Object> GetAllDailyTaskEntity(String uid) {
        String stringBuffer = "from DailyTaskEntity where uid='" + uid + "'";
        List<Object> list = MySql.queryForList(stringBuffer);
        return list;
    }

}
