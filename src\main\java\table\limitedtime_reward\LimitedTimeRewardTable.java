package table.limitedtime_reward;

import table.TableManager;
import table.pvp_reward.pvp_rewardLine;

import java.util.Comparator;

public class LimitedTimeRewardTable extends TableManager<LimitedTimeRewardLine> {
    private static LimitedTimeRewardTable inst = null;
    public static LimitedTimeRewardTable getInstance() {
        if (inst == null) {
            inst = new LimitedTimeRewardTable();
        }
        return inst;
    }
    @Override
    public void Parse() {
        this.GetAllItem().sort(Comparator.comparingInt(a -> a.id));
        for (LimitedTimeRewardLine data :
                GetAllItem()) {
            data.Parse();
        }
    }
    @Override
    public String LinePath() {
        return "table.limitedtime_reward.LimitedTimeRewardLine";
    }

    @Override
    public String TableName() {
        return "sevendaybpReward";
    }
}
