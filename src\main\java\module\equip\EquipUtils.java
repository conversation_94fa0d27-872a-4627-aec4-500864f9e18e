package module.equip;

import common.ComposeConfig;
import common.EquipmentConfig;
import common.ResourceNotFound;
import common.SuperConfig;
import entities.EquipEntity;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import module.robot.ranName;
import protocol.ProtoData;

import java.util.*;

public class EquipUtils {
    public static EquipEntity createEquip(String uid, int equipType) {
        EquipEntity equipEntity = new EquipEntity();
        equipEntity.setCurrentExp(0);
        equipEntity.setCurrentLevel(1);
        equipEntity.setFriendId(uid);
        equipEntity.setEquipType(equipType);
        equipEntity.setName("");
        Redis jedis = Redis.getInstance();
        Iterator<String> iterator = jedis.keys("equipmentconfig:*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> mailMap = jedis.hgetAll(key);
            int keys = Integer.parseInt(mailMap.get("ID"));
            if (keys == equipEntity.getEquipType()) {
                equipEntity.setSort(Integer.parseInt(String.valueOf(mailMap.get("sort"))));
            }
        }
        try {
            //等级决定各个系数
            int level_l = 1;
            EquipmentConfig equipmentConfig = (EquipmentConfig) SuperConfig.getCongifObject(SuperConfig.equipConfig, level_l);
            //hp
            List<Float> hpFactorList = equipmentConfig.getHp();
            int hpFactorindex = (int) (Math.random() * hpFactorList.size());
            float hpFactor = hpFactorList.get(hpFactorindex);
            equipEntity.setHpFactor(hpFactor);
            //ad
            List<Float> AdFactorList = equipmentConfig.getPhysical_attack();
            int AdFactorIndex = (int) (Math.random() * AdFactorList.size());
            float AdFactor = AdFactorList.get(AdFactorIndex);
            equipEntity.setAdFactor(AdFactor);
            //ap
            List<Float> apFactorList = equipmentConfig.getMagical_attack();
            int apFactorIndex = (int) (Math.random() * apFactorList.size());
            float apFactor = apFactorList.get(apFactorIndex);
            equipEntity.setApFactor(apFactor);
            //arm
            List<Float> armFactorList = equipmentConfig.getPhysical_defence();
            int armFactorIndex = (int) (Math.random() * armFactorList.size());
            float armFactor = armFactorList.get(armFactorIndex);
            equipEntity.setArmFactor(armFactor);
            //mdf
            List<Float> mdfFactorList = equipmentConfig.getMagical_defence();
            int mdfFactorIndex = (int) (Math.random() * mdfFactorList.size());
            float mdfFactor = mdfFactorList.get(mdfFactorIndex);
            equipEntity.setMdfFactor(mdfFactor);
            //speed
            List<Float> speedFactorList = equipmentConfig.getSpeed();
            int speedFactorIndex = (int) (Math.random() * speedFactorList.size());
            float speedFactor = speedFactorList.get(speedFactorIndex);
            equipEntity.setSpeedFactor(speedFactor);
        } catch (ResourceNotFound resourceNotFound) {
            resourceNotFound.printStackTrace();
        }
        int equipId = createEquipEid(equipEntity.getCurrentLevel(), uid, equipType);
        equipEntity.setEquipEid(equipId);

        System.err.println("新添加的装备=" + equipEntity.toString());
        MySql.insert(equipEntity);
        return equipEntity;
    }

    private static int createEquipEid(int character, String friendId, int equipType) {
        int equipEid = 0;
        equipEid = ((int) (Math.random() * 128)) << 16 + ((int) TimerHandler.nowTimeStamp) >> 16 << 8 + equipType % 64 << 2 + character % 4;
        equipEid = ((int) (Math.random() * Integer.MAX_VALUE));
        return equipEid;
    }


}