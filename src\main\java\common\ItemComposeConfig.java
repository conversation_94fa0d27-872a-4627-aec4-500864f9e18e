package common;

import model.CommonInfo;

import java.util.List;
import java.util.Objects;

@ExcelConfigObject(key = "ItemCompose")
public class ItemComposeConfig {
    @ExcelColumn(name = "id") //查找ID
    private int id;//
    @ExcelColumn(name = "ItemID") //道具ID
    private int itemId;
    @ExcelColumn(name = "CostItem", isList = true, isCommonInfo = true)//花費道具
    private List<CommonInfo> costItem; //花费的道具数额

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getItemId() {
        return itemId;
    }

    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    public List<CommonInfo> getCostItem() {
        return costItem;
    }

    public void setCostItem(List<CommonInfo> costItem) {
        this.costItem = costItem;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ItemComposeConfig that = (ItemComposeConfig) o;
        return getId() == that.getId() &&
                getItemId() == that.getItemId() &&
                Objects.equals(getCostItem(), that.getCostItem());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getItemId(), getCostItem());
    }

    @Override
    public String toString() {
        return "ItemComposeConfig{" +
                "id=" + id +
                ", itemId=" + itemId +
                ", costItem=" + costItem +
                '}';
    }
}
