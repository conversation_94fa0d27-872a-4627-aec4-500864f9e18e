package module.synchronization.match;

import manager.Redis;
import manager.TimerHandler;
import model.CupboardInfo;
import module.synchronization.RoomRoleInfo;
import module.synchronization.match.MatchInfo;
import model.RoleDressInfo;
import module.login.ILogin;
import module.login.LoginDao;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * Created by nara on 2018/5/23.
 */
public class MatchingHandler implements Runnable {
    private static List<MatchInfo> playerList = new LinkedList<MatchInfo>();

    public void run() {
        while (true){
            try {
                setMatching();
                Thread.sleep(1000);
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

    public void setMatching(){
        MatchInfo matchInfo = playerList.get(0);
        while (matchInfo != null && matchInfo.getStartStamp()+60*1000 < TimerHandler.nowTimeStamp){
            playerList.remove(0);
            matchInfo = playerList.get(0);
        }
        ILogin iLogin = LoginDao.getInstance();
        Redis jedis = Redis.getInstance();
        while (playerList.size() > 3){
            List<RoomRoleInfo> syncChapterInfoList = new ArrayList<RoomRoleInfo>();
            for (int i = 0 ; i < 4 ; i++){
                matchInfo = playerList.remove(0);
                RoomRoleInfo roomRoleInfo = new RoomRoleInfo();
                roomRoleInfo.setPlayerId(matchInfo.getPlayerId());
                String roleId = jedis.hget("role:"+matchInfo.getUid(),"roleid");
                String key = "roledress:"+matchInfo.getUid()+"#"+roleId;
                RoleDressInfo roleDressInfo = iLogin.getRoleDressFromRedis(key);
                CupboardInfo cupboard = iLogin.getCupboardInfo(roleDressInfo.getCupboardList(),0);
                if (cupboard == null){
                    cupboard = new CupboardInfo();
                    cupboard.setId(0);
                }
                roomRoleInfo.setCupboard(cupboard);
                syncChapterInfoList.add(roomRoleInfo);
            }
            //广播进入游戏

        }
    }
}