package entities;

import protocol.EquipData;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "equip", schema = "", catalog = "super_star_fruit")
public class EquipEntity {
    private int id;
    private String friendId;
    private int equipEid;
    private int equipType;
    private int sort;
    private String name;
    private int currentLevel;
    private int currentExp;
    private int hp;//体力
    private int ad;//物攻
    private int ap;//法攻
    private int arm;//物防
    private int mdf;//法防
    private int speed;//速度
    private float hpFactor;
    private float adFactor;
    private float apFactor;
    private float armFactor;
    private float mdfFactor;
    private float speedFactor;


    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "friendId")
    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }


    @Basic
    @Column(name = "sort")
    public int getSort() {
        return sort;
    }

    public void setSort(int sort) {
        this.sort = sort;
    }


    @Basic
    @Column(name = "equipEid")
    public int getEquipEid() {
        return equipEid;
    }

    public void setEquipEid(int equipEid) {
        this.equipEid = equipEid;
    }


    @Basic
    @Column(name = "equipType")

    public int getEquipType() {
        return equipType;
    }

    public void setEquipType(int equipType) {
        this.equipType = equipType;
    }


    @Basic
    @Column(name = "currentLevel")
    public int getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(int currentLevel) {
        this.currentLevel = currentLevel;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    @Basic
    @Column(name = "currentExp")
    public int getCurrentExp() {
        return currentExp;
    }

    public void setCurrentExp(int currentExp) {
        this.currentExp = currentExp;
    }

    @Basic
    @Column(name = "hp")
    public int getHp() {
        return hp;
    }

    public void setHp(int hp) {
        this.hp = hp;
    }

    @Basic
    @Column(name = "ad")
    public int getAd() {
        return ad;
    }

    public void setAd(int ad) {
        this.ad = ad;
    }


    @Basic
    @Column(name = "ap")
    public int getAp() {
        return ap;
    }

    public void setAp(int ap) {
        this.ap = ap;
    }


    @Basic
    @Column(name = "arm")

    public int getArm() {
        return arm;
    }

    public void setArm(int arm) {
        this.arm = arm;
    }


    @Basic
    @Column(name = "mdf")
    public int getMdf() {
        return mdf;
    }

    public void setMdf(int mdf) {
        this.mdf = mdf;
    }


    @Basic
    @Column(name = "speed")

    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    @Basic
    @Column(name = "hpFactor")
    public float getHpFactor() {
        return hpFactor;
    }

    public void setHpFactor(float hpFactor) {
        this.hpFactor = hpFactor;
    }

    @Basic
    @Column(name = "adFactor")
    public float getAdFactor() {
        return adFactor;
    }

    public void setAdFactor(float adFactor) {
        this.adFactor = adFactor;
    }

    @Basic
    @Column(name = "apFactor")
    public float getApFactor() {
        return apFactor;
    }

    public void setApFactor(float apFactor) {
        this.apFactor = apFactor;
    }

    @Basic
    @Column(name = "armFactor")
    public float getArmFactor() {
        return armFactor;
    }

    public void setArmFactor(float armFactor) {
        this.armFactor = armFactor;
    }

    @Basic
    @Column(name = "mdfFactor")
    public float getMdfFactor() {
        return mdfFactor;
    }

    public void setMdfFactor(float mdfFactor) {
        this.mdfFactor = mdfFactor;
    }

    @Basic
    @Column(name = "speedFactor")
    public float getSpeedFactor() {
        return speedFactor;
    }

    public void setSpeedFactor(float speedFactor) {
        this.speedFactor = speedFactor;
    }

    public static EquipData.Equip entityToPb(EquipEntity entity) {
        EquipData.Equip equip = null;
        if (entity != null) {
            EquipData.Equip.Builder builder = EquipData.Equip.newBuilder();
            builder.setEquipType(entity.getEquipType());
            builder.setName(entity.getName());
            builder.setSort(entity.getSort());
            builder.setCurrentExp(entity.getCurrentExp());
            builder.setCurrentLevel(entity.getCurrentLevel());
            builder.setEquipEid(entity.getEquipEid());
            builder.setAdFactor(entity.getAdFactor());
            builder.setApFactor(entity.getApFactor());
            builder.setHpFactor(entity.getHpFactor());
            builder.setArmFactor(entity.getArmFactor());
            builder.setMdfFactor(entity.getMdfFactor());
            builder.setSpeedFactor(entity.getSpeedFactor());
            equip = builder.build();
        }
        return equip;
    }

    @Override
    public String toString() {
        return "EquipEntity{" +
                "id=" + id +
                ", friendId='" + friendId + '\'' +
                ", equipEid=" + equipEid +
                ", equipType=" + equipType +
                ", name='" + name + '\'' +
                ", sort=" + sort +
                ", currentLevel=" + currentLevel +
                ", currentExp=" + currentExp +
                ", hp=" + hp +
                ", ad=" + ad +
                ", ap=" + ap +
                ", arm=" + arm +
                ", mdf=" + mdf +
                ", speed=" + speed +
                ", hpFactor=" + hpFactor +
                ", adFactor=" + adFactor +
                ", apFactor=" + apFactor +
                ", armFactor=" + armFactor +
                ", mdfFactor=" + mdfFactor +
                ", speedFactor=" + speedFactor +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        EquipEntity that = (EquipEntity) o;
        return getId() == that.getId() &&
                getEquipEid() == that.getEquipEid() &&
                getEquipType() == that.getEquipType() &&
                getSort() == that.getSort() &&
                getCurrentLevel() == that.getCurrentLevel() &&
                getCurrentExp() == that.getCurrentExp() &&
                getHp() == that.getHp() &&
                getAd() == that.getAd() &&
                getAp() == that.getAp() &&
                getArm() == that.getArm() &&
                getMdf() == that.getMdf() &&
                getSpeed() == that.getSpeed() &&
                Float.compare(that.getHpFactor(), getHpFactor()) == 0 &&
                Float.compare(that.getAdFactor(), getAdFactor()) == 0 &&
                Float.compare(that.getApFactor(), getApFactor()) == 0 &&
                Float.compare(that.getArmFactor(), getArmFactor()) == 0 &&
                Float.compare(that.getMdfFactor(), getMdfFactor()) == 0 &&
                Float.compare(that.getSpeedFactor(), getSpeedFactor()) == 0 &&
                Objects.equals(getFriendId(), that.getFriendId()) &&
                Objects.equals(getName(), that.getName());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getFriendId(), getEquipEid(), getEquipType(), getSort(), getName(), getCurrentLevel(), getCurrentExp(), getHp(), getAd(), getAp(), getArm(), getMdf(), getSpeed(), getHpFactor(), getAdFactor(), getApFactor(), getArmFactor(), getMdfFactor(), getSpeedFactor());
    }
}
