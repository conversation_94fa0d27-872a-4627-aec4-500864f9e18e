package model;

import common.PlayerStatus;
import io.netty.channel.ChannelHandlerContext;

public class PlayerInfo {
    private String ip;  //玩家ip
    private String uid;  // 服务器的uid

    private PlayerStatus status; // 玩家状态
    private ChannelHandlerContext ctx;  // 连接

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public PlayerStatus getStatus() {
        return status;
    }

    public void setStatus(PlayerStatus status) {
        this.status = status;
    }

    public ChannelHandlerContext getCtx() {
        return ctx;
    }

    public void setCtx(ChannelHandlerContext ctx) {
        this.ctx = ctx;
    }
}
