package entities;

import javax.persistence.*;

/**
 * Created by nara on 2019/4/2.
 */
@Entity
@Table(name = "payorder", schema = "", catalog = "super_star_fruit")
public class PayorderEntity {
    private int id;
    private String transactionid;
    private String uid;
    private int payid;
    private String timestamp;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "transactionid")
    public String getTransactionid() {
        return transactionid;
    }

    public void setTransactionid(String transactionid) {
        this.transactionid = transactionid;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "payid")
    public int getPayid() {
        return payid;
    }

    public void setPayid(int payid) {
        this.payid = payid;
    }

    @Basic
    @Column(name = "timestamp")
    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PayorderEntity that = (PayorderEntity) o;

        if (id != that.id) return false;
        if (payid != that.payid) return false;
        if (transactionid != null ? !transactionid.equals(that.transactionid) : that.transactionid != null)
            return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (timestamp != null ? !timestamp.equals(that.timestamp) : that.timestamp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + (transactionid != null ? transactionid.hashCode() : 0);
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + payid;
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }
}
