package Json;

import module.Dispatch.DispatchRule;

import java.util.List;

public class ExperienceJson {
    private int key;  //表格的id
    private int status;//1初始状态 2进行状态 3完成状态
    private long finishTime;//开始时间戳
    private List<Integer> petId;
    private List<DispatchRule> rules;

    public int getKey() {
        return key;
    }

    public void setKey(int key) {
        this.key = key;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public long getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(long finishTime) {
        this.finishTime = finishTime;
    }

    public List<Integer> getPetId() {
        return petId;
    }

    public void setPetId(List<Integer> petId) {
        this.petId = petId;
    }

    public List<DispatchRule> getRules() {
        return rules;
    }

    public void setRules(List<DispatchRule> rules) {
        this.rules = rules;
    }

    @Override
    public String toString() {
        return "ExperienceJson{" +
                "key=" + key +
                ", status=" + status +
                ", finishTime=" + finishTime +
                ", petId=" + petId +
                ", rules=" + rules +
                '}';
    }
}
