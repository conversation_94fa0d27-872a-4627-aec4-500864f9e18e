package module.activity.LimitedTime;

import entities.LimitedTimeRewardTaskEntity;
import manager.MySql;
import org.hibernate.Session;

import java.util.List;

public class LimitedTimeRewardTaskDao {
    private static LimitedTimeRewardTaskDao inst = null;
    public static LimitedTimeRewardTaskDao getInstance() {
        if (inst == null) {
            inst = new LimitedTimeRewardTaskDao();
        }
        return inst;
    }

    public void insert(LimitedTimeRewardTaskEntity entity){
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public void update(LimitedTimeRewardTaskEntity entity) {
        Session session = MySql.getSession();
        session.update(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public LimitedTimeRewardTaskEntity GetTaskEntity(String uid, String taskType) {
        StringBuffer stringBuffer = new StringBuffer("from LimitedTimeRewardTaskEntity where uid='").append(uid).append("'")
                .append(" and type='").append(taskType).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (LimitedTimeRewardTaskEntity) entity;
    }

    public List<Object> GetAll(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from LimitedTimeRewardTaskEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());
        return list;
    }

    public void Delete() {
        StringBuffer stringBuffer = new StringBuffer("Delete from LimitedTimeRewardTaskEntity");
        MySql.updateSomes(stringBuffer.toString());
    }
}
