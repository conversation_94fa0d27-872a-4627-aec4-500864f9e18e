package module.mail;

import com.google.protobuf.InvalidProtocolBufferException;
import entities.NoticeEntity;
import protocol.NoticeData;

public class NoticeDao {
    private static NoticeDao inst;

    public static NoticeDao getInstance() {
        if (inst == null) {
            inst = new NoticeDao();
        }
        return inst;
    }

    public byte[] operateNotice(byte[] bytes, String uid) {
        NoticeData.RequestOperateNotice requestOperateNotice = null;
        NoticeData.ReportNewNotice.Builder builder = NoticeData.ReportNewNotice.newBuilder();

        //反序列化操作
        try {
            requestOperateNotice = NoticeData.RequestOperateNotice.parseFrom(bytes);
            int nid = requestOperateNotice.getNid();
            //   builder.setNewNotice(nid);


        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        return builder.build().toByteArray();
    }


    public NoticeData.Notice entityToPb(NoticeEntity entity) {
        NoticeData.Notice.Builder builder = NoticeData.Notice.newBuilder();
        builder.setNid(entity.getNid());
//        builder.setSender(entity.getSender());
        builder.setOwner(entity.getOwner());
        //  builder.setTitle(entity.getTitle());
        builder.setContent(entity.getContent());
        //  builder.setTimeStamp(entity.getTimestamp());
        String sender, owner, title, content = null;
        /*if ((sender = entity.getSender()) != null) {
            builder.setSender(sender);
        }*/
        if ((owner = entity.getOwner()) != null) {
            builder.setOwner(owner);
        }
//        if ((title = entity.getTitle()) != null) {
//            builder.setTitle(title);
//        }
        if ((content = entity.getContent()) != null) {
            builder.setContent(content);
        }
        return builder.build();
    }
}
