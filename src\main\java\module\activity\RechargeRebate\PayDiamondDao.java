package module.activity.RechargeRebate;

import entities.PayDiamondEntity;
import manager.MySql;

public class PayDiamondDao {
    private static PayDiamondDao inst = null;
    public static PayDiamondDao getInstance() {
        if (inst == null) {
            inst = new PayDiamondDao();
        }
        return inst;
    }

    public PayDiamondEntity GetPlayerPayDiamond(String uid){
        PayDiamondEntity payDiamondEntity = null;
        StringBuffer sql = new StringBuffer("from PayDiamondEntity where uid='").append(uid).append("'");
        payDiamondEntity = (PayDiamondEntity) MySql.queryForOne(sql.toString());

        if (payDiamondEntity == null){
            payDiamondEntity = new PayDiamondEntity();

            payDiamondEntity.setUid(uid);
            payDiamondEntity.setDiamond_num(0L);

            MySql.insert(payDiamondEntity);
        }
        return payDiamondEntity;
    }

    public PayDiamondEntity PlayerPayDiamond(String uid, Long diamondNum) {
        PayDiamondEntity payDiamondEntity = null;
        StringBuffer sql = new StringBuffer("from PayDiamondEntity where uid='").append(uid).append("'");
        payDiamondEntity = (PayDiamondEntity) MySql.queryForOne(sql.toString());

        if (payDiamondEntity == null){
            payDiamondEntity = new PayDiamondEntity();

            payDiamondEntity.setUid(uid);
            payDiamondEntity.setDiamond_num(diamondNum);

            MySql.insert(payDiamondEntity);
        }else {
            payDiamondEntity.setDiamond_num(payDiamondEntity.getDiamond_num() + diamondNum);
            MySql.update(payDiamondEntity);
        }
        return payDiamondEntity;
    }
}
