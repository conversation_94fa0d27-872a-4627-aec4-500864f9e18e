package module.daily_task;

import entities.CompleteDailyTaskEntity;
import entities.DailyTaskEntity;
import manager.MySql;
import org.hibernate.Session;

import java.util.List;

public class CompleteDailtTaskDao {
    private static CompleteDailtTaskDao inst = null;
    public static CompleteDailtTaskDao getInstance() {
        if (inst == null) {
            inst = new CompleteDailtTaskDao();
        }
        return inst;
    }

    public void insert(CompleteDailyTaskEntity entity){
        Session session = MySql.getSession();
        session.save(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public void update(CompleteDailyTaskEntity entity) {
        Session session = MySql.getSession();
        session.update(entity);
        session.beginTransaction().commit();
        session.close();
    }

    public CompleteDailyTaskEntity GetCompleteDailyTaskEntity(String uid, String dailyId) {
        StringBuffer stringBuffer = new StringBuffer("from CompleteDailyTaskEntity where uid='").append(uid).append("'")
                .append(" and daily_task_id='").append(dailyId).append("'");
        Object entity = MySql.queryForOne(stringBuffer.toString());
        return (CompleteDailyTaskEntity) entity;
    }

    public List<Object> GetAllCompleteDailyTaskEntity(String uid) {
        StringBuffer stringBuffer = new StringBuffer("from CompleteDailyTaskEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuffer.toString());
        return list;
    }
}
