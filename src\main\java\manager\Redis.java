package manager;

import common.SuperConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ProtoData;
import redis.clients.jedis.*;

import java.io.*;
import java.util.*;

/**
 * Created by nara on 2016/12/5.
 */
public class Redis {
    private static Redis inst = null;

    public static Redis getInstance(){
        if (inst == null) {
            inst = new Redis();
        }
        if (jedisPool == null){
            initialPool();
        }
        return inst;
    }

    private static Logger log = LoggerFactory.getLogger(Redis.class);
    private static String pwd = null;
    private static int rindex;

    private static final String LOCK_SUCCESS = "OK";
    private static final String SET_IF_NOT_EXIST = "nx";
    private static final String SET_WITH_EXPIRE_TIME = "ex";

    private static final Long RELEASE_SUCCESS = 1L;

    public static Jedis getJedis(int index){
        Jedis jedis = null;
        try{
            jedis = jedisPool.getResource();
            jedis.auth(pwd);
            if (index == -1){
                jedis.select(rindex);
            }else {
                jedis.select(index);
            }

        }catch (Exception e){
            return null;
        }
        return jedis;
    }

    public static Jedis getJedisConfig(int index){
        Jedis jedis = null;
        try{
            jedis = jedisPool.getResource();
            jedis.auth(pwd);
            if (index == -1){
                //  jedis.select(rindex);
                jedis.select(0);
            }else {
                jedis.select(index);
            }

        }catch (Exception e){
            return null;
        }
        return jedis;
    }
    public static void destory(Jedis jedis){
        if(jedis != null){
            jedis.close();
        }
    }

    private static JedisPool jedisPool = null;
    private static void initialPool() {
        try {
            Properties prop = new Properties();
            InputStream in = null;
//            if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_DEBUG){
//                in = new BufferedInputStream(new FileInputStream("src/main/resources/config.properties"));
//            }else if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_RELEASE){
                in = Properties.class.getResourceAsStream("/config.properties");
//            }
            prop.load(in);

            JedisPoolConfig config = new JedisPoolConfig();
            config.setMaxTotal(Integer.parseInt(prop.getProperty("redis.maxTotal")));
            config.setMaxIdle(Integer.parseInt(prop.getProperty("redis.maxIdle")));
            config.setMaxWaitMillis(Integer.parseInt(prop.getProperty("redis.maxWaitMillis")));
            jedisPool = new JedisPool(config, prop.getProperty("redis.host"),Integer.parseInt(prop.getProperty("redis.port")));
            pwd = prop.getProperty("redis.pwd");
            rindex = Integer.parseInt(prop.getProperty("redis.index"));
            if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_PLOTTER || SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_MISSION_PLOTTER){
                rindex = 7;
            }
            in.close();
//            /// System.out.println("Server is running: "+jedisPool);
//            /// System.out.println("Server is running:  rindex"+rindex);
        }catch (Exception e){
            e.printStackTrace();
        }

    }

    /**
     * 尝试获取分布式锁
     * @param lockKey 锁
     * @param requestId 请求标识
     * @param expireTime 超期时间
     * @return 是否获取成功
     */
    public static boolean tryGetDistributedLock( String lockKey, String requestId, int expireTime) {
        Jedis jedis = getJedis(-1);
        String result = jedis.set(lockKey, requestId, SET_IF_NOT_EXIST, SET_WITH_EXPIRE_TIME, expireTime);

        if (LOCK_SUCCESS.equals(result)) {
            return true;
        }
        destory(jedis);
        return false;
    }

    /**
     * 释放分布式锁
     * @param lockKey 锁
     * @param requestId 请求标识
     * @return 是否释放成功
     */
    public static boolean releaseDistributedLock( String lockKey, String requestId) {
        Jedis jedis = getJedis(-1);
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Object result = jedis.eval(script, Collections.singletonList(lockKey), Collections.singletonList(requestId));

        if (RELEASE_SUCCESS.equals(result)) {
            return true;
        }
        destory(jedis);
        return false;
    }

    public String hget(String key,String field){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            String value = jedis.hget(key, field);
            // if(value==null){
            //     jedis = getJedis(0);
            //   value = jedis.hget(key, field);
            // }
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            String value = jedis.hget(key, field);

            return value;
        }finally{
            destory(jedis);
        }
    }

    public Map<String,String> hgetAll(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            Map<String,String> value = jedis.hgetAll(key);
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            Map<String,String> value = jedis.hgetAll(key);
            return value;
        }finally{
            destory(jedis);
        }
    }

//     public Map<String,String> hgetAll2(String key){
//         Jedis jedis = null;
//         try {
//             jedis = getJedis(0);
//             Map<String,String> value = jedis.hgetAll(key);
//             return value;
//         } catch (Exception e) {
// //            e.printStackTrace();
//             while (jedis == null){
//                 try {
//                     Thread.sleep(2000);
//                 }catch (Exception e1){
// //                    /// System.out.println("jedis thread error!!!");
//                     log.error("jedis thread error!!!");
//                 }
//                 jedis = getJedis(-1);
//             }
//             Map<String,String> value = jedis.hgetAll(key);
//             return value;
//         }finally{
//             destory(jedis);
//         }
//     }
    public long hset(String key,String field,String val){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long value = jedis.hset(key, field ,val);
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long value = jedis.hset(key, field, val);
            return value;
        }finally{
            destory(jedis);
        }
    }

    public String hmset(String key,Map<String,String> map){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            String value = jedis.hmset(key, map);
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            String value = jedis.hmset(key, map);
            return value;
        }finally{
            destory(jedis);
        }
    }

    public long lpush(String key,String val){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long value = jedis.lpush(key, val);
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long value = jedis.lpush(key, val);
            return value;
        }finally{
            destory(jedis);
        }
    }

    public long llen(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long value = jedis.llen(key);
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long value = jedis.llen(key);
            return value;
        }finally{
            destory(jedis);
        }
    }

    public List<String> lrange(String key,long start,long end){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            List<String> value = jedis.lrange(key,start,end);
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            List<String> value = jedis.lrange(key,start,end);
            return value;
        }finally{
            destory(jedis);
        }
    }

    public static long hdel(String key,String... var2){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long d = jedis.hdel(key,var2);
            return d;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long d = jedis.hdel(key,var2);
            return d;
        }finally{
            destory(jedis);
        }
    }

    public static long del(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long d = jedis.del(key);
            return d;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long d = jedis.del(key);
            return d;
        }finally{
            destory(jedis);
        }
    }

    public static long expire(String key,int second){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long ex = jedis.expire(key, second);
            return ex;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long ex = jedis.expire(key, second);
            return ex;
        }finally{
            destory(jedis);
        }
    }

    public static long persist(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long ex = jedis.persist(key);
            return ex;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long ex = jedis.persist(key);
            return ex;
        }finally{
            destory(jedis);
        }
    }

    public static Set<String> keys(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            Set<String> set = jedis.keys(key);
            return set;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            Set<String> set = jedis.keys(key);
            return set;
        }finally{
            destory(jedis);
        }
    }

    public static boolean exists(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            boolean bo = jedis.exists(key);
            return bo;
        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            boolean bo = jedis.exists(key);
            return bo;
        }finally{
            destory(jedis);
        }
    }

    public static Pipeline pipelined(){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            Pipeline pipeline = jedis.pipelined();
            return pipeline;
        } catch (Exception e) {
//            e.printStackTrace();
//            return null;
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            Pipeline pipeline = jedis.pipelined();
            return pipeline;
        }finally{
            destory(jedis);
        }
    }

    public static long ttl(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            long t = jedis.ttl(key);
            return t;
        } catch (Exception e) {
//            e.printStackTrace();
//            return -101;
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            long t = jedis.ttl(key);
            return t;
        }finally{
            destory(jedis);
        }
    }

    public static String getExcelInfo(String excel,String id,String name){
        Redis jedis = getInstance();
        StringBuffer key = new StringBuffer(excel).append(":").append(id);
        String s = jedis.hget(key.toString(),name);
        return s;
    }

    public static Map<String,String> getExcelMap(String excel,int id){
        Redis jedis = getInstance();
        StringBuffer key = new StringBuffer(excel).append(":").append(id);
        Map<String,String> map = jedis.hgetAll(key.toString());
        return map;
    }

    public static void setDelTimeForUserInfoInRedis(String uid){
        Redis jedis = getInstance();
        jedis.expire("user:"+uid,SuperConfig.EXPIRETIMESECOND);
    }

    public static int getRoleStatus(String uid){
        Redis jedis = getInstance();
        String string = jedis.hget("role:" + uid, "status");
        return Integer.parseInt(string);
    }

    public static void setRoleOnLine(String uid){
        Redis jedis = getInstance();
        jedis.hset("role:"+uid,"status","1");
        removeTTL(uid);
        String name = jedis.hget("role:" + uid,"name");
        ReportManager.reportUpdateFriendStatus(uid,1);
        ReportManager.reportSystemBroadcastFriend(uid,2116,name);
    }

    public static void setRoleOffLine(String uid){
        Redis jedis = getInstance();
        jedis.hset("role:" + uid, "status", "0");
        String name = jedis.hget("role:" + uid,"name");
        ReportManager.reportUpdateFriendStatus(uid,0);
        ReportManager.reportSystemBroadcastFriend(uid,2117,name);
    }

    public static int getRoleServer(String uid){
        try {
            Redis jedis = getInstance();
            String userId = jedis.hget("role:"+uid,"userid");
            String string = jedis.hget("user:" + userId, "serverid");
            if(string==null){
                return -1;
            }
            return Integer.parseInt(string);
        }catch (Exception e){
            e.printStackTrace();
            return -1;
        }
    }

    public static void setDelTimeForRoleAllInfoInRedis(String uid){
        Redis jedis = getInstance();
        jedis.expire("nowmission:" + uid,SuperConfig.EXPIRETIMESECOND);
        Iterator<String> iterator = jedis.keys("missioninfo:"+uid+"*").iterator();
        while (iterator.hasNext()){
            String key = iterator.next();
            jedis.expire(key,SuperConfig.EXPIRETIMESECOND);
        }
        jedis.expire("role:"+uid,SuperConfig.EXPIRETIMESECOND);

        jedis.expire("roleitem:"+uid+"#0",SuperConfig.EXPIRETIMESECOND);
        jedis.expire("roleitem:"+uid+"#1",SuperConfig.EXPIRETIMESECOND);
        jedis.expire("roleitem:"+uid+"#2",SuperConfig.EXPIRETIMESECOND);
        jedis.expire("roleitem:"+uid+"#3",SuperConfig.EXPIRETIMESECOND);

//        jedis.expire("rolemission:"+uid,SuperConfig.EXPIRETIMESECOND);
        jedis.expire("roletask:"+uid,SuperConfig.EXPIRETIMESECOND);
        jedis.expire("rolemessages:"+uid,SuperConfig.EXPIRETIMESECOND);
        jedis.expire("rolefriends:"+uid,SuperConfig.EXPIRETIMESECOND);

        jedis.expire("roledress:"+uid+"#1",SuperConfig.EXPIRETIMESECOND);
        jedis.expire("roledress:"+uid+"#2",SuperConfig.EXPIRETIMESECOND);
        jedis.expire("roledress:"+uid+"#3",SuperConfig.EXPIRETIMESECOND);
        //删除键值
        String robotPassenger="rolePassenger"+uid;
        if(Redis.exists(robotPassenger)){
            Redis.del(robotPassenger);
        }
        String userId = jedis.hget("role:"+uid,"userid");
        setDelTimeForUserInfoInRedis(userId);
    }

    private static void removeTTL(String uid){
        Redis jedis = getInstance();
        jedis.persist("role:" + uid);
        jedis.persist("roleitem:" + uid +"#0");
        jedis.persist("roleitem:" + uid +"#1");
        jedis.persist("roleitem:" + uid +"#2");
        jedis.persist("roleitem:" + uid +"#3");
//        jedis.persist("rolemission:" + uid);
        jedis.persist("roletask:" + uid);
        jedis.persist("rolemessages:" + uid);
        jedis.persist("rolefriends:"+uid);
        String userId = jedis.hget("role:" + uid, "userid");
        jedis.persist("user:"+userId);
//        String roleId = jedis.hget("role:" + uid, "roleId");
        jedis.persist("roledress:"+uid+"#1");
        jedis.persist("roledress:"+uid+"#2");
        jedis.persist("roledress:"+uid+"#3");

        jedis.persist("nowmission:" + uid);
        Iterator<String> iterator = jedis.keys("missioninfo:"+uid+"*").iterator();
        while (iterator.hasNext()){
            String key = iterator.next();
            jedis.persist(key);
        }
    }

    public static boolean judgeRoleCacheExit(String uid){
        Redis jedis = getInstance();
        Map<String,String> map = jedis.hgetAll("role:"+uid);
        return map.size() == 0 ? false : true;
    }
    public static Set<String> Scan() {
        Set<String> KeySet = new HashSet<String>();
        Jedis jedis = null;
        try {
            jedis = getJedis(-1);
            //  jedis.scan();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            destory(jedis);
        }
        return  KeySet;
    }


    public static String getExcelInfofromRedis0(String excel,String id,String name){
        Redis jedis = getInstance();
        StringBuffer key = new StringBuffer(excel).append(":").append(id);
        String s = jedis.hgetfromRedis0(key.toString(),name);
        return s;
    }

    public String hgetfromRedis0(String key,String field){
        Jedis jedis = null;
        try {
            jedis = getJedis(0);
            String value = jedis.hget(key, field);
            if(value==null){
                jedis = getJedis(0);
                value = jedis.hget(key, field);
            }
            return value;
        } catch (Exception e) {
//            e.printStackTrace();
            while (jedis == null){
                try {
                    Thread.sleep(2000);
                }catch (Exception e1){
//                    /// System.out.println("jedis thread error!!!");
                    log.error("jedis thread error!!!");
                }
                jedis = getJedis(-1);
            }
            String value = jedis.hget(key, field);

            return value;
        }finally{
            destory(jedis);
        }
    }

    // keys方法会阻塞，scan增量式迭代
    public static Set<String> scan(String pattern){
        Jedis jedis = null;
        Set<String> set=new HashSet<String>();
        ScanParams scanParams=new ScanParams();
        scanParams.match(pattern).count(1000);
        String cursor=ScanParams.SCAN_POINTER_START;
        try{
            jedis= getJedis(-1);
            while(true){
                ScanResult<String> result =jedis.scan(cursor,scanParams);
                cursor=result.getStringCursor();
                List<String> list=result.getResult();
                set.addAll(list);
                if("0".equals(cursor)){
                    break;
                }
            }

        }catch (Exception e){
            e.printStackTrace();
        }
        return  set;
    }
}