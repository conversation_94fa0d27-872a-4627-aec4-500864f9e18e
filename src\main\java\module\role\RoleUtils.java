package module.role;


import common.SuperConfig;
import common.*;
import entities.RoleEntity;
import manager.MySql;

public class RoleUtils {
    public static RoleEntity addRoleExp(RoleEntity role, int addExp) {
        int nowLv = role.getLv();
        int nowExp = role.getExp();
        try {
            RoleExpConfig roleExpConfig = (RoleExpConfig) SuperConfig.getCongifObject(SuperConfig.roleExpConfig, nowLv);
            int lvNeedExp = roleExpConfig.getToLevelNeedExp();
            nowExp = addExp + nowExp;
            int uplv=0;

            while (nowExp>=lvNeedExp){
                nowExp-=lvNeedExp;
                nowLv++;
                uplv++;
                roleExpConfig= (RoleExpConfig) SuperConfig.getCongifObject(SuperConfig.roleExpConfig,nowLv);
                if (roleExpConfig==null){
                    nowExp=0;
                    break;//最高级
                }
                lvNeedExp=roleExpConfig.getToLevelNeedExp();
                System.err.println("=======On Update Next Level Need Exp:"+lvNeedExp);

                System.err.println("=======Now Level:"+nowLv);
            }
            role.setExp(nowExp);
            role.setLv(nowLv);
            if (uplv!=0){
                roleUpLv(role,uplv);
            }else {
                MySql.update(role);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return role;
    }
    private static void roleUpLv(RoleEntity role, int upLv) {
        MySql.update(role);
    }

}
