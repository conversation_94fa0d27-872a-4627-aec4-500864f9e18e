package common;

@ExcelConfigObject(key = "petskill")
public class PetSkillConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "spmaxlv")
    private int spmaxlv;
  /*  private int spneednum;
    private int targettype;
    private int skillrange;
    private int skillscope;
    private int calculation;
    private int autolesion;
    private int  rebound;
    private int  absorb;
    private int  minatknum;
    private int  maxatknum;
    private int  skilltype;
    private int  skillclass;
    private int  skillpower;
    private int  skillhit;
    private int skillcrt;
    private int bufftarget;
    private int buffprobability;
    private int buffid;
    private int buffroundnum;
    private int statetarget;
    private int stateprobability;
    private int statetype;
    private int stateupordown;
    private int statenumtype;
    private int statenum;
    private int stateroundnum;
    private int hittime;
    private int attacktime;*/

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getSpmaxlv() {
        return spmaxlv;
    }

    public void setSpmaxlv(int spmaxlv) {
        this.spmaxlv = spmaxlv;
    }
}
