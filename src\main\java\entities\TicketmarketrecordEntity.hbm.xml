<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>

    <class name="entities.TicketmarketrecordEntity" table="ticketmarketrecord" schema="" catalog="super_star_fruit">
        <id name="id" column="id">
            <generator class="native"/>
        </id>
        <property name="type" column="type"/>
        <property name="uid" column="uid"/>
        <property name="goodsId" column="goodsId"/>
        <property name="nums" column="nums"/>
        <property name="priece" column="priece"/>
        <property name="time" column="time"/>
    </class>
</hibernate-mapping>
