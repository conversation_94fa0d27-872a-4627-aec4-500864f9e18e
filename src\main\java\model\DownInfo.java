package model;

/**
 * Created by nara on 2017/12/21.
 */
public class DownInfo {
    private int id;
    private PointInfo point;
    private int type;//1小球 2技能

    public DownInfo() {
        point = new PointInfo();
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public PointInfo getPoint() {
        return point;
    }

    public void setPoint(PointInfo point) {
        this.point = point;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
}
