// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: sex.proto

package protocol;

public final class SexData {
  private SexData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RoleSexOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 sex = 1;
    /**
     * <code>required int32 sex = 1;</code>
     */
    boolean hasSex();
    /**
     * <code>required int32 sex = 1;</code>
     */
    int getSex();
  }
  /**
   * Protobuf type {@code protocol.RoleSex}
   */
  public static final class RoleSex extends
      com.google.protobuf.GeneratedMessage
      implements RoleSexOrBuilder {
    // Use RoleSex.newBuilder() to construct.
    private RoleSex(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RoleSex(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RoleSex defaultInstance;
    public static RoleSex getDefaultInstance() {
      return defaultInstance;
    }

    public RoleSex getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RoleSex(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              sex_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SexData.internal_static_protocol_RoleSex_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SexData.internal_static_protocol_RoleSex_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SexData.RoleSex.class, protocol.SexData.RoleSex.Builder.class);
    }

    public static com.google.protobuf.Parser<RoleSex> PARSER =
        new com.google.protobuf.AbstractParser<RoleSex>() {
      public RoleSex parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RoleSex(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RoleSex> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 sex = 1;
    public static final int SEX_FIELD_NUMBER = 1;
    private int sex_;
    /**
     * <code>required int32 sex = 1;</code>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 sex = 1;</code>
     */
    public int getSex() {
      return sex_;
    }

    private void initFields() {
      sex_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, sex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, sex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SexData.RoleSex parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.RoleSex parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.RoleSex parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.RoleSex parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.RoleSex parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.RoleSex parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SexData.RoleSex parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SexData.RoleSex parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SexData.RoleSex parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.RoleSex parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SexData.RoleSex prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RoleSex}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SexData.RoleSexOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SexData.internal_static_protocol_RoleSex_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SexData.internal_static_protocol_RoleSex_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SexData.RoleSex.class, protocol.SexData.RoleSex.Builder.class);
      }

      // Construct using protocol.SexData.RoleSex.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        sex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SexData.internal_static_protocol_RoleSex_descriptor;
      }

      public protocol.SexData.RoleSex getDefaultInstanceForType() {
        return protocol.SexData.RoleSex.getDefaultInstance();
      }

      public protocol.SexData.RoleSex build() {
        protocol.SexData.RoleSex result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SexData.RoleSex buildPartial() {
        protocol.SexData.RoleSex result = new protocol.SexData.RoleSex(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sex_ = sex_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SexData.RoleSex) {
          return mergeFrom((protocol.SexData.RoleSex)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SexData.RoleSex other) {
        if (other == protocol.SexData.RoleSex.getDefaultInstance()) return this;
        if (other.hasSex()) {
          setSex(other.getSex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasSex()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SexData.RoleSex parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SexData.RoleSex) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 sex = 1;
      private int sex_ ;
      /**
       * <code>required int32 sex = 1;</code>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 sex = 1;</code>
       */
      public int getSex() {
        return sex_;
      }
      /**
       * <code>required int32 sex = 1;</code>
       */
      public Builder setSex(int value) {
        bitField0_ |= 0x00000001;
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sex = 1;</code>
       */
      public Builder clearSex() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sex_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RoleSex)
    }

    static {
      defaultInstance = new RoleSex(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RoleSex)
  }

  public interface RequestRoleSexOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 sex = 1;
    /**
     * <code>required int32 sex = 1;</code>
     *
     * <pre>
     *性别
     * </pre>
     */
    boolean hasSex();
    /**
     * <code>required int32 sex = 1;</code>
     *
     * <pre>
     *性别
     * </pre>
     */
    int getSex();
  }
  /**
   * Protobuf type {@code protocol.RequestRoleSex}
   *
   * <pre>
   *1212
   * </pre>
   */
  public static final class RequestRoleSex extends
      com.google.protobuf.GeneratedMessage
      implements RequestRoleSexOrBuilder {
    // Use RequestRoleSex.newBuilder() to construct.
    private RequestRoleSex(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestRoleSex(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestRoleSex defaultInstance;
    public static RequestRoleSex getDefaultInstance() {
      return defaultInstance;
    }

    public RequestRoleSex getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestRoleSex(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              sex_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SexData.internal_static_protocol_RequestRoleSex_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SexData.internal_static_protocol_RequestRoleSex_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SexData.RequestRoleSex.class, protocol.SexData.RequestRoleSex.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestRoleSex> PARSER =
        new com.google.protobuf.AbstractParser<RequestRoleSex>() {
      public RequestRoleSex parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestRoleSex(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestRoleSex> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 sex = 1;
    public static final int SEX_FIELD_NUMBER = 1;
    private int sex_;
    /**
     * <code>required int32 sex = 1;</code>
     *
     * <pre>
     *性别
     * </pre>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 sex = 1;</code>
     *
     * <pre>
     *性别
     * </pre>
     */
    public int getSex() {
      return sex_;
    }

    private void initFields() {
      sex_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, sex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, sex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SexData.RequestRoleSex parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SexData.RequestRoleSex parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SexData.RequestRoleSex parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.RequestRoleSex parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SexData.RequestRoleSex prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestRoleSex}
     *
     * <pre>
     *1212
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SexData.RequestRoleSexOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SexData.internal_static_protocol_RequestRoleSex_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SexData.internal_static_protocol_RequestRoleSex_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SexData.RequestRoleSex.class, protocol.SexData.RequestRoleSex.Builder.class);
      }

      // Construct using protocol.SexData.RequestRoleSex.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        sex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SexData.internal_static_protocol_RequestRoleSex_descriptor;
      }

      public protocol.SexData.RequestRoleSex getDefaultInstanceForType() {
        return protocol.SexData.RequestRoleSex.getDefaultInstance();
      }

      public protocol.SexData.RequestRoleSex build() {
        protocol.SexData.RequestRoleSex result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SexData.RequestRoleSex buildPartial() {
        protocol.SexData.RequestRoleSex result = new protocol.SexData.RequestRoleSex(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sex_ = sex_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SexData.RequestRoleSex) {
          return mergeFrom((protocol.SexData.RequestRoleSex)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SexData.RequestRoleSex other) {
        if (other == protocol.SexData.RequestRoleSex.getDefaultInstance()) return this;
        if (other.hasSex()) {
          setSex(other.getSex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasSex()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SexData.RequestRoleSex parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SexData.RequestRoleSex) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 sex = 1;
      private int sex_ ;
      /**
       * <code>required int32 sex = 1;</code>
       *
       * <pre>
       *性别
       * </pre>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 sex = 1;</code>
       *
       * <pre>
       *性别
       * </pre>
       */
      public int getSex() {
        return sex_;
      }
      /**
       * <code>required int32 sex = 1;</code>
       *
       * <pre>
       *性别
       * </pre>
       */
      public Builder setSex(int value) {
        bitField0_ |= 0x00000001;
        sex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sex = 1;</code>
       *
       * <pre>
       *性别
       * </pre>
       */
      public Builder clearSex() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sex_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestRoleSex)
    }

    static {
      defaultInstance = new RequestRoleSex(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestRoleSex)
  }

  public interface ResponseRoleSexOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // required .protocol.RoleSex sex = 2;
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    boolean hasSex();
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    protocol.SexData.RoleSex getSex();
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    protocol.SexData.RoleSexOrBuilder getSexOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseRoleSex}
   *
   * <pre>
   *2212
   * </pre>
   */
  public static final class ResponseRoleSex extends
      com.google.protobuf.GeneratedMessage
      implements ResponseRoleSexOrBuilder {
    // Use ResponseRoleSex.newBuilder() to construct.
    private ResponseRoleSex(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseRoleSex(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseRoleSex defaultInstance;
    public static ResponseRoleSex getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseRoleSex getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseRoleSex(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              protocol.SexData.RoleSex.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = sex_.toBuilder();
              }
              sex_ = input.readMessage(protocol.SexData.RoleSex.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sex_);
                sex_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SexData.internal_static_protocol_ResponseRoleSex_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SexData.internal_static_protocol_ResponseRoleSex_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SexData.ResponseRoleSex.class, protocol.SexData.ResponseRoleSex.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseRoleSex> PARSER =
        new com.google.protobuf.AbstractParser<ResponseRoleSex>() {
      public ResponseRoleSex parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseRoleSex(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseRoleSex> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required .protocol.RoleSex sex = 2;
    public static final int SEX_FIELD_NUMBER = 2;
    private protocol.SexData.RoleSex sex_;
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    public protocol.SexData.RoleSex getSex() {
      return sex_;
    }
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    public protocol.SexData.RoleSexOrBuilder getSexOrBuilder() {
      return sex_;
    }

    private void initFields() {
      errorId_ = 0;
      sex_ = protocol.SexData.RoleSex.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getSex().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, sex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, sex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SexData.ResponseRoleSex parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SexData.ResponseRoleSex parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SexData.ResponseRoleSex parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.ResponseRoleSex parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SexData.ResponseRoleSex prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseRoleSex}
     *
     * <pre>
     *2212
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SexData.ResponseRoleSexOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SexData.internal_static_protocol_ResponseRoleSex_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SexData.internal_static_protocol_ResponseRoleSex_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SexData.ResponseRoleSex.class, protocol.SexData.ResponseRoleSex.Builder.class);
      }

      // Construct using protocol.SexData.ResponseRoleSex.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getSexFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (sexBuilder_ == null) {
          sex_ = protocol.SexData.RoleSex.getDefaultInstance();
        } else {
          sexBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SexData.internal_static_protocol_ResponseRoleSex_descriptor;
      }

      public protocol.SexData.ResponseRoleSex getDefaultInstanceForType() {
        return protocol.SexData.ResponseRoleSex.getDefaultInstance();
      }

      public protocol.SexData.ResponseRoleSex build() {
        protocol.SexData.ResponseRoleSex result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SexData.ResponseRoleSex buildPartial() {
        protocol.SexData.ResponseRoleSex result = new protocol.SexData.ResponseRoleSex(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (sexBuilder_ == null) {
          result.sex_ = sex_;
        } else {
          result.sex_ = sexBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SexData.ResponseRoleSex) {
          return mergeFrom((protocol.SexData.ResponseRoleSex)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SexData.ResponseRoleSex other) {
        if (other == protocol.SexData.ResponseRoleSex.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSex()) {
          mergeSex(other.getSex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasSex()) {
          
          return false;
        }
        if (!getSex().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SexData.ResponseRoleSex parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SexData.ResponseRoleSex) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required .protocol.RoleSex sex = 2;
      private protocol.SexData.RoleSex sex_ = protocol.SexData.RoleSex.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.SexData.RoleSex, protocol.SexData.RoleSex.Builder, protocol.SexData.RoleSexOrBuilder> sexBuilder_;
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public protocol.SexData.RoleSex getSex() {
        if (sexBuilder_ == null) {
          return sex_;
        } else {
          return sexBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder setSex(protocol.SexData.RoleSex value) {
        if (sexBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sex_ = value;
          onChanged();
        } else {
          sexBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder setSex(
          protocol.SexData.RoleSex.Builder builderForValue) {
        if (sexBuilder_ == null) {
          sex_ = builderForValue.build();
          onChanged();
        } else {
          sexBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder mergeSex(protocol.SexData.RoleSex value) {
        if (sexBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              sex_ != protocol.SexData.RoleSex.getDefaultInstance()) {
            sex_ =
              protocol.SexData.RoleSex.newBuilder(sex_).mergeFrom(value).buildPartial();
          } else {
            sex_ = value;
          }
          onChanged();
        } else {
          sexBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder clearSex() {
        if (sexBuilder_ == null) {
          sex_ = protocol.SexData.RoleSex.getDefaultInstance();
          onChanged();
        } else {
          sexBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public protocol.SexData.RoleSex.Builder getSexBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSexFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public protocol.SexData.RoleSexOrBuilder getSexOrBuilder() {
        if (sexBuilder_ != null) {
          return sexBuilder_.getMessageOrBuilder();
        } else {
          return sex_;
        }
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.SexData.RoleSex, protocol.SexData.RoleSex.Builder, protocol.SexData.RoleSexOrBuilder> 
          getSexFieldBuilder() {
        if (sexBuilder_ == null) {
          sexBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.SexData.RoleSex, protocol.SexData.RoleSex.Builder, protocol.SexData.RoleSexOrBuilder>(
                  sex_,
                  getParentForChildren(),
                  isClean());
          sex_ = null;
        }
        return sexBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseRoleSex)
    }

    static {
      defaultInstance = new ResponseRoleSex(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseRoleSex)
  }

  public interface RequestGetSexOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetSex}
   *
   * <pre>
   *1213
   * </pre>
   */
  public static final class RequestGetSex extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetSexOrBuilder {
    // Use RequestGetSex.newBuilder() to construct.
    private RequestGetSex(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetSex(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetSex defaultInstance;
    public static RequestGetSex getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetSex getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetSex(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SexData.internal_static_protocol_RequestGetSex_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SexData.internal_static_protocol_RequestGetSex_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SexData.RequestGetSex.class, protocol.SexData.RequestGetSex.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetSex> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetSex>() {
      public RequestGetSex parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetSex(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetSex> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SexData.RequestGetSex parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.RequestGetSex parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.RequestGetSex parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.RequestGetSex parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.RequestGetSex parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.RequestGetSex parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SexData.RequestGetSex parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SexData.RequestGetSex parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SexData.RequestGetSex parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.RequestGetSex parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SexData.RequestGetSex prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetSex}
     *
     * <pre>
     *1213
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SexData.RequestGetSexOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SexData.internal_static_protocol_RequestGetSex_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SexData.internal_static_protocol_RequestGetSex_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SexData.RequestGetSex.class, protocol.SexData.RequestGetSex.Builder.class);
      }

      // Construct using protocol.SexData.RequestGetSex.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SexData.internal_static_protocol_RequestGetSex_descriptor;
      }

      public protocol.SexData.RequestGetSex getDefaultInstanceForType() {
        return protocol.SexData.RequestGetSex.getDefaultInstance();
      }

      public protocol.SexData.RequestGetSex build() {
        protocol.SexData.RequestGetSex result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SexData.RequestGetSex buildPartial() {
        protocol.SexData.RequestGetSex result = new protocol.SexData.RequestGetSex(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SexData.RequestGetSex) {
          return mergeFrom((protocol.SexData.RequestGetSex)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SexData.RequestGetSex other) {
        if (other == protocol.SexData.RequestGetSex.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SexData.RequestGetSex parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SexData.RequestGetSex) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetSex)
    }

    static {
      defaultInstance = new RequestGetSex(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetSex)
  }

  public interface ResponseGetSexOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     */
    int getErrorId();

    // required .protocol.RoleSex sex = 2;
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    boolean hasSex();
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    protocol.SexData.RoleSex getSex();
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    protocol.SexData.RoleSexOrBuilder getSexOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.ResponseGetSex}
   *
   * <pre>
   *2213
   * </pre>
   */
  public static final class ResponseGetSex extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetSexOrBuilder {
    // Use ResponseGetSex.newBuilder() to construct.
    private ResponseGetSex(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetSex(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetSex defaultInstance;
    public static ResponseGetSex getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetSex getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetSex(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              protocol.SexData.RoleSex.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) == 0x00000002)) {
                subBuilder = sex_.toBuilder();
              }
              sex_ = input.readMessage(protocol.SexData.RoleSex.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(sex_);
                sex_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.SexData.internal_static_protocol_ResponseGetSex_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.SexData.internal_static_protocol_ResponseGetSex_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.SexData.ResponseGetSex.class, protocol.SexData.ResponseGetSex.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetSex> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetSex>() {
      public ResponseGetSex parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetSex(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetSex> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required .protocol.RoleSex sex = 2;
    public static final int SEX_FIELD_NUMBER = 2;
    private protocol.SexData.RoleSex sex_;
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    public boolean hasSex() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    public protocol.SexData.RoleSex getSex() {
      return sex_;
    }
    /**
     * <code>required .protocol.RoleSex sex = 2;</code>
     */
    public protocol.SexData.RoleSexOrBuilder getSexOrBuilder() {
      return sex_;
    }

    private void initFields() {
      errorId_ = 0;
      sex_ = protocol.SexData.RoleSex.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSex()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getSex().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeMessage(2, sex_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, sex_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.SexData.ResponseGetSex parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.SexData.ResponseGetSex parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.SexData.ResponseGetSex parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.SexData.ResponseGetSex parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.SexData.ResponseGetSex prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetSex}
     *
     * <pre>
     *2213
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.SexData.ResponseGetSexOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.SexData.internal_static_protocol_ResponseGetSex_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.SexData.internal_static_protocol_ResponseGetSex_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.SexData.ResponseGetSex.class, protocol.SexData.ResponseGetSex.Builder.class);
      }

      // Construct using protocol.SexData.ResponseGetSex.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getSexFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (sexBuilder_ == null) {
          sex_ = protocol.SexData.RoleSex.getDefaultInstance();
        } else {
          sexBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.SexData.internal_static_protocol_ResponseGetSex_descriptor;
      }

      public protocol.SexData.ResponseGetSex getDefaultInstanceForType() {
        return protocol.SexData.ResponseGetSex.getDefaultInstance();
      }

      public protocol.SexData.ResponseGetSex build() {
        protocol.SexData.ResponseGetSex result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.SexData.ResponseGetSex buildPartial() {
        protocol.SexData.ResponseGetSex result = new protocol.SexData.ResponseGetSex(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        if (sexBuilder_ == null) {
          result.sex_ = sex_;
        } else {
          result.sex_ = sexBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.SexData.ResponseGetSex) {
          return mergeFrom((protocol.SexData.ResponseGetSex)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.SexData.ResponseGetSex other) {
        if (other == protocol.SexData.ResponseGetSex.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSex()) {
          mergeSex(other.getSex());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasSex()) {
          
          return false;
        }
        if (!getSex().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.SexData.ResponseGetSex parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.SexData.ResponseGetSex) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required .protocol.RoleSex sex = 2;
      private protocol.SexData.RoleSex sex_ = protocol.SexData.RoleSex.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.SexData.RoleSex, protocol.SexData.RoleSex.Builder, protocol.SexData.RoleSexOrBuilder> sexBuilder_;
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public boolean hasSex() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public protocol.SexData.RoleSex getSex() {
        if (sexBuilder_ == null) {
          return sex_;
        } else {
          return sexBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder setSex(protocol.SexData.RoleSex value) {
        if (sexBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          sex_ = value;
          onChanged();
        } else {
          sexBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder setSex(
          protocol.SexData.RoleSex.Builder builderForValue) {
        if (sexBuilder_ == null) {
          sex_ = builderForValue.build();
          onChanged();
        } else {
          sexBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder mergeSex(protocol.SexData.RoleSex value) {
        if (sexBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002) &&
              sex_ != protocol.SexData.RoleSex.getDefaultInstance()) {
            sex_ =
              protocol.SexData.RoleSex.newBuilder(sex_).mergeFrom(value).buildPartial();
          } else {
            sex_ = value;
          }
          onChanged();
        } else {
          sexBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public Builder clearSex() {
        if (sexBuilder_ == null) {
          sex_ = protocol.SexData.RoleSex.getDefaultInstance();
          onChanged();
        } else {
          sexBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public protocol.SexData.RoleSex.Builder getSexBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSexFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      public protocol.SexData.RoleSexOrBuilder getSexOrBuilder() {
        if (sexBuilder_ != null) {
          return sexBuilder_.getMessageOrBuilder();
        } else {
          return sex_;
        }
      }
      /**
       * <code>required .protocol.RoleSex sex = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.SexData.RoleSex, protocol.SexData.RoleSex.Builder, protocol.SexData.RoleSexOrBuilder> 
          getSexFieldBuilder() {
        if (sexBuilder_ == null) {
          sexBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.SexData.RoleSex, protocol.SexData.RoleSex.Builder, protocol.SexData.RoleSexOrBuilder>(
                  sex_,
                  getParentForChildren(),
                  isClean());
          sex_ = null;
        }
        return sexBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetSex)
    }

    static {
      defaultInstance = new ResponseGetSex(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetSex)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RoleSex_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RoleSex_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestRoleSex_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestRoleSex_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseRoleSex_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseRoleSex_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetSex_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetSex_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetSex_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetSex_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tsex.proto\022\010protocol\032\013proto.proto\032\nitem" +
      ".proto\032\ntask.proto\032\014friend.proto\032\014common" +
      ".proto\032\nmail.proto\032\tpet.proto\"\026\n\007RoleSex" +
      "\022\013\n\003sex\030\001 \002(\005\"\035\n\016RequestRoleSex\022\013\n\003sex\030\001" +
      " \002(\005\"B\n\017ResponseRoleSex\022\017\n\007errorId\030\001 \002(\005" +
      "\022\036\n\003sex\030\002 \002(\0132\021.protocol.RoleSex\"\017\n\rRequ" +
      "estGetSex\"A\n\016ResponseGetSex\022\017\n\007errorId\030\001" +
      " \002(\005\022\036\n\003sex\030\002 \002(\0132\021.protocol.RoleSexB\tB\007" +
      "SexData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RoleSex_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RoleSex_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RoleSex_descriptor,
              new java.lang.String[] { "Sex", });
          internal_static_protocol_RequestRoleSex_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestRoleSex_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestRoleSex_descriptor,
              new java.lang.String[] { "Sex", });
          internal_static_protocol_ResponseRoleSex_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ResponseRoleSex_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseRoleSex_descriptor,
              new java.lang.String[] { "ErrorId", "Sex", });
          internal_static_protocol_RequestGetSex_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestGetSex_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetSex_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetSex_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_ResponseGetSex_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetSex_descriptor,
              new java.lang.String[] { "ErrorId", "Sex", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
          protocol.ItemData.getDescriptor(),
          protocol.TaskData.getDescriptor(),
          protocol.FriendData.getDescriptor(),
          protocol.CommonData.getDescriptor(),
          protocol.MailData.getDescriptor(),
          protocol.PetData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
