package entities;


import javax.persistence.*;

@Entity
@Table(name = "taskrecord", schema = "", catalog = "super_star_fruit")
public class TaskRecordEntity {
    private int id;
    private String uid;
    private int taskid;
    private String reward;


    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "taskid")
    public int getTaskid() {
        return taskid;
    }

    public void setTaskid(int taskid) {
        this.taskid = taskid;
    }

    @Basic
    @Column(name = "reward")

    public String getReward() {
        return reward;
    }

    public void setReward(String reward) {
        this.reward = reward;
    }

    @Override
    public String toString() {
        return "TaskRecordEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", taskid=" + taskid +
                ", reward='" + reward + '\'' +
                '}';
    }
}
