// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: proto.proto

package protocol;

public final class ProtoData {
  private ProtoData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  /**
   * Protobuf enum {@code protocol.CToS}
   */
  public enum CToS
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REQUESTSERVER = 1000;</code>
     *
     * <pre>
     *游戏区服列表
     * </pre>
     */
    REQUESTSERVER(0, 1000),
    /**
     * <code>REQUESTREGISTER = 1005;</code>
     *
     * <pre>
     *注册
     * </pre>
     */
    REQUESTREGISTER(1, 1005),
    /**
     * <code>REQUESTLOGIN = 1001;</code>
     *
     * <pre>
     *登入
     * </pre>
     */
    REQUESTLOGIN(2, 1001),
    /**
     * <code>REQUESTCREATE = 1002;</code>
     *
     * <pre>
     *创建角色
     * </pre>
     */
    REQUESTCREATE(3, 1002),
    /**
     * <code>REQUESTROLESEX = 1212;</code>
     *
     * <pre>
     *创建性别
     * </pre>
     */
    REQUESTROLESEX(4, 1212),
    /**
     * <code>REQUESTGETSEX = 1213;</code>
     *
     * <pre>
     *获得性别
     * </pre>
     */
    REQUESTGETSEX(5, 1213),
    /**
     * <code>REQUESTSETINFO = 1003;</code>
     *
     * <pre>
     *设置名字
     * </pre>
     */
    REQUESTSETINFO(6, 1003),
    /**
     * <code>REQUESTCHOOSEROLE = 1004;</code>
     *
     * <pre>
     *选择角色
     * </pre>
     */
    REQUESTCHOOSEROLE(7, 1004),
    /**
     * <code>REQUESTBUYITEM = 1006;</code>
     *
     * <pre>
     *购买物品
     * </pre>
     */
    REQUESTBUYITEM(8, 1006),
    /**
     * <code>REQUESTUSEITEM = 1007;</code>
     *
     * <pre>
     *使用物品
     * </pre>
     */
    REQUESTUSEITEM(9, 1007),
    /**
     * <code>REQUESTOPENBAGCELL = 1008;</code>
     *
     * <pre>
     *解锁背包格子
     * </pre>
     */
    REQUESTOPENBAGCELL(10, 1008),
    /**
     * <code>REQUESTCOMPOSE = 1009;</code>
     *
     * <pre>
     *合成
     * </pre>
     */
    REQUESTCOMPOSE(11, 1009),
    /**
     * <code>REQUESTBEGINMISSION = 1010;</code>
     *
     * <pre>
     *开始闯关
     * </pre>
     */
    REQUESTBEGINMISSION(12, 1010),
    /**
     * <code>REQUESTCOUNTMISSION = 1011;</code>
     *
     * <pre>
     *结算闯关
     * </pre>
     */
    REQUESTCOUNTMISSION(13, 1011),
    /**
     * <code>REQUESTBEGINENDLESS = 1012;</code>
     *
     * <pre>
     *开始无尽
     * </pre>
     */
    REQUESTBEGINENDLESS(14, 1012),
    /**
     * <code>REQUESTCOUNTENDLESS = 1013;</code>
     *
     * <pre>
     *结算无尽
     * </pre>
     */
    REQUESTCOUNTENDLESS(15, 1013),
    /**
     * <code>REQUESTUPDATENEWUSER = 1014;</code>
     *
     * <pre>
     *更新新手引导
     * </pre>
     */
    REQUESTUPDATENEWUSER(16, 1014),
    /**
     * <code>REQUESTWECHATLOGIN = 1015;</code>
     *
     * <pre>
     *微信登录
     * </pre>
     */
    REQUESTWECHATLOGIN(17, 1015),
    /**
     * <code>REQUESTAPPLELOGIN = 1016;</code>
     *
     * <pre>
     *苹果登录
     * </pre>
     */
    REQUESTAPPLELOGIN(18, 1016),
    /**
     * <code>REQUESTQQLOGIN = 1017;</code>
     *
     * <pre>
     *QQ登录
     * </pre>
     */
    REQUESTQQLOGIN(19, 1017),
    /**
     * <code>REQUESTYSDKLOGIN = 1880;</code>
     *
     * <pre>
     *YSDK登录
     * </pre>
     */
    REQUESTYSDKLOGIN(20, 1880),
    /**
     * <code>REQUESTHUAWEILOGIN = 1881;</code>
     *
     * <pre>
     *华为登录	
     * </pre>
     */
    REQUESTHUAWEILOGIN(21, 1881),
    /**
     * <code>REQUESTGOOGLELOGIN = 1882;</code>
     *
     * <pre>
     *google登录	
     * </pre>
     */
    REQUESTGOOGLELOGIN(22, 1882),
    /**
     * <code>REQUESTSENDCHAT = 1020;</code>
     *
     * <pre>
     *发送聊天
     * </pre>
     */
    REQUESTSENDCHAT(23, 1020),
    /**
     * <code>REQUESTFINISHTASK = 1030;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    REQUESTFINISHTASK(24, 1030),
    /**
     * <code>REQUESTOPERATEVIEW = 1050;</code>
     *
     * <pre>
     *操作界面
     * </pre>
     */
    REQUESTOPERATEVIEW(25, 1050),
    /**
     * <code>REQUESTBUYCLOTHING = 1051;</code>
     *
     * <pre>
     *购买衣服
     * </pre>
     */
    REQUESTBUYCLOTHING(26, 1051),
    /**
     * <code>REQUESTCHANGECUPBOARD = 1052;</code>
     *
     * <pre>
     *衣柜操作
     * </pre>
     */
    REQUESTCHANGECUPBOARD(27, 1052),
    /**
     * <code>REQUESTCHANGEDRESS = 1053;</code>
     *
     * <pre>
     *换装扮
     * </pre>
     */
    REQUESTCHANGEDRESS(28, 1053),
    /**
     * <code>REQUESTCUPBOARDTOBODY = 1054;</code>
     *
     * <pre>
     *衣柜到当前装
     * </pre>
     */
    REQUESTCUPBOARDTOBODY(29, 1054),
    /**
     * <code>REQUESTSTARBITOMONEY = 1055;</code>
     *
     * <pre>
     *星币换金币
     * </pre>
     */
    REQUESTSTARBITOMONEY(30, 1055),
    /**
     * <code>REQUESTOPERATEFRIEND = 1060;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    REQUESTOPERATEFRIEND(31, 1060),
    /**
     * <code>REQUESTOPERATEFRIEND1 = 1201;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    REQUESTOPERATEFRIEND1(32, 1201),
    /**
     * <code>REQUESTOPERATEMESSAGE = 1061;</code>
     *
     * <pre>
     *操作消息
     * </pre>
     */
    REQUESTOPERATEMESSAGE(33, 1061),
    /**
     * <code>REQUESTPETHATCH = 1466;</code>
     *
     * <pre>
     *培育金钱
     * </pre>
     */
    REQUESTPETHATCH(34, 1466),
    /**
     * <code>REQUESTRECOMMENDFRIEND = 1064;</code>
     *
     * <pre>
     *推荐好友
     * </pre>
     */
    REQUESTRECOMMENDFRIEND(35, 1064),
    /**
     * <code>REQUESTSTATIONLOCATION = 1066;</code>
     *
     * <pre>
     *车站走动
     * </pre>
     */
    REQUESTSTATIONLOCATION(36, 1066),
    /**
     * <code>REQUESTGIVEPRESENT = 1067;</code>
     *
     * <pre>
     *赠送礼物
     * </pre>
     */
    REQUESTGIVEPRESENT(37, 1067),
    /**
     * <code>REQUESTACCUSATION = 1068;</code>
     *
     * <pre>
     *举报
     * </pre>
     */
    REQUESTACCUSATION(38, 1068),
    /**
     * <code>REQUESTGETRANK = 1070;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    REQUESTGETRANK(39, 1070),
    /**
     * <code>REQUESTGETRANKING = 1095;</code>
     *
     * <pre>
     *排
     * </pre>
     */
    REQUESTGETRANKING(40, 1095),
    /**
     * <code>REQUESTGETTOTALENDLESS = 1071;</code>
     *
     * <pre>
     *获取无尽总积分奖励
     * </pre>
     */
    REQUESTGETTOTALENDLESS(41, 1071),
    /**
     * <code>REQUESTJOINROOM = 1080;</code>
     *
     * <pre>
     *进入房间
     * </pre>
     */
    REQUESTJOINROOM(42, 1080),
    /**
     * <code>REQUESTCREATEROOM = 1081;</code>
     *
     * <pre>
     *创建房间
     * </pre>
     */
    REQUESTCREATEROOM(43, 1081),
    /**
     * <code>REQUESTLEAVEROOM = 1082;</code>
     *
     * <pre>
     *离开房间
     * </pre>
     */
    REQUESTLEAVEROOM(44, 1082),
    /**
     * <code>REQUESTUPDATEROOMINFO = 1083;</code>
     *
     * <pre>
     *更新房间信息
     * </pre>
     */
    REQUESTUPDATEROOMINFO(45, 1083),
    /**
     * <code>REQUESTUPDATEROOMROLE = 1084;</code>
     *
     * <pre>
     *更新房间内玩家信息
     * </pre>
     */
    REQUESTUPDATEROOMROLE(46, 1084),
    /**
     * <code>REQUESTJOINFIGHTHALL = 1085;</code>
     *
     * <pre>
     *请求进出多人战斗大厅
     * </pre>
     */
    REQUESTJOINFIGHTHALL(47, 1085),
    /**
     * <code>REQUESTSTARTINROOM = 1086;</code>
     *
     * <pre>
     *房主开始游戏
     * </pre>
     */
    REQUESTSTARTINROOM(48, 1086),
    /**
     * <code>REQUESTRECONNECTION = 1200;</code>
     *
     * <pre>
     *断线重连
     * </pre>
     */
    REQUESTRECONNECTION(49, 1200),
    /**
     * <code>REQUESTCHOOSEPAY = 1500;</code>
     *
     * <pre>
     *选择购买内容
     * </pre>
     */
    REQUESTCHOOSEPAY(50, 1500),
    /**
     * <code>REQUESTSUBMITPAYBACK = 1501;</code>
     *
     * <pre>
     *支付
     * </pre>
     */
    REQUESTSUBMITPAYBACK(51, 1501),
    /**
     * <code>REQUESTGOOGLEPAY = 1502;</code>
     *
     * <pre>
     *google支付
     * </pre>
     */
    REQUESTGOOGLEPAY(52, 1502),
    /**
     * <code>REQUESTWECHATPAY = 1503;</code>
     *
     * <pre>
     *微信支付
     * </pre>
     */
    REQUESTWECHATPAY(53, 1503),
    /**
     * <code>REQUESTALIPAY = 1504;</code>
     *
     * <pre>
     *支付宝支付
     * </pre>
     */
    REQUESTALIPAY(54, 1504),
    /**
     * <code>REQUESTCONFIRMHUAWEIPURCHASE = 1505;</code>
     *
     * <pre>
     *华为确认支付
     * </pre>
     */
    REQUESTCONFIRMHUAWEIPURCHASE(55, 1505),
    /**
     * <code>REQUESTYSDKBALANCE = 1506;</code>
     *
     * <pre>
     *YSDK确认支付
     * </pre>
     */
    REQUESTYSDKBALANCE(56, 1506),
    /**
     * <code>REQUESTBEGINHEADBALL = 1018;</code>
     *
     * <pre>
     *请求顶球模式
     * </pre>
     */
    REQUESTBEGINHEADBALL(57, 1018),
    /**
     * <code>REQUESTCOUNTHEADBALL = 1019;</code>
     *
     * <pre>
     *请求结算顶球模式
     * </pre>
     */
    REQUESTCOUNTHEADBALL(58, 1019),
    /**
     * <code>REQUESTADPLAY = 1602;</code>
     *
     * <pre>
     *请求播放广告
     * </pre>
     */
    REQUESTADPLAY(59, 1602),
    /**
     * <code>REQUESTADNUN = 1603;</code>
     *
     * <pre>
     *请求播放广告次数
     * </pre>
     */
    REQUESTADNUN(60, 1603),
    /**
     * <code>REQUESTCOUNTGOLD = 1700;</code>
     *
     * <pre>
     *金币模式请求结算
     * </pre>
     */
    REQUESTCOUNTGOLD(61, 1700),
    /**
     * <code>REQUESTBEGINGOLD = 1701;</code>
     *
     * <pre>
     *金币模式请求刷球
     * </pre>
     */
    REQUESTBEGINGOLD(62, 1701),
    /**
     * <code>REQUESTBEGINDOWN = 1702;</code>
     *
     * <pre>
     *落球模式
     * </pre>
     */
    REQUESTBEGINDOWN(63, 1702),
    /**
     * <code>REQUESTCOUNTDOWN = 1704;</code>
     *
     * <pre>
     *请求落球结算
     * </pre>
     */
    REQUESTCOUNTDOWN(64, 1704),
    /**
     * <code>REQUESTBEGINSTACK = 1705;</code>
     *
     * <pre>
     *堆球模式		
     * </pre>
     */
    REQUESTBEGINSTACK(65, 1705),
    /**
     * <code>REQUESTCOUNTSTACK = 1709;</code>
     *
     * <pre>
     *请求堆球结算
     * </pre>
     */
    REQUESTCOUNTSTACK(66, 1709),
    /**
     * <code>REQUESTCOUNTLOTTO = 1703;</code>
     *
     * <pre>
     *请求合成道具
     * </pre>
     */
    REQUESTCOUNTLOTTO(67, 1703),
    /**
     * <code>REQUESTSOURCESMACHINE = 1708;</code>
     *
     * <pre>
     *请求绿机器能源机合成
     * </pre>
     */
    REQUESTSOURCESMACHINE(68, 1708),
    /**
     * <code>REQURESTMAKEITEM = 1706;</code>
     *
     * <pre>
     *请求培养皿物品
     * </pre>
     */
    REQURESTMAKEITEM(69, 1706),
    /**
     * <code>REQUESTPRODUCT = 1707;</code>
     *
     * <pre>
     *请求成品的收获     
     * </pre>
     */
    REQUESTPRODUCT(70, 1707),
    /**
     * <code>REQUESTVISITOROPERATION = 1900;</code>
     *
     * <pre>
     *游客登录相关操作  	
     * </pre>
     */
    REQUESTVISITOROPERATION(71, 1900),
    /**
     * <code>REQUESTAPPROVAL = 1920;</code>
     *
     * <pre>
     *点赞请求
     * </pre>
     */
    REQUESTAPPROVAL(72, 1920),
    /**
     * <code>REQUESTAUTHENTICATION = 1921;</code>
     *
     * <pre>
     *身份认证
     * </pre>
     */
    REQUESTAUTHENTICATION(73, 1921),
    /**
     * <code>REQUESTGETAPPROVAL = 1922;</code>
     *
     * <pre>
     *获得当前玩家受到点赞数
     * </pre>
     */
    REQUESTGETAPPROVAL(74, 1922),
    /**
     * <code>REQUESTSTARTGAMECOPY = 1122;</code>
     *
     * <pre>
     *请求进入每日关卡（任务金币）
     * </pre>
     */
    REQUESTSTARTGAMECOPY(75, 1122),
    /**
     * <code>REQUESTCOUNTGAMECOPY = 1123;</code>
     *
     * <pre>
     *请求每日关卡结算
     * </pre>
     */
    REQUESTCOUNTGAMECOPY(76, 1123),
    /**
     * <code>REQUESTGETLUCKYITEM = 1124;</code>
     *
     * <pre>
     *车站界面幸运物品获取
     * </pre>
     */
    REQUESTGETLUCKYITEM(77, 1124),
    /**
     * <code>REQUESTFINISHACTIVITIES = 1126;</code>
     *
     * <pre>
     *请求活动完成
     * </pre>
     */
    REQUESTFINISHACTIVITIES(78, 1126),
    /**
     * <code>REQUESTMARKETINFORMATION = 1129;</code>
     *
     * <pre>
     *请求交易市场信息
     * </pre>
     */
    REQUESTMARKETINFORMATION(79, 1129),
    /**
     * <code>REQUESTGOODSOPERATE = 1130;</code>
     *
     * <pre>
     * 交易市场商品买入卖出
     * </pre>
     */
    REQUESTGOODSOPERATE(80, 1130),
    /**
     * <code>REQUESTTICKTEEXCHANGE = 1131;</code>
     *
     * <pre>
     * 票券兑换
     * </pre>
     */
    REQUESTTICKTEEXCHANGE(81, 1131),
    /**
     * <code>REQUESTQUERYROLEINFORMATION = 1135;</code>
     *
     * <pre>
     * 查询玩家角色信息
     * </pre>
     */
    REQUESTQUERYROLEINFORMATION(82, 1135),
    /**
     * <code>REQUESTWAITITEM = 1136;</code>
     *
     * <pre>
     *挂机获得物品
     * </pre>
     */
    REQUESTWAITITEM(83, 1136),
    /**
     * <code>REQUESTUPWAITITEMLEVEL = 1137;</code>
     *
     * <pre>
     *提升等物品功能背包等級
     * </pre>
     */
    REQUESTUPWAITITEMLEVEL(84, 1137),
    /**
     * <code>REQUESTGETWAITITEM = 1138;</code>
     *
     * <pre>
     *活得等物品功能物品
     * </pre>
     */
    REQUESTGETWAITITEM(85, 1138),
    /**
     * <code>REQUESTHOUSE = 1600;</code>
     *
     * <pre>
     * 装饰功能模块信息
     * </pre>
     */
    REQUESTHOUSE(86, 1600),
    /**
     * <code>REQUESTHOUSEPART = 1605;</code>
     *
     * <pre>
     *保存玩家房间装饰记录
     * </pre>
     */
    REQUESTHOUSEPART(87, 1605),
    /**
     * <code>REQUESTBUYFURNITURE = 1670;</code>
     *
     * <pre>
     * 买家具
     * </pre>
     */
    REQUESTBUYFURNITURE(88, 1670),
    /**
     * <code>REQUESTOPERATEMAIL = 1110;</code>
     *
     * <pre>
     * 操作邮件
     * </pre>
     */
    REQUESTOPERATEMAIL(89, 1110),
    /**
     * <code>REQUESTOPERATENOTICE = 1111;</code>
     *
     * <pre>
     *操作公告
     * </pre>
     */
    REQUESTOPERATENOTICE(90, 1111),
    /**
     * <code>REQUESTGETPETSINFO = 1210;</code>
     *
     * <pre>
     *获取玩家宠物信息
     * </pre>
     */
    REQUESTGETPETSINFO(91, 1210),
    /**
     * <code>REQUESTGETEQUIP = 1215;</code>
     *
     * <pre>
     *获取玩家装备信息
     * </pre>
     */
    REQUESTGETEQUIP(92, 1215),
    /**
     * <code>REQUESTOPERATEPET = 1211;</code>
     *
     * <pre>
     *操作宠物
     * </pre>
     */
    REQUESTOPERATEPET(93, 1211),
    /**
     * <code>REQUESTOPERATEROLE = 1311;</code>
     *
     * <pre>
     *操作角色
     * </pre>
     */
    REQUESTOPERATEROLE(94, 1311),
    /**
     * <code>REQUESTCHOICEEQUIP = 1218;</code>
     *
     * <pre>
     *选择装备
     * </pre>
     */
    REQUESTCHOICEEQUIP(95, 1218),
    /**
     * <code>REQUESTOPERATEEQUIP = 1216;</code>
     *
     * <pre>
     *操作装备
     * </pre>
     */
    REQUESTOPERATEEQUIP(96, 1216),
    /**
     * <code>REQUESTCHANGEEQUIP = 1217;</code>
     *
     * <pre>
     *换装备
     * </pre>
     */
    REQUESTCHANGEEQUIP(97, 1217),
    /**
     * <code>REQUESTBINDIDCARD = 1113;</code>
     *
     * <pre>
     *  绑定身份证
     * </pre>
     */
    REQUESTBINDIDCARD(98, 1113),
    /**
     * <code>REQUESTGETDISPATCHINFOS = 1120;</code>
     *
     * <pre>
     *  获得派遣任务信息
     * </pre>
     */
    REQUESTGETDISPATCHINFOS(99, 1120),
    /**
     * <code>REQUESTOPERATEDISPATCHTASK = 1121;</code>
     *
     * <pre>
     *    操作派遣任务
     * </pre>
     */
    REQUESTOPERATEDISPATCHTASK(100, 1121),
    /**
     * <code>REQUESTRAFFLEPOOL = 1150;</code>
     *
     * <pre>
     *获取抽奖池
     * </pre>
     */
    REQUESTRAFFLEPOOL(101, 1150),
    /**
     * <code>REQUESTGETEVENTINFO = 1160;</code>
     *
     * <pre>
     *事件信息
     * </pre>
     */
    REQUESTGETEVENTINFO(102, 1160),
    /**
     * <code>REQUESTFLUSHEVENT = 1161;</code>
     *
     * <pre>
     *刷新事件
     * </pre>
     */
    REQUESTFLUSHEVENT(103, 1161),
    /**
     * <code>REQUESTCOUNTBATTLE = 1170;</code>
     *
     * <pre>
     * 怪物副本结算
     * </pre>
     */
    REQUESTCOUNTBATTLE(104, 1170),
    /**
     * <code>REQUESTPLAYERITEMS = 1180;</code>
     *
     * <pre>
     *获取玩家物品数据
     * </pre>
     */
    REQUESTPLAYERITEMS(105, 1180),
    /**
     * <code>REQUESTUPPETSKILL = 1190;</code>
     *
     * <pre>
     *强化宠物技能等级
     * </pre>
     */
    REQUESTUPPETSKILL(106, 1190),
    /**
     * <code>REQUESTPETEXP = 1580;</code>
     *
     * <pre>
     *获取宠物经验
     * </pre>
     */
    REQUESTPETEXP(107, 1580),
    /**
     * <code>REQUESTBALCKMARKET = 1250;</code>
     *
     * <pre>
     *获取黑市商店信息
     * </pre>
     */
    REQUESTBALCKMARKET(108, 1250),
    /**
     * <code>REQUESTBUYBALCKMARKETITEM = 1251;</code>
     *
     * <pre>
     * 购买黑市商店物品
     * </pre>
     */
    REQUESTBUYBALCKMARKETITEM(109, 1251),
    /**
     * <code>REQUESTPETFORMATION = 1261;</code>
     *
     * <pre>
     *请求宠物编队
     * </pre>
     */
    REQUESTPETFORMATION(110, 1261),
    /**
     * <code>REQUESTGETPETFORMATION = 1262;</code>
     *
     * <pre>
     *获取宠物编队信息
     * </pre>
     */
    REQUESTGETPETFORMATION(111, 1262),
    /**
     * <code>REQUESTPETCOMPOSE = 1265;</code>
     *
     * <pre>
     * 宠物合成
     * </pre>
     */
    REQUESTPETCOMPOSE(112, 1265),
    /**
     * <code>REQUESTPETBREEDINOFRMATION = 1272;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    REQUESTPETBREEDINOFRMATION(113, 1272),
    /**
     * <code>REQUESTPETBREEDOPERATION = 1273;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    REQUESTPETBREEDOPERATION(114, 1273),
    /**
     * <code>REQUESTPETBREED = 1274;</code>
     *
     * <pre>
     *	登录返回正在孵化的蛋
     * </pre>
     */
    REQUESTPETBREED(115, 1274),
    /**
     * <code>REQUESTGETPETBREEDINFO = 1270;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    REQUESTGETPETBREEDINFO(116, 1270),
    /**
     * <code>REQUESTOPERATEPETBREED = 1271;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    REQUESTOPERATEPETBREED(117, 1271),
    /**
     * <code>REQUESTGETPETGROWINFO = 1300;</code>
     *
     * <pre>
     *获取正在进化的宠物链表
     * </pre>
     */
    REQUESTGETPETGROWINFO(118, 1300),
    /**
     * <code>REQUESTPETEVOLUTION = 1301;</code>
     *
     * <pre>
     *操作宠物进行进化
     * </pre>
     */
    REQUESTPETEVOLUTION(119, 1301),
    /**
     * <code>REQUESTPETBREAKTHROUGH = 1302;</code>
     *
     * <pre>
     *宠物突破
     * </pre>
     */
    REQUESTPETBREAKTHROUGH(120, 1302),
    /**
     * <code>REQUESTGETEXPERIENCES = 1280;</code>
     *
     * <pre>
     *获取探险任务的信息
     * </pre>
     */
    REQUESTGETEXPERIENCES(121, 1280),
    /**
     * <code>REQUESTFLUSHEXPERIENCES = 1281;</code>
     *
     * <pre>
     *刷新探险任务
     * </pre>
     */
    REQUESTFLUSHEXPERIENCES(122, 1281),
    /**
     * <code>REQUESTOPERATEEXPERIENCES = 1282;</code>
     *
     * <pre>
     *操作探险任务
     * </pre>
     */
    REQUESTOPERATEEXPERIENCES(123, 1282),
    /**
     * <code>REQUESTITEMCOMPOSE = 1283;</code>
     *
     * <pre>
     *道具合成  
     * </pre>
     */
    REQUESTITEMCOMPOSE(124, 1283),
    /**
     * <code>REQUESTPHYSICAL = 1046;</code>
     *
     * <pre>
     *消耗物品
     * </pre>
     */
    REQUESTPHYSICAL(125, 1046),
    /**
     * <code>REQUESTGETROLEEXP = 1318;</code>
     *
     * <pre>
     *得到人物经验
     * </pre>
     */
    REQUESTGETROLEEXP(126, 1318),
    /**
     * <code>REQUESTOPERATIONFRIENDS = 1303;</code>
     *
     * <pre>
     *操作好友申请信息
     * </pre>
     */
    REQUESTOPERATIONFRIENDS(127, 1303),
    /**
     * <code>REQUESTGETMAP = 1370;</code>
     *
     * <pre>
     *地图
     * </pre>
     */
    REQUESTGETMAP(128, 1370),
    /**
     * <code>REQUESTUPDMAP = 1371;</code>
     *
     * <pre>
     *修改地图
     * </pre>
     */
    REQUESTUPDMAP(129, 1371),
    /**
     * <code>REQUESTREMOVEPET = 1285;</code>
     *
     * <pre>
     *回收宠物
     * </pre>
     */
    REQUESTREMOVEPET(130, 1285),
    /**
     * <code>REQUESTDAILYTANSKS = 1290;</code>
     *
     * <pre>
     *进行任务
     * </pre>
     */
    REQUESTDAILYTANSKS(131, 1290),
    /**
     * <code>REQUESTCOMPLETEDAILYTASKS = 1291;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    REQUESTCOMPLETEDAILYTASKS(132, 1291),
    /**
     * <code>REQUESTTASK = 1292;</code>
     *
     * <pre>
     *进入游戏获取任务进度
     * </pre>
     */
    REQUESTTASK(133, 1292),
    /**
     * <code>REQUESTACHIEVEMENTTYPE = 1293;</code>
     *
     * <pre>
     *进行成就
     * </pre>
     */
    REQUESTACHIEVEMENTTYPE(134, 1293),
    /**
     * <code>REQUESTACHIEVEMENTIS = 1294;</code>
     *
     * <pre>
     *完成成就
     * </pre>
     */
    REQUESTACHIEVEMENTIS(135, 1294),
    /**
     * <code>REQUESTACHIEVEMENT = 1295;</code>
     *
     * <pre>
     *进入游戏获取成就进度
     * </pre>
     */
    REQUESTACHIEVEMENT(136, 1295),
    /**
     * <code>REQUESTADDITEM = 1056;</code>
     *
     * <pre>
     *添加道具
     * </pre>
     */
    REQUESTADDITEM(137, 1056),
    /**
     * <code>REQUESTSYNTHESIS = 1057;</code>
     *
     * <pre>
     *合成道具
     * </pre>
     */
    REQUESTSYNTHESIS(138, 1057),
    /**
     * <code>REQUESTPLAYERRECOMMEND = 1298;</code>
     *
     * <pre>
     *推荐玩家
     * </pre>
     */
    REQUESTPLAYERRECOMMEND(139, 1298),
    /**
     * <code>REQUESTFRIENDSAPPLY = 1299;</code>
     *
     * <pre>
     *上线刷新好友申请信息
     * </pre>
     */
    REQUESTFRIENDSAPPLY(140, 1299),
    /**
     * <code>REQUESTSENDOUTINFORMATION = 1304;</code>
     *
     * <pre>
     *发送消息
     * </pre>
     */
    REQUESTSENDOUTINFORMATION(141, 1304),
    /**
     * <code>REQUESTCHATRECORD = 1305;</code>
     *
     * <pre>
     *上线获取玩家聊天记录
     * </pre>
     */
    REQUESTCHATRECORD(142, 1305),
    /**
     * <code>REQUESTUPDATETYPE = 1306;</code>
     *
     * <pre>
     *修改状态已读未读删除
     * </pre>
     */
    REQUESTUPDATETYPE(143, 1306),
    /**
     * <code>REQUESTRANK = 1307;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    REQUESTRANK(144, 1307),
    /**
     * <code>REQUESTLEVEL = 1308;</code>
     *
     * <pre>
     *关卡    
     * </pre>
     */
    REQUESTLEVEL(145, 1308),
    /**
     * <code>REQUESTLEVELD = 1309;</code>
     *
     * <pre>
     * 操作关卡数据
     * </pre>
     */
    REQUESTLEVELD(146, 1309),
    /**
     * <code>REQUESTPlont = 1320;</code>
     *
     * <pre>
     *获取剧情1
     * </pre>
     */
    REQUESTPlont(147, 1320),
    /**
     * <code>REQUESTChap = 1321;</code>
     *
     * <pre>
     *返回剧情1
     * </pre>
     */
    REQUESTChap(148, 1321),
    /**
     * <code>REQUESTGetEquipA = 1322;</code>
     *
     * <pre>
     *创建装备
     * </pre>
     */
    REQUESTGetEquipA(149, 1322),
    /**
     * <code>REQUESTDDDEquip = 1323;</code>
     *
     * <pre>
     *获取装备
     * </pre>
     */
    REQUESTDDDEquip(150, 1323),
    /**
     * <code>REQUESTEquipF = 1324;</code>
     *
     * <pre>
     *接受装备
     * </pre>
     */
    REQUESTEquipF(151, 1324),
    /**
     * <code>REQUESTbossTiem = 1325;</code>
     *
     * <pre>
     *世界boss
     * </pre>
     */
    REQUESTbossTiem(152, 1325),
    /**
     * <code>REQUESTbossdear = 1326;</code>
     *
     * <pre>
     *世界血量存活
     * </pre>
     */
    REQUESTbossdear(153, 1326),
    /**
     * <code>REQUESTPVPPetOperate = 1330;</code>
     *
     * <pre>
     * PVP宠物存储
     * </pre>
     */
    REQUESTPVPPetOperate(154, 1330),
    /**
     * <code>REQUESTPVPBaseData = 1331;</code>
     *
     * <pre>
     * PVP个人信息
     * </pre>
     */
    REQUESTPVPBaseData(155, 1331),
    /**
     * <code>REQUESTPVPBattle = 1332;</code>
     *
     * <pre>
     * PVP战斗
     * </pre>
     */
    REQUESTPVPBattle(156, 1332),
    /**
     * <code>REQUESTPVPBattleResult = 1333;</code>
     *
     * <pre>
     * PVP战斗 输赢
     * </pre>
     */
    REQUESTPVPBattleResult(157, 1333),
    /**
     * <code>REQUESTPVPBATTLEREMAINTIME = 1334;</code>
     *
     * <pre>
     * PVP战斗 剩余时间
     * </pre>
     */
    REQUESTPVPBATTLEREMAINTIME(158, 1334),
    /**
     * <code>REQUESTNewMailTest = 1340;</code>
     *
     * <pre>
     * 获取一个新邮件
     * </pre>
     */
    REQUESTNewMailTest(159, 1340),
    /**
     * <code>REQUESTADITEM = 1341;</code>
     *
     * <pre>
     *观看广告奖励
     * </pre>
     */
    REQUESTADITEM(160, 1341),
    /**
     * <code>REQUESTRECHAREGREBATEREWARD = 1342;</code>
     *
     * <pre>
     * 充值返利任务id修改
     * </pre>
     */
    REQUESTRECHAREGREBATEREWARD(161, 1342),
    /**
     * <code>REQUESTLIMITEDTIMEREWARD = 1350;</code>
     *
     * <pre>
     * 修改限时奖励
     * </pre>
     */
    REQUESTLIMITEDTIMEREWARD(162, 1350),
    /**
     * <code>REQUESTLIMITEDTIMEREWARDTYPE = 1351;</code>
     *
     * <pre>
     * 修改限时奖励
     * </pre>
     */
    REQUESTLIMITEDTIMEREWARDTYPE(163, 1351),
    /**
     * <code>REQUESTLIMITEDTIMEREWARDFINISH = 1352;</code>
     *
     * <pre>
     * 修改限时奖励
     * </pre>
     */
    REQUESTLIMITEDTIMEREWARDFINISH(164, 1352),
    /**
     * <code>REQUESTREMAINTIME = 1354;</code>
     *
     * <pre>
     * 剩余时间
     * </pre>
     */
    REQUESTREMAINTIME(165, 1354),
    /**
     * <code>REQUESTPETEGGEXP = 1360;</code>
     *
     * <pre>
     * 查询蛋的经验值和等级
     * </pre>
     */
    REQUESTPETEGGEXP(166, 1360),
    /**
     * <code>REQUESTADDPETEGGEXP = 1361;</code>
     *
     * <pre>
     * 蛋的经验值增加
     * </pre>
     */
    REQUESTADDPETEGGEXP(167, 1361),
    /**
     * <code>RESPONSEPETEGGFASTHATCH = 1362;</code>
     *
     * <pre>
     * 快速孵化蛋
     * </pre>
     */
    RESPONSEPETEGGFASTHATCH(168, 1362),
    /**
     * <code>REQUESTTEMITEM = 1380;</code>
     *
     * <pre>
     * 温度层获得道具限制次数
     * </pre>
     */
    REQUESTTEMITEM(169, 1380),
    /**
     * <code>REQUESTDELETEALLPETS = 1390;</code>
     *
     * <pre>
     * 删除全部宠物
     * </pre>
     */
    REQUESTDELETEALLPETS(170, 1390),
    ;

    /**
     * <code>REQUESTSERVER = 1000;</code>
     *
     * <pre>
     *游戏区服列表
     * </pre>
     */
    public static final int REQUESTSERVER_VALUE = 1000;
    /**
     * <code>REQUESTREGISTER = 1005;</code>
     *
     * <pre>
     *注册
     * </pre>
     */
    public static final int REQUESTREGISTER_VALUE = 1005;
    /**
     * <code>REQUESTLOGIN = 1001;</code>
     *
     * <pre>
     *登入
     * </pre>
     */
    public static final int REQUESTLOGIN_VALUE = 1001;
    /**
     * <code>REQUESTCREATE = 1002;</code>
     *
     * <pre>
     *创建角色
     * </pre>
     */
    public static final int REQUESTCREATE_VALUE = 1002;
    /**
     * <code>REQUESTROLESEX = 1212;</code>
     *
     * <pre>
     *创建性别
     * </pre>
     */
    public static final int REQUESTROLESEX_VALUE = 1212;
    /**
     * <code>REQUESTGETSEX = 1213;</code>
     *
     * <pre>
     *获得性别
     * </pre>
     */
    public static final int REQUESTGETSEX_VALUE = 1213;
    /**
     * <code>REQUESTSETINFO = 1003;</code>
     *
     * <pre>
     *设置名字
     * </pre>
     */
    public static final int REQUESTSETINFO_VALUE = 1003;
    /**
     * <code>REQUESTCHOOSEROLE = 1004;</code>
     *
     * <pre>
     *选择角色
     * </pre>
     */
    public static final int REQUESTCHOOSEROLE_VALUE = 1004;
    /**
     * <code>REQUESTBUYITEM = 1006;</code>
     *
     * <pre>
     *购买物品
     * </pre>
     */
    public static final int REQUESTBUYITEM_VALUE = 1006;
    /**
     * <code>REQUESTUSEITEM = 1007;</code>
     *
     * <pre>
     *使用物品
     * </pre>
     */
    public static final int REQUESTUSEITEM_VALUE = 1007;
    /**
     * <code>REQUESTOPENBAGCELL = 1008;</code>
     *
     * <pre>
     *解锁背包格子
     * </pre>
     */
    public static final int REQUESTOPENBAGCELL_VALUE = 1008;
    /**
     * <code>REQUESTCOMPOSE = 1009;</code>
     *
     * <pre>
     *合成
     * </pre>
     */
    public static final int REQUESTCOMPOSE_VALUE = 1009;
    /**
     * <code>REQUESTBEGINMISSION = 1010;</code>
     *
     * <pre>
     *开始闯关
     * </pre>
     */
    public static final int REQUESTBEGINMISSION_VALUE = 1010;
    /**
     * <code>REQUESTCOUNTMISSION = 1011;</code>
     *
     * <pre>
     *结算闯关
     * </pre>
     */
    public static final int REQUESTCOUNTMISSION_VALUE = 1011;
    /**
     * <code>REQUESTBEGINENDLESS = 1012;</code>
     *
     * <pre>
     *开始无尽
     * </pre>
     */
    public static final int REQUESTBEGINENDLESS_VALUE = 1012;
    /**
     * <code>REQUESTCOUNTENDLESS = 1013;</code>
     *
     * <pre>
     *结算无尽
     * </pre>
     */
    public static final int REQUESTCOUNTENDLESS_VALUE = 1013;
    /**
     * <code>REQUESTUPDATENEWUSER = 1014;</code>
     *
     * <pre>
     *更新新手引导
     * </pre>
     */
    public static final int REQUESTUPDATENEWUSER_VALUE = 1014;
    /**
     * <code>REQUESTWECHATLOGIN = 1015;</code>
     *
     * <pre>
     *微信登录
     * </pre>
     */
    public static final int REQUESTWECHATLOGIN_VALUE = 1015;
    /**
     * <code>REQUESTAPPLELOGIN = 1016;</code>
     *
     * <pre>
     *苹果登录
     * </pre>
     */
    public static final int REQUESTAPPLELOGIN_VALUE = 1016;
    /**
     * <code>REQUESTQQLOGIN = 1017;</code>
     *
     * <pre>
     *QQ登录
     * </pre>
     */
    public static final int REQUESTQQLOGIN_VALUE = 1017;
    /**
     * <code>REQUESTYSDKLOGIN = 1880;</code>
     *
     * <pre>
     *YSDK登录
     * </pre>
     */
    public static final int REQUESTYSDKLOGIN_VALUE = 1880;
    /**
     * <code>REQUESTHUAWEILOGIN = 1881;</code>
     *
     * <pre>
     *华为登录	
     * </pre>
     */
    public static final int REQUESTHUAWEILOGIN_VALUE = 1881;
    /**
     * <code>REQUESTGOOGLELOGIN = 1882;</code>
     *
     * <pre>
     *google登录	
     * </pre>
     */
    public static final int REQUESTGOOGLELOGIN_VALUE = 1882;
    /**
     * <code>REQUESTSENDCHAT = 1020;</code>
     *
     * <pre>
     *发送聊天
     * </pre>
     */
    public static final int REQUESTSENDCHAT_VALUE = 1020;
    /**
     * <code>REQUESTFINISHTASK = 1030;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    public static final int REQUESTFINISHTASK_VALUE = 1030;
    /**
     * <code>REQUESTOPERATEVIEW = 1050;</code>
     *
     * <pre>
     *操作界面
     * </pre>
     */
    public static final int REQUESTOPERATEVIEW_VALUE = 1050;
    /**
     * <code>REQUESTBUYCLOTHING = 1051;</code>
     *
     * <pre>
     *购买衣服
     * </pre>
     */
    public static final int REQUESTBUYCLOTHING_VALUE = 1051;
    /**
     * <code>REQUESTCHANGECUPBOARD = 1052;</code>
     *
     * <pre>
     *衣柜操作
     * </pre>
     */
    public static final int REQUESTCHANGECUPBOARD_VALUE = 1052;
    /**
     * <code>REQUESTCHANGEDRESS = 1053;</code>
     *
     * <pre>
     *换装扮
     * </pre>
     */
    public static final int REQUESTCHANGEDRESS_VALUE = 1053;
    /**
     * <code>REQUESTCUPBOARDTOBODY = 1054;</code>
     *
     * <pre>
     *衣柜到当前装
     * </pre>
     */
    public static final int REQUESTCUPBOARDTOBODY_VALUE = 1054;
    /**
     * <code>REQUESTSTARBITOMONEY = 1055;</code>
     *
     * <pre>
     *星币换金币
     * </pre>
     */
    public static final int REQUESTSTARBITOMONEY_VALUE = 1055;
    /**
     * <code>REQUESTOPERATEFRIEND = 1060;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    public static final int REQUESTOPERATEFRIEND_VALUE = 1060;
    /**
     * <code>REQUESTOPERATEFRIEND1 = 1201;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    public static final int REQUESTOPERATEFRIEND1_VALUE = 1201;
    /**
     * <code>REQUESTOPERATEMESSAGE = 1061;</code>
     *
     * <pre>
     *操作消息
     * </pre>
     */
    public static final int REQUESTOPERATEMESSAGE_VALUE = 1061;
    /**
     * <code>REQUESTPETHATCH = 1466;</code>
     *
     * <pre>
     *培育金钱
     * </pre>
     */
    public static final int REQUESTPETHATCH_VALUE = 1466;
    /**
     * <code>REQUESTRECOMMENDFRIEND = 1064;</code>
     *
     * <pre>
     *推荐好友
     * </pre>
     */
    public static final int REQUESTRECOMMENDFRIEND_VALUE = 1064;
    /**
     * <code>REQUESTSTATIONLOCATION = 1066;</code>
     *
     * <pre>
     *车站走动
     * </pre>
     */
    public static final int REQUESTSTATIONLOCATION_VALUE = 1066;
    /**
     * <code>REQUESTGIVEPRESENT = 1067;</code>
     *
     * <pre>
     *赠送礼物
     * </pre>
     */
    public static final int REQUESTGIVEPRESENT_VALUE = 1067;
    /**
     * <code>REQUESTACCUSATION = 1068;</code>
     *
     * <pre>
     *举报
     * </pre>
     */
    public static final int REQUESTACCUSATION_VALUE = 1068;
    /**
     * <code>REQUESTGETRANK = 1070;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    public static final int REQUESTGETRANK_VALUE = 1070;
    /**
     * <code>REQUESTGETRANKING = 1095;</code>
     *
     * <pre>
     *排
     * </pre>
     */
    public static final int REQUESTGETRANKING_VALUE = 1095;
    /**
     * <code>REQUESTGETTOTALENDLESS = 1071;</code>
     *
     * <pre>
     *获取无尽总积分奖励
     * </pre>
     */
    public static final int REQUESTGETTOTALENDLESS_VALUE = 1071;
    /**
     * <code>REQUESTJOINROOM = 1080;</code>
     *
     * <pre>
     *进入房间
     * </pre>
     */
    public static final int REQUESTJOINROOM_VALUE = 1080;
    /**
     * <code>REQUESTCREATEROOM = 1081;</code>
     *
     * <pre>
     *创建房间
     * </pre>
     */
    public static final int REQUESTCREATEROOM_VALUE = 1081;
    /**
     * <code>REQUESTLEAVEROOM = 1082;</code>
     *
     * <pre>
     *离开房间
     * </pre>
     */
    public static final int REQUESTLEAVEROOM_VALUE = 1082;
    /**
     * <code>REQUESTUPDATEROOMINFO = 1083;</code>
     *
     * <pre>
     *更新房间信息
     * </pre>
     */
    public static final int REQUESTUPDATEROOMINFO_VALUE = 1083;
    /**
     * <code>REQUESTUPDATEROOMROLE = 1084;</code>
     *
     * <pre>
     *更新房间内玩家信息
     * </pre>
     */
    public static final int REQUESTUPDATEROOMROLE_VALUE = 1084;
    /**
     * <code>REQUESTJOINFIGHTHALL = 1085;</code>
     *
     * <pre>
     *请求进出多人战斗大厅
     * </pre>
     */
    public static final int REQUESTJOINFIGHTHALL_VALUE = 1085;
    /**
     * <code>REQUESTSTARTINROOM = 1086;</code>
     *
     * <pre>
     *房主开始游戏
     * </pre>
     */
    public static final int REQUESTSTARTINROOM_VALUE = 1086;
    /**
     * <code>REQUESTRECONNECTION = 1200;</code>
     *
     * <pre>
     *断线重连
     * </pre>
     */
    public static final int REQUESTRECONNECTION_VALUE = 1200;
    /**
     * <code>REQUESTCHOOSEPAY = 1500;</code>
     *
     * <pre>
     *选择购买内容
     * </pre>
     */
    public static final int REQUESTCHOOSEPAY_VALUE = 1500;
    /**
     * <code>REQUESTSUBMITPAYBACK = 1501;</code>
     *
     * <pre>
     *支付
     * </pre>
     */
    public static final int REQUESTSUBMITPAYBACK_VALUE = 1501;
    /**
     * <code>REQUESTGOOGLEPAY = 1502;</code>
     *
     * <pre>
     *google支付
     * </pre>
     */
    public static final int REQUESTGOOGLEPAY_VALUE = 1502;
    /**
     * <code>REQUESTWECHATPAY = 1503;</code>
     *
     * <pre>
     *微信支付
     * </pre>
     */
    public static final int REQUESTWECHATPAY_VALUE = 1503;
    /**
     * <code>REQUESTALIPAY = 1504;</code>
     *
     * <pre>
     *支付宝支付
     * </pre>
     */
    public static final int REQUESTALIPAY_VALUE = 1504;
    /**
     * <code>REQUESTCONFIRMHUAWEIPURCHASE = 1505;</code>
     *
     * <pre>
     *华为确认支付
     * </pre>
     */
    public static final int REQUESTCONFIRMHUAWEIPURCHASE_VALUE = 1505;
    /**
     * <code>REQUESTYSDKBALANCE = 1506;</code>
     *
     * <pre>
     *YSDK确认支付
     * </pre>
     */
    public static final int REQUESTYSDKBALANCE_VALUE = 1506;
    /**
     * <code>REQUESTBEGINHEADBALL = 1018;</code>
     *
     * <pre>
     *请求顶球模式
     * </pre>
     */
    public static final int REQUESTBEGINHEADBALL_VALUE = 1018;
    /**
     * <code>REQUESTCOUNTHEADBALL = 1019;</code>
     *
     * <pre>
     *请求结算顶球模式
     * </pre>
     */
    public static final int REQUESTCOUNTHEADBALL_VALUE = 1019;
    /**
     * <code>REQUESTADPLAY = 1602;</code>
     *
     * <pre>
     *请求播放广告
     * </pre>
     */
    public static final int REQUESTADPLAY_VALUE = 1602;
    /**
     * <code>REQUESTADNUN = 1603;</code>
     *
     * <pre>
     *请求播放广告次数
     * </pre>
     */
    public static final int REQUESTADNUN_VALUE = 1603;
    /**
     * <code>REQUESTCOUNTGOLD = 1700;</code>
     *
     * <pre>
     *金币模式请求结算
     * </pre>
     */
    public static final int REQUESTCOUNTGOLD_VALUE = 1700;
    /**
     * <code>REQUESTBEGINGOLD = 1701;</code>
     *
     * <pre>
     *金币模式请求刷球
     * </pre>
     */
    public static final int REQUESTBEGINGOLD_VALUE = 1701;
    /**
     * <code>REQUESTBEGINDOWN = 1702;</code>
     *
     * <pre>
     *落球模式
     * </pre>
     */
    public static final int REQUESTBEGINDOWN_VALUE = 1702;
    /**
     * <code>REQUESTCOUNTDOWN = 1704;</code>
     *
     * <pre>
     *请求落球结算
     * </pre>
     */
    public static final int REQUESTCOUNTDOWN_VALUE = 1704;
    /**
     * <code>REQUESTBEGINSTACK = 1705;</code>
     *
     * <pre>
     *堆球模式		
     * </pre>
     */
    public static final int REQUESTBEGINSTACK_VALUE = 1705;
    /**
     * <code>REQUESTCOUNTSTACK = 1709;</code>
     *
     * <pre>
     *请求堆球结算
     * </pre>
     */
    public static final int REQUESTCOUNTSTACK_VALUE = 1709;
    /**
     * <code>REQUESTCOUNTLOTTO = 1703;</code>
     *
     * <pre>
     *请求合成道具
     * </pre>
     */
    public static final int REQUESTCOUNTLOTTO_VALUE = 1703;
    /**
     * <code>REQUESTSOURCESMACHINE = 1708;</code>
     *
     * <pre>
     *请求绿机器能源机合成
     * </pre>
     */
    public static final int REQUESTSOURCESMACHINE_VALUE = 1708;
    /**
     * <code>REQURESTMAKEITEM = 1706;</code>
     *
     * <pre>
     *请求培养皿物品
     * </pre>
     */
    public static final int REQURESTMAKEITEM_VALUE = 1706;
    /**
     * <code>REQUESTPRODUCT = 1707;</code>
     *
     * <pre>
     *请求成品的收获     
     * </pre>
     */
    public static final int REQUESTPRODUCT_VALUE = 1707;
    /**
     * <code>REQUESTVISITOROPERATION = 1900;</code>
     *
     * <pre>
     *游客登录相关操作  	
     * </pre>
     */
    public static final int REQUESTVISITOROPERATION_VALUE = 1900;
    /**
     * <code>REQUESTAPPROVAL = 1920;</code>
     *
     * <pre>
     *点赞请求
     * </pre>
     */
    public static final int REQUESTAPPROVAL_VALUE = 1920;
    /**
     * <code>REQUESTAUTHENTICATION = 1921;</code>
     *
     * <pre>
     *身份认证
     * </pre>
     */
    public static final int REQUESTAUTHENTICATION_VALUE = 1921;
    /**
     * <code>REQUESTGETAPPROVAL = 1922;</code>
     *
     * <pre>
     *获得当前玩家受到点赞数
     * </pre>
     */
    public static final int REQUESTGETAPPROVAL_VALUE = 1922;
    /**
     * <code>REQUESTSTARTGAMECOPY = 1122;</code>
     *
     * <pre>
     *请求进入每日关卡（任务金币）
     * </pre>
     */
    public static final int REQUESTSTARTGAMECOPY_VALUE = 1122;
    /**
     * <code>REQUESTCOUNTGAMECOPY = 1123;</code>
     *
     * <pre>
     *请求每日关卡结算
     * </pre>
     */
    public static final int REQUESTCOUNTGAMECOPY_VALUE = 1123;
    /**
     * <code>REQUESTGETLUCKYITEM = 1124;</code>
     *
     * <pre>
     *车站界面幸运物品获取
     * </pre>
     */
    public static final int REQUESTGETLUCKYITEM_VALUE = 1124;
    /**
     * <code>REQUESTFINISHACTIVITIES = 1126;</code>
     *
     * <pre>
     *请求活动完成
     * </pre>
     */
    public static final int REQUESTFINISHACTIVITIES_VALUE = 1126;
    /**
     * <code>REQUESTMARKETINFORMATION = 1129;</code>
     *
     * <pre>
     *请求交易市场信息
     * </pre>
     */
    public static final int REQUESTMARKETINFORMATION_VALUE = 1129;
    /**
     * <code>REQUESTGOODSOPERATE = 1130;</code>
     *
     * <pre>
     * 交易市场商品买入卖出
     * </pre>
     */
    public static final int REQUESTGOODSOPERATE_VALUE = 1130;
    /**
     * <code>REQUESTTICKTEEXCHANGE = 1131;</code>
     *
     * <pre>
     * 票券兑换
     * </pre>
     */
    public static final int REQUESTTICKTEEXCHANGE_VALUE = 1131;
    /**
     * <code>REQUESTQUERYROLEINFORMATION = 1135;</code>
     *
     * <pre>
     * 查询玩家角色信息
     * </pre>
     */
    public static final int REQUESTQUERYROLEINFORMATION_VALUE = 1135;
    /**
     * <code>REQUESTWAITITEM = 1136;</code>
     *
     * <pre>
     *挂机获得物品
     * </pre>
     */
    public static final int REQUESTWAITITEM_VALUE = 1136;
    /**
     * <code>REQUESTUPWAITITEMLEVEL = 1137;</code>
     *
     * <pre>
     *提升等物品功能背包等級
     * </pre>
     */
    public static final int REQUESTUPWAITITEMLEVEL_VALUE = 1137;
    /**
     * <code>REQUESTGETWAITITEM = 1138;</code>
     *
     * <pre>
     *活得等物品功能物品
     * </pre>
     */
    public static final int REQUESTGETWAITITEM_VALUE = 1138;
    /**
     * <code>REQUESTHOUSE = 1600;</code>
     *
     * <pre>
     * 装饰功能模块信息
     * </pre>
     */
    public static final int REQUESTHOUSE_VALUE = 1600;
    /**
     * <code>REQUESTHOUSEPART = 1605;</code>
     *
     * <pre>
     *保存玩家房间装饰记录
     * </pre>
     */
    public static final int REQUESTHOUSEPART_VALUE = 1605;
    /**
     * <code>REQUESTBUYFURNITURE = 1670;</code>
     *
     * <pre>
     * 买家具
     * </pre>
     */
    public static final int REQUESTBUYFURNITURE_VALUE = 1670;
    /**
     * <code>REQUESTOPERATEMAIL = 1110;</code>
     *
     * <pre>
     * 操作邮件
     * </pre>
     */
    public static final int REQUESTOPERATEMAIL_VALUE = 1110;
    /**
     * <code>REQUESTOPERATENOTICE = 1111;</code>
     *
     * <pre>
     *操作公告
     * </pre>
     */
    public static final int REQUESTOPERATENOTICE_VALUE = 1111;
    /**
     * <code>REQUESTGETPETSINFO = 1210;</code>
     *
     * <pre>
     *获取玩家宠物信息
     * </pre>
     */
    public static final int REQUESTGETPETSINFO_VALUE = 1210;
    /**
     * <code>REQUESTGETEQUIP = 1215;</code>
     *
     * <pre>
     *获取玩家装备信息
     * </pre>
     */
    public static final int REQUESTGETEQUIP_VALUE = 1215;
    /**
     * <code>REQUESTOPERATEPET = 1211;</code>
     *
     * <pre>
     *操作宠物
     * </pre>
     */
    public static final int REQUESTOPERATEPET_VALUE = 1211;
    /**
     * <code>REQUESTOPERATEROLE = 1311;</code>
     *
     * <pre>
     *操作角色
     * </pre>
     */
    public static final int REQUESTOPERATEROLE_VALUE = 1311;
    /**
     * <code>REQUESTCHOICEEQUIP = 1218;</code>
     *
     * <pre>
     *选择装备
     * </pre>
     */
    public static final int REQUESTCHOICEEQUIP_VALUE = 1218;
    /**
     * <code>REQUESTOPERATEEQUIP = 1216;</code>
     *
     * <pre>
     *操作装备
     * </pre>
     */
    public static final int REQUESTOPERATEEQUIP_VALUE = 1216;
    /**
     * <code>REQUESTCHANGEEQUIP = 1217;</code>
     *
     * <pre>
     *换装备
     * </pre>
     */
    public static final int REQUESTCHANGEEQUIP_VALUE = 1217;
    /**
     * <code>REQUESTBINDIDCARD = 1113;</code>
     *
     * <pre>
     *  绑定身份证
     * </pre>
     */
    public static final int REQUESTBINDIDCARD_VALUE = 1113;
    /**
     * <code>REQUESTGETDISPATCHINFOS = 1120;</code>
     *
     * <pre>
     *  获得派遣任务信息
     * </pre>
     */
    public static final int REQUESTGETDISPATCHINFOS_VALUE = 1120;
    /**
     * <code>REQUESTOPERATEDISPATCHTASK = 1121;</code>
     *
     * <pre>
     *    操作派遣任务
     * </pre>
     */
    public static final int REQUESTOPERATEDISPATCHTASK_VALUE = 1121;
    /**
     * <code>REQUESTRAFFLEPOOL = 1150;</code>
     *
     * <pre>
     *获取抽奖池
     * </pre>
     */
    public static final int REQUESTRAFFLEPOOL_VALUE = 1150;
    /**
     * <code>REQUESTGETEVENTINFO = 1160;</code>
     *
     * <pre>
     *事件信息
     * </pre>
     */
    public static final int REQUESTGETEVENTINFO_VALUE = 1160;
    /**
     * <code>REQUESTFLUSHEVENT = 1161;</code>
     *
     * <pre>
     *刷新事件
     * </pre>
     */
    public static final int REQUESTFLUSHEVENT_VALUE = 1161;
    /**
     * <code>REQUESTCOUNTBATTLE = 1170;</code>
     *
     * <pre>
     * 怪物副本结算
     * </pre>
     */
    public static final int REQUESTCOUNTBATTLE_VALUE = 1170;
    /**
     * <code>REQUESTPLAYERITEMS = 1180;</code>
     *
     * <pre>
     *获取玩家物品数据
     * </pre>
     */
    public static final int REQUESTPLAYERITEMS_VALUE = 1180;
    /**
     * <code>REQUESTUPPETSKILL = 1190;</code>
     *
     * <pre>
     *强化宠物技能等级
     * </pre>
     */
    public static final int REQUESTUPPETSKILL_VALUE = 1190;
    /**
     * <code>REQUESTPETEXP = 1580;</code>
     *
     * <pre>
     *获取宠物经验
     * </pre>
     */
    public static final int REQUESTPETEXP_VALUE = 1580;
    /**
     * <code>REQUESTBALCKMARKET = 1250;</code>
     *
     * <pre>
     *获取黑市商店信息
     * </pre>
     */
    public static final int REQUESTBALCKMARKET_VALUE = 1250;
    /**
     * <code>REQUESTBUYBALCKMARKETITEM = 1251;</code>
     *
     * <pre>
     * 购买黑市商店物品
     * </pre>
     */
    public static final int REQUESTBUYBALCKMARKETITEM_VALUE = 1251;
    /**
     * <code>REQUESTPETFORMATION = 1261;</code>
     *
     * <pre>
     *请求宠物编队
     * </pre>
     */
    public static final int REQUESTPETFORMATION_VALUE = 1261;
    /**
     * <code>REQUESTGETPETFORMATION = 1262;</code>
     *
     * <pre>
     *获取宠物编队信息
     * </pre>
     */
    public static final int REQUESTGETPETFORMATION_VALUE = 1262;
    /**
     * <code>REQUESTPETCOMPOSE = 1265;</code>
     *
     * <pre>
     * 宠物合成
     * </pre>
     */
    public static final int REQUESTPETCOMPOSE_VALUE = 1265;
    /**
     * <code>REQUESTPETBREEDINOFRMATION = 1272;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    public static final int REQUESTPETBREEDINOFRMATION_VALUE = 1272;
    /**
     * <code>REQUESTPETBREEDOPERATION = 1273;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    public static final int REQUESTPETBREEDOPERATION_VALUE = 1273;
    /**
     * <code>REQUESTPETBREED = 1274;</code>
     *
     * <pre>
     *	登录返回正在孵化的蛋
     * </pre>
     */
    public static final int REQUESTPETBREED_VALUE = 1274;
    /**
     * <code>REQUESTGETPETBREEDINFO = 1270;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    public static final int REQUESTGETPETBREEDINFO_VALUE = 1270;
    /**
     * <code>REQUESTOPERATEPETBREED = 1271;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    public static final int REQUESTOPERATEPETBREED_VALUE = 1271;
    /**
     * <code>REQUESTGETPETGROWINFO = 1300;</code>
     *
     * <pre>
     *获取正在进化的宠物链表
     * </pre>
     */
    public static final int REQUESTGETPETGROWINFO_VALUE = 1300;
    /**
     * <code>REQUESTPETEVOLUTION = 1301;</code>
     *
     * <pre>
     *操作宠物进行进化
     * </pre>
     */
    public static final int REQUESTPETEVOLUTION_VALUE = 1301;
    /**
     * <code>REQUESTPETBREAKTHROUGH = 1302;</code>
     *
     * <pre>
     *宠物突破
     * </pre>
     */
    public static final int REQUESTPETBREAKTHROUGH_VALUE = 1302;
    /**
     * <code>REQUESTGETEXPERIENCES = 1280;</code>
     *
     * <pre>
     *获取探险任务的信息
     * </pre>
     */
    public static final int REQUESTGETEXPERIENCES_VALUE = 1280;
    /**
     * <code>REQUESTFLUSHEXPERIENCES = 1281;</code>
     *
     * <pre>
     *刷新探险任务
     * </pre>
     */
    public static final int REQUESTFLUSHEXPERIENCES_VALUE = 1281;
    /**
     * <code>REQUESTOPERATEEXPERIENCES = 1282;</code>
     *
     * <pre>
     *操作探险任务
     * </pre>
     */
    public static final int REQUESTOPERATEEXPERIENCES_VALUE = 1282;
    /**
     * <code>REQUESTITEMCOMPOSE = 1283;</code>
     *
     * <pre>
     *道具合成  
     * </pre>
     */
    public static final int REQUESTITEMCOMPOSE_VALUE = 1283;
    /**
     * <code>REQUESTPHYSICAL = 1046;</code>
     *
     * <pre>
     *消耗物品
     * </pre>
     */
    public static final int REQUESTPHYSICAL_VALUE = 1046;
    /**
     * <code>REQUESTGETROLEEXP = 1318;</code>
     *
     * <pre>
     *得到人物经验
     * </pre>
     */
    public static final int REQUESTGETROLEEXP_VALUE = 1318;
    /**
     * <code>REQUESTOPERATIONFRIENDS = 1303;</code>
     *
     * <pre>
     *操作好友申请信息
     * </pre>
     */
    public static final int REQUESTOPERATIONFRIENDS_VALUE = 1303;
    /**
     * <code>REQUESTGETMAP = 1370;</code>
     *
     * <pre>
     *地图
     * </pre>
     */
    public static final int REQUESTGETMAP_VALUE = 1370;
    /**
     * <code>REQUESTUPDMAP = 1371;</code>
     *
     * <pre>
     *修改地图
     * </pre>
     */
    public static final int REQUESTUPDMAP_VALUE = 1371;
    /**
     * <code>REQUESTREMOVEPET = 1285;</code>
     *
     * <pre>
     *回收宠物
     * </pre>
     */
    public static final int REQUESTREMOVEPET_VALUE = 1285;
    /**
     * <code>REQUESTDAILYTANSKS = 1290;</code>
     *
     * <pre>
     *进行任务
     * </pre>
     */
    public static final int REQUESTDAILYTANSKS_VALUE = 1290;
    /**
     * <code>REQUESTCOMPLETEDAILYTASKS = 1291;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    public static final int REQUESTCOMPLETEDAILYTASKS_VALUE = 1291;
    /**
     * <code>REQUESTTASK = 1292;</code>
     *
     * <pre>
     *进入游戏获取任务进度
     * </pre>
     */
    public static final int REQUESTTASK_VALUE = 1292;
    /**
     * <code>REQUESTACHIEVEMENTTYPE = 1293;</code>
     *
     * <pre>
     *进行成就
     * </pre>
     */
    public static final int REQUESTACHIEVEMENTTYPE_VALUE = 1293;
    /**
     * <code>REQUESTACHIEVEMENTIS = 1294;</code>
     *
     * <pre>
     *完成成就
     * </pre>
     */
    public static final int REQUESTACHIEVEMENTIS_VALUE = 1294;
    /**
     * <code>REQUESTACHIEVEMENT = 1295;</code>
     *
     * <pre>
     *进入游戏获取成就进度
     * </pre>
     */
    public static final int REQUESTACHIEVEMENT_VALUE = 1295;
    /**
     * <code>REQUESTADDITEM = 1056;</code>
     *
     * <pre>
     *添加道具
     * </pre>
     */
    public static final int REQUESTADDITEM_VALUE = 1056;
    /**
     * <code>REQUESTSYNTHESIS = 1057;</code>
     *
     * <pre>
     *合成道具
     * </pre>
     */
    public static final int REQUESTSYNTHESIS_VALUE = 1057;
    /**
     * <code>REQUESTPLAYERRECOMMEND = 1298;</code>
     *
     * <pre>
     *推荐玩家
     * </pre>
     */
    public static final int REQUESTPLAYERRECOMMEND_VALUE = 1298;
    /**
     * <code>REQUESTFRIENDSAPPLY = 1299;</code>
     *
     * <pre>
     *上线刷新好友申请信息
     * </pre>
     */
    public static final int REQUESTFRIENDSAPPLY_VALUE = 1299;
    /**
     * <code>REQUESTSENDOUTINFORMATION = 1304;</code>
     *
     * <pre>
     *发送消息
     * </pre>
     */
    public static final int REQUESTSENDOUTINFORMATION_VALUE = 1304;
    /**
     * <code>REQUESTCHATRECORD = 1305;</code>
     *
     * <pre>
     *上线获取玩家聊天记录
     * </pre>
     */
    public static final int REQUESTCHATRECORD_VALUE = 1305;
    /**
     * <code>REQUESTUPDATETYPE = 1306;</code>
     *
     * <pre>
     *修改状态已读未读删除
     * </pre>
     */
    public static final int REQUESTUPDATETYPE_VALUE = 1306;
    /**
     * <code>REQUESTRANK = 1307;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    public static final int REQUESTRANK_VALUE = 1307;
    /**
     * <code>REQUESTLEVEL = 1308;</code>
     *
     * <pre>
     *关卡    
     * </pre>
     */
    public static final int REQUESTLEVEL_VALUE = 1308;
    /**
     * <code>REQUESTLEVELD = 1309;</code>
     *
     * <pre>
     * 操作关卡数据
     * </pre>
     */
    public static final int REQUESTLEVELD_VALUE = 1309;
    /**
     * <code>REQUESTPlont = 1320;</code>
     *
     * <pre>
     *获取剧情1
     * </pre>
     */
    public static final int REQUESTPlont_VALUE = 1320;
    /**
     * <code>REQUESTChap = 1321;</code>
     *
     * <pre>
     *返回剧情1
     * </pre>
     */
    public static final int REQUESTChap_VALUE = 1321;
    /**
     * <code>REQUESTGetEquipA = 1322;</code>
     *
     * <pre>
     *创建装备
     * </pre>
     */
    public static final int REQUESTGetEquipA_VALUE = 1322;
    /**
     * <code>REQUESTDDDEquip = 1323;</code>
     *
     * <pre>
     *获取装备
     * </pre>
     */
    public static final int REQUESTDDDEquip_VALUE = 1323;
    /**
     * <code>REQUESTEquipF = 1324;</code>
     *
     * <pre>
     *接受装备
     * </pre>
     */
    public static final int REQUESTEquipF_VALUE = 1324;
    /**
     * <code>REQUESTbossTiem = 1325;</code>
     *
     * <pre>
     *世界boss
     * </pre>
     */
    public static final int REQUESTbossTiem_VALUE = 1325;
    /**
     * <code>REQUESTbossdear = 1326;</code>
     *
     * <pre>
     *世界血量存活
     * </pre>
     */
    public static final int REQUESTbossdear_VALUE = 1326;
    /**
     * <code>REQUESTPVPPetOperate = 1330;</code>
     *
     * <pre>
     * PVP宠物存储
     * </pre>
     */
    public static final int REQUESTPVPPetOperate_VALUE = 1330;
    /**
     * <code>REQUESTPVPBaseData = 1331;</code>
     *
     * <pre>
     * PVP个人信息
     * </pre>
     */
    public static final int REQUESTPVPBaseData_VALUE = 1331;
    /**
     * <code>REQUESTPVPBattle = 1332;</code>
     *
     * <pre>
     * PVP战斗
     * </pre>
     */
    public static final int REQUESTPVPBattle_VALUE = 1332;
    /**
     * <code>REQUESTPVPBattleResult = 1333;</code>
     *
     * <pre>
     * PVP战斗 输赢
     * </pre>
     */
    public static final int REQUESTPVPBattleResult_VALUE = 1333;
    /**
     * <code>REQUESTPVPBATTLEREMAINTIME = 1334;</code>
     *
     * <pre>
     * PVP战斗 剩余时间
     * </pre>
     */
    public static final int REQUESTPVPBATTLEREMAINTIME_VALUE = 1334;
    /**
     * <code>REQUESTNewMailTest = 1340;</code>
     *
     * <pre>
     * 获取一个新邮件
     * </pre>
     */
    public static final int REQUESTNewMailTest_VALUE = 1340;
    /**
     * <code>REQUESTADITEM = 1341;</code>
     *
     * <pre>
     *观看广告奖励
     * </pre>
     */
    public static final int REQUESTADITEM_VALUE = 1341;
    /**
     * <code>REQUESTRECHAREGREBATEREWARD = 1342;</code>
     *
     * <pre>
     * 充值返利任务id修改
     * </pre>
     */
    public static final int REQUESTRECHAREGREBATEREWARD_VALUE = 1342;
    /**
     * <code>REQUESTLIMITEDTIMEREWARD = 1350;</code>
     *
     * <pre>
     * 修改限时奖励
     * </pre>
     */
    public static final int REQUESTLIMITEDTIMEREWARD_VALUE = 1350;
    /**
     * <code>REQUESTLIMITEDTIMEREWARDTYPE = 1351;</code>
     *
     * <pre>
     * 修改限时奖励
     * </pre>
     */
    public static final int REQUESTLIMITEDTIMEREWARDTYPE_VALUE = 1351;
    /**
     * <code>REQUESTLIMITEDTIMEREWARDFINISH = 1352;</code>
     *
     * <pre>
     * 修改限时奖励
     * </pre>
     */
    public static final int REQUESTLIMITEDTIMEREWARDFINISH_VALUE = 1352;
    /**
     * <code>REQUESTREMAINTIME = 1354;</code>
     *
     * <pre>
     * 剩余时间
     * </pre>
     */
    public static final int REQUESTREMAINTIME_VALUE = 1354;
    /**
     * <code>REQUESTPETEGGEXP = 1360;</code>
     *
     * <pre>
     * 查询蛋的经验值和等级
     * </pre>
     */
    public static final int REQUESTPETEGGEXP_VALUE = 1360;
    /**
     * <code>REQUESTADDPETEGGEXP = 1361;</code>
     *
     * <pre>
     * 蛋的经验值增加
     * </pre>
     */
    public static final int REQUESTADDPETEGGEXP_VALUE = 1361;
    /**
     * <code>RESPONSEPETEGGFASTHATCH = 1362;</code>
     *
     * <pre>
     * 快速孵化蛋
     * </pre>
     */
    public static final int RESPONSEPETEGGFASTHATCH_VALUE = 1362;
    /**
     * <code>REQUESTTEMITEM = 1380;</code>
     *
     * <pre>
     * 温度层获得道具限制次数
     * </pre>
     */
    public static final int REQUESTTEMITEM_VALUE = 1380;

    /**
     * <code>REQUESTDELETEALLPETS = 1390;</code>
     *
     * <pre>
     * 删除全部宠物
     * </pre>
     */
    public static final int REQUESTDELETEALLPETS_VALUE = 1390;

    /**
     * <code>REQUESTCREATENEWMAIL = 1400;</code>
     *
     * <pre>
     * 创建一个新邮件
     * </pre>
     */
    public static final int REQUESTCREATENEWMAIL_VALUE = 1400;

    /**
     * <code>REQUESTCREATENEWMAIL = 1800;</code>
     *
     * <pre>
     * 新手指引抽奖
     * </pre>
     */
    public static final int REQUESTGUIDEPETEXTRACTION_VALUE = 1800;

    public final int getNumber() { return value; }

    public static CToS valueOf(int value) {
      switch (value) {
        case 1000: return REQUESTSERVER;
        case 1005: return REQUESTREGISTER;
        case 1001: return REQUESTLOGIN;
        case 1002: return REQUESTCREATE;
        case 1212: return REQUESTROLESEX;
        case 1213: return REQUESTGETSEX;
        case 1003: return REQUESTSETINFO;
        case 1004: return REQUESTCHOOSEROLE;
        case 1006: return REQUESTBUYITEM;
        case 1007: return REQUESTUSEITEM;
        case 1008: return REQUESTOPENBAGCELL;
        case 1009: return REQUESTCOMPOSE;
        case 1010: return REQUESTBEGINMISSION;
        case 1011: return REQUESTCOUNTMISSION;
        case 1012: return REQUESTBEGINENDLESS;
        case 1013: return REQUESTCOUNTENDLESS;
        case 1014: return REQUESTUPDATENEWUSER;
        case 1015: return REQUESTWECHATLOGIN;
        case 1016: return REQUESTAPPLELOGIN;
        case 1017: return REQUESTQQLOGIN;
        case 1880: return REQUESTYSDKLOGIN;
        case 1881: return REQUESTHUAWEILOGIN;
        case 1882: return REQUESTGOOGLELOGIN;
        case 1020: return REQUESTSENDCHAT;
        case 1030: return REQUESTFINISHTASK;
        case 1050: return REQUESTOPERATEVIEW;
        case 1051: return REQUESTBUYCLOTHING;
        case 1052: return REQUESTCHANGECUPBOARD;
        case 1053: return REQUESTCHANGEDRESS;
        case 1054: return REQUESTCUPBOARDTOBODY;
        case 1055: return REQUESTSTARBITOMONEY;
        case 1060: return REQUESTOPERATEFRIEND;
        case 1201: return REQUESTOPERATEFRIEND1;
        case 1061: return REQUESTOPERATEMESSAGE;
        case 1466: return REQUESTPETHATCH;
        case 1064: return REQUESTRECOMMENDFRIEND;
        case 1066: return REQUESTSTATIONLOCATION;
        case 1067: return REQUESTGIVEPRESENT;
        case 1068: return REQUESTACCUSATION;
        case 1070: return REQUESTGETRANK;
        case 1095: return REQUESTGETRANKING;
        case 1071: return REQUESTGETTOTALENDLESS;
        case 1080: return REQUESTJOINROOM;
        case 1081: return REQUESTCREATEROOM;
        case 1082: return REQUESTLEAVEROOM;
        case 1083: return REQUESTUPDATEROOMINFO;
        case 1084: return REQUESTUPDATEROOMROLE;
        case 1085: return REQUESTJOINFIGHTHALL;
        case 1086: return REQUESTSTARTINROOM;
        case 1200: return REQUESTRECONNECTION;
        case 1500: return REQUESTCHOOSEPAY;
        case 1501: return REQUESTSUBMITPAYBACK;
        case 1502: return REQUESTGOOGLEPAY;
        case 1503: return REQUESTWECHATPAY;
        case 1504: return REQUESTALIPAY;
        case 1505: return REQUESTCONFIRMHUAWEIPURCHASE;
        case 1506: return REQUESTYSDKBALANCE;
        case 1018: return REQUESTBEGINHEADBALL;
        case 1019: return REQUESTCOUNTHEADBALL;
        case 1602: return REQUESTADPLAY;
        case 1603: return REQUESTADNUN;
        case 1700: return REQUESTCOUNTGOLD;
        case 1701: return REQUESTBEGINGOLD;
        case 1702: return REQUESTBEGINDOWN;
        case 1704: return REQUESTCOUNTDOWN;
        case 1705: return REQUESTBEGINSTACK;
        case 1709: return REQUESTCOUNTSTACK;
        case 1703: return REQUESTCOUNTLOTTO;
        case 1708: return REQUESTSOURCESMACHINE;
        case 1706: return REQURESTMAKEITEM;
        case 1707: return REQUESTPRODUCT;
        case 1900: return REQUESTVISITOROPERATION;
        case 1920: return REQUESTAPPROVAL;
        case 1921: return REQUESTAUTHENTICATION;
        case 1922: return REQUESTGETAPPROVAL;
        case 1122: return REQUESTSTARTGAMECOPY;
        case 1123: return REQUESTCOUNTGAMECOPY;
        case 1124: return REQUESTGETLUCKYITEM;
        case 1126: return REQUESTFINISHACTIVITIES;
        case 1129: return REQUESTMARKETINFORMATION;
        case 1130: return REQUESTGOODSOPERATE;
        case 1131: return REQUESTTICKTEEXCHANGE;
        case 1135: return REQUESTQUERYROLEINFORMATION;
        case 1136: return REQUESTWAITITEM;
        case 1137: return REQUESTUPWAITITEMLEVEL;
        case 1138: return REQUESTGETWAITITEM;
        case 1600: return REQUESTHOUSE;
        case 1605: return REQUESTHOUSEPART;
        case 1670: return REQUESTBUYFURNITURE;
        case 1110: return REQUESTOPERATEMAIL;
        case 1111: return REQUESTOPERATENOTICE;
        case 1210: return REQUESTGETPETSINFO;
        case 1215: return REQUESTGETEQUIP;
        case 1211: return REQUESTOPERATEPET;
        case 1311: return REQUESTOPERATEROLE;
        case 1218: return REQUESTCHOICEEQUIP;
        case 1216: return REQUESTOPERATEEQUIP;
        case 1217: return REQUESTCHANGEEQUIP;
        case 1113: return REQUESTBINDIDCARD;
        case 1120: return REQUESTGETDISPATCHINFOS;
        case 1121: return REQUESTOPERATEDISPATCHTASK;
        case 1150: return REQUESTRAFFLEPOOL;
        case 1160: return REQUESTGETEVENTINFO;
        case 1161: return REQUESTFLUSHEVENT;
        case 1170: return REQUESTCOUNTBATTLE;
        case 1180: return REQUESTPLAYERITEMS;
        case 1190: return REQUESTUPPETSKILL;
        case 1580: return REQUESTPETEXP;
        case 1250: return REQUESTBALCKMARKET;
        case 1251: return REQUESTBUYBALCKMARKETITEM;
        case 1261: return REQUESTPETFORMATION;
        case 1262: return REQUESTGETPETFORMATION;
        case 1265: return REQUESTPETCOMPOSE;
        case 1272: return REQUESTPETBREEDINOFRMATION;
        case 1273: return REQUESTPETBREEDOPERATION;
        case 1274: return REQUESTPETBREED;
        case 1270: return REQUESTGETPETBREEDINFO;
        case 1271: return REQUESTOPERATEPETBREED;
        case 1300: return REQUESTGETPETGROWINFO;
        case 1301: return REQUESTPETEVOLUTION;
        case 1302: return REQUESTPETBREAKTHROUGH;
        case 1280: return REQUESTGETEXPERIENCES;
        case 1281: return REQUESTFLUSHEXPERIENCES;
        case 1282: return REQUESTOPERATEEXPERIENCES;
        case 1283: return REQUESTITEMCOMPOSE;
        case 1046: return REQUESTPHYSICAL;
        case 1318: return REQUESTGETROLEEXP;
        case 1303: return REQUESTOPERATIONFRIENDS;
        case 1370: return REQUESTGETMAP;
        case 1371: return REQUESTUPDMAP;
        case 1285: return REQUESTREMOVEPET;
        case 1290: return REQUESTDAILYTANSKS;
        case 1291: return REQUESTCOMPLETEDAILYTASKS;
        case 1292: return REQUESTTASK;
        case 1293: return REQUESTACHIEVEMENTTYPE;
        case 1294: return REQUESTACHIEVEMENTIS;
        case 1295: return REQUESTACHIEVEMENT;
        case 1056: return REQUESTADDITEM;
        case 1057: return REQUESTSYNTHESIS;
        case 1298: return REQUESTPLAYERRECOMMEND;
        case 1299: return REQUESTFRIENDSAPPLY;
        case 1304: return REQUESTSENDOUTINFORMATION;
        case 1305: return REQUESTCHATRECORD;
        case 1306: return REQUESTUPDATETYPE;
        case 1307: return REQUESTRANK;
        case 1308: return REQUESTLEVEL;
        case 1309: return REQUESTLEVELD;
        case 1320: return REQUESTPlont;
        case 1321: return REQUESTChap;
        case 1322: return REQUESTGetEquipA;
        case 1323: return REQUESTDDDEquip;
        case 1324: return REQUESTEquipF;
        case 1325: return REQUESTbossTiem;
        case 1326: return REQUESTbossdear;
        case 1330: return REQUESTPVPPetOperate;
        case 1331: return REQUESTPVPBaseData;
        case 1332: return REQUESTPVPBattle;
        case 1333: return REQUESTPVPBattleResult;
        case 1334: return REQUESTPVPBATTLEREMAINTIME;
        case 1340: return REQUESTNewMailTest;
        case 1341: return REQUESTADITEM;
        case 1342: return REQUESTRECHAREGREBATEREWARD;
        case 1350: return REQUESTLIMITEDTIMEREWARD;
        case 1351: return REQUESTLIMITEDTIMEREWARDTYPE;
        case 1352: return REQUESTLIMITEDTIMEREWARDFINISH;
        case 1354: return REQUESTREMAINTIME;
        case 1360: return REQUESTPETEGGEXP;
        case 1361: return REQUESTADDPETEGGEXP;
        case 1362: return RESPONSEPETEGGFASTHATCH;
        case 1380: return REQUESTTEMITEM;
        case 1390: return REQUESTDELETEALLPETS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<CToS>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<CToS>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<CToS>() {
            public CToS findValueByNumber(int number) {
              return CToS.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(0);
    }

    private static final CToS[] VALUES = values();

    public static CToS valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private CToS(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.CToS)
  }

  /**
   * Protobuf enum {@code protocol.SToC}
   */
  public enum SToC
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>RESPONSESERVER = 2000;</code>
     *
     * <pre>
     *游戏区服列表
     * </pre>
     */
    RESPONSESERVER(0, 2000),
    /**
     * <code>RESPONSEREGISTER = 2005;</code>
     *
     * <pre>
     *注册
     * </pre>
     */
    RESPONSEREGISTER(1, 2005),
    /**
     * <code>RESPONSELOGIN = 2001;</code>
     *
     * <pre>
     *登入
     * </pre>
     */
    RESPONSELOGIN(2, 2001),
    /**
     * <code>RESPONSECREATE = 2002;</code>
     *
     * <pre>
     *创建角色
     * </pre>
     */
    RESPONSECREATE(3, 2002),
    /**
     * <code>RESPONSEROLESEX = 2112;</code>
     *
     * <pre>
     *创建性别
     * </pre>
     */
    RESPONSEROLESEX(4, 2112),
    /**
     * <code>RESPONSEGETSEX = 2213;</code>
     *
     * <pre>
     *获得性别
     * </pre>
     */
    RESPONSEGETSEX(5, 2213),
    /**
     * <code>RESPONSESETINFO = 2003;</code>
     *
     * <pre>
     *设置名字
     * </pre>
     */
    RESPONSESETINFO(6, 2003),
    /**
     * <code>RESPONSECHOOSEROLE = 2004;</code>
     *
     * <pre>
     *选择角色
     * </pre>
     */
    RESPONSECHOOSEROLE(7, 2004),
    /**
     * <code>RESPONSEBUYITEM = 2006;</code>
     *
     * <pre>
     *购买物品
     * </pre>
     */
    RESPONSEBUYITEM(8, 2006),
    /**
     * <code>RESPONSEUSEITEM = 2007;</code>
     *
     * <pre>
     *使用物品
     * </pre>
     */
    RESPONSEUSEITEM(9, 2007),
    /**
     * <code>RESPONSEOPENBAGCELL = 2008;</code>
     *
     * <pre>
     *解锁背包格子
     * </pre>
     */
    RESPONSEOPENBAGCELL(10, 2008),
    /**
     * <code>RESPONSECOMPOSE = 2009;</code>
     *
     * <pre>
     *合成
     * </pre>
     */
    RESPONSECOMPOSE(11, 2009),
    /**
     * <code>RESPONSEBEGINMISSION = 2010;</code>
     *
     * <pre>
     *开始闯关
     * </pre>
     */
    RESPONSEBEGINMISSION(12, 2010),
    /**
     * <code>RESPONSECOUNTMISSION = 2011;</code>
     *
     * <pre>
     *结算闯关
     * </pre>
     */
    RESPONSECOUNTMISSION(13, 2011),
    /**
     * <code>RESPONSEBEGINENDLESS = 2012;</code>
     *
     * <pre>
     *开始无尽
     * </pre>
     */
    RESPONSEBEGINENDLESS(14, 2012),
    /**
     * <code>RESPONSECOUNTENDLESS = 2013;</code>
     *
     * <pre>
     *结算无尽
     * </pre>
     */
    RESPONSECOUNTENDLESS(15, 2013),
    /**
     * <code>RESPONSEUPDATENEWUSER = 2014;</code>
     *
     * <pre>
     *更新新手引导
     * </pre>
     */
    RESPONSEUPDATENEWUSER(16, 2014),
    /**
     * <code>RESPONSEPETHATCH = 2466;</code>
     */
    RESPONSEPETHATCH(17, 2466),
    /**
     * <code>RESPONSESENDCHAT = 2020;</code>
     *
     * <pre>
     *发送聊天
     * </pre>
     */
    RESPONSESENDCHAT(18, 2020),
    /**
     * <code>REPORTCHAT = 2021;</code>
     *
     * <pre>
     *发送聊天
     * </pre>
     */
    REPORTCHAT(19, 2021),
    /**
     * <code>REPORTMESSAGE = 2022;</code>
     *
     * <pre>
     *发送消息
     * </pre>
     */
    REPORTMESSAGE(20, 2022),
    /**
     * <code>REPORTSYSTEM = 2023;</code>
     *
     * <pre>
     *系统播报
     * </pre>
     */
    REPORTSYSTEM(21, 2023),
    /**
     * <code>RESPONSEPHYSICAL = 2046;</code>
     *
     * <pre>
     *消耗
     * </pre>
     */
    RESPONSEPHYSICAL(22, 2046),
    /**
     * <code>RESPONSEFINISHTASK = 2030;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    RESPONSEFINISHTASK(23, 2030),
    /**
     * <code>REPORTUPDATETASK = 2031;</code>
     *
     * <pre>
     *更新任务
     * </pre>
     */
    REPORTUPDATETASK(24, 2031),
    /**
     * <code>RESPONSEGETROLEEXP = 2318;</code>
     *
     * <pre>
     *得到人物经验
     * </pre>
     */
    RESPONSEGETROLEEXP(25, 2318),
    /**
     * <code>RESPONSEBUYCLOTHING = 2051;</code>
     *
     * <pre>
     *购买衣服
     * </pre>
     */
    RESPONSEBUYCLOTHING(26, 2051),
    /**
     * <code>RESPONSECHANGECUPBOARD = 2052;</code>
     *
     * <pre>
     *衣柜操作
     * </pre>
     */
    RESPONSECHANGECUPBOARD(27, 2052),
    /**
     * <code>RESPONSECHANGEDRESS = 2053;</code>
     *
     * <pre>
     *换装扮
     * </pre>
     */
    RESPONSECHANGEDRESS(28, 2053),
    /**
     * <code>RESPONSECUPBOARDTOBODY = 2054;</code>
     *
     * <pre>
     *衣柜到当前装
     * </pre>
     */
    RESPONSECUPBOARDTOBODY(29, 2054),
    /**
     * <code>RESPONSESTARBITOMONEY = 2055;</code>
     *
     * <pre>
     *星币换金币
     * </pre>
     */
    RESPONSESTARBITOMONEY(30, 2055),
    /**
     * <code>REPORTITEM = 2056;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    REPORTITEM(31, 2056),
    /**
     * <code>RESPONSEOPERATEFRIEND = 2060;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    RESPONSEOPERATEFRIEND(32, 2060),
    /**
     * <code>RESPONSEOPERATEFRIEND1 = 2201;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    RESPONSEOPERATEFRIEND1(33, 2201),
    /**
     * <code>RESPONSEOPERATEMESSAGE = 2061;</code>
     *
     * <pre>
     *操作消息
     * </pre>
     */
    RESPONSEOPERATEMESSAGE(34, 2061),
    /**
     * <code>REPORTFRIEND = 2062;</code>
     *
     * <pre>
     *增加好友
     * </pre>
     */
    REPORTFRIEND(35, 2062),
    /**
     * <code>REPORTDELETEFRIEND = 2063;</code>
     *
     * <pre>
     *删除好友
     * </pre>
     */
    REPORTDELETEFRIEND(36, 2063),
    /**
     * <code>RESPONSERECOMMENDFRIEND = 2064;</code>
     *
     * <pre>
     *推荐好友
     * </pre>
     */
    RESPONSERECOMMENDFRIEND(37, 2064),
    /**
     * <code>REPORTINANDOUTSTATION = 2065;</code>
     *
     * <pre>
     *通知进出车站
     * </pre>
     */
    REPORTINANDOUTSTATION(38, 2065),
    /**
     * <code>REPORTSTATIONLOCATION = 2066;</code>
     *
     * <pre>
     *车站走动
     * </pre>
     */
    REPORTSTATIONLOCATION(39, 2066),
    /**
     * <code>REPORTUPDATESTATIONROLE = 2075;</code>
     *
     * <pre>
     *车站走动
     * </pre>
     */
    REPORTUPDATESTATIONROLE(40, 2075),
    /**
     * <code>RESPONSEGETMAP = 2370;</code>
     *
     * <pre>
     *地图
     * </pre>
     */
    RESPONSEGETMAP(41, 2370),
    /**
     * <code>RESPONSEUPDMAP = 2371;</code>
     *
     * <pre>
     *修改地图
     * </pre>
     */
    RESPONSEUPDMAP(42, 2371),
    /**
     * <code>RESPONSEGIVEPRESENT = 2067;</code>
     *
     * <pre>
     *赠送礼物
     * </pre>
     */
    RESPONSEGIVEPRESENT(43, 2067),
    /**
     * <code>RESPONSEACCUSATION = 2068;</code>
     *
     * <pre>
     *举报
     * </pre>
     */
    RESPONSEACCUSATION(44, 2068),
    /**
     * <code>REPORTUPDATEFRIEND = 2069;</code>
     *
     * <pre>
     *更新好友信息
     * </pre>
     */
    REPORTUPDATEFRIEND(45, 2069),
    /**
     * <code>RESPONSEGETRANK = 2070;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    RESPONSEGETRANK(46, 2070),
    /**
     * <code>RESPONSEGETRANKING = 2095;</code>
     *
     * <pre>
     *排
     * </pre>
     */
    RESPONSEGETRANKING(47, 2095),
    /**
     * <code>RESPONSEGETTOTALENDLESS = 2071;</code>
     *
     * <pre>
     *获取无尽总积分奖励
     * </pre>
     */
    RESPONSEGETTOTALENDLESS(48, 2071),
    /**
     * <code>RESPONSEOPERATEROLE = 2311;</code>
     *
     * <pre>
     *操作角色
     * </pre>
     */
    RESPONSEOPERATEROLE(49, 2311),
    /**
     * <code>RESPONSEJOINROOM = 2080;</code>
     *
     * <pre>
     *进入房间
     * </pre>
     */
    RESPONSEJOINROOM(50, 2080),
    /**
     * <code>RESPONSECREATEROOM = 2081;</code>
     *
     * <pre>
     *创建房间
     * </pre>
     */
    RESPONSECREATEROOM(51, 2081),
    /**
     * <code>RESPONSELEAVEROOM = 2082;</code>
     *
     * <pre>
     *离开房间
     * </pre>
     */
    RESPONSELEAVEROOM(52, 2082),
    /**
     * <code>RESPONSEUPDATEROOMINFO = 2083;</code>
     *
     * <pre>
     *更新房间信息
     * </pre>
     */
    RESPONSEUPDATEROOMINFO(53, 2083),
    /**
     * <code>RESPONSEUPDATEROOMROLE = 2084;</code>
     *
     * <pre>
     *更新房间内玩家信息
     * </pre>
     */
    RESPONSEUPDATEROOMROLE(54, 2084),
    /**
     * <code>RESPONSEJOINFIGHTHALL = 2085;</code>
     *
     * <pre>
     *请求进出多人战斗大厅
     * </pre>
     */
    RESPONSEJOINFIGHTHALL(55, 2085),
    /**
     * <code>RESPONSESTARTINROOM = 2086;</code>
     *
     * <pre>
     *房主开始游戏
     * </pre>
     */
    RESPONSESTARTINROOM(56, 2086),
    /**
     * <code>REPORTROOM = 2087;</code>
     *
     * <pre>
     *更新房间数量
     * </pre>
     */
    REPORTROOM(57, 2087),
    /**
     * <code>REPORTADDROOMROLE = 2088;</code>
     *
     * <pre>
     *进入房间玩家
     * </pre>
     */
    REPORTADDROOMROLE(58, 2088),
    /**
     * <code>REPORTUPDATEROOMINSIDE = 2089;</code>
     *
     * <pre>
     *通知更新房内玩家
     * </pre>
     */
    REPORTUPDATEROOMINSIDE(59, 2089),
    /**
     * <code>REPORTCOMETOFIGHT = 2090;</code>
     *
     * <pre>
     *通知去战斗服
     * </pre>
     */
    REPORTCOMETOFIGHT(60, 2090),
    /**
     * <code>RESPONSESTAMP = 2100;</code>
     *
     * <pre>
     *时间戳
     * </pre>
     */
    RESPONSESTAMP(61, 2100),
    /**
     * <code>REPORTREPEATLOGIN = 2101;</code>
     *
     * <pre>
     *重复登入
     * </pre>
     */
    REPORTREPEATLOGIN(62, 2101),
    /**
     * <code>REPORTFORCETOOFFLINE = 2102;</code>
     *
     * <pre>
     *强制下线
     * </pre>
     */
    REPORTFORCETOOFFLINE(63, 2102),
    /**
     * <code>RESPONSERECONNECTION = 2200;</code>
     *
     * <pre>
     *断线重连
     * </pre>
     */
    RESPONSERECONNECTION(64, 2200),
    /**
     * <code>RESPONSECHOOSEPAY = 2500;</code>
     *
     * <pre>
     *选择购买内容
     * </pre>
     */
    RESPONSECHOOSEPAY(65, 2500),
    /**
     * <code>RESPONSESUBMITPAYBACK = 2501;</code>
     *
     * <pre>
     *支付返回
     * </pre>
     */
    RESPONSESUBMITPAYBACK(66, 2501),
    /**
     * <code>RESPONSEGOOGLEPAY = 2502;</code>
     *
     * <pre>
     *google支付返回
     * </pre>
     */
    RESPONSEGOOGLEPAY(67, 2502),
    /**
     * <code>RESPONSEWECHATPAY = 2503;</code>
     *
     * <pre>
     *微信支付返回
     * </pre>
     */
    RESPONSEWECHATPAY(68, 2503),
    /**
     * <code>RESPONSEALIPAY = 2504;</code>
     *
     * <pre>
     *支付宝支付返回
     * </pre>
     */
    RESPONSEALIPAY(69, 2504),
    /**
     * <code>RESPONSECONFIRMHUAWEIPURCHASE = 2505;</code>
     *
     * <pre>
     *华为确认支付返回
     * </pre>
     */
    RESPONSECONFIRMHUAWEIPURCHASE(70, 2505),
    /**
     * <code>RESPONSEYSDKBALANCE = 2506;</code>
     *
     * <pre>
     *YSDK余额返回
     * </pre>
     */
    RESPONSEYSDKBALANCE(71, 2506),
    /**
     * <code>RESPONSEBEGINHEADBALL = 2018;</code>
     *
     * <pre>
     *请求顶球返回
     * </pre>
     */
    RESPONSEBEGINHEADBALL(72, 2018),
    /**
     * <code>RESPONSECOUNTHEADBALL = 2019;</code>
     *
     * <pre>
     *请求结算顶球模式返回
     * </pre>
     */
    RESPONSECOUNTHEADBALL(73, 2019),
    /**
     * <code>RESPONSEJEWEL = 3015;</code>
     *
     * <pre>
     *返回砖石
     * </pre>
     */
    RESPONSEJEWEL(74, 3015),
    /**
     * <code>RREPORTADREWARD = 2601;</code>
     *
     * <pre>
     *广告奖励
     * </pre>
     */
    RREPORTADREWARD(75, 2601),
    /**
     * <code>RESPONSEADPLAY = 2602;</code>
     *
     * <pre>
     *请求播放广告
     * </pre>
     */
    RESPONSEADPLAY(76, 2602),
    /**
     * <code>RESPONSEADNUM = 2603;</code>
     *
     * <pre>
     *请求播放广告次数
     * </pre>
     */
    RESPONSEADNUM(77, 2603),
    /**
     * <code>RESPONSECOUNTGOLD = 2700;</code>
     *
     * <pre>
     *结算金币模式
     * </pre>
     */
    RESPONSECOUNTGOLD(78, 2700),
    /**
     * <code>RESPONSEBEGINGOLD = 2701;</code>
     *
     * <pre>
     *返回金币模式球
     * </pre>
     */
    RESPONSEBEGINGOLD(79, 2701),
    /**
     * <code>RESPONSEBEGINDOWN = 2702;</code>
     *
     * <pre>
     *返回进入落球请求
     * </pre>
     */
    RESPONSEBEGINDOWN(80, 2702),
    /**
     * <code>RESPONSEBEGINSTACK = 2706;</code>
     *
     * <pre>
     *返回进入堆球请求
     * </pre>
     */
    RESPONSEBEGINSTACK(81, 2706),
    /**
     * <code>RESPONSECOUNTDOWN = 2704;</code>
     *
     * <pre>
     *返回落球结算
     * </pre>
     */
    RESPONSECOUNTDOWN(82, 2704),
    /**
     * <code>RESPONSECOUNTSTACK = 2705;</code>
     *
     * <pre>
     *返回堆球结算
     * </pre>
     */
    RESPONSECOUNTSTACK(83, 2705),
    /**
     * <code>RESPONSECOUNTLOTTO = 2703;</code>
     *
     * <pre>
     *返回合成道具
     * </pre>
     */
    RESPONSECOUNTLOTTO(84, 2703),
    /**
     * <code>RESPONSESOURCESMACHINE = 2078;</code>
     *
     * <pre>
     *返回能源机合成信息
     * </pre>
     */
    RESPONSESOURCESMACHINE(85, 2078),
    /**
     * <code>RESPONSEMAKEITEM = 2076;</code>
     *
     * <pre>
     *返回培养皿请求的回复
     * </pre>
     */
    RESPONSEMAKEITEM(86, 2076),
    /**
     * <code>RESPONSEPRODUCT = 2077;</code>
     *
     * <pre>
     *返回成品获得的物品
     * </pre>
     */
    RESPONSEPRODUCT(87, 2077),
    /**
     * <code>RESPONSEWARDROBEVIEW = 2800;</code>
     *
     * <pre>
     *返回衣柜视图
     * </pre>
     */
    RESPONSEWARDROBEVIEW(88, 2800),
    /**
     * <code>RESPONSEVISITOROPERATION = 2900;</code>
     *
     * <pre>
     *返回游客登录相关操作
     * </pre>
     */
    RESPONSEVISITOROPERATION(89, 2900),
    /**
     * <code>RESPONSEAPPROVAL = 2920;</code>
     *
     * <pre>
     *点赞回复
     * </pre>
     */
    RESPONSEAPPROVAL(90, 2920),
    /**
     * <code>RESPONSAUTHENTICATION = 2921;</code>
     *
     * <pre>
     *身份认证
     * </pre>
     */
    RESPONSAUTHENTICATION(91, 2921),
    /**
     * <code>RESPONSEGETAPPROVAL = 2922;</code>
     *
     * <pre>
     *获得当前玩家受到点赞数
     * </pre>
     */
    RESPONSEGETAPPROVAL(92, 2922),
    /**
     * <code>RESPOSESTARTGAMECOPY = 2122;</code>
     *
     * <pre>
     *返回进入每日关卡（任务金币）
     * </pre>
     */
    RESPOSESTARTGAMECOPY(93, 2122),
    /**
     * <code>REAPOSECOUNTGAMECOPY = 2123;</code>
     *
     * <pre>
     *返回每日关卡结算
     * </pre>
     */
    REAPOSECOUNTGAMECOPY(94, 2123),
    /**
     * <code>RESPOSEGETLUCKYITEM = 2124;</code>
     *
     * <pre>
     *返回车站界面幸运物品领取
     * </pre>
     */
    RESPOSEGETLUCKYITEM(95, 2124),
    /**
     * <code>RESPOSEUPDATELUCKYITEM = 2125;</code>
     *
     * <pre>
     *通知用户幸运物品刷新
     * </pre>
     */
    RESPOSEUPDATELUCKYITEM(96, 2125),
    /**
     * <code>RESPOSEFINISHACTIVITIES = 2126;</code>
     *
     * <pre>
     * 返回完成活动的奖励回复
     * </pre>
     */
    RESPOSEFINISHACTIVITIES(97, 2126),
    /**
     * <code>REPORTUPDATEACTIVITIES = 2127;</code>
     *
     * <pre>
     *通知更新活动信息
     * </pre>
     */
    REPORTUPDATEACTIVITIES(98, 2127),
    /**
     * <code>REPORTBROADCASTLUCKYUSER = 2128;</code>
     *
     * <pre>
     *  广播通知玩家获得稀有物品
     * </pre>
     */
    REPORTBROADCASTLUCKYUSER(99, 2128),
    /**
     * <code>RESPOSEMARKETINFORMATION = 2129;</code>
     *
     * <pre>
     *交易市场信息回复
     * </pre>
     */
    RESPOSEMARKETINFORMATION(100, 2129),
    /**
     * <code>RESPOSEGOODSOPERATE = 2130;</code>
     *
     * <pre>
     * 交易市场商品买入卖出
     * </pre>
     */
    RESPOSEGOODSOPERATE(101, 2130),
    /**
     * <code>RESPOSETICKTEEXCHANGE = 2131;</code>
     *
     * <pre>
     * 票券兑换
     * </pre>
     */
    RESPOSETICKTEEXCHANGE(102, 2131),
    /**
     * <code>RESPOSEQUERYROLEINFORMATION = 2135;</code>
     *
     * <pre>
     * 查询玩家角色信息
     * </pre>
     */
    RESPOSEQUERYROLEINFORMATION(103, 2135),
    /**
     * <code>RESPOSEWAITITEM = 2136;</code>
     *
     * <pre>
     * 挂机获得物品
     * </pre>
     */
    RESPOSEWAITITEM(104, 2136),
    /**
     * <code>RESPOSEUPWAITITEMLEVEL = 2137;</code>
     *
     * <pre>
     *提升等物品功能背包等級
     * </pre>
     */
    RESPOSEUPWAITITEMLEVEL(105, 2137),
    /**
     * <code>RESPOSEGETWAITITEM = 2138;</code>
     *
     * <pre>
     *活得等物品功能物品
     * </pre>
     */
    RESPOSEGETWAITITEM(106, 2138),
    /**
     * <code>RESPOSEHOUSE = 2600;</code>
     *
     * <pre>
     *获取玩家房间装饰信息
     * </pre>
     */
    RESPOSEHOUSE(107, 2600),
    /**
     * <code>RESPOSEHOUSEPART = 2605;</code>
     *
     * <pre>
     *保存玩家房间装饰记录
     * </pre>
     */
    RESPOSEHOUSEPART(108, 2605),
    /**
     * <code>RESPOSEBUYFURNITURE = 2670;</code>
     *
     * <pre>
     * 买家具
     * </pre>
     */
    RESPOSEBUYFURNITURE(109, 2670),
    /**
     * <code>RESPOSEOPERATEMAIL = 2110;</code>
     *
     * <pre>
     * 操作邮件
     * </pre>
     */
    RESPOSEOPERATEMAIL(110, 2110),
    /**
     * <code>REPORTNEWMAIL = 2111;</code>
     *
     * <pre>
     *通知新邮件
     * </pre>
     */
    REPORTNEWMAIL(111, 2111),
    /**
     * <code>REPORTNEWNOTICE = 2222;</code>
     *
     * <pre>
     *通知新公告
     * </pre>
     */
    REPORTNEWNOTICE(112, 2222),
    /**
     * <code>RESPOSEGETPETSINFO = 2210;</code>
     *
     * <pre>
     *获取玩家宠物信息
     * </pre>
     */
    RESPOSEGETPETSINFO(113, 2210),
    /**
     * <code>RESPONSEGETEQUIP = 2215;</code>
     *
     * <pre>
     *获取玩家装备
     * </pre>
     */
    RESPONSEGETEQUIP(114, 2215),
    /**
     * <code>RESPONSECHOICEEQUIP = 2218;</code>
     *
     * <pre>
     *选择装备
     * </pre>
     */
    RESPONSECHOICEEQUIP(115, 2218),
    /**
     * <code>RESPOSEOPERATEPET = 2211;</code>
     *
     * <pre>
     *操作宠物
     * </pre>
     */
    RESPOSEOPERATEPET(116, 2211),
    /**
     * <code>RESPONSEOPERATEEQUIP = 2216;</code>
     *
     * <pre>
     *操作装备
     * </pre>
     */
    RESPONSEOPERATEEQUIP(117, 2216),
    /**
     * <code>RESPONSECHANGEEQUIP = 2217;</code>
     *
     * <pre>
     *换装备
     * </pre>
     */
    RESPONSECHANGEEQUIP(118, 2217),
    /**
     * <code>RESPOSEBINDIDCARD = 2113;</code>
     *
     * <pre>
     *  绑定身份证
     * </pre>
     */
    RESPOSEBINDIDCARD(119, 2113),
    /**
     * <code>RESPOSEGETDISPATCHINFOS = 2120;</code>
     *
     * <pre>
     *  获得派遣任务信息
     * </pre>
     */
    RESPOSEGETDISPATCHINFOS(120, 2120),
    /**
     * <code>RESPOSEOPERATEDISPATCHTASK = 2121;</code>
     *
     * <pre>
     *    操作派遣任务
     * </pre>
     */
    RESPOSEOPERATEDISPATCHTASK(121, 2121),
    /**
     * <code>RESPOSERAFFLEPOOL = 2150;</code>
     *
     * <pre>
     *获取抽奖池
     * </pre>
     */
    RESPOSERAFFLEPOOL(122, 2150),
    /**
     * <code>RESPONSEPETEXP = 2580;</code>
     *
     * <pre>
     *获取宠物经验
     * </pre>
     */
    RESPONSEPETEXP(123, 2580),
    /**
     * <code>RESPOSEGETEVENTINFO = 2160;</code>
     *
     * <pre>
     *事件信息
     * </pre>
     */
    RESPOSEGETEVENTINFO(124, 2160),
    /**
     * <code>RESPOSEFLUSHEVENT = 2161;</code>
     *
     * <pre>
     *刷新事件
     * </pre>
     */
    RESPOSEFLUSHEVENT(125, 2161),
    /**
     * <code>RESPOSECOUNTBATTLE = 2170;</code>
     *
     * <pre>
     * 怪物副本结算
     * </pre>
     */
    RESPOSECOUNTBATTLE(126, 2170),
    /**
     * <code>RESPOSEPLAYERITEMS = 2180;</code>
     *
     * <pre>
     *获取玩家物品数据
     * </pre>
     */
    RESPOSEPLAYERITEMS(127, 2180),
    /**
     * <code>RESPOSEUPPETSKILL = 2190;</code>
     *
     * <pre>
     *强化宠物技能等级
     * </pre>
     */
    RESPOSEUPPETSKILL(128, 2190),
    /**
     * <code>RESPOSEBALCKMARKET = 2250;</code>
     *
     * <pre>
     *获取黑市商店信息
     * </pre>
     */
    RESPOSEBALCKMARKET(129, 2250),
    /**
     * <code>RESPOSEBUYBALCKMARKETITEM = 2251;</code>
     *
     * <pre>
     * 购买黑市商店物品
     * </pre>
     */
    RESPOSEBUYBALCKMARKETITEM(130, 2251),
    /**
     * <code>RESPOSEPETFORMATION = 2261;</code>
     *
     * <pre>
     *宠物编队
     * </pre>
     */
    RESPOSEPETFORMATION(131, 2261),
    /**
     * <code>RESPOSEGETPETFORMATION = 2262;</code>
     *
     * <pre>
     *获取宠物编队信息
     * </pre>
     */
    RESPOSEGETPETFORMATION(132, 2262),
    /**
     * <code>RESPOSEPETCOMPOSE = 2265;</code>
     *
     * <pre>
     * 宠物合成
     * </pre>
     */
    RESPOSEPETCOMPOSE(133, 2265),
    /**
     * <code>RESPOSEGETPETBREEDINFO = 2270;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    RESPOSEGETPETBREEDINFO(134, 2270),
    /**
     * <code>RESPOSEOPERATEPETBREED = 2271;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    RESPOSEOPERATEPETBREED(135, 2271),
    /**
     * <code>RESPONSEPETBREEDINOFRMATION = 2272;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    RESPONSEPETBREEDINOFRMATION(136, 2272),
    /**
     * <code>RESPONSEPETBREEDOPERATION = 2273;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    RESPONSEPETBREEDOPERATION(137, 2273),
    /**
     * <code>RESPONSEPETBREED = 2274;</code>
     *
     * <pre>
     *登录返回正在孵化的蛋
     * </pre>
     */
    RESPONSEPETBREED(138, 2274),
    /**
     * <code>RESPOSEGETPETGROWINFO = 2300;</code>
     *
     * <pre>
     *获取正在进化的宠物链表
     * </pre>
     */
    RESPOSEGETPETGROWINFO(139, 2300),
    /**
     * <code>RESPOSEPETEVOLUTION = 2301;</code>
     *
     * <pre>
     *操作宠物进行进化
     * </pre>
     */
    RESPOSEPETEVOLUTION(140, 2301),
    /**
     * <code>RESPOSEPETBREAKTHROUGH = 2302;</code>
     *
     * <pre>
     *宠物突破
     * </pre>
     */
    RESPOSEPETBREAKTHROUGH(141, 2302),
    /**
     * <code>RESPOSEGETEXPERIENCES = 2280;</code>
     *
     * <pre>
     *获取探险任务的信息
     * </pre>
     */
    RESPOSEGETEXPERIENCES(142, 2280),
    /**
     * <code>RESPOSEFLUSHEXPERIENCES = 2281;</code>
     *
     * <pre>
     *刷新探险任务
     * </pre>
     */
    RESPOSEFLUSHEXPERIENCES(143, 2281),
    /**
     * <code>RESPOSEOPERATEEXPERIENCES = 2282;</code>
     *
     * <pre>
     *操作探险任务
     * </pre>
     */
    RESPOSEOPERATEEXPERIENCES(144, 2282),
    /**
     * <code>RESPOSEITEMCOMPOSE = 2283;</code>
     *
     * <pre>
     *道具合成 
     * </pre>
     */
    RESPOSEITEMCOMPOSE(145, 2283),
    /**
     * <code>RESPONSEOPERATIONFRIENDS = 2303;</code>
     *
     * <pre>
     *操作好友申请信息
     * </pre>
     */
    RESPONSEOPERATIONFRIENDS(146, 2303),
    /**
     * <code>RESPONSESYNTHESIS = 2057;</code>
     *
     * <pre>
     *合成道具
     * </pre>
     */
    RESPONSESYNTHESIS(147, 2057),
    /**
     * <code>RESPONSEREMOVEPET = 2285;</code>
     *
     * <pre>
     *回收宠物
     * </pre>
     */
    RESPONSEREMOVEPET(148, 2285),
    /**
     * <code>RESPONSEDAILYTANSKS = 2290;</code>
     *
     * <pre>
     *进行任务
     * </pre>
     */
    RESPONSEDAILYTANSKS(149, 2290),
    /**
     * <code>RESPONSECOMPLETEDAILYTASKS = 2291;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    RESPONSECOMPLETEDAILYTASKS(150, 2291),
    /**
     * <code>RESPONSETASK = 2292;</code>
     *
     * <pre>
     *进入游戏返回任务进度
     * </pre>
     */
    RESPONSETASK(151, 2292),
    /**
     * <code>RESPONSEACHIEVEMENTTYPE = 2293;</code>
     *
     * <pre>
     *进行成就
     * </pre>
     */
    RESPONSEACHIEVEMENTTYPE(152, 2293),
    /**
     * <code>RESPONSEACHIEVEMENTIS = 2294;</code>
     *
     * <pre>
     *完成成就
     * </pre>
     */
    RESPONSEACHIEVEMENTIS(153, 2294),
    /**
     * <code>RESPONSEACHIEVEMENT = 2295;</code>
     *
     * <pre>
     *进入游戏返回成就进度
     * </pre>
     */
    RESPONSEACHIEVEMENT(154, 2295),
    /**
     * <code>RESPONSEPLAYERONLINEORNOT = 2296;</code>
     *
     * <pre>
     *登录游戏刷新用户好友信息
     * </pre>
     */
    RESPONSEPLAYERONLINEORNOT(155, 2296),
    /**
     * <code>RESPONSEPLAYEROFFLINEORNOT = 2297;</code>
     *
     * <pre>
     *玩家上线离线刷新
     * </pre>
     */
    RESPONSEPLAYEROFFLINEORNOT(156, 2297),
    /**
     * <code>RESPONSEPLAYERRECOMMEND = 2298;</code>
     *
     * <pre>
     *推荐玩家
     * </pre>
     */
    RESPONSEPLAYERRECOMMEND(157, 2298),
    /**
     * <code>RESPONSEFRIENDSAPPLY = 2299;</code>
     *
     * <pre>
     *上线刷新好友申请信息
     * </pre>
     */
    RESPONSEFRIENDSAPPLY(158, 2299),
    /**
     * <code>RESPONSESENDOUTINFORMATION = 2304;</code>
     *
     * <pre>
     *发送消息
     * </pre>
     */
    RESPONSESENDOUTINFORMATION(159, 2304),
    /**
     * <code>RESPONSECHATRECORD = 2305;</code>
     *
     * <pre>
     *上线获取玩家聊天记录
     * </pre>
     */
    RESPONSECHATRECORD(160, 2305),
    /**
     * <code>RESPONSEUPDATETYPE = 2306;</code>
     *
     * <pre>
     *修改状态
     * </pre>
     */
    RESPONSEUPDATETYPE(161, 2306),
    /**
     * <code>RESPONSERANK = 2307;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    RESPONSERANK(162, 2307),
    /**
     * <code>RESPONSELEVEL = 2308;</code>
     *
     * <pre>
     *关卡
     * </pre>
     */
    RESPONSELEVEL(163, 2308),
    /**
     * <code>RESPPNSELEVELD = 2309;</code>
     *
     * <pre>
     *操作关卡
     * </pre>
     */
    RESPPNSELEVELD(164, 2309),
    /**
     * <code>RESPONSETemper = 2315;</code>
     *
     * <pre>
     *温度层
     * </pre>
     */
    RESPONSETemper(165, 2315),
    /**
     * <code>RESPONSEPlont = 2320;</code>
     *
     * <pre>
     *获取剧情11
     * </pre>
     */
    RESPONSEPlont(166, 2320),
    /**
     * <code>RESPONSEChap = 2321;</code>
     *
     * <pre>
     *返回剧情2
     * </pre>
     */
    RESPONSEChap(167, 2321),
    /**
     * <code>RESPONSEGetEquipA = 2322;</code>
     *
     * <pre>
     *获取装备
     * </pre>
     */
    RESPONSEGetEquipA(168, 2322),
    /**
     * <code>RESPONSEDDDEquip = 2323;</code>
     *
     * <pre>
     *返回装备
     * </pre>
     */
    RESPONSEDDDEquip(169, 2323),
    /**
     * <code>RESPONSEbosstime = 2325;</code>
     *
     * <pre>
     *世界boss
     * </pre>
     */
    RESPONSEbosstime(170, 2325),
    /**
     * <code>RESPONSEbossdown = 2326;</code>
     *
     * <pre>
     *boss剩余血量
     * </pre>
     */
    RESPONSEbossdown(171, 2326),
    /**
     * <code>RESPONSEbosslive = 2327;</code>
     *
     * <pre>
     *boss血量及死亡
     * </pre>
     */
    RESPONSEbosslive(172, 2327),
    /**
     * <code>RESPONSEPVPPetOperate = 2330;</code>
     *
     * <pre>
     * PVP宠物存储
     * </pre>
     */
    RESPONSEPVPPetOperate(173, 2330),
    /**
     * <code>RESPONSEPVPBaseData = 2331;</code>
     *
     * <pre>
     * PVP个人信息
     * </pre>
     */
    RESPONSEPVPBaseData(174, 2331),
    /**
     * <code>RESPONSEPVPBattle = 2332;</code>
     *
     * <pre>
     * PVP战斗
     * </pre>
     */
    RESPONSEPVPBattle(175, 2332),
    /**
     * <code>RESPONSEPVPBATTLEREMAINTIME = 2334;</code>
     *
     * <pre>
     * PVP 剩余时间
     * </pre>
     */
    RESPONSEPVPBATTLEREMAINTIME(176, 2334),
    /**
     * <code>RESPONSEPAYDIAMOND = 2340;</code>
     *
     * <pre>
     * 玩家充值钻石总数
     * </pre>
     */
    RESPONSEPAYDIAMOND(177, 2340),
    /**
     * <code>RESPONSERECHAREGREBATEREWARD = 2342;</code>
     *
     * <pre>
     * 玩家充值钻石 领取奖励的任务ID
     * </pre>
     */
    RESPONSERECHAREGREBATEREWARD(178, 2342),
    /**
     * <code>RESPONSELIMITEDTIMEREWARD = 2350;</code>
     *
     * <pre>
     * 返回显示奖励的信息
     * </pre>
     */
    RESPONSELIMITEDTIMEREWARD(179, 2350),
    /**
     * <code>RESPONSELIMITEDTIMEREWARDTASKDATA = 2353;</code>
     *
     * <pre>
     * 返回全部数据
     * </pre>
     */
    RESPONSELIMITEDTIMEREWARDTASKDATA(180, 2353),
    /**
     * <code>RESPONSEREMAINTIME = 2354;</code>
     *
     * <pre>
     * 时间
     * </pre>
     */
    RESPONSEREMAINTIME(181, 2354),
    /**
     * <code>RESPONSEPETEGGEXP = 2360;</code>
     *
     * <pre>
     * 查询蛋的经验值和等级
     * </pre>
     */
    RESPONSEPETEGGEXP(182, 2360),
    /**
     * <code>RESPONSEALLPETEGGEXP = 2361;</code>
     *
     * <pre>
     * 蛋的全部数据
     * </pre>
     */
    RESPONSEALLPETEGGEXP(183, 2361),
    /**
     * <code>RESPONSETEMITEM = 2380;</code>
     *
     * <pre>
     * 温度层获得道具限制次数
     * </pre>
     */
    RESPONSETEMITEM(184, 2380),
    /**
     * <code>RESPONSEDELETEALLPETS = 2390;</code>
     *
     * <pre>
     * 删除全部宠物
     * </pre>
     */
    RESPONSEDELETEALLPETS(185, 2390),
    ;

    /**
     * <code>RESPONSESERVER = 2000;</code>
     *
     * <pre>
     *游戏区服列表
     * </pre>
     */
    public static final int RESPONSESERVER_VALUE = 2000;
    /**
     * <code>RESPONSEREGISTER = 2005;</code>
     *
     * <pre>
     *注册
     * </pre>
     */
    public static final int RESPONSEREGISTER_VALUE = 2005;
    /**
     * <code>RESPONSELOGIN = 2001;</code>
     *
     * <pre>
     *登入
     * </pre>
     */
    public static final int RESPONSELOGIN_VALUE = 2001;
    /**
     * <code>RESPONSECREATE = 2002;</code>
     *
     * <pre>
     *创建角色
     * </pre>
     */
    public static final int RESPONSECREATE_VALUE = 2002;
    /**
     * <code>RESPONSEROLESEX = 2112;</code>
     *
     * <pre>
     *创建性别
     * </pre>
     */
    public static final int RESPONSEROLESEX_VALUE = 2112;
    /**
     * <code>RESPONSEGETSEX = 2213;</code>
     *
     * <pre>
     *获得性别
     * </pre>
     */
    public static final int RESPONSEGETSEX_VALUE = 2213;
    /**
     * <code>RESPONSESETINFO = 2003;</code>
     *
     * <pre>
     *设置名字
     * </pre>
     */
    public static final int RESPONSESETINFO_VALUE = 2003;
    /**
     * <code>RESPONSECHOOSEROLE = 2004;</code>
     *
     * <pre>
     *选择角色
     * </pre>
     */
    public static final int RESPONSECHOOSEROLE_VALUE = 2004;
    /**
     * <code>RESPONSEBUYITEM = 2006;</code>
     *
     * <pre>
     *购买物品
     * </pre>
     */
    public static final int RESPONSEBUYITEM_VALUE = 2006;
    /**
     * <code>RESPONSEUSEITEM = 2007;</code>
     *
     * <pre>
     *使用物品
     * </pre>
     */
    public static final int RESPONSEUSEITEM_VALUE = 2007;
    /**
     * <code>RESPONSEOPENBAGCELL = 2008;</code>
     *
     * <pre>
     *解锁背包格子
     * </pre>
     */
    public static final int RESPONSEOPENBAGCELL_VALUE = 2008;
    /**
     * <code>RESPONSECOMPOSE = 2009;</code>
     *
     * <pre>
     *合成
     * </pre>
     */
    public static final int RESPONSECOMPOSE_VALUE = 2009;
    /**
     * <code>RESPONSEBEGINMISSION = 2010;</code>
     *
     * <pre>
     *开始闯关
     * </pre>
     */
    public static final int RESPONSEBEGINMISSION_VALUE = 2010;
    /**
     * <code>RESPONSECOUNTMISSION = 2011;</code>
     *
     * <pre>
     *结算闯关
     * </pre>
     */
    public static final int RESPONSECOUNTMISSION_VALUE = 2011;
    /**
     * <code>RESPONSEBEGINENDLESS = 2012;</code>
     *
     * <pre>
     *开始无尽
     * </pre>
     */
    public static final int RESPONSEBEGINENDLESS_VALUE = 2012;
    /**
     * <code>RESPONSECOUNTENDLESS = 2013;</code>
     *
     * <pre>
     *结算无尽
     * </pre>
     */
    public static final int RESPONSECOUNTENDLESS_VALUE = 2013;
    /**
     * <code>RESPONSEUPDATENEWUSER = 2014;</code>
     *
     * <pre>
     *更新新手引导
     * </pre>
     */
    public static final int RESPONSEUPDATENEWUSER_VALUE = 2014;
    /**
     * <code>RESPONSEPETHATCH = 2466;</code>
     */
    public static final int RESPONSEPETHATCH_VALUE = 2466;
    /**
     * <code>RESPONSESENDCHAT = 2020;</code>
     *
     * <pre>
     *发送聊天
     * </pre>
     */
    public static final int RESPONSESENDCHAT_VALUE = 2020;
    /**
     * <code>REPORTCHAT = 2021;</code>
     *
     * <pre>
     *发送聊天
     * </pre>
     */
    public static final int REPORTCHAT_VALUE = 2021;
    /**
     * <code>REPORTMESSAGE = 2022;</code>
     *
     * <pre>
     *发送消息
     * </pre>
     */
    public static final int REPORTMESSAGE_VALUE = 2022;
    /**
     * <code>REPORTSYSTEM = 2023;</code>
     *
     * <pre>
     *系统播报
     * </pre>
     */
    public static final int REPORTSYSTEM_VALUE = 2023;
    /**
     * <code>RESPONSEPHYSICAL = 2046;</code>
     *
     * <pre>
     *消耗
     * </pre>
     */
    public static final int RESPONSEPHYSICAL_VALUE = 2046;
    /**
     * <code>RESPONSEFINISHTASK = 2030;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    public static final int RESPONSEFINISHTASK_VALUE = 2030;
    /**
     * <code>REPORTUPDATETASK = 2031;</code>
     *
     * <pre>
     *更新任务
     * </pre>
     */
    public static final int REPORTUPDATETASK_VALUE = 2031;
    /**
     * <code>RESPONSEGETROLEEXP = 2318;</code>
     *
     * <pre>
     *得到人物经验
     * </pre>
     */
    public static final int RESPONSEGETROLEEXP_VALUE = 2318;
    /**
     * <code>RESPONSEBUYCLOTHING = 2051;</code>
     *
     * <pre>
     *购买衣服
     * </pre>
     */
    public static final int RESPONSEBUYCLOTHING_VALUE = 2051;
    /**
     * <code>RESPONSECHANGECUPBOARD = 2052;</code>
     *
     * <pre>
     *衣柜操作
     * </pre>
     */
    public static final int RESPONSECHANGECUPBOARD_VALUE = 2052;
    /**
     * <code>RESPONSECHANGEDRESS = 2053;</code>
     *
     * <pre>
     *换装扮
     * </pre>
     */
    public static final int RESPONSECHANGEDRESS_VALUE = 2053;
    /**
     * <code>RESPONSECUPBOARDTOBODY = 2054;</code>
     *
     * <pre>
     *衣柜到当前装
     * </pre>
     */
    public static final int RESPONSECUPBOARDTOBODY_VALUE = 2054;
    /**
     * <code>RESPONSESTARBITOMONEY = 2055;</code>
     *
     * <pre>
     *星币换金币
     * </pre>
     */
    public static final int RESPONSESTARBITOMONEY_VALUE = 2055;
    /**
     * <code>REPORTITEM = 2056;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public static final int REPORTITEM_VALUE = 2056;
    /**
     * <code>RESPONSEOPERATEFRIEND = 2060;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    public static final int RESPONSEOPERATEFRIEND_VALUE = 2060;
    /**
     * <code>RESPONSEOPERATEFRIEND1 = 2201;</code>
     *
     * <pre>
     *操作好友
     * </pre>
     */
    public static final int RESPONSEOPERATEFRIEND1_VALUE = 2201;
    /**
     * <code>RESPONSEOPERATEMESSAGE = 2061;</code>
     *
     * <pre>
     *操作消息
     * </pre>
     */
    public static final int RESPONSEOPERATEMESSAGE_VALUE = 2061;
    /**
     * <code>REPORTFRIEND = 2062;</code>
     *
     * <pre>
     *增加好友
     * </pre>
     */
    public static final int REPORTFRIEND_VALUE = 2062;
    /**
     * <code>REPORTDELETEFRIEND = 2063;</code>
     *
     * <pre>
     *删除好友
     * </pre>
     */
    public static final int REPORTDELETEFRIEND_VALUE = 2063;
    /**
     * <code>RESPONSERECOMMENDFRIEND = 2064;</code>
     *
     * <pre>
     *推荐好友
     * </pre>
     */
    public static final int RESPONSERECOMMENDFRIEND_VALUE = 2064;
    /**
     * <code>REPORTINANDOUTSTATION = 2065;</code>
     *
     * <pre>
     *通知进出车站
     * </pre>
     */
    public static final int REPORTINANDOUTSTATION_VALUE = 2065;
    /**
     * <code>REPORTSTATIONLOCATION = 2066;</code>
     *
     * <pre>
     *车站走动
     * </pre>
     */
    public static final int REPORTSTATIONLOCATION_VALUE = 2066;
    /**
     * <code>REPORTUPDATESTATIONROLE = 2075;</code>
     *
     * <pre>
     *车站走动
     * </pre>
     */
    public static final int REPORTUPDATESTATIONROLE_VALUE = 2075;
    /**
     * <code>RESPONSEGETMAP = 2370;</code>
     *
     * <pre>
     *地图
     * </pre>
     */
    public static final int RESPONSEGETMAP_VALUE = 2370;
    /**
     * <code>RESPONSEUPDMAP = 2371;</code>
     *
     * <pre>
     *修改地图
     * </pre>
     */
    public static final int RESPONSEUPDMAP_VALUE = 2371;
    /**
     * <code>RESPONSEGIVEPRESENT = 2067;</code>
     *
     * <pre>
     *赠送礼物
     * </pre>
     */
    public static final int RESPONSEGIVEPRESENT_VALUE = 2067;
    /**
     * <code>RESPONSEACCUSATION = 2068;</code>
     *
     * <pre>
     *举报
     * </pre>
     */
    public static final int RESPONSEACCUSATION_VALUE = 2068;
    /**
     * <code>REPORTUPDATEFRIEND = 2069;</code>
     *
     * <pre>
     *更新好友信息
     * </pre>
     */
    public static final int REPORTUPDATEFRIEND_VALUE = 2069;
    /**
     * <code>RESPONSEGETRANK = 2070;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    public static final int RESPONSEGETRANK_VALUE = 2070;
    /**
     * <code>RESPONSEGETRANKING = 2095;</code>
     *
     * <pre>
     *排
     * </pre>
     */
    public static final int RESPONSEGETRANKING_VALUE = 2095;
    /**
     * <code>RESPONSEGETTOTALENDLESS = 2071;</code>
     *
     * <pre>
     *获取无尽总积分奖励
     * </pre>
     */
    public static final int RESPONSEGETTOTALENDLESS_VALUE = 2071;
    /**
     * <code>RESPONSEOPERATEROLE = 2311;</code>
     *
     * <pre>
     *操作角色
     * </pre>
     */
    public static final int RESPONSEOPERATEROLE_VALUE = 2311;
    /**
     * <code>RESPONSEJOINROOM = 2080;</code>
     *
     * <pre>
     *进入房间
     * </pre>
     */
    public static final int RESPONSEJOINROOM_VALUE = 2080;
    /**
     * <code>RESPONSECREATEROOM = 2081;</code>
     *
     * <pre>
     *创建房间
     * </pre>
     */
    public static final int RESPONSECREATEROOM_VALUE = 2081;
    /**
     * <code>RESPONSELEAVEROOM = 2082;</code>
     *
     * <pre>
     *离开房间
     * </pre>
     */
    public static final int RESPONSELEAVEROOM_VALUE = 2082;
    /**
     * <code>RESPONSEUPDATEROOMINFO = 2083;</code>
     *
     * <pre>
     *更新房间信息
     * </pre>
     */
    public static final int RESPONSEUPDATEROOMINFO_VALUE = 2083;
    /**
     * <code>RESPONSEUPDATEROOMROLE = 2084;</code>
     *
     * <pre>
     *更新房间内玩家信息
     * </pre>
     */
    public static final int RESPONSEUPDATEROOMROLE_VALUE = 2084;
    /**
     * <code>RESPONSEJOINFIGHTHALL = 2085;</code>
     *
     * <pre>
     *请求进出多人战斗大厅
     * </pre>
     */
    public static final int RESPONSEJOINFIGHTHALL_VALUE = 2085;
    /**
     * <code>RESPONSESTARTINROOM = 2086;</code>
     *
     * <pre>
     *房主开始游戏
     * </pre>
     */
    public static final int RESPONSESTARTINROOM_VALUE = 2086;
    /**
     * <code>REPORTROOM = 2087;</code>
     *
     * <pre>
     *更新房间数量
     * </pre>
     */
    public static final int REPORTROOM_VALUE = 2087;
    /**
     * <code>REPORTADDROOMROLE = 2088;</code>
     *
     * <pre>
     *进入房间玩家
     * </pre>
     */
    public static final int REPORTADDROOMROLE_VALUE = 2088;
    /**
     * <code>REPORTUPDATEROOMINSIDE = 2089;</code>
     *
     * <pre>
     *通知更新房内玩家
     * </pre>
     */
    public static final int REPORTUPDATEROOMINSIDE_VALUE = 2089;
    /**
     * <code>REPORTCOMETOFIGHT = 2090;</code>
     *
     * <pre>
     *通知去战斗服
     * </pre>
     */
    public static final int REPORTCOMETOFIGHT_VALUE = 2090;
    /**
     * <code>RESPONSESTAMP = 2100;</code>
     *
     * <pre>
     *时间戳
     * </pre>
     */
    public static final int RESPONSESTAMP_VALUE = 2100;
    /**
     * <code>REPORTREPEATLOGIN = 2101;</code>
     *
     * <pre>
     *重复登入
     * </pre>
     */
    public static final int REPORTREPEATLOGIN_VALUE = 2101;
    /**
     * <code>REPORTFORCETOOFFLINE = 2102;</code>
     *
     * <pre>
     *强制下线
     * </pre>
     */
    public static final int REPORTFORCETOOFFLINE_VALUE = 2102;
    /**
     * <code>RESPONSERECONNECTION = 2200;</code>
     *
     * <pre>
     *断线重连
     * </pre>
     */
    public static final int RESPONSERECONNECTION_VALUE = 2200;
    /**
     * <code>RESPONSECHOOSEPAY = 2500;</code>
     *
     * <pre>
     *选择购买内容
     * </pre>
     */
    public static final int RESPONSECHOOSEPAY_VALUE = 2500;
    /**
     * <code>RESPONSESUBMITPAYBACK = 2501;</code>
     *
     * <pre>
     *支付返回
     * </pre>
     */
    public static final int RESPONSESUBMITPAYBACK_VALUE = 2501;
    /**
     * <code>RESPONSEGOOGLEPAY = 2502;</code>
     *
     * <pre>
     *google支付返回
     * </pre>
     */
    public static final int RESPONSEGOOGLEPAY_VALUE = 2502;
    /**
     * <code>RESPONSEWECHATPAY = 2503;</code>
     *
     * <pre>
     *微信支付返回
     * </pre>
     */
    public static final int RESPONSEWECHATPAY_VALUE = 2503;
    /**
     * <code>RESPONSEALIPAY = 2504;</code>
     *
     * <pre>
     *支付宝支付返回
     * </pre>
     */
    public static final int RESPONSEALIPAY_VALUE = 2504;
    /**
     * <code>RESPONSECONFIRMHUAWEIPURCHASE = 2505;</code>
     *
     * <pre>
     *华为确认支付返回
     * </pre>
     */
    public static final int RESPONSECONFIRMHUAWEIPURCHASE_VALUE = 2505;
    /**
     * <code>RESPONSEYSDKBALANCE = 2506;</code>
     *
     * <pre>
     *YSDK余额返回
     * </pre>
     */
    public static final int RESPONSEYSDKBALANCE_VALUE = 2506;
    /**
     * <code>RESPONSEBEGINHEADBALL = 2018;</code>
     *
     * <pre>
     *请求顶球返回
     * </pre>
     */
    public static final int RESPONSEBEGINHEADBALL_VALUE = 2018;
    /**
     * <code>RESPONSECOUNTHEADBALL = 2019;</code>
     *
     * <pre>
     *请求结算顶球模式返回
     * </pre>
     */
    public static final int RESPONSECOUNTHEADBALL_VALUE = 2019;
    /**
     * <code>RESPONSEJEWEL = 3015;</code>
     *
     * <pre>
     *返回砖石
     * </pre>
     */
    public static final int RESPONSEJEWEL_VALUE = 3015;
    /**
     * <code>RREPORTADREWARD = 2601;</code>
     *
     * <pre>
     *广告奖励
     * </pre>
     */
    public static final int RREPORTADREWARD_VALUE = 2601;
    /**
     * <code>RESPONSEADPLAY = 2602;</code>
     *
     * <pre>
     *请求播放广告
     * </pre>
     */
    public static final int RESPONSEADPLAY_VALUE = 2602;
    /**
     * <code>RESPONSEADNUM = 2603;</code>
     *
     * <pre>
     *请求播放广告次数
     * </pre>
     */
    public static final int RESPONSEADNUM_VALUE = 2603;
    /**
     * <code>RESPONSECOUNTGOLD = 2700;</code>
     *
     * <pre>
     *结算金币模式
     * </pre>
     */
    public static final int RESPONSECOUNTGOLD_VALUE = 2700;
    /**
     * <code>RESPONSEBEGINGOLD = 2701;</code>
     *
     * <pre>
     *返回金币模式球
     * </pre>
     */
    public static final int RESPONSEBEGINGOLD_VALUE = 2701;
    /**
     * <code>RESPONSEBEGINDOWN = 2702;</code>
     *
     * <pre>
     *返回进入落球请求
     * </pre>
     */
    public static final int RESPONSEBEGINDOWN_VALUE = 2702;
    /**
     * <code>RESPONSEBEGINSTACK = 2706;</code>
     *
     * <pre>
     *返回进入堆球请求
     * </pre>
     */
    public static final int RESPONSEBEGINSTACK_VALUE = 2706;
    /**
     * <code>RESPONSECOUNTDOWN = 2704;</code>
     *
     * <pre>
     *返回落球结算
     * </pre>
     */
    public static final int RESPONSECOUNTDOWN_VALUE = 2704;
    /**
     * <code>RESPONSECOUNTSTACK = 2705;</code>
     *
     * <pre>
     *返回堆球结算
     * </pre>
     */
    public static final int RESPONSECOUNTSTACK_VALUE = 2705;
    /**
     * <code>RESPONSECOUNTLOTTO = 2703;</code>
     *
     * <pre>
     *返回合成道具
     * </pre>
     */
    public static final int RESPONSECOUNTLOTTO_VALUE = 2703;
    /**
     * <code>RESPONSESOURCESMACHINE = 2078;</code>
     *
     * <pre>
     *返回能源机合成信息
     * </pre>
     */
    public static final int RESPONSESOURCESMACHINE_VALUE = 2078;
    /**
     * <code>RESPONSEMAKEITEM = 2076;</code>
     *
     * <pre>
     *返回培养皿请求的回复
     * </pre>
     */
    public static final int RESPONSEMAKEITEM_VALUE = 2076;
    /**
     * <code>RESPONSEPRODUCT = 2077;</code>
     *
     * <pre>
     *返回成品获得的物品
     * </pre>
     */
    public static final int RESPONSEPRODUCT_VALUE = 2077;
    /**
     * <code>RESPONSEWARDROBEVIEW = 2800;</code>
     *
     * <pre>
     *返回衣柜视图
     * </pre>
     */
    public static final int RESPONSEWARDROBEVIEW_VALUE = 2800;
    /**
     * <code>RESPONSEVISITOROPERATION = 2900;</code>
     *
     * <pre>
     *返回游客登录相关操作
     * </pre>
     */
    public static final int RESPONSEVISITOROPERATION_VALUE = 2900;
    /**
     * <code>RESPONSEAPPROVAL = 2920;</code>
     *
     * <pre>
     *点赞回复
     * </pre>
     */
    public static final int RESPONSEAPPROVAL_VALUE = 2920;
    /**
     * <code>RESPONSAUTHENTICATION = 2921;</code>
     *
     * <pre>
     *身份认证
     * </pre>
     */
    public static final int RESPONSAUTHENTICATION_VALUE = 2921;
    /**
     * <code>RESPONSEGETAPPROVAL = 2922;</code>
     *
     * <pre>
     *获得当前玩家受到点赞数
     * </pre>
     */
    public static final int RESPONSEGETAPPROVAL_VALUE = 2922;
    /**
     * <code>RESPOSESTARTGAMECOPY = 2122;</code>
     *
     * <pre>
     *返回进入每日关卡（任务金币）
     * </pre>
     */
    public static final int RESPOSESTARTGAMECOPY_VALUE = 2122;
    /**
     * <code>REAPOSECOUNTGAMECOPY = 2123;</code>
     *
     * <pre>
     *返回每日关卡结算
     * </pre>
     */
    public static final int REAPOSECOUNTGAMECOPY_VALUE = 2123;
    /**
     * <code>RESPOSEGETLUCKYITEM = 2124;</code>
     *
     * <pre>
     *返回车站界面幸运物品领取
     * </pre>
     */
    public static final int RESPOSEGETLUCKYITEM_VALUE = 2124;
    /**
     * <code>RESPOSEUPDATELUCKYITEM = 2125;</code>
     *
     * <pre>
     *通知用户幸运物品刷新
     * </pre>
     */
    public static final int RESPOSEUPDATELUCKYITEM_VALUE = 2125;
    /**
     * <code>RESPOSEFINISHACTIVITIES = 2126;</code>
     *
     * <pre>
     * 返回完成活动的奖励回复
     * </pre>
     */
    public static final int RESPOSEFINISHACTIVITIES_VALUE = 2126;
    /**
     * <code>REPORTUPDATEACTIVITIES = 2127;</code>
     *
     * <pre>
     *通知更新活动信息
     * </pre>
     */
    public static final int REPORTUPDATEACTIVITIES_VALUE = 2127;
    /**
     * <code>REPORTBROADCASTLUCKYUSER = 2128;</code>
     *
     * <pre>
     *  广播通知玩家获得稀有物品
     * </pre>
     */
    public static final int REPORTBROADCASTLUCKYUSER_VALUE = 2128;
    /**
     * <code>RESPOSEMARKETINFORMATION = 2129;</code>
     *
     * <pre>
     *交易市场信息回复
     * </pre>
     */
    public static final int RESPOSEMARKETINFORMATION_VALUE = 2129;
    /**
     * <code>RESPOSEGOODSOPERATE = 2130;</code>
     *
     * <pre>
     * 交易市场商品买入卖出
     * </pre>
     */
    public static final int RESPOSEGOODSOPERATE_VALUE = 2130;
    /**
     * <code>RESPOSETICKTEEXCHANGE = 2131;</code>
     *
     * <pre>
     * 票券兑换
     * </pre>
     */
    public static final int RESPOSETICKTEEXCHANGE_VALUE = 2131;
    /**
     * <code>RESPOSEQUERYROLEINFORMATION = 2135;</code>
     *
     * <pre>
     * 查询玩家角色信息
     * </pre>
     */
    public static final int RESPOSEQUERYROLEINFORMATION_VALUE = 2135;
    /**
     * <code>RESPOSEWAITITEM = 2136;</code>
     *
     * <pre>
     * 挂机获得物品
     * </pre>
     */
    public static final int RESPOSEWAITITEM_VALUE = 2136;
    /**
     * <code>RESPOSEUPWAITITEMLEVEL = 2137;</code>
     *
     * <pre>
     *提升等物品功能背包等級
     * </pre>
     */
    public static final int RESPOSEUPWAITITEMLEVEL_VALUE = 2137;
    /**
     * <code>RESPOSEGETWAITITEM = 2138;</code>
     *
     * <pre>
     *活得等物品功能物品
     * </pre>
     */
    public static final int RESPOSEGETWAITITEM_VALUE = 2138;
    /**
     * <code>RESPOSEHOUSE = 2600;</code>
     *
     * <pre>
     *获取玩家房间装饰信息
     * </pre>
     */
    public static final int RESPOSEHOUSE_VALUE = 2600;
    /**
     * <code>RESPOSEHOUSEPART = 2605;</code>
     *
     * <pre>
     *保存玩家房间装饰记录
     * </pre>
     */
    public static final int RESPOSEHOUSEPART_VALUE = 2605;
    /**
     * <code>RESPOSEBUYFURNITURE = 2670;</code>
     *
     * <pre>
     * 买家具
     * </pre>
     */
    public static final int RESPOSEBUYFURNITURE_VALUE = 2670;
    /**
     * <code>RESPOSEOPERATEMAIL = 2110;</code>
     *
     * <pre>
     * 操作邮件
     * </pre>
     */
    public static final int RESPOSEOPERATEMAIL_VALUE = 2110;
    /**
     * <code>REPORTNEWMAIL = 2111;</code>
     *
     * <pre>
     *通知新邮件
     * </pre>
     */
    public static final int REPORTNEWMAIL_VALUE = 2111;
    /**
     * <code>REPORTNEWNOTICE = 2222;</code>
     *
     * <pre>
     *通知新公告
     * </pre>
     */
    public static final int REPORTNEWNOTICE_VALUE = 2222;
    /**
     * <code>RESPOSEGETPETSINFO = 2210;</code>
     *
     * <pre>
     *获取玩家宠物信息
     * </pre>
     */
    public static final int RESPOSEGETPETSINFO_VALUE = 2210;
    /**
     * <code>RESPONSEGETEQUIP = 2215;</code>
     *
     * <pre>
     *获取玩家装备
     * </pre>
     */
    public static final int RESPONSEGETEQUIP_VALUE = 2215;
    /**
     * <code>RESPONSECHOICEEQUIP = 2218;</code>
     *
     * <pre>
     *选择装备
     * </pre>
     */
    public static final int RESPONSECHOICEEQUIP_VALUE = 2218;
    /**
     * <code>RESPOSEOPERATEPET = 2211;</code>
     *
     * <pre>
     *操作宠物
     * </pre>
     */
    public static final int RESPOSEOPERATEPET_VALUE = 2211;
    /**
     * <code>RESPONSEOPERATEEQUIP = 2216;</code>
     *
     * <pre>
     *操作装备
     * </pre>
     */
    public static final int RESPONSEOPERATEEQUIP_VALUE = 2216;
    /**
     * <code>RESPONSECHANGEEQUIP = 2217;</code>
     *
     * <pre>
     *换装备
     * </pre>
     */
    public static final int RESPONSECHANGEEQUIP_VALUE = 2217;
    /**
     * <code>RESPOSEBINDIDCARD = 2113;</code>
     *
     * <pre>
     *  绑定身份证
     * </pre>
     */
    public static final int RESPOSEBINDIDCARD_VALUE = 2113;
    /**
     * <code>RESPOSEGETDISPATCHINFOS = 2120;</code>
     *
     * <pre>
     *  获得派遣任务信息
     * </pre>
     */
    public static final int RESPOSEGETDISPATCHINFOS_VALUE = 2120;
    /**
     * <code>RESPOSEOPERATEDISPATCHTASK = 2121;</code>
     *
     * <pre>
     *    操作派遣任务
     * </pre>
     */
    public static final int RESPOSEOPERATEDISPATCHTASK_VALUE = 2121;
    /**
     * <code>RESPOSERAFFLEPOOL = 2150;</code>
     *
     * <pre>
     *获取抽奖池
     * </pre>
     */
    public static final int RESPOSERAFFLEPOOL_VALUE = 2150;
    /**
     * <code>RESPONSEPETEXP = 2580;</code>
     *
     * <pre>
     *获取宠物经验
     * </pre>
     */
    public static final int RESPONSEPETEXP_VALUE = 2580;
    /**
     * <code>RESPOSEGETEVENTINFO = 2160;</code>
     *
     * <pre>
     *事件信息
     * </pre>
     */
    public static final int RESPOSEGETEVENTINFO_VALUE = 2160;
    /**
     * <code>RESPOSEFLUSHEVENT = 2161;</code>
     *
     * <pre>
     *刷新事件
     * </pre>
     */
    public static final int RESPOSEFLUSHEVENT_VALUE = 2161;
    /**
     * <code>RESPOSECOUNTBATTLE = 2170;</code>
     *
     * <pre>
     * 怪物副本结算
     * </pre>
     */
    public static final int RESPOSECOUNTBATTLE_VALUE = 2170;
    /**
     * <code>RESPOSEPLAYERITEMS = 2180;</code>
     *
     * <pre>
     *获取玩家物品数据
     * </pre>
     */
    public static final int RESPOSEPLAYERITEMS_VALUE = 2180;
    /**
     * <code>RESPOSEUPPETSKILL = 2190;</code>
     *
     * <pre>
     *强化宠物技能等级
     * </pre>
     */
    public static final int RESPOSEUPPETSKILL_VALUE = 2190;
    /**
     * <code>RESPOSEBALCKMARKET = 2250;</code>
     *
     * <pre>
     *获取黑市商店信息
     * </pre>
     */
    public static final int RESPOSEBALCKMARKET_VALUE = 2250;
    /**
     * <code>RESPOSEBUYBALCKMARKETITEM = 2251;</code>
     *
     * <pre>
     * 购买黑市商店物品
     * </pre>
     */
    public static final int RESPOSEBUYBALCKMARKETITEM_VALUE = 2251;
    /**
     * <code>RESPOSEPETFORMATION = 2261;</code>
     *
     * <pre>
     *宠物编队
     * </pre>
     */
    public static final int RESPOSEPETFORMATION_VALUE = 2261;
    /**
     * <code>RESPOSEGETPETFORMATION = 2262;</code>
     *
     * <pre>
     *获取宠物编队信息
     * </pre>
     */
    public static final int RESPOSEGETPETFORMATION_VALUE = 2262;
    /**
     * <code>RESPOSEPETCOMPOSE = 2265;</code>
     *
     * <pre>
     * 宠物合成
     * </pre>
     */
    public static final int RESPOSEPETCOMPOSE_VALUE = 2265;
    /**
     * <code>RESPOSEGETPETBREEDINFO = 2270;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    public static final int RESPOSEGETPETBREEDINFO_VALUE = 2270;
    /**
     * <code>RESPOSEOPERATEPETBREED = 2271;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    public static final int RESPOSEOPERATEPETBREED_VALUE = 2271;
    /**
     * <code>RESPONSEPETBREEDINOFRMATION = 2272;</code>
     *
     * <pre>
     *获取宠物孵化相关信息
     * </pre>
     */
    public static final int RESPONSEPETBREEDINOFRMATION_VALUE = 2272;
    /**
     * <code>RESPONSEPETBREEDOPERATION = 2273;</code>
     *
     * <pre>
     *宠物孵化操作
     * </pre>
     */
    public static final int RESPONSEPETBREEDOPERATION_VALUE = 2273;
    /**
     * <code>RESPONSEPETBREED = 2274;</code>
     *
     * <pre>
     *登录返回正在孵化的蛋
     * </pre>
     */
    public static final int RESPONSEPETBREED_VALUE = 2274;
    /**
     * <code>RESPOSEGETPETGROWINFO = 2300;</code>
     *
     * <pre>
     *获取正在进化的宠物链表
     * </pre>
     */
    public static final int RESPOSEGETPETGROWINFO_VALUE = 2300;
    /**
     * <code>RESPOSEPETEVOLUTION = 2301;</code>
     *
     * <pre>
     *操作宠物进行进化
     * </pre>
     */
    public static final int RESPOSEPETEVOLUTION_VALUE = 2301;
    /**
     * <code>RESPOSEPETBREAKTHROUGH = 2302;</code>
     *
     * <pre>
     *宠物突破
     * </pre>
     */
    public static final int RESPOSEPETBREAKTHROUGH_VALUE = 2302;
    /**
     * <code>RESPOSEGETEXPERIENCES = 2280;</code>
     *
     * <pre>
     *获取探险任务的信息
     * </pre>
     */
    public static final int RESPOSEGETEXPERIENCES_VALUE = 2280;
    /**
     * <code>RESPOSEFLUSHEXPERIENCES = 2281;</code>
     *
     * <pre>
     *刷新探险任务
     * </pre>
     */
    public static final int RESPOSEFLUSHEXPERIENCES_VALUE = 2281;
    /**
     * <code>RESPOSEOPERATEEXPERIENCES = 2282;</code>
     *
     * <pre>
     *操作探险任务
     * </pre>
     */
    public static final int RESPOSEOPERATEEXPERIENCES_VALUE = 2282;
    /**
     * <code>RESPOSEITEMCOMPOSE = 2283;</code>
     *
     * <pre>
     *道具合成 
     * </pre>
     */
    public static final int RESPOSEITEMCOMPOSE_VALUE = 2283;
    /**
     * <code>RESPONSEOPERATIONFRIENDS = 2303;</code>
     *
     * <pre>
     *操作好友申请信息
     * </pre>
     */
    public static final int RESPONSEOPERATIONFRIENDS_VALUE = 2303;
    /**
     * <code>RESPONSESYNTHESIS = 2057;</code>
     *
     * <pre>
     *合成道具
     * </pre>
     */
    public static final int RESPONSESYNTHESIS_VALUE = 2057;
    /**
     * <code>RESPONSEREMOVEPET = 2285;</code>
     *
     * <pre>
     *回收宠物
     * </pre>
     */
    public static final int RESPONSEREMOVEPET_VALUE = 2285;
    /**
     * <code>RESPONSEDAILYTANSKS = 2290;</code>
     *
     * <pre>
     *进行任务
     * </pre>
     */
    public static final int RESPONSEDAILYTANSKS_VALUE = 2290;
    /**
     * <code>RESPONSECOMPLETEDAILYTASKS = 2291;</code>
     *
     * <pre>
     *完成任务
     * </pre>
     */
    public static final int RESPONSECOMPLETEDAILYTASKS_VALUE = 2291;
    /**
     * <code>RESPONSETASK = 2292;</code>
     *
     * <pre>
     *进入游戏返回任务进度
     * </pre>
     */
    public static final int RESPONSETASK_VALUE = 2292;
    /**
     * <code>RESPONSEACHIEVEMENTTYPE = 2293;</code>
     *
     * <pre>
     *进行成就
     * </pre>
     */
    public static final int RESPONSEACHIEVEMENTTYPE_VALUE = 2293;
    /**
     * <code>RESPONSEACHIEVEMENTIS = 2294;</code>
     *
     * <pre>
     *完成成就
     * </pre>
     */
    public static final int RESPONSEACHIEVEMENTIS_VALUE = 2294;
    /**
     * <code>RESPONSEACHIEVEMENT = 2295;</code>
     *
     * <pre>
     *进入游戏返回成就进度
     * </pre>
     */
    public static final int RESPONSEACHIEVEMENT_VALUE = 2295;
    /**
     * <code>RESPONSEPLAYERONLINEORNOT = 2296;</code>
     *
     * <pre>
     *登录游戏刷新用户好友信息
     * </pre>
     */
    public static final int RESPONSEPLAYERONLINEORNOT_VALUE = 2296;
    /**
     * <code>RESPONSEPLAYEROFFLINEORNOT = 2297;</code>
     *
     * <pre>
     *玩家上线离线刷新
     * </pre>
     */
    public static final int RESPONSEPLAYEROFFLINEORNOT_VALUE = 2297;
    /**
     * <code>RESPONSEPLAYERRECOMMEND = 2298;</code>
     *
     * <pre>
     *推荐玩家
     * </pre>
     */
    public static final int RESPONSEPLAYERRECOMMEND_VALUE = 2298;
    /**
     * <code>RESPONSEFRIENDSAPPLY = 2299;</code>
     *
     * <pre>
     *上线刷新好友申请信息
     * </pre>
     */
    public static final int RESPONSEFRIENDSAPPLY_VALUE = 2299;
    /**
     * <code>RESPONSESENDOUTINFORMATION = 2304;</code>
     *
     * <pre>
     *发送消息
     * </pre>
     */
    public static final int RESPONSESENDOUTINFORMATION_VALUE = 2304;
    /**
     * <code>RESPONSECHATRECORD = 2305;</code>
     *
     * <pre>
     *上线获取玩家聊天记录
     * </pre>
     */
    public static final int RESPONSECHATRECORD_VALUE = 2305;
    /**
     * <code>RESPONSEUPDATETYPE = 2306;</code>
     *
     * <pre>
     *修改状态
     * </pre>
     */
    public static final int RESPONSEUPDATETYPE_VALUE = 2306;
    /**
     * <code>RESPONSERANK = 2307;</code>
     *
     * <pre>
     *排行榜
     * </pre>
     */
    public static final int RESPONSERANK_VALUE = 2307;
    /**
     * <code>RESPONSELEVEL = 2308;</code>
     *
     * <pre>
     *关卡
     * </pre>
     */
    public static final int RESPONSELEVEL_VALUE = 2308;
    /**
     * <code>RESPPNSELEVELD = 2309;</code>
     *
     * <pre>
     *操作关卡
     * </pre>
     */
    public static final int RESPPNSELEVELD_VALUE = 2309;
    /**
     * <code>RESPONSETemper = 2315;</code>
     *
     * <pre>
     *温度层
     * </pre>
     */
    public static final int RESPONSETemper_VALUE = 2315;
    /**
     * <code>RESPONSEPlont = 2320;</code>
     *
     * <pre>
     *获取剧情11
     * </pre>
     */
    public static final int RESPONSEPlont_VALUE = 2320;
    /**
     * <code>RESPONSEChap = 2321;</code>
     *
     * <pre>
     *返回剧情2
     * </pre>
     */
    public static final int RESPONSEChap_VALUE = 2321;
    /**
     * <code>RESPONSEGetEquipA = 2322;</code>
     *
     * <pre>
     *获取装备
     * </pre>
     */
    public static final int RESPONSEGetEquipA_VALUE = 2322;
    /**
     * <code>RESPONSEDDDEquip = 2323;</code>
     *
     * <pre>
     *返回装备
     * </pre>
     */
    public static final int RESPONSEDDDEquip_VALUE = 2323;
    /**
     * <code>RESPONSEbosstime = 2325;</code>
     *
     * <pre>
     *世界boss
     * </pre>
     */
    public static final int RESPONSEbosstime_VALUE = 2325;
    /**
     * <code>RESPONSEbossdown = 2326;</code>
     *
     * <pre>
     *boss剩余血量
     * </pre>
     */
    public static final int RESPONSEbossdown_VALUE = 2326;
    /**
     * <code>RESPONSEbosslive = 2327;</code>
     *
     * <pre>
     *boss血量及死亡
     * </pre>
     */
    public static final int RESPONSEbosslive_VALUE = 2327;
    /**
     * <code>RESPONSEPVPPetOperate = 2330;</code>
     *
     * <pre>
     * PVP宠物存储
     * </pre>
     */
    public static final int RESPONSEPVPPetOperate_VALUE = 2330;
    /**
     * <code>RESPONSEPVPBaseData = 2331;</code>
     *
     * <pre>
     * PVP个人信息
     * </pre>
     */
    public static final int RESPONSEPVPBaseData_VALUE = 2331;
    /**
     * <code>RESPONSEPVPBattle = 2332;</code>
     *
     * <pre>
     * PVP战斗
     * </pre>
     */
    public static final int RESPONSEPVPBattle_VALUE = 2332;
    /**
     * <code>RESPONSEPVPBATTLEREMAINTIME = 2334;</code>
     *
     * <pre>
     * PVP 剩余时间
     * </pre>
     */
    public static final int RESPONSEPVPBATTLEREMAINTIME_VALUE = 2334;
    /**
     * <code>RESPONSEPAYDIAMOND = 2340;</code>
     *
     * <pre>
     * 玩家充值钻石总数
     * </pre>
     */
    public static final int RESPONSEPAYDIAMOND_VALUE = 2340;
    /**
     * <code>RESPONSERECHAREGREBATEREWARD = 2342;</code>
     *
     * <pre>
     * 玩家充值钻石 领取奖励的任务ID
     * </pre>
     */
    public static final int RESPONSERECHAREGREBATEREWARD_VALUE = 2342;
    /**
     * <code>RESPONSELIMITEDTIMEREWARD = 2350;</code>
     *
     * <pre>
     * 返回显示奖励的信息
     * </pre>
     */
    public static final int RESPONSELIMITEDTIMEREWARD_VALUE = 2350;
    /**
     * <code>RESPONSELIMITEDTIMEREWARDTASKDATA = 2353;</code>
     *
     * <pre>
     * 返回全部数据
     * </pre>
     */
    public static final int RESPONSELIMITEDTIMEREWARDTASKDATA_VALUE = 2353;
    /**
     * <code>RESPONSEREMAINTIME = 2354;</code>
     *
     * <pre>
     * 时间
     * </pre>
     */
    public static final int RESPONSEREMAINTIME_VALUE = 2354;
    /**
     * <code>RESPONSEPETEGGEXP = 2360;</code>
     *
     * <pre>
     * 查询蛋的经验值和等级
     * </pre>
     */
    public static final int RESPONSEPETEGGEXP_VALUE = 2360;
    /**
     * <code>RESPONSEALLPETEGGEXP = 2361;</code>
     *
     * <pre>
     * 蛋的全部数据
     * </pre>
     */
    public static final int RESPONSEALLPETEGGEXP_VALUE = 2361;
    /**
     * <code>RESPONSETEMITEM = 2380;</code>
     *
     * <pre>
     * 温度层获得道具限制次数
     * </pre>
     */
    public static final int RESPONSETEMITEM_VALUE = 2380;
    /**
     * <code>RESPONSEDELETEALLPETS = 2390;</code>
     *
     *
     * <pre>
     * 删除全部宠物
     * </pre>
     */
    public static final int RESPONSEDELETEALLPETS_VALUE = 2390;


    public final int getNumber() { return value; }

    public static SToC valueOf(int value) {
      switch (value) {
        case 2000: return RESPONSESERVER;
        case 2005: return RESPONSEREGISTER;
        case 2001: return RESPONSELOGIN;
        case 2002: return RESPONSECREATE;
        case 2112: return RESPONSEROLESEX;
        case 2213: return RESPONSEGETSEX;
        case 2003: return RESPONSESETINFO;
        case 2004: return RESPONSECHOOSEROLE;
        case 2006: return RESPONSEBUYITEM;
        case 2007: return RESPONSEUSEITEM;
        case 2008: return RESPONSEOPENBAGCELL;
        case 2009: return RESPONSECOMPOSE;
        case 2010: return RESPONSEBEGINMISSION;
        case 2011: return RESPONSECOUNTMISSION;
        case 2012: return RESPONSEBEGINENDLESS;
        case 2013: return RESPONSECOUNTENDLESS;
        case 2014: return RESPONSEUPDATENEWUSER;
        case 2466: return RESPONSEPETHATCH;
        case 2020: return RESPONSESENDCHAT;
        case 2021: return REPORTCHAT;
        case 2022: return REPORTMESSAGE;
        case 2023: return REPORTSYSTEM;
        case 2046: return RESPONSEPHYSICAL;
        case 2030: return RESPONSEFINISHTASK;
        case 2031: return REPORTUPDATETASK;
        case 2318: return RESPONSEGETROLEEXP;
        case 2051: return RESPONSEBUYCLOTHING;
        case 2052: return RESPONSECHANGECUPBOARD;
        case 2053: return RESPONSECHANGEDRESS;
        case 2054: return RESPONSECUPBOARDTOBODY;
        case 2055: return RESPONSESTARBITOMONEY;
        case 2056: return REPORTITEM;
        case 2060: return RESPONSEOPERATEFRIEND;
        case 2201: return RESPONSEOPERATEFRIEND1;
        case 2061: return RESPONSEOPERATEMESSAGE;
        case 2062: return REPORTFRIEND;
        case 2063: return REPORTDELETEFRIEND;
        case 2064: return RESPONSERECOMMENDFRIEND;
        case 2065: return REPORTINANDOUTSTATION;
        case 2066: return REPORTSTATIONLOCATION;
        case 2075: return REPORTUPDATESTATIONROLE;
        case 2370: return RESPONSEGETMAP;
        case 2371: return RESPONSEUPDMAP;
        case 2067: return RESPONSEGIVEPRESENT;
        case 2068: return RESPONSEACCUSATION;
        case 2069: return REPORTUPDATEFRIEND;
        case 2070: return RESPONSEGETRANK;
        case 2095: return RESPONSEGETRANKING;
        case 2071: return RESPONSEGETTOTALENDLESS;
        case 2311: return RESPONSEOPERATEROLE;
        case 2080: return RESPONSEJOINROOM;
        case 2081: return RESPONSECREATEROOM;
        case 2082: return RESPONSELEAVEROOM;
        case 2083: return RESPONSEUPDATEROOMINFO;
        case 2084: return RESPONSEUPDATEROOMROLE;
        case 2085: return RESPONSEJOINFIGHTHALL;
        case 2086: return RESPONSESTARTINROOM;
        case 2087: return REPORTROOM;
        case 2088: return REPORTADDROOMROLE;
        case 2089: return REPORTUPDATEROOMINSIDE;
        case 2090: return REPORTCOMETOFIGHT;
        case 2100: return RESPONSESTAMP;
        case 2101: return REPORTREPEATLOGIN;
        case 2102: return REPORTFORCETOOFFLINE;
        case 2200: return RESPONSERECONNECTION;
        case 2500: return RESPONSECHOOSEPAY;
        case 2501: return RESPONSESUBMITPAYBACK;
        case 2502: return RESPONSEGOOGLEPAY;
        case 2503: return RESPONSEWECHATPAY;
        case 2504: return RESPONSEALIPAY;
        case 2505: return RESPONSECONFIRMHUAWEIPURCHASE;
        case 2506: return RESPONSEYSDKBALANCE;
        case 2018: return RESPONSEBEGINHEADBALL;
        case 2019: return RESPONSECOUNTHEADBALL;
        case 3015: return RESPONSEJEWEL;
        case 2601: return RREPORTADREWARD;
        case 2602: return RESPONSEADPLAY;
        case 2603: return RESPONSEADNUM;
        case 2700: return RESPONSECOUNTGOLD;
        case 2701: return RESPONSEBEGINGOLD;
        case 2702: return RESPONSEBEGINDOWN;
        case 2706: return RESPONSEBEGINSTACK;
        case 2704: return RESPONSECOUNTDOWN;
        case 2705: return RESPONSECOUNTSTACK;
        case 2703: return RESPONSECOUNTLOTTO;
        case 2078: return RESPONSESOURCESMACHINE;
        case 2076: return RESPONSEMAKEITEM;
        case 2077: return RESPONSEPRODUCT;
        case 2800: return RESPONSEWARDROBEVIEW;
        case 2900: return RESPONSEVISITOROPERATION;
        case 2920: return RESPONSEAPPROVAL;
        case 2921: return RESPONSAUTHENTICATION;
        case 2922: return RESPONSEGETAPPROVAL;
        case 2122: return RESPOSESTARTGAMECOPY;
        case 2123: return REAPOSECOUNTGAMECOPY;
        case 2124: return RESPOSEGETLUCKYITEM;
        case 2125: return RESPOSEUPDATELUCKYITEM;
        case 2126: return RESPOSEFINISHACTIVITIES;
        case 2127: return REPORTUPDATEACTIVITIES;
        case 2128: return REPORTBROADCASTLUCKYUSER;
        case 2129: return RESPOSEMARKETINFORMATION;
        case 2130: return RESPOSEGOODSOPERATE;
        case 2131: return RESPOSETICKTEEXCHANGE;
        case 2135: return RESPOSEQUERYROLEINFORMATION;
        case 2136: return RESPOSEWAITITEM;
        case 2137: return RESPOSEUPWAITITEMLEVEL;
        case 2138: return RESPOSEGETWAITITEM;
        case 2600: return RESPOSEHOUSE;
        case 2605: return RESPOSEHOUSEPART;
        case 2670: return RESPOSEBUYFURNITURE;
        case 2110: return RESPOSEOPERATEMAIL;
        case 2111: return REPORTNEWMAIL;
        case 2222: return REPORTNEWNOTICE;
        case 2210: return RESPOSEGETPETSINFO;
        case 2215: return RESPONSEGETEQUIP;
        case 2218: return RESPONSECHOICEEQUIP;
        case 2211: return RESPOSEOPERATEPET;
        case 2216: return RESPONSEOPERATEEQUIP;
        case 2217: return RESPONSECHANGEEQUIP;
        case 2113: return RESPOSEBINDIDCARD;
        case 2120: return RESPOSEGETDISPATCHINFOS;
        case 2121: return RESPOSEOPERATEDISPATCHTASK;
        case 2150: return RESPOSERAFFLEPOOL;
        case 2580: return RESPONSEPETEXP;
        case 2160: return RESPOSEGETEVENTINFO;
        case 2161: return RESPOSEFLUSHEVENT;
        case 2170: return RESPOSECOUNTBATTLE;
        case 2180: return RESPOSEPLAYERITEMS;
        case 2190: return RESPOSEUPPETSKILL;
        case 2250: return RESPOSEBALCKMARKET;
        case 2251: return RESPOSEBUYBALCKMARKETITEM;
        case 2261: return RESPOSEPETFORMATION;
        case 2262: return RESPOSEGETPETFORMATION;
        case 2265: return RESPOSEPETCOMPOSE;
        case 2270: return RESPOSEGETPETBREEDINFO;
        case 2271: return RESPOSEOPERATEPETBREED;
        case 2272: return RESPONSEPETBREEDINOFRMATION;
        case 2273: return RESPONSEPETBREEDOPERATION;
        case 2274: return RESPONSEPETBREED;
        case 2300: return RESPOSEGETPETGROWINFO;
        case 2301: return RESPOSEPETEVOLUTION;
        case 2302: return RESPOSEPETBREAKTHROUGH;
        case 2280: return RESPOSEGETEXPERIENCES;
        case 2281: return RESPOSEFLUSHEXPERIENCES;
        case 2282: return RESPOSEOPERATEEXPERIENCES;
        case 2283: return RESPOSEITEMCOMPOSE;
        case 2303: return RESPONSEOPERATIONFRIENDS;
        case 2057: return RESPONSESYNTHESIS;
        case 2285: return RESPONSEREMOVEPET;
        case 2290: return RESPONSEDAILYTANSKS;
        case 2291: return RESPONSECOMPLETEDAILYTASKS;
        case 2292: return RESPONSETASK;
        case 2293: return RESPONSEACHIEVEMENTTYPE;
        case 2294: return RESPONSEACHIEVEMENTIS;
        case 2295: return RESPONSEACHIEVEMENT;
        case 2296: return RESPONSEPLAYERONLINEORNOT;
        case 2297: return RESPONSEPLAYEROFFLINEORNOT;
        case 2298: return RESPONSEPLAYERRECOMMEND;
        case 2299: return RESPONSEFRIENDSAPPLY;
        case 2304: return RESPONSESENDOUTINFORMATION;
        case 2305: return RESPONSECHATRECORD;
        case 2306: return RESPONSEUPDATETYPE;
        case 2307: return RESPONSERANK;
        case 2308: return RESPONSELEVEL;
        case 2309: return RESPPNSELEVELD;
        case 2315: return RESPONSETemper;
        case 2320: return RESPONSEPlont;
        case 2321: return RESPONSEChap;
        case 2322: return RESPONSEGetEquipA;
        case 2323: return RESPONSEDDDEquip;
        case 2325: return RESPONSEbosstime;
        case 2326: return RESPONSEbossdown;
        case 2327: return RESPONSEbosslive;
        case 2330: return RESPONSEPVPPetOperate;
        case 2331: return RESPONSEPVPBaseData;
        case 2332: return RESPONSEPVPBattle;
        case 2334: return RESPONSEPVPBATTLEREMAINTIME;
        case 2340: return RESPONSEPAYDIAMOND;
        case 2342: return RESPONSERECHAREGREBATEREWARD;
        case 2350: return RESPONSELIMITEDTIMEREWARD;
        case 2353: return RESPONSELIMITEDTIMEREWARDTASKDATA;
        case 2354: return RESPONSEREMAINTIME;
        case 2360: return RESPONSEPETEGGEXP;
        case 2361: return RESPONSEALLPETEGGEXP;
        case 2380: return RESPONSETEMITEM;
        case 2390: return RESPONSEDELETEALLPETS;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SToC>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<SToC>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SToC>() {
            public SToC findValueByNumber(int number) {
              return SToC.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(1);
    }

    private static final SToC[] VALUES = values();

    public static SToC valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private SToC(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.SToC)
  }

  /**
   * Protobuf enum {@code protocol.CToM}
   */
  public enum CToM
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REQUESTCONNECTFIGHT = 3030;</code>
     *
     * <pre>
     *请求连接战斗服
     * </pre>
     */
    REQUESTCONNECTFIGHT(0, 3030),
    /**
     * <code>REQUESTLOADFINISH = 3031;</code>
     *
     * <pre>
     *加载完成
     * </pre>
     */
    REQUESTLOADFINISH(1, 3031),
    /**
     * <code>REQUESTSYNC = 3050;</code>
     */
    REQUESTSYNC(2, 3050),
    /**
     * <code>REQUESTHEART = 3000;</code>
     *
     * <pre>
     *心跳包
     * </pre>
     */
    REQUESTHEART(3, 3000),
    /**
     * <code>REQUESTCONNECTBATTLE = 3100;</code>
     *
     * <pre>
     *请求连接双人对战
     * </pre>
     */
    REQUESTCONNECTBATTLE(4, 3100),
    /**
     * <code>REQUESTSYNCBATTLE = 3101;</code>
     *
     * <pre>
     * 同步游戏信息
     * </pre>
     */
    REQUESTSYNCBATTLE(5, 3101),
    /**
     * <code>UPDATEUSERSCORE = 3102;</code>
     *
     * <pre>
     *更新玩家得分情况
     * </pre>
     */
    UPDATEUSERSCORE(6, 3102),
    /**
     * <code>REQUESTCREATESKILL = 3103;</code>
     *
     * <pre>
     * 游戏生成技能
     * </pre>
     */
    REQUESTCREATESKILL(7, 3103),
    /**
     * <code>REQUESTTRIGGERSKILL = 3105;</code>
     *
     * <pre>
     * 游戏触发技能
     * </pre>
     */
    REQUESTTRIGGERSKILL(8, 3105),
    /**
     * <code>REQUESTUPDATEROBOTINFO = 3333;</code>
     *
     * <pre>
     *更新机器人游戏信息
     * </pre>
     */
    REQUESTUPDATEROBOTINFO(9, 3333),
    ;

    /**
     * <code>REQUESTCONNECTFIGHT = 3030;</code>
     *
     * <pre>
     *请求连接战斗服
     * </pre>
     */
    public static final int REQUESTCONNECTFIGHT_VALUE = 3030;
    /**
     * <code>REQUESTLOADFINISH = 3031;</code>
     *
     * <pre>
     *加载完成
     * </pre>
     */
    public static final int REQUESTLOADFINISH_VALUE = 3031;
    /**
     * <code>REQUESTSYNC = 3050;</code>
     */
    public static final int REQUESTSYNC_VALUE = 3050;
    /**
     * <code>REQUESTHEART = 3000;</code>
     *
     * <pre>
     *心跳包
     * </pre>
     */
    public static final int REQUESTHEART_VALUE = 3000;
    /**
     * <code>REQUESTCONNECTBATTLE = 3100;</code>
     *
     * <pre>
     *请求连接双人对战
     * </pre>
     */
    public static final int REQUESTCONNECTBATTLE_VALUE = 3100;
    /**
     * <code>REQUESTSYNCBATTLE = 3101;</code>
     *
     * <pre>
     * 同步游戏信息
     * </pre>
     */
    public static final int REQUESTSYNCBATTLE_VALUE = 3101;
    /**
     * <code>UPDATEUSERSCORE = 3102;</code>
     *
     * <pre>
     *更新玩家得分情况
     * </pre>
     */
    public static final int UPDATEUSERSCORE_VALUE = 3102;
    /**
     * <code>REQUESTCREATESKILL = 3103;</code>
     *
     * <pre>
     * 游戏生成技能
     * </pre>
     */
    public static final int REQUESTCREATESKILL_VALUE = 3103;
    /**
     * <code>REQUESTTRIGGERSKILL = 3105;</code>
     *
     * <pre>
     * 游戏触发技能
     * </pre>
     */
    public static final int REQUESTTRIGGERSKILL_VALUE = 3105;
    /**
     * <code>REQUESTUPDATEROBOTINFO = 3333;</code>
     *
     * <pre>
     *更新机器人游戏信息
     * </pre>
     */
    public static final int REQUESTUPDATEROBOTINFO_VALUE = 3333;


    public final int getNumber() { return value; }

    public static CToM valueOf(int value) {
      switch (value) {
        case 3030: return REQUESTCONNECTFIGHT;
        case 3031: return REQUESTLOADFINISH;
        case 3050: return REQUESTSYNC;
        case 3000: return REQUESTHEART;
        case 3100: return REQUESTCONNECTBATTLE;
        case 3101: return REQUESTSYNCBATTLE;
        case 3102: return UPDATEUSERSCORE;
        case 3103: return REQUESTCREATESKILL;
        case 3105: return REQUESTTRIGGERSKILL;
        case 3333: return REQUESTUPDATEROBOTINFO;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<CToM>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<CToM>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<CToM>() {
            public CToM findValueByNumber(int number) {
              return CToM.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(2);
    }

    private static final CToM[] VALUES = values();

    public static CToM valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private CToM(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.CToM)
  }

  /**
   * Protobuf enum {@code protocol.MToC}
   */
  public enum MToC
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>RESPONSECONNECTFIGHT = 4030;</code>
     *
     * <pre>
     *请求连接战斗服
     * </pre>
     */
    RESPONSECONNECTFIGHT(0, 4030),
    /**
     * <code>RESPONSELOADFINISH = 4031;</code>
     *
     * <pre>
     *加载完成
     * </pre>
     */
    RESPONSELOADFINISH(1, 4031),
    /**
     * <code>RESPONSESYNC = 4050;</code>
     */
    RESPONSESYNC(2, 4050),
    /**
     * <code>REPORTBROADCASTSYNC = 4051;</code>
     */
    REPORTBROADCASTSYNC(3, 4051),
    /**
     * <code>REPORTMISSIONPROGRESS = 4052;</code>
     *
     * <pre>
     *新增球技能
     * </pre>
     */
    REPORTMISSIONPROGRESS(4, 4052),
    /**
     * <code>REPORTCHANGESCORE = 4053;</code>
     *
     * <pre>
     *分数更改
     * </pre>
     */
    REPORTCHANGESCORE(5, 4053),
    /**
     * <code>REPORTKICK = 4054;</code>
     *
     * <pre>
     *踢人
     * </pre>
     */
    REPORTKICK(6, 4054),
    /**
     * <code>REPORTSTARTFIGHT = 4060;</code>
     *
     * <pre>
     *通知开始战斗
     * </pre>
     */
    REPORTSTARTFIGHT(7, 4060),
    /**
     * <code>REPORTOVERFIGHT = 4061;</code>
     */
    REPORTOVERFIGHT(8, 4061),
    /**
     * <code>RESPONSEHEART = 4000;</code>
     *
     * <pre>
     *心跳包
     * </pre>
     */
    RESPONSEHEART(9, 4000),
    /**
     * <code>RESPONSECONNECTBATTLE = 4100;</code>
     *
     * <pre>
     *请求连接双人对战
     * </pre>
     */
    RESPONSECONNECTBATTLE(10, 4100),
    /**
     * <code>RESPONSESYNCBATTLE = 4101;</code>
     *
     * <pre>
     *同步游戏信息
     * </pre>
     */
    RESPONSESYNCBATTLE(11, 4101),
    /**
     * <code>REPORTBROADCASTFIGHT = 4200;</code>
     *
     * <pre>
     *通知对战玩家操作信息
     * </pre>
     */
    REPORTBROADCASTFIGHT(12, 4200),
    /**
     * <code>RESPONSECREATESKILL = 4103;</code>
     *
     * <pre>
     * 游戏生成技能
     * </pre>
     */
    RESPONSECREATESKILL(13, 4103),
    /**
     * <code>RESPONSETRIGGERSKILL = 4105;</code>
     *
     * <pre>
     * 游戏触发技能
     * </pre>
     */
    RESPONSETRIGGERSKILL(14, 4105),
    /**
     * <code>REPORTCREATEBALL = 4108;</code>
     *
     * <pre>
     *通知产生球
     * </pre>
     */
    REPORTCREATEBALL(15, 4108),
    ;

    /**
     * <code>RESPONSECONNECTFIGHT = 4030;</code>
     *
     * <pre>
     *请求连接战斗服
     * </pre>
     */
    public static final int RESPONSECONNECTFIGHT_VALUE = 4030;
    /**
     * <code>RESPONSELOADFINISH = 4031;</code>
     *
     * <pre>
     *加载完成
     * </pre>
     */
    public static final int RESPONSELOADFINISH_VALUE = 4031;
    /**
     * <code>RESPONSESYNC = 4050;</code>
     */
    public static final int RESPONSESYNC_VALUE = 4050;
    /**
     * <code>REPORTBROADCASTSYNC = 4051;</code>
     */
    public static final int REPORTBROADCASTSYNC_VALUE = 4051;
    /**
     * <code>REPORTMISSIONPROGRESS = 4052;</code>
     *
     * <pre>
     *新增球技能
     * </pre>
     */
    public static final int REPORTMISSIONPROGRESS_VALUE = 4052;
    /**
     * <code>REPORTCHANGESCORE = 4053;</code>
     *
     * <pre>
     *分数更改
     * </pre>
     */
    public static final int REPORTCHANGESCORE_VALUE = 4053;
    /**
     * <code>REPORTKICK = 4054;</code>
     *
     * <pre>
     *踢人
     * </pre>
     */
    public static final int REPORTKICK_VALUE = 4054;
    /**
     * <code>REPORTSTARTFIGHT = 4060;</code>
     *
     * <pre>
     *通知开始战斗
     * </pre>
     */
    public static final int REPORTSTARTFIGHT_VALUE = 4060;
    /**
     * <code>REPORTOVERFIGHT = 4061;</code>
     */
    public static final int REPORTOVERFIGHT_VALUE = 4061;
    /**
     * <code>RESPONSEHEART = 4000;</code>
     *
     * <pre>
     *心跳包
     * </pre>
     */
    public static final int RESPONSEHEART_VALUE = 4000;
    /**
     * <code>RESPONSECONNECTBATTLE = 4100;</code>
     *
     * <pre>
     *请求连接双人对战
     * </pre>
     */
    public static final int RESPONSECONNECTBATTLE_VALUE = 4100;
    /**
     * <code>RESPONSESYNCBATTLE = 4101;</code>
     *
     * <pre>
     *同步游戏信息
     * </pre>
     */
    public static final int RESPONSESYNCBATTLE_VALUE = 4101;
    /**
     * <code>REPORTBROADCASTFIGHT = 4200;</code>
     *
     * <pre>
     *通知对战玩家操作信息
     * </pre>
     */
    public static final int REPORTBROADCASTFIGHT_VALUE = 4200;
    /**
     * <code>RESPONSECREATESKILL = 4103;</code>
     *
     * <pre>
     * 游戏生成技能
     * </pre>
     */
    public static final int RESPONSECREATESKILL_VALUE = 4103;
    /**
     * <code>RESPONSETRIGGERSKILL = 4105;</code>
     *
     * <pre>
     * 游戏触发技能
     * </pre>
     */
    public static final int RESPONSETRIGGERSKILL_VALUE = 4105;
    /**
     * <code>REPORTCREATEBALL = 4108;</code>
     *
     * <pre>
     *通知产生球
     * </pre>
     */
    public static final int REPORTCREATEBALL_VALUE = 4108;


    public final int getNumber() { return value; }

    public static MToC valueOf(int value) {
      switch (value) {
        case 4030: return RESPONSECONNECTFIGHT;
        case 4031: return RESPONSELOADFINISH;
        case 4050: return RESPONSESYNC;
        case 4051: return REPORTBROADCASTSYNC;
        case 4052: return REPORTMISSIONPROGRESS;
        case 4053: return REPORTCHANGESCORE;
        case 4054: return REPORTKICK;
        case 4060: return REPORTSTARTFIGHT;
        case 4061: return REPORTOVERFIGHT;
        case 4000: return RESPONSEHEART;
        case 4100: return RESPONSECONNECTBATTLE;
        case 4101: return RESPONSESYNCBATTLE;
        case 4200: return REPORTBROADCASTFIGHT;
        case 4103: return RESPONSECREATESKILL;
        case 4105: return RESPONSETRIGGERSKILL;
        case 4108: return REPORTCREATEBALL;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MToC>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<MToC>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MToC>() {
            public MToC findValueByNumber(int number) {
              return MToC.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(3);
    }

    private static final MToC[] VALUES = values();

    public static MToC valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private MToC(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.MToC)
  }

  /**
   * Protobuf enum {@code protocol.SToM}
   */
  public enum SToM
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>REQUESTENTERMISSION = 5001;</code>
     */
    REQUESTENTERMISSION(0, 5001),
    /**
     * <code>RESPONSEOVERMISSION = 5010;</code>
     */
    RESPONSEOVERMISSION(1, 5010),
    /**
     * <code>REQUESTALLOCATEROOM = 10001;</code>
     *
     * <pre>
     * 请求战斗服分配房间
     * </pre>
     */
    REQUESTALLOCATEROOM(2, 10001),
    ;

    /**
     * <code>REQUESTENTERMISSION = 5001;</code>
     */
    public static final int REQUESTENTERMISSION_VALUE = 5001;
    /**
     * <code>RESPONSEOVERMISSION = 5010;</code>
     */
    public static final int RESPONSEOVERMISSION_VALUE = 5010;
    /**
     * <code>REQUESTALLOCATEROOM = 10001;</code>
     *
     * <pre>
     * 请求战斗服分配房间
     * </pre>
     */
    public static final int REQUESTALLOCATEROOM_VALUE = 10001;


    public final int getNumber() { return value; }

    public static SToM valueOf(int value) {
      switch (value) {
        case 5001: return REQUESTENTERMISSION;
        case 5010: return RESPONSEOVERMISSION;
        case 10001: return REQUESTALLOCATEROOM;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<SToM>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<SToM>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<SToM>() {
            public SToM findValueByNumber(int number) {
              return SToM.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(4);
    }

    private static final SToM[] VALUES = values();

    public static SToM valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private SToM(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.SToM)
  }

  /**
   * Protobuf enum {@code protocol.MToS}
   */
  public enum MToS
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>RESPONSEENTERMISSION = 6001;</code>
     *
     * <pre>
     * REPORTBATTLESERVERONCALL=10000; //通知主服务器战斗服已经启动好
     * </pre>
     */
    RESPONSEENTERMISSION(0, 6001),
    /**
     * <code>REQUESTOVERMISSION = 6010;</code>
     */
    REQUESTOVERMISSION(1, 6010),
    ;

    /**
     * <code>RESPONSEENTERMISSION = 6001;</code>
     *
     * <pre>
     * REPORTBATTLESERVERONCALL=10000; //通知主服务器战斗服已经启动好
     * </pre>
     */
    public static final int RESPONSEENTERMISSION_VALUE = 6001;
    /**
     * <code>REQUESTOVERMISSION = 6010;</code>
     */
    public static final int REQUESTOVERMISSION_VALUE = 6010;


    public final int getNumber() { return value; }

    public static MToS valueOf(int value) {
      switch (value) {
        case 6001: return RESPONSEENTERMISSION;
        case 6010: return REQUESTOVERMISSION;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<MToS>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<MToS>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<MToS>() {
            public MToS findValueByNumber(int number) {
              return MToS.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(5);
    }

    private static final MToS[] VALUES = values();

    public static MToS valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private MToS(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.MToS)
  }

  /**
   * Protobuf enum {@code protocol.ErrorCode}
   */
  public enum ErrorCode
      implements com.google.protobuf.ProtocolMessageEnum {
    /**
     * <code>UNKNOWERROR = 10000;</code>
     *
     * <pre>
     *未知错误
     * </pre>
     */
    UNKNOWERROR(0, 10000),
    /**
     * <code>PROTOERROR = 10001;</code>
     *
     * <pre>
     *协议错误
     * </pre>
     */
    PROTOERROR(1, 10001),
    /**
     * <code>NOUSER = 10002;</code>
     *
     * <pre>
     *不存在该用户
     * </pre>
     */
    NOUSER(2, 10002),
    /**
     * <code>USEROFFLINE = 10003;</code>
     *
     * <pre>
     *用户不在线
     * </pre>
     */
    USEROFFLINE(3, 10003),
    /**
     * <code>ITEMERROR = 10004;</code>
     *
     * <pre>
     *物品不存在
     * </pre>
     */
    ITEMERROR(4, 10004),
    /**
     * <code>ITEMNUMERROR = 10005;</code>
     *
     * <pre>
     *物品数量错误
     * </pre>
     */
    ITEMNUMERROR(5, 10005),
    /**
     * <code>ITEMCANTUSEERROR = 10006;</code>
     *
     * <pre>
     *物品不可使用
     * </pre>
     */
    ITEMCANTUSEERROR(6, 10006),
    /**
     * <code>MISSIONIDERROR = 10007;</code>
     *
     * <pre>
     *关卡id有误
     * </pre>
     */
    MISSIONIDERROR(7, 10007),
    /**
     * <code>TASKNUMERROR = 10008;</code>
     *
     * <pre>
     *任务数量有误
     * </pre>
     */
    TASKNUMERROR(8, 10008),
    /**
     * <code>BAGFULLERROR = 10009;</code>
     *
     * <pre>
     *背包已满
     * </pre>
     */
    BAGFULLERROR(9, 10009),
    /**
     * <code>MESSAGENONE = 10010;</code>
     *
     * <pre>
     *消息不存在
     * </pre>
     */
    MESSAGENONE(10, 10010),
    /**
     * <code>NOTFRIEND = 10011;</code>
     *
     * <pre>
     *已经是朋友关系
     * </pre>
     */
    NOTFRIEND(11, 10011),
    /**
     * <code>FRIENDNUMERROR = 10012;</code>
     *
     * <pre>
     *好友数量已达上限
     * </pre>
     */
    FRIENDNUMERROR(12, 10012),
    /**
     * <code>ACTIONFULL = 10013;</code>
     *
     * <pre>
     *体力满
     * </pre>
     */
    ACTIONFULL(13, 10013),
    /**
     * <code>ACTIONLACK = 10029;</code>
     *
     * <pre>
     *体力不足
     * </pre>
     */
    ACTIONLACK(14, 10029),
    /**
     * <code>MESSAGEGETED = 10014;</code>
     *
     * <pre>
     *邮件已领取
     * </pre>
     */
    MESSAGEGETED(15, 10014),
    /**
     * <code>EXCELERROR = 10015;</code>
     *
     * <pre>
     *表有误
     * </pre>
     */
    EXCELERROR(16, 10015),
    /**
     * <code>NAMECREATED = 10016;</code>
     *
     * <pre>
     *名字已被创建
     * </pre>
     */
    NAMECREATED(17, 10016),
    /**
     * <code>NAMEERROR = 10017;</code>
     *
     * <pre>
     *名字有误
     * </pre>
     */
    NAMEERROR(18, 10017),
    /**
     * <code>LENGTHERROR = 10018;</code>
     *
     * <pre>
     *长度有误
     * </pre>
     */
    LENGTHERROR(19, 10018),
    /**
     * <code>LVERROR = 10019;</code>
     *
     * <pre>
     *等级有误
     * </pre>
     */
    LVERROR(20, 10019),
    /**
     * <code>ADDDSELFERROR = 10020;</code>
     *
     * <pre>
     *好友不可添加自己
     * </pre>
     */
    ADDDSELFERROR(21, 10020),
    /**
     * <code>FIGHTHALLFULL = 10022;</code>
     *
     * <pre>
     *对战大厅人已满
     * </pre>
     */
    FIGHTHALLFULL(22, 10022),
    /**
     * <code>ROOMNOTEXIT = 10023;</code>
     *
     * <pre>
     *房间不存在
     * </pre>
     */
    ROOMNOTEXIT(23, 10023),
    /**
     * <code>NOTINROOM = 10024;</code>
     *
     * <pre>
     *不在房内
     * </pre>
     */
    NOTINROOM(24, 10024),
    /**
     * <code>NOTINHALL = 10025;</code>
     *
     * <pre>
     *不在大厅
     * </pre>
     */
    NOTINHALL(25, 10025),
    /**
     * <code>ROOMPWDERROR = 10026;</code>
     *
     * <pre>
     *房间密码错误
     * </pre>
     */
    ROOMPWDERROR(26, 10026),
    /**
     * <code>ROOMFULL = 10027;</code>
     *
     * <pre>
     *房间已满
     * </pre>
     */
    ROOMFULL(27, 10027),
    /**
     * <code>TOOLONGTIMEFORCONNECT = 10028;</code>
     *
     * <pre>
     *断线重连过期
     * </pre>
     */
    TOOLONGTIMEFORCONNECT(28, 10028),
    /**
     * <code>ACCOUNTERROR = 20000;</code>
     *
     * <pre>
     *账号错误
     * </pre>
     */
    ACCOUNTERROR(29, 20000),
    /**
     * <code>BANFORTALK = 20001;</code>
     *
     * <pre>
     *禁止发言
     * </pre>
     */
    BANFORTALK(30, 20001),
    /**
     * <code>ACCOUNTEXIST = 30000;</code>
     *
     * <pre>
     *帐户已存在
     * </pre>
     */
    ACCOUNTEXIST(31, 30000),
    /**
     * <code>ACCOUNTPWDNOTEXIST = 30001;</code>
     *
     * <pre>
     *当前密码不存在
     * </pre>
     */
    ACCOUNTPWDNOTEXIST(32, 30001),
    /**
     * <code>ACCOUNTNAMEERROR = 30002;</code>
     *
     * <pre>
     *账号不存在
     * </pre>
     */
    ACCOUNTNAMEERROR(33, 30002),
    /**
     * <code>ACCOUNTPWDERROR = 30003;</code>
     *
     * <pre>
     *密码有误
     * </pre>
     */
    ACCOUNTPWDERROR(34, 30003),
    /**
     * <code>PAYRECEIPTNOTEXIST = 40001;</code>
     *
     * <pre>
     *苹果支付receipt无效
     * </pre>
     */
    PAYRECEIPTNOTEXIST(35, 40001),
    /**
     * <code>PAYFAILED = 40002;</code>
     *
     * <pre>
     *支付失败
     * </pre>
     */
    PAYFAILED(36, 40002),
    /**
     * <code>PAYEXIST = 40003;</code>
     *
     * <pre>
     *已经购买成功
     * </pre>
     */
    PAYEXIST(37, 40003),
    /**
     * <code>HAVEFIRSTRECHARGE = 40004;</code>
     *
     * <pre>
     *已经首充
     * </pre>
     */
    HAVEFIRSTRECHARGE(38, 40004),
    /**
     * <code>ADMAX = 50001;</code>
     *
     * <pre>
     *播放广告的次数达到上限
     * </pre>
     */
    ADMAX(39, 50001),
    ;

    /**
     * <code>UNKNOWERROR = 10000;</code>
     *
     * <pre>
     *未知错误
     * </pre>
     */
    public static final int UNKNOWERROR_VALUE = 10000;
    /**
     * <code>PROTOERROR = 10001;</code>
     *
     * <pre>
     *协议错误
     * </pre>
     */
    public static final int PROTOERROR_VALUE = 10001;
    /**
     * <code>NOUSER = 10002;</code>
     *
     * <pre>
     *不存在该用户
     * </pre>
     */
    public static final int NOUSER_VALUE = 10002;
    /**
     * <code>USEROFFLINE = 10003;</code>
     *
     * <pre>
     *用户不在线
     * </pre>
     */
    public static final int USEROFFLINE_VALUE = 10003;
    /**
     * <code>ITEMERROR = 10004;</code>
     *
     * <pre>
     *物品不存在
     * </pre>
     */
    public static final int ITEMERROR_VALUE = 10004;
    /**
     * <code>ITEMNUMERROR = 10005;</code>
     *
     * <pre>
     *物品数量错误
     * </pre>
     */
    public static final int ITEMNUMERROR_VALUE = 10005;
    /**
     * <code>ITEMCANTUSEERROR = 10006;</code>
     *
     * <pre>
     *物品不可使用
     * </pre>
     */
    public static final int ITEMCANTUSEERROR_VALUE = 10006;
    /**
     * <code>MISSIONIDERROR = 10007;</code>
     *
     * <pre>
     *关卡id有误
     * </pre>
     */
    public static final int MISSIONIDERROR_VALUE = 10007;
    /**
     * <code>TASKNUMERROR = 10008;</code>
     *
     * <pre>
     *任务数量有误
     * </pre>
     */
    public static final int TASKNUMERROR_VALUE = 10008;
    /**
     * <code>BAGFULLERROR = 10009;</code>
     *
     * <pre>
     *背包已满
     * </pre>
     */
    public static final int BAGFULLERROR_VALUE = 10009;
    /**
     * <code>MESSAGENONE = 10010;</code>
     *
     * <pre>
     *消息不存在
     * </pre>
     */
    public static final int MESSAGENONE_VALUE = 10010;
    /**
     * <code>NOTFRIEND = 10011;</code>
     *
     * <pre>
     *已经是朋友关系
     * </pre>
     */
    public static final int NOTFRIEND_VALUE = 10011;
    /**
     * <code>FRIENDNUMERROR = 10012;</code>
     *
     * <pre>
     *好友数量已达上限
     * </pre>
     */
    public static final int FRIENDNUMERROR_VALUE = 10012;
    /**
     * <code>ACTIONFULL = 10013;</code>
     *
     * <pre>
     *体力满
     * </pre>
     */
    public static final int ACTIONFULL_VALUE = 10013;
    /**
     * <code>ACTIONLACK = 10029;</code>
     *
     * <pre>
     *体力不足
     * </pre>
     */
    public static final int ACTIONLACK_VALUE = 10029;
    /**
     * <code>MESSAGEGETED = 10014;</code>
     *
     * <pre>
     *邮件已领取
     * </pre>
     */
    public static final int MESSAGEGETED_VALUE = 10014;
    /**
     * <code>EXCELERROR = 10015;</code>
     *
     * <pre>
     *表有误
     * </pre>
     */
    public static final int EXCELERROR_VALUE = 10015;
    /**
     * <code>NAMECREATED = 10016;</code>
     *
     * <pre>
     *名字已被创建
     * </pre>
     */
    public static final int NAMECREATED_VALUE = 10016;
    /**
     * <code>NAMEERROR = 10017;</code>
     *
     * <pre>
     *名字有误
     * </pre>
     */
    public static final int NAMEERROR_VALUE = 10017;
    /**
     * <code>LENGTHERROR = 10018;</code>
     *
     * <pre>
     *长度有误
     * </pre>
     */
    public static final int LENGTHERROR_VALUE = 10018;
    /**
     * <code>LVERROR = 10019;</code>
     *
     * <pre>
     *等级有误
     * </pre>
     */
    public static final int LVERROR_VALUE = 10019;
    /**
     * <code>ADDDSELFERROR = 10020;</code>
     *
     * <pre>
     *好友不可添加自己
     * </pre>
     */
    public static final int ADDDSELFERROR_VALUE = 10020;
    /**
     * <code>FIGHTHALLFULL = 10022;</code>
     *
     * <pre>
     *对战大厅人已满
     * </pre>
     */
    public static final int FIGHTHALLFULL_VALUE = 10022;
    /**
     * <code>ROOMNOTEXIT = 10023;</code>
     *
     * <pre>
     *房间不存在
     * </pre>
     */
    public static final int ROOMNOTEXIT_VALUE = 10023;
    /**
     * <code>NOTINROOM = 10024;</code>
     *
     * <pre>
     *不在房内
     * </pre>
     */
    public static final int NOTINROOM_VALUE = 10024;
    /**
     * <code>NOTINHALL = 10025;</code>
     *
     * <pre>
     *不在大厅
     * </pre>
     */
    public static final int NOTINHALL_VALUE = 10025;
    /**
     * <code>ROOMPWDERROR = 10026;</code>
     *
     * <pre>
     *房间密码错误
     * </pre>
     */
    public static final int ROOMPWDERROR_VALUE = 10026;
    /**
     * <code>ROOMFULL = 10027;</code>
     *
     * <pre>
     *房间已满
     * </pre>
     */
    public static final int ROOMFULL_VALUE = 10027;
    /**
     * <code>TOOLONGTIMEFORCONNECT = 10028;</code>
     *
     * <pre>
     *断线重连过期
     * </pre>
     */
    public static final int TOOLONGTIMEFORCONNECT_VALUE = 10028;
    /**
     * <code>ACCOUNTERROR = 20000;</code>
     *
     * <pre>
     *账号错误
     * </pre>
     */
    public static final int ACCOUNTERROR_VALUE = 20000;
    /**
     * <code>BANFORTALK = 20001;</code>
     *
     * <pre>
     *禁止发言
     * </pre>
     */
    public static final int BANFORTALK_VALUE = 20001;
    /**
     * <code>ACCOUNTEXIST = 30000;</code>
     *
     * <pre>
     *帐户已存在
     * </pre>
     */
    public static final int ACCOUNTEXIST_VALUE = 30000;
    /**
     * <code>ACCOUNTPWDNOTEXIST = 30001;</code>
     *
     * <pre>
     *当前密码不存在
     * </pre>
     */
    public static final int ACCOUNTPWDNOTEXIST_VALUE = 30001;
    /**
     * <code>ACCOUNTNAMEERROR = 30002;</code>
     *
     * <pre>
     *账号不存在
     * </pre>
     */
    public static final int ACCOUNTNAMEERROR_VALUE = 30002;
    /**
     * <code>ACCOUNTPWDERROR = 30003;</code>
     *
     * <pre>
     *密码有误
     * </pre>
     */
    public static final int ACCOUNTPWDERROR_VALUE = 30003;
    /**
     * <code>PAYRECEIPTNOTEXIST = 40001;</code>
     *
     * <pre>
     *苹果支付receipt无效
     * </pre>
     */
    public static final int PAYRECEIPTNOTEXIST_VALUE = 40001;
    /**
     * <code>PAYFAILED = 40002;</code>
     *
     * <pre>
     *支付失败
     * </pre>
     */
    public static final int PAYFAILED_VALUE = 40002;
    /**
     * <code>PAYEXIST = 40003;</code>
     *
     * <pre>
     *已经购买成功
     * </pre>
     */
    public static final int PAYEXIST_VALUE = 40003;
    /**
     * <code>HAVEFIRSTRECHARGE = 40004;</code>
     *
     * <pre>
     *已经首充
     * </pre>
     */
    public static final int HAVEFIRSTRECHARGE_VALUE = 40004;
    /**
     * <code>ADMAX = 50001;</code>
     *
     * <pre>
     *播放广告的次数达到上限
     * </pre>
     */
    public static final int ADMAX_VALUE = 50001;


    public final int getNumber() { return value; }

    public static ErrorCode valueOf(int value) {
      switch (value) {
        case 10000: return UNKNOWERROR;
        case 10001: return PROTOERROR;
        case 10002: return NOUSER;
        case 10003: return USEROFFLINE;
        case 10004: return ITEMERROR;
        case 10005: return ITEMNUMERROR;
        case 10006: return ITEMCANTUSEERROR;
        case 10007: return MISSIONIDERROR;
        case 10008: return TASKNUMERROR;
        case 10009: return BAGFULLERROR;
        case 10010: return MESSAGENONE;
        case 10011: return NOTFRIEND;
        case 10012: return FRIENDNUMERROR;
        case 10013: return ACTIONFULL;
        case 10029: return ACTIONLACK;
        case 10014: return MESSAGEGETED;
        case 10015: return EXCELERROR;
        case 10016: return NAMECREATED;
        case 10017: return NAMEERROR;
        case 10018: return LENGTHERROR;
        case 10019: return LVERROR;
        case 10020: return ADDDSELFERROR;
        case 10022: return FIGHTHALLFULL;
        case 10023: return ROOMNOTEXIT;
        case 10024: return NOTINROOM;
        case 10025: return NOTINHALL;
        case 10026: return ROOMPWDERROR;
        case 10027: return ROOMFULL;
        case 10028: return TOOLONGTIMEFORCONNECT;
        case 20000: return ACCOUNTERROR;
        case 20001: return BANFORTALK;
        case 30000: return ACCOUNTEXIST;
        case 30001: return ACCOUNTPWDNOTEXIST;
        case 30002: return ACCOUNTNAMEERROR;
        case 30003: return ACCOUNTPWDERROR;
        case 40001: return PAYRECEIPTNOTEXIST;
        case 40002: return PAYFAILED;
        case 40003: return PAYEXIST;
        case 40004: return HAVEFIRSTRECHARGE;
        case 50001: return ADMAX;
        default: return null;
      }
    }

    public static com.google.protobuf.Internal.EnumLiteMap<ErrorCode>
        internalGetValueMap() {
      return internalValueMap;
    }
    private static com.google.protobuf.Internal.EnumLiteMap<ErrorCode>
        internalValueMap =
          new com.google.protobuf.Internal.EnumLiteMap<ErrorCode>() {
            public ErrorCode findValueByNumber(int number) {
              return ErrorCode.valueOf(number);
            }
          };

    public final com.google.protobuf.Descriptors.EnumValueDescriptor
        getValueDescriptor() {
      return getDescriptor().getValues().get(index);
    }
    public final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptorForType() {
      return getDescriptor();
    }
    public static final com.google.protobuf.Descriptors.EnumDescriptor
        getDescriptor() {
      return protocol.ProtoData.getDescriptor().getEnumTypes().get(6);
    }

    private static final ErrorCode[] VALUES = values();

    public static ErrorCode valueOf(
        com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
      if (desc.getType() != getDescriptor()) {
        throw new java.lang.IllegalArgumentException(
          "EnumValueDescriptor is not for this type.");
      }
      return VALUES[desc.getIndex()];
    }

    private final int index;
    private final int value;

    private ErrorCode(int index, int value) {
      this.index = index;
      this.value = value;
    }

    // @@protoc_insertion_point(enum_scope:protocol.ErrorCode)
  }


  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013proto.proto\022\010protocol*\333!\n\004CToS\022\022\n\rREQU" +
      "ESTSERVER\020\350\007\022\024\n\017REQUESTREGISTER\020\355\007\022\021\n\014RE" +
      "QUESTLOGIN\020\351\007\022\022\n\rREQUESTCREATE\020\352\007\022\023\n\016REQ" +
      "UESTROLESEX\020\274\t\022\022\n\rREQUESTGETSEX\020\275\t\022\023\n\016RE" +
      "QUESTSETINFO\020\353\007\022\026\n\021REQUESTCHOOSEROLE\020\354\007\022" +
      "\023\n\016REQUESTBUYITEM\020\356\007\022\023\n\016REQUESTUSEITEM\020\357" +
      "\007\022\027\n\022REQUESTOPENBAGCELL\020\360\007\022\023\n\016REQUESTCOM" +
      "POSE\020\361\007\022\030\n\023REQUESTBEGINMISSION\020\362\007\022\030\n\023REQ" +
      "UESTCOUNTMISSION\020\363\007\022\030\n\023REQUESTBEGINENDLE" +
      "SS\020\364\007\022\030\n\023REQUESTCOUNTENDLESS\020\365\007\022\031\n\024REQUE",
      "STUPDATENEWUSER\020\366\007\022\027\n\022REQUESTWECHATLOGIN" +
      "\020\367\007\022\026\n\021REQUESTAPPLELOGIN\020\370\007\022\023\n\016REQUESTQQ" +
      "LOGIN\020\371\007\022\025\n\020REQUESTYSDKLOGIN\020\330\016\022\027\n\022REQUE" +
      "STHUAWEILOGIN\020\331\016\022\027\n\022REQUESTGOOGLELOGIN\020\332" +
      "\016\022\024\n\017REQUESTSENDCHAT\020\374\007\022\026\n\021REQUESTFINISH" +
      "TASK\020\206\010\022\027\n\022REQUESTOPERATEVIEW\020\232\010\022\027\n\022REQU" +
      "ESTBUYCLOTHING\020\233\010\022\032\n\025REQUESTCHANGECUPBOA" +
      "RD\020\234\010\022\027\n\022REQUESTCHANGEDRESS\020\235\010\022\032\n\025REQUES" +
      "TCUPBOARDTOBODY\020\236\010\022\031\n\024REQUESTSTARBITOMON" +
      "EY\020\237\010\022\031\n\024REQUESTOPERATEFRIEND\020\244\010\022\032\n\025REQU",
      "ESTOPERATEFRIEND1\020\261\t\022\032\n\025REQUESTOPERATEME" +
      "SSAGE\020\245\010\022\024\n\017REQUESTPETHATCH\020\272\013\022\033\n\026REQUES" +
      "TRECOMMENDFRIEND\020\250\010\022\033\n\026REQUESTSTATIONLOC" +
      "ATION\020\252\010\022\027\n\022REQUESTGIVEPRESENT\020\253\010\022\026\n\021REQ" +
      "UESTACCUSATION\020\254\010\022\023\n\016REQUESTGETRANK\020\256\010\022\026" +
      "\n\021REQUESTGETRANKING\020\307\010\022\033\n\026REQUESTGETTOTA" +
      "LENDLESS\020\257\010\022\024\n\017REQUESTJOINROOM\020\270\010\022\026\n\021REQ" +
      "UESTCREATEROOM\020\271\010\022\025\n\020REQUESTLEAVEROOM\020\272\010" +
      "\022\032\n\025REQUESTUPDATEROOMINFO\020\273\010\022\032\n\025REQUESTU" +
      "PDATEROOMROLE\020\274\010\022\031\n\024REQUESTJOINFIGHTHALL",
      "\020\275\010\022\027\n\022REQUESTSTARTINROOM\020\276\010\022\030\n\023REQUESTR" +
      "ECONNECTION\020\260\t\022\025\n\020REQUESTCHOOSEPAY\020\334\013\022\031\n" +
      "\024REQUESTSUBMITPAYBACK\020\335\013\022\025\n\020REQUESTGOOGL" +
      "EPAY\020\336\013\022\025\n\020REQUESTWECHATPAY\020\337\013\022\022\n\rREQUES" +
      "TALIPAY\020\340\013\022!\n\034REQUESTCONFIRMHUAWEIPURCHA" +
      "SE\020\341\013\022\027\n\022REQUESTYSDKBALANCE\020\342\013\022\031\n\024REQUES" +
      "TBEGINHEADBALL\020\372\007\022\031\n\024REQUESTCOUNTHEADBAL" +
      "L\020\373\007\022\022\n\rREQUESTADPLAY\020\302\014\022\021\n\014REQUESTADNUN" +
      "\020\303\014\022\025\n\020REQUESTCOUNTGOLD\020\244\r\022\025\n\020REQUESTBEG" +
      "INGOLD\020\245\r\022\025\n\020REQUESTBEGINDOWN\020\246\r\022\025\n\020REQU",
      "ESTCOUNTDOWN\020\250\r\022\026\n\021REQUESTBEGINSTACK\020\251\r\022" +
      "\026\n\021REQUESTCOUNTSTACK\020\255\r\022\026\n\021REQUESTCOUNTL" +
      "OTTO\020\247\r\022\032\n\025REQUESTSOURCESMACHINE\020\254\r\022\025\n\020R" +
      "EQURESTMAKEITEM\020\252\r\022\023\n\016REQUESTPRODUCT\020\253\r\022" +
      "\034\n\027REQUESTVISITOROPERATION\020\354\016\022\024\n\017REQUEST" +
      "APPROVAL\020\200\017\022\032\n\025REQUESTAUTHENTICATION\020\201\017\022" +
      "\027\n\022REQUESTGETAPPROVAL\020\202\017\022\031\n\024REQUESTSTART" +
      "GAMECOPY\020\342\010\022\031\n\024REQUESTCOUNTGAMECOPY\020\343\010\022\030" +
      "\n\023REQUESTGETLUCKYITEM\020\344\010\022\034\n\027REQUESTFINIS" +
      "HACTIVITIES\020\346\010\022\035\n\030REQUESTMARKETINFORMATI",
      "ON\020\351\010\022\030\n\023REQUESTGOODSOPERATE\020\352\010\022\032\n\025REQUE" +
      "STTICKTEEXCHANGE\020\353\010\022 \n\033REQUESTQUERYROLEI" +
      "NFORMATION\020\357\010\022\024\n\017REQUESTWAITITEM\020\360\010\022\033\n\026R" +
      "EQUESTUPWAITITEMLEVEL\020\361\010\022\027\n\022REQUESTGETWA" +
      "ITITEM\020\362\010\022\021\n\014REQUESTHOUSE\020\300\014\022\025\n\020REQUESTH" +
      "OUSEPART\020\305\014\022\030\n\023REQUESTBUYFURNITURE\020\206\r\022\027\n" +
      "\022REQUESTOPERATEMAIL\020\326\010\022\031\n\024REQUESTOPERATE" +
      "NOTICE\020\327\010\022\027\n\022REQUESTGETPETSINFO\020\272\t\022\024\n\017RE" +
      "QUESTGETEQUIP\020\277\t\022\026\n\021REQUESTOPERATEPET\020\273\t" +
      "\022\027\n\022REQUESTOPERATEROLE\020\237\n\022\027\n\022REQUESTCHOI",
      "CEEQUIP\020\302\t\022\030\n\023REQUESTOPERATEEQUIP\020\300\t\022\027\n\022" +
      "REQUESTCHANGEEQUIP\020\301\t\022\026\n\021REQUESTBINDIDCA" +
      "RD\020\331\010\022\034\n\027REQUESTGETDISPATCHINFOS\020\340\010\022\037\n\032R" +
      "EQUESTOPERATEDISPATCHTASK\020\341\010\022\026\n\021REQUESTR" +
      "AFFLEPOOL\020\376\010\022\030\n\023REQUESTGETEVENTINFO\020\210\t\022\026" +
      "\n\021REQUESTFLUSHEVENT\020\211\t\022\027\n\022REQUESTCOUNTBA" +
      "TTLE\020\222\t\022\027\n\022REQUESTPLAYERITEMS\020\234\t\022\026\n\021REQU" +
      "ESTUPPETSKILL\020\246\t\022\022\n\rREQUESTPETEXP\020\254\014\022\027\n\022" +
      "REQUESTBALCKMARKET\020\342\t\022\036\n\031REQUESTBUYBALCK" +
      "MARKETITEM\020\343\t\022\030\n\023REQUESTPETFORMATION\020\355\t\022",
      "\033\n\026REQUESTGETPETFORMATION\020\356\t\022\026\n\021REQUESTP" +
      "ETCOMPOSE\020\361\t\022\037\n\032REQUESTPETBREEDINOFRMATI" +
      "ON\020\370\t\022\035\n\030REQUESTPETBREEDOPERATION\020\371\t\022\024\n\017" +
      "REQUESTPETBREED\020\372\t\022\033\n\026REQUESTGETPETBREED" +
      "INFO\020\366\t\022\033\n\026REQUESTOPERATEPETBREED\020\367\t\022\032\n\025" +
      "REQUESTGETPETGROWINFO\020\224\n\022\030\n\023REQUESTPETEV" +
      "OLUTION\020\225\n\022\033\n\026REQUESTPETBREAKTHROUGH\020\226\n\022" +
      "\032\n\025REQUESTGETEXPERIENCES\020\200\n\022\034\n\027REQUESTFL" +
      "USHEXPERIENCES\020\201\n\022\036\n\031REQUESTOPERATEEXPER" +
      "IENCES\020\202\n\022\027\n\022REQUESTITEMCOMPOSE\020\203\n\022\024\n\017RE",
      "QUESTPHYSICAL\020\226\010\022\026\n\021REQUESTGETROLEEXP\020\246\n" +
      "\022\034\n\027REQUESTOPERATIONFRIENDS\020\227\n\022\022\n\rREQUES" +
      "TGETMAP\020\332\n\022\022\n\rREQUESTUPDMAP\020\333\n\022\025\n\020REQUES" +
      "TREMOVEPET\020\205\n\022\027\n\022REQUESTDAILYTANSKS\020\212\n\022\036" +
      "\n\031REQUESTCOMPLETEDAILYTASKS\020\213\n\022\020\n\013REQUES" +
      "TTASK\020\214\n\022\033\n\026REQUESTACHIEVEMENTTYPE\020\215\n\022\031\n" +
      "\024REQUESTACHIEVEMENTIS\020\216\n\022\027\n\022REQUESTACHIE" +
      "VEMENT\020\217\n\022\023\n\016REQUESTADDITEM\020\240\010\022\025\n\020REQUES" +
      "TSYNTHESIS\020\241\010\022\033\n\026REQUESTPLAYERRECOMMEND\020" +
      "\222\n\022\030\n\023REQUESTFRIENDSAPPLY\020\223\n\022\036\n\031REQUESTS",
      "ENDOUTINFORMATION\020\230\n\022\026\n\021REQUESTCHATRECOR" +
      "D\020\231\n\022\026\n\021REQUESTUPDATETYPE\020\232\n\022\020\n\013REQUESTR" +
      "ANK\020\233\n\022\021\n\014REQUESTLEVEL\020\234\n\022\022\n\rREQUESTLEVE" +
      "LD\020\235\n\022\021\n\014REQUESTPlont\020\250\n\022\020\n\013REQUESTChap\020" +
      "\251\n\022\025\n\020REQUESTGetEquipA\020\252\n\022\024\n\017REQUESTDDDE" +
      "quip\020\253\n\022\022\n\rREQUESTEquipF\020\254\n\022\024\n\017REQUESTbo" +
      "ssTiem\020\255\n\022\024\n\017REQUESTbossdear\020\256\n\022\031\n\024REQUE" +
      "STPVPPetOperate\020\262\n\022\027\n\022REQUESTPVPBaseData" +
      "\020\263\n\022\025\n\020REQUESTPVPBattle\020\264\n\022\033\n\026REQUESTPVP" +
      "BattleResult\020\265\n\022\037\n\032REQUESTPVPBATTLEREMAI",
      "NTIME\020\266\n\022\027\n\022REQUESTNewMailTest\020\274\n\022\022\n\rREQ" +
      "UESTADITEM\020\275\n\022 \n\033REQUESTRECHAREGREBATERE" +
      "WARD\020\276\n\022\035\n\030REQUESTLIMITEDTIMEREWARD\020\306\n\022!" +
      "\n\034REQUESTLIMITEDTIMEREWARDTYPE\020\307\n\022#\n\036REQ" +
      "UESTLIMITEDTIMEREWARDFINISH\020\310\n\022\026\n\021REQUES" +
      "TREMAINTIME\020\312\n\022\025\n\020REQUESTPETEGGEXP\020\320\n\022\030\n" +
      "\023REQUESTADDPETEGGEXP\020\321\n\022\034\n\027RESPONSEPETEG" +
      "GFASTHATCH\020\322\n\022\023\n\016REQUESTTEMITEM\020\344\n\022\031\n\024RE" +
      "QUESTDELETEALLPETS\020\356\n*\250%\n\004SToC\022\023\n\016RESPON" +
      "SESERVER\020\320\017\022\025\n\020RESPONSEREGISTER\020\325\017\022\022\n\rRE",
      "SPONSELOGIN\020\321\017\022\023\n\016RESPONSECREATE\020\322\017\022\024\n\017R" +
      "ESPONSEROLESEX\020\300\020\022\023\n\016RESPONSEGETSEX\020\245\021\022\024" +
      "\n\017RESPONSESETINFO\020\323\017\022\027\n\022RESPONSECHOOSERO" +
      "LE\020\324\017\022\024\n\017RESPONSEBUYITEM\020\326\017\022\024\n\017RESPONSEU" +
      "SEITEM\020\327\017\022\030\n\023RESPONSEOPENBAGCELL\020\330\017\022\024\n\017R" +
      "ESPONSECOMPOSE\020\331\017\022\031\n\024RESPONSEBEGINMISSIO" +
      "N\020\332\017\022\031\n\024RESPONSECOUNTMISSION\020\333\017\022\031\n\024RESPO" +
      "NSEBEGINENDLESS\020\334\017\022\031\n\024RESPONSECOUNTENDLE" +
      "SS\020\335\017\022\032\n\025RESPONSEUPDATENEWUSER\020\336\017\022\025\n\020RES" +
      "PONSEPETHATCH\020\242\023\022\025\n\020RESPONSESENDCHAT\020\344\017\022",
      "\017\n\nREPORTCHAT\020\345\017\022\022\n\rREPORTMESSAGE\020\346\017\022\021\n\014" +
      "REPORTSYSTEM\020\347\017\022\025\n\020RESPONSEPHYSICAL\020\376\017\022\027" +
      "\n\022RESPONSEFINISHTASK\020\356\017\022\025\n\020REPORTUPDATET" +
      "ASK\020\357\017\022\027\n\022RESPONSEGETROLEEXP\020\216\022\022\030\n\023RESPO" +
      "NSEBUYCLOTHING\020\203\020\022\033\n\026RESPONSECHANGECUPBO" +
      "ARD\020\204\020\022\030\n\023RESPONSECHANGEDRESS\020\205\020\022\033\n\026RESP" +
      "ONSECUPBOARDTOBODY\020\206\020\022\032\n\025RESPONSESTARBIT" +
      "OMONEY\020\207\020\022\017\n\nREPORTITEM\020\210\020\022\032\n\025RESPONSEOP" +
      "ERATEFRIEND\020\214\020\022\033\n\026RESPONSEOPERATEFRIEND1" +
      "\020\231\021\022\033\n\026RESPONSEOPERATEMESSAGE\020\215\020\022\021\n\014REPO",
      "RTFRIEND\020\216\020\022\027\n\022REPORTDELETEFRIEND\020\217\020\022\034\n\027" +
      "RESPONSERECOMMENDFRIEND\020\220\020\022\032\n\025REPORTINAN" +
      "DOUTSTATION\020\221\020\022\032\n\025REPORTSTATIONLOCATION\020" +
      "\222\020\022\034\n\027REPORTUPDATESTATIONROLE\020\233\020\022\023\n\016RESP" +
      "ONSEGETMAP\020\302\022\022\023\n\016RESPONSEUPDMAP\020\303\022\022\030\n\023RE" +
      "SPONSEGIVEPRESENT\020\223\020\022\027\n\022RESPONSEACCUSATI" +
      "ON\020\224\020\022\027\n\022REPORTUPDATEFRIEND\020\225\020\022\024\n\017RESPON" +
      "SEGETRANK\020\226\020\022\027\n\022RESPONSEGETRANKING\020\257\020\022\034\n" +
      "\027RESPONSEGETTOTALENDLESS\020\227\020\022\030\n\023RESPONSEO" +
      "PERATEROLE\020\207\022\022\025\n\020RESPONSEJOINROOM\020\240\020\022\027\n\022",
      "RESPONSECREATEROOM\020\241\020\022\026\n\021RESPONSELEAVERO" +
      "OM\020\242\020\022\033\n\026RESPONSEUPDATEROOMINFO\020\243\020\022\033\n\026RE" +
      "SPONSEUPDATEROOMROLE\020\244\020\022\032\n\025RESPONSEJOINF" +
      "IGHTHALL\020\245\020\022\030\n\023RESPONSESTARTINROOM\020\246\020\022\017\n" +
      "\nREPORTROOM\020\247\020\022\026\n\021REPORTADDROOMROLE\020\250\020\022\033" +
      "\n\026REPORTUPDATEROOMINSIDE\020\251\020\022\026\n\021REPORTCOM" +
      "ETOFIGHT\020\252\020\022\022\n\rRESPONSESTAMP\020\264\020\022\026\n\021REPOR" +
      "TREPEATLOGIN\020\265\020\022\031\n\024REPORTFORCETOOFFLINE\020" +
      "\266\020\022\031\n\024RESPONSERECONNECTION\020\230\021\022\026\n\021RESPONS" +
      "ECHOOSEPAY\020\304\023\022\032\n\025RESPONSESUBMITPAYBACK\020\305",
      "\023\022\026\n\021RESPONSEGOOGLEPAY\020\306\023\022\026\n\021RESPONSEWEC" +
      "HATPAY\020\307\023\022\023\n\016RESPONSEALIPAY\020\310\023\022\"\n\035RESPON" +
      "SECONFIRMHUAWEIPURCHASE\020\311\023\022\030\n\023RESPONSEYS" +
      "DKBALANCE\020\312\023\022\032\n\025RESPONSEBEGINHEADBALL\020\342\017" +
      "\022\032\n\025RESPONSECOUNTHEADBALL\020\343\017\022\022\n\rRESPONSE" +
      "JEWEL\020\307\027\022\024\n\017RREPORTADREWARD\020\251\024\022\023\n\016RESPON" +
      "SEADPLAY\020\252\024\022\022\n\rRESPONSEADNUM\020\253\024\022\026\n\021RESPO" +
      "NSECOUNTGOLD\020\214\025\022\026\n\021RESPONSEBEGINGOLD\020\215\025\022" +
      "\026\n\021RESPONSEBEGINDOWN\020\216\025\022\027\n\022RESPONSEBEGIN" +
      "STACK\020\222\025\022\026\n\021RESPONSECOUNTDOWN\020\220\025\022\027\n\022RESP",
      "ONSECOUNTSTACK\020\221\025\022\027\n\022RESPONSECOUNTLOTTO\020" +
      "\217\025\022\033\n\026RESPONSESOURCESMACHINE\020\236\020\022\025\n\020RESPO" +
      "NSEMAKEITEM\020\234\020\022\024\n\017RESPONSEPRODUCT\020\235\020\022\031\n\024" +
      "RESPONSEWARDROBEVIEW\020\360\025\022\035\n\030RESPONSEVISIT" +
      "OROPERATION\020\324\026\022\025\n\020RESPONSEAPPROVAL\020\350\026\022\032\n" +
      "\025RESPONSAUTHENTICATION\020\351\026\022\030\n\023RESPONSEGET" +
      "APPROVAL\020\352\026\022\031\n\024RESPOSESTARTGAMECOPY\020\312\020\022\031" +
      "\n\024REAPOSECOUNTGAMECOPY\020\313\020\022\030\n\023RESPOSEGETL" +
      "UCKYITEM\020\314\020\022\033\n\026RESPOSEUPDATELUCKYITEM\020\315\020" +
      "\022\034\n\027RESPOSEFINISHACTIVITIES\020\316\020\022\033\n\026REPORT",
      "UPDATEACTIVITIES\020\317\020\022\035\n\030REPORTBROADCASTLU" +
      "CKYUSER\020\320\020\022\035\n\030RESPOSEMARKETINFORMATION\020\321" +
      "\020\022\030\n\023RESPOSEGOODSOPERATE\020\322\020\022\032\n\025RESPOSETI" +
      "CKTEEXCHANGE\020\323\020\022 \n\033RESPOSEQUERYROLEINFOR" +
      "MATION\020\327\020\022\024\n\017RESPOSEWAITITEM\020\330\020\022\033\n\026RESPO" +
      "SEUPWAITITEMLEVEL\020\331\020\022\027\n\022RESPOSEGETWAITIT" +
      "EM\020\332\020\022\021\n\014RESPOSEHOUSE\020\250\024\022\025\n\020RESPOSEHOUSE" +
      "PART\020\255\024\022\030\n\023RESPOSEBUYFURNITURE\020\356\024\022\027\n\022RES" +
      "POSEOPERATEMAIL\020\276\020\022\022\n\rREPORTNEWMAIL\020\277\020\022\024" +
      "\n\017REPORTNEWNOTICE\020\256\021\022\027\n\022RESPOSEGETPETSIN",
      "FO\020\242\021\022\025\n\020RESPONSEGETEQUIP\020\247\021\022\030\n\023RESPONSE" +
      "CHOICEEQUIP\020\252\021\022\026\n\021RESPOSEOPERATEPET\020\243\021\022\031" +
      "\n\024RESPONSEOPERATEEQUIP\020\250\021\022\030\n\023RESPONSECHA" +
      "NGEEQUIP\020\251\021\022\026\n\021RESPOSEBINDIDCARD\020\301\020\022\034\n\027R" +
      "ESPOSEGETDISPATCHINFOS\020\310\020\022\037\n\032RESPOSEOPER" +
      "ATEDISPATCHTASK\020\311\020\022\026\n\021RESPOSERAFFLEPOOL\020" +
      "\346\020\022\023\n\016RESPONSEPETEXP\020\224\024\022\030\n\023RESPOSEGETEVE" +
      "NTINFO\020\360\020\022\026\n\021RESPOSEFLUSHEVENT\020\361\020\022\027\n\022RES" +
      "POSECOUNTBATTLE\020\372\020\022\027\n\022RESPOSEPLAYERITEMS" +
      "\020\204\021\022\026\n\021RESPOSEUPPETSKILL\020\216\021\022\027\n\022RESPOSEBA",
      "LCKMARKET\020\312\021\022\036\n\031RESPOSEBUYBALCKMARKETITE" +
      "M\020\313\021\022\030\n\023RESPOSEPETFORMATION\020\325\021\022\033\n\026RESPOS" +
      "EGETPETFORMATION\020\326\021\022\026\n\021RESPOSEPETCOMPOSE" +
      "\020\331\021\022\033\n\026RESPOSEGETPETBREEDINFO\020\336\021\022\033\n\026RESP" +
      "OSEOPERATEPETBREED\020\337\021\022 \n\033RESPONSEPETBREE" +
      "DINOFRMATION\020\340\021\022\036\n\031RESPONSEPETBREEDOPERA" +
      "TION\020\341\021\022\025\n\020RESPONSEPETBREED\020\342\021\022\032\n\025RESPOS" +
      "EGETPETGROWINFO\020\374\021\022\030\n\023RESPOSEPETEVOLUTIO" +
      "N\020\375\021\022\033\n\026RESPOSEPETBREAKTHROUGH\020\376\021\022\032\n\025RES" +
      "POSEGETEXPERIENCES\020\350\021\022\034\n\027RESPOSEFLUSHEXP",
      "ERIENCES\020\351\021\022\036\n\031RESPOSEOPERATEEXPERIENCES" +
      "\020\352\021\022\027\n\022RESPOSEITEMCOMPOSE\020\353\021\022\035\n\030RESPONSE" +
      "OPERATIONFRIENDS\020\377\021\022\026\n\021RESPONSESYNTHESIS" +
      "\020\211\020\022\026\n\021RESPONSEREMOVEPET\020\355\021\022\030\n\023RESPONSED" +
      "AILYTANSKS\020\362\021\022\037\n\032RESPONSECOMPLETEDAILYTA" +
      "SKS\020\363\021\022\021\n\014RESPONSETASK\020\364\021\022\034\n\027RESPONSEACH" +
      "IEVEMENTTYPE\020\365\021\022\032\n\025RESPONSEACHIEVEMENTIS" +
      "\020\366\021\022\030\n\023RESPONSEACHIEVEMENT\020\367\021\022\036\n\031RESPONS" +
      "EPLAYERONLINEORNOT\020\370\021\022\037\n\032RESPONSEPLAYERO" +
      "FFLINEORNOT\020\371\021\022\034\n\027RESPONSEPLAYERRECOMMEN",
      "D\020\372\021\022\031\n\024RESPONSEFRIENDSAPPLY\020\373\021\022\037\n\032RESPO" +
      "NSESENDOUTINFORMATION\020\200\022\022\027\n\022RESPONSECHAT" +
      "RECORD\020\201\022\022\027\n\022RESPONSEUPDATETYPE\020\202\022\022\021\n\014RE" +
      "SPONSERANK\020\203\022\022\022\n\rRESPONSELEVEL\020\204\022\022\023\n\016RES" +
      "PPNSELEVELD\020\205\022\022\023\n\016RESPONSETemper\020\213\022\022\022\n\rR" +
      "ESPONSEPlont\020\220\022\022\021\n\014RESPONSEChap\020\221\022\022\026\n\021RE" +
      "SPONSEGetEquipA\020\222\022\022\025\n\020RESPONSEDDDEquip\020\223" +
      "\022\022\025\n\020RESPONSEbosstime\020\225\022\022\025\n\020RESPONSEboss" +
      "down\020\226\022\022\025\n\020RESPONSEbosslive\020\227\022\022\032\n\025RESPON" +
      "SEPVPPetOperate\020\232\022\022\030\n\023RESPONSEPVPBaseDat",
      "a\020\233\022\022\026\n\021RESPONSEPVPBattle\020\234\022\022 \n\033RESPONSE" +
      "PVPBATTLEREMAINTIME\020\236\022\022\027\n\022RESPONSEPAYDIA" +
      "MOND\020\244\022\022!\n\034RESPONSERECHAREGREBATEREWARD\020" +
      "\246\022\022\036\n\031RESPONSELIMITEDTIMEREWARD\020\256\022\022&\n!RE" +
      "SPONSELIMITEDTIMEREWARDTASKDATA\020\261\022\022\027\n\022RE" +
      "SPONSEREMAINTIME\020\262\022\022\026\n\021RESPONSEPETEGGEXP" +
      "\020\270\022\022\031\n\024RESPONSEALLPETEGGEXP\020\271\022\022\024\n\017RESPON" +
      "SETEMITEM\020\314\022\022\032\n\025RESPONSEDELETEALLPETS\020\326\022" +
      "*\366\001\n\004CToM\022\030\n\023REQUESTCONNECTFIGHT\020\326\027\022\026\n\021R" +
      "EQUESTLOADFINISH\020\327\027\022\020\n\013REQUESTSYNC\020\352\027\022\021\n",
      "\014REQUESTHEART\020\270\027\022\031\n\024REQUESTCONNECTBATTLE" +
      "\020\234\030\022\026\n\021REQUESTSYNCBATTLE\020\235\030\022\024\n\017UPDATEUSE" +
      "RSCORE\020\236\030\022\027\n\022REQUESTCREATESKILL\020\237\030\022\030\n\023RE" +
      "QUESTTRIGGERSKILL\020\241\030\022\033\n\026REQUESTUPDATEROB" +
      "OTINFO\020\205\032*\211\003\n\004MToC\022\031\n\024RESPONSECONNECTFIG" +
      "HT\020\276\037\022\027\n\022RESPONSELOADFINISH\020\277\037\022\021\n\014RESPON" +
      "SESYNC\020\322\037\022\030\n\023REPORTBROADCASTSYNC\020\323\037\022\032\n\025R" +
      "EPORTMISSIONPROGRESS\020\324\037\022\026\n\021REPORTCHANGES" +
      "CORE\020\325\037\022\017\n\nREPORTKICK\020\326\037\022\025\n\020REPORTSTARTF" +
      "IGHT\020\334\037\022\024\n\017REPORTOVERFIGHT\020\335\037\022\022\n\rRESPONS",
      "EHEART\020\240\037\022\032\n\025RESPONSECONNECTBATTLE\020\204 \022\027\n" +
      "\022RESPONSESYNCBATTLE\020\205 \022\031\n\024REPORTBROADCAS" +
      "TFIGHT\020\350 \022\030\n\023RESPONSECREATESKILL\020\207 \022\031\n\024R" +
      "ESPONSETRIGGERSKILL\020\211 \022\025\n\020REPORTCREATEBA" +
      "LL\020\214 *T\n\004SToM\022\030\n\023REQUESTENTERMISSION\020\211\'\022" +
      "\030\n\023RESPONSEOVERMISSION\020\222\'\022\030\n\023REQUESTALLO" +
      "CATEROOM\020\221N*:\n\004MToS\022\031\n\024RESPONSEENTERMISS" +
      "ION\020\361.\022\027\n\022REQUESTOVERMISSION\020\372.*\375\005\n\tErro" +
      "rCode\022\020\n\013UNKNOWERROR\020\220N\022\017\n\nPROTOERROR\020\221N" +
      "\022\013\n\006NOUSER\020\222N\022\020\n\013USEROFFLINE\020\223N\022\016\n\tITEME",
      "RROR\020\224N\022\021\n\014ITEMNUMERROR\020\225N\022\025\n\020ITEMCANTUS" +
      "EERROR\020\226N\022\023\n\016MISSIONIDERROR\020\227N\022\021\n\014TASKNU" +
      "MERROR\020\230N\022\021\n\014BAGFULLERROR\020\231N\022\020\n\013MESSAGEN" +
      "ONE\020\232N\022\016\n\tNOTFRIEND\020\233N\022\023\n\016FRIENDNUMERROR" +
      "\020\234N\022\017\n\nACTIONFULL\020\235N\022\017\n\nACTIONLACK\020\255N\022\021\n" +
      "\014MESSAGEGETED\020\236N\022\017\n\nEXCELERROR\020\237N\022\020\n\013NAM" +
      "ECREATED\020\240N\022\016\n\tNAMEERROR\020\241N\022\020\n\013LENGTHERR" +
      "OR\020\242N\022\014\n\007LVERROR\020\243N\022\022\n\rADDDSELFERROR\020\244N\022" +
      "\022\n\rFIGHTHALLFULL\020\246N\022\020\n\013ROOMNOTEXIT\020\247N\022\016\n" +
      "\tNOTINROOM\020\250N\022\016\n\tNOTINHALL\020\251N\022\021\n\014ROOMPWD",
      "ERROR\020\252N\022\r\n\010ROOMFULL\020\253N\022\032\n\025TOOLONGTIMEFO" +
      "RCONNECT\020\254N\022\022\n\014ACCOUNTERROR\020\240\234\001\022\020\n\nBANFO" +
      "RTALK\020\241\234\001\022\022\n\014ACCOUNTEXIST\020\260\352\001\022\030\n\022ACCOUNT" +
      "PWDNOTEXIST\020\261\352\001\022\026\n\020ACCOUNTNAMEERROR\020\262\352\001\022" +
      "\025\n\017ACCOUNTPWDERROR\020\263\352\001\022\030\n\022PAYRECEIPTNOTE" +
      "XIST\020\301\270\002\022\017\n\tPAYFAILED\020\302\270\002\022\016\n\010PAYEXIST\020\303\270" +
      "\002\022\027\n\021HAVEFIRSTRECHARGE\020\304\270\002\022\013\n\005ADMAX\020\321\206\003B" +
      "\013B\tProtoData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
