package module.boss;


import com.google.protobuf.InvalidProtocolBufferException;
import entities.BossEntity;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.ReportManager;
import org.springframework.context.annotation.Configuration;


import org.springframework.stereotype.Service;
import protocol.BossData;
import protocol.ProtoData;
import server.SuperServerHandler;
import table.worldbossreward.worldbossrewardTable;
import table.worldbossreward.worldbossrewardLine;
import java.text.SimpleDateFormat;
import java.util.*;


enum WorldBossState{
    Start,
    During,
    End,
}
@Service
@Configuration

public class BossService {
    private static BossService inst = null;

    public BossService() {
    }

    public static BossService getInstance() {
        if (inst == null) {
            inst = new BossService();
        }
        return inst;
    }
    // 攻击BOSS的玩家
    private HashMap<String, Float> worldBossBattlePlayer = new HashMap<String, Float>();

    // BOSS血量
    private final float worldBossBlood = 20000;
    private float worldBossCurrentBlood = 0;

    // 世界BOSS持续  5小时
    private final int worldBossDuringTime = 3600;
    private int worldBossCurrentDuringTime = 0;

    // BOSS状态
    private WorldBossState worldBossState = WorldBossState.End;

    // 世界BOSS开始时间，同一持续一个小时
    // 同一时间只能开启一个世界BOSS，另一个开启，前面一个要关闭。时间间隔要分开
    private String[] startWorldBossTime = {
           "12:00",
//            "18:00",
 //             "18:00",
//           "18:25",
//            "19:00",
//            "19:30",
//            "20:00",
//            "20:30",
//            "21:00",
//            "21:30",
//            "22:00",
//            "22:30",
//            "23:00",
//            "23:30",
    };
    private int startWorldBossTimeIndex = 0;

    // 每秒调用一次
    public void CheckWorldTime() {
//        System.out.println("时间调用，状态" + worldBossState);
        if (GetCurrentTime().equals("00:00")){
            startWorldBossTimeIndex = 0;
        }

        if (worldBossState == WorldBossState.Start){
            InitWorldBoss();
        }
        if (worldBossState == WorldBossState.During){
            DuringWorldBoss();
        }


        if (worldBossState == WorldBossState.End){
            for (int i = startWorldBossTimeIndex; i < startWorldBossTime.length; i++) {
                if (GetCurrentTime().equals(startWorldBossTime[i])){
                    startWorldBossTimeIndex = i + 1;
                    worldBossState = WorldBossState.Start;
                }
            }
        }
    }
    // 战斗前的初始化
    private void InitWorldBoss(){
        worldBossCurrentDuringTime = worldBossDuringTime;
        worldBossCurrentBlood = worldBossBlood;

        for (Map.Entry<ChannelHandlerContext, String> entry
                : SuperServerHandler.linkMap.entrySet()) {
            if (entry.getValue()!=null) {
                BossData.Responsebosstime.Builder builder = BossData.Responsebosstime.newBuilder();
                builder.setStart(true);
                ReportManager.reportInfo(entry.getValue(), ProtoData.SToC.RESPONSEbosstime_VALUE, builder.build().toByteArray());
            }
        }

        worldBossState = WorldBossState.During;
    }
    // 战斗期间的时间流逝判断
    private void DuringWorldBoss(){
        worldBossCurrentDuringTime--;
        if (worldBossCurrentDuringTime <= 0 ){
            EndWorldBoss(false);
        }else{
            // 发送BOSS血量
            for (Map.Entry<ChannelHandlerContext, String> entry
                    : SuperServerHandler.linkMap.entrySet()) {
                if (entry.getValue()!=null) {
                    BossData.Responsebossdown.Builder builder2 = BossData.Responsebossdown.newBuilder();
                    builder2.setBossblood(worldBossCurrentBlood / worldBossBlood);
                    ReportManager.reportInfo(entry.getValue(), ProtoData.SToC.RESPONSEbossdown_VALUE, builder2.build().toByteArray());
                }
            }
        }
    }
    private void EndWorldBoss(boolean isDead){
        //worldbossrewardLine line = worldbossrewardTable.getInstance().GetItem(80);
        for (Map.Entry<ChannelHandlerContext, String> entry
                : SuperServerHandler.linkMap.entrySet()) {
            if (entry.getValue()!=null) {
                BossData.Responsebosslive.Builder builder = BossData.Responsebosslive.newBuilder();
                builder.setDead(isDead);
                if (isDead){
                    builder.setPercentage((worldBossBattlePlayer.get(entry.getValue()) / worldBossBlood)*100);
                    builder.setHarmValue(worldBossBattlePlayer.get(entry.getValue()));
                    builder.setBossblood(worldBossBlood);
                }else{
                    builder.setPercentage(0);
                }
                ReportManager.reportInfo(entry.getValue(), ProtoData.SToC.RESPONSEbosslive_VALUE, builder.build().toByteArray());

                BossData.Responsebosstime.Builder builder2 = BossData.Responsebosstime.newBuilder();
                builder2.setStart(false);
                ReportManager.reportInfo(entry.getValue(), ProtoData.SToC.RESPONSEbosstime_VALUE, builder2.build().toByteArray());
            }
        }
        worldBossBattlePlayer.clear();
        worldBossState = WorldBossState.End;
    }

    public byte[] RequestWorldBossState(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        BossData.Responsebosstime.Builder builder = BossData.Responsebosstime.newBuilder();
        if (worldBossState == WorldBossState.During){
            builder.setStart(true);
        }else {
            builder.setStart(false);
        }
        return builder.build().toByteArray();
    }

    public byte[] RequestWorldBossInjureNumber(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        if (worldBossState != WorldBossState.During){
            return null;
        }
        float curBlood;
        BossData.Responsebossdown parse = BossData.Responsebossdown.parseFrom(bytes);
        curBlood = CountWorldBossBlood(parse.getBossblood());

        if (worldBossBattlePlayer.containsKey(uid)){
            worldBossBattlePlayer.put(uid, worldBossBattlePlayer.get(uid) + curBlood);
        }else{
            worldBossBattlePlayer.put(uid, curBlood);
        }

        if (worldBossCurrentBlood <= 0){
            EndWorldBoss(true);
        }

        BossData.Responsebossdown.Builder builder = BossData.Responsebossdown.newBuilder();
        builder.setBossblood(worldBossCurrentBlood / worldBossBlood);

        return builder.build().toByteArray();
    }

    // 玩家攻击后的血量
    private float CountWorldBossBlood(float playerAttackBlood){
        float curBlood;
        if(worldBossCurrentBlood - playerAttackBlood > 0.0){
            curBlood = playerAttackBlood;
            worldBossCurrentBlood -= playerAttackBlood;
        }else{
            curBlood = worldBossCurrentBlood;
            worldBossCurrentBlood = 0;
        }
        return curBlood;
    }

    // 获取时间
    private String GetCurrentTime(){
        Date date1 = new Date();
        SimpleDateFormat format1 = new SimpleDateFormat("HH:mm");
        String time1 = format1.format(date1);
        return  time1;
    }













//    @PostConstruct

//    public void RequestEBoss(byte[] bytes, String uid) {
////        BossData.RequestbossTiem requestbossTiem = null;
//        BossData.Responsebosstime.Builder builder = BossData.Responsebosstime.newBuilder();
//        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEbosstime_VALUE, builder.build().toByteArray());
//    }

    BossEntity bossEntity = new BossEntity();

    public void bossxl() {
        bossEntity.setBlood(2000000);
        if (bossEntity.getBlood() < 0) {
            bossEntity.setBlood(0);
        }
    }

    public byte[] RequestBossAll(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        BossData.Requestbossdear request = null;
        BossData.Responsebossdown.Builder builder = BossData.Responsebossdown.newBuilder();
        if (bossEntity.getBlood() != 0) {
            BossData.Responsebossdown parse = BossData.Responsebossdown.parseFrom(bytes);
            BossEntity entity = null;
            try {
                entity = BossDao.getInstance().queryUid(uid);
            } catch (Exception e) {
            }
            if (entity == null) {
                BossEntity newEntity = new BossEntity();
                newEntity.setUid(uid);
                float blood = this.bossEntity.getBlood();//boss当前血量
                float bossblood = parse.getBossblood();//boss伤害
                newEntity.setSH(bossblood);
                newEntity.setBlood(blood - bossblood);
                float blood1 = newEntity.getBlood();
                if (blood1 < 0) {
                    blood1 = 0;
                }
                bossEntity.setBlood(blood1);
                newEntity.setBlood(blood1);
                MySql.insert(newEntity);
                builder.setBossblood(blood1);

            } else {
                float blood = bossEntity.getBlood();//boss剩余血量
                float bossblood = parse.getBossblood();//打boss伤害
                entity.setBlood(blood - bossblood);//剩余血量
                float blood1 = entity.getBlood();//获取剩余血量
                if (blood1 < 0) {
                    blood1 = 0;
                }
                float SH = entity.getSH();
                float d = SH + bossblood;
                entity.setBlood(blood1);
                bossEntity.setBlood(blood1);
                entity.setSH(d);
                MySql.update(entity);
                builder.setBossblood(blood1);

            }
        } else {
            bossxl();
            BossData.Responsebossdown parse = BossData.Responsebossdown.parseFrom(bytes);
            bossEntity.setUid(uid);
            float blood = bossEntity.getBlood();
            float bossblood = parse.getBossblood();
            bossEntity.setBlood(blood - bossblood);
            float blood1 = bossEntity.getBlood();
            float SH = bossEntity.getSH();
            float d = SH + bossblood;
            bossEntity.setSH(d);
            MySql.insert(bossEntity);
            bossEntity.setBlood(blood1);
            builder.setBossblood(blood1);
        }

//        if (request==null){
//            BossData.Requestbossdear bossdear = BossData.Requestbossdear.parseFrom(bytes);
//            float blood = bossEntity.getBlood();
//            float tage = bossdear.getTage();
//         bossEntity.setBlood(blood-tage);
//            float boss =2000000;
//            float BFB =(tage/boss);
//            builder.setPercentage(BFB);
//           if (blood==0){
//               builder.setDead(true);
//           }else {
//               builder.setDead(false);
//           }
//            MySql.insert(bossEntity);
//        }
        return builder.build().toByteArray();
    }

    private boolean executed = false;

    public void ResopnseBossJL() {
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String time = format.format(date);
        if (time.compareTo("19:00") >= 0 && time.compareTo("21:05") <= 0) {
            executed = false;
        }
        if (!executed) {
            Date date1 = new Date();
            SimpleDateFormat format1 = new SimpleDateFormat("HH:mm");
            String time1 = format1.format(date1);
            float blood = bossEntity.getBlood();
            String uid = bossEntity.getUid();
            if (blood < 0) {
                blood = 0;
            }
            if (blood == 0 && uid != null || time1.compareTo("18:29") == 0 || time1.compareTo("21:00") == 0) {
                System.out.println("boss123");
                for (Map.Entry<ChannelHandlerContext, String> entry : SuperServerHandler.linkMap.entrySet()) {
                    if (entry.getValue() != null) {
                        BossEntity bossEntity = BossDao.getInstance().queryUid(entry.getValue());
                        float sh = bossEntity.getSH();
                        float result = sh / 2000000;
                        float percentage = result * 100;
                        BossData.Responsebosslive.Builder builder = BossData.Responsebosslive.newBuilder();
                        builder.setDead(true);
                        builder.setPercentage(percentage);
                        ReportManager.reportInfo(entry.getValue(), ProtoData.SToC.RESPONSEbosslive_VALUE, builder.build().toByteArray());
                        break;
                    }
                }
                executed = true;
                bossEntity.setUid(null);
                BossDao.getInstance().BossDelete();
            }
        }
    }

    public byte[] RequestBossStart1(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        BossData.RequestbossTiem request = null;
        BossData.Responsebosstime.Builder builder = BossData.Responsebosstime.newBuilder();
        Date date = new Date();
        SimpleDateFormat format = new SimpleDateFormat("HH:mm");
        String time = format.format(date);
        if (time.compareTo("12:00") >= 0 && time.compareTo("13:00") <= 0 || time.compareTo("18:00") >= 0 && time.compareTo("19:00") <= 0) {
            builder.setStart(true);
        } else {
            builder.setStart(false);
        }

        return builder.build().toByteArray();
    }


}


