<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD//EN"
        "http://www.hibernate.org/dtd/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>

        <property name="hibernate.connection.isolation">2</property>
        <property name="hibernate.connection.provider_class">org.hibernate.c3p0.internal.C3P0ConnectionProvider</property>
        <!-- 连接池中最大连接数 -->
        <property name="hibernate.c3p0.max_size">60</property>
        <!-- 连接池中最小连接数 -->
        <property name="hibernate.c3p0.min_size">5</property>
        <!--timeout这个表示连接池中的连接对象在多长时间没有使用过后，就应该被销毁-->
        <property name="hibernate.c3p0.timeout">120</property>
        <!-- 这个表示连接池检测线程多长时间检测一次池内的所有链接对象是否超时 -->
        <property name="hibernate.c3p0.idle_test_period">3000</property>
        <!--当连接池中的连接耗尽的时候c3p0一次同时获取的连接数。Default: 3 -->
        <property name="hibernate.c3p0.acquire_increment">2</property>

        <!--  Mysql配置  -->
        <property name="connection.url">*******************************************************************************************************************</property>
        <property name="connection.driver_class">com.mysql.jdbc.Driver</property>
        <!--  mysql账户名  -->
        <property name="connection.username">superStar</property>
        <!--  mysql密码  -->
        <property name="connection.password">Ss@youj.c0m</property>

        <property name="connection.autoReconnect">true</property>

        <!--  数据库方言  -->
        <property name="dialect">org.hibernate.dialect.MySQLDialect</property>

        <!--  显示sql语句  -->
        <property name="show_sql">false</property>

        <!--  格式化sql语句  -->
        <!--<property name="format_sql">true</property>-->

        <!--  根据需要创建数据库-->
        <property name="hbm2ddl.create">update</property>
        <mapping class="entities.UserEntity"/>
        <mapping class="entities.NoticeEntity"/>
        <mapping class="entities.MapEntity"/>
        <mapping class="entities.EquipEntity"/>
        <mapping class="entities.ConductTaskEntity"/>
        <mapping class="entities.CompleteTaskEntity"/>
        <mapping class="entities.RoleEntity"/>
        <mapping class="entities.PVPBaseDataEntity"/>
        <mapping class="entities.PVPPetsEntity"/>
        <mapping class="entities.RankEntity"/>
        <mapping class="entities.ItemEntity"/>
        <mapping class="entities.DressEntity"/>
        <mapping class="entities.TaskEntity"/>
        <mapping class="entities.MissionEntity"/>
        <mapping class="entities.RelativeshipEntity"/>
        <mapping class="entities.MessageEntity"/>
        <mapping class="entities.UtilityEntity"/>
        <mapping class="entities.PartEntity"/>
        <mapping class="entities.AccusationEntity"/>
        <mapping class="entities.PresentEntity"/>
        <mapping class="entities.PayorderEntity"/>
        <mapping class="entities.HeadBallEntity"/>
        <mapping class="entities.AdLogEntity"/>
        <mapping class="entities.ClickNumEntity"/>
        <mapping class="entities.SourceMachineEntity"/>
        <mapping class="entities.UserdataEntity"/>
        <mapping class="entities.ShoporderEntity"/>
        <mapping class="entities.TaskRecordEntity"/>
        <mapping class="entities.ConsumeRecordEntity"/>
        <mapping class="entities.GoldRecordEntity"/>
        <mapping class="entities.ProductrecordEntity"/>
        <mapping class="entities.GameRecordEntity"/>
        <mapping class="entities.ActivitiesEntity"/>
        <mapping class="entities.TicketmarketrecordEntity"/>
        <mapping class="entities.GameBattleEntity"/>
        <mapping class="entities.WaitItemEntity"/>
        <mapping class="entities.WaitItemInfoEntity"/>
        <mapping class="entities.RoomEntity"/>
        <mapping class="entities.FurnitureShopEntity"/>
        <mapping class="entities.MailEntity"/>
        <mapping class="entities.PetEntity"/>
        <mapping class="entities.EventEntity"/>
        <mapping class="entities.RoleAdditionalEntity"/>
        <mapping class="entities.FriendApplicationEntity"/>
        <mapping class="entities.InformationEntity"/>
        <mapping class="entities.DailyTaskEntity"/>
        <mapping class="entities.CompleteDailyTaskEntity"/>
        <mapping class="entities.AchievementEntity"/>
        <mapping class="entities.CompleteAchievementEntity"/>
        <mapping class="entities.LevelEntity"/>
        <mapping class="entities.plotEntity"/>
        <mapping class="entities.EquipAEntity"/>
        <mapping class="entities.BossEntity"/>
        <mapping class="entities.PayDiamondEntity"/>
        <mapping class="entities.RechargeRebateRewardEntity"/>
        <mapping class="entities.LimitedTimeRewardEntity"/>
        <mapping class="entities.LimitedTimeRewardTaskEntity"/>
        <mapping class="entities.LimitedTimeRewardCompleteTaskEntity"/>
        <mapping class="entities.PetEggEntity"/>
        <mapping class="entities.RoleOfflineTimeEntity"/>
        <mapping class="entities.TemItemEntity"/>
        <!-- DB schema will be updated if needed -->
        <!-- <property name="hbm2ddl.auto">update</property> -->
    </session-factory>
</hibernate-configuration>