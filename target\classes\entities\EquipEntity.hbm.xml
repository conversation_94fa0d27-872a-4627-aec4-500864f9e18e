<?xml version='1.0' encoding='utf-8' ?>
<!DOCTYPE hibernate-mapping PUBLIC
        "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd">
<hibernate-mapping>
    <class name="entities.EquipEntity" table="equip" schema="" catalog="super_star_fruit">
        <id name="id" column="id"/>
        <property name="friendId" column="friendId"/>
        <property name="equipEid" column="equipEid"/>
        <property name="equipType" column="equipType"/>
        <property name="name" column="name"/>
        <property name="sort" column="sort"/>
        <property name="currentLevel" column="currentLevel"/>
        <property name="currentExp" column="currentExp"/>
        <property name="hp" column="hp"/>
        <property name="ad" column="ad"/>
        <property name="ap" column="ap"/>
        <property name="arm" column="arm"/>
        <property name="mdf" column="mdf"/>
        <property name="speed" column="speed"/>
        <property name="hpFactor" column="hpFactor"/>
        <property name="adFactor" column="adFactor"/>
        <property name="apFactor" column="apFactor"/>
        <property name="armFactor" column="armFactor"/>
        <property name="mdfFactor" column="mdfFactor"/>
        <property name="speedFactor" column="speedFactor"/>
    </class>
</hibernate-mapping>