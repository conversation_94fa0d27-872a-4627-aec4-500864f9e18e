package entities;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "role_offline_time", schema = "super_star_fruit", catalog = "super_star_fruit")
public class RoleOfflineTimeEntity {
    private int id;
    private String roleUid;
    private Date offlineTime;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }
    @Basic
    @Column(name = "roleUid")
    public String getRoleUid() {
        return roleUid;
    }

    public void setRoleUid(String roleUid) {
        this.roleUid = roleUid;
    }
    @Basic
    @Column(name = "offlineTime")
    public Date getOfflineTime() {
        return offlineTime;
    }

    public void setOfflineTime(Date offlineTime) {
        this.offlineTime = offlineTime;
    }
}
