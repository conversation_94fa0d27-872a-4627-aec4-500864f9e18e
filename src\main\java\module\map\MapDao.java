package module.map;

import entities.MapEntity;
import manager.MySql;

import java.util.List;

public class MapDao {
    private static MapDao inst = null;

    public static MapDao getInstance() {
        if (inst == null) {
            inst = new MapDao();
        }
        return inst;
    }
    public List getplayerMap(String uid) {
        StringBuilder sql = new StringBuilder("from MapEntity where friendId='").append(uid).append("'");
        List<Object> list = MySql.queryForList(sql.toString());
        return list;
    }
}
