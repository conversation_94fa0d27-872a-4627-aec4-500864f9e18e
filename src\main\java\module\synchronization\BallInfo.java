package module.synchronization;

import model.PointInfo;
import protocol.MissionData;

/**
 * Created by nara on 2018/6/11.
 */
public class BallInfo {
    private int index;
    private int ballId;//0基地
    private int type;//0中间基地 1左边 2右边
    private int integral;
    private float speed;
    private float direction;
    private PointInfo point;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public int getIntegral() {
        return integral;
    }

    public void setIntegral(int integral) {
        this.integral = integral;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public PointInfo getPoint() {
        return point;
    }

    public void setPoint(PointInfo point) {
        this.point = point;
    }

    public float getDirection() {
        return direction;
    }

    public void setDirection(float direction) {
        this.direction = direction;
    }

    public float getSpeed() {
        return speed;
    }

    public void setSpeed(float speed) {
        this.speed = speed;
    }

    public int getBallId() {
        return ballId;
    }

    public void setBallId(int ballId) {
        this.ballId = ballId;
    }

    public static BallInfo getBaseBall(){
        BallInfo ballInfo = new BallInfo();
        ballInfo.setType(0);
        ballInfo.setBallId(0);
        ballInfo.setIntegral(0);
        PointInfo pointInfo = new PointInfo();
        pointInfo.setX(0);
        pointInfo.setY(0);
        ballInfo.setPoint(pointInfo);
        return ballInfo;
    }

    public static MissionData.BigBall setBigBall(BallInfo ballInfo){
        MissionData.BigBall.Builder ballBu = MissionData.BigBall.newBuilder();
        MissionData.Buff.Builder buffBu = MissionData.Buff.newBuilder();
        MissionData.Body.Builder bodyBu = MissionData.Body.newBuilder();
        MissionData.Point.Builder pointBu = MissionData.Point.newBuilder();
        pointBu.setX(ballInfo.getPoint().getX());
        pointBu.setY(ballInfo.getPoint().getY());
        bodyBu.setP(pointBu);
        bodyBu.setId(ballInfo.getBallId());
        buffBu.setBall(bodyBu);
        buffBu.setIsSpeed(false);
        ballBu.setBall(buffBu);
        ballBu.setIntegral(ballInfo.getIntegral());
        ballBu.setSkillId(0);
        return ballBu.build();
    }
}
