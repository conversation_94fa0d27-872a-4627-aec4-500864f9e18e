package manager;

import io.netty.channel.ChannelHandlerContext;
import module.synchronization.RoomCtxInfo;
import module.synchronization.RoomManager;
import server.SuperProtocol;
import server.SuperServerHandler;

import java.util.Map;

/**
 * Created by nara on 2018/6/3.
 */
public class BoardcastHandler implements Runnable {
    private int server;
    private int type;
    private int msgId;
    private byte[] reportMsg;

    public void setServer(int server) {
        this.server = server;
    }

    public void setType(int type) {
        this.type = type;
    }

    public void setReportMsg(byte[] reportMsg) {
        this.reportMsg = reportMsg;
    }

    public void setMsgId(int msgId) {
        this.msgId = msgId;
    }

    public void run() {
        if (reportMsg == null){
            return;
        }
        if (type == 1 || type == 2){//全体多人战斗初级/高级大厅玩家
            for (Map.Entry<String,RoomCtxInfo>entry: RoomManager.getRoomRoleMap().entrySet()){
                RoomCtxInfo roomCtxInfo = entry.getValue();
                if (roomCtxInfo.getHall() == type){
                    try {
                        ChannelHandlerContext ctx = roomCtxInfo.getCtx();
                        SuperProtocol response = new SuperProtocol(msgId,reportMsg.length, reportMsg);
                        ctx.writeAndFlush(response);
                    }catch (Exception e){
                        e.printStackTrace();
                    }
                }
            }
        }else if (type == 100){//全体在线玩家
            for (Map.Entry<String, ChannelHandlerContext>entry:SuperServerHandler.serverRoleMap.get(server).entrySet()){
                ChannelHandlerContext ctx = entry.getValue();
                SuperProtocol response = new SuperProtocol(msgId,reportMsg.length, reportMsg);
                ctx.writeAndFlush(response);
            }
        }
    }
}