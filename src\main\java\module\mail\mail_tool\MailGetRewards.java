package module.mail.mail_tool;

import entities.PetEntity;
import manager.ReportManager;
import module.equipA.EquipAService;
import module.item.ItemDao;
import module.pet.PetCreate;
import module.pet.PetUtils;
import protocol.*;

import java.util.List;

public class MailGetRewards {
    private static MailGetRewards inst = null;
    private MailGetRewards() {
    }
    public static MailGetRewards getInstance() {
        if (inst == null) {
            inst = new MailGetRewards();
        }
        return inst;
    }

    public void GetRewards(String uid, MailData.Attachment attacment) {
        switch (attacment.getType()){
            case 1:
                GetItems(uid, attacment);
                break;
            case 2:
                GetEquips(uid, attacment);
                break;
            case 3:
                GetPet(uid, attacment);
                break;
        }
    }



    private void GetItems(String uid, MailData.Attachment attacment){
        ItemDao itemDao = ItemDao.getInstance();
        ItemData.ReportItem.Builder builder = ItemData.ReportItem.newBuilder();
        List<ItemData.Item> itemList = attacment.getItemList();
        //领取邮件的奖励
//        responseOperateMailBu.addAllItem(itemList);
        for (ItemData.Item item : itemList) {
            double addNums = item.getNum();
            int itemId = item.getId();
            double nowValue = itemDao.updateItemInfo(uid, itemId, (long) addNums);
            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setNum(nowValue);
            itemBu.setId(itemId);
            builder.addItem(itemBu);
        }
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTITEM_VALUE, builder.build().toByteArray());
    }

    private void GetEquips(String uid, MailData.Attachment attacment) {
        for (EquipAData.EquipA equip :
                attacment.getEquipList()) {
            EquipAService.getInstance().GetEquip(uid, equip);
        }
    }

    private void GetPet(String uid, MailData.Attachment attacment) {
        for (MailData.DefaultPet defaultPet :
                attacment.getDefaultPetList()) {
            PetCreate.getInstance().DefaultCreatePet(uid, defaultPet.getPetId(),
                    defaultPet.getIsEgg() == 0);
        }
    }


}
