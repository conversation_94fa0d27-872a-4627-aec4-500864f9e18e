package module.friend;

import Json.CommonJson;
import Json.MailJson;
import common.SuperConfig;
import entities.*;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import model.*;
import module.callback.CallBack;
import module.callback.CallBackOrder;
import module.item.IItem;
import module.item.ItemDao;
import module.login.ILogin;
import module.login.LoginDao;
import module.mail.MailDao;
import module.mail.NoticeDao;
import module.mail.mail_tool.MailAttachment;
import module.mission.IMission;
import module.mission.MissionDao;
import module.role.RoleDao;
import module.synchronization.RoomInfo;
import module.synchronization.RoomManager;
import module.synchronization.RoomRoleInfo;
import module.task.ITask;
import module.task.TaskDao;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.*;
import redis.clients.jedis.Pipeline;
import server.SuperServerHandler;
import utils.MyUtils;
import utils.Root;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by nara on 2018/2/1.
 */
public class FriendDao implements IFriend {
    private static Logger log = LoggerFactory.getLogger(FriendDao.class);
    private static FriendDao inst = null;

    public static FriendDao getInstance() {
        if (inst == null) {
            inst = new FriendDao();
        }
        return inst;
    }

    public FriendInfo getFriendInfoById(String uid, int friendId) {
        Redis jedis = Redis.getInstance();
        String friendStr = jedis.hget("rolefriends:" + uid, friendId + "");
        if (friendStr == null) {
            return null;
        }
        FriendInfo friendInfo = (FriendInfo) MyUtils.jsonToBean(friendStr, FriendInfo.class);
        boolean needUpdate = false;
        if (friendInfo.getDailyScript() != 0) {
            boolean bo = MyUtils.isOneDay(Long.parseLong(friendInfo.getScriptStamp()), TimerHandler.nowTimeStamp);
            if (bo == false) {
                needUpdate = true;
                friendInfo.setDailyScript(0);
                friendInfo.setScriptStamp("0");
                StringBuffer sql = new StringBuffer("delete from PresentEntity where uid='").append(uid).append("' and friend = '").append(friendInfo.getUid()).append("' and type = 1");
                MySql.updateSomes(sql.toString());
            }
        }
        if (friendInfo.getDailyItem() != 0) {
            boolean bo = MyUtils.isOneDay(Long.parseLong(friendInfo.getItemStamp()), TimerHandler.nowTimeStamp);
            if (bo == false) {
                needUpdate = true;
                friendInfo.setDailyItem(0);
                friendInfo.setItemStamp("0");
                StringBuffer sql = new StringBuffer("delete from PresentEntity where uid='").append(uid).append("' and friend = '").append(friendInfo.getUid()).append("' and type = 2");
                MySql.updateSomes(sql.toString());
            }
        }
        if (needUpdate == true) {
            jedis.hset("rolefriends:" + uid, friendInfo.getId() + "", MyUtils.objectToJson(friendInfo));
        }
        return friendInfo;
    }

    private boolean judgeGMOrder(String uid, String string) {
        boolean isChat = true;
        String[] strings = string.split("## mission ");
        if (strings.length == 2) {
            Redis jedis = Redis.getInstance();
            String tmp = strings[1];
            int max = 0;
            if (tmp.equals("max")) {
                Iterator<String> iterator = jedis.keys("missionconfig*").iterator();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    int id = Integer.parseInt(key.split(":")[1]);
                    if (id > max) {
                        max = id;
                    }
                }
            } else {
                if (StringUtils.isNumeric(tmp) == false) {
                    return true;
                }
                max = Integer.parseInt(tmp);
            }
            jedis.hset("role:" + uid, "missionid", max + "");

            StringBuffer hql = new StringBuffer("update RoleEntity set missionid = ").append(max).append(",missionstamp = '").append(System.currentTimeMillis())
                    .append("' where uid = '").append(uid).append("'");
            MySql.updateSomes(hql.toString());

            isChat = false;
        }

        return isChat;
    }

    public FriendData.ResponseSendChat.Builder sendChat(String uid, FriendData.Chat chat, int friendId) {
        FriendData.ResponseSendChat.Builder result = FriendData.ResponseSendChat.newBuilder();
        result.setErrorId(0);
        ILogin iLogin = LoginDao.getInstance();
        // RoleEntity roleEntity = iLogin.getRoleEntityFromRedis(uid);
        StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
        FriendData.Player.Builder builder = FriendData.Player.newBuilder();
//        builder.setStation(1);
        builder.setId(roleEntity.getId());
        builder.setHead(roleEntity.getHead());
        builder.setLv(roleEntity.getLv());
        builder.setName(roleEntity.getName());
        if (chat.getContent().length() > 5 && "##".equals(chat.getContent().substring(0, 2))) {
            Root.root(chat.getContent(), uid);
            return null;
        }
        if (chat.getContent().length() > 5 && "##\\t".equals(chat.getContent().substring(0, 3))) {
            Root.GmOrder(chat.getContent().substring(3, chat.getContent().length()), uid);
        }

        int type = chat.getType();
        if (type == FriendData.ChatType.WORLD_VALUE) {//世界
            boolean isChat = true;
//            if (SuperConfig.getGAMEMODE() == SuperConfig.GAMEMODE_PLOTTER){
            isChat = judgeGMOrder(uid, chat.getContent());
//            }
            if (isChat == true) {
                reportChat(uid, chat, builder.build(), null, 0);
            }
//好友
        } else if (type == FriendData.ChatType.FRIEND_VALUE) {
            FriendInfo friendInfo = getFriendInfoById(uid, friendId);
            if (friendInfo == null) {
                result.setErrorId(ProtoData.ErrorCode.NOTFRIEND_VALUE);
                return result;
            }
            String friendUid = friendInfo.getUid();
            reportChat(uid, chat, builder.build(), friendUid, 0);
            reportChat(uid, chat, builder.build(), uid, friendId);//自己
        } else if (type == FriendData.ChatType.SUONA_TALK_VALUE) {//喇叭说话
            if (chat.getContent().length() > 30) {
                log.error(uid + ":[sendChat] error 1 >>>length:" + chat.getContent().length());
                result.setErrorId(ProtoData.ErrorCode.LENGTHERROR_VALUE);
                return result;
            }
            IItem iItem = ItemDao.getInstance();
            double itemNum = iItem.getItemNum(uid, 16);
            if (itemNum < 1) {
                log.error(uid + ":[sendChat] error 2 >>>itemNum:" + itemNum);
                result.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return result;
            }
            Redis jedis = Redis.getInstance();
            int lv = Integer.parseInt(jedis.hget("role:" + uid, "lv"));
            if (lv < 10) {
                log.error(uid + ":[sendChat] error 3 >>>lv:" + lv);
                result.setErrorId(ProtoData.ErrorCode.LVERROR_VALUE);
                return result;
            }
            double totalNum = iItem.updateItemInfo(uid, 16, -1);
            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setId(16);
            itemBu.setNum(totalNum);
            result.addItem(itemBu);
            reportChat(uid, chat, builder.build(), null, 0);
        } else if (type == FriendData.ChatType.STRANGER_VALUE) {//陌生人
//            String friendUid = getRoleUidById(friendId);
//            reportChat(uid,chat, builder.build(),friendUid,0);
//            reportChat(uid,chat, builder.build(),uid,friendId);//自己

            List<Object> list = new ArrayList<Object>();
            list.add(0, uid);
            list.add(1, friendId);
            list.add(2, builder.build());
            list.add(3, chat);
            CallBack callBack = new FriendCallBack(CallBackOrder.SENDCHATTOSTRANGER);
            callBack.addParameterList(list);
            getRoleUidById(friendId, callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
        } else if (type == FriendData.ChatType.ROOM_VALUE) {
            int nowRoomId = RoomManager.getRoleRoomId(uid);
            if (nowRoomId <= 0) {
                log.error(uid + ":[sendChat] error 4>>>nowRoomId:" + nowRoomId);
                result.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                return result;
            }
            int hall = RoomManager.getRoleHall(uid);
            int roomType = RoomManager.getRoleRoomType(uid);
            RoomInfo roomInfo = RoomManager.getRoomInfoById(roomType, hall, nowRoomId);
            if (roomInfo == null) {
                RoomManager.leaveRoom(uid, nowRoomId);
                log.error(uid + ":[sendChat] error 5>>>nowRoomId:" + nowRoomId);
                result.setErrorId(ProtoData.ErrorCode.ROOMNOTEXIT_VALUE);
                return result;
            }
            FriendData.ReportChat.Builder reportChatBu = FriendData.ReportChat.newBuilder();
            reportChatBu.setChat(chat);
            reportChatBu.setPlayer(builder);
            reportChatBu.setTimeStamp(TimerHandler.nowTimeStamp + "");
            for (int i = 0; i < roomInfo.getRoomRoleList().size(); i++) {
                RoomRoleInfo roomRoleInfo = roomInfo.getRoomRoleList().get(i);
                ReportManager.reportInfo(roomRoleInfo.getUid(), ProtoData.SToC.REPORTCHAT_VALUE, reportChatBu.build().toByteArray());
            }
        } else if (type == FriendData.ChatType.CURRENT_VALUE) {
            int station = iLogin.getStationFromUid(uid);
            if (station != -1) {
                FriendData.ReportChat.Builder reportChatBu = FriendData.ReportChat.newBuilder();
                reportChatBu.setChat(chat);
                reportChatBu.setPlayer(builder);
                reportChatBu.setTimeStamp(TimerHandler.nowTimeStamp + "");
                List<StationInfo> list = SuperServerHandler.stationMap.get(station);
                for (int i = 0; i < list.size(); i++) {
                    StationInfo stationInfo = list.get(i);
                    ReportManager.reportInfo(stationInfo.getCtx(), ProtoData.SToC.REPORTCHAT_VALUE, reportChatBu.build().toByteArray());
                }
            }
        }
        return result;
    }



    public void reportChat(String uid, FriendData.Chat chat, FriendData.Player player, String sendUid, int friendId) {
        FriendData.ReportChat.Builder builder = FriendData.ReportChat.newBuilder();
        long timeStamp = System.currentTimeMillis();
        builder.setChat(chat);
        builder.setPlayer(player);
        builder.setTimeStamp(timeStamp + "");
        if ((chat.getType() == FriendData.ChatType.FRIEND_VALUE || chat.getType() == FriendData.ChatType.STRANGER_VALUE) && friendId != 0) {
            builder.setFriendId(friendId);
        }
        if (sendUid == null) {
            int server = Redis.getRoleServer(uid);
            ReportManager.boardcastInfo(server, 100, ProtoData.SToC.REPORTCHAT_VALUE, builder.build().toByteArray());
        } else {
            if (SuperServerHandler.getCtxFromUid(sendUid) == null) {//不在线
                ///       /// System.err.println("好友不在线");
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("id", player.getId());
                jsonObject.put("content", chat.getContent());
                String mid = MyUtils.setRandom();
                addNewMessage(sendUid, SuperConfig.OFFLINEMESSAGE, jsonObject.toString(), mid, timeStamp);
            } else {
                ReportManager.reportInfo(sendUid, ProtoData.SToC.REPORTCHAT_VALUE, builder.build().toByteArray());
            }
        }
    }

    public void getRoleUidById(int id, CallBack callBack, int type) {
        StringBuffer sql = new StringBuffer("select uid from RoleEntity where id=").append(id);
        MySql.queryInSql(sql.toString(), callBack, type);
    }

//    public String getRoleUidById(int id){
//        StringBuffer sql = new StringBuffer("select uid from RoleEntity where id=").append(id);
//        Object uid = MySql.queryForOne(sql.toString());
//        return uid == null ? null : uid.toString();
//    }

    //新增消息插入redis
    public MessageInfo addNewMessage(String uid, int type, String content, String mid, long timeStamp) {
        Redis jedis = Redis.getInstance();
        Map<String, String> messageMap = jedis.hgetAll("rolemessages:" + uid);
        for (Map.Entry<String, String> entry : messageMap.entrySet()) {
            MessageInfo messageInfo = (MessageInfo) MyUtils.jsonToBean(entry.getValue(), MessageInfo.class);
            if (messageInfo.getType() == 1 && messageInfo.getContent().equals(content)) {
                return null;

            }
        }

        MessageEntity messageEntity = new MessageEntity();
        messageEntity.setType(type);
        messageEntity.setMid(mid);
        messageEntity.setContent(content);
        messageEntity.setUid(uid);
        messageEntity.setTimestamp(timeStamp + "");
        messageEntity.setStatus(0);
        MySql.insert(messageEntity);

        MessageInfo messageInfo = new MessageInfo();
        messageInfo.setId(messageEntity.getId());
        messageInfo.setType(messageEntity.getType());
        messageInfo.setMid(messageEntity.getMid());
        messageInfo.setContent(messageEntity.getContent());
        messageInfo.setTimeStamp(messageEntity.getTimestamp());
        messageInfo.setStatus(messageEntity.getStatus());
        String string = MyUtils.objectToJson(messageInfo);
        jedis.hset("rolemessages:" + uid, messageInfo.getMid(), string);
        log.info("[addNewMessage]>>>uid:" + uid + ">>>" + MyUtils.objectToJson(messageInfo));
        return messageInfo;
    }

    public FriendData.ResponseOperateFriend.Builder operateFriend(String uid, int type, int id) {
        FriendData.ResponseOperateFriend.Builder builder = FriendData.ResponseOperateFriend.newBuilder();
        builder.setId(id);
        builder.setType(type);
        builder.setErrorId(0);
        Redis jedis = Redis.getInstance();
        FriendInfo friendInfo = getFriendInfoById(uid, id);
        if (type == 1) {//增加好友
            if (friendInfo != null) {
                log.error(uid + ":[operateFriend] error 1");
                builder.setErrorId(ProtoData.ErrorCode.NOTFRIEND_VALUE);
                return builder;
            }
            int friendSize = jedis.hgetAll("rolefriends:" + uid).size();
            if (friendSize >= 100) {
                log.error(uid + ":[operateFriend] error 2 >>>friendSize:" + friendSize);
                builder.setErrorId(ProtoData.ErrorCode.FRIENDNUMERROR_VALUE);
                return builder;
            }
            CallBack callBack = new FriendCallBack(CallBackOrder.ADDFRIENDBACK);
            List<Object> list = new ArrayList<Object>();
            list.add(0, uid);
            list.add(1, type);
            list.add(2, id);
            callBack.addParameterList(list);
            getRoleUidById(id, callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
            return null;
//            String friendUid = getRoleUidById(id);
//            if (friendUid == null){
//                log.error(uid+":[operateFriend] error 3");
//                builder.setErrorId(ProtoData.ErrorCode.NOUSER_VALUE);
//                return builder;
//            }
//            if (friendUid.equals(uid)){
//                log.error(uid+":[operateFriend] error 4 >>> can not be self");
//                builder.setErrorId(ProtoData.ErrorCode.ADDDSELFERROR_VALUE);
//                return builder;
//            }
//            String roleName = jedis.hget("role:"+uid,"name");
//            String content = uid+"|"+roleName;
//            String mid = MyUtils.setRandom();
//            boolean bo = addNewMessage(friendUid,1,content,mid,System.currentTimeMillis());
//            if (bo == true){
//                reportMessage(mid, 1, roleName, friendUid);
//            }
        } else if (type == 2) {//删除好友
            if (friendInfo == null) {
                log.error(uid + ":[operateFriend] error 5");
                builder.setErrorId(ProtoData.ErrorCode.NOTFRIEND_VALUE);
                return builder;
            }
            String roleId = jedis.hget("role:" + uid, "id");
            jedis.hdel("rolefriends:" + uid, id + "");
            jedis.hdel("rolefriends:" + friendInfo.getUid(), roleId);
            StringBuffer hql = new StringBuffer("delete from RelativeshipEntity ")
                    .append(" where roleuid1 = '").append(uid).append("' and roleuid2 = '").append(friendInfo.getUid()).append("'");
            MySql.updateSomes(hql.toString());
            StringBuffer hql2 = new StringBuffer("delete from RelativeshipEntity ")
                    .append(" where roleuid2 = '").append(uid).append("' and roleuid1 = '").append(friendInfo.getUid()).append("'");
            MySql.updateSomes(hql2.toString());
            StringBuffer hql3 = new StringBuffer("delete from PresentEntity ")
                    .append(" where type = 1 and uid = '").append(uid).append("' and friend = '").append(friendInfo.getUid()).append("'");
            MySql.updateSomes(hql3.toString());
            StringBuffer hql4 = new StringBuffer("delete from PresentEntity ")
                    .append(" where type = 1 and friend = '").append(uid).append("' and uid = '").append(friendInfo.getUid()).append("'");
            MySql.updateSomes(hql4.toString());
            FriendData.ReportDeleteFriend.Builder deleteBu = FriendData.ReportDeleteFriend.newBuilder();
            deleteBu.setId(Integer.parseInt(roleId));
            ReportManager.reportInfo(friendInfo.getUid(), ProtoData.SToC.REPORTDELETEFRIEND_VALUE, deleteBu.build().toByteArray());
            ITask iTask = TaskDao.getInstance();
            iTask.updateAchievement(uid, 27, 1);
        } else if (type == 3) {//查找玩家
            String friendUid = null;
            FriendData.FindPlayer.Builder findBu = FriendData.FindPlayer.newBuilder();
            FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
            if (friendInfo != null) {
                friendUid = friendInfo.getUid();
                findBu.setStatus(1);
                playBu.setId(friendInfo.getId());
                playBu.setHead(friendInfo.getHead());
                playBu.setLv(friendInfo.getLv());
                playBu.setName(friendInfo.getName());
//                playBu.setStation(friendInfo.getStation());
            } else {
                List<Object> list = new ArrayList<Object>();
                list.add(0, uid);
                list.add(1, type);
                list.add(2, id);
                CallBack callBack = new FriendCallBack(CallBackOrder.FINDFRIENDBACK);
                callBack.addParameterList(list);
                ILogin iLogin = LoginDao.getInstance();
                iLogin.getRoleEntityFromDBById(id, callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
                return null;

//                ILogin iLogin = LoginDao.getInstance();
//                RoleEntity roleEntity = iLogin.getRoleEntityFromDBById(id);
//                if (roleEntity == null){
//                    log.error(uid+":[operateFriend] error 6");
//                    builder.setErrorId(ProtoData.ErrorCode.NOUSER_VALUE);
//                    return builder;
//                }
//                friendUid = roleEntity.getUid();
//                if (friendUid.equals(uid)){
//                    log.error(uid+":[operateFriend] error 7 >>> can not be self");
//                    builder.setErrorId(ProtoData.ErrorCode.ADDDSELFERROR_VALUE);
//                    return builder;
//                }
//                findBu.setStatus(0);
//                playBu.setId(roleEntity.getId());
//                playBu.setName(roleEntity.getName());
//                playBu.setLv(roleEntity.getLv());
//                playBu.setHead(roleEntity.getHead());
//                playBu.setStation(roleEntity.getStation());
            }
            ILogin iLogin = LoginDao.getInstance();
            findBu.setIsOnline(iLogin.getRoleOnlineStatus(friendUid));
            findBu.setPlayer(playBu);
            builder.setPlayer(findBu);
        }
        return builder;
    }

    private MessageInfo getOneMessageInfo(String uid, String mid) {
        MessageInfo messageInfo = null;
        Redis jedis = Redis.getInstance();
        String messageStr = jedis.hget("rolemessages:" + uid, mid);
        if (messageStr != null) {
            messageInfo = (MessageInfo) MyUtils.jsonToBean(messageStr, MessageInfo.class);
        }
        return messageInfo;
    }

    private void updateMessageStatus(String uid, String mid, int status, MessageInfo messageInfo) {
        Redis jedis = Redis.getInstance();
        messageInfo.setStatus(status);
        jedis.hset("rolemessages:" + uid, mid, MyUtils.objectToJson(messageInfo));
        StringBuffer hql = new StringBuffer("update MessageEntity set status = ").append(status)
                .append(" where uid = '").append(uid).append("' and mid = '").append(mid).append("'");
        MySql.updateSomes(hql.toString());
    }

    public void deleteOffLineMessage(String uid, List<MessageInfo> messageList) {
        Pipeline p = Redis.pipelined();
        for (int i = 0; i < messageList.size(); i++) {
            MessageInfo messageInfo = messageList.get(i);
            if (messageInfo.getType() == SuperConfig.OFFLINEMESSAGE || messageInfo.getType() == 3) {
                p.hdel("rolemessages:" + uid, messageInfo.getMid());
            }
        }
        p.sync();
        StringBuffer hql = new StringBuffer("delete from MessageEntity ")
                .append(" where uid = '").append(uid).append("' and type = ").append(SuperConfig.OFFLINEMESSAGE).append(" or type = 3");
        MySql.updateSomes(hql.toString());
    }

    public void deleteMessage(String uid, String mid) {
        Redis jedis = Redis.getInstance();
        jedis.hdel("rolemessages:" + uid, mid);
        deleteMessageSql(uid, mid);
    }

    public void deleteMessageSql(String uid, String mid) {
        StringBuffer hql = new StringBuffer("delete from MessageEntity ")
                .append(" where uid = '").append(uid).append("' and mid = '").append(mid).append("'");
        MySql.updateSomes(hql.toString());
    }

    private boolean isMail(String mid) {
        Redis jedis = Redis.getInstance();
        Map<String, String> map = jedis.hgetAll("mailinfo:" + mid);
        boolean bo = map.size() == 0 ? false : true;
        return bo;
    }

    //操作消息
    public FriendData.ResponseOperateMessage.Builder operateMessage(String uid, String mid, int type) {
        FriendData.ResponseOperateMessage.Builder builder = FriendData.ResponseOperateMessage.newBuilder();
        builder.setMid(mid);
        builder.setType(type);
        builder.setErrorId(0);
        MessageInfo messageInfo = getOneMessageInfo(uid, mid);
        if (messageInfo == null) {
            log.error(uid + ":[operateMessage] error 1");
            builder.setErrorId(ProtoData.ErrorCode.MESSAGENONE_VALUE);
            return builder;
        }
        //删除（拒绝）
        if (type == 0) {
            if (isMail(mid) == true) {
                updateMessageStatus(uid, mid, -1, messageInfo);
            } else {
                deleteMessage(uid, mid);
            }
            //领取（同意）
        } else if (type == 1) {
            if (messageInfo.getType() == 1) {
                String friendUid = messageInfo.getContent().split("&")[0];
                ILogin iLogin = LoginDao.getInstance();
                RoleEntity roleEntity = iLogin.getRoleEntityFromRedis(friendUid);
//                boolean bo = true;
                if (roleEntity == null) {
//                    bo = false;
//                    roleEntity = iLogin.getRoleEntityFromDB(friendUid);

                    List<Object> list = new ArrayList<Object>();
                    list.add(0, uid);
                    list.add(1, mid);
                    list.add(2, type);
                    list.add(3, friendUid);
                    CallBack callBack = new FriendCallBack(CallBackOrder.AGREEWITHFRIENFBACK);
                    callBack.addParameterList(list);
                    iLogin.getRoleEntityFromDB(friendUid, callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
                    return null;
                }
//                if (roleEntity == null){
//                    log.error(uid+":[operateMessage] error 2 >>>friendUid:"+friendUid);
//                    builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
//                    return builder;
//                }
                FriendData.Friend.Builder friendBu = agreeWithFriend(roleEntity, true, uid, mid);
                builder.setFriend(friendBu);
//                FriendData.Friend.Builder friendBu = FriendData.Friend.newBuilder();
//                FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
//                playBu.setId(roleEntity.getId());
//                playBu.setName(roleEntity.getName());
//                playBu.setLv(roleEntity.getLv());
//                playBu.setHead(roleEntity.getHead());
//                playBu.setStation(roleEntity.getStation());
//                friendBu.setPlayer(playBu);
//                friendBu.setSignature(roleEntity.getSignature());
//                friendBu.setStatus(iLogin.getRoleOnlineStatus(friendUid));
//                friendBu.setValue(0);
//                friendBu.setDailyItem(0);
//                friendBu.setDailyScript(0);
//                builder.setFriend(friendBu);
//
//                Redis jedis = Redis.getInstance();
//                FriendInfo friendInfo = roleEntityToFriendInfo(roleEntity);
//                jedis.hset("rolefriends:" + uid, friendInfo.getId()+"",MyUtils.objectToJson(friendInfo));
//                if (bo == true){
//                    RoleEntity myRoleEntity = iLogin.getRoleEntityFromRedis(uid);
//                    FriendInfo friendInfo2 = roleEntityToFriendInfo(myRoleEntity);
//                    jedis.hset("rolefriends:" + friendUid, friendInfo2.getId()+"",MyUtils.objectToJson(friendInfo2));
//                    FriendData.ReportFriend reportFriend = setReportFriend(friendInfo2.getUid(),friendInfo2.getStatus());
//                    ReportManager.reportInfo(friendUid, ProtoData.SToC.REPORTFRIEND_VALUE,reportFriend.toByteArray());
//                }
//                RelativeshipEntity relativeshipEntity = new RelativeshipEntity();
//                relativeshipEntity.setRoleuid1(uid);
//                relativeshipEntity.setRoleuid2(friendUid);
//                relativeshipEntity.setType(SuperConfig.RELATIVESHIP_FRIEND);
//                relativeshipEntity.setValue(0);
//                MySql.insert(relativeshipEntity);
//
//                deleteMessage(uid, mid);
//
//                RoleEntity selfEntity = iLogin.getRoleEntityFromRedis(uid);
//                friendInStation(selfEntity,roleEntity);
//
//                ITask iTask = TaskDao.getInstance();
//                iTask.updateAchievement(uid,5,1);
//                iTask.updateAchievement(friendUid,5,1);
            } else {
                if (messageInfo.getStatus() == 2) {
                    log.error(uid + ":[operateMessage] error 3");
                    builder.setErrorId(ProtoData.ErrorCode.MESSAGEGETED_VALUE);
                    return builder;
                }
                if (messageInfo.getType() == SuperConfig.SPECIALMESSAGE) {

                    MailJson mailJson = (MailJson) MyUtils.jsonToBean(messageInfo.getContent(), MailJson.class);
                    List<CommonJson> itemList = mailJson.getItems();
                    IItem iItem = ItemDao.getInstance();
                    List<ItemInfo> itemInfoList = new ArrayList<ItemInfo>();
                    for (int i = 0; i < itemList.size(); i++) {
                        CommonJson item = itemList.get(i);
                        int itemId = item.getKey();
                        int itemNum = item.getValue();
                        ItemInfo itemInfo = new ItemInfo();
                        itemInfo.setId(itemId);
                        itemInfo.setNum(itemNum);
                        itemInfoList.add(itemInfo);
                    }
                    boolean bo = iItem.judgebagCell(uid, itemInfoList);
                    if (bo == false) {
                        log.error(uid + ":[operateMessage] error 4 >>>mid:" + messageInfo.getMid());
                        builder.setErrorId(ProtoData.ErrorCode.BAGFULLERROR_VALUE);
                        return builder;
                    }
                    for (int i = 0; i < itemList.size(); i++) {
                        CommonJson item = itemList.get(i);
                        int itemId = item.getKey();
                        int itemNum = item.getValue();
                        double totalNum = iItem.updateItemInfo(uid, itemId, itemNum);
                        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                        itemBu.setId(itemId);
                        itemBu.setNum(totalNum);
                        builder.addItem(itemBu);
                    }
                    // 修复前端邮件领取图标bug
                    deleteMessage(uid, mid);
                    FriendData.ResponseOperateMessage.Builder updateBuilder = FriendData.ResponseOperateMessage.newBuilder();
                    updateBuilder.setMid(mid);
                    updateBuilder.setType(0);
                    updateBuilder.setErrorId(0);
                    ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEOPERATEMESSAGE_VALUE, updateBuilder.build().toByteArray());
                } else {
                    Map<String, String> messageMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MESSAGE, messageInfo.getType());
                    String itemIdStr = messageMap.get("itemid");
                    if (itemIdStr.equals("0")) {
                        log.error(uid + ":[operateMessage] error 5 >>>id:" + messageInfo.getType());
                        builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                        return builder;
                    }
                    String[] itemIds = itemIdStr.split("\\|");
                    String[] itemNums = messageMap.get("itemnum").split("\\|");
                    if (itemIds.length != itemNums.length) {
                        log.error(uid + ":[operateMessage] error 6 >>>itemIds:" + itemIdStr + ",itemNums:" + messageMap.get("itemnum"));
                        builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
                        return builder;
                    }
                    IItem iItem = ItemDao.getInstance();
                    List<ItemInfo> itemInfoList = new ArrayList<ItemInfo>();
                    for (int i = 0; i < itemIds.length; i++) {
                        int itemId = Integer.parseInt(itemIds[i]);
                        int itemNum = Integer.parseInt(itemNums[i]);
                        ItemInfo itemInfo = new ItemInfo();
                        itemInfo.setId(itemId);
                        itemInfo.setNum(itemNum);
                        itemInfoList.add(itemInfo);
                    }
                    boolean bo = iItem.judgebagCell(uid, itemInfoList);
                    if (bo == false) {
                        log.error(uid + ":[operateMessage] error 7 >>>mid:" + messageInfo.getMid());
                        builder.setErrorId(ProtoData.ErrorCode.BAGFULLERROR_VALUE);
                        return builder;
                    }
                    for (int i = 0; i < itemIds.length; i++) {
                        int itemId = Integer.parseInt(itemIds[i]);
                        int itemNum = Integer.parseInt(itemNums[i]);
                        double totalNum = iItem.updateItemInfo(uid, itemId, itemNum);
                        ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                        itemBu.setId(itemId);
                        itemBu.setNum(totalNum);
                        builder.addItem(itemBu);
                    }
                }
                updateMessageStatus(uid, mid, 2, messageInfo);
            }
        } else {
            log.error(uid + ":[operateMessage] error 8");
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
        }
        return builder;
    }

    public FriendData.Friend.Builder agreeWithFriend(RoleEntity roleEntity, boolean isOnline, String uid, String mid) {
        ILogin iLogin = LoginDao.getInstance();
        FriendData.Friend.Builder friendBu = FriendData.Friend.newBuilder();
        FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
        playBu.setId(roleEntity.getId());
        playBu.setName(roleEntity.getName());
        playBu.setLv(roleEntity.getLv());
        playBu.setHead(roleEntity.getHead());
        friendBu.setPlayer(playBu);
        friendBu.setStatus(iLogin.getRoleOnlineStatus(roleEntity.getUid()));
        friendBu.setValue(0);
        friendBu.setDailyItem(0);
        friendBu.setDailyScript(0);
        Redis jedis = Redis.getInstance();
        FriendInfo friendInfo = roleEntityToFriendInfo(roleEntity);
        jedis.hset("rolefriends:" + uid, friendInfo.getId() + "", MyUtils.objectToJson(friendInfo));
        if (isOnline == true) {
            RoleEntity myRoleEntity = iLogin.getRoleEntityFromRedis(uid);
            FriendInfo friendInfo2 = roleEntityToFriendInfo(myRoleEntity);
            jedis.hset("rolefriends:" + roleEntity.getUid(), friendInfo2.getId() + "", MyUtils.objectToJson(friendInfo2));
            FriendData.ReportFriend reportFriend = setReportFriend(friendInfo2.getUid(), friendInfo2.getStatus());
            ReportManager.reportInfo(roleEntity.getUid(), ProtoData.SToC.REPORTFRIEND_VALUE, reportFriend.toByteArray());
        }
        RelativeshipEntity relativeshipEntity = new RelativeshipEntity();
        relativeshipEntity.setRoleuid1(uid);
        relativeshipEntity.setRoleuid2(roleEntity.getUid());
        relativeshipEntity.setType(SuperConfig.RELATIVESHIP_FRIEND);
        relativeshipEntity.setValue(0);
        MySql.insert(relativeshipEntity);

        deleteMessage(uid, mid);

        RoleEntity selfEntity = iLogin.getRoleEntityFromRedis(uid);
        friendInStation(selfEntity, roleEntity);

        ITask iTask = TaskDao.getInstance();
        iTask.updateAchievement(uid, 5, 1);
        iTask.updateAchievement(roleEntity.getUid(), 5, 1);
        iTask.updateMainAchievement(uid, 9, 1);
        iTask.updateMainAchievement(roleEntity.getUid(), 9, 1);
        return friendBu;
    }

    private void friendInStation(RoleEntity selfEntity, RoleEntity friendEntity) {
        int selfStation = 1;
        int friendStation = 1;
        if (selfStation == friendStation) {
            ChannelHandlerContext selfCtx = SuperServerHandler.getServerIdCtxFromUid(selfEntity.getUid());
            ChannelHandlerContext friendCtx = SuperServerHandler.getServerIdCtxFromUid(friendEntity.getUid());
            if (selfCtx != null && friendCtx != null) {
                PointDoubleInfo selfPoint = null;
                PointDoubleInfo friendPoint = null;
                List<StationInfo> list = SuperServerHandler.stationMap.get(selfStation);
                for (int i = 0; i < list.size(); i++) {
                    StationInfo stationInfo = list.get(i);
                    if (stationInfo.getCtx() == selfCtx) {
                        selfPoint = stationInfo.getPoint();
                    } else if (stationInfo.getCtx() == friendCtx) {
                        friendPoint = stationInfo.getPoint();
                    }
                    if (selfPoint != null && friendPoint != null) {
                        break;
                    }
                }
                if (selfPoint != null && friendPoint != null) {
                    ILogin iLogin = LoginDao.getInstance();
                    UserData.ReportInAndOutStation.Builder selfStationBu = UserData.ReportInAndOutStation.newBuilder();
                    selfStationBu.setType(1);
                    ReportManager.reportInfo(selfEntity.getUid(), ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, selfStationBu.build().toByteArray());
                    UserData.ReportInAndOutStation.Builder friendStationBu = UserData.ReportInAndOutStation.newBuilder();
                    friendStationBu.setType(1);

                    ReportManager.reportInfo(friendEntity.getUid(), ProtoData.SToC.REPORTINANDOUTSTATION_VALUE, friendStationBu.build().toByteArray());
                }
            }
        }
    }

    private FriendInfo roleEntityToFriendInfo(RoleEntity roleEntity) {
        FriendInfo friendInfo = new FriendInfo();
        friendInfo.setUid(roleEntity.getUid());
        friendInfo.setId(roleEntity.getId());
        friendInfo.setName(roleEntity.getName());
        friendInfo.setLv(roleEntity.getLv());
        friendInfo.setHead(roleEntity.getHead());

        ILogin iLogin = LoginDao.getInstance();
        friendInfo.setStatus(iLogin.getRoleOnlineStatus(roleEntity.getUid()));
        return friendInfo;
    }

    //增加好友
    public FriendData.ReportFriend setReportFriend(String uid, int status) {
        ILogin iLogin = LoginDao.getInstance();
        FriendData.ReportFriend.Builder builder = FriendData.ReportFriend.newBuilder();
        FriendData.Friend.Builder friendBu = FriendData.Friend.newBuilder();
        RoleEntity roleEntity = iLogin.getRoleEntityFromRedis(uid);
//        if (roleEntity == null){
//            roleEntity = iLogin.getRoleEntityFromDB(uid);
//        }
        if (roleEntity == null) {
            return null;
        }
        FriendData.Player.Builder playBu = FriendData.Player.newBuilder();
        playBu.setId(roleEntity.getId());
        playBu.setName(roleEntity.getName());
        playBu.setLv(roleEntity.getLv());
        playBu.setHead(roleEntity.getHead());
        friendBu.setPlayer(playBu);
        friendBu.setStatus(status);
        friendBu.setValue(0);
        friendBu.setDailyScript(0);
        friendBu.setDailyItem(0);
        builder.setFriend(friendBu);
        return builder.build();
    }

    //更新好友信息
    public FriendData.ReportUpdateFriend setUpdateFriend(String uid, String name, int lv, int head, int status, int station, String signature, long endless) {
        FriendData.ReportUpdateFriend.Builder builder = FriendData.ReportUpdateFriend.newBuilder();
        ILogin iLogin = LoginDao.getInstance();
        int id = iLogin.getIdFromUid(uid);
        builder.setId(id);
        if (name != null) {
            builder.setName(name);
        }
        if (lv != -1) {
            builder.setLv(lv);
        }
        if (head != -1) {
            builder.setHead(head);
        }
        if (status != -1) {
            builder.setStatus(status);
        }
        if (station != -1) {
            builder.setStation(station);
        }
        if (signature != null) {
            builder.setSignature(signature);
        }
        if (endless != -1) {
            builder.setTotalEndless(endless);
        }
        return builder.build();
    }

    private void updateFriendValue(String uid, FriendInfo friendInfo, int totalVal) {
        Redis jedis = Redis.getInstance();
        friendInfo.setValue(totalVal);
        jedis.hset("rolefriends:" + uid, friendInfo.getId() + "", MyUtils.objectToJson(friendInfo));

        ILogin iLogin = LoginDao.getInstance();
        int myId = iLogin.getIdFromUid(uid);
        FriendInfo myInfo = getFriendInfoById(friendInfo.getUid(), myId);
        if (myInfo != null) {
            myInfo.setValue(totalVal);
            jedis.hset("rolefriends:" + friendInfo.getUid(), myInfo.getId() + "", MyUtils.objectToJson(myInfo));
        }

        List<Object> list = new ArrayList<Object>();
        list.add(0, uid);
        list.add(1, friendInfo.getUid());
        list.add(2, totalVal);
        CallBack callBack = new FriendCallBack(CallBackOrder.JUDGEFRIENGVALUEBACK);
        callBack.addParameterList(list);
        StringBuffer sql = new StringBuffer(" from RelativeshipEntity where roleuid1='").append(uid).append("' and roleuid2='").append(friendInfo.getUid()).append("'");
        MySql.queryInSql(sql.toString(), callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
        ITask task = TaskDao.getInstance();
        task.updateOneTask(uid, 10015, 1);
//        Object object = MySql.queryForOne(sql.toString());
//        if (object != null) {
//            sql = new StringBuffer("update RelativeshipEntity set value = ").append(totalVal)
//                    .append(" where roleuid1='").append(uid).append("' and roleuid2='").append(friendInfo.getUid()).append("'");
//            MySql.updateSomes(sql.toString());
//        }else {
//            sql = new StringBuffer("update RelativeshipEntity set value = ").append(totalVal)
//                    .append(" where roleuid2='").append(uid).append("' and roleuid1='").append(friendInfo.getUid()).append("'");
//            MySql.updateSomes(sql.toString());
//        }
    }

    private void updateFriendPresentToDB(int type, String uid, String friendUid, int nowNum, int oldNum) {
        if (oldNum == 0) {
            PresentEntity presentEntity = new PresentEntity();
            presentEntity.setType(type);
            presentEntity.setUid(uid);
            presentEntity.setFriend(friendUid);
            presentEntity.setNum(nowNum);
            presentEntity.setTimestamp(TimerHandler.nowTimeStamp);
            MySql.insert(presentEntity);
        } else {
            StringBuffer sql = new StringBuffer("update PresentEntity set num = ").append(nowNum)
                    .append(" where uid='").append(uid).append("' and friend='").append(friendUid).append("' and type = ").append(type);
            MySql.updateSomes(sql.toString());
        }
    }

    public FriendData.ResponseGivePresent.Builder givePresent(String uid, int friendId, int type, List<Integer> idList) {
        FriendData.ResponseGivePresent.Builder builder = FriendData.ResponseGivePresent.newBuilder();
        builder.setValue(0);
        builder.setFriendId(friendId);
        builder.setErrorId(0);
        FriendInfo friendInfo = getFriendInfoById(uid, friendId);
        if (friendInfo == null) {
            log.error(uid + ":[givePresent] error 1 >>>friendId:" + friendId);
            builder.setErrorId(ProtoData.ErrorCode.NOTFRIEND_VALUE);
            return builder;
        }
        String friendUid = friendInfo.getUid();
        if (friendUid == null) {
            log.error(uid + ":[givePresent] error 2 >>>id:" + friendId);
            builder.setErrorId(ProtoData.ErrorCode.NOUSER_VALUE);
            return builder;
        }
        Redis jedis = Redis.getInstance();
        String roleName = jedis.hget("role:" + uid, "name");
        ILogin iLogin = LoginDao.getInstance();
        int status = iLogin.getRoleOnlineStatus(friendUid);
        int addVal = 0;
        if (type == 1) {//文字
            if ((friendInfo.getDailyScript() + idList.size()) > SuperConfig.getDailyPresentScript()) {
                log.error(uid + ":[givePresent] error 3 >>>DailyScript:" + friendInfo.getDailyScript() + ",addNum:" + idList.size());
                builder.setErrorId(ProtoData.ErrorCode.NOTFRIEND_VALUE);
                return builder;
            }

            for (int i = 0; i < idList.size(); i++) {
                int id = idList.get(i);
                Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_INTERACTION, id);
                String[] strings = map.get("feel").split("\\|");
                int val = 0;
                if (strings.length == 1) {
                    val = Integer.parseInt(map.get("feel"));
                } else if (strings.length == 2) {
                    int min = Integer.parseInt(strings[0]);
                    int max = Integer.parseInt(strings[1]);
                    val = (int) (min + Math.random() * (max - min + 1));
                    while (val == 0) {
                        val = (int) (min + Math.random() * (max - min + 1));
                    }
                } else {
                    continue;
                }
                builder.addSingle(val);
                addVal += val;

                String content = roleName + "|" + map.get("dose");
                String mid = MyUtils.setRandom();

                if (status == 0) {
                    MessageInfo info = addNewMessage(friendUid, 3, content, mid, TimerHandler.nowTimeStamp);
                    if (info == null) {
                        log.error(uid + ":[givePresent] error 4 >>>friendId:" + friendId);
                    }
                } else {
                    reportMessage(mid, 3, content, friendUid);
                }

                mid = MyUtils.setRandom();
                content = friendInfo.getName() + "|" + map.get("dose");
                reportMessage(mid, 6, content, uid);
            }

            int dailyScript = friendInfo.getDailyScript() + idList.size();
            updateFriendPresentToDB(type, uid, friendUid, dailyScript, friendInfo.getDailyScript());
            friendInfo.setDailyScript(dailyScript);
            if (dailyScript > 0) {
                friendInfo.setScriptStamp(TimerHandler.nowTimeStamp + "");
            }
            builder.setDailyScript(dailyScript);
        } else if (type == 2) {//物品
            IItem iItem = ItemDao.getInstance();
            int daibiaowodexin = 0;
            int addDailyItemNum = 0;
            for (int i = 0; i < idList.size(); i++) {
                int id = idList.get(i);
                if (id == 33) {
                    daibiaowodexin += 1;
                }
                double itemNum = iItem.getItemNum(uid, id);
                if (itemNum < 1) {
                    log.error(uid + ":[givePresent] error 6 >>>itemId:" + id + ",itemNum:" + itemNum);
                    builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                    return builder;
                }
                int use = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, id + "", "use"));
                if (use != 7) {
                    addDailyItemNum += 1;
                }
            }
            if ((friendInfo.getDailyItem() + addDailyItemNum) > SuperConfig.getDailyPresentItem()) {
                log.error(uid + ":[givePresent] error 5 >>>DailyItem:" + friendInfo.getDailyItem() + ",addNum:" + idList.size());
                builder.setErrorId(ProtoData.ErrorCode.ITEMNUMERROR_VALUE);
                return builder;
            }
            ItemData.ReportItem.Builder reportItemBu = ItemData.ReportItem.newBuilder();
            for (int i = 0; i < idList.size(); i++) {
                int id = idList.get(i);
                double total = iItem.updateItemInfo(uid, id, -1);
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(id);
                itemBu.setNum(total);
                builder.addItem(itemBu);

                Map<String, String> map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, id);
                int use = Integer.parseInt(map.get("use"));
                int donate = Integer.parseInt(map.get("donate"));
                int val = Integer.parseInt(map.get("get_num"));
                if (use == 7 || donate == 1) {
                    String mid = MyUtils.setRandom();
                    String content = roleName + "|" + map.get("name");
                    if (use == 7) {
                        builder.addSingle(val);
                        addVal += val;
                        if (status == 0) {
                            MessageInfo info = addNewMessage(friendUid, 3, uid + "&" + content, mid, TimerHandler.nowTimeStamp);
                            if (info == null) {
                                log.error(uid + ":[givePresent] error 7 >>>friendId:" + friendId);
                            }
                        } else {
                            reportMessage(mid, 3, content, friendUid);
                        }
                    } else if (donate == 1) {
//                        double totalFriend = iItem.updateItemInfo(friendInfo.getUid(),id,1);
//                        if (totalFriend != -2){
//                            ItemData.Item.Builder addItemBu = ItemData.Item.newBuilder();
//                            addItemBu.setId(id);
//                            addItemBu.setNum(totalFriend);
//                            reportItemBu.addItem(itemBu);
//                        }

                        MailJson mailJson = new MailJson();
                        mailJson.setSender(roleName);
                        mailJson.setTitle("好友赠送");
                        mailJson.setContent("您的好友," + roleName + ",给您赠送了礼物，请查收。");
                        List<CommonJson> itemJson = new ArrayList<CommonJson>();
                        CommonJson commonJson = new CommonJson();
                        commonJson.setKey(id);
                        commonJson.setValue(1);
                        itemJson.add(commonJson);
                        mailJson.setItems(itemJson);
                        content = MyUtils.objectToJson(mailJson);
                        MessageInfo info = addNewMessage(friendUid, 1001, content, mid, TimerHandler.nowTimeStamp);
                        if (info != null) {
                            reportMessage(mid, 1001, content, friendUid);
                        }
                    }
                    mid = MyUtils.setRandom();
                    String itemName = Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_ITEM, id + "", "name");
                    content = friendInfo.getName() + "|" + itemName;
                    reportMessage(mid, 6, content, uid);
                }
            }
            int dailyItem = friendInfo.getDailyItem() + addDailyItemNum;
            updateFriendPresentToDB(type, uid, friendUid, dailyItem, friendInfo.getDailyItem());
            friendInfo.setDailyItem(dailyItem);
            if (dailyItem > 0) {
                friendInfo.setItemStamp(TimerHandler.nowTimeStamp + "");
            }
            builder.setDailyItem(dailyItem);
            if (reportItemBu.getItemList().size() > 0) {
                ReportManager.reportInfo(friendUid, ProtoData.SToC.REPORTITEM_VALUE, reportItemBu.build().toByteArray());
            }

            if (daibiaowodexin > 0) {
                ITask iTask = TaskDao.getInstance();
                iTask.updateAchievement(uid, 22, daibiaowodexin);
            }
        } else if (type == 3) {//一键文字
            int num = SuperConfig.getDailyPresentScript() - friendInfo.getDailyScript();
            if (num <= 0) {
                log.error(uid + ":[givePresent] error 8 >>>DailyScript:" + friendInfo.getDailyScript());
                builder.setErrorId(ProtoData.ErrorCode.NOTFRIEND_VALUE);
                return builder;
            }
            Set<String> set = jedis.keys(SuperConfig.REDIS_EXCEL_INTERACTION + "*");
            Iterator<String> iterator = set.iterator();
            if (num > set.size()) {
                log.error(uid + ":[givePresent] error 9");
                builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
                return builder;
            } else if (num == set.size()) {
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    Map<String, String> map = jedis.hgetAll(key);
                    String[] strings = map.get("feel").split("\\|");
                    int val = 0;
                    if (strings.length == 1) {
                        val = Integer.parseInt(map.get("feel"));
                    } else if (strings.length == 2) {
                        int min = Integer.parseInt(strings[0]);
                        int max = Integer.parseInt(strings[1]);
                        val = (int) (min + Math.random() * (max - min + 1));
                        while (val == 0) {
                            val = (int) (min + Math.random() * (max - min + 1));
                        }
                    } else {
                        continue;
                    }
                    builder.addSingle(val);
                    addVal += val;

                    String content = roleName + "|" + map.get("dose");
                    String mid = MyUtils.setRandom();

                    if (status == 0) {
                        MessageInfo info = addNewMessage(friendUid, 3, content, mid, TimerHandler.nowTimeStamp);
                        if (info == null) {
                            log.error(uid + ":[givePresent] error 10 >>>friendId:" + friendId);
                        }
                    } else {
                        reportMessage(mid, 3, content, friendUid);
                    }

                    mid = MyUtils.setRandom();
                    content = friendInfo.getName() + "|" + map.get("dose");
                    reportMessage(mid, 6, content, uid);
                }

                int dailyScript = friendInfo.getDailyScript() + num;
                updateFriendPresentToDB(type, uid, friendUid, dailyScript, friendInfo.getDailyScript());
                friendInfo.setDailyScript(dailyScript);
                if (dailyScript > 0) {
                    friendInfo.setScriptStamp(TimerHandler.nowTimeStamp + "");
                }

            } else {
                Map<String, Integer> keyMap = new HashMap<String, Integer>();
                while (iterator.hasNext()) {
                    String key = iterator.next();
                    int rate = Integer.parseInt(jedis.hget(key, "rate"));
                    keyMap.put(key, rate);
                }
                int total = 100;
                for (int i = 0; i < num; i++) {
                    int rand = (int) (1 + Math.random() * (total - 1 + 1));
                    int all = 0;
                    String key = null;
                    for (Map.Entry<String, Integer> entry : keyMap.entrySet()) {
                        all += entry.getValue();
                        if (all <= rand) {
                            key = entry.getKey();
                            total -= all;
                            Map<String, String> map = jedis.hgetAll(entry.getKey());
                            String[] strings = map.get("feel").split("\\|");
                            int val = 0;
                            if (strings.length == 1) {
                                val = Integer.parseInt(map.get("feel"));
                            } else if (strings.length == 2) {
                                int min = Integer.parseInt(strings[0]);
                                int max = Integer.parseInt(strings[1]);
                                val = (int) (min + Math.random() * (max - min + 1));
                                while (val == 0) {
                                    val = (int) (min + Math.random() * (max - min + 1));
                                }
                            } else {
                                continue;
                            }
                            builder.addSingle(val);
                            addVal += val;

                            String content = roleName + "|" + map.get("dose");
                            String mid = MyUtils.setRandom();

                            if (status == 0) {
                                MessageInfo info = addNewMessage(friendUid, 3, content, mid, TimerHandler.nowTimeStamp);
                                if (info == null) {
                                    log.error(uid + ":[givePresent] error 11 >>>friendId:" + friendId);
                                }
                            } else {
                                reportMessage(mid, 3, content, friendUid);
                            }
                            mid = MyUtils.setRandom();
                            content = friendInfo.getName() + "|" + map.get("dose");
                            reportMessage(mid, 6, content, uid);
                            break;
                        }
                        keyMap.remove(key);
                    }
                }
                int dailyScript = friendInfo.getDailyScript() + num;
                updateFriendPresentToDB(type, uid, friendUid, dailyScript, friendInfo.getDailyScript());
                friendInfo.setDailyScript(dailyScript);
                if (dailyScript > 0) {
                    friendInfo.setScriptStamp(TimerHandler.nowTimeStamp + "");
                }
            }
            builder.setDailyScript(friendInfo.getDailyScript());
        }
        int total = friendInfo.getValue() + addVal;
        int max = SuperConfig.getFriendValueMax();
        int min = SuperConfig.getFriendValueMin();
        if (total > max) {
            total = max;
        }
        if (total < min) {
            total = min;
        }
        if (addVal != 0) {
            FriendData.ReportUpdateFriend.Builder updateBu = FriendData.ReportUpdateFriend.newBuilder();
            int myId = iLogin.getIdFromUid(uid);
            updateBu.setId(myId);
            updateBu.setValue(total);
            ReportManager.reportInfo(friendInfo.getUid(), ProtoData.SToC.REPORTUPDATEFRIEND_VALUE, updateBu.build().toByteArray());
            updateFriendValue(uid, friendInfo, total);
        }
        jedis.hset("rolefriends:" + uid, friendInfo.getId() + "", MyUtils.objectToJson(friendInfo));
        builder.setValue(total);
        return builder;
    }

    //发送消息reportMessage
    public void reportMessage(String mid, int type, String content, String sendUid) {
        FriendData.ReportMessage.Builder reportBu = FriendData.ReportMessage.newBuilder();
        FriendData.Message.Builder messagebu = FriendData.Message.newBuilder();
        messagebu.setMid(mid);
        messagebu.setType(type);
        messagebu.setContent(content);
        messagebu.setStatus(0);
        messagebu.setTimeStamp(TimerHandler.nowTimeStamp + "");
        reportBu.setMessage(messagebu);
        //发送消息2022给前端
        ReportManager.reportInfo(sendUid, ProtoData.SToC.REPORTMESSAGE_VALUE, reportBu.build().toByteArray());
    }

    public int accusation(String uid, int targetId) {
        ILogin iLogin = LoginDao.getInstance();
        int id = iLogin.getIdFromUid(uid);
        AccusationEntity accusationEntity = new AccusationEntity();
        accusationEntity.setPlayerid(id);
        accusationEntity.setTargetid(targetId);
        Timestamp timestamp = new Timestamp(TimerHandler.nowTimeStamp);
        accusationEntity.setTimestamp(timestamp);
        MySql.insert(accusationEntity);
        return 0;
    }

    public FriendData.ReportBroadcast setReportBroadcast(int type, int id, String content) {
        FriendData.ReportBroadcast.Builder builder = FriendData.ReportBroadcast.newBuilder();
        builder.setType(type);
        builder.setId(id);
        builder.setContent(content);
        builder.setTimeStamp(TimerHandler.nowTimeStamp + "");
        return builder.build();
    }


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

    /**
     * {
     * "type":"all",
     * "playId":1,
     * "mailId":2,
     * "content":"ajsflajg"
     * "publishTime":1343523
     * }
     * //content仅在mailId=1001自定义邮件
     * <p>
     * {
     * "playId":3544,
     * "sender":"官方",
     * "mailId":1001,
     * "overdueTime":1657689043378,
     * "type":"part",
     * "title":"070401",
     * "content":
     * {
     * "sender":"官方",
     * "title":"070401","items":
     * [{
     * "value":1,      //itemId
     * "key":1     //数量
     * }],
     * "content":"070401"
     * },
     * "status":0,
     * "timestamp":1657084243378
     * }
     */
    //推送自定义邮件,表格邮件
    int mailId = 0;
    String content = "";
    String result = "";
    String subjectType = "";
    int roleId = 0;
    String sender = "";
    String title = "";
    long timestamp = 0;
    long overdueTime = 0;
    int status = 0;

    public String publishMail(JSONObject jsonObject) {
        System.err.println("前端传回的数据邮件+" + jsonObject);
        result = "OK";
        mailId = jsonObject.getInt("mailId");
        if (mailId == 1) {
            result = "mailId error！";
            return result;
        }
        if (mailId != SuperConfig.SPECIALMESSAGE) {
            Map<String, String> mailMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_MESSAGE, mailId);
            if (mailMap.size() == 0) {
                result = "mailId not exist！";
                return result;
            }
        }
        //邮件主题类型:群发/指定用户
        subjectType = jsonObject.getString("subjectType");
        if (subjectType.equals("all") || subjectType.equals("part")) {
            roleId = jsonObject.getInt("playId");
            sender = jsonObject.getString("sender");
            title = jsonObject.getString("title");
            content = jsonObject.getString("content");
            status = jsonObject.getInt("status");
            timestamp = jsonObject.getLong("timestamp");
            overdueTime = jsonObject.getLong("overdueTime");

            String str1 = MyUtils.stampToDate2(timestamp);
            str1 = str1.substring(0, 4) + str1.substring(5, 7) + str1.substring(8, str1.length());
            timestamp = Long.parseLong(str1);

            String str2 = MyUtils.stampToDate2(overdueTime);
            str2 = str2.substring(0, 4) + str2.substring(5, 7) + str2.substring(8, str2.length());
            overdueTime = Long.parseLong(str2);
            long publishTimeStamp = jsonObject.getLong("publishTimeStamp");
            if (publishTimeStamp != 0) {
                //定时发送
                TimeTest(Integer.parseInt(MyUtils.stampToDate(publishTimeStamp).substring(5, 7)),
                        Integer.parseInt(MyUtils.stampToDate(publishTimeStamp).substring(8, 10)),
                        Integer.parseInt(MyUtils.stampToDate(publishTimeStamp).substring(11, 13)),
                        Integer.parseInt(MyUtils.stampToDate(publishTimeStamp).substring(14, 16)),
                        Integer.parseInt(MyUtils.stampToDate(publishTimeStamp).substring(MyUtils.stampToDate(publishTimeStamp).length() - 2, MyUtils.stampToDate(publishTimeStamp).length())));
            } else {
                if (mailId == SuperConfig.SPECIALMESSAGE) {
                    MailJson mailJson = (MailJson) MyUtils.jsonToBean(content, MailJson.class);
                    List<CommonJson> itemList = mailJson.getItems();
                    if (itemList != null) {
                        for (int i = 0; i < itemList.size(); i++) {
                            CommonJson item = itemList.get(i);
                            Map<String, String> itemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, item.getKey());
                            if (itemMap.size() == 0) {
                                result = "itemId not exist！>>>>>" + item.getKey();
                                return result;
                            }
                        }
                    }
                }
                setMail(subjectType, roleId, mailId, sender, title, content, status, timestamp, overdueTime,null);
            }
        } else {
            result = "type error！";
        }
        return result;
    }
    public void  Em(){
        RoleDao roleDao = new RoleDao();
        List<Object> list = roleDao.queryId();
        for (int i = 0; i < list.size(); i++) {
            Object id = list.get(i);
        }
    }

    //发布新公告
    public NoticeEntity setNoticeId(String nid, String content, RoleEntity roleEntity) {
        NoticeEntity entity = new NoticeEntity();
        entity.setOwner(roleEntity.getUid());
        entity.setNid(NoticeId);
        entity.setContent(content);
        NoticeData.ReportNewNotice.Builder builder = NoticeData.ReportNewNotice.newBuilder();
        builder.setNewNotice(NoticeDao.getInstance().entityToPb(entity));
        ReportManager.reportInfo(roleEntity.getUid(), ProtoData.SToC.REPORTNEWNOTICE_VALUE, builder.build().toByteArray());
        return entity;
    }

    //公告
    int NoticeId = 0;
    //公告内容
    String NoticeContent = "";

    @Override
    public String publishNotice(JSONObject jsonObject) {
        System.err.println("前端传回的数据+" + jsonObject);
        result = "OK";
        NoticeId = jsonObject.getInt("nid");
        if (NoticeId != 0) {
            NoticeId = jsonObject.getInt("nid");
            System.out.println(NoticeId + "wwwwwwwwwwwwwwwwwwwwwwww");
        }
        if (NoticeContent != null) {
            NoticeContent = jsonObject.getString("content");
            System.out.println(NoticeContent + "kkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkkk");
        }
        setNotice(roleId, NoticeContent);
        return result;
    }

    //设置公告
    private void setNotice(int roleId, String content) {
        NoticeInfo noticeInfo = new NoticeInfo();
        String nid = MyUtils.setRandom();
        noticeInfo.setNid(nid);
        noticeInfo.setContent(content);
        noticeInfo.setRoleId(roleId);
        StringBuilder hql = new StringBuilder(" from RoleEntity where id=").append(roleId);
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(hql.toString());
        if (subjectType.equals("part")) {
            NoticeEntity entity = setNoticeId(nid, content, roleEntity);
            MySql.insert(entity);
        } else {
            hql = new StringBuilder("from RoleEntity");
            List<Object> obj = MySql.queryForList(hql.toString());
            NoticeEntity entity;
            for (int i = 0; i < obj.size(); i++) {
                RoleEntity roleEntity1 = (RoleEntity) obj.get(i);
                entity = setNoticeId(nid, content, roleEntity1);
                if (SuperServerHandler.getCtxFromUid(roleEntity1.getUid()) != null) {
//                    /// System.out.println(roleEntity1.getUid());
                    MySql.insert(entity);
                }
            }
        }
    }

    public void SendMail(String subjectType, int roleId, int mailId, String sender, String title, String content, int status, long timestamp, long overdueTime, MailData.Attachment att)
    {
        setMail(subjectType,roleId,mailId,sender,title,content,status,timestamp,overdueTime,att);
    }

    //3设置邮件
    private void setMail(String subjectType, int roleId, int mailId, String sender, String title, String content, int status, long timestamp, long overdueTime, MailData.Attachment att) {
        //邮件信息表
        MailInfo mailInfo = new MailInfo();
        String mid = MyUtils.setRandom();
        mailInfo.setMailId(mailId);
        mailInfo.setType(subjectType);
        mailInfo.setContent(content);
        mailInfo.setRoleId(roleId);
        mailInfo.setMid(mid);
        setMailToRedis(mailInfo);

        StringBuilder hql = new StringBuilder(" from RoleEntity where id=").append(roleId);
        RoleEntity roleEntity = (RoleEntity) MySql.queryForOne(hql.toString());
        if(roleEntity == null) System.err.println("mad this is empty!!!!!!!!!!!!!!!!");
        if (subjectType.equals("part")) {
            MailEntity entity = setMailid(mid, sender, subjectType, mailId, title, content, status, timestamp, overdueTime, roleEntity,att);
            MySql.insert(entity);
        } else {
            hql = new StringBuilder("from RoleEntity");
            List<Object> obj = MySql.queryForList(hql.toString());
            MailEntity entity;
            for (int i = 0; i < obj.size(); i++) {
                RoleEntity roleEntity1 = (RoleEntity) obj.get(i);
                entity = setMailid(mid, sender, subjectType, mailId, title, content, status, timestamp, overdueTime, roleEntity1,null);
//                if (SuperServerHandler.getCtxFromUid(roleEntity1.getUid()) != null) {
//                     System.out.println(roleEntity1.getUid());
                    MySql.insert(entity);
//                }
            }
        }
    }

    public MailEntity setMailid(String mid, String sender, String subjectType, int mailId, String title, String content, int status, long timestamp, long overdueTime, RoleEntity roleEntity,MailData.Attachment att) {
        //邮件全新版本ReportNewMail2111
        MailEntity entity = new MailEntity();
        entity.setOwner(roleEntity.getUid());
        entity.setMid(mid.hashCode());
        entity.setSender(sender);
        entity.setSubjectType(subjectType);
        entity.setMailId(mailId);
        entity.setTitle(title);
        entity.setContent(content);
        entity.setStatus(status);
        entity.setTimestamp(timestamp);
        Date mdate = new Date();
        SimpleDateFormat msimpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        long current = Long.parseLong(MyUtils.dateToStampCompletes(msimpleDateFormat.format(mdate))) / 1000;
        long overdue = Long.parseLong(MyUtils.dateToStampCompletes(String.valueOf(overdueTime))) / 1000;
        entity.setOverdueTime(((overdue - current) / 60 / 60 / 24) + 1);
        /* ItemData.Item.Builder builder=ItemData.Item.newBuilder();
         builder.setId(1);
         builder.setNum(111111);*/

        MailJson mailJson = (MailJson) MyUtils.jsonToBean(entity.getContent(), MailJson.class);
        entity.setContent(mailJson.getContent());

        System.out.println("邮件的道具Str：" + mailJson.getItemStr());
//        ItemData.Item.Builder builder = ItemData.Item.newBuilder();
//        List<CommonJson> itemList = mailJson.getItems();
//        builder.setNum(0);
//        builder.setId(0);
//        MailData.Attachment.Builder attachment = MailData.Attachment.newBuilder();
//        if (itemList != null) {
//            for (int i = 0; i < itemList.size(); i++) {
//                CommonJson item = itemList.get(i);
//                int itemId = item.getKey();
//                int itemNum = item.getValue();
//                builder.setId(itemId);
//                builder.setNum(itemNum);
//                attachment.addItem(builder);
//            }
//        }

        MailData.Attachment.Builder attachment = null;
        if (mailJson.getItemStr().length() > 0){
            attachment = MailAttachment.getInstance().GetAttachment(mailJson.getItemStr());
        }else {
            attachment = MailData.Attachment.newBuilder();
            attachment.setType(0);
        }

        //邮件附件
        if(att == null)
            entity.setAttchment(attachment.build().toByteArray());
        else
            entity.setAttchment(att.toByteArray());
        MailData.ReportNewMail.Builder reportNewMail = MailData.ReportNewMail.newBuilder();
        reportNewMail.setNewMail(MailDao.getInstance().entityToPb(entity));
        //通知新邮件
        System.out.println("发送邮件11111");
        ReportManager.reportInfo(roleEntity.getUid(), ProtoData.SToC.REPORTNEWMAIL_VALUE, reportNewMail.build().toByteArray());
        entity.setOverdueTime(overdueTime);
        return entity;
    }

    //2邮件信息实例
    public MailInfo mapToMailInfo(Map<String, String> mailMap) {
        if (mailMap.size() == 0) {
            return null;
        }
        MailInfo mailInfo = new MailInfo();
        mailInfo.setMailId(Integer.parseInt(mailMap.get("mailid")));
        mailInfo.setType(mailMap.get("type"));
        mailInfo.setMid(mailMap.get("mid"));
        mailInfo.setContent(mailMap.get("content"));
        mailInfo.setRoleId(Integer.parseInt(mailMap.get("playId")));
        mailInfo.setPublishTime(Long.parseLong(mailMap.get("publishtime")));
        mailInfo.setStatus(Integer.parseInt(mailMap.get("status")));
        return mailInfo;
    }

    //1上传邮件信息到redis
    private void setMailToRedis(MailInfo mailInfo) {
        Redis jedis = Redis.getInstance();
        Map<String, String> mailMap = new HashMap<String, String>();
        mailMap.put("mailid", mailInfo.getMailId() + "");
        mailMap.put("type", mailInfo.getType());
        mailMap.put("mid", mailInfo.getMid());
        mailMap.put("content", mailInfo.getContent());
        mailMap.put("playId", mailInfo.getRoleId() + "");
        mailMap.put("publishtime", mailInfo.getPublishTime() + "");
        mailMap.put("status", mailInfo.getStatus() + "");
        jedis.hmset("mailinfo:" + mailInfo.getMid(), mailMap);
    }


    public List<FriendData.Recommend> getOnlineRecommend(int server, int num, int type, List<String> dumpList) {
        return null;
    }

    private void getMissionRecommend(int server, String uid, int missionId, int id) {
        int min = missionId - 20 < 0 ? 1 : missionId - 20;
        int max = missionId + 20;
        String hql = " from RoleEntity where serverid = " + server + " and missionid > " + min + " and missionid <= " + max;
        CallBack callBack = new FriendCallBack(CallBackOrder.FRIENDRECOMMEND);
        List<Object> parameterList = new ArrayList<Object>();
        parameterList.add(0, uid);
        parameterList.add(1, server);
        parameterList.add(2, FriendData.RecommendType.REC_MISSION_VALUE);
        parameterList.add(3, id);
        callBack.addParameterList(parameterList);
        MySql.queryInSql(hql, callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
    }

    private void getEndlessRecommend(int server, String uid, int endless, int id) {
        int min = endless - 300 < 0 ? 1 : endless - 300;
        int max = endless + 300;
        String hql = " from RoleEntity where serverid = " + server + " and endless > " + min + " and endless <= " + max;
        CallBack callBack = new FriendCallBack(CallBackOrder.FRIENDRECOMMEND);
        List<Object> parameterList = new ArrayList<Object>();
        parameterList.add(0, uid);
        parameterList.add(1, server);
        parameterList.add(2, FriendData.RecommendType.REC_ENDLESS_VALUE);
        parameterList.add(3, id);
        callBack.addParameterList(parameterList);
        MySql.queryInSql(hql, callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
    }

    private void getFightRecommend(int server, String uid, int id) {
        String hql = "";
        CallBack callBack = new FriendCallBack(CallBackOrder.FRIENDRECOMMEND);
        List<Object> parameterList = new ArrayList<Object>();
        parameterList.add(0, uid);
        parameterList.add(1, server);
        parameterList.add(2, FriendData.RecommendType.REC_FRIGHT_VALUE);
        parameterList.add(3, id);
        callBack.addParameterList(parameterList);
        MySql.queryInSql(hql, callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
    }

    private void getClothingRecommend(int server, String uid, int clothNum, int id) {
        int min = clothNum - 20 < 0 ? 1 : clothNum - 20;
        int max = clothNum + 20;
        //"select uid,COUNT(dressid) from dress group by uid"
//        String hql = "select *,COUNT(t2.dressid) AS rst FROM RoleEntity AS t1 left join DressEntity as t2 on t1.uid = t2.uid GROUP BY t2.uid HAVING rst>5";
        // String hql = "select t1.id,t1.name,t1.lv,t1.head,COUNT(t2.dressid) FROM RoleEntity AS t1 left join DressEntity as t2 on t1.uid = t2.uid GROUP BY t2.uid,t1.id HAVING COUNT(*)>"+min+" and COUNT(*)<"+max;
        String hql = "select id,name,lv,head, dressnums from RoleEntity where dressnums>" + min + "and dressnums<" + max;
        CallBack callBack = new FriendCallBack(CallBackOrder.CLOTHINGRECOMMEND);
        List<Object> parameterList = new ArrayList<Object>();
        parameterList.add(0, uid);
        parameterList.add(1, server);
        parameterList.add(2, FriendData.RecommendType.REC_CLOTHING_VALUE);
        parameterList.add(3, id);
        callBack.addParameterList(parameterList);
        MySql.queryInSql(hql, callBack, SuperConfig.DB_QUERYFORLISTLCALLBACK);
    }

    public void recommendFriend(String uid, int type) {
        ILogin iLogin = LoginDao.getInstance();
        int id = iLogin.getIdFromUid(uid);
        int server = Redis.getRoleServer(uid);
        switch (type) {
            case FriendData.RecommendType.REC_MISSION_VALUE:
                IMission iMission = MissionDao.getInstance();
                int missionMax = iMission.getMissionMax(uid);
                getMissionRecommend(server, uid, missionMax, id);
                break;
            case FriendData.RecommendType.REC_ENDLESS_VALUE:
                IMission iMission2 = MissionDao.getInstance();
                int endless = iMission2.getEndlessMax(uid);
                getEndlessRecommend(server, uid, endless, id);
                break;
            case FriendData.RecommendType.REC_FRIGHT_VALUE:
                //  getFightRecommend(server,uid,id);
                // 双人对战推介好友需求会不一样，所以单独写一个方法，不采用回调

                break;
            case FriendData.RecommendType.REC_CLOTHING_VALUE:
                int clothNum = 0;
                List<RoleDressInfo> roleList = iLogin.getUserAllDressFromRedis(uid);
                for (int i = 0; i < roleList.size(); i++) {
                    clothNum += roleList.get(i).getDressList().size();
                }
                getClothingRecommend(server, uid, clothNum, id);
                break;
        }
    }

    public static class TestDateTimer extends TimerTask {
        @Override
        public void run() {
            if (FriendDao.getInstance().mailId == SuperConfig.SPECIALMESSAGE) {
                MailJson mailJson = (MailJson) MyUtils.jsonToBean(FriendDao.getInstance().content, MailJson.class);
                List<CommonJson> itemList = mailJson.getItems();
                if (itemList != null) {
                    for (int i = 0; i < itemList.size(); i++) {
                        CommonJson item = itemList.get(i);
                        Map<String, String> itemMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_ITEM, item.getKey());
                        if (itemMap.size() == 0) {
                            FriendDao.getInstance().result = "itemId not exist！>>>>>" + item.getKey();
                        }
                    }
                }
            }
            FriendDao.getInstance().setMail(FriendDao.getInstance().subjectType, FriendDao.getInstance().roleId, FriendDao.getInstance().mailId, FriendDao.getInstance().sender, FriendDao.getInstance().title, FriendDao.getInstance().content, FriendDao.getInstance().status, FriendDao.getInstance().timestamp, FriendDao.getInstance().overdueTime,null);
        }
    }

    public static void TimeTest(int yue, int ri, int shi, int fen, int miao) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.MONTH, (yue - 1));//月份
        calendar.set(Calendar.DAY_OF_MONTH, ri);//日
        calendar.set(Calendar.HOUR_OF_DAY, shi);//小时
        calendar.set(Calendar.MINUTE, fen);//分钟
        calendar.set(Calendar.SECOND, miao);//秒
        Date time = calendar.getTime();
        Timer timer = new Timer();
        timer.schedule(new FriendDao.TestDateTimer(), time);
    }

}