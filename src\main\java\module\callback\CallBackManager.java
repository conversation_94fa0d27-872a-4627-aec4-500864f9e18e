package module.callback;

import java.util.List;

/**
 * Created by nara on 2018/5/8.
 */
public class CallBackManager implements CallBack {
    protected int callBackId;
    protected Object parameter = null;
    protected List<Object> parameterList = null;

    public CallBackManager(int callBackId) {
        this.callBackId = callBackId;
    }

    public void addParameter(Object parameter) {
        this.parameter = parameter;
    }

    public void addParameterList(List<Object> parameterList) {
        this.parameterList = parameterList;
    }

    public void execute(Object object) {

    }

    public void execute(List<Object> objectList) {

    }
}
