package module.ranking;

import module.callback.CallBackManager;

import common.SuperConfig;
import entities.PartEntity;
import entities.RoleEntity;
import manager.Redis;
import model.CupboardInfo;
import model.RankInfo;
import module.callback.CallBackManager;
import module.callback.CallBackOrder;
import net.sf.json.JSONArray;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.RankingData;
import protocol.UserData;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Pipeline;
import utils.MyUtils;

import java.util.ArrayList;
import java.util.List;
//
//public class RankingCallBack extends CallBackManager {
//    private static Logger log = LoggerFactory.getLogger(RankingCallBack.class);
//    public RankingCallBack(int callBackId) {
//        super(callBackId);
//    }
//    public void execute(List<Object> objectList) {
//        switch (callBackId) {
//            case CallBackOrder.RANKFORLVBACK:
//                rankForBack(objectList, RankingData.RankType.LV_VALUE);
//                break;
//            case CallBackOrder.RANKFORSCORE:
//                rankForBack(objectList,RankingData.RankType.SCORE_VALUE);
//                break;
//            case CallBackOrder.RANKFORPETNUMBER:
//                rankForBack(objectList,RankingData.RankType.PETNUM_VALUE);
//                break;
//            default:
//                break;
//        }
//    }
//    private void rankForBack(List<Object> objectList,int type){
//        if (objectList != null){
//            String key = "rankinfo:"+type;
//            List<RankInfo> rankInfoList = new ArrayList<RankInfo>();
//            for (int i = 0; i<objectList.size() && i < SuperConfig.RANKSIZE ; i++){
//                JSONArray jsonArray = JSONArray.fromObject(objectList.get(i));
//                RoleEntity roleEntity = (RoleEntity)MyUtils.jsonToBean(jsonArray.getString(0), RoleEntity.class);
//                RankInfo rankInfo = new RankInfo();
//                rankInfo.setType(type);
//                rankInfo.setRank(i+1);
//                rankInfo.setUid(roleEntity.getUid());
//                rankInfo.setId(roleEntity.getId());
//                rankInfo.setHead(roleEntity.getHead());
//                rankInfo.setLv(roleEntity.getLv());
//                rankInfo.setName(roleEntity.getName());
//                rankInfo.setRoleId(roleEntity.getRoleid());
//                if (type == RankingData.RankType.LV_VALUE){
//                    rankInfo.setScore(roleEntity.getLv());
//                }else if (type == RankingData.RankType.SCORE_VALUE) {
//                    rankInfo.setScore(roleEntity.getScore());
//                }else if (type == RankingData.RankType.PETNUM_VALUE){
//                    rankInfo.setPetNumber(roleEntity.getRanknumber());
//                }
//                rankInfoList.add(rankInfo);
//            }
//            if (rankInfoList.size() > 0){
//                Jedis jedis = Redis.getJedis(-1);
//                Pipeline pipeline = jedis.pipelined();
//                int i = 0;
//                for (; i < SuperConfig.RANKSIZE && i < rankInfoList.size(); i++){
//                    RankInfo rankInfo = rankInfoList.get(i);
//                    pipeline.hset(key,(i+1)+"", MyUtils.objectToJson(rankInfo));
//                }
//                for ( ; i<SuperConfig.RANKSIZE ; i++){
//                    pipeline.hdel(key,(i+1)+"");
//                }
//                pipeline.sync();
//                Redis.destory(jedis);
//            }
//        }
//    }
//}
