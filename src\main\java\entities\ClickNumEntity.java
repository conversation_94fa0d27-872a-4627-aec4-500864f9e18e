package entities;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2019-06-03 19:26
 */
@Entity
@Table(name = "clicknum", schema = "", catalog = "super_star_fruit")
public class ClickNumEntity {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private String user_id;
    private Integer click_num;
    private String item_type;
    private Date click_time;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public Integer getClick_num() {
        return click_num;
    }

    public void setClick_num(Integer click_num) {
        this.click_num = click_num;
    }

    public String getItem_type() {
        return item_type;
    }

    public void setItem_type(String item_type) {
        this.item_type = item_type;
    }

    public Date getClick_time() {
        return click_time;
    }

    public void setClick_time(Date click_time) {
        this.click_time = click_time;
    }
}
