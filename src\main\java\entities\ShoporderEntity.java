package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "shoporder", schema = "", catalog = "super_star_fruit")
public class ShoporderEntity {
    private int id;
    private String uid;
    private int shopid;
    private int nums;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "shopid")
    public int getShopid() {
        return shopid;
    }

    public void setShopid(int shopid) {
        this.shopid = shopid;
    }

    @Basic
    @Column(name = "nums")
    public int getNums() {
        return nums;
    }

    public void setNums(int nums) {
        this.nums = nums;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ShoporderEntity that = (ShoporderEntity) o;
        return getId() == that.getId() &&
                getShopid() == that.getShopid() &&
                getNums() == that.getNums() &&
                Objects.equals(getUid(), that.getUid());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getUid(), getShopid(), getNums());
    }

    @Override
    public String toString() {
        return "ShoporderEntity{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", shopid=" + shopid +
                ", nums=" + nums +
                '}';
    }
}
