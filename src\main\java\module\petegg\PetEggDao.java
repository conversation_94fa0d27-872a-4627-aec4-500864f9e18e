package module.petegg;

import entities.PetEggEntity;
import manager.MySql;
import org.hibernate.NonUniqueResultException;
import protocol.PetEggData;

import java.util.List;

public class PetEggDao {
    private static PetEggDao inst = null;

    public static PetEggDao getInstance() {
        if (inst == null) {
            inst = new PetEggDao();
        }
        return inst;
    }

    private void InitData(PetEggEntity data, String uid, int petUid){
        data.setPetUid(petUid);
        data.setExp(0);
        data.setLv(1);

        data.setUid(uid);
        MySql.insert(data);
    }

    private void UpdateData(PetEggEntity data,
                            PetEggData.RequestAddPetEggExp updateData){

        data.setExp(data.getExp() + updateData.getAddExp());

        MySql.update(data);
    }

    public PetEggEntity Get(String uid, int petUid) {
        PetEggEntity data = null;
        String sql = String.format("FROM PetEggEntity WHERE uid='%s' AND petUid = '%d'", uid, petUid);
        try {
            data = (PetEggEntity) MySql.queryForOne(sql);
        }catch (Exception e){
            data = null;
        }

        if (data == null){
            System.err.println("初始化蛋数据");
            data = new PetEggEntity();

            // 初始化数据
            InitData(data, uid, petUid);
        }
        return data;
    }

    public List<Object> GetAll(String uid) {
        StringBuilder stringBuilder = new StringBuilder(" from PetEggEntity where uid='").append(uid).append("'");
        List<Object> list = MySql.queryForList(stringBuilder.toString());
        return list;
    }

    public PetEggEntity Update(String uid, PetEggData.RequestAddPetEggExp updateData) {
        PetEggEntity data = null;
//        StringBuffer sql = new StringBuffer("from PetEggEntity where petUid=").append(updateData.getPetUid()).append("");
        String sql = String.format("FROM PetEggEntity WHERE uid='%s' AND petUid = '%d'", uid, updateData.getPetUid());
        try {
            data = (PetEggEntity) MySql.queryForOne(sql);
        }catch (Exception e){
            data = null;
        }
//        System.err.println(sql);
        if (data == null){
            System.err.println("初始化蛋数据");
            data = new PetEggEntity();
            // 初始化数据
            InitData(data, uid, updateData.getPetUid());
        }else {
            UpdateData(data, updateData);
        }
        return data;
    }

}
