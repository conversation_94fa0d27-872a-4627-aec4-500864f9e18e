package model;

import entities.ActivitiesEntity;

import java.util.List;
import java.util.Map;

/**
 * Created by nara on 2017/11/22.
 */
public class RoleInfo {
    private int id;
    private int type;
    private String userid;
    private int roleid;
    private String uid;
    private int advance;
    private int head;
    private String name;
    private int sex;
    private int lv;
    //    private int exp;
    private int bagmax;
    private int station;//当前频道
    //    private int action;
    private String actionstamp;
    private int signin;
    private String signstamp;
    private String signature;
    private int cupboardNum;
    private String cupboards;
    private int moneyNum;
    private String moneyStamp;
    private String monthStamp;
    private int missionId;
    private int endless;
    private long totalEndless;


    private int getTotalEndless;
    private int totalMoney;
    private Map<Integer, List<ItemInfo>> itemList;//key背包类型
    private List<RoleDressInfo> roleList;
    /* private List<MissionInfo> missionList;*/
    private List<TaskInfo> dailyTask;
    private List<TaskInfo> achievement;
    private List<FriendInfo> friendList;
    private Map<String, FriendInStationInfo> friendInStationList;
    private List<MessageInfo> messageList;
    private List<UtilityInfo> utilityList;
    private int headballmissionid;
    private int firstRecharge;
    private List<TaskInfo> mainAchievement;
    private int headballAdvance;
    private int endlesspprovalnums;
    private int lvApprovalnums;
    private int missionApprovalnums;
    private int dressApprovalnums;
    //private List<GamecopyInfo> missionInfo;
    private int gamecopy1;
    private int gamecopy2;
    private int firstChargeRecord;
    private int luckyItemVersion;
    private int luckyItemId;
    private List<ActivitiesInfo> activitiesList;
    private long dailyGameTime;
    private int dailyLoginflag;
    private int dailyLoginLoop;

    public int getHeadballmissionid() {
        return headballmissionid;
    }

    public void setHeadballmissionid(int headballmissionid) {
        this.headballmissionid = headballmissionid;
    }

    public int getTotalMoney() {
        return totalMoney;
    }

    public void setTotalMoney(int totalMoney) {
        this.totalMoney = totalMoney;
    }

    public String getCupboards() {
        return cupboards;
    }

    public void setCupboards(String cupboards) {
        this.cupboards = cupboards;
    }

    public Map<String, FriendInStationInfo> getFriendInStationList() {
        return friendInStationList;
    }

    public void setFriendInStationList(Map<String, FriendInStationInfo> friendInStationList) {
        this.friendInStationList = friendInStationList;
    }


    public int getSex() {
        return sex;
    }

    public void setSex(int sex) {
        this.sex = sex;
    }

    public int getGetTotalEndless() {
        return getTotalEndless;
    }

    public void setGetTotalEndless(int getTotalEndless) {
        this.getTotalEndless = getTotalEndless;
    }

    public long getTotalEndless() {
        return totalEndless;
    }

    public void setTotalEndless(long totalEndless) {
        this.totalEndless = totalEndless;
    }

    public int getEndless() {
        return endless;
    }

    public void setEndless(int endless) {
        this.endless = endless;
    }

    public int getMissionId() {
        return missionId;
    }

    public void setMissionId(int missionId) {
        this.missionId = missionId;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public int getStation() {
        return station;
    }

    public void setStation(int station) {
        this.station = station;
    }

    public String getMonthStamp() {
        return monthStamp;
    }

    public void setMonthStamp(String monthStamp) {
        this.monthStamp = monthStamp;
    }

    public String getMoneyStamp() {
        return moneyStamp;
    }

    public void setMoneyStamp(String moneyStamp) {
        this.moneyStamp = moneyStamp;
    }

    public int getMoneyNum() {
        return moneyNum;
    }

    public void setMoneyNum(int moneyNum) {
        this.moneyNum = moneyNum;
    }

    public int getCupboardNum() {
        return cupboardNum;
    }

    public void setCupboardNum(int cupboardNum) {
        this.cupboardNum = cupboardNum;
    }

    public List<UtilityInfo> getUtilityList() {
        return utilityList;
    }

    public void setUtilityList(List<UtilityInfo> utilityList) {
        this.utilityList = utilityList;
    }

    public List<MessageInfo> getMessageList() {
        return messageList;
    }

    public void setMessageList(List<MessageInfo> messageList) {
        this.messageList = messageList;
    }

    public List<FriendInfo> getFriendList() {
        return friendList;
    }

    public void setFriendList(List<FriendInfo> friendList) {
        this.friendList = friendList;
    }

    public int getHead() {
        return head;
    }

    public void setHead(int head) {
        this.head = head;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public int getLv() {
        return lv;
    }

    public void setLv(int lv) {
        this.lv = lv;
    }

    public int getBagmax() {
        return bagmax;
    }

    public void setBagmax(int bagmax) {
        this.bagmax = bagmax;
    }

    public List<TaskInfo> getAchievement() {
        return achievement;
    }

    public void setAchievement(List<TaskInfo> achievement) {
        this.achievement = achievement;
    }

    public List<TaskInfo> getDailyTask() {
        return dailyTask;
    }

    public void setDailyTask(List<TaskInfo> dailyTask) {
        this.dailyTask = dailyTask;
    }

    public void setSignin(int signin) {
        this.signin = signin;
    }

   /*  public List<MissionInfo> getMissionList() {
        return missionList;
  }

   public void setMissionList(List<MissionInfo> missionList) {
       this.missionList = missionList;
   }*/

    public String getActionstamp() {
        return actionstamp;
    }

    public void setActionstamp(String actionstamp) {
        this.actionstamp = actionstamp;
    }

    public List<RoleDressInfo> getRoleList() {
        return roleList;
    }

    public void setRoleList(List<RoleDressInfo> roleList) {
        this.roleList = roleList;
    }

    public Map<Integer, List<ItemInfo>> getItemList() {
        return itemList;
    }

    public void setItemList(Map<Integer, List<ItemInfo>> itemList) {
        this.itemList = itemList;
    }

    public int getRoleid() {
        return roleid;
    }

    public void setRoleid(int roleid) {
        this.roleid = roleid;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getAdvance() {
        return advance;
    }

    public void setAdvance(int advance) {
        this.advance = advance;
    }

    public String getSignstamp() {
        return signstamp;
    }

    public void setSignstamp(String signstamp) {
        this.signstamp = signstamp;
    }

    public Integer getSignin() {
        return signin;
    }

    public void setSignin(Integer signin) {
        this.signin = signin;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getFirstRecharge() {
        return firstRecharge;
    }

    public void setFirstRecharge(int firstRecharge) {
        this.firstRecharge = firstRecharge;
    }

    public void setMainAchievement(List<TaskInfo> mainAchievement) {
        this.mainAchievement = mainAchievement;
    }

    public List<TaskInfo> getMainAchievement() {
        return mainAchievement;
    }


    public int getHeadballAdvance() {
        return headballAdvance;
    }

    public void setHeadballAdvance(int headballAdvance) {
        this.headballAdvance = headballAdvance;
    }

    public int getEndlesspprovalnums() {
        return endlesspprovalnums;
    }

    public void setEndlesspprovalnums(int endlesspprovalnums) {
        this.endlesspprovalnums = endlesspprovalnums;
    }

    public int getLvApprovalnums() {
        return lvApprovalnums;
    }

    public void setLvApprovalnums(int lvApprovalnums) {
        this.lvApprovalnums = lvApprovalnums;
    }

    public int getMissionApprovalnums() {
        return missionApprovalnums;
    }

    public void setMissionApprovalnums(int missionApprovalnums) {
        this.missionApprovalnums = missionApprovalnums;
    }

    public int getDressApprovalnums() {
        return dressApprovalnums;
    }

    public void setDressApprovalnums(int dressApprovalnums) {
        this.dressApprovalnums = dressApprovalnums;
    }

    public int getGamecopy1() {
        return gamecopy1;
    }

    public void setGamecopy1(int gamecopy1) {
        this.gamecopy1 = gamecopy1;
    }

    public int getGamecopy2() {
        return gamecopy2;
    }

    public void setGamecopy2(int gamecopy2) {
        this.gamecopy2 = gamecopy2;
    }

    public int getFirstChargeRecord() {
        return firstChargeRecord;
    }

    public void setFirstChargeRecord(int firstChargeRecord) {
        this.firstChargeRecord = firstChargeRecord;
    }

    public int getLuckyItemVersion() {
        return luckyItemVersion;
    }

    public void setLuckyItemVersion(int luckyItemVersion) {
        this.luckyItemVersion = luckyItemVersion;
    }

    public int getLuckyItemId() {
        return luckyItemId;
    }

    public void setLuckyItemId(int luckyItemId) {
        this.luckyItemId = luckyItemId;
    }

    public List<ActivitiesInfo> getActivitiesList() {
        return activitiesList;
    }

    public void setActivitiesList(List<ActivitiesInfo> activitiesList) {
        this.activitiesList = activitiesList;
    }

    public long getDailyGameTime() {
        return dailyGameTime;
    }

    public void setDailyGameTime(long dailyGameTime) {
        this.dailyGameTime = dailyGameTime;
    }

    public int getDailyLoginflag() {
        return dailyLoginflag;
    }

    public void setDailyLoginflag(int dailyLoginflag) {
        this.dailyLoginflag = dailyLoginflag;
    }

    public int getDailyLoginLoop() {
        return dailyLoginLoop;
    }

    public void setDailyLoginLoop(int dailyLoginLoop) {
        this.dailyLoginLoop = dailyLoginLoop;
    }
}


