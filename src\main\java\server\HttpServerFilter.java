package server;

import javax.net.ssl.SSLEngine;

import io.netty.buffer.UnpooledByteBufAllocator;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.socket.SocketChannel;
import io.netty.handler.codec.http.HttpObjectAggregator;
import io.netty.handler.codec.http.HttpRequestDecoder;
import io.netty.handler.codec.http.HttpResponseEncoder;
import io.netty.handler.ssl.SslContext;
import io.netty.handler.ssl.SslHandler;
 

/**
 * 
* Title: NettyServerFilter
* Description: Netty 服务端过滤器
* Version:1.0.0  
* <AUTHOR>
* @date 2017年10月26日
 */
public class HttpServerFilter extends ChannelInitializer<SocketChannel> {
 
    private final SslContext sslCtx;

    HttpServerFilter(SslContext sslCtx)
    {
        this.sslCtx = sslCtx;
    }
    
    @Override
     protected void initChannel(SocketChannel ch) throws Exception {

         ChannelPipeline pipeline = ch.pipeline();
        // pipeline.addLast("ssl",sslCtx.newHandler(ch.alloc()));

        //  SSLEngine sslEngine = sslCtx.newEngine(UnpooledByteBufAllocator.DEFAULT);
        //  pipeline.addLast("ssl", new SslHandler(sslEngine));        

         //处理http服务的关键handler
         pipeline.addLast("encoder",new HttpResponseEncoder());
         pipeline.addLast("decoder",new HttpRequestDecoder());
         pipeline.addLast("aggregator", new HttpObjectAggregator(10*1024*1024)); 
         pipeline.addLast("handler", new HttpServerHandler());// 服务端业务逻辑
     }
 }
