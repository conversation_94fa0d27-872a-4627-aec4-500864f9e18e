package module.synchronization;

import manager.ReportManager;
import module.callback.CallBackManager;
import module.callback.CallBackOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.MissionData;
import protocol.ProtoData;
import server.SuperProtocol;

/**
 * Created by nara on 2018/9/20.
 */
public class SyncCallBack extends CallBackManager {
    private static Logger log = LoggerFactory.getLogger(SyncCallBack.class);

    public SyncCallBack(int callBackId){
        super(callBackId);
    }

    public void execute(Object object) {
        switch (callBackId) {
            case CallBackOrder.ENTERMISSION:
                boardcastToFight(object);
                break;
            default:
                break;
        }
    }

    private void boardcastToFight(Object object){
        SuperProtocol superProtocol = (SuperProtocol)object;
        if (superProtocol.getMsgId() != ProtoData.MToS.RESPONSEENTERMISSION_VALUE){
            return;
        }
        MissionData.SRequestEnterMission sRequestEnterMission = null;
        try {
            sRequestEnterMission = MissionData.SRequestEnterMission.parseFrom(superProtocol.getContent());
        }catch (Exception e){
            return;
        }

        MissionData.ReportComeToFight.Builder builder = MissionData.ReportComeToFight.newBuilder();
        builder.setKey(sRequestEnterMission.getKey());
        RoomInfo roomInfo = (RoomInfo)this.parameter;
        for (int i = 0 ; i<roomInfo.getRoomRoleList().size() ; i++){
            RoomRoleInfo roomRoleInfo = roomInfo.getRoomRoleList().get(i);
            ReportManager.reportInfo(roomRoleInfo.getUid(), ProtoData.SToC.REPORTCOMETOFIGHT_VALUE, builder.build().toByteArray());
        }
    }
}
