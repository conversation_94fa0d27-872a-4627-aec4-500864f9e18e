package module.pvp;

import entities.PVPBaseDataEntity;
import entities.PVPPetsEntity;
import manager.MySql;

import java.util.ArrayList;
import java.util.List;

public class PVPDao {
    private static PVPDao inst = null;

    public static PVPDao getInstance() {
        if (inst == null) {
            inst = new PVPDao();
        }
        return inst;
    }

    public PVPPetsEntity GetPVPPets(String uid) {
        PVPPetsEntity pvpPetsEntity = null;
        StringBuffer sql = new StringBuffer("from PVPPetsEntity where roleUid='").append(uid).append("'");
        pvpPetsEntity = (PVPPetsEntity) MySql.queryForOne(sql.toString());
        if (pvpPetsEntity == null){
            pvpPetsEntity = new PVPPetsEntity();
            pvpPetsEntity.setRoleUid(uid);
            MySql.insert(pvpPetsEntity);
        }
        return pvpPetsEntity;
    }

    public PVPBaseDataEntity GetPVPBaseData(String uid, int rank) {
        PVPBaseDataEntity pvpBaseDataEntity = null;
        StringBuffer sql = new StringBuffer("from PVPBaseDataEntity where roleUid='").append(uid).append("'");
        pvpBaseDataEntity = (PVPBaseDataEntity) MySql.queryForOne(sql.toString());
        if (pvpBaseDataEntity == null){
            pvpBaseDataEntity = new PVPBaseDataEntity();
            pvpBaseDataEntity.setRoleUid(uid);
            pvpBaseDataEntity.setRank(rank);
            pvpBaseDataEntity.setScore(0);
            pvpBaseDataEntity.setVictory(0);
            pvpBaseDataEntity.setFail(0);
            MySql.insert(pvpBaseDataEntity);
        }
        return pvpBaseDataEntity;
    }
    public List<PVPBaseDataEntity> GetPVPBaseDataAll() {
        List<PVPBaseDataEntity> pvpBaseDataEntity = new ArrayList<>();
        StringBuffer sql = new StringBuffer("FROM PVPBaseDataEntity");
        for (Object data :
                MySql.queryForList(sql.toString())) {
            pvpBaseDataEntity.add((PVPBaseDataEntity)data);
        }
        return pvpBaseDataEntity;
    }


}
