package entities;

import javax.persistence.*;

/**
 * <AUTHOR>
 * @create 2019-05-29 15:59
 */
@Entity
@Table(name = "headball", schema = "", catalog = "super_star_fruit")
public class HeadBallEntity {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;
    private Integer is_first;
    private Integer battle_num;
    private Integer jump_num;
    private Integer jump_missionid;
    private String user_id;
    private Integer is_threestar;

    public Integer getIs_threestar() {
        return is_threestar;
    }

    public void setIs_threestar(Integer is_threestar) {
        this.is_threestar = is_threestar;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getIs_first() {
        return is_first;
    }

    public void setIs_first(Integer is_first) {
        this.is_first = is_first;
    }

    public Integer getBattle_num() {
        return battle_num;
    }

    public void setBattle_num(Integer battle_num) {
        this.battle_num = battle_num;
    }

    public Integer getJump_num() {
        return jump_num;
    }

    public void setJump_num(Integer jump_num) {
        this.jump_num = jump_num;
    }

    public Integer getJump_missionid() {
        return jump_missionid;
    }

    public void setJump_missionid(Integer jump_missionid) {
        this.jump_missionid = jump_missionid;
    }
}
