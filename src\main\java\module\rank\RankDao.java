package module.rank;

import entities.PVPBaseDataEntity;
import entities.RoleEntity;
import manager.MySql;
import module.pet.PetDao;
import module.pvp.PVPRank;
import protocol.FriendData;
import protocol.RankData;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;

public class RankDao {
    private static RankDao inst = null;

    public static RankDao getInstance() {
        if (inst == null) {
            inst = new RankDao();
        }
        return inst;
    }
    // 1
    public void GetRankingScoreList(String uid, RankData.ResponseRank.Builder builder) {
        List<PVPBaseDataEntity> pvpDataList = PVPRank.getInstance().GetPVPPlayerBaseDataArrayList();
        PVPBaseDataEntity curData = null;
        for (int i = 0; i < 50 && i < pvpDataList.size(); i++) {
            curData = pvpDataList.get(i);
            StringBuffer sql=new  StringBuffer("from RoleEntity where uid='").append(curData.getRoleUid()).append("'");
            RoleEntity roleEntity =(RoleEntity)MySql.queryForOne(sql.toString());
            RankData.Ranking.Builder ranking = GetRanking(roleEntity, curData.getScore(), i + 1, curData.getRoleUid());
            builder.addRankList(ranking);
//            if (roleEntity != null &&
//                    uid.equals(roleEntity.getUid())) {
//                builder.setUser(ranking);
//            }
        }

//        System.err.println("添加自身的Rank");
        StringBuffer sql=new  StringBuffer("from RoleEntity where uid='").append(uid).append("'");
        RoleEntity roleEntity =(RoleEntity)MySql.queryForOne(sql.toString());
        RankData.Ranking.Builder ranking = GetRanking(roleEntity,
                PVPRank.getInstance().GetPvpPlayerBaseDataHashMap().get(uid).getScore()
                , PVPRank.getInstance().GetPvpPlayerBaseDataHashMap().get(uid).getRank(),
                uid);
        builder.setUser(ranking);
    }

    private RankData.Ranking.Builder GetRanking(RoleEntity roleEntity, int score, int rank, String uid) {
        RankData.Ranking.Builder ranking = RankData.Ranking.newBuilder();
//        FriendData.Player.Builder player = FriendData.Player.newBuilder();
//
//        player.setId(roleEntity.getRoleid());
//        player.setLv(roleEntity.getLv());
//        player.setHead(roleEntity.getHead());
//        player.setName(roleEntity.getName());
        ranking.setRankNumber(rank);
        ranking.setScore(score);

        if (uid.charAt(0) == '#'){
            ranking.setLv(1);
            ranking.setId(1);
            ranking.setName(uid.split("#")[1]);

            ranking.setHead(1);
            ranking.setSex(1);
        }else {
            ranking.setLv(roleEntity.getLv());
            ranking.setHead(roleEntity.getHead());
            ranking.setName(roleEntity.getName());
            ranking.setId(roleEntity.getId());
            ranking.setSex(roleEntity.getSex());
        }


//        ranking.setPlayer(player);
        return ranking;
    }



    // 2
    public void GetRankingLevelList(String uid, RankData.ResponseRank.Builder builder) {
        StringBuilder sql = new StringBuilder("from RoleEntity");
        List<Object> roleEntity = MySql.queryForList(sql.toString());
        roleEntity.sort(new Comparator<Object>() {
            @Override
            public int compare(Object o1, Object o2) {
                RoleEntity a = (RoleEntity) o1;
                RoleEntity b = (RoleEntity) o2;
                return b.getLv() - a.getLv();
            }
        });

        // 返回50个
        for (int i = 0; i < 50 && i < roleEntity.size(); i++) {
            try {
                RoleEntity role = (RoleEntity) roleEntity.get(i);
                RankData.Ranking.Builder ranking = RankData.Ranking.newBuilder();

                ranking.setRankNumber(i+1);
                ranking.setScore(role.getLv());
                ranking.setLv(role.getLv());
                ranking.setHead(role.getHead());
                ranking.setName(role.getName());
                ranking.setId(role.getId());
                ranking.setSex(role.getSex());

                builder.addRankList(ranking);
                if (uid.equals(role.getUid())){
                    builder.setUser(ranking);
                }
            } catch (Exception e) {
                System.err.println(e);
            }
        }
         // 自己
        if (!builder.hasUser()){
            StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity role = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            RankData.Ranking.Builder ranking = RankData.Ranking.newBuilder();
            ranking.setRankNumber(51);
            ranking.setScore(role.getLv());
            ranking.setLv(role.getLv());
            ranking.setHead(role.getHead());
            ranking.setName(role.getName());
            ranking.setId(role.getId());
            ranking.setSex(role.getSex());
            builder.setUser(ranking);
        }
    }

    // 3
    public void GetRankingPetList(String uid, RankData.ResponseRank.Builder builder) {
        StringBuilder sql = new StringBuilder("from RoleEntity");
        List<Object> roleEntity = MySql.queryForList(sql.toString());
        HashMap<String, Integer> score = new HashMap<>();

        for (Object o :
                roleEntity) {
            RoleEntity role = (RoleEntity) o;
            score.put(role.getUid(), PetDao.getInstance().queryPet(role.getUid()).size());
        }

        roleEntity.sort(new Comparator<Object>() {
            @Override
            public int compare(Object o1, Object o2) {
                RoleEntity a = (RoleEntity) o1;
                RoleEntity b = (RoleEntity) o2;
                return score.get(b.getUid()) - score.get(a.getUid());
            }
        });

        // 返回50个
        for (int i = 0; i < 50 && i < roleEntity.size(); i++) {
            try {
                RoleEntity role = (RoleEntity) roleEntity.get(i);
                RankData.Ranking.Builder ranking = RankData.Ranking.newBuilder();

                ranking.setRankNumber(i+1);
                ranking.setScore(score.get(role.getUid()));
                ranking.setLv(role.getLv());
                ranking.setHead(role.getHead());
                ranking.setName(role.getName());
                ranking.setId(role.getId());
                ranking.setSex(role.getSex());

                builder.addRankList(ranking);
                if (uid.equals(role.getUid())){
                    builder.setUser(ranking);
                }
            } catch (Exception e) {
                System.err.println(e);
            }
        }
        // 自己
        if (!builder.hasUser()){
            StringBuilder stringBuilder = new StringBuilder("from RoleEntity where uid='").append(uid).append("'");
            RoleEntity role = (RoleEntity) MySql.queryForOne(stringBuilder.toString());
            RankData.Ranking.Builder ranking = RankData.Ranking.newBuilder();
            ranking.setRankNumber(51);
            ranking.setScore(score.get(role.getUid()));
            ranking.setLv(role.getLv());
            ranking.setHead(role.getHead());
            ranking.setName(role.getName());
            ranking.setId(role.getId());
            ranking.setSex(role.getSex());
            builder.setUser(ranking);
        }
    }
}
