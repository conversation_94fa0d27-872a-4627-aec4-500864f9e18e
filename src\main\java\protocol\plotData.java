// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: plnot.proto

package protocol;

public final class plotData {
  private plotData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestPlontOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 chapterd = 1;
    /**
     * <code>required int32 chapterd = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    boolean hasChapterd();
    /**
     * <code>required int32 chapterd = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    int getChapterd();

    // required int32 sectiond = 2;
    /**
     * <code>required int32 sectiond = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    boolean hasSectiond();
    /**
     * <code>required int32 sectiond = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    int getSectiond();
  }
  /**
   * Protobuf type {@code protocol.RequestPlont}
   *
   * <pre>
   *1320
   * </pre>
   */
  public static final class RequestPlont extends
      com.google.protobuf.GeneratedMessage
      implements RequestPlontOrBuilder {
    // Use RequestPlont.newBuilder() to construct.
    private RequestPlont(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestPlont(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestPlont defaultInstance;
    public static RequestPlont getDefaultInstance() {
      return defaultInstance;
    }

    public RequestPlont getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestPlont(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              chapterd_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              sectiond_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.plotData.internal_static_protocol_RequestPlont_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.plotData.internal_static_protocol_RequestPlont_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.plotData.RequestPlont.class, protocol.plotData.RequestPlont.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestPlont> PARSER =
        new com.google.protobuf.AbstractParser<RequestPlont>() {
      public RequestPlont parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestPlont(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestPlont> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 chapterd = 1;
    public static final int CHAPTERD_FIELD_NUMBER = 1;
    private int chapterd_;
    /**
     * <code>required int32 chapterd = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    public boolean hasChapterd() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 chapterd = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    public int getChapterd() {
      return chapterd_;
    }

    // required int32 sectiond = 2;
    public static final int SECTIOND_FIELD_NUMBER = 2;
    private int sectiond_;
    /**
     * <code>required int32 sectiond = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    public boolean hasSectiond() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 sectiond = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    public int getSectiond() {
      return sectiond_;
    }

    private void initFields() {
      chapterd_ = 0;
      sectiond_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasChapterd()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSectiond()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, chapterd_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, sectiond_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chapterd_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, sectiond_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.plotData.RequestPlont parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.RequestPlont parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.RequestPlont parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.RequestPlont parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.RequestPlont parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.RequestPlont parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.plotData.RequestPlont parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.plotData.RequestPlont parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.plotData.RequestPlont parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.RequestPlont parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.plotData.RequestPlont prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestPlont}
     *
     * <pre>
     *1320
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.plotData.RequestPlontOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.plotData.internal_static_protocol_RequestPlont_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.plotData.internal_static_protocol_RequestPlont_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.plotData.RequestPlont.class, protocol.plotData.RequestPlont.Builder.class);
      }

      // Construct using protocol.plotData.RequestPlont.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        chapterd_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        sectiond_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.plotData.internal_static_protocol_RequestPlont_descriptor;
      }

      public protocol.plotData.RequestPlont getDefaultInstanceForType() {
        return protocol.plotData.RequestPlont.getDefaultInstance();
      }

      public protocol.plotData.RequestPlont build() {
        protocol.plotData.RequestPlont result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.plotData.RequestPlont buildPartial() {
        protocol.plotData.RequestPlont result = new protocol.plotData.RequestPlont(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.chapterd_ = chapterd_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.sectiond_ = sectiond_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.plotData.RequestPlont) {
          return mergeFrom((protocol.plotData.RequestPlont)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.plotData.RequestPlont other) {
        if (other == protocol.plotData.RequestPlont.getDefaultInstance()) return this;
        if (other.hasChapterd()) {
          setChapterd(other.getChapterd());
        }
        if (other.hasSectiond()) {
          setSectiond(other.getSectiond());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasChapterd()) {
          
          return false;
        }
        if (!hasSectiond()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.plotData.RequestPlont parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.plotData.RequestPlont) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 chapterd = 1;
      private int chapterd_ ;
      /**
       * <code>required int32 chapterd = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public boolean hasChapterd() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 chapterd = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public int getChapterd() {
        return chapterd_;
      }
      /**
       * <code>required int32 chapterd = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public Builder setChapterd(int value) {
        bitField0_ |= 0x00000001;
        chapterd_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 chapterd = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public Builder clearChapterd() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterd_ = 0;
        onChanged();
        return this;
      }

      // required int32 sectiond = 2;
      private int sectiond_ ;
      /**
       * <code>required int32 sectiond = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public boolean hasSectiond() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 sectiond = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public int getSectiond() {
        return sectiond_;
      }
      /**
       * <code>required int32 sectiond = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public Builder setSectiond(int value) {
        bitField0_ |= 0x00000002;
        sectiond_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 sectiond = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public Builder clearSectiond() {
        bitField0_ = (bitField0_ & ~0x00000002);
        sectiond_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestPlont)
    }

    static {
      defaultInstance = new RequestPlont(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestPlont)
  }

  public interface ResponsePlontOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.ResponsePlont}
   *
   * <pre>
   *2320
   * </pre>
   */
  public static final class ResponsePlont extends
      com.google.protobuf.GeneratedMessage
      implements ResponsePlontOrBuilder {
    // Use ResponsePlont.newBuilder() to construct.
    private ResponsePlont(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponsePlont(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponsePlont defaultInstance;
    public static ResponsePlont getDefaultInstance() {
      return defaultInstance;
    }

    public ResponsePlont getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponsePlont(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.plotData.internal_static_protocol_ResponsePlont_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.plotData.internal_static_protocol_ResponsePlont_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.plotData.ResponsePlont.class, protocol.plotData.ResponsePlont.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponsePlont> PARSER =
        new com.google.protobuf.AbstractParser<ResponsePlont>() {
      public ResponsePlont parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponsePlont(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponsePlont> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.plotData.ResponsePlont parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.ResponsePlont parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.ResponsePlont parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.ResponsePlont parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.ResponsePlont parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.ResponsePlont parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.plotData.ResponsePlont parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.plotData.ResponsePlont parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.plotData.ResponsePlont parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.ResponsePlont parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.plotData.ResponsePlont prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponsePlont}
     *
     * <pre>
     *2320
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.plotData.ResponsePlontOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.plotData.internal_static_protocol_ResponsePlont_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.plotData.internal_static_protocol_ResponsePlont_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.plotData.ResponsePlont.class, protocol.plotData.ResponsePlont.Builder.class);
      }

      // Construct using protocol.plotData.ResponsePlont.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.plotData.internal_static_protocol_ResponsePlont_descriptor;
      }

      public protocol.plotData.ResponsePlont getDefaultInstanceForType() {
        return protocol.plotData.ResponsePlont.getDefaultInstance();
      }

      public protocol.plotData.ResponsePlont build() {
        protocol.plotData.ResponsePlont result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.plotData.ResponsePlont buildPartial() {
        protocol.plotData.ResponsePlont result = new protocol.plotData.ResponsePlont(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.plotData.ResponsePlont) {
          return mergeFrom((protocol.plotData.ResponsePlont)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.plotData.ResponsePlont other) {
        if (other == protocol.plotData.ResponsePlont.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.plotData.ResponsePlont parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.plotData.ResponsePlont) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponsePlont)
    }

    static {
      defaultInstance = new ResponsePlont(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponsePlont)
  }

  public interface RequestChapOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestChap}
   *
   * <pre>
   *1321
   * </pre>
   */
  public static final class RequestChap extends
      com.google.protobuf.GeneratedMessage
      implements RequestChapOrBuilder {
    // Use RequestChap.newBuilder() to construct.
    private RequestChap(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestChap(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestChap defaultInstance;
    public static RequestChap getDefaultInstance() {
      return defaultInstance;
    }

    public RequestChap getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestChap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.plotData.internal_static_protocol_RequestChap_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.plotData.internal_static_protocol_RequestChap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.plotData.RequestChap.class, protocol.plotData.RequestChap.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestChap> PARSER =
        new com.google.protobuf.AbstractParser<RequestChap>() {
      public RequestChap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestChap(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestChap> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.plotData.RequestChap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.RequestChap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.RequestChap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.RequestChap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.RequestChap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.RequestChap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.plotData.RequestChap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.plotData.RequestChap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.plotData.RequestChap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.RequestChap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.plotData.RequestChap prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestChap}
     *
     * <pre>
     *1321
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.plotData.RequestChapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.plotData.internal_static_protocol_RequestChap_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.plotData.internal_static_protocol_RequestChap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.plotData.RequestChap.class, protocol.plotData.RequestChap.Builder.class);
      }

      // Construct using protocol.plotData.RequestChap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.plotData.internal_static_protocol_RequestChap_descriptor;
      }

      public protocol.plotData.RequestChap getDefaultInstanceForType() {
        return protocol.plotData.RequestChap.getDefaultInstance();
      }

      public protocol.plotData.RequestChap build() {
        protocol.plotData.RequestChap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.plotData.RequestChap buildPartial() {
        protocol.plotData.RequestChap result = new protocol.plotData.RequestChap(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.plotData.RequestChap) {
          return mergeFrom((protocol.plotData.RequestChap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.plotData.RequestChap other) {
        if (other == protocol.plotData.RequestChap.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.plotData.RequestChap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.plotData.RequestChap) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestChap)
    }

    static {
      defaultInstance = new RequestChap(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestChap)
  }

  public interface ResponseChapOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 chapter = 1;
    /**
     * <code>required int32 chapter = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    boolean hasChapter();
    /**
     * <code>required int32 chapter = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    int getChapter();

    // required int32 section = 2;
    /**
     * <code>required int32 section = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    boolean hasSection();
    /**
     * <code>required int32 section = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    int getSection();
  }
  /**
   * Protobuf type {@code protocol.ResponseChap}
   *
   * <pre>
   *2321
   * </pre>
   */
  public static final class ResponseChap extends
      com.google.protobuf.GeneratedMessage
      implements ResponseChapOrBuilder {
    // Use ResponseChap.newBuilder() to construct.
    private ResponseChap(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseChap(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseChap defaultInstance;
    public static ResponseChap getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseChap getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseChap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              chapter_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              section_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.plotData.internal_static_protocol_ResponseChap_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.plotData.internal_static_protocol_ResponseChap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.plotData.ResponseChap.class, protocol.plotData.ResponseChap.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseChap> PARSER =
        new com.google.protobuf.AbstractParser<ResponseChap>() {
      public ResponseChap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseChap(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseChap> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 chapter = 1;
    public static final int CHAPTER_FIELD_NUMBER = 1;
    private int chapter_;
    /**
     * <code>required int32 chapter = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    public boolean hasChapter() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 chapter = 1;</code>
     *
     * <pre>
     *当前章数
     * </pre>
     */
    public int getChapter() {
      return chapter_;
    }

    // required int32 section = 2;
    public static final int SECTION_FIELD_NUMBER = 2;
    private int section_;
    /**
     * <code>required int32 section = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    public boolean hasSection() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 section = 2;</code>
     *
     * <pre>
     *当前节数
     * </pre>
     */
    public int getSection() {
      return section_;
    }

    private void initFields() {
      chapter_ = 0;
      section_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasChapter()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSection()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, chapter_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, section_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, chapter_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, section_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.plotData.ResponseChap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.ResponseChap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.ResponseChap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.plotData.ResponseChap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.plotData.ResponseChap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.ResponseChap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.plotData.ResponseChap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.plotData.ResponseChap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.plotData.ResponseChap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.plotData.ResponseChap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.plotData.ResponseChap prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseChap}
     *
     * <pre>
     *2321
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.plotData.ResponseChapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.plotData.internal_static_protocol_ResponseChap_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.plotData.internal_static_protocol_ResponseChap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.plotData.ResponseChap.class, protocol.plotData.ResponseChap.Builder.class);
      }

      // Construct using protocol.plotData.ResponseChap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        chapter_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        section_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.plotData.internal_static_protocol_ResponseChap_descriptor;
      }

      public protocol.plotData.ResponseChap getDefaultInstanceForType() {
        return protocol.plotData.ResponseChap.getDefaultInstance();
      }

      public protocol.plotData.ResponseChap build() {
        protocol.plotData.ResponseChap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.plotData.ResponseChap buildPartial() {
        protocol.plotData.ResponseChap result = new protocol.plotData.ResponseChap(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.chapter_ = chapter_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.section_ = section_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.plotData.ResponseChap) {
          return mergeFrom((protocol.plotData.ResponseChap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.plotData.ResponseChap other) {
        if (other == protocol.plotData.ResponseChap.getDefaultInstance()) return this;
        if (other.hasChapter()) {
          setChapter(other.getChapter());
        }
        if (other.hasSection()) {
          setSection(other.getSection());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasChapter()) {
          
          return false;
        }
        if (!hasSection()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.plotData.ResponseChap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.plotData.ResponseChap) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 chapter = 1;
      private int chapter_ ;
      /**
       * <code>required int32 chapter = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public boolean hasChapter() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 chapter = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public int getChapter() {
        return chapter_;
      }
      /**
       * <code>required int32 chapter = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public Builder setChapter(int value) {
        bitField0_ |= 0x00000001;
        chapter_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 chapter = 1;</code>
       *
       * <pre>
       *当前章数
       * </pre>
       */
      public Builder clearChapter() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapter_ = 0;
        onChanged();
        return this;
      }

      // required int32 section = 2;
      private int section_ ;
      /**
       * <code>required int32 section = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public boolean hasSection() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 section = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public int getSection() {
        return section_;
      }
      /**
       * <code>required int32 section = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public Builder setSection(int value) {
        bitField0_ |= 0x00000002;
        section_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 section = 2;</code>
       *
       * <pre>
       *当前节数
       * </pre>
       */
      public Builder clearSection() {
        bitField0_ = (bitField0_ & ~0x00000002);
        section_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseChap)
    }

    static {
      defaultInstance = new ResponseChap(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseChap)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestPlont_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestPlont_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponsePlont_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponsePlont_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestChap_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestChap_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseChap_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseChap_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013plnot.proto\022\010protocol\032\013proto.proto\"2\n\014" +
      "RequestPlont\022\020\n\010chapterd\030\001 \002(\005\022\020\n\010sectio" +
      "nd\030\002 \002(\005\"\017\n\rResponsePlont\"\r\n\013RequestChap" +
      "\"0\n\014ResponseChap\022\017\n\007chapter\030\001 \002(\005\022\017\n\007sec" +
      "tion\030\002 \002(\005B\nB\010plotData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestPlont_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestPlont_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestPlont_descriptor,
              new java.lang.String[] { "Chapterd", "Sectiond", });
          internal_static_protocol_ResponsePlont_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponsePlont_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponsePlont_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_RequestChap_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestChap_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestChap_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseChap_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseChap_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseChap_descriptor,
              new java.lang.String[] { "Chapter", "Section", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ProtoData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
