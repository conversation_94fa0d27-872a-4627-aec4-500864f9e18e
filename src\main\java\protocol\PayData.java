// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: pay.proto

package protocol;

public final class PayData {
  private PayData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface RequestChoosePayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 id = 1;
    /**
     * <code>required int32 id = 1;</code>
     */
    boolean hasId();
    /**
     * <code>required int32 id = 1;</code>
     */
    int getId();

    // required int32 platform = 2;
    /**
     * <code>required int32 platform = 2;</code>
     *
     * <pre>
     *0 苹果，1 google 3 微信 4 支付宝	
     * </pre>
     */
    boolean hasPlatform();
    /**
     * <code>required int32 platform = 2;</code>
     *
     * <pre>
     *0 苹果，1 google 3 微信 4 支付宝	
     * </pre>
     */
    int getPlatform();
  }
  /**
   * Protobuf type {@code protocol.RequestChoosePay}
   */
  public static final class RequestChoosePay extends
      com.google.protobuf.GeneratedMessage
      implements RequestChoosePayOrBuilder {
    // Use RequestChoosePay.newBuilder() to construct.
    private RequestChoosePay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestChoosePay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestChoosePay defaultInstance;
    public static RequestChoosePay getDefaultInstance() {
      return defaultInstance;
    }

    public RequestChoosePay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestChoosePay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              id_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              platform_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_RequestChoosePay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_RequestChoosePay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.RequestChoosePay.class, protocol.PayData.RequestChoosePay.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestChoosePay> PARSER =
        new com.google.protobuf.AbstractParser<RequestChoosePay>() {
      public RequestChoosePay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestChoosePay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestChoosePay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 id = 1;
    public static final int ID_FIELD_NUMBER = 1;
    private int id_;
    /**
     * <code>required int32 id = 1;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 id = 1;</code>
     */
    public int getId() {
      return id_;
    }

    // required int32 platform = 2;
    public static final int PLATFORM_FIELD_NUMBER = 2;
    private int platform_;
    /**
     * <code>required int32 platform = 2;</code>
     *
     * <pre>
     *0 苹果，1 google 3 微信 4 支付宝	
     * </pre>
     */
    public boolean hasPlatform() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 platform = 2;</code>
     *
     * <pre>
     *0 苹果，1 google 3 微信 4 支付宝	
     * </pre>
     */
    public int getPlatform() {
      return platform_;
    }

    private void initFields() {
      id_ = 0;
      platform_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPlatform()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, platform_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, id_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, platform_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.RequestChoosePay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestChoosePay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.RequestChoosePay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestChoosePay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.RequestChoosePay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestChoosePay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.RequestChoosePayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_RequestChoosePay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_RequestChoosePay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.RequestChoosePay.class, protocol.PayData.RequestChoosePay.Builder.class);
      }

      // Construct using protocol.PayData.RequestChoosePay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        platform_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_RequestChoosePay_descriptor;
      }

      public protocol.PayData.RequestChoosePay getDefaultInstanceForType() {
        return protocol.PayData.RequestChoosePay.getDefaultInstance();
      }

      public protocol.PayData.RequestChoosePay build() {
        protocol.PayData.RequestChoosePay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.RequestChoosePay buildPartial() {
        protocol.PayData.RequestChoosePay result = new protocol.PayData.RequestChoosePay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.platform_ = platform_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.RequestChoosePay) {
          return mergeFrom((protocol.PayData.RequestChoosePay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.RequestChoosePay other) {
        if (other == protocol.PayData.RequestChoosePay.getDefaultInstance()) return this;
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasPlatform()) {
          setPlatform(other.getPlatform());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasId()) {
          
          return false;
        }
        if (!hasPlatform()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.RequestChoosePay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.RequestChoosePay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 id = 1;
      private int id_ ;
      /**
       * <code>required int32 id = 1;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 id = 1;</code>
       */
      public int getId() {
        return id_;
      }
      /**
       * <code>required int32 id = 1;</code>
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000001;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 id = 1;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }

      // required int32 platform = 2;
      private int platform_ ;
      /**
       * <code>required int32 platform = 2;</code>
       *
       * <pre>
       *0 苹果，1 google 3 微信 4 支付宝	
       * </pre>
       */
      public boolean hasPlatform() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 platform = 2;</code>
       *
       * <pre>
       *0 苹果，1 google 3 微信 4 支付宝	
       * </pre>
       */
      public int getPlatform() {
        return platform_;
      }
      /**
       * <code>required int32 platform = 2;</code>
       *
       * <pre>
       *0 苹果，1 google 3 微信 4 支付宝	
       * </pre>
       */
      public Builder setPlatform(int value) {
        bitField0_ |= 0x00000002;
        platform_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 platform = 2;</code>
       *
       * <pre>
       *0 苹果，1 google 3 微信 4 支付宝	
       * </pre>
       */
      public Builder clearPlatform() {
        bitField0_ = (bitField0_ & ~0x00000002);
        platform_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestChoosePay)
    }

    static {
      defaultInstance = new RequestChoosePay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestChoosePay)
  }

  public interface ResponseChoosePayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // required int32 id = 2;
    /**
     * <code>required int32 id = 2;</code>
     */
    boolean hasId();
    /**
     * <code>required int32 id = 2;</code>
     */
    int getId();

    // optional string transactionID = 3;
    /**
     * <code>optional string transactionID = 3;</code>
     */
    boolean hasTransactionID();
    /**
     * <code>optional string transactionID = 3;</code>
     */
    java.lang.String getTransactionID();
    /**
     * <code>optional string transactionID = 3;</code>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // optional string result = 4;
    /**
     * <code>optional string result = 4;</code>
     */
    boolean hasResult();
    /**
     * <code>optional string result = 4;</code>
     */
    java.lang.String getResult();
    /**
     * <code>optional string result = 4;</code>
     */
    com.google.protobuf.ByteString
        getResultBytes();

    // optional int32 platform = 5;
    /**
     * <code>optional int32 platform = 5;</code>
     */
    boolean hasPlatform();
    /**
     * <code>optional int32 platform = 5;</code>
     */
    int getPlatform();
  }
  /**
   * Protobuf type {@code protocol.ResponseChoosePay}
   */
  public static final class ResponseChoosePay extends
      com.google.protobuf.GeneratedMessage
      implements ResponseChoosePayOrBuilder {
    // Use ResponseChoosePay.newBuilder() to construct.
    private ResponseChoosePay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseChoosePay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseChoosePay defaultInstance;
    public static ResponseChoosePay getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseChoosePay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseChoosePay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              id_ = input.readInt32();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              transactionID_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              result_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              platform_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseChoosePay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseChoosePay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseChoosePay.class, protocol.PayData.ResponseChoosePay.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseChoosePay> PARSER =
        new com.google.protobuf.AbstractParser<ResponseChoosePay>() {
      public ResponseChoosePay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseChoosePay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseChoosePay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 id = 2;
    public static final int ID_FIELD_NUMBER = 2;
    private int id_;
    /**
     * <code>required int32 id = 2;</code>
     */
    public boolean hasId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 id = 2;</code>
     */
    public int getId() {
      return id_;
    }

    // optional string transactionID = 3;
    public static final int TRANSACTIONID_FIELD_NUMBER = 3;
    private java.lang.Object transactionID_;
    /**
     * <code>optional string transactionID = 3;</code>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string transactionID = 3;</code>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string transactionID = 3;</code>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional string result = 4;
    public static final int RESULT_FIELD_NUMBER = 4;
    private java.lang.Object result_;
    /**
     * <code>optional string result = 4;</code>
     */
    public boolean hasResult() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional string result = 4;</code>
     */
    public java.lang.String getResult() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          result_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string result = 4;</code>
     */
    public com.google.protobuf.ByteString
        getResultBytes() {
      java.lang.Object ref = result_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        result_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 platform = 5;
    public static final int PLATFORM_FIELD_NUMBER = 5;
    private int platform_;
    /**
     * <code>optional int32 platform = 5;</code>
     */
    public boolean hasPlatform() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>optional int32 platform = 5;</code>
     */
    public int getPlatform() {
      return platform_;
    }

    private void initFields() {
      errorId_ = 0;
      id_ = 0;
      transactionID_ = "";
      result_ = "";
      platform_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, id_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getResultBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, platform_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, id_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getResultBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, platform_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseChoosePay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseChoosePay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseChoosePay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseChoosePay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseChoosePay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseChoosePay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseChoosePayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseChoosePay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseChoosePay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseChoosePay.class, protocol.PayData.ResponseChoosePay.Builder.class);
      }

      // Construct using protocol.PayData.ResponseChoosePay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        result_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        platform_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseChoosePay_descriptor;
      }

      public protocol.PayData.ResponseChoosePay getDefaultInstanceForType() {
        return protocol.PayData.ResponseChoosePay.getDefaultInstance();
      }

      public protocol.PayData.ResponseChoosePay build() {
        protocol.PayData.ResponseChoosePay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseChoosePay buildPartial() {
        protocol.PayData.ResponseChoosePay result = new protocol.PayData.ResponseChoosePay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.id_ = id_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.result_ = result_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.platform_ = platform_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseChoosePay) {
          return mergeFrom((protocol.PayData.ResponseChoosePay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseChoosePay other) {
        if (other == protocol.PayData.ResponseChoosePay.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasId()) {
          setId(other.getId());
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000004;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasResult()) {
          bitField0_ |= 0x00000008;
          result_ = other.result_;
          onChanged();
        }
        if (other.hasPlatform()) {
          setPlatform(other.getPlatform());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasId()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseChoosePay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseChoosePay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 id = 2;
      private int id_ ;
      /**
       * <code>required int32 id = 2;</code>
       */
      public boolean hasId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 id = 2;</code>
       */
      public int getId() {
        return id_;
      }
      /**
       * <code>required int32 id = 2;</code>
       */
      public Builder setId(int value) {
        bitField0_ |= 0x00000002;
        id_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 id = 2;</code>
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        id_ = 0;
        onChanged();
        return this;
      }

      // optional string transactionID = 3;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>optional string transactionID = 3;</code>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>optional string transactionID = 3;</code>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string transactionID = 3;</code>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string transactionID = 3;</code>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 3;</code>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000004);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 3;</code>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // optional string result = 4;
      private java.lang.Object result_ = "";
      /**
       * <code>optional string result = 4;</code>
       */
      public boolean hasResult() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string result = 4;</code>
       */
      public java.lang.String getResult() {
        java.lang.Object ref = result_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          result_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string result = 4;</code>
       */
      public com.google.protobuf.ByteString
          getResultBytes() {
        java.lang.Object ref = result_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          result_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string result = 4;</code>
       */
      public Builder setResult(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        result_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 4;</code>
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000008);
        result_ = getDefaultInstance().getResult();
        onChanged();
        return this;
      }
      /**
       * <code>optional string result = 4;</code>
       */
      public Builder setResultBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        result_ = value;
        onChanged();
        return this;
      }

      // optional int32 platform = 5;
      private int platform_ ;
      /**
       * <code>optional int32 platform = 5;</code>
       */
      public boolean hasPlatform() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 platform = 5;</code>
       */
      public int getPlatform() {
        return platform_;
      }
      /**
       * <code>optional int32 platform = 5;</code>
       */
      public Builder setPlatform(int value) {
        bitField0_ |= 0x00000010;
        platform_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 platform = 5;</code>
       */
      public Builder clearPlatform() {
        bitField0_ = (bitField0_ & ~0x00000010);
        platform_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseChoosePay)
    }

    static {
      defaultInstance = new ResponseChoosePay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseChoosePay)
  }

  public interface RequestSubmitPayBackOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string transactionID = 1;
    /**
     * <code>required string transactionID = 1;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>required string transactionID = 1;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>required string transactionID = 1;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // required string receipt = 2;
    /**
     * <code>required string receipt = 2;</code>
     *
     * <pre>
     *苹果返回消息
     * </pre>
     */
    boolean hasReceipt();
    /**
     * <code>required string receipt = 2;</code>
     *
     * <pre>
     *苹果返回消息
     * </pre>
     */
    java.lang.String getReceipt();
    /**
     * <code>required string receipt = 2;</code>
     *
     * <pre>
     *苹果返回消息
     * </pre>
     */
    com.google.protobuf.ByteString
        getReceiptBytes();
  }
  /**
   * Protobuf type {@code protocol.RequestSubmitPayBack}
   */
  public static final class RequestSubmitPayBack extends
      com.google.protobuf.GeneratedMessage
      implements RequestSubmitPayBackOrBuilder {
    // Use RequestSubmitPayBack.newBuilder() to construct.
    private RequestSubmitPayBack(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestSubmitPayBack(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestSubmitPayBack defaultInstance;
    public static RequestSubmitPayBack getDefaultInstance() {
      return defaultInstance;
    }

    public RequestSubmitPayBack getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestSubmitPayBack(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              transactionID_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              receipt_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_RequestSubmitPayBack_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_RequestSubmitPayBack_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.RequestSubmitPayBack.class, protocol.PayData.RequestSubmitPayBack.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestSubmitPayBack> PARSER =
        new com.google.protobuf.AbstractParser<RequestSubmitPayBack>() {
      public RequestSubmitPayBack parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestSubmitPayBack(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestSubmitPayBack> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string transactionID = 1;
    public static final int TRANSACTIONID_FIELD_NUMBER = 1;
    private java.lang.Object transactionID_;
    /**
     * <code>required string transactionID = 1;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string transactionID = 1;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string transactionID = 1;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string receipt = 2;
    public static final int RECEIPT_FIELD_NUMBER = 2;
    private java.lang.Object receipt_;
    /**
     * <code>required string receipt = 2;</code>
     *
     * <pre>
     *苹果返回消息
     * </pre>
     */
    public boolean hasReceipt() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string receipt = 2;</code>
     *
     * <pre>
     *苹果返回消息
     * </pre>
     */
    public java.lang.String getReceipt() {
      java.lang.Object ref = receipt_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          receipt_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string receipt = 2;</code>
     *
     * <pre>
     *苹果返回消息
     * </pre>
     */
    public com.google.protobuf.ByteString
        getReceiptBytes() {
      java.lang.Object ref = receipt_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        receipt_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      transactionID_ = "";
      receipt_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasTransactionID()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasReceipt()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getReceiptBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getReceiptBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.RequestSubmitPayBack parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestSubmitPayBack parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.RequestSubmitPayBack parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestSubmitPayBack parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.RequestSubmitPayBack prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestSubmitPayBack}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.RequestSubmitPayBackOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_RequestSubmitPayBack_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_RequestSubmitPayBack_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.RequestSubmitPayBack.class, protocol.PayData.RequestSubmitPayBack.Builder.class);
      }

      // Construct using protocol.PayData.RequestSubmitPayBack.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        receipt_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_RequestSubmitPayBack_descriptor;
      }

      public protocol.PayData.RequestSubmitPayBack getDefaultInstanceForType() {
        return protocol.PayData.RequestSubmitPayBack.getDefaultInstance();
      }

      public protocol.PayData.RequestSubmitPayBack build() {
        protocol.PayData.RequestSubmitPayBack result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.RequestSubmitPayBack buildPartial() {
        protocol.PayData.RequestSubmitPayBack result = new protocol.PayData.RequestSubmitPayBack(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.receipt_ = receipt_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.RequestSubmitPayBack) {
          return mergeFrom((protocol.PayData.RequestSubmitPayBack)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.RequestSubmitPayBack other) {
        if (other == protocol.PayData.RequestSubmitPayBack.getDefaultInstance()) return this;
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000001;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasReceipt()) {
          bitField0_ |= 0x00000002;
          receipt_ = other.receipt_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasTransactionID()) {
          
          return false;
        }
        if (!hasReceipt()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.RequestSubmitPayBack parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.RequestSubmitPayBack) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string transactionID = 1;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>required string transactionID = 1;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string transactionID = 1;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string transactionID = 1;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string transactionID = 1;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string transactionID = 1;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000001);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>required string transactionID = 1;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // required string receipt = 2;
      private java.lang.Object receipt_ = "";
      /**
       * <code>required string receipt = 2;</code>
       *
       * <pre>
       *苹果返回消息
       * </pre>
       */
      public boolean hasReceipt() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string receipt = 2;</code>
       *
       * <pre>
       *苹果返回消息
       * </pre>
       */
      public java.lang.String getReceipt() {
        java.lang.Object ref = receipt_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          receipt_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string receipt = 2;</code>
       *
       * <pre>
       *苹果返回消息
       * </pre>
       */
      public com.google.protobuf.ByteString
          getReceiptBytes() {
        java.lang.Object ref = receipt_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          receipt_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string receipt = 2;</code>
       *
       * <pre>
       *苹果返回消息
       * </pre>
       */
      public Builder setReceipt(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        receipt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string receipt = 2;</code>
       *
       * <pre>
       *苹果返回消息
       * </pre>
       */
      public Builder clearReceipt() {
        bitField0_ = (bitField0_ & ~0x00000002);
        receipt_ = getDefaultInstance().getReceipt();
        onChanged();
        return this;
      }
      /**
       * <code>required string receipt = 2;</code>
       *
       * <pre>
       *苹果返回消息
       * </pre>
       */
      public Builder setReceiptBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        receipt_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestSubmitPayBack)
    }

    static {
      defaultInstance = new RequestSubmitPayBack(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestSubmitPayBack)
  }

  public interface ResponseSubmitPayBackOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // optional int32 successId = 2;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    boolean hasSuccessId();
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    int getSuccessId();

    // repeated .protocol.Item item = 3;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // optional string transactionID = 4;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // optional int32 firstRecharge = 5;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    boolean hasFirstRecharge();
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    int getFirstRecharge();
  }
  /**
   * Protobuf type {@code protocol.ResponseSubmitPayBack}
   */
  public static final class ResponseSubmitPayBack extends
      com.google.protobuf.GeneratedMessage
      implements ResponseSubmitPayBackOrBuilder {
    // Use ResponseSubmitPayBack.newBuilder() to construct.
    private ResponseSubmitPayBack(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseSubmitPayBack(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseSubmitPayBack defaultInstance;
    public static ResponseSubmitPayBack getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseSubmitPayBack getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseSubmitPayBack(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              successId_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000004;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              bitField0_ |= 0x00000004;
              transactionID_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              firstRecharge_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseSubmitPayBack_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseSubmitPayBack_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseSubmitPayBack.class, protocol.PayData.ResponseSubmitPayBack.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseSubmitPayBack> PARSER =
        new com.google.protobuf.AbstractParser<ResponseSubmitPayBack>() {
      public ResponseSubmitPayBack parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseSubmitPayBack(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseSubmitPayBack> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // optional int32 successId = 2;
    public static final int SUCCESSID_FIELD_NUMBER = 2;
    private int successId_;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public boolean hasSuccessId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public int getSuccessId() {
      return successId_;
    }

    // repeated .protocol.Item item = 3;
    public static final int ITEM_FIELD_NUMBER = 3;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // optional string transactionID = 4;
    public static final int TRANSACTIONID_FIELD_NUMBER = 4;
    private java.lang.Object transactionID_;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 firstRecharge = 5;
    public static final int FIRSTRECHARGE_FIELD_NUMBER = 5;
    private int firstRecharge_;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    public boolean hasFirstRecharge() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    public int getFirstRecharge() {
      return firstRecharge_;
    }

    private void initFields() {
      errorId_ = 0;
      successId_ = 0;
      item_ = java.util.Collections.emptyList();
      transactionID_ = "";
      firstRecharge_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(5, firstRecharge_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, firstRecharge_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseSubmitPayBack parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseSubmitPayBack parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseSubmitPayBack prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseSubmitPayBack}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseSubmitPayBackOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseSubmitPayBack_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseSubmitPayBack_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseSubmitPayBack.class, protocol.PayData.ResponseSubmitPayBack.Builder.class);
      }

      // Construct using protocol.PayData.ResponseSubmitPayBack.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        successId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          itemBuilder_.clear();
        }
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        firstRecharge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseSubmitPayBack_descriptor;
      }

      public protocol.PayData.ResponseSubmitPayBack getDefaultInstanceForType() {
        return protocol.PayData.ResponseSubmitPayBack.getDefaultInstance();
      }

      public protocol.PayData.ResponseSubmitPayBack build() {
        protocol.PayData.ResponseSubmitPayBack result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseSubmitPayBack buildPartial() {
        protocol.PayData.ResponseSubmitPayBack result = new protocol.PayData.ResponseSubmitPayBack(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.successId_ = successId_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        result.firstRecharge_ = firstRecharge_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseSubmitPayBack) {
          return mergeFrom((protocol.PayData.ResponseSubmitPayBack)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseSubmitPayBack other) {
        if (other == protocol.PayData.ResponseSubmitPayBack.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSuccessId()) {
          setSuccessId(other.getSuccessId());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000008;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasFirstRecharge()) {
          setFirstRecharge(other.getFirstRecharge());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseSubmitPayBack parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseSubmitPayBack) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // optional int32 successId = 2;
      private int successId_ ;
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public boolean hasSuccessId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public int getSuccessId() {
        return successId_;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder setSuccessId(int value) {
        bitField0_ |= 0x00000002;
        successId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder clearSuccessId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        successId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 3;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // optional string transactionID = 4;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000008);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // optional int32 firstRecharge = 5;
      private int firstRecharge_ ;
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public boolean hasFirstRecharge() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public int getFirstRecharge() {
        return firstRecharge_;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public Builder setFirstRecharge(int value) {
        bitField0_ |= 0x00000010;
        firstRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public Builder clearFirstRecharge() {
        bitField0_ = (bitField0_ & ~0x00000010);
        firstRecharge_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseSubmitPayBack)
    }

    static {
      defaultInstance = new ResponseSubmitPayBack(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseSubmitPayBack)
  }

  public interface RequestGooglePayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string OrderId = 1;
    /**
     * <code>required string OrderId = 1;</code>
     *
     * <pre>
     *google订单号
     * </pre>
     */
    boolean hasOrderId();
    /**
     * <code>required string OrderId = 1;</code>
     *
     * <pre>
     *google订单号
     * </pre>
     */
    java.lang.String getOrderId();
    /**
     * <code>required string OrderId = 1;</code>
     *
     * <pre>
     *google订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getOrderIdBytes();

    // required string ProductId = 2;
    /**
     * <code>required string ProductId = 2;</code>
     *
     * <pre>
     *google商品ID
     * </pre>
     */
    boolean hasProductId();
    /**
     * <code>required string ProductId = 2;</code>
     *
     * <pre>
     *google商品ID
     * </pre>
     */
    java.lang.String getProductId();
    /**
     * <code>required string ProductId = 2;</code>
     *
     * <pre>
     *google商品ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getProductIdBytes();

    // required string PurchaseToken = 3;
    /**
     * <code>required string PurchaseToken = 3;</code>
     *
     * <pre>
     *google token
     * </pre>
     */
    boolean hasPurchaseToken();
    /**
     * <code>required string PurchaseToken = 3;</code>
     *
     * <pre>
     *google token
     * </pre>
     */
    java.lang.String getPurchaseToken();
    /**
     * <code>required string PurchaseToken = 3;</code>
     *
     * <pre>
     *google token
     * </pre>
     */
    com.google.protobuf.ByteString
        getPurchaseTokenBytes();

    // required string cp_order = 4;
    /**
     * <code>required string cp_order = 4;</code>
     *
     * <pre>
     *游戏服务器的订单号
     * </pre>
     */
    boolean hasCpOrder();
    /**
     * <code>required string cp_order = 4;</code>
     *
     * <pre>
     *游戏服务器的订单号
     * </pre>
     */
    java.lang.String getCpOrder();
    /**
     * <code>required string cp_order = 4;</code>
     *
     * <pre>
     *游戏服务器的订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getCpOrderBytes();

    // required int32 purchaseState = 5;
    /**
     * <code>required int32 purchaseState = 5;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPurchaseState();
    /**
     * <code>required int32 purchaseState = 5;</code>
     *
     * <pre>
     * </pre>
     */
    int getPurchaseState();
  }
  /**
   * Protobuf type {@code protocol.RequestGooglePay}
   */
  public static final class RequestGooglePay extends
      com.google.protobuf.GeneratedMessage
      implements RequestGooglePayOrBuilder {
    // Use RequestGooglePay.newBuilder() to construct.
    private RequestGooglePay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGooglePay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGooglePay defaultInstance;
    public static RequestGooglePay getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGooglePay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGooglePay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              orderId_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              productId_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              purchaseToken_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              cpOrder_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              purchaseState_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_RequestGooglePay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_RequestGooglePay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.RequestGooglePay.class, protocol.PayData.RequestGooglePay.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGooglePay> PARSER =
        new com.google.protobuf.AbstractParser<RequestGooglePay>() {
      public RequestGooglePay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGooglePay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGooglePay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string OrderId = 1;
    public static final int ORDERID_FIELD_NUMBER = 1;
    private java.lang.Object orderId_;
    /**
     * <code>required string OrderId = 1;</code>
     *
     * <pre>
     *google订单号
     * </pre>
     */
    public boolean hasOrderId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string OrderId = 1;</code>
     *
     * <pre>
     *google订单号
     * </pre>
     */
    public java.lang.String getOrderId() {
      java.lang.Object ref = orderId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          orderId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string OrderId = 1;</code>
     *
     * <pre>
     *google订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getOrderIdBytes() {
      java.lang.Object ref = orderId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        orderId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string ProductId = 2;
    public static final int PRODUCTID_FIELD_NUMBER = 2;
    private java.lang.Object productId_;
    /**
     * <code>required string ProductId = 2;</code>
     *
     * <pre>
     *google商品ID
     * </pre>
     */
    public boolean hasProductId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string ProductId = 2;</code>
     *
     * <pre>
     *google商品ID
     * </pre>
     */
    public java.lang.String getProductId() {
      java.lang.Object ref = productId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          productId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string ProductId = 2;</code>
     *
     * <pre>
     *google商品ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getProductIdBytes() {
      java.lang.Object ref = productId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string PurchaseToken = 3;
    public static final int PURCHASETOKEN_FIELD_NUMBER = 3;
    private java.lang.Object purchaseToken_;
    /**
     * <code>required string PurchaseToken = 3;</code>
     *
     * <pre>
     *google token
     * </pre>
     */
    public boolean hasPurchaseToken() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string PurchaseToken = 3;</code>
     *
     * <pre>
     *google token
     * </pre>
     */
    public java.lang.String getPurchaseToken() {
      java.lang.Object ref = purchaseToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          purchaseToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string PurchaseToken = 3;</code>
     *
     * <pre>
     *google token
     * </pre>
     */
    public com.google.protobuf.ByteString
        getPurchaseTokenBytes() {
      java.lang.Object ref = purchaseToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        purchaseToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string cp_order = 4;
    public static final int CP_ORDER_FIELD_NUMBER = 4;
    private java.lang.Object cpOrder_;
    /**
     * <code>required string cp_order = 4;</code>
     *
     * <pre>
     *游戏服务器的订单号
     * </pre>
     */
    public boolean hasCpOrder() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string cp_order = 4;</code>
     *
     * <pre>
     *游戏服务器的订单号
     * </pre>
     */
    public java.lang.String getCpOrder() {
      java.lang.Object ref = cpOrder_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cpOrder_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string cp_order = 4;</code>
     *
     * <pre>
     *游戏服务器的订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getCpOrderBytes() {
      java.lang.Object ref = cpOrder_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cpOrder_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required int32 purchaseState = 5;
    public static final int PURCHASESTATE_FIELD_NUMBER = 5;
    private int purchaseState_;
    /**
     * <code>required int32 purchaseState = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPurchaseState() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required int32 purchaseState = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public int getPurchaseState() {
      return purchaseState_;
    }

    private void initFields() {
      orderId_ = "";
      productId_ = "";
      purchaseToken_ = "";
      cpOrder_ = "";
      purchaseState_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasOrderId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasProductId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPurchaseToken()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCpOrder()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPurchaseState()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getOrderIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getProductIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getPurchaseTokenBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getCpOrderBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, purchaseState_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getOrderIdBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getProductIdBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getPurchaseTokenBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getCpOrderBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, purchaseState_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.RequestGooglePay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestGooglePay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.RequestGooglePay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestGooglePay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.RequestGooglePay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGooglePay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.RequestGooglePayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_RequestGooglePay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_RequestGooglePay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.RequestGooglePay.class, protocol.PayData.RequestGooglePay.Builder.class);
      }

      // Construct using protocol.PayData.RequestGooglePay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        orderId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        productId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        purchaseToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        cpOrder_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        purchaseState_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_RequestGooglePay_descriptor;
      }

      public protocol.PayData.RequestGooglePay getDefaultInstanceForType() {
        return protocol.PayData.RequestGooglePay.getDefaultInstance();
      }

      public protocol.PayData.RequestGooglePay build() {
        protocol.PayData.RequestGooglePay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.RequestGooglePay buildPartial() {
        protocol.PayData.RequestGooglePay result = new protocol.PayData.RequestGooglePay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.orderId_ = orderId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.productId_ = productId_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.purchaseToken_ = purchaseToken_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.cpOrder_ = cpOrder_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.purchaseState_ = purchaseState_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.RequestGooglePay) {
          return mergeFrom((protocol.PayData.RequestGooglePay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.RequestGooglePay other) {
        if (other == protocol.PayData.RequestGooglePay.getDefaultInstance()) return this;
        if (other.hasOrderId()) {
          bitField0_ |= 0x00000001;
          orderId_ = other.orderId_;
          onChanged();
        }
        if (other.hasProductId()) {
          bitField0_ |= 0x00000002;
          productId_ = other.productId_;
          onChanged();
        }
        if (other.hasPurchaseToken()) {
          bitField0_ |= 0x00000004;
          purchaseToken_ = other.purchaseToken_;
          onChanged();
        }
        if (other.hasCpOrder()) {
          bitField0_ |= 0x00000008;
          cpOrder_ = other.cpOrder_;
          onChanged();
        }
        if (other.hasPurchaseState()) {
          setPurchaseState(other.getPurchaseState());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasOrderId()) {
          
          return false;
        }
        if (!hasProductId()) {
          
          return false;
        }
        if (!hasPurchaseToken()) {
          
          return false;
        }
        if (!hasCpOrder()) {
          
          return false;
        }
        if (!hasPurchaseState()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.RequestGooglePay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.RequestGooglePay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string OrderId = 1;
      private java.lang.Object orderId_ = "";
      /**
       * <code>required string OrderId = 1;</code>
       *
       * <pre>
       *google订单号
       * </pre>
       */
      public boolean hasOrderId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string OrderId = 1;</code>
       *
       * <pre>
       *google订单号
       * </pre>
       */
      public java.lang.String getOrderId() {
        java.lang.Object ref = orderId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          orderId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string OrderId = 1;</code>
       *
       * <pre>
       *google订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getOrderIdBytes() {
        java.lang.Object ref = orderId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          orderId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string OrderId = 1;</code>
       *
       * <pre>
       *google订单号
       * </pre>
       */
      public Builder setOrderId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        orderId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string OrderId = 1;</code>
       *
       * <pre>
       *google订单号
       * </pre>
       */
      public Builder clearOrderId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        orderId_ = getDefaultInstance().getOrderId();
        onChanged();
        return this;
      }
      /**
       * <code>required string OrderId = 1;</code>
       *
       * <pre>
       *google订单号
       * </pre>
       */
      public Builder setOrderIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        orderId_ = value;
        onChanged();
        return this;
      }

      // required string ProductId = 2;
      private java.lang.Object productId_ = "";
      /**
       * <code>required string ProductId = 2;</code>
       *
       * <pre>
       *google商品ID
       * </pre>
       */
      public boolean hasProductId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string ProductId = 2;</code>
       *
       * <pre>
       *google商品ID
       * </pre>
       */
      public java.lang.String getProductId() {
        java.lang.Object ref = productId_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          productId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string ProductId = 2;</code>
       *
       * <pre>
       *google商品ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getProductIdBytes() {
        java.lang.Object ref = productId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          productId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string ProductId = 2;</code>
       *
       * <pre>
       *google商品ID
       * </pre>
       */
      public Builder setProductId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        productId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string ProductId = 2;</code>
       *
       * <pre>
       *google商品ID
       * </pre>
       */
      public Builder clearProductId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        productId_ = getDefaultInstance().getProductId();
        onChanged();
        return this;
      }
      /**
       * <code>required string ProductId = 2;</code>
       *
       * <pre>
       *google商品ID
       * </pre>
       */
      public Builder setProductIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        productId_ = value;
        onChanged();
        return this;
      }

      // required string PurchaseToken = 3;
      private java.lang.Object purchaseToken_ = "";
      /**
       * <code>required string PurchaseToken = 3;</code>
       *
       * <pre>
       *google token
       * </pre>
       */
      public boolean hasPurchaseToken() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string PurchaseToken = 3;</code>
       *
       * <pre>
       *google token
       * </pre>
       */
      public java.lang.String getPurchaseToken() {
        java.lang.Object ref = purchaseToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          purchaseToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string PurchaseToken = 3;</code>
       *
       * <pre>
       *google token
       * </pre>
       */
      public com.google.protobuf.ByteString
          getPurchaseTokenBytes() {
        java.lang.Object ref = purchaseToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          purchaseToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string PurchaseToken = 3;</code>
       *
       * <pre>
       *google token
       * </pre>
       */
      public Builder setPurchaseToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        purchaseToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string PurchaseToken = 3;</code>
       *
       * <pre>
       *google token
       * </pre>
       */
      public Builder clearPurchaseToken() {
        bitField0_ = (bitField0_ & ~0x00000004);
        purchaseToken_ = getDefaultInstance().getPurchaseToken();
        onChanged();
        return this;
      }
      /**
       * <code>required string PurchaseToken = 3;</code>
       *
       * <pre>
       *google token
       * </pre>
       */
      public Builder setPurchaseTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        purchaseToken_ = value;
        onChanged();
        return this;
      }

      // required string cp_order = 4;
      private java.lang.Object cpOrder_ = "";
      /**
       * <code>required string cp_order = 4;</code>
       *
       * <pre>
       *游戏服务器的订单号
       * </pre>
       */
      public boolean hasCpOrder() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string cp_order = 4;</code>
       *
       * <pre>
       *游戏服务器的订单号
       * </pre>
       */
      public java.lang.String getCpOrder() {
        java.lang.Object ref = cpOrder_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          cpOrder_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string cp_order = 4;</code>
       *
       * <pre>
       *游戏服务器的订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getCpOrderBytes() {
        java.lang.Object ref = cpOrder_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cpOrder_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string cp_order = 4;</code>
       *
       * <pre>
       *游戏服务器的订单号
       * </pre>
       */
      public Builder setCpOrder(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        cpOrder_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string cp_order = 4;</code>
       *
       * <pre>
       *游戏服务器的订单号
       * </pre>
       */
      public Builder clearCpOrder() {
        bitField0_ = (bitField0_ & ~0x00000008);
        cpOrder_ = getDefaultInstance().getCpOrder();
        onChanged();
        return this;
      }
      /**
       * <code>required string cp_order = 4;</code>
       *
       * <pre>
       *游戏服务器的订单号
       * </pre>
       */
      public Builder setCpOrderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        cpOrder_ = value;
        onChanged();
        return this;
      }

      // required int32 purchaseState = 5;
      private int purchaseState_ ;
      /**
       * <code>required int32 purchaseState = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPurchaseState() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required int32 purchaseState = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public int getPurchaseState() {
        return purchaseState_;
      }
      /**
       * <code>required int32 purchaseState = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPurchaseState(int value) {
        bitField0_ |= 0x00000010;
        purchaseState_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 purchaseState = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPurchaseState() {
        bitField0_ = (bitField0_ & ~0x00000010);
        purchaseState_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGooglePay)
    }

    static {
      defaultInstance = new RequestGooglePay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGooglePay)
  }

  public interface ResponseGooglePayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // optional int32 successId = 2;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    boolean hasSuccessId();
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    int getSuccessId();

    // repeated .protocol.Item item = 3;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // optional string transactionID = 4;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // optional int32 firstRecharge = 5;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    boolean hasFirstRecharge();
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    int getFirstRecharge();
  }
  /**
   * Protobuf type {@code protocol.ResponseGooglePay}
   */
  public static final class ResponseGooglePay extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGooglePayOrBuilder {
    // Use ResponseGooglePay.newBuilder() to construct.
    private ResponseGooglePay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGooglePay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGooglePay defaultInstance;
    public static ResponseGooglePay getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGooglePay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGooglePay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              successId_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000004;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              bitField0_ |= 0x00000004;
              transactionID_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              firstRecharge_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseGooglePay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseGooglePay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseGooglePay.class, protocol.PayData.ResponseGooglePay.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGooglePay> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGooglePay>() {
      public ResponseGooglePay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGooglePay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGooglePay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // optional int32 successId = 2;
    public static final int SUCCESSID_FIELD_NUMBER = 2;
    private int successId_;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public boolean hasSuccessId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public int getSuccessId() {
      return successId_;
    }

    // repeated .protocol.Item item = 3;
    public static final int ITEM_FIELD_NUMBER = 3;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // optional string transactionID = 4;
    public static final int TRANSACTIONID_FIELD_NUMBER = 4;
    private java.lang.Object transactionID_;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 firstRecharge = 5;
    public static final int FIRSTRECHARGE_FIELD_NUMBER = 5;
    private int firstRecharge_;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    public boolean hasFirstRecharge() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    public int getFirstRecharge() {
      return firstRecharge_;
    }

    private void initFields() {
      errorId_ = 0;
      successId_ = 0;
      item_ = java.util.Collections.emptyList();
      transactionID_ = "";
      firstRecharge_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(5, firstRecharge_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, firstRecharge_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseGooglePay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseGooglePay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseGooglePay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseGooglePay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseGooglePay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGooglePay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseGooglePayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseGooglePay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseGooglePay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseGooglePay.class, protocol.PayData.ResponseGooglePay.Builder.class);
      }

      // Construct using protocol.PayData.ResponseGooglePay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        successId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          itemBuilder_.clear();
        }
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        firstRecharge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseGooglePay_descriptor;
      }

      public protocol.PayData.ResponseGooglePay getDefaultInstanceForType() {
        return protocol.PayData.ResponseGooglePay.getDefaultInstance();
      }

      public protocol.PayData.ResponseGooglePay build() {
        protocol.PayData.ResponseGooglePay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseGooglePay buildPartial() {
        protocol.PayData.ResponseGooglePay result = new protocol.PayData.ResponseGooglePay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.successId_ = successId_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        result.firstRecharge_ = firstRecharge_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseGooglePay) {
          return mergeFrom((protocol.PayData.ResponseGooglePay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseGooglePay other) {
        if (other == protocol.PayData.ResponseGooglePay.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSuccessId()) {
          setSuccessId(other.getSuccessId());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000008;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasFirstRecharge()) {
          setFirstRecharge(other.getFirstRecharge());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseGooglePay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseGooglePay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // optional int32 successId = 2;
      private int successId_ ;
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public boolean hasSuccessId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public int getSuccessId() {
        return successId_;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder setSuccessId(int value) {
        bitField0_ |= 0x00000002;
        successId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder clearSuccessId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        successId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 3;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // optional string transactionID = 4;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000008);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // optional int32 firstRecharge = 5;
      private int firstRecharge_ ;
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public boolean hasFirstRecharge() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public int getFirstRecharge() {
        return firstRecharge_;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public Builder setFirstRecharge(int value) {
        bitField0_ |= 0x00000010;
        firstRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public Builder clearFirstRecharge() {
        bitField0_ = (bitField0_ & ~0x00000010);
        firstRecharge_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGooglePay)
    }

    static {
      defaultInstance = new ResponseGooglePay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGooglePay)
  }

  public interface ResponseWeChatPayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // optional int32 successId = 2;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    boolean hasSuccessId();
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    int getSuccessId();

    // repeated .protocol.Item item = 3;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // optional string transactionID = 4;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // optional int32 firstRecharge = 5;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    boolean hasFirstRecharge();
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    int getFirstRecharge();
  }
  /**
   * Protobuf type {@code protocol.ResponseWeChatPay}
   */
  public static final class ResponseWeChatPay extends
      com.google.protobuf.GeneratedMessage
      implements ResponseWeChatPayOrBuilder {
    // Use ResponseWeChatPay.newBuilder() to construct.
    private ResponseWeChatPay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseWeChatPay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseWeChatPay defaultInstance;
    public static ResponseWeChatPay getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseWeChatPay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseWeChatPay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              successId_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000004;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              bitField0_ |= 0x00000004;
              transactionID_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              firstRecharge_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseWeChatPay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseWeChatPay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseWeChatPay.class, protocol.PayData.ResponseWeChatPay.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseWeChatPay> PARSER =
        new com.google.protobuf.AbstractParser<ResponseWeChatPay>() {
      public ResponseWeChatPay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseWeChatPay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseWeChatPay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // optional int32 successId = 2;
    public static final int SUCCESSID_FIELD_NUMBER = 2;
    private int successId_;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public boolean hasSuccessId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public int getSuccessId() {
      return successId_;
    }

    // repeated .protocol.Item item = 3;
    public static final int ITEM_FIELD_NUMBER = 3;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // optional string transactionID = 4;
    public static final int TRANSACTIONID_FIELD_NUMBER = 4;
    private java.lang.Object transactionID_;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 firstRecharge = 5;
    public static final int FIRSTRECHARGE_FIELD_NUMBER = 5;
    private int firstRecharge_;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    public boolean hasFirstRecharge() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充	
     * </pre>
     */
    public int getFirstRecharge() {
      return firstRecharge_;
    }

    private void initFields() {
      errorId_ = 0;
      successId_ = 0;
      item_ = java.util.Collections.emptyList();
      transactionID_ = "";
      firstRecharge_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(5, firstRecharge_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, firstRecharge_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseWeChatPay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseWeChatPay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseWeChatPay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseWeChatPay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseWeChatPay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseWeChatPay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseWeChatPayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseWeChatPay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseWeChatPay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseWeChatPay.class, protocol.PayData.ResponseWeChatPay.Builder.class);
      }

      // Construct using protocol.PayData.ResponseWeChatPay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        successId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          itemBuilder_.clear();
        }
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        firstRecharge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseWeChatPay_descriptor;
      }

      public protocol.PayData.ResponseWeChatPay getDefaultInstanceForType() {
        return protocol.PayData.ResponseWeChatPay.getDefaultInstance();
      }

      public protocol.PayData.ResponseWeChatPay build() {
        protocol.PayData.ResponseWeChatPay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseWeChatPay buildPartial() {
        protocol.PayData.ResponseWeChatPay result = new protocol.PayData.ResponseWeChatPay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.successId_ = successId_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        result.firstRecharge_ = firstRecharge_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseWeChatPay) {
          return mergeFrom((protocol.PayData.ResponseWeChatPay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseWeChatPay other) {
        if (other == protocol.PayData.ResponseWeChatPay.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSuccessId()) {
          setSuccessId(other.getSuccessId());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000008;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasFirstRecharge()) {
          setFirstRecharge(other.getFirstRecharge());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseWeChatPay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseWeChatPay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // optional int32 successId = 2;
      private int successId_ ;
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public boolean hasSuccessId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public int getSuccessId() {
        return successId_;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder setSuccessId(int value) {
        bitField0_ |= 0x00000002;
        successId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder clearSuccessId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        successId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 3;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // optional string transactionID = 4;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000008);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // optional int32 firstRecharge = 5;
      private int firstRecharge_ ;
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public boolean hasFirstRecharge() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public int getFirstRecharge() {
        return firstRecharge_;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public Builder setFirstRecharge(int value) {
        bitField0_ |= 0x00000010;
        firstRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充	
       * </pre>
       */
      public Builder clearFirstRecharge() {
        bitField0_ = (bitField0_ & ~0x00000010);
        firstRecharge_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseWeChatPay)
    }

    static {
      defaultInstance = new ResponseWeChatPay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseWeChatPay)
  }

  public interface ResponseAliPayOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // optional int32 successId = 2;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    boolean hasSuccessId();
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    int getSuccessId();

    // repeated .protocol.Item item = 3;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // optional string transactionID = 4;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // optional int32 firstRecharge = 5;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充
     * </pre>
     */
    boolean hasFirstRecharge();
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充
     * </pre>
     */
    int getFirstRecharge();
  }
  /**
   * Protobuf type {@code protocol.ResponseAliPay}
   */
  public static final class ResponseAliPay extends
      com.google.protobuf.GeneratedMessage
      implements ResponseAliPayOrBuilder {
    // Use ResponseAliPay.newBuilder() to construct.
    private ResponseAliPay(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseAliPay(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseAliPay defaultInstance;
    public static ResponseAliPay getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseAliPay getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseAliPay(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              successId_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000004;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              bitField0_ |= 0x00000004;
              transactionID_ = input.readBytes();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              firstRecharge_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseAliPay_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseAliPay_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseAliPay.class, protocol.PayData.ResponseAliPay.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseAliPay> PARSER =
        new com.google.protobuf.AbstractParser<ResponseAliPay>() {
      public ResponseAliPay parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseAliPay(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseAliPay> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // optional int32 successId = 2;
    public static final int SUCCESSID_FIELD_NUMBER = 2;
    private int successId_;
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public boolean hasSuccessId() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>optional int32 successId = 2;</code>
     *
     * <pre>
     *成功购买id
     * </pre>
     */
    public int getSuccessId() {
      return successId_;
    }

    // repeated .protocol.Item item = 3;
    public static final int ITEM_FIELD_NUMBER = 3;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 3;</code>
     *
     * <pre>
     *更新物品
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // optional string transactionID = 4;
    public static final int TRANSACTIONID_FIELD_NUMBER = 4;
    private java.lang.Object transactionID_;
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string transactionID = 4;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // optional int32 firstRecharge = 5;
    public static final int FIRSTRECHARGE_FIELD_NUMBER = 5;
    private int firstRecharge_;
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充
     * </pre>
     */
    public boolean hasFirstRecharge() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>optional int32 firstRecharge = 5;</code>
     *
     * <pre>
     *是否首充
     * </pre>
     */
    public int getFirstRecharge() {
      return firstRecharge_;
    }

    private void initFields() {
      errorId_ = 0;
      successId_ = 0;
      item_ = java.util.Collections.emptyList();
      transactionID_ = "";
      firstRecharge_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(5, firstRecharge_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, successId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, item_.get(i));
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, firstRecharge_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseAliPay parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseAliPay parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseAliPay parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseAliPay parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseAliPay prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseAliPay}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseAliPayOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseAliPay_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseAliPay_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseAliPay.class, protocol.PayData.ResponseAliPay.Builder.class);
      }

      // Construct using protocol.PayData.ResponseAliPay.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        successId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          itemBuilder_.clear();
        }
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        firstRecharge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseAliPay_descriptor;
      }

      public protocol.PayData.ResponseAliPay getDefaultInstanceForType() {
        return protocol.PayData.ResponseAliPay.getDefaultInstance();
      }

      public protocol.PayData.ResponseAliPay build() {
        protocol.PayData.ResponseAliPay result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseAliPay buildPartial() {
        protocol.PayData.ResponseAliPay result = new protocol.PayData.ResponseAliPay(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.successId_ = successId_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000004) == 0x00000004)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        result.firstRecharge_ = firstRecharge_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseAliPay) {
          return mergeFrom((protocol.PayData.ResponseAliPay)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseAliPay other) {
        if (other == protocol.PayData.ResponseAliPay.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasSuccessId()) {
          setSuccessId(other.getSuccessId());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000004);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000008;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasFirstRecharge()) {
          setFirstRecharge(other.getFirstRecharge());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseAliPay parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseAliPay) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // optional int32 successId = 2;
      private int successId_ ;
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public boolean hasSuccessId() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public int getSuccessId() {
        return successId_;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder setSuccessId(int value) {
        bitField0_ |= 0x00000002;
        successId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 successId = 2;</code>
       *
       * <pre>
       *成功购买id
       * </pre>
       */
      public Builder clearSuccessId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        successId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 3;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000004) == 0x00000004)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 3;</code>
       *
       * <pre>
       *更新物品
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000004) == 0x00000004),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // optional string transactionID = 4;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000008);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>optional string transactionID = 4;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // optional int32 firstRecharge = 5;
      private int firstRecharge_ ;
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充
       * </pre>
       */
      public boolean hasFirstRecharge() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充
       * </pre>
       */
      public int getFirstRecharge() {
        return firstRecharge_;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充
       * </pre>
       */
      public Builder setFirstRecharge(int value) {
        bitField0_ |= 0x00000010;
        firstRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 firstRecharge = 5;</code>
       *
       * <pre>
       *是否首充
       * </pre>
       */
      public Builder clearFirstRecharge() {
        bitField0_ = (bitField0_ & ~0x00000010);
        firstRecharge_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseAliPay)
    }

    static {
      defaultInstance = new ResponseAliPay(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseAliPay)
  }

  public interface RequestConfirmHuaWeiPurchaseOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string purchaseToken = 1;
    /**
     * <code>required string purchaseToken = 1;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPurchaseToken();
    /**
     * <code>required string purchaseToken = 1;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getPurchaseToken();
    /**
     * <code>required string purchaseToken = 1;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getPurchaseTokenBytes();

    // required string transactionID = 2;
    /**
     * <code>required string transactionID = 2;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>required string transactionID = 2;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>required string transactionID = 2;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // required string key = 3;
    /**
     * <code>required string key = 3;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasKey();
    /**
     * <code>required string key = 3;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getKey();
    /**
     * <code>required string key = 3;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getKeyBytes();

    // required string huaWeiOrderID = 4;
    /**
     * <code>required string huaWeiOrderID = 4;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasHuaWeiOrderID();
    /**
     * <code>required string huaWeiOrderID = 4;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getHuaWeiOrderID();
    /**
     * <code>required string huaWeiOrderID = 4;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getHuaWeiOrderIDBytes();
  }
  /**
   * Protobuf type {@code protocol.RequestConfirmHuaWeiPurchase}
   */
  public static final class RequestConfirmHuaWeiPurchase extends
      com.google.protobuf.GeneratedMessage
      implements RequestConfirmHuaWeiPurchaseOrBuilder {
    // Use RequestConfirmHuaWeiPurchase.newBuilder() to construct.
    private RequestConfirmHuaWeiPurchase(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestConfirmHuaWeiPurchase(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestConfirmHuaWeiPurchase defaultInstance;
    public static RequestConfirmHuaWeiPurchase getDefaultInstance() {
      return defaultInstance;
    }

    public RequestConfirmHuaWeiPurchase getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestConfirmHuaWeiPurchase(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              purchaseToken_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              transactionID_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              key_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              huaWeiOrderID_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_RequestConfirmHuaWeiPurchase_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_RequestConfirmHuaWeiPurchase_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.RequestConfirmHuaWeiPurchase.class, protocol.PayData.RequestConfirmHuaWeiPurchase.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestConfirmHuaWeiPurchase> PARSER =
        new com.google.protobuf.AbstractParser<RequestConfirmHuaWeiPurchase>() {
      public RequestConfirmHuaWeiPurchase parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestConfirmHuaWeiPurchase(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestConfirmHuaWeiPurchase> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string purchaseToken = 1;
    public static final int PURCHASETOKEN_FIELD_NUMBER = 1;
    private java.lang.Object purchaseToken_;
    /**
     * <code>required string purchaseToken = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPurchaseToken() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string purchaseToken = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getPurchaseToken() {
      java.lang.Object ref = purchaseToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          purchaseToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string purchaseToken = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getPurchaseTokenBytes() {
      java.lang.Object ref = purchaseToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        purchaseToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string transactionID = 2;
    public static final int TRANSACTIONID_FIELD_NUMBER = 2;
    private java.lang.Object transactionID_;
    /**
     * <code>required string transactionID = 2;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string transactionID = 2;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string transactionID = 2;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string key = 3;
    public static final int KEY_FIELD_NUMBER = 3;
    private java.lang.Object key_;
    /**
     * <code>required string key = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasKey() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string key = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          key_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string key = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string huaWeiOrderID = 4;
    public static final int HUAWEIORDERID_FIELD_NUMBER = 4;
    private java.lang.Object huaWeiOrderID_;
    /**
     * <code>required string huaWeiOrderID = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasHuaWeiOrderID() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string huaWeiOrderID = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getHuaWeiOrderID() {
      java.lang.Object ref = huaWeiOrderID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          huaWeiOrderID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string huaWeiOrderID = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getHuaWeiOrderIDBytes() {
      java.lang.Object ref = huaWeiOrderID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        huaWeiOrderID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      purchaseToken_ = "";
      transactionID_ = "";
      key_ = "";
      huaWeiOrderID_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasPurchaseToken()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTransactionID()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasHuaWeiOrderID()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getPurchaseTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getKeyBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getHuaWeiOrderIDBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getPurchaseTokenBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getKeyBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getHuaWeiOrderIDBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.RequestConfirmHuaWeiPurchase prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestConfirmHuaWeiPurchase}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.RequestConfirmHuaWeiPurchaseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_RequestConfirmHuaWeiPurchase_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_RequestConfirmHuaWeiPurchase_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.RequestConfirmHuaWeiPurchase.class, protocol.PayData.RequestConfirmHuaWeiPurchase.Builder.class);
      }

      // Construct using protocol.PayData.RequestConfirmHuaWeiPurchase.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        purchaseToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        key_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        huaWeiOrderID_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_RequestConfirmHuaWeiPurchase_descriptor;
      }

      public protocol.PayData.RequestConfirmHuaWeiPurchase getDefaultInstanceForType() {
        return protocol.PayData.RequestConfirmHuaWeiPurchase.getDefaultInstance();
      }

      public protocol.PayData.RequestConfirmHuaWeiPurchase build() {
        protocol.PayData.RequestConfirmHuaWeiPurchase result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.RequestConfirmHuaWeiPurchase buildPartial() {
        protocol.PayData.RequestConfirmHuaWeiPurchase result = new protocol.PayData.RequestConfirmHuaWeiPurchase(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.purchaseToken_ = purchaseToken_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.key_ = key_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.huaWeiOrderID_ = huaWeiOrderID_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.RequestConfirmHuaWeiPurchase) {
          return mergeFrom((protocol.PayData.RequestConfirmHuaWeiPurchase)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.RequestConfirmHuaWeiPurchase other) {
        if (other == protocol.PayData.RequestConfirmHuaWeiPurchase.getDefaultInstance()) return this;
        if (other.hasPurchaseToken()) {
          bitField0_ |= 0x00000001;
          purchaseToken_ = other.purchaseToken_;
          onChanged();
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000002;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasKey()) {
          bitField0_ |= 0x00000004;
          key_ = other.key_;
          onChanged();
        }
        if (other.hasHuaWeiOrderID()) {
          bitField0_ |= 0x00000008;
          huaWeiOrderID_ = other.huaWeiOrderID_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasPurchaseToken()) {
          
          return false;
        }
        if (!hasTransactionID()) {
          
          return false;
        }
        if (!hasKey()) {
          
          return false;
        }
        if (!hasHuaWeiOrderID()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.RequestConfirmHuaWeiPurchase parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.RequestConfirmHuaWeiPurchase) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string purchaseToken = 1;
      private java.lang.Object purchaseToken_ = "";
      /**
       * <code>required string purchaseToken = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPurchaseToken() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string purchaseToken = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getPurchaseToken() {
        java.lang.Object ref = purchaseToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          purchaseToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string purchaseToken = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getPurchaseTokenBytes() {
        java.lang.Object ref = purchaseToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          purchaseToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string purchaseToken = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPurchaseToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        purchaseToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string purchaseToken = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPurchaseToken() {
        bitField0_ = (bitField0_ & ~0x00000001);
        purchaseToken_ = getDefaultInstance().getPurchaseToken();
        onChanged();
        return this;
      }
      /**
       * <code>required string purchaseToken = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPurchaseTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        purchaseToken_ = value;
        onChanged();
        return this;
      }

      // required string transactionID = 2;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>required string transactionID = 2;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string transactionID = 2;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string transactionID = 2;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string transactionID = 2;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string transactionID = 2;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000002);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>required string transactionID = 2;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // required string key = 3;
      private java.lang.Object key_ = "";
      /**
       * <code>required string key = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasKey() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string key = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string key = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string key = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearKey() {
        bitField0_ = (bitField0_ & ~0x00000004);
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        key_ = value;
        onChanged();
        return this;
      }

      // required string huaWeiOrderID = 4;
      private java.lang.Object huaWeiOrderID_ = "";
      /**
       * <code>required string huaWeiOrderID = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasHuaWeiOrderID() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string huaWeiOrderID = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getHuaWeiOrderID() {
        java.lang.Object ref = huaWeiOrderID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          huaWeiOrderID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string huaWeiOrderID = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getHuaWeiOrderIDBytes() {
        java.lang.Object ref = huaWeiOrderID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          huaWeiOrderID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string huaWeiOrderID = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setHuaWeiOrderID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        huaWeiOrderID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string huaWeiOrderID = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearHuaWeiOrderID() {
        bitField0_ = (bitField0_ & ~0x00000008);
        huaWeiOrderID_ = getDefaultInstance().getHuaWeiOrderID();
        onChanged();
        return this;
      }
      /**
       * <code>required string huaWeiOrderID = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setHuaWeiOrderIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        huaWeiOrderID_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestConfirmHuaWeiPurchase)
    }

    static {
      defaultInstance = new RequestConfirmHuaWeiPurchase(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestConfirmHuaWeiPurchase)
  }

  public interface ResponseConfirmHuaWeiPurchaseOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // repeated .protocol.Item item = 2;
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // required int32 firstRecharge = 3;
    /**
     * <code>required int32 firstRecharge = 3;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    boolean hasFirstRecharge();
    /**
     * <code>required int32 firstRecharge = 3;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    int getFirstRecharge();

    // required string purchaseToken = 4;
    /**
     * <code>required string purchaseToken = 4;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPurchaseToken();
    /**
     * <code>required string purchaseToken = 4;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getPurchaseToken();
    /**
     * <code>required string purchaseToken = 4;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getPurchaseTokenBytes();

    // required string transactionID = 5;
    /**
     * <code>required string transactionID = 5;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    boolean hasTransactionID();
    /**
     * <code>required string transactionID = 5;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    java.lang.String getTransactionID();
    /**
     * <code>required string transactionID = 5;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    com.google.protobuf.ByteString
        getTransactionIDBytes();

    // required string productID = 6;
    /**
     * <code>required string productID = 6;</code>
     *
     * <pre>
     *成功购买商品ID
     * </pre>
     */
    boolean hasProductID();
    /**
     * <code>required string productID = 6;</code>
     *
     * <pre>
     *成功购买商品ID
     * </pre>
     */
    java.lang.String getProductID();
    /**
     * <code>required string productID = 6;</code>
     *
     * <pre>
     *成功购买商品ID
     * </pre>
     */
    com.google.protobuf.ByteString
        getProductIDBytes();
  }
  /**
   * Protobuf type {@code protocol.ResponseConfirmHuaWeiPurchase}
   */
  public static final class ResponseConfirmHuaWeiPurchase extends
      com.google.protobuf.GeneratedMessage
      implements ResponseConfirmHuaWeiPurchaseOrBuilder {
    // Use ResponseConfirmHuaWeiPurchase.newBuilder() to construct.
    private ResponseConfirmHuaWeiPurchase(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseConfirmHuaWeiPurchase(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseConfirmHuaWeiPurchase defaultInstance;
    public static ResponseConfirmHuaWeiPurchase getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseConfirmHuaWeiPurchase getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseConfirmHuaWeiPurchase(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000002;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              firstRecharge_ = input.readInt32();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000004;
              purchaseToken_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000008;
              transactionID_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000010;
              productID_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) == 0x00000002)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseConfirmHuaWeiPurchase_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseConfirmHuaWeiPurchase_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseConfirmHuaWeiPurchase.class, protocol.PayData.ResponseConfirmHuaWeiPurchase.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseConfirmHuaWeiPurchase> PARSER =
        new com.google.protobuf.AbstractParser<ResponseConfirmHuaWeiPurchase>() {
      public ResponseConfirmHuaWeiPurchase parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseConfirmHuaWeiPurchase(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseConfirmHuaWeiPurchase> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // repeated .protocol.Item item = 2;
    public static final int ITEM_FIELD_NUMBER = 2;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 2;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // required int32 firstRecharge = 3;
    public static final int FIRSTRECHARGE_FIELD_NUMBER = 3;
    private int firstRecharge_;
    /**
     * <code>required int32 firstRecharge = 3;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    public boolean hasFirstRecharge() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 firstRecharge = 3;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    public int getFirstRecharge() {
      return firstRecharge_;
    }

    // required string purchaseToken = 4;
    public static final int PURCHASETOKEN_FIELD_NUMBER = 4;
    private java.lang.Object purchaseToken_;
    /**
     * <code>required string purchaseToken = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPurchaseToken() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string purchaseToken = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getPurchaseToken() {
      java.lang.Object ref = purchaseToken_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          purchaseToken_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string purchaseToken = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getPurchaseTokenBytes() {
      java.lang.Object ref = purchaseToken_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        purchaseToken_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string transactionID = 5;
    public static final int TRANSACTIONID_FIELD_NUMBER = 5;
    private java.lang.Object transactionID_;
    /**
     * <code>required string transactionID = 5;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public boolean hasTransactionID() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string transactionID = 5;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public java.lang.String getTransactionID() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          transactionID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string transactionID = 5;</code>
     *
     * <pre>
     *订单号
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTransactionIDBytes() {
      java.lang.Object ref = transactionID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        transactionID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string productID = 6;
    public static final int PRODUCTID_FIELD_NUMBER = 6;
    private java.lang.Object productID_;
    /**
     * <code>required string productID = 6;</code>
     *
     * <pre>
     *成功购买商品ID
     * </pre>
     */
    public boolean hasProductID() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string productID = 6;</code>
     *
     * <pre>
     *成功购买商品ID
     * </pre>
     */
    public java.lang.String getProductID() {
      java.lang.Object ref = productID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          productID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string productID = 6;</code>
     *
     * <pre>
     *成功购买商品ID
     * </pre>
     */
    public com.google.protobuf.ByteString
        getProductIDBytes() {
      java.lang.Object ref = productID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      errorId_ = 0;
      item_ = java.util.Collections.emptyList();
      firstRecharge_ = 0;
      purchaseToken_ = "";
      transactionID_ = "";
      productID_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFirstRecharge()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPurchaseToken()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTransactionID()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasProductID()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(2, item_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(3, firstRecharge_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(4, getPurchaseTokenBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(5, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(6, getProductIDBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, item_.get(i));
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, firstRecharge_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getPurchaseTokenBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getTransactionIDBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getProductIDBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseConfirmHuaWeiPurchase parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseConfirmHuaWeiPurchase prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseConfirmHuaWeiPurchase}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseConfirmHuaWeiPurchaseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseConfirmHuaWeiPurchase_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseConfirmHuaWeiPurchase_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseConfirmHuaWeiPurchase.class, protocol.PayData.ResponseConfirmHuaWeiPurchase.Builder.class);
      }

      // Construct using protocol.PayData.ResponseConfirmHuaWeiPurchase.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          itemBuilder_.clear();
        }
        firstRecharge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        purchaseToken_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        transactionID_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        productID_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseConfirmHuaWeiPurchase_descriptor;
      }

      public protocol.PayData.ResponseConfirmHuaWeiPurchase getDefaultInstanceForType() {
        return protocol.PayData.ResponseConfirmHuaWeiPurchase.getDefaultInstance();
      }

      public protocol.PayData.ResponseConfirmHuaWeiPurchase build() {
        protocol.PayData.ResponseConfirmHuaWeiPurchase result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseConfirmHuaWeiPurchase buildPartial() {
        protocol.PayData.ResponseConfirmHuaWeiPurchase result = new protocol.PayData.ResponseConfirmHuaWeiPurchase(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000002) == 0x00000002)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000002;
        }
        result.firstRecharge_ = firstRecharge_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000004;
        }
        result.purchaseToken_ = purchaseToken_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000008;
        }
        result.transactionID_ = transactionID_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000010;
        }
        result.productID_ = productID_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseConfirmHuaWeiPurchase) {
          return mergeFrom((protocol.PayData.ResponseConfirmHuaWeiPurchase)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseConfirmHuaWeiPurchase other) {
        if (other == protocol.PayData.ResponseConfirmHuaWeiPurchase.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000002);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasFirstRecharge()) {
          setFirstRecharge(other.getFirstRecharge());
        }
        if (other.hasPurchaseToken()) {
          bitField0_ |= 0x00000008;
          purchaseToken_ = other.purchaseToken_;
          onChanged();
        }
        if (other.hasTransactionID()) {
          bitField0_ |= 0x00000010;
          transactionID_ = other.transactionID_;
          onChanged();
        }
        if (other.hasProductID()) {
          bitField0_ |= 0x00000020;
          productID_ = other.productID_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasFirstRecharge()) {
          
          return false;
        }
        if (!hasPurchaseToken()) {
          
          return false;
        }
        if (!hasTransactionID()) {
          
          return false;
        }
        if (!hasProductID()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseConfirmHuaWeiPurchase parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseConfirmHuaWeiPurchase) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 2;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000002) == 0x00000002)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 2;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000002) == 0x00000002),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // required int32 firstRecharge = 3;
      private int firstRecharge_ ;
      /**
       * <code>required int32 firstRecharge = 3;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public boolean hasFirstRecharge() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 firstRecharge = 3;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public int getFirstRecharge() {
        return firstRecharge_;
      }
      /**
       * <code>required int32 firstRecharge = 3;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public Builder setFirstRecharge(int value) {
        bitField0_ |= 0x00000004;
        firstRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 firstRecharge = 3;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public Builder clearFirstRecharge() {
        bitField0_ = (bitField0_ & ~0x00000004);
        firstRecharge_ = 0;
        onChanged();
        return this;
      }

      // required string purchaseToken = 4;
      private java.lang.Object purchaseToken_ = "";
      /**
       * <code>required string purchaseToken = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPurchaseToken() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string purchaseToken = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getPurchaseToken() {
        java.lang.Object ref = purchaseToken_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          purchaseToken_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string purchaseToken = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getPurchaseTokenBytes() {
        java.lang.Object ref = purchaseToken_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          purchaseToken_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string purchaseToken = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPurchaseToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        purchaseToken_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string purchaseToken = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPurchaseToken() {
        bitField0_ = (bitField0_ & ~0x00000008);
        purchaseToken_ = getDefaultInstance().getPurchaseToken();
        onChanged();
        return this;
      }
      /**
       * <code>required string purchaseToken = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPurchaseTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        purchaseToken_ = value;
        onChanged();
        return this;
      }

      // required string transactionID = 5;
      private java.lang.Object transactionID_ = "";
      /**
       * <code>required string transactionID = 5;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public boolean hasTransactionID() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string transactionID = 5;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public java.lang.String getTransactionID() {
        java.lang.Object ref = transactionID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          transactionID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string transactionID = 5;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTransactionIDBytes() {
        java.lang.Object ref = transactionID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          transactionID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string transactionID = 5;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        transactionID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string transactionID = 5;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder clearTransactionID() {
        bitField0_ = (bitField0_ & ~0x00000010);
        transactionID_ = getDefaultInstance().getTransactionID();
        onChanged();
        return this;
      }
      /**
       * <code>required string transactionID = 5;</code>
       *
       * <pre>
       *订单号
       * </pre>
       */
      public Builder setTransactionIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        transactionID_ = value;
        onChanged();
        return this;
      }

      // required string productID = 6;
      private java.lang.Object productID_ = "";
      /**
       * <code>required string productID = 6;</code>
       *
       * <pre>
       *成功购买商品ID
       * </pre>
       */
      public boolean hasProductID() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required string productID = 6;</code>
       *
       * <pre>
       *成功购买商品ID
       * </pre>
       */
      public java.lang.String getProductID() {
        java.lang.Object ref = productID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          productID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string productID = 6;</code>
       *
       * <pre>
       *成功购买商品ID
       * </pre>
       */
      public com.google.protobuf.ByteString
          getProductIDBytes() {
        java.lang.Object ref = productID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          productID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string productID = 6;</code>
       *
       * <pre>
       *成功购买商品ID
       * </pre>
       */
      public Builder setProductID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        productID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string productID = 6;</code>
       *
       * <pre>
       *成功购买商品ID
       * </pre>
       */
      public Builder clearProductID() {
        bitField0_ = (bitField0_ & ~0x00000020);
        productID_ = getDefaultInstance().getProductID();
        onChanged();
        return this;
      }
      /**
       * <code>required string productID = 6;</code>
       *
       * <pre>
       *成功购买商品ID
       * </pre>
       */
      public Builder setProductIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        productID_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseConfirmHuaWeiPurchase)
    }

    static {
      defaultInstance = new ResponseConfirmHuaWeiPurchase(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseConfirmHuaWeiPurchase)
  }

  public interface RequestYSDKBalanceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required string openid = 1;
    /**
     * <code>required string openid = 1;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasOpenid();
    /**
     * <code>required string openid = 1;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getOpenid();
    /**
     * <code>required string openid = 1;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getOpenidBytes();

    // required string openkey = 2;
    /**
     * <code>required string openkey = 2;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasOpenkey();
    /**
     * <code>required string openkey = 2;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getOpenkey();
    /**
     * <code>required string openkey = 2;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getOpenkeyBytes();

    // required string ts = 3;
    /**
     * <code>required string ts = 3;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasTs();
    /**
     * <code>required string ts = 3;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getTs();
    /**
     * <code>required string ts = 3;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getTsBytes();

    // required string pf = 4;
    /**
     * <code>required string pf = 4;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPf();
    /**
     * <code>required string pf = 4;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getPf();
    /**
     * <code>required string pf = 4;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getPfBytes();

    // required string pfkey = 5;
    /**
     * <code>required string pfkey = 5;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasPfkey();
    /**
     * <code>required string pfkey = 5;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getPfkey();
    /**
     * <code>required string pfkey = 5;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getPfkeyBytes();

    // required string zoneid = 6;
    /**
     * <code>required string zoneid = 6;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasZoneid();
    /**
     * <code>required string zoneid = 6;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getZoneid();
    /**
     * <code>required string zoneid = 6;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getZoneidBytes();

    // required string productID = 7;
    /**
     * <code>required string productID = 7;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasProductID();
    /**
     * <code>required string productID = 7;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getProductID();
    /**
     * <code>required string productID = 7;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getProductIDBytes();

    // required string cp_order = 8;
    /**
     * <code>required string cp_order = 8;</code>
     *
     * <pre>
     * </pre>
     */
    boolean hasCpOrder();
    /**
     * <code>required string cp_order = 8;</code>
     *
     * <pre>
     * </pre>
     */
    java.lang.String getCpOrder();
    /**
     * <code>required string cp_order = 8;</code>
     *
     * <pre>
     * </pre>
     */
    com.google.protobuf.ByteString
        getCpOrderBytes();

    // required string key = 9;
    /**
     * <code>required string key = 9;</code>
     *
     * <pre>
     *	
     * </pre>
     */
    boolean hasKey();
    /**
     * <code>required string key = 9;</code>
     *
     * <pre>
     *	
     * </pre>
     */
    java.lang.String getKey();
    /**
     * <code>required string key = 9;</code>
     *
     * <pre>
     *	
     * </pre>
     */
    com.google.protobuf.ByteString
        getKeyBytes();
  }
  /**
   * Protobuf type {@code protocol.RequestYSDKBalance}
   */
  public static final class RequestYSDKBalance extends
      com.google.protobuf.GeneratedMessage
      implements RequestYSDKBalanceOrBuilder {
    // Use RequestYSDKBalance.newBuilder() to construct.
    private RequestYSDKBalance(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestYSDKBalance(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestYSDKBalance defaultInstance;
    public static RequestYSDKBalance getDefaultInstance() {
      return defaultInstance;
    }

    public RequestYSDKBalance getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestYSDKBalance(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              bitField0_ |= 0x00000001;
              openid_ = input.readBytes();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              openkey_ = input.readBytes();
              break;
            }
            case 26: {
              bitField0_ |= 0x00000004;
              ts_ = input.readBytes();
              break;
            }
            case 34: {
              bitField0_ |= 0x00000008;
              pf_ = input.readBytes();
              break;
            }
            case 42: {
              bitField0_ |= 0x00000010;
              pfkey_ = input.readBytes();
              break;
            }
            case 50: {
              bitField0_ |= 0x00000020;
              zoneid_ = input.readBytes();
              break;
            }
            case 58: {
              bitField0_ |= 0x00000040;
              productID_ = input.readBytes();
              break;
            }
            case 66: {
              bitField0_ |= 0x00000080;
              cpOrder_ = input.readBytes();
              break;
            }
            case 74: {
              bitField0_ |= 0x00000100;
              key_ = input.readBytes();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_RequestYSDKBalance_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_RequestYSDKBalance_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.RequestYSDKBalance.class, protocol.PayData.RequestYSDKBalance.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestYSDKBalance> PARSER =
        new com.google.protobuf.AbstractParser<RequestYSDKBalance>() {
      public RequestYSDKBalance parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestYSDKBalance(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestYSDKBalance> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required string openid = 1;
    public static final int OPENID_FIELD_NUMBER = 1;
    private java.lang.Object openid_;
    /**
     * <code>required string openid = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasOpenid() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required string openid = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getOpenid() {
      java.lang.Object ref = openid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openid_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string openid = 1;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getOpenidBytes() {
      java.lang.Object ref = openid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string openkey = 2;
    public static final int OPENKEY_FIELD_NUMBER = 2;
    private java.lang.Object openkey_;
    /**
     * <code>required string openkey = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasOpenkey() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required string openkey = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getOpenkey() {
      java.lang.Object ref = openkey_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openkey_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string openkey = 2;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getOpenkeyBytes() {
      java.lang.Object ref = openkey_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openkey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string ts = 3;
    public static final int TS_FIELD_NUMBER = 3;
    private java.lang.Object ts_;
    /**
     * <code>required string ts = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasTs() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required string ts = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getTs() {
      java.lang.Object ref = ts_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          ts_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string ts = 3;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getTsBytes() {
      java.lang.Object ref = ts_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        ts_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string pf = 4;
    public static final int PF_FIELD_NUMBER = 4;
    private java.lang.Object pf_;
    /**
     * <code>required string pf = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPf() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required string pf = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getPf() {
      java.lang.Object ref = pf_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pf_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string pf = 4;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getPfBytes() {
      java.lang.Object ref = pf_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pf_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string pfkey = 5;
    public static final int PFKEY_FIELD_NUMBER = 5;
    private java.lang.Object pfkey_;
    /**
     * <code>required string pfkey = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasPfkey() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required string pfkey = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getPfkey() {
      java.lang.Object ref = pfkey_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pfkey_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string pfkey = 5;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getPfkeyBytes() {
      java.lang.Object ref = pfkey_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pfkey_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string zoneid = 6;
    public static final int ZONEID_FIELD_NUMBER = 6;
    private java.lang.Object zoneid_;
    /**
     * <code>required string zoneid = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasZoneid() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required string zoneid = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getZoneid() {
      java.lang.Object ref = zoneid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          zoneid_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string zoneid = 6;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getZoneidBytes() {
      java.lang.Object ref = zoneid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        zoneid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string productID = 7;
    public static final int PRODUCTID_FIELD_NUMBER = 7;
    private java.lang.Object productID_;
    /**
     * <code>required string productID = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasProductID() {
      return ((bitField0_ & 0x00000040) == 0x00000040);
    }
    /**
     * <code>required string productID = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getProductID() {
      java.lang.Object ref = productID_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          productID_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string productID = 7;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getProductIDBytes() {
      java.lang.Object ref = productID_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productID_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string cp_order = 8;
    public static final int CP_ORDER_FIELD_NUMBER = 8;
    private java.lang.Object cpOrder_;
    /**
     * <code>required string cp_order = 8;</code>
     *
     * <pre>
     * </pre>
     */
    public boolean hasCpOrder() {
      return ((bitField0_ & 0x00000080) == 0x00000080);
    }
    /**
     * <code>required string cp_order = 8;</code>
     *
     * <pre>
     * </pre>
     */
    public java.lang.String getCpOrder() {
      java.lang.Object ref = cpOrder_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          cpOrder_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string cp_order = 8;</code>
     *
     * <pre>
     * </pre>
     */
    public com.google.protobuf.ByteString
        getCpOrderBytes() {
      java.lang.Object ref = cpOrder_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        cpOrder_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    // required string key = 9;
    public static final int KEY_FIELD_NUMBER = 9;
    private java.lang.Object key_;
    /**
     * <code>required string key = 9;</code>
     *
     * <pre>
     *	
     * </pre>
     */
    public boolean hasKey() {
      return ((bitField0_ & 0x00000100) == 0x00000100);
    }
    /**
     * <code>required string key = 9;</code>
     *
     * <pre>
     *	
     * </pre>
     */
    public java.lang.String getKey() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          key_ = s;
        }
        return s;
      }
    }
    /**
     * <code>required string key = 9;</code>
     *
     * <pre>
     *	
     * </pre>
     */
    public com.google.protobuf.ByteString
        getKeyBytes() {
      java.lang.Object ref = key_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        key_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private void initFields() {
      openid_ = "";
      openkey_ = "";
      ts_ = "";
      pf_ = "";
      pfkey_ = "";
      zoneid_ = "";
      productID_ = "";
      cpOrder_ = "";
      key_ = "";
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasOpenid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasOpenkey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasTs()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPf()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasPfkey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasZoneid()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasProductID()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasCpOrder()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasKey()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeBytes(1, getOpenidBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeBytes(2, getOpenkeyBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeBytes(3, getTsBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeBytes(4, getPfBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeBytes(5, getPfkeyBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeBytes(6, getZoneidBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        output.writeBytes(7, getProductIDBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        output.writeBytes(8, getCpOrderBytes());
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        output.writeBytes(9, getKeyBytes());
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(1, getOpenidBytes());
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, getOpenkeyBytes());
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(3, getTsBytes());
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(4, getPfBytes());
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(5, getPfkeyBytes());
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(6, getZoneidBytes());
      }
      if (((bitField0_ & 0x00000040) == 0x00000040)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(7, getProductIDBytes());
      }
      if (((bitField0_ & 0x00000080) == 0x00000080)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(8, getCpOrderBytes());
      }
      if (((bitField0_ & 0x00000100) == 0x00000100)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(9, getKeyBytes());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.RequestYSDKBalance parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestYSDKBalance parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.RequestYSDKBalance parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.RequestYSDKBalance parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.RequestYSDKBalance prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestYSDKBalance}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.RequestYSDKBalanceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_RequestYSDKBalance_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_RequestYSDKBalance_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.RequestYSDKBalance.class, protocol.PayData.RequestYSDKBalance.Builder.class);
      }

      // Construct using protocol.PayData.RequestYSDKBalance.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        openid_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        openkey_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        ts_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        pf_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        pfkey_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        zoneid_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        productID_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        cpOrder_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        key_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_RequestYSDKBalance_descriptor;
      }

      public protocol.PayData.RequestYSDKBalance getDefaultInstanceForType() {
        return protocol.PayData.RequestYSDKBalance.getDefaultInstance();
      }

      public protocol.PayData.RequestYSDKBalance build() {
        protocol.PayData.RequestYSDKBalance result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.RequestYSDKBalance buildPartial() {
        protocol.PayData.RequestYSDKBalance result = new protocol.PayData.RequestYSDKBalance(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openid_ = openid_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.openkey_ = openkey_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.ts_ = ts_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.pf_ = pf_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.pfkey_ = pfkey_;
        if (((from_bitField0_ & 0x00000020) == 0x00000020)) {
          to_bitField0_ |= 0x00000020;
        }
        result.zoneid_ = zoneid_;
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000040;
        }
        result.productID_ = productID_;
        if (((from_bitField0_ & 0x00000080) == 0x00000080)) {
          to_bitField0_ |= 0x00000080;
        }
        result.cpOrder_ = cpOrder_;
        if (((from_bitField0_ & 0x00000100) == 0x00000100)) {
          to_bitField0_ |= 0x00000100;
        }
        result.key_ = key_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.RequestYSDKBalance) {
          return mergeFrom((protocol.PayData.RequestYSDKBalance)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.RequestYSDKBalance other) {
        if (other == protocol.PayData.RequestYSDKBalance.getDefaultInstance()) return this;
        if (other.hasOpenid()) {
          bitField0_ |= 0x00000001;
          openid_ = other.openid_;
          onChanged();
        }
        if (other.hasOpenkey()) {
          bitField0_ |= 0x00000002;
          openkey_ = other.openkey_;
          onChanged();
        }
        if (other.hasTs()) {
          bitField0_ |= 0x00000004;
          ts_ = other.ts_;
          onChanged();
        }
        if (other.hasPf()) {
          bitField0_ |= 0x00000008;
          pf_ = other.pf_;
          onChanged();
        }
        if (other.hasPfkey()) {
          bitField0_ |= 0x00000010;
          pfkey_ = other.pfkey_;
          onChanged();
        }
        if (other.hasZoneid()) {
          bitField0_ |= 0x00000020;
          zoneid_ = other.zoneid_;
          onChanged();
        }
        if (other.hasProductID()) {
          bitField0_ |= 0x00000040;
          productID_ = other.productID_;
          onChanged();
        }
        if (other.hasCpOrder()) {
          bitField0_ |= 0x00000080;
          cpOrder_ = other.cpOrder_;
          onChanged();
        }
        if (other.hasKey()) {
          bitField0_ |= 0x00000100;
          key_ = other.key_;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasOpenid()) {
          
          return false;
        }
        if (!hasOpenkey()) {
          
          return false;
        }
        if (!hasTs()) {
          
          return false;
        }
        if (!hasPf()) {
          
          return false;
        }
        if (!hasPfkey()) {
          
          return false;
        }
        if (!hasZoneid()) {
          
          return false;
        }
        if (!hasProductID()) {
          
          return false;
        }
        if (!hasCpOrder()) {
          
          return false;
        }
        if (!hasKey()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.RequestYSDKBalance parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.RequestYSDKBalance) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required string openid = 1;
      private java.lang.Object openid_ = "";
      /**
       * <code>required string openid = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasOpenid() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required string openid = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getOpenid() {
        java.lang.Object ref = openid_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          openid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string openid = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getOpenidBytes() {
        java.lang.Object ref = openid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string openid = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setOpenid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string openid = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearOpenid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openid_ = getDefaultInstance().getOpenid();
        onChanged();
        return this;
      }
      /**
       * <code>required string openid = 1;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setOpenidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openid_ = value;
        onChanged();
        return this;
      }

      // required string openkey = 2;
      private java.lang.Object openkey_ = "";
      /**
       * <code>required string openkey = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasOpenkey() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required string openkey = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getOpenkey() {
        java.lang.Object ref = openkey_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          openkey_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string openkey = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getOpenkeyBytes() {
        java.lang.Object ref = openkey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openkey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string openkey = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setOpenkey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openkey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string openkey = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearOpenkey() {
        bitField0_ = (bitField0_ & ~0x00000002);
        openkey_ = getDefaultInstance().getOpenkey();
        onChanged();
        return this;
      }
      /**
       * <code>required string openkey = 2;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setOpenkeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openkey_ = value;
        onChanged();
        return this;
      }

      // required string ts = 3;
      private java.lang.Object ts_ = "";
      /**
       * <code>required string ts = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasTs() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required string ts = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getTs() {
        java.lang.Object ref = ts_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          ts_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string ts = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getTsBytes() {
        java.lang.Object ref = ts_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          ts_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string ts = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setTs(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        ts_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string ts = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearTs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        ts_ = getDefaultInstance().getTs();
        onChanged();
        return this;
      }
      /**
       * <code>required string ts = 3;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setTsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        ts_ = value;
        onChanged();
        return this;
      }

      // required string pf = 4;
      private java.lang.Object pf_ = "";
      /**
       * <code>required string pf = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPf() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required string pf = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getPf() {
        java.lang.Object ref = pf_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          pf_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string pf = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getPfBytes() {
        java.lang.Object ref = pf_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pf_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string pf = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPf(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        pf_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string pf = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPf() {
        bitField0_ = (bitField0_ & ~0x00000008);
        pf_ = getDefaultInstance().getPf();
        onChanged();
        return this;
      }
      /**
       * <code>required string pf = 4;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPfBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        pf_ = value;
        onChanged();
        return this;
      }

      // required string pfkey = 5;
      private java.lang.Object pfkey_ = "";
      /**
       * <code>required string pfkey = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasPfkey() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required string pfkey = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getPfkey() {
        java.lang.Object ref = pfkey_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          pfkey_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string pfkey = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getPfkeyBytes() {
        java.lang.Object ref = pfkey_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pfkey_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string pfkey = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPfkey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        pfkey_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string pfkey = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearPfkey() {
        bitField0_ = (bitField0_ & ~0x00000010);
        pfkey_ = getDefaultInstance().getPfkey();
        onChanged();
        return this;
      }
      /**
       * <code>required string pfkey = 5;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setPfkeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        pfkey_ = value;
        onChanged();
        return this;
      }

      // required string zoneid = 6;
      private java.lang.Object zoneid_ = "";
      /**
       * <code>required string zoneid = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasZoneid() {
        return ((bitField0_ & 0x00000020) == 0x00000020);
      }
      /**
       * <code>required string zoneid = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getZoneid() {
        java.lang.Object ref = zoneid_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          zoneid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string zoneid = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getZoneidBytes() {
        java.lang.Object ref = zoneid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          zoneid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string zoneid = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setZoneid(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        zoneid_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string zoneid = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearZoneid() {
        bitField0_ = (bitField0_ & ~0x00000020);
        zoneid_ = getDefaultInstance().getZoneid();
        onChanged();
        return this;
      }
      /**
       * <code>required string zoneid = 6;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setZoneidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        zoneid_ = value;
        onChanged();
        return this;
      }

      // required string productID = 7;
      private java.lang.Object productID_ = "";
      /**
       * <code>required string productID = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasProductID() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required string productID = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getProductID() {
        java.lang.Object ref = productID_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          productID_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string productID = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getProductIDBytes() {
        java.lang.Object ref = productID_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          productID_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string productID = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setProductID(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        productID_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string productID = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearProductID() {
        bitField0_ = (bitField0_ & ~0x00000040);
        productID_ = getDefaultInstance().getProductID();
        onChanged();
        return this;
      }
      /**
       * <code>required string productID = 7;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setProductIDBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        productID_ = value;
        onChanged();
        return this;
      }

      // required string cp_order = 8;
      private java.lang.Object cpOrder_ = "";
      /**
       * <code>required string cp_order = 8;</code>
       *
       * <pre>
       * </pre>
       */
      public boolean hasCpOrder() {
        return ((bitField0_ & 0x00000080) == 0x00000080);
      }
      /**
       * <code>required string cp_order = 8;</code>
       *
       * <pre>
       * </pre>
       */
      public java.lang.String getCpOrder() {
        java.lang.Object ref = cpOrder_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          cpOrder_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string cp_order = 8;</code>
       *
       * <pre>
       * </pre>
       */
      public com.google.protobuf.ByteString
          getCpOrderBytes() {
        java.lang.Object ref = cpOrder_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          cpOrder_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string cp_order = 8;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setCpOrder(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cpOrder_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string cp_order = 8;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder clearCpOrder() {
        bitField0_ = (bitField0_ & ~0x00000080);
        cpOrder_ = getDefaultInstance().getCpOrder();
        onChanged();
        return this;
      }
      /**
       * <code>required string cp_order = 8;</code>
       *
       * <pre>
       * </pre>
       */
      public Builder setCpOrderBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        cpOrder_ = value;
        onChanged();
        return this;
      }

      // required string key = 9;
      private java.lang.Object key_ = "";
      /**
       * <code>required string key = 9;</code>
       *
       * <pre>
       *	
       * </pre>
       */
      public boolean hasKey() {
        return ((bitField0_ & 0x00000100) == 0x00000100);
      }
      /**
       * <code>required string key = 9;</code>
       *
       * <pre>
       *	
       * </pre>
       */
      public java.lang.String getKey() {
        java.lang.Object ref = key_;
        if (!(ref instanceof java.lang.String)) {
          java.lang.String s = ((com.google.protobuf.ByteString) ref)
              .toStringUtf8();
          key_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>required string key = 9;</code>
       *
       * <pre>
       *	
       * </pre>
       */
      public com.google.protobuf.ByteString
          getKeyBytes() {
        java.lang.Object ref = key_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          key_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>required string key = 9;</code>
       *
       * <pre>
       *	
       * </pre>
       */
      public Builder setKey(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        key_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 9;</code>
       *
       * <pre>
       *	
       * </pre>
       */
      public Builder clearKey() {
        bitField0_ = (bitField0_ & ~0x00000100);
        key_ = getDefaultInstance().getKey();
        onChanged();
        return this;
      }
      /**
       * <code>required string key = 9;</code>
       *
       * <pre>
       *	
       * </pre>
       */
      public Builder setKeyBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        key_ = value;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestYSDKBalance)
    }

    static {
      defaultInstance = new RequestYSDKBalance(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestYSDKBalance)
  }

  public interface ResponseYSDKBalanceOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 errorId = 1;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    boolean hasErrorId();
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    int getErrorId();

    // required int32 balance = 2;
    /**
     * <code>required int32 balance = 2;</code>
     *
     * <pre>
     *游戏币个数（包含了赠送游戏币）
     * </pre>
     */
    boolean hasBalance();
    /**
     * <code>required int32 balance = 2;</code>
     *
     * <pre>
     *游戏币个数（包含了赠送游戏币）
     * </pre>
     */
    int getBalance();

    // required int32 gen_balance = 3;
    /**
     * <code>required int32 gen_balance = 3;</code>
     *
     * <pre>
     *赠送游戏币个数
     * </pre>
     */
    boolean hasGenBalance();
    /**
     * <code>required int32 gen_balance = 3;</code>
     *
     * <pre>
     *赠送游戏币个数
     * </pre>
     */
    int getGenBalance();

    // required int32 first_save = 4;
    /**
     * <code>required int32 first_save = 4;</code>
     *
     * <pre>
     *是否满足首次充值，1：满足，0：不满足。
     * </pre>
     */
    boolean hasFirstSave();
    /**
     * <code>required int32 first_save = 4;</code>
     *
     * <pre>
     *是否满足首次充值，1：满足，0：不满足。
     * </pre>
     */
    int getFirstSave();

    // required int32 save_amt = 5;
    /**
     * <code>required int32 save_amt = 5;</code>
     *
     * <pre>
     *累计充值金额的游戏币数量
     * </pre>
     */
    boolean hasSaveAmt();
    /**
     * <code>required int32 save_amt = 5;</code>
     *
     * <pre>
     *累计充值金额的游戏币数量
     * </pre>
     */
    int getSaveAmt();

    // repeated .protocol.Item item = 6;
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    java.util.List<protocol.ItemData.Item> 
        getItemList();
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    protocol.ItemData.Item getItem(int index);
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    int getItemCount();
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList();
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index);

    // required int32 firstRecharge = 7;
    /**
     * <code>required int32 firstRecharge = 7;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    boolean hasFirstRecharge();
    /**
     * <code>required int32 firstRecharge = 7;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    int getFirstRecharge();
  }
  /**
   * Protobuf type {@code protocol.ResponseYSDKBalance}
   */
  public static final class ResponseYSDKBalance extends
      com.google.protobuf.GeneratedMessage
      implements ResponseYSDKBalanceOrBuilder {
    // Use ResponseYSDKBalance.newBuilder() to construct.
    private ResponseYSDKBalance(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseYSDKBalance(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseYSDKBalance defaultInstance;
    public static ResponseYSDKBalance getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseYSDKBalance getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseYSDKBalance(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              errorId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              balance_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              genBalance_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              firstSave_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              saveAmt_ = input.readInt32();
              break;
            }
            case 50: {
              if (!((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
                item_ = new java.util.ArrayList<protocol.ItemData.Item>();
                mutable_bitField0_ |= 0x00000020;
              }
              item_.add(input.readMessage(protocol.ItemData.Item.PARSER, extensionRegistry));
              break;
            }
            case 56: {
              bitField0_ |= 0x00000020;
              firstRecharge_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000020) == 0x00000020)) {
          item_ = java.util.Collections.unmodifiableList(item_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.PayData.internal_static_protocol_ResponseYSDKBalance_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.PayData.internal_static_protocol_ResponseYSDKBalance_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.PayData.ResponseYSDKBalance.class, protocol.PayData.ResponseYSDKBalance.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseYSDKBalance> PARSER =
        new com.google.protobuf.AbstractParser<ResponseYSDKBalance>() {
      public ResponseYSDKBalance parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseYSDKBalance(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseYSDKBalance> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 errorId = 1;
    public static final int ERRORID_FIELD_NUMBER = 1;
    private int errorId_;
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public boolean hasErrorId() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 errorId = 1;</code>
     *
     * <pre>
     *0表示成功其余为错误id
     * </pre>
     */
    public int getErrorId() {
      return errorId_;
    }

    // required int32 balance = 2;
    public static final int BALANCE_FIELD_NUMBER = 2;
    private int balance_;
    /**
     * <code>required int32 balance = 2;</code>
     *
     * <pre>
     *游戏币个数（包含了赠送游戏币）
     * </pre>
     */
    public boolean hasBalance() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 balance = 2;</code>
     *
     * <pre>
     *游戏币个数（包含了赠送游戏币）
     * </pre>
     */
    public int getBalance() {
      return balance_;
    }

    // required int32 gen_balance = 3;
    public static final int GEN_BALANCE_FIELD_NUMBER = 3;
    private int genBalance_;
    /**
     * <code>required int32 gen_balance = 3;</code>
     *
     * <pre>
     *赠送游戏币个数
     * </pre>
     */
    public boolean hasGenBalance() {
      return ((bitField0_ & 0x00000004) == 0x00000004);
    }
    /**
     * <code>required int32 gen_balance = 3;</code>
     *
     * <pre>
     *赠送游戏币个数
     * </pre>
     */
    public int getGenBalance() {
      return genBalance_;
    }

    // required int32 first_save = 4;
    public static final int FIRST_SAVE_FIELD_NUMBER = 4;
    private int firstSave_;
    /**
     * <code>required int32 first_save = 4;</code>
     *
     * <pre>
     *是否满足首次充值，1：满足，0：不满足。
     * </pre>
     */
    public boolean hasFirstSave() {
      return ((bitField0_ & 0x00000008) == 0x00000008);
    }
    /**
     * <code>required int32 first_save = 4;</code>
     *
     * <pre>
     *是否满足首次充值，1：满足，0：不满足。
     * </pre>
     */
    public int getFirstSave() {
      return firstSave_;
    }

    // required int32 save_amt = 5;
    public static final int SAVE_AMT_FIELD_NUMBER = 5;
    private int saveAmt_;
    /**
     * <code>required int32 save_amt = 5;</code>
     *
     * <pre>
     *累计充值金额的游戏币数量
     * </pre>
     */
    public boolean hasSaveAmt() {
      return ((bitField0_ & 0x00000010) == 0x00000010);
    }
    /**
     * <code>required int32 save_amt = 5;</code>
     *
     * <pre>
     *累计充值金额的游戏币数量
     * </pre>
     */
    public int getSaveAmt() {
      return saveAmt_;
    }

    // repeated .protocol.Item item = 6;
    public static final int ITEM_FIELD_NUMBER = 6;
    private java.util.List<protocol.ItemData.Item> item_;
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public java.util.List<protocol.ItemData.Item> getItemList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
        getItemOrBuilderList() {
      return item_;
    }
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public int getItemCount() {
      return item_.size();
    }
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public protocol.ItemData.Item getItem(int index) {
      return item_.get(index);
    }
    /**
     * <code>repeated .protocol.Item item = 6;</code>
     *
     * <pre>
     *更新物品	
     * </pre>
     */
    public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
        int index) {
      return item_.get(index);
    }

    // required int32 firstRecharge = 7;
    public static final int FIRSTRECHARGE_FIELD_NUMBER = 7;
    private int firstRecharge_;
    /**
     * <code>required int32 firstRecharge = 7;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    public boolean hasFirstRecharge() {
      return ((bitField0_ & 0x00000020) == 0x00000020);
    }
    /**
     * <code>required int32 firstRecharge = 7;</code>
     *
     * <pre>
     *是否首充		
     * </pre>
     */
    public int getFirstRecharge() {
      return firstRecharge_;
    }

    private void initFields() {
      errorId_ = 0;
      balance_ = 0;
      genBalance_ = 0;
      firstSave_ = 0;
      saveAmt_ = 0;
      item_ = java.util.Collections.emptyList();
      firstRecharge_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasErrorId()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasBalance()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasGenBalance()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFirstSave()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasSaveAmt()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasFirstRecharge()) {
        memoizedIsInitialized = 0;
        return false;
      }
      for (int i = 0; i < getItemCount(); i++) {
        if (!getItem(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, balance_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        output.writeInt32(3, genBalance_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        output.writeInt32(4, firstSave_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        output.writeInt32(5, saveAmt_);
      }
      for (int i = 0; i < item_.size(); i++) {
        output.writeMessage(6, item_.get(i));
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        output.writeInt32(7, firstRecharge_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, errorId_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, balance_);
      }
      if (((bitField0_ & 0x00000004) == 0x00000004)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, genBalance_);
      }
      if (((bitField0_ & 0x00000008) == 0x00000008)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, firstSave_);
      }
      if (((bitField0_ & 0x00000010) == 0x00000010)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, saveAmt_);
      }
      for (int i = 0; i < item_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, item_.get(i));
      }
      if (((bitField0_ & 0x00000020) == 0x00000020)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, firstRecharge_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.PayData.ResponseYSDKBalance parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseYSDKBalance parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.PayData.ResponseYSDKBalance parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.PayData.ResponseYSDKBalance parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.PayData.ResponseYSDKBalance prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseYSDKBalance}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.PayData.ResponseYSDKBalanceOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.PayData.internal_static_protocol_ResponseYSDKBalance_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.PayData.internal_static_protocol_ResponseYSDKBalance_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.PayData.ResponseYSDKBalance.class, protocol.PayData.ResponseYSDKBalance.Builder.class);
      }

      // Construct using protocol.PayData.ResponseYSDKBalance.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getItemFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        errorId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        balance_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        genBalance_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        firstSave_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        saveAmt_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
        } else {
          itemBuilder_.clear();
        }
        firstRecharge_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.PayData.internal_static_protocol_ResponseYSDKBalance_descriptor;
      }

      public protocol.PayData.ResponseYSDKBalance getDefaultInstanceForType() {
        return protocol.PayData.ResponseYSDKBalance.getDefaultInstance();
      }

      public protocol.PayData.ResponseYSDKBalance build() {
        protocol.PayData.ResponseYSDKBalance result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.PayData.ResponseYSDKBalance buildPartial() {
        protocol.PayData.ResponseYSDKBalance result = new protocol.PayData.ResponseYSDKBalance(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.errorId_ = errorId_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.balance_ = balance_;
        if (((from_bitField0_ & 0x00000004) == 0x00000004)) {
          to_bitField0_ |= 0x00000004;
        }
        result.genBalance_ = genBalance_;
        if (((from_bitField0_ & 0x00000008) == 0x00000008)) {
          to_bitField0_ |= 0x00000008;
        }
        result.firstSave_ = firstSave_;
        if (((from_bitField0_ & 0x00000010) == 0x00000010)) {
          to_bitField0_ |= 0x00000010;
        }
        result.saveAmt_ = saveAmt_;
        if (itemBuilder_ == null) {
          if (((bitField0_ & 0x00000020) == 0x00000020)) {
            item_ = java.util.Collections.unmodifiableList(item_);
            bitField0_ = (bitField0_ & ~0x00000020);
          }
          result.item_ = item_;
        } else {
          result.item_ = itemBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000040) == 0x00000040)) {
          to_bitField0_ |= 0x00000020;
        }
        result.firstRecharge_ = firstRecharge_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.PayData.ResponseYSDKBalance) {
          return mergeFrom((protocol.PayData.ResponseYSDKBalance)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.PayData.ResponseYSDKBalance other) {
        if (other == protocol.PayData.ResponseYSDKBalance.getDefaultInstance()) return this;
        if (other.hasErrorId()) {
          setErrorId(other.getErrorId());
        }
        if (other.hasBalance()) {
          setBalance(other.getBalance());
        }
        if (other.hasGenBalance()) {
          setGenBalance(other.getGenBalance());
        }
        if (other.hasFirstSave()) {
          setFirstSave(other.getFirstSave());
        }
        if (other.hasSaveAmt()) {
          setSaveAmt(other.getSaveAmt());
        }
        if (itemBuilder_ == null) {
          if (!other.item_.isEmpty()) {
            if (item_.isEmpty()) {
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000020);
            } else {
              ensureItemIsMutable();
              item_.addAll(other.item_);
            }
            onChanged();
          }
        } else {
          if (!other.item_.isEmpty()) {
            if (itemBuilder_.isEmpty()) {
              itemBuilder_.dispose();
              itemBuilder_ = null;
              item_ = other.item_;
              bitField0_ = (bitField0_ & ~0x00000020);
              itemBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getItemFieldBuilder() : null;
            } else {
              itemBuilder_.addAllMessages(other.item_);
            }
          }
        }
        if (other.hasFirstRecharge()) {
          setFirstRecharge(other.getFirstRecharge());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasErrorId()) {
          
          return false;
        }
        if (!hasBalance()) {
          
          return false;
        }
        if (!hasGenBalance()) {
          
          return false;
        }
        if (!hasFirstSave()) {
          
          return false;
        }
        if (!hasSaveAmt()) {
          
          return false;
        }
        if (!hasFirstRecharge()) {
          
          return false;
        }
        for (int i = 0; i < getItemCount(); i++) {
          if (!getItem(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.PayData.ResponseYSDKBalance parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.PayData.ResponseYSDKBalance) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 errorId = 1;
      private int errorId_ ;
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public boolean hasErrorId() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public int getErrorId() {
        return errorId_;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder setErrorId(int value) {
        bitField0_ |= 0x00000001;
        errorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 errorId = 1;</code>
       *
       * <pre>
       *0表示成功其余为错误id
       * </pre>
       */
      public Builder clearErrorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        errorId_ = 0;
        onChanged();
        return this;
      }

      // required int32 balance = 2;
      private int balance_ ;
      /**
       * <code>required int32 balance = 2;</code>
       *
       * <pre>
       *游戏币个数（包含了赠送游戏币）
       * </pre>
       */
      public boolean hasBalance() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 balance = 2;</code>
       *
       * <pre>
       *游戏币个数（包含了赠送游戏币）
       * </pre>
       */
      public int getBalance() {
        return balance_;
      }
      /**
       * <code>required int32 balance = 2;</code>
       *
       * <pre>
       *游戏币个数（包含了赠送游戏币）
       * </pre>
       */
      public Builder setBalance(int value) {
        bitField0_ |= 0x00000002;
        balance_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 balance = 2;</code>
       *
       * <pre>
       *游戏币个数（包含了赠送游戏币）
       * </pre>
       */
      public Builder clearBalance() {
        bitField0_ = (bitField0_ & ~0x00000002);
        balance_ = 0;
        onChanged();
        return this;
      }

      // required int32 gen_balance = 3;
      private int genBalance_ ;
      /**
       * <code>required int32 gen_balance = 3;</code>
       *
       * <pre>
       *赠送游戏币个数
       * </pre>
       */
      public boolean hasGenBalance() {
        return ((bitField0_ & 0x00000004) == 0x00000004);
      }
      /**
       * <code>required int32 gen_balance = 3;</code>
       *
       * <pre>
       *赠送游戏币个数
       * </pre>
       */
      public int getGenBalance() {
        return genBalance_;
      }
      /**
       * <code>required int32 gen_balance = 3;</code>
       *
       * <pre>
       *赠送游戏币个数
       * </pre>
       */
      public Builder setGenBalance(int value) {
        bitField0_ |= 0x00000004;
        genBalance_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 gen_balance = 3;</code>
       *
       * <pre>
       *赠送游戏币个数
       * </pre>
       */
      public Builder clearGenBalance() {
        bitField0_ = (bitField0_ & ~0x00000004);
        genBalance_ = 0;
        onChanged();
        return this;
      }

      // required int32 first_save = 4;
      private int firstSave_ ;
      /**
       * <code>required int32 first_save = 4;</code>
       *
       * <pre>
       *是否满足首次充值，1：满足，0：不满足。
       * </pre>
       */
      public boolean hasFirstSave() {
        return ((bitField0_ & 0x00000008) == 0x00000008);
      }
      /**
       * <code>required int32 first_save = 4;</code>
       *
       * <pre>
       *是否满足首次充值，1：满足，0：不满足。
       * </pre>
       */
      public int getFirstSave() {
        return firstSave_;
      }
      /**
       * <code>required int32 first_save = 4;</code>
       *
       * <pre>
       *是否满足首次充值，1：满足，0：不满足。
       * </pre>
       */
      public Builder setFirstSave(int value) {
        bitField0_ |= 0x00000008;
        firstSave_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 first_save = 4;</code>
       *
       * <pre>
       *是否满足首次充值，1：满足，0：不满足。
       * </pre>
       */
      public Builder clearFirstSave() {
        bitField0_ = (bitField0_ & ~0x00000008);
        firstSave_ = 0;
        onChanged();
        return this;
      }

      // required int32 save_amt = 5;
      private int saveAmt_ ;
      /**
       * <code>required int32 save_amt = 5;</code>
       *
       * <pre>
       *累计充值金额的游戏币数量
       * </pre>
       */
      public boolean hasSaveAmt() {
        return ((bitField0_ & 0x00000010) == 0x00000010);
      }
      /**
       * <code>required int32 save_amt = 5;</code>
       *
       * <pre>
       *累计充值金额的游戏币数量
       * </pre>
       */
      public int getSaveAmt() {
        return saveAmt_;
      }
      /**
       * <code>required int32 save_amt = 5;</code>
       *
       * <pre>
       *累计充值金额的游戏币数量
       * </pre>
       */
      public Builder setSaveAmt(int value) {
        bitField0_ |= 0x00000010;
        saveAmt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 save_amt = 5;</code>
       *
       * <pre>
       *累计充值金额的游戏币数量
       * </pre>
       */
      public Builder clearSaveAmt() {
        bitField0_ = (bitField0_ & ~0x00000010);
        saveAmt_ = 0;
        onChanged();
        return this;
      }

      // repeated .protocol.Item item = 6;
      private java.util.List<protocol.ItemData.Item> item_ =
        java.util.Collections.emptyList();
      private void ensureItemIsMutable() {
        if (!((bitField0_ & 0x00000020) == 0x00000020)) {
          item_ = new java.util.ArrayList<protocol.ItemData.Item>(item_);
          bitField0_ |= 0x00000020;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> itemBuilder_;

      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item> getItemList() {
        if (itemBuilder_ == null) {
          return java.util.Collections.unmodifiableList(item_);
        } else {
          return itemBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public int getItemCount() {
        if (itemBuilder_ == null) {
          return item_.size();
        } else {
          return itemBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item getItem(int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);
        } else {
          return itemBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.set(index, value);
          onChanged();
        } else {
          itemBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder setItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.set(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(value);
          onChanged();
        } else {
          itemBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item value) {
        if (itemBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureItemIsMutable();
          item_.add(index, value);
          onChanged();
        } else {
          itemBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(
          protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addItem(
          int index, protocol.ItemData.Item.Builder builderForValue) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.add(index, builderForValue.build());
          onChanged();
        } else {
          itemBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder addAllItem(
          java.lang.Iterable<? extends protocol.ItemData.Item> values) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          super.addAll(values, item_);
          onChanged();
        } else {
          itemBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder clearItem() {
        if (itemBuilder_ == null) {
          item_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
        } else {
          itemBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public Builder removeItem(int index) {
        if (itemBuilder_ == null) {
          ensureItemIsMutable();
          item_.remove(index);
          onChanged();
        } else {
          itemBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item.Builder getItemBuilder(
          int index) {
        return getItemFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.ItemOrBuilder getItemOrBuilder(
          int index) {
        if (itemBuilder_ == null) {
          return item_.get(index);  } else {
          return itemBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public java.util.List<? extends protocol.ItemData.ItemOrBuilder> 
           getItemOrBuilderList() {
        if (itemBuilder_ != null) {
          return itemBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(item_);
        }
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder() {
        return getItemFieldBuilder().addBuilder(
            protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public protocol.ItemData.Item.Builder addItemBuilder(
          int index) {
        return getItemFieldBuilder().addBuilder(
            index, protocol.ItemData.Item.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Item item = 6;</code>
       *
       * <pre>
       *更新物品	
       * </pre>
       */
      public java.util.List<protocol.ItemData.Item.Builder> 
           getItemBuilderList() {
        return getItemFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder> 
          getItemFieldBuilder() {
        if (itemBuilder_ == null) {
          itemBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.ItemData.Item, protocol.ItemData.Item.Builder, protocol.ItemData.ItemOrBuilder>(
                  item_,
                  ((bitField0_ & 0x00000020) == 0x00000020),
                  getParentForChildren(),
                  isClean());
          item_ = null;
        }
        return itemBuilder_;
      }

      // required int32 firstRecharge = 7;
      private int firstRecharge_ ;
      /**
       * <code>required int32 firstRecharge = 7;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public boolean hasFirstRecharge() {
        return ((bitField0_ & 0x00000040) == 0x00000040);
      }
      /**
       * <code>required int32 firstRecharge = 7;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public int getFirstRecharge() {
        return firstRecharge_;
      }
      /**
       * <code>required int32 firstRecharge = 7;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public Builder setFirstRecharge(int value) {
        bitField0_ |= 0x00000040;
        firstRecharge_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 firstRecharge = 7;</code>
       *
       * <pre>
       *是否首充		
       * </pre>
       */
      public Builder clearFirstRecharge() {
        bitField0_ = (bitField0_ & ~0x00000040);
        firstRecharge_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseYSDKBalance)
    }

    static {
      defaultInstance = new ResponseYSDKBalance(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseYSDKBalance)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestChoosePay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestChoosePay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseChoosePay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseChoosePay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestSubmitPayBack_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestSubmitPayBack_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseSubmitPayBack_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseSubmitPayBack_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGooglePay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGooglePay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGooglePay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGooglePay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseWeChatPay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseWeChatPay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseAliPay_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseAliPay_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestConfirmHuaWeiPurchase_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestConfirmHuaWeiPurchase_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseConfirmHuaWeiPurchase_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseConfirmHuaWeiPurchase_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestYSDKBalance_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestYSDKBalance_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseYSDKBalance_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseYSDKBalance_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tpay.proto\022\010protocol\032\nitem.proto\"0\n\020Req" +
      "uestChoosePay\022\n\n\002id\030\001 \002(\005\022\020\n\010platform\030\002 " +
      "\002(\005\"i\n\021ResponseChoosePay\022\017\n\007errorId\030\001 \002(" +
      "\005\022\n\n\002id\030\002 \002(\005\022\025\n\rtransactionID\030\003 \001(\t\022\016\n\006" +
      "result\030\004 \001(\t\022\020\n\010platform\030\005 \001(\005\">\n\024Reques" +
      "tSubmitPayBack\022\025\n\rtransactionID\030\001 \002(\t\022\017\n" +
      "\007receipt\030\002 \002(\t\"\207\001\n\025ResponseSubmitPayBack" +
      "\022\017\n\007errorId\030\001 \002(\005\022\021\n\tsuccessId\030\002 \001(\005\022\034\n\004" +
      "item\030\003 \003(\0132\016.protocol.Item\022\025\n\rtransactio" +
      "nID\030\004 \001(\t\022\025\n\rfirstRecharge\030\005 \001(\005\"v\n\020Requ",
      "estGooglePay\022\017\n\007OrderId\030\001 \002(\t\022\021\n\tProduct" +
      "Id\030\002 \002(\t\022\025\n\rPurchaseToken\030\003 \002(\t\022\020\n\010cp_or" +
      "der\030\004 \002(\t\022\025\n\rpurchaseState\030\005 \002(\005\"\203\001\n\021Res" +
      "ponseGooglePay\022\017\n\007errorId\030\001 \002(\005\022\021\n\tsucce" +
      "ssId\030\002 \001(\005\022\034\n\004item\030\003 \003(\0132\016.protocol.Item" +
      "\022\025\n\rtransactionID\030\004 \001(\t\022\025\n\rfirstRecharge" +
      "\030\005 \001(\005\"\203\001\n\021ResponseWeChatPay\022\017\n\007errorId\030" +
      "\001 \002(\005\022\021\n\tsuccessId\030\002 \001(\005\022\034\n\004item\030\003 \003(\0132\016" +
      ".protocol.Item\022\025\n\rtransactionID\030\004 \001(\t\022\025\n" +
      "\rfirstRecharge\030\005 \001(\005\"\200\001\n\016ResponseAliPay\022",
      "\017\n\007errorId\030\001 \002(\005\022\021\n\tsuccessId\030\002 \001(\005\022\034\n\004i" +
      "tem\030\003 \003(\0132\016.protocol.Item\022\025\n\rtransaction" +
      "ID\030\004 \001(\t\022\025\n\rfirstRecharge\030\005 \001(\005\"p\n\034Reque" +
      "stConfirmHuaWeiPurchase\022\025\n\rpurchaseToken" +
      "\030\001 \002(\t\022\025\n\rtransactionID\030\002 \002(\t\022\013\n\003key\030\003 \002" +
      "(\t\022\025\n\rhuaWeiOrderID\030\004 \002(\t\"\246\001\n\035ResponseCo" +
      "nfirmHuaWeiPurchase\022\017\n\007errorId\030\001 \002(\005\022\034\n\004" +
      "item\030\002 \003(\0132\016.protocol.Item\022\025\n\rfirstRecha" +
      "rge\030\003 \002(\005\022\025\n\rpurchaseToken\030\004 \002(\t\022\025\n\rtran" +
      "sactionID\030\005 \002(\t\022\021\n\tproductID\030\006 \002(\t\"\236\001\n\022R",
      "equestYSDKBalance\022\016\n\006openid\030\001 \002(\t\022\017\n\007ope" +
      "nkey\030\002 \002(\t\022\n\n\002ts\030\003 \002(\t\022\n\n\002pf\030\004 \002(\t\022\r\n\005pf" +
      "key\030\005 \002(\t\022\016\n\006zoneid\030\006 \002(\t\022\021\n\tproductID\030\007" +
      " \002(\t\022\020\n\010cp_order\030\010 \002(\t\022\013\n\003key\030\t \002(\t\"\247\001\n\023" +
      "ResponseYSDKBalance\022\017\n\007errorId\030\001 \002(\005\022\017\n\007" +
      "balance\030\002 \002(\005\022\023\n\013gen_balance\030\003 \002(\005\022\022\n\nfi" +
      "rst_save\030\004 \002(\005\022\020\n\010save_amt\030\005 \002(\005\022\034\n\004item" +
      "\030\006 \003(\0132\016.protocol.Item\022\025\n\rfirstRecharge\030" +
      "\007 \002(\005B\tB\007PayData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_RequestChoosePay_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_RequestChoosePay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestChoosePay_descriptor,
              new java.lang.String[] { "Id", "Platform", });
          internal_static_protocol_ResponseChoosePay_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_ResponseChoosePay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseChoosePay_descriptor,
              new java.lang.String[] { "ErrorId", "Id", "TransactionID", "Result", "Platform", });
          internal_static_protocol_RequestSubmitPayBack_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_RequestSubmitPayBack_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestSubmitPayBack_descriptor,
              new java.lang.String[] { "TransactionID", "Receipt", });
          internal_static_protocol_ResponseSubmitPayBack_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_ResponseSubmitPayBack_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseSubmitPayBack_descriptor,
              new java.lang.String[] { "ErrorId", "SuccessId", "Item", "TransactionID", "FirstRecharge", });
          internal_static_protocol_RequestGooglePay_descriptor =
            getDescriptor().getMessageTypes().get(4);
          internal_static_protocol_RequestGooglePay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGooglePay_descriptor,
              new java.lang.String[] { "OrderId", "ProductId", "PurchaseToken", "CpOrder", "PurchaseState", });
          internal_static_protocol_ResponseGooglePay_descriptor =
            getDescriptor().getMessageTypes().get(5);
          internal_static_protocol_ResponseGooglePay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGooglePay_descriptor,
              new java.lang.String[] { "ErrorId", "SuccessId", "Item", "TransactionID", "FirstRecharge", });
          internal_static_protocol_ResponseWeChatPay_descriptor =
            getDescriptor().getMessageTypes().get(6);
          internal_static_protocol_ResponseWeChatPay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseWeChatPay_descriptor,
              new java.lang.String[] { "ErrorId", "SuccessId", "Item", "TransactionID", "FirstRecharge", });
          internal_static_protocol_ResponseAliPay_descriptor =
            getDescriptor().getMessageTypes().get(7);
          internal_static_protocol_ResponseAliPay_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseAliPay_descriptor,
              new java.lang.String[] { "ErrorId", "SuccessId", "Item", "TransactionID", "FirstRecharge", });
          internal_static_protocol_RequestConfirmHuaWeiPurchase_descriptor =
            getDescriptor().getMessageTypes().get(8);
          internal_static_protocol_RequestConfirmHuaWeiPurchase_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestConfirmHuaWeiPurchase_descriptor,
              new java.lang.String[] { "PurchaseToken", "TransactionID", "Key", "HuaWeiOrderID", });
          internal_static_protocol_ResponseConfirmHuaWeiPurchase_descriptor =
            getDescriptor().getMessageTypes().get(9);
          internal_static_protocol_ResponseConfirmHuaWeiPurchase_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseConfirmHuaWeiPurchase_descriptor,
              new java.lang.String[] { "ErrorId", "Item", "FirstRecharge", "PurchaseToken", "TransactionID", "ProductID", });
          internal_static_protocol_RequestYSDKBalance_descriptor =
            getDescriptor().getMessageTypes().get(10);
          internal_static_protocol_RequestYSDKBalance_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestYSDKBalance_descriptor,
              new java.lang.String[] { "Openid", "Openkey", "Ts", "Pf", "Pfkey", "Zoneid", "ProductID", "CpOrder", "Key", });
          internal_static_protocol_ResponseYSDKBalance_descriptor =
            getDescriptor().getMessageTypes().get(11);
          internal_static_protocol_ResponseYSDKBalance_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseYSDKBalance_descriptor,
              new java.lang.String[] { "ErrorId", "Balance", "GenBalance", "FirstSave", "SaveAmt", "Item", "FirstRecharge", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
