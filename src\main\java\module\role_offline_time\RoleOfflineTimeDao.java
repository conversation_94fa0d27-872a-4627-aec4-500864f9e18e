package module.role_offline_time;

import entities.RoleOfflineTimeEntity;
import manager.MySql;
import module.playerstatus.PlayerOnlineTimer;

import java.util.Date;

public class RoleOfflineTimeDao {
    private static RoleOfflineTimeDao inst = null;
    public static RoleOfflineTimeDao getInstance() {
        if (inst == null) {
            inst = new RoleOfflineTimeDao();
        }
        return inst;
    }

    public RoleOfflineTimeEntity Get(String uid){
        StringBuffer stringBuffer = new StringBuffer("from RoleOfflineTimeEntity where roleUid='").append(uid).append("'");
        RoleOfflineTimeEntity entity = (RoleOfflineTimeEntity) MySql.queryForOne(stringBuffer.toString());
        if (entity == null){
            entity = new RoleOfflineTimeEntity();

            Date date = new Date();
            date.setTime(System.currentTimeMillis() - PlayerOnlineTimer.getInstance().BatteryRefreshMaxNum * 1000 * 100);
            entity.setRoleUid(uid);
            entity.setOfflineTime(date);

            MySql.insert(entity);
        }
        return entity;
    }

    public void Update(String uid){
        StringBuffer stringBuffer = new StringBuffer("from RoleOfflineTimeEntity where roleUid='").append(uid).append("'");
        RoleOfflineTimeEntity entity = (RoleOfflineTimeEntity) MySql.queryForOne(stringBuffer.toString());

        Date date = new Date();
        if (entity == null){
            entity = new RoleOfflineTimeEntity();

            entity.setRoleUid(uid);
        }
        entity.setOfflineTime(date);
        MySql.update(entity);
    }
}
