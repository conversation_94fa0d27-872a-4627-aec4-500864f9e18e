package module.map;


import com.google.protobuf.InvalidProtocolBufferException;
import entities.MapEntity;
import manager.MySql;
import manager.ReportManager;
import module.item.ItemUtils;
import protocol.MapData;
import protocol.ProtoData;

import java.util.List;

public class MapService {
    private static MapDao mapDao = null;
    private static MapService inst = null;
    public static MapService getInstance() {
        if (inst == null) {
            mapDao=MapDao.getInstance();
            inst = new MapService();
        }
        return inst;
    }
    public byte[] countMap(byte[] bytes, String uid) {
        MapData.ResponseGetMap.Builder responseMap = MapData.ResponseGetMap.newBuilder();
        try {
            List mapInfoList=mapDao.getplayerMap(uid);
            if (mapInfoList==null||mapInfoList.size()==0){
                MapDao mapDao=MapDao.getInstance();
                MapEntity mapEntity=new MapEntity();
                mapEntity.setMapnumnow(1);
                mapEntity.setMapcount(1);
                mapEntity.setFriendId(uid);
                MySql.insert(mapEntity);
                MapData.Map.Builder map=MapData.Map.newBuilder();
                map.setMapCurrent(1);
                map.setMapCount(1);
                responseMap.addMap(map);
            }else {
                for (int i = 0; i < mapInfoList.size(); i++) {
                    MapEntity entity = (MapEntity) mapInfoList.get(i);
                    responseMap.addMap(MapUtils.entityToMapData(entity));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return responseMap.build().toByteArray();
    }
    public byte[] UpdMap(byte[] bytes, String uid) {
        MapData.RequestUpdMap requestUpdMap = null;
        MapData.ResponseGetMap.Builder responseMap = MapData.ResponseGetMap.newBuilder();
        try {
            requestUpdMap=MapData.RequestUpdMap.parseFrom(bytes);
            MapEntity mapEntity = new MapEntity();
            MapData.Map map =requestUpdMap.getMap();
            mapEntity.setMapcount(map.getMapCount());
            mapEntity.setMapnumnow(map.getMapCurrent());
            StringBuffer sql=new StringBuffer();
            sql.append("update MapEntity set mapcount = ").append(map.getMapCount()).append(", mapnumnow = ").append(map.getMapCurrent())
                .append(" where friendId = '").append(uid).append("'");
            MySql.updateSomes(sql.toString());
        } catch (InvalidProtocolBufferException e) {
            e.printStackTrace();
        }
        return null;
    }
}
