package entities;

import javax.persistence.*;
import java.sql.Timestamp;

/**
 * Created by nara on 2018/4/26.
 */
@Entity
@Table(name = "accusation", schema = "", catalog = "super_star_fruit")
public class AccusationEntity {
    private int id;
    private int playerid;
    private int targetid;
    private Timestamp timestamp;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "playerid")
    public int getPlayerid() {
        return playerid;
    }

    public void setPlayerid(int playerid) {
        this.playerid = playerid;
    }

    @Basic
    @Column(name = "targetid")
    public int getTargetid() {
        return targetid;
    }

    public void setTargetid(int targetid) {
        this.targetid = targetid;
    }

    @Basic
    @Column(name = "timestamp")
    public Timestamp getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(Timestamp timestamp) {
        this.timestamp = timestamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        AccusationEntity that = (AccusationEntity) o;

        if (id != that.id) return false;
        if (playerid != that.playerid) return false;
        if (targetid != that.targetid) return false;
        if (timestamp != null ? !timestamp.equals(that.timestamp) : that.timestamp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + playerid;
        result = 31 * result + targetid;
        result = 31 * result + (timestamp != null ? timestamp.hashCode() : 0);
        return result;
    }
}
