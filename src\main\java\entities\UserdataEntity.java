package entities;

import javax.persistence.*;
import java.util.Objects;

@Entity
@Table(name = "userdata", schema = "", catalog = "super_star_fruit")
public class UserdataEntity {

    private int id;
    private String date;
    private int registernums;
    private int loginnums;
    private int next;
    private int week;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "date")
    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }

    @Basic
    @Column(name = "registernums")
    public int getRegisternums() {
        return registernums;
    }

    public void setRegisternums(int registernums) {
        this.registernums = registernums;
    }

    @Basic
    @Column(name = "loginnums")
    public int getLoginnums() {
        return loginnums;
    }

    public void setLoginnums(int loginnums) {
        this.loginnums = loginnums;
    }

    @Basic
    @Column(name = "next")
    public int getNext() {
        return next;
    }

    public void setNext(int next) {
        this.next = next;
    }


    @Basic
    @Column(name = "week")
    public int getWeek() {
        return week;
    }

    public void setWeek(int week) {
        this.week = week;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserdataEntity that = (UserdataEntity) o;
        return getId() == that.getId() &&
                getRegisternums() == that.getRegisternums() &&
                getLoginnums() == that.getLoginnums() &&
                getNext() == that.getNext() &&
                getWeek() == that.getWeek() &&
                Objects.equals(getDate(), that.getDate());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getDate(), getRegisternums(), getLoginnums(), getNext(), getWeek());
    }

    @Override
    public String toString() {
        return "UserdataEntity{" +
                "id=" + id +
                ", date='" + date + '\'' +
                ", registernums=" + registernums +
                ", loginnums=" + loginnums +
                ", next=" + next +
                ", week=" + week +
                '}';
    }
}
