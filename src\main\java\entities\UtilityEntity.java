package entities;

import javax.persistence.*;

/**
 * Created by nara on 2018/3/8.
 */
@Entity
@Table(name = "utility", schema = "", catalog = "super_star_fruit")
public class UtilityEntity {
    private int id;
    private int version;
    private String uid;
    private int eid;
    private String overstamp;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "version")
    public int getVersion() {
        return version;
    }

    public void setVersion(int version) {
        this.version = version;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "eid")
    public int getEid() {
        return eid;
    }

    public void setEid(int eid) {
        this.eid = eid;
    }

    @Basic
    @Column(name = "overstamp")
    public String getOverstamp() {
        return overstamp;
    }

    public void setOverstamp(String overstamp) {
        this.overstamp = overstamp;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        UtilityEntity that = (UtilityEntity) o;

        if (id != that.id) return false;
        if (version != that.version) return false;
        if (eid != that.eid) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (overstamp != null ? !overstamp.equals(that.overstamp) : that.overstamp != null) return false;

        return true;
    }

    @Override
    public int hashCode() {
        int result = id;
        result = 31 * result + version;
        result = 31 * result + (uid != null ? uid.hashCode() : 0);
        result = 31 * result + eid;
        result = 31 * result + (overstamp != null ? overstamp.hashCode() : 0);
        return result;
    }
}
