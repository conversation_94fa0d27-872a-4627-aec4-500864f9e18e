package module.pay;

import com.googlecode.protobuf.format.JsonFormat;
import common.SuperConfig;
import entities.ActivitiesEntity;
import entities.DressEntity;
import entities.PayorderEntity;
import io.netty.channel.ChannelHandlerContext;
import manager.MySql;
import manager.Redis;
import manager.ReportManager;
import manager.TimerHandler;
import model.RoleDressInfo;
import model.UtilityInfo;
import module.callback.CallBack;
import module.callback.CallBackOrder;
import module.item.IItem;
import module.item.ItemDao;
import module.item.ItemService;
import module.login.ILogin;
import module.login.LoginDao;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.*;
import server.SuperServerHandler;
import utils.MyUtils;
import utils.huawei.OrderService;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.InetSocketAddress;
import java.net.URLEncoder;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradeAppPayModel;
import com.alipay.api.internal.*;
import com.alipay.api.request.AlipayTradeAppPayRequest;
import com.alipay.api.response.AlipayTradeAppPayResponse;
import com.google.api.client.googleapis.auth.oauth2.GoogleCredential;
import com.google.api.client.googleapis.javanet.GoogleNetHttpTransport;
import com.google.api.client.json.jackson2.JacksonFactory;
import com.google.api.services.androidpublisher.AndroidPublisher;
import com.google.api.services.androidpublisher.AndroidPublisherScopes;
import com.google.api.services.androidpublisher.AndroidPublisher.Purchases;
import com.google.api.services.androidpublisher.model.ProductPurchase;
import com.google.auth.http.HttpCredentialsAdapter;

import javax.swing.plaf.basic.BasicMenuUI.ChangeHandler;

import com.thoughtworks.xstream.XStream;
import com.thoughtworks.xstream.mapper.Mapper.Null;

/**
 * Created by nara on 2019/3/21.
 */
public class PayDao implements IPay {

    private static String alipayCallback = "";
    private static Logger log = LoggerFactory.getLogger(PayDao.class);
    private static Logger paylog = LoggerFactory.getLogger("paylog");
    private static GoogleCredential  credentials;
    private static PayDao inst = null;

    public static PayDao getInstance() {
        if (inst == null) {
            inst = new PayDao();
        }

        return inst;
    }

    private void setPayInfoToCache(PayInfo payInfo) {
        Redis jedis = Redis.getInstance();
        Map<String, String> map = new HashMap<String, String>();
        map.put("uid", payInfo.getUid());
        map.put("payid", payInfo.getPayId() + "");
        map.put("order", payInfo.getOrder());
        map.put("productid", payInfo.getProductID() + "");
        map.put("status", payInfo.getStatus() + "");
        map.put("transaction_id", payInfo.getTransaction_id());
        map.put("platformOrder", payInfo.getPlatformOrder());
        StringBuffer s = new StringBuffer("payinfo:").append(payInfo.getUid()).append("$$")
                .append(payInfo.getProductID());
        jedis.hmset(s.toString(), map);
        resetPayInfo(s.toString());
        log.info("===========newPay=========uid:" + payInfo.getUid() + ",payId:" + payInfo.getPayId() + ",order:" + payInfo.getOrder());
    }

    private void resetPayInfo(String key) {
        Redis jedis = Redis.getInstance();
        int second = 60 * 60 * 12; // 12小时
        jedis.expire(key, second);
    }

    private PayInfo getPayInfo(String uid, String productID) {
        Redis jedis = Redis.getInstance();
        StringBuffer s = new StringBuffer("payinfo:").append(uid).append("$$").append(productID);
        Map<String, String> map = jedis.hgetAll(s.toString());
        if (map == null || map.size() == 0)
            return null;

        String order = map.get("order");
        int status = Integer.parseInt(map.get("status"));
        int payId = Integer.parseInt(map.get("payid"));
        String transaction_id = map.get("transaction_id");
        String platformOrder = map.get("platformOrder");
        PayInfo payInfo = new PayInfo(uid, order, productID, payId, platformOrder);
        payInfo.setStatus(status);
        payInfo.setTransaction_id(transaction_id);
        return payInfo;
    }

    private void setPaySuccess(String uid, String productID, String platformOrder) {
        Redis jedis = Redis.getInstance();
        StringBuffer s = new StringBuffer("payinfo:").append(uid).append("$$").append(productID);
        jedis.hset(s.toString(), "status", "1");
        jedis.hset(s.toString(), "transaction_id", platformOrder);
    }

    public PayData.ResponseChoosePay.Builder requestChoosePay(String uid, int payId, int platform) {
        log.info("===========choosePay=========uid:" + uid + ",payId:" + payId);

        ChannelHandlerContext ctx = SuperServerHandler.getCtxFromUid(uid);
        InetSocketAddress insocket = (InetSocketAddress) ctx.channel().remoteAddress();
        String clientIP = insocket.getAddress().getHostAddress();

        PayData.ResponseChoosePay.Builder builder = PayData.ResponseChoosePay.newBuilder();
        builder.setErrorId(0);
        builder.setId(payId);
        builder.setPlatform(platform);
        Map<String, String> map = null;
        String platformOrder = "";
        String cp_order = null;

        if (platform == 0)
            map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_PAY, payId);
        else
            map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payId);

        log.info("map  :  " + map.toString());    

        if (map == null) {
            log.error(uid + ":[requestChoosePay] error 1 >>>payId:" + payId);
            builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
            return builder;
        }

        String firstRecharge = map.get("firstRecharge");
        if (firstRecharge == null) {
            log.error(uid + ":[requestChoosePay] error 2 >>>no firstRecharge");
            builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
            return builder;
        }
        if (firstRecharge.equals("1")) {
            ILogin iLogin = LoginDao.getInstance();
            if (iLogin.getFirstRecharge(uid) > 3) {
                log.error(uid + ":[requestChoosePay] error 2 >>>have first recharge:" + payId);
                builder.setErrorId(ProtoData.ErrorCode.HAVEFIRSTRECHARGE_VALUE);
                return builder;
            }

        }
        String productID = map.get("Product_ID");
        if (productID == null) {
            log.error(uid + ":[requestChoosePay] error 2 >>>payId:" + payId);
            builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
            return builder;
        }
        // PayInfo payInfo = getPayInfo(uid,productID);
        // if (payInfo != null && payInfo.getStatus() != 1)
        // {
        // order = payInfo.getOrder();
        // StringBuffer s = new
        // StringBuffer("payinfo:").append(payInfo.getUid()).append("$$").append(payInfo.getProductID());
        // resetPayInfo(s.toString());
        // }
        // else

        cp_order = MyUtils.getOrder();
        int rmb = Integer.parseInt(map.get("rmb"));
        StringBuffer key = new StringBuffer("payinfo:").append(uid).append("$$").append(productID);

        if (platform == 3)// 微信支付
        {
            int money = rmb * 100;
            Map<String, String> mapResult = null;
            try {
                mapResult = WeChatUtil.unifiedOrder(uid, cp_order, productID, money, rmb + " YUAN", clientIP, key);
            } catch (Exception e) {
                return null;
            }
            if (mapResult == null)
                builder.setErrorId(ProtoData.ErrorCode.EXCELERROR_VALUE);
            else {
                platformOrder = mapResult.get("prepayid");

                String noncestr = MyUtils.setRandom();
                String timeitamp = MyUtils.GetTimestamp();
                String sign = WeChatUtil.createClientSign(noncestr, platformOrder, timeitamp);

                JSONObject json = new JSONObject();
                json.put("appid", WeChatUtil.AppID);
                json.put("noncestr", noncestr);
                json.put("package", "Sign=WXPay");
                json.put("partnerid", WeChatUtil.MCH_ID);
                json.put("prepayid", platformOrder);
                json.put("timestamp", timeitamp);
                json.put("sign", sign);
                builder.setResult(json.toString());
            }

        } else if (platform == 4)// 支付宝
        {
            try {
                float money = rmb;/// 100.0f;
                AlipayClient alipayClient = new DefaultAlipayClient(AliPaytUtil.Gateway, AliPaytUtil.AppID,
                        AliPaytUtil.RSA_PRIVATE_KEY, AliPaytUtil.FORMAT, AliPaytUtil.CHARSET,
                        AliPaytUtil.ALIPAY_PUBLIC_KEY, AliPaytUtil.SIGNTYPE);

                AlipayTradeAppPayRequest ali_request = new AlipayTradeAppPayRequest();

                AlipayTradeAppPayModel model = new AlipayTradeAppPayModel();

                model.setPassbackParams(key.toString()); // 公用参数（附加数据）
                model.setBody("获得" + map.get("name_design"));
                model.setSubject("充值" + rmb + "元");
                model.setOutTradeNo(cp_order);
                model.setTimeoutExpress("30m");
                // model.setTotalAmount(rmb+".00");
                model.setTotalAmount(money + "");
                model.setProductCode("QUICK_MSECURITY_PAY");
                ali_request.setBizModel(model);
                ali_request.setNotifyUrl(AliPaytUtil.callbackUrl);

                // 这里和普通的接口调用不同，使用的是sdkExecute
                // 返回支付宝订单信息(预处理)
                AlipayTradeAppPayResponse alipayTradeAppPayResponse = alipayClient.sdkExecute(ali_request);
                // 就是orderString 可以直接给APP请求，无需再做处理。
                String orderString = alipayTradeAppPayResponse.getBody();
                /// System.out.println(orderString);
                builder.setResult(orderString);

            } catch (AlipayApiException e) {
                log.error(e.getMessage());
                return null;
            }
        } 
        else if (platform == 5)// 华为
        {
            builder.setResult(key.toString());
        }
        else if (platform == 6)// YSDK
        {
            builder.setResult(key.toString());
        }
        PayInfo payInfo = new PayInfo(uid, cp_order, productID, payId, platformOrder);
        setPayInfoToCache(payInfo);

        log.info("requestChoosePay order:" + cp_order);
        builder.setTransactionID(cp_order);
        return builder;
    }

    public PayData.ResponseSubmitPayBack.Builder requestSubmitPayBack(String uid, String order, String receipt) {
        log.info("===========payBack=========uid:" + uid + ">>>order：" + order);

        PayData.ResponseSubmitPayBack.Builder builder = PayData.ResponseSubmitPayBack.newBuilder();
        builder.setTransactionID(order);
        synchronized (uid) {
            try {
                builder.setErrorId(0);
                String verifyResult = IosVerifyUtil.buyAppVerify(receipt, 1); // 1.先线上测试 发送平台验证
                if (verifyResult == null) { // 苹果服务器没有返回验证结果
                    log.info("===========payBack=========无订单信息!>>>uid:" + uid);
                    builder.setErrorId(ProtoData.ErrorCode.PAYRECEIPTNOTEXIST_VALUE);
                } else { // 苹果验证有返回结果
                    /// System.out.println("线上，苹果平台返回JSON:" + verifyResult);
                    JSONObject job = JSONObject.fromObject(verifyResult);
                    String states = job.getString("status");

                    if ("21007".equals(states)) { // 是沙盒环境，应沙盒测试，否则执行下面
                        verifyResult = IosVerifyUtil.buyAppVerify(receipt, 0); // 2.再沙盒测试 发送平台验证
                        /// System.out.println("沙盒环境，苹果平台返回JSON:" + verifyResult);
                        job = JSONObject.fromObject(verifyResult);
                        states = job.getString("status");
                    }

                    /// System.out.println("苹果平台返回值：job" + job);
                    if (states.equals("0")) { // 前端所提供的收据是有效的 验证成功
                        String r_receipt = job.getString("receipt");
                        JSONObject returnJson = JSONObject.fromObject(r_receipt);
                        String product_id = null;
                        String transaction_id = null;
                        if (returnJson.containsKey("in_app")) {
                            String in_app = returnJson.getString("in_app");
                            JSONObject in_appJson = JSONObject.fromObject(in_app.substring(1, in_app.length() - 1));
                            product_id = in_appJson.getString("product_id");
                            transaction_id = in_appJson.getString("transaction_id"); // 订单号
                        } else {
                            product_id = returnJson.getString("product_id");
                            transaction_id = returnJson.getString("transaction_id"); // 订单号
                        }

                        /************************************************
                         * +自己的业务逻辑
                         **********************************************************/
                        PayInfo payInfo = getPayInfo(uid, product_id);
                        if (payInfo == null) {
                            log.info("===========payBack=========订单不存在!>>>uid:" + uid + ",product_id:" + product_id + ",transaction_id:" + transaction_id);
                            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
                        } else {
                            // 如果单号一致 则保存到数据库
                            if (payInfo.getOrder().equals(order)) 
                            {
                                if (payInfo.getStatus() == 1 && payInfo.getTransaction_id().equals(transaction_id)) 
                                {
                                    log.info("===========payBack=========该订单已完成支付!>>>uid:" + uid + ",product_id:" + product_id + ",transaction_id:" + transaction_id);
                                    builder.setErrorId(ProtoData.ErrorCode.PAYEXIST_VALUE);
                                } 
                                else 
                                {
                                    if (payInfo.getStatus() != 1) 
                                    {
                                        setPaySuccess(uid, product_id, transaction_id);
                                        List<ItemData.Item.Builder> listItem = paySuccess(uid, payInfo.getPayId(), product_id, transaction_id, 0);

                                        for (int i = 0; i < listItem.size(); i++)
                                            builder.addItem(listItem.get(i));

                                        Map<String, String> mapPay = Redis
                                                .getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payInfo.getPayId());

                                        if (mapPay.get("firstRecharge").equals("1")) {
                                            ILogin iLogin = LoginDao.getInstance();
                                            builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                                        } else
                                            builder.setFirstRecharge(0);

                                        builder.setSuccessId(payInfo.getPayId());
                                    } 
                                    else 
                                    {
                                        findPayInSql(uid, transaction_id, product_id, order, 0);
                                        return null;
                                    }
                                }
                            } 
                            else 
                            {
                                findPayInSql(uid, transaction_id, product_id, order, 0);
                                return null;
                            }
                        }
                        /************************************************
                         * +自己的业务逻辑end
                         **********************************************************/
                    } else {
                        log.info("===========payBack=========支付失败!>>>uid:" + uid + ",states:" + states);
                        builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);
                    }
                }
            } catch (Exception e) {
                log.error(uid + ":[requestSubmitPayBack] error >>>uid:" + uid);
                log.error(e.getMessage(), e);
                return null;
            }
        }

        return builder;
    }

    public List<ItemData.Item.Builder> paySuccess(String uid, int payId, String product_id, String transaction_id,
            int platform) {

        // builder.setSuccessId(payId);
        List<ItemData.Item.Builder> itemList = new ArrayList<ItemData.Item.Builder>();

        // 更新物品
        IItem iItem = ItemDao.getInstance();

        Map<String, String> map = null;
        if (platform == 0)
            map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_PAY, payId);
        else
            map = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payId);

        // 活动界面充值接口
         if(payId==16){
             //
             int type=Integer.parseInt(map.get("item_id").split(",")[0]);
             TaskData.ReportUpdateActivities.Builder updateBuilder=TaskData.ReportUpdateActivities.newBuilder();
             TaskData.Activities.Builder activitiesBuilder=TaskData.Activities.newBuilder();
             StringBuffer queryActivities =new StringBuffer("from ActivitiesEntity where uid='").append(uid).append("' and type=").append(type);
             List<Object> activitiesList=MySql.queryForList(queryActivities.toString());
             activitiesBuilder.setType(type);
             Redis jedis=Redis.getInstance();
             for(int i=0;i<activitiesList.size();i++){
                 ActivitiesEntity entity  =(ActivitiesEntity)activitiesList.get(i);
                     TaskData.ActivitiesDetail.Builder activitiesDetailBU=  TaskData.ActivitiesDetail.newBuilder();
                     activitiesDetailBU.setId(entity.getActivitiesId());
                     activitiesDetailBU.setReceive(false);
                     activitiesDetailBU.setNowNum(1);
                     activitiesBuilder.addActivitiesInfo(activitiesDetailBU);
                     entity.setNum(1);
                 jedis.hset("roleactivities:"+uid,entity.getActivitiesId()+"",MyUtils.objectToJson(entity));
             }

             StringBuffer sql  =new StringBuffer("update ActivitiesEntity set num=").append(1)
                     .append(" where uid='").append(uid).append("' and type=").append(type);
             MySql.updateSomes(sql.toString());
             updateBuilder.addActivities(activitiesBuilder);
             ReportManager.reportInfo(uid,ProtoData.SToC.REPORTUPDATEACTIVITIES_VALUE,updateBuilder.build().toByteArray());
             return itemList;
         }


        if (map.get("firstRecharge").equals("1")) {
            ILogin iLogin = LoginDao.getInstance();
            iLogin.setFirstRecharge(uid);
        }

        String diamondCountStr = map.get("diamondCount");
        //String itemExcel = map.get("item_id");// Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_PAY,payId+"","item_id");
        //String[] items = itemExcel.split("\\|");
        //for (int i = 0; i < items.length; i++)
        if(!product_id.equals("com.youjgame.dreamworld.b6"))
        {
            int itemId = 2;//Integer.parseInt(items[i].split(",")[0]);
            int itemNum = Integer.parseInt(diamondCountStr);
            // 判断星币礼包是否首充，是就双倍奖励
            //if (LoginDao.shopItemMap.get(payId + "") != null) 
            // {
            //     int itemID = Integer.parseInt(LoginDao.shopItemMap.get(payId + ""));
            //     Redis redis = Redis.getInstance();
            //     int firstChargeFlag = Integer.parseInt(redis.hget("role:" + uid, "firstChargeRecord"));
            //     if ((firstChargeFlag & (1 << (itemID - 1))) == 0) {
            //         itemNum *= 2;
            //         firstChargeFlag += (1 << (itemID - 1));
            //         redis.hset("role:" + uid, "firstChargeRecord", firstChargeFlag + "");
            //         StringBuffer sql = new StringBuffer("update RoleEntity  set firstChargeRecord=")
            //                 .append(firstChargeFlag).append(" where uid='").append(uid).append("'");
            //         MySql.updateSomes(sql.toString());
            //     }
            // }
            double total = iItem.updateItemInfo(uid, itemId, itemNum);

            ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
            itemBu.setId(itemId);
            itemBu.setNum(total);

            itemList.add(itemBu);
        }

        int rmb = 0;
        if (platform == 0)
            rmb = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_PAY, payId + "", "rmb"));
        else
            rmb = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payId + "", "rmb"));   
        
        Redis jedis = Redis.getInstance();
        int nowMoney = Integer.parseInt(jedis.hget("role:" + uid, "totalmoney"));
        nowMoney += rmb;
        jedis.hset("role:" + uid, "totalmoney", nowMoney + "");
        StringBuffer hql = new StringBuffer("update RoleEntity set totalmoney = ").append(nowMoney)
                .append(" where uid = '").append(uid).append("'");
        MySql.updateSomes(hql.toString());

        PayorderEntity payorderEntity = new PayorderEntity();
        payorderEntity.setPayid(payId);
        payorderEntity.setUid(uid);
        payorderEntity.setTransactionid(transaction_id);
        payorderEntity.setTimestamp(TimerHandler.nowTimeStamp / 1000 + "");
        MySql.insert(payorderEntity);

        if( product_id.equals("com.youjgame.dreamworld.b6"))
            ItemService.getInstance().GetPetByRecharge(uid);

        paylog.info("=================paySuccess=============uid:" + uid + ",transaction_id:" + transaction_id + ",product_id:" + product_id);
        return itemList;
    }

    // private String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtSNl6A2DM3Vq0/SZAHgQgVpMCwPRUoHuL1oKvVMuENH7slycsPhv0xTTbqdaSUaMxk1zeP0o2BeL76TuAwXuPs+hNfCxr40yJWvA/vXrqxGKxF8TdZSXh8hozarXkWyVo6dVpDXRRDG8WXYUeWaWEdsHvGlbfIYxd4Jw5TdPRL1oM0In9vLHW7Vy1Osd/52OVfv81wrwkLrgbyRWiZMS5vD5MT5eDPbZU0kIyAzfK/CNZw0/zKt68fAEFABjf27SatlogDziZpqNRqDBUc1VrYTgpJ1bjYGQh5G5N1BmFLjrWdQi3JLpSfonDzJgtVmB2OkUFAiH3Ifvmt26CZhq3QIDAQAB";
    // private String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtSNl6A2DM3Vq0/SZAHgQgVpMCwPRUoHuL1oKvVMuENH7slycsPhv0xTTbqdaSUaMxk1zeP0o2BeL76TuAwXuPs+hNfCxr40yJWvA/vXrqxGKxF8TdZSXh8hozarXkWyVo6dVpDXRRDG8WXYUeWaWEdsHvGlbfIYxd4Jw5TdPRL1oM0In9vLHW7Vy1Osd/52OVfv81wrwkLrgbyRWiZMS5vD5MT5eDPbZU0kIyAzfK/CNZw0/zKt68fAEFABjf27SatlogDziZpqNRqDBUc1VrYTgpJ1bjYGQh5G5N1BmFLjrWdQi3JLpSfonDzJgtVmB2OkUFAiH3Ifvmt26CZhq3QIDAQAB";

    public PayData.ResponseGooglePay.Builder requestGooglePay(String uid,String packageName,String productId, String purchaseToken, String orderId, String cp_order) 
    {
        PayData.ResponseGooglePay.Builder builder = PayData.ResponseGooglePay.newBuilder();
        builder.setErrorId(0);

        if( credentials == null )
        {    
            try{       
                InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream("freshmania-server.json");

                List<String> scopes = new ArrayList<String>();   
                scopes.add(AndroidPublisherScopes.ANDROIDPUBLISHER);
                credentials = GoogleCredential.fromStream(inputStream).createScoped(scopes);
            }
            catch(IOException ex)
            {
                builder.setErrorId(1);
                log.error("加载 freshmania-server.json 失败:" + ex.getMessage());
                return builder;
            }

        }


        try 
        {
            AndroidPublisher publisher = new AndroidPublisher.Builder(
            GoogleNetHttpTransport.newTrustedTransport(),
            JacksonFactory.getDefaultInstance(),
            credentials ).setApplicationName("com.youjgame.dream").build();

            AndroidPublisher.Purchases purchases = publisher.purchases();
            final AndroidPublisher.Purchases.Products.Get request = purchases.products().get(packageName, productId,purchaseToken);

            final ProductPurchase purchase = request.execute();

            //订单状态 0. Purchased 1. Canceled 2. Pending
            if(purchase.getPurchaseState() !=0 )
            {
                builder.setErrorId(1);
                log.error("购买失败");
                return builder;
            }
        }
        catch(GeneralSecurityException generalSecurityException)
        {
            builder.setErrorId(1);
            log.error("校验失败:" + generalSecurityException.getMessage());
            return builder;                
        }         
        catch (Exception e) 
        {
            builder.setErrorId(1);
            log.error("校验失败 " + e.getMessage());
            return builder;

        }

        // 调用google api二次校验

        String product_id = productId;
        String transaction_id = orderId; // 平台订单号

        /************************************************
         * +自己的业务逻辑
         **********************************************************/
        PayInfo payInfo = getPayInfo(uid, product_id);
        if (payInfo == null) {
            log.info("===========payBack=========订单不存在!>>>uid:" + uid + ",product_id:" + product_id + ",transaction_id:" + transaction_id);
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
        } 
        else
        {
            // 如果单号一致 则保存到数据库
            if (payInfo.getOrder().equals(cp_order)) {
                if (payInfo.getStatus() == 1 && payInfo.getTransaction_id().equals(transaction_id)) 
                {
                    log.info("===========payBack=========该订单已完成支付!>>>uid:" + uid + ",product_id:" + product_id + ",transaction_id:" + transaction_id);
                    builder.setErrorId(ProtoData.ErrorCode.PAYEXIST_VALUE);
                } 
                else 
                {
                    if (payInfo.getStatus() != 1) 
                    {
                        setPaySuccess(uid, product_id, transaction_id);
                        List<ItemData.Item.Builder> listItem = paySuccess(uid, payInfo.getPayId(), product_id, transaction_id, 1);
                        for (int i = 0; i < listItem.size(); i++)
                            builder.addItem(listItem.get(i));

                        Map<String, String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payInfo.getPayId());

                        if (mapPay.get("firstRecharge").equals("1")) {
                            ILogin iLogin = LoginDao.getInstance();
                            builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                        } else
                            builder.setFirstRecharge(0);

                        builder.setSuccessId(payInfo.getPayId());
                        log.info("支付成功");
                    } 
                    else 
                    {
                        findPayInSql(uid, transaction_id, product_id, cp_order, 1);
                        return null;
                    }
                }
            } else {
                findPayInSql(uid, transaction_id, product_id, cp_order, 1);
                return null;
            }
        }
        /************************************************
         * +自己的业务逻辑end
         **********************************************************/
        return builder;
    }

    private boolean docheck(String content, String sign, String publicKey) throws Exception {
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        byte[] encodedKey = Base64.getDecoder().decode(publicKey);
        PublicKey pubKey = keyFactory.generatePublic(new X509EncodedKeySpec(encodedKey));
        Signature signature = Signature.getInstance("SHA1WithRSA");

        signature.initVerify(pubKey);
        signature.update(content.getBytes("utf-8"));
        return signature.verify(Base64.getDecoder().decode(sign));
    }

    private void findPayInSql(String uid, String transaction_id, String product_id, String cp_order, int platform) {
        List<Object> parameterList = new ArrayList<Object>();
        parameterList.add(0, uid);
        parameterList.add(1, transaction_id);
        parameterList.add(2, product_id);
        parameterList.add(3, cp_order);
        StringBuffer sql = new StringBuffer(" from PayorderEntity where uid = '").append(uid)
                .append("' and transactionid = '").append(transaction_id).append("'");
        CallBack callBack = null;

        if (platform == 0)
            callBack = new PayCallBack(CallBackOrder.PAYFINDBACK);
        else
            callBack = new PayCallBack(CallBackOrder.GOOGLEPAYFINDBACK);

        callBack.addParameterList(parameterList);
        MySql.queryInSql(sql.toString(), callBack, SuperConfig.DB_QUERYFORONELCALLBACK);
    }

    public PayData.ResponseConfirmHuaWeiPurchase.Builder requestConfirmHuaWeiPurchase(String uid,String key,String transactionID,String purchaseToken,String huaWeiOrderID)
    {
        PayData.ResponseConfirmHuaWeiPurchase.Builder builder = PayData.ResponseConfirmHuaWeiPurchase.newBuilder();
        builder.setErrorId(0);

        Redis jedis = Redis.getInstance();
        try{
            if (Redis.exists(key)) 
            {
                Map<String, String> map = jedis.hgetAll(key);
                if (map == null || map.size() == 0) 
                {
                    return builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);
                }

                String payID = map.get("payid");

                String cp_order = map.get("order");
                String product_id = map.get("productid");

                if (!cp_order.equals(transactionID)) 
                {
                    return builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);
                }
                if (Integer.parseInt(map.get("status")) == 0) 
                {
                    if(!OrderService.verifyToken(purchaseToken, product_id))
                    {
                        return builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);
                    }
                    OrderService.confirmPurchase(purchaseToken, product_id);
                    setPaySuccess(uid, product_id, huaWeiOrderID);
                    IPay iPay = PayDao.getInstance();

                    int payIndex = Integer.parseInt(payID);
                    List<ItemData.Item.Builder> listItem = iPay.paySuccess(uid, payIndex, product_id, huaWeiOrderID, 4);
                    for (int i = 0; i < listItem.size(); i++)
                        builder.addItem(listItem.get(i));

                    builder.setErrorId(0);
                    builder.setProductID(product_id);
                    builder.setPurchaseToken(purchaseToken);
                    
                    Map<String,String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payIndex);
            
                    if(mapPay.get("firstRecharge").equals("1") )   
                    {
                        ILogin iLogin = LoginDao.getInstance();
                        builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                    }                 
                    else
                        builder.setFirstRecharge(0);

                } else {
                    /// System.out.println("huawei_PAY already get!");
                }
                return builder;

            }
        }catch(Exception e)
        {
            /// System.out.println(e.toString());
        }
        builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);

        return builder;
    }

    public PayData.ResponseYSDKBalance.Builder requestYSDKBalance(String uid,String key, String openid,String openkey,String ts,String pf,String pfkey, String transactionID  )
    {
        PayData.ResponseYSDKBalance.Builder builder = PayData.ResponseYSDKBalance.newBuilder();

        Redis jedis = Redis.getInstance();
        try
        {
            if (Redis.exists(key)) 
            {
                Map<String, String> map = jedis.hgetAll(key);
                if (map == null || map.size() == 0) 
                {
                    return builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);
                }

                String payID = map.get("payid");

                String cp_order = map.get("order");
                String product_id = map.get("productid");

                // if (!cp_order.equals(transactionID)) 
                // {
                //     return builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);
                // }
                if (Integer.parseInt(map.get("status")) == 0) 
                {

                    setPaySuccess(uid, product_id, transactionID);
                    //IPay iPay = PayDao.getInstance();

                    int payIndex = Integer.parseInt(payID);
                    List<ItemData.Item.Builder> listItem = paySuccess(uid, payIndex, product_id, transactionID, 4);
                    for (int i = 0; i < listItem.size(); i++)
                        builder.addItem(listItem.get(i));

                    builder.setErrorId(0);
                    
                    Map<String,String> mapPay = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_GOOGLE_PAY, payIndex);
            
                    if(mapPay.get("firstRecharge").equals("1") )   
                    {
                        ILogin iLogin = LoginDao.getInstance();
                        builder.setFirstRecharge(iLogin.getFirstRecharge(uid));
                    }                 
                    else
                        builder.setFirstRecharge(0);

                } else {
                    /// System.out.println("YSDK_PAY already get!");
                }
                return builder;

            }
        }catch(Exception e)
        {
            /// System.out.println(e.toString());
        }
        builder.setErrorId(ProtoData.ErrorCode.PAYFAILED_VALUE);        
        return builder; 
        
    }

}