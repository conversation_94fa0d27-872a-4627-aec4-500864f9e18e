package entities;

import protocol.PetData;

import javax.persistence.*;
import java.util.Arrays;
import java.util.Objects;


@Entity
@Table(name = "pet", schema = "", catalog = "super_star_fruit")
public class PetEntity {
    private int id;
    private String friendId;
    private int petUId;//唯一编号
    private int petType;// 种类
    private int currentLevel;//当前等级
    private int currentExp;//当前经验
    private int starLevel;//星级
    private int strongLevel;//强化等级
    private int petCharacter;//性格
    private int mainAttribute;
    private int subAttribute;
    private int hp;
    private int atk;
    private int def;
    private int satk;
    private int sdef;
    private int speed;
    private int normalSkillId;
    private int spSkillId;
    private int normalSkillLv;
    private int spSkillLv;
    private String name;
    private int lockStatus;////锁定状态（标记位从右往左第1位是通用状态锁定，第2位是编队锁， 第3位是孵化锁 4派遣）
    private int breakLV;
    private float hpFactor;
    private float atkFactor;
    private float defFactor;
    private float satkFactor;
    private float sdefFactor;
    private float speedFactor;
    private int rarity;
    private int accessType;
    private byte[] growInfo;
    private boolean growing;
    private int mode;
    private long getTime;
    private int profession;



    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "friendId")
    public String getFriendId() {
        return friendId;
    }

    public void setFriendId(String friendId) {
        this.friendId = friendId;
    }


    @Basic
    @Column(name = "petUId")
    public int getPetUId() {
        return petUId;
    }

    public void setPetUId(int petUId) {
        this.petUId = petUId;
    }

    @Basic
    @Column(name = "petType")
    public int getPetType() {
        return petType;
    }

    public void setPetType(int petType) {
        this.petType = petType;
    }

    @Basic
    @Column(name = "currentLevel")
    public int getCurrentLevel() {
        return currentLevel;
    }

    public void setCurrentLevel(int currentLevel) {
        this.currentLevel = currentLevel;
    }

    @Basic
    @Column(name = "currentExp")
    public int getCurrentExp() {
        return currentExp;
    }

    public void setCurrentExp(int currentExp) {
        this.currentExp = currentExp;
    }

    @Basic
    @Column(name = "starLevel")
    public int getStarLevel() {
        return starLevel;
    }

    public void setStarLevel(int starLevel) {
        this.starLevel = starLevel;
    }

    @Basic
    @Column(name = "strongLevel")
    public int getStrongLevel() {
        return strongLevel;
    }

    public void setStrongLevel(int strongLevel) {
        this.strongLevel = strongLevel;
    }

    @Basic
    @Column(name = "petCharacter")
    public int getPetCharacter() {
        return petCharacter;
    }

    public void setPetCharacter(int petCharacter) {
        this.petCharacter = petCharacter;
    }

    @Basic
    @Column(name = "mainAttribute")
    public int getMainAttribute() {
        return mainAttribute;
    }

    public void setMainAttribute(int mainAttribute) {
        this.mainAttribute = mainAttribute;
    }

    @Basic
    @Column(name = "subAttribute")
    public int getSubAttribute() {
        return subAttribute;
    }

    public void setSubAttribute(int subAttribute) {
        this.subAttribute = subAttribute;
    }

    @Basic
    @Column(name = "hp")
    public int getHp() {
        return hp;
    }

    public void setHp(int hp) {
        this.hp = hp;
    }

    @Basic
    @Column(name = "atk")
    public int getAtk() {
        return atk;
    }

    public void setAtk(int atk) {
        this.atk = atk;
    }

    @Basic
    @Column(name = "def")
    public int getDef() {
        return def;
    }

    public void setDef(int def) {
        this.def = def;
    }

    @Basic
    @Column(name = "satk")
    public int getSatk() {
        return satk;
    }

    public void setSatk(int satk) {
        this.satk = satk;
    }

    @Basic
    @Column(name = "sdef")
    public int getSdef() {
        return sdef;
    }

    public void setSdef(int sdef) {
        this.sdef = sdef;
    }

    @Basic
    @Column(name = "speed")
    public int getSpeed() {
        return speed;
    }

    public void setSpeed(int speed) {
        this.speed = speed;
    }

    @Basic
    @Column(name = "normalSkillId")
    public int getNormalSkillId() {
        return normalSkillId;
    }

    public void setNormalSkillId(int normalSkillId) {
        this.normalSkillId = normalSkillId;
    }

    @Basic
    @Column(name = "spSkillId")
    public int getSpSkillId() {
        return spSkillId;
    }

    public void setSpSkillId(int spSkillId) {
        this.spSkillId = spSkillId;
    }

    @Basic
    @Column(name = "normalSkillLv")
    public int getNormalSkillLv() {
        return normalSkillLv;
    }

    public void setNormalSkillLv(int normalSkillLv) {
        this.normalSkillLv = normalSkillLv;
    }

    @Basic
    @Column(name = "spSkillLv")
    public int getSpSkillLv() {
        return spSkillLv;
    }

    public void setSpSkillLv(int spSkillLv) {
        this.spSkillLv = spSkillLv;
    }

    @Basic
    @Column(name = "name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }


    @Basic
    @Column(name = "lockStatus")
    public int getLockStatus() {
        return lockStatus;
    }

    public void setLockStatus(int lockStatus) {
        this.lockStatus = lockStatus;
    }

    @Basic
    @Column(name = "breakLV")
    public int getBreakLV() {
        return breakLV;
    }

    public void setBreakLV(int breakLV) {
        this.breakLV = breakLV;
    }

    @Basic
    @Column(name = "hpFactor")
    public float getHpFactor() {
        return hpFactor;
    }

    public void setHpFactor(float hpFactor) {
        this.hpFactor = hpFactor;
    }

    @Basic
    @Column(name = "atkFactor")
    public float getAtkFactor() {
        return atkFactor;
    }

    public void setAtkFactor(float atkFactor) {
        this.atkFactor = atkFactor;
    }

    @Basic
    @Column(name = "defFactor")
    public float getDefFactor() {
        return defFactor;
    }

    public void setDefFactor(float defFactor) {
        this.defFactor = defFactor;
    }

    @Basic
    @Column(name = "satkFactor")
    public float getSatkFactor() {
        return satkFactor;
    }

    public void setSatkFactor(float satkFactor) {
        this.satkFactor = satkFactor;
    }

    @Basic
    @Column(name = "sdefFactor")
    public float getSdefFactor() {
        return sdefFactor;
    }

    public void setSdefFactor(float sdefFactor) {
        this.sdefFactor = sdefFactor;
    }

    @Basic
    @Column(name = "speedFactor")
    public float getSpeedFactor() {
        return speedFactor;
    }

    public void setSpeedFactor(float speedFactor) {
        this.speedFactor = speedFactor;
    }

    @Basic
    @Column(name = "rarity")
    public int getRarity() {
        return rarity;
    }

    public void setRarity(int rarity) {
        this.rarity = rarity;
    }

    @Basic
    @Column(name = "accessType")
    public int getAccessType() {
        return accessType;
    }

    public void setAccessType(int accessType) {
        this.accessType = accessType;
    }

    @Basic
    @Column(name = "growInfo")

    public byte[] getGrowInfo() {
        return growInfo;
    }

    public void setGrowInfo(byte[] growInfo) {
        this.growInfo = growInfo;
    }

    @Basic
    @Column(name = "growing")
    public boolean isGrowing() {
        return growing;
    }

    public void setGrowing(boolean growing) {
        this.growing = growing;
    }

    @Basic
    @Column(name = "petMode")
    public int getMode() {
        return mode;
    }

    public void setMode(int mode) {
        this.mode = mode;
    }

    @Basic
    @Column(name = "getTime")
    public long getGetTime() {
        return getTime;
    }

    public void setGetTime(long getTime) {
        this.getTime = getTime;
    }

    @Basic
    @Column(name = "profession")
    public int getProfession() {
        return profession;
    }

    public void setProfession(int profession) {
        this.profession = profession;
    }

    public static PetData.Pet entityToPb(PetEntity entity) {
        PetData.Pet pet = null;
        if (entity != null) {
            PetData.Pet.Builder builder = PetData.Pet.newBuilder();
            builder.setCharacter(entity.getPetCharacter());
            builder.setCurrentExp(entity.getCurrentExp());
            builder.setCurrentLevel(entity.getCurrentLevel());
            builder.setPetType(entity.getPetType());
            builder.setPetUId(entity.getPetUId());
            builder.setStarLevel(entity.getStarLevel());
            builder.setStrongLevel(entity.getStrongLevel());
            builder.setNormalSkillLv(entity.getNormalSkillLv());
            builder.setSpSkillLv(entity.getSpSkillLv());
            builder.setLockStatus(entity.getLockStatus());
            builder.setName(entity.getName());
            builder.setBreakLv(entity.getBreakLV());
            builder.setRarity(entity.getRarity());
            builder.setAccessType(entity.getAccessType());
            builder.setIsGrowing(entity.isGrowing());
            builder.setMode(entity.getMode());
            builder.setHpFactor(entity.getHpFactor());
            builder.setAtkFactor(entity.getAtkFactor());
            builder.setDefFactor(entity.getDefFactor());
            builder.setSatkFactor(entity.getSatkFactor());
            builder.setSdefFactor(entity.getSdefFactor());
            builder.setSpeedFactor(entity.getSpeedFactor());
            builder.setGetTime(entity.getGetTime());
            pet = builder.build();
        }
        return pet;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PetEntity petEntity = (PetEntity) o;
        return getId() == petEntity.getId() &&
                getPetUId() == petEntity.getPetUId() &&
                getPetType() == petEntity.getPetType() &&
                getCurrentLevel() == petEntity.getCurrentLevel() &&
                getCurrentExp() == petEntity.getCurrentExp() &&
                getStarLevel() == petEntity.getStarLevel() &&
                getStrongLevel() == petEntity.getStrongLevel() &&
                getPetCharacter() == petEntity.getPetCharacter() &&
                getMainAttribute() == petEntity.getMainAttribute() &&
                getSubAttribute() == petEntity.getSubAttribute() &&
                getHp() == petEntity.getHp() &&
                getAtk() == petEntity.getAtk() &&
                getDef() == petEntity.getDef() &&
                getSatk() == petEntity.getSatk() &&
                getSdef() == petEntity.getSdef() &&
                getSpeed() == petEntity.getSpeed() &&
                getNormalSkillId() == petEntity.getNormalSkillId() &&
                getSpSkillId() == petEntity.getSpSkillId() &&
                getNormalSkillLv() == petEntity.getNormalSkillLv() &&
                getSpSkillLv() == petEntity.getSpSkillLv() &&
                getLockStatus() == petEntity.getLockStatus() &&
                getBreakLV() == petEntity.getBreakLV() &&
                Float.compare(petEntity.getHpFactor(), getHpFactor()) == 0 &&
                Float.compare(petEntity.getAtkFactor(), getAtkFactor()) == 0 &&
                Float.compare(petEntity.getDefFactor(), getDefFactor()) == 0 &&
                Float.compare(petEntity.getSatkFactor(), getSatkFactor()) == 0 &&
                Float.compare(petEntity.getSdefFactor(), getSdefFactor()) == 0 &&
                Float.compare(petEntity.getSpeedFactor(), getSpeedFactor()) == 0 &&
                getRarity() == petEntity.getRarity() &&
                getAccessType() == petEntity.getAccessType() &&
                isGrowing() == petEntity.isGrowing() &&
                getMode() == petEntity.getMode() &&
                getGetTime() == petEntity.getGetTime() &&
                Objects.equals(getFriendId(), petEntity.getFriendId()) &&
                Objects.equals(getName(), petEntity.getName()) &&
                Arrays.equals(getGrowInfo(), petEntity.getGrowInfo());
    }

    @Override
    public int hashCode() {

        int result = Objects.hash(getId(), getFriendId(), getPetUId(), getPetType(), getCurrentLevel(), getCurrentExp(), getStarLevel(), getStrongLevel(), getPetCharacter(), getMainAttribute(), getSubAttribute(), getHp(), getAtk(), getDef(), getSatk(), getSdef(), getSpeed(), getNormalSkillId(), getSpSkillId(), getNormalSkillLv(), getSpSkillLv(), getName(), getLockStatus(), getBreakLV(), getHpFactor(), getAtkFactor(), getDefFactor(), getSatkFactor(), getSdefFactor(), getSpeedFactor(), getRarity(), getAccessType(), isGrowing(), getMode(), getGetTime());
        result = 31 * result + Arrays.hashCode(getGrowInfo());
        return result;
    }

    @Override
    public String toString() {
        return "PetEntity{" +
                "id=" + id +
                ", friendId='" + friendId + '\'' +
                ", petUId=" + petUId +
                ", petType=" + petType +
                ", currentLevel=" + currentLevel +
                ", currentExp=" + currentExp +
                ", starLevel=" + starLevel +
                ", strongLevel=" + strongLevel +
                ", petCharacter=" + petCharacter +
                ", mainAttribute=" + mainAttribute +
                ", subAttribute=" + subAttribute +
                ", hp=" + hp +
                ", atk=" + atk +
                ", def=" + def +
                ", satk=" + satk +
                ", sdef=" + sdef +
                ", speed=" + speed +
                ", normalSkillId=" + normalSkillId +
                ", spSkillId=" + spSkillId +
                ", normalSkillLv=" + normalSkillLv +
                ", spSkillLv=" + spSkillLv +
                ", name='" + name + '\'' +
                ", lockStatus=" + lockStatus +
                ", breakLV=" + breakLV +
                ", hpFactor=" + hpFactor +
                ", atkFactor=" + atkFactor +
                ", defFactor=" + defFactor +
                ", satkFactor=" + satkFactor +
                ", sdefFactor=" + sdefFactor +
                ", speedFactor=" + speedFactor +
                ", rarity=" + rarity +
                ", accessType=" + accessType +
                ", growInfo=" + Arrays.toString(growInfo) +
                ", growing=" + growing +
                ", mode=" + mode +
                ", getTime=" + getTime +
                '}';
    }
}
