// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: map.proto

package protocol;

public final class MapData {
  private MapData() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
  }
  public interface MapOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required int32 mapCount = 1;
    /**
     * <code>required int32 mapCount = 1;</code>
     *
     * <pre>
     *地图总数
     * </pre>
     */
    boolean hasMapCount();
    /**
     * <code>required int32 mapCount = 1;</code>
     *
     * <pre>
     *地图总数
     * </pre>
     */
    int getMapCount();

    // required int32 mapCurrent = 2;
    /**
     * <code>required int32 mapCurrent = 2;</code>
     *
     * <pre>
     *当前地图数
     * </pre>
     */
    boolean hasMapCurrent();
    /**
     * <code>required int32 mapCurrent = 2;</code>
     *
     * <pre>
     *当前地图数
     * </pre>
     */
    int getMapCurrent();
  }
  /**
   * Protobuf type {@code protocol.Map}
   */
  public static final class Map extends
      com.google.protobuf.GeneratedMessage
      implements MapOrBuilder {
    // Use Map.newBuilder() to construct.
    private Map(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private Map(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final Map defaultInstance;
    public static Map getDefaultInstance() {
      return defaultInstance;
    }

    public Map getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private Map(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 8: {
              bitField0_ |= 0x00000001;
              mapCount_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mapCurrent_ = input.readInt32();
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.MapData.internal_static_protocol_Map_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.MapData.internal_static_protocol_Map_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.MapData.Map.class, protocol.MapData.Map.Builder.class);
    }

    public static com.google.protobuf.Parser<Map> PARSER =
        new com.google.protobuf.AbstractParser<Map>() {
      public Map parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Map(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<Map> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required int32 mapCount = 1;
    public static final int MAPCOUNT_FIELD_NUMBER = 1;
    private int mapCount_;
    /**
     * <code>required int32 mapCount = 1;</code>
     *
     * <pre>
     *地图总数
     * </pre>
     */
    public boolean hasMapCount() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required int32 mapCount = 1;</code>
     *
     * <pre>
     *地图总数
     * </pre>
     */
    public int getMapCount() {
      return mapCount_;
    }

    // required int32 mapCurrent = 2;
    public static final int MAPCURRENT_FIELD_NUMBER = 2;
    private int mapCurrent_;
    /**
     * <code>required int32 mapCurrent = 2;</code>
     *
     * <pre>
     *当前地图数
     * </pre>
     */
    public boolean hasMapCurrent() {
      return ((bitField0_ & 0x00000002) == 0x00000002);
    }
    /**
     * <code>required int32 mapCurrent = 2;</code>
     *
     * <pre>
     *当前地图数
     * </pre>
     */
    public int getMapCurrent() {
      return mapCurrent_;
    }

    private void initFields() {
      mapCount_ = 0;
      mapCurrent_ = 0;
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasMapCount()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!hasMapCurrent()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeInt32(1, mapCount_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        output.writeInt32(2, mapCurrent_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, mapCount_);
      }
      if (((bitField0_ & 0x00000002) == 0x00000002)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, mapCurrent_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.MapData.Map parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.Map parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.Map parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.Map parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.Map parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.Map parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.MapData.Map parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.MapData.Map parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.MapData.Map parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.Map parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.MapData.Map prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.Map}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.MapData.MapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.MapData.internal_static_protocol_Map_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.MapData.internal_static_protocol_Map_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.MapData.Map.class, protocol.MapData.Map.Builder.class);
      }

      // Construct using protocol.MapData.Map.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        mapCount_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        mapCurrent_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.MapData.internal_static_protocol_Map_descriptor;
      }

      public protocol.MapData.Map getDefaultInstanceForType() {
        return protocol.MapData.Map.getDefaultInstance();
      }

      public protocol.MapData.Map build() {
        protocol.MapData.Map result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.MapData.Map buildPartial() {
        protocol.MapData.Map result = new protocol.MapData.Map(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        result.mapCount_ = mapCount_;
        if (((from_bitField0_ & 0x00000002) == 0x00000002)) {
          to_bitField0_ |= 0x00000002;
        }
        result.mapCurrent_ = mapCurrent_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.MapData.Map) {
          return mergeFrom((protocol.MapData.Map)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.MapData.Map other) {
        if (other == protocol.MapData.Map.getDefaultInstance()) return this;
        if (other.hasMapCount()) {
          setMapCount(other.getMapCount());
        }
        if (other.hasMapCurrent()) {
          setMapCurrent(other.getMapCurrent());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasMapCount()) {
          
          return false;
        }
        if (!hasMapCurrent()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.MapData.Map parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.MapData.Map) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required int32 mapCount = 1;
      private int mapCount_ ;
      /**
       * <code>required int32 mapCount = 1;</code>
       *
       * <pre>
       *地图总数
       * </pre>
       */
      public boolean hasMapCount() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required int32 mapCount = 1;</code>
       *
       * <pre>
       *地图总数
       * </pre>
       */
      public int getMapCount() {
        return mapCount_;
      }
      /**
       * <code>required int32 mapCount = 1;</code>
       *
       * <pre>
       *地图总数
       * </pre>
       */
      public Builder setMapCount(int value) {
        bitField0_ |= 0x00000001;
        mapCount_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mapCount = 1;</code>
       *
       * <pre>
       *地图总数
       * </pre>
       */
      public Builder clearMapCount() {
        bitField0_ = (bitField0_ & ~0x00000001);
        mapCount_ = 0;
        onChanged();
        return this;
      }

      // required int32 mapCurrent = 2;
      private int mapCurrent_ ;
      /**
       * <code>required int32 mapCurrent = 2;</code>
       *
       * <pre>
       *当前地图数
       * </pre>
       */
      public boolean hasMapCurrent() {
        return ((bitField0_ & 0x00000002) == 0x00000002);
      }
      /**
       * <code>required int32 mapCurrent = 2;</code>
       *
       * <pre>
       *当前地图数
       * </pre>
       */
      public int getMapCurrent() {
        return mapCurrent_;
      }
      /**
       * <code>required int32 mapCurrent = 2;</code>
       *
       * <pre>
       *当前地图数
       * </pre>
       */
      public Builder setMapCurrent(int value) {
        bitField0_ |= 0x00000002;
        mapCurrent_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>required int32 mapCurrent = 2;</code>
       *
       * <pre>
       *当前地图数
       * </pre>
       */
      public Builder clearMapCurrent() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mapCurrent_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.Map)
    }

    static {
      defaultInstance = new Map(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.Map)
  }

  public interface RequestGetMapOrBuilder
      extends com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code protocol.RequestGetMap}
   *
   * <pre>
   *1370
   * </pre>
   */
  public static final class RequestGetMap extends
      com.google.protobuf.GeneratedMessage
      implements RequestGetMapOrBuilder {
    // Use RequestGetMap.newBuilder() to construct.
    private RequestGetMap(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestGetMap(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestGetMap defaultInstance;
    public static RequestGetMap getDefaultInstance() {
      return defaultInstance;
    }

    public RequestGetMap getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestGetMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.MapData.internal_static_protocol_RequestGetMap_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.MapData.internal_static_protocol_RequestGetMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.MapData.RequestGetMap.class, protocol.MapData.RequestGetMap.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestGetMap> PARSER =
        new com.google.protobuf.AbstractParser<RequestGetMap>() {
      public RequestGetMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestGetMap(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestGetMap> getParserForType() {
      return PARSER;
    }

    private void initFields() {
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.MapData.RequestGetMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.RequestGetMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.RequestGetMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.RequestGetMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.RequestGetMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.RequestGetMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.MapData.RequestGetMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.MapData.RequestGetMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.MapData.RequestGetMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.RequestGetMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.MapData.RequestGetMap prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestGetMap}
     *
     * <pre>
     *1370
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.MapData.RequestGetMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.MapData.internal_static_protocol_RequestGetMap_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.MapData.internal_static_protocol_RequestGetMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.MapData.RequestGetMap.class, protocol.MapData.RequestGetMap.Builder.class);
      }

      // Construct using protocol.MapData.RequestGetMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.MapData.internal_static_protocol_RequestGetMap_descriptor;
      }

      public protocol.MapData.RequestGetMap getDefaultInstanceForType() {
        return protocol.MapData.RequestGetMap.getDefaultInstance();
      }

      public protocol.MapData.RequestGetMap build() {
        protocol.MapData.RequestGetMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.MapData.RequestGetMap buildPartial() {
        protocol.MapData.RequestGetMap result = new protocol.MapData.RequestGetMap(this);
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.MapData.RequestGetMap) {
          return mergeFrom((protocol.MapData.RequestGetMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.MapData.RequestGetMap other) {
        if (other == protocol.MapData.RequestGetMap.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.MapData.RequestGetMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.MapData.RequestGetMap) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestGetMap)
    }

    static {
      defaultInstance = new RequestGetMap(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestGetMap)
  }

  public interface ResponseGetMapOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // repeated .protocol.Map map = 1;
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    java.util.List<protocol.MapData.Map> 
        getMapList();
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    protocol.MapData.Map getMap(int index);
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    int getMapCount();
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    java.util.List<? extends protocol.MapData.MapOrBuilder> 
        getMapOrBuilderList();
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    protocol.MapData.MapOrBuilder getMapOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code protocol.ResponseGetMap}
   *
   * <pre>
   *2370
   * </pre>
   */
  public static final class ResponseGetMap extends
      com.google.protobuf.GeneratedMessage
      implements ResponseGetMapOrBuilder {
    // Use ResponseGetMap.newBuilder() to construct.
    private ResponseGetMap(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private ResponseGetMap(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final ResponseGetMap defaultInstance;
    public static ResponseGetMap getDefaultInstance() {
      return defaultInstance;
    }

    public ResponseGetMap getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private ResponseGetMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
                map_ = new java.util.ArrayList<protocol.MapData.Map>();
                mutable_bitField0_ |= 0x00000001;
              }
              map_.add(input.readMessage(protocol.MapData.Map.PARSER, extensionRegistry));
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) == 0x00000001)) {
          map_ = java.util.Collections.unmodifiableList(map_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.MapData.internal_static_protocol_ResponseGetMap_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.MapData.internal_static_protocol_ResponseGetMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.MapData.ResponseGetMap.class, protocol.MapData.ResponseGetMap.Builder.class);
    }

    public static com.google.protobuf.Parser<ResponseGetMap> PARSER =
        new com.google.protobuf.AbstractParser<ResponseGetMap>() {
      public ResponseGetMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResponseGetMap(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<ResponseGetMap> getParserForType() {
      return PARSER;
    }

    // repeated .protocol.Map map = 1;
    public static final int MAP_FIELD_NUMBER = 1;
    private java.util.List<protocol.MapData.Map> map_;
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    public java.util.List<protocol.MapData.Map> getMapList() {
      return map_;
    }
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    public java.util.List<? extends protocol.MapData.MapOrBuilder> 
        getMapOrBuilderList() {
      return map_;
    }
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    public int getMapCount() {
      return map_.size();
    }
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    public protocol.MapData.Map getMap(int index) {
      return map_.get(index);
    }
    /**
     * <code>repeated .protocol.Map map = 1;</code>
     */
    public protocol.MapData.MapOrBuilder getMapOrBuilder(
        int index) {
      return map_.get(index);
    }

    private void initFields() {
      map_ = java.util.Collections.emptyList();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      for (int i = 0; i < getMapCount(); i++) {
        if (!getMap(i).isInitialized()) {
          memoizedIsInitialized = 0;
          return false;
        }
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      for (int i = 0; i < map_.size(); i++) {
        output.writeMessage(1, map_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < map_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, map_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.MapData.ResponseGetMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.MapData.ResponseGetMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.MapData.ResponseGetMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.ResponseGetMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.MapData.ResponseGetMap prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.ResponseGetMap}
     *
     * <pre>
     *2370
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.MapData.ResponseGetMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.MapData.internal_static_protocol_ResponseGetMap_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.MapData.internal_static_protocol_ResponseGetMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.MapData.ResponseGetMap.class, protocol.MapData.ResponseGetMap.Builder.class);
      }

      // Construct using protocol.MapData.ResponseGetMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getMapFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (mapBuilder_ == null) {
          map_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          mapBuilder_.clear();
        }
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.MapData.internal_static_protocol_ResponseGetMap_descriptor;
      }

      public protocol.MapData.ResponseGetMap getDefaultInstanceForType() {
        return protocol.MapData.ResponseGetMap.getDefaultInstance();
      }

      public protocol.MapData.ResponseGetMap build() {
        protocol.MapData.ResponseGetMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.MapData.ResponseGetMap buildPartial() {
        protocol.MapData.ResponseGetMap result = new protocol.MapData.ResponseGetMap(this);
        int from_bitField0_ = bitField0_;
        if (mapBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001)) {
            map_ = java.util.Collections.unmodifiableList(map_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.map_ = map_;
        } else {
          result.map_ = mapBuilder_.build();
        }
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.MapData.ResponseGetMap) {
          return mergeFrom((protocol.MapData.ResponseGetMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.MapData.ResponseGetMap other) {
        if (other == protocol.MapData.ResponseGetMap.getDefaultInstance()) return this;
        if (mapBuilder_ == null) {
          if (!other.map_.isEmpty()) {
            if (map_.isEmpty()) {
              map_ = other.map_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureMapIsMutable();
              map_.addAll(other.map_);
            }
            onChanged();
          }
        } else {
          if (!other.map_.isEmpty()) {
            if (mapBuilder_.isEmpty()) {
              mapBuilder_.dispose();
              mapBuilder_ = null;
              map_ = other.map_;
              bitField0_ = (bitField0_ & ~0x00000001);
              mapBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getMapFieldBuilder() : null;
            } else {
              mapBuilder_.addAllMessages(other.map_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        for (int i = 0; i < getMapCount(); i++) {
          if (!getMap(i).isInitialized()) {
            
            return false;
          }
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.MapData.ResponseGetMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.MapData.ResponseGetMap) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // repeated .protocol.Map map = 1;
      private java.util.List<protocol.MapData.Map> map_ =
        java.util.Collections.emptyList();
      private void ensureMapIsMutable() {
        if (!((bitField0_ & 0x00000001) == 0x00000001)) {
          map_ = new java.util.ArrayList<protocol.MapData.Map>(map_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.MapData.Map, protocol.MapData.Map.Builder, protocol.MapData.MapOrBuilder> mapBuilder_;

      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public java.util.List<protocol.MapData.Map> getMapList() {
        if (mapBuilder_ == null) {
          return java.util.Collections.unmodifiableList(map_);
        } else {
          return mapBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public int getMapCount() {
        if (mapBuilder_ == null) {
          return map_.size();
        } else {
          return mapBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public protocol.MapData.Map getMap(int index) {
        if (mapBuilder_ == null) {
          return map_.get(index);
        } else {
          return mapBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder setMap(
          int index, protocol.MapData.Map value) {
        if (mapBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMapIsMutable();
          map_.set(index, value);
          onChanged();
        } else {
          mapBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder setMap(
          int index, protocol.MapData.Map.Builder builderForValue) {
        if (mapBuilder_ == null) {
          ensureMapIsMutable();
          map_.set(index, builderForValue.build());
          onChanged();
        } else {
          mapBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder addMap(protocol.MapData.Map value) {
        if (mapBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMapIsMutable();
          map_.add(value);
          onChanged();
        } else {
          mapBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder addMap(
          int index, protocol.MapData.Map value) {
        if (mapBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMapIsMutable();
          map_.add(index, value);
          onChanged();
        } else {
          mapBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder addMap(
          protocol.MapData.Map.Builder builderForValue) {
        if (mapBuilder_ == null) {
          ensureMapIsMutable();
          map_.add(builderForValue.build());
          onChanged();
        } else {
          mapBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder addMap(
          int index, protocol.MapData.Map.Builder builderForValue) {
        if (mapBuilder_ == null) {
          ensureMapIsMutable();
          map_.add(index, builderForValue.build());
          onChanged();
        } else {
          mapBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder addAllMap(
          java.lang.Iterable<? extends protocol.MapData.Map> values) {
        if (mapBuilder_ == null) {
          ensureMapIsMutable();
          super.addAll(values, map_);
          onChanged();
        } else {
          mapBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder clearMap() {
        if (mapBuilder_ == null) {
          map_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          mapBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public Builder removeMap(int index) {
        if (mapBuilder_ == null) {
          ensureMapIsMutable();
          map_.remove(index);
          onChanged();
        } else {
          mapBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public protocol.MapData.Map.Builder getMapBuilder(
          int index) {
        return getMapFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public protocol.MapData.MapOrBuilder getMapOrBuilder(
          int index) {
        if (mapBuilder_ == null) {
          return map_.get(index);  } else {
          return mapBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public java.util.List<? extends protocol.MapData.MapOrBuilder> 
           getMapOrBuilderList() {
        if (mapBuilder_ != null) {
          return mapBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(map_);
        }
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public protocol.MapData.Map.Builder addMapBuilder() {
        return getMapFieldBuilder().addBuilder(
            protocol.MapData.Map.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public protocol.MapData.Map.Builder addMapBuilder(
          int index) {
        return getMapFieldBuilder().addBuilder(
            index, protocol.MapData.Map.getDefaultInstance());
      }
      /**
       * <code>repeated .protocol.Map map = 1;</code>
       */
      public java.util.List<protocol.MapData.Map.Builder> 
           getMapBuilderList() {
        return getMapFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          protocol.MapData.Map, protocol.MapData.Map.Builder, protocol.MapData.MapOrBuilder> 
          getMapFieldBuilder() {
        if (mapBuilder_ == null) {
          mapBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              protocol.MapData.Map, protocol.MapData.Map.Builder, protocol.MapData.MapOrBuilder>(
                  map_,
                  ((bitField0_ & 0x00000001) == 0x00000001),
                  getParentForChildren(),
                  isClean());
          map_ = null;
        }
        return mapBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.ResponseGetMap)
    }

    static {
      defaultInstance = new ResponseGetMap(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.ResponseGetMap)
  }

  public interface RequestUpdMapOrBuilder
      extends com.google.protobuf.MessageOrBuilder {

    // required .protocol.Map map = 1;
    /**
     * <code>required .protocol.Map map = 1;</code>
     *
     * <pre>
     *修改
     * </pre>
     */
    boolean hasMap();
    /**
     * <code>required .protocol.Map map = 1;</code>
     *
     * <pre>
     *修改
     * </pre>
     */
    protocol.MapData.Map getMap();
    /**
     * <code>required .protocol.Map map = 1;</code>
     *
     * <pre>
     *修改
     * </pre>
     */
    protocol.MapData.MapOrBuilder getMapOrBuilder();
  }
  /**
   * Protobuf type {@code protocol.RequestUpdMap}
   *
   * <pre>
   *1371 
   * </pre>
   */
  public static final class RequestUpdMap extends
      com.google.protobuf.GeneratedMessage
      implements RequestUpdMapOrBuilder {
    // Use RequestUpdMap.newBuilder() to construct.
    private RequestUpdMap(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
      this.unknownFields = builder.getUnknownFields();
    }
    private RequestUpdMap(boolean noInit) { this.unknownFields = com.google.protobuf.UnknownFieldSet.getDefaultInstance(); }

    private static final RequestUpdMap defaultInstance;
    public static RequestUpdMap getDefaultInstance() {
      return defaultInstance;
    }

    public RequestUpdMap getDefaultInstanceForType() {
      return defaultInstance;
    }

    private final com.google.protobuf.UnknownFieldSet unknownFields;
    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
        getUnknownFields() {
      return this.unknownFields;
    }
    private RequestUpdMap(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      initFields();
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(input, unknownFields,
                                     extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
            case 10: {
              protocol.MapData.Map.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) == 0x00000001)) {
                subBuilder = map_.toBuilder();
              }
              map_ = input.readMessage(protocol.MapData.Map.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(map_);
                map_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e.getMessage()).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return protocol.MapData.internal_static_protocol_RequestUpdMap_descriptor;
    }

    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return protocol.MapData.internal_static_protocol_RequestUpdMap_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              protocol.MapData.RequestUpdMap.class, protocol.MapData.RequestUpdMap.Builder.class);
    }

    public static com.google.protobuf.Parser<RequestUpdMap> PARSER =
        new com.google.protobuf.AbstractParser<RequestUpdMap>() {
      public RequestUpdMap parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new RequestUpdMap(input, extensionRegistry);
      }
    };

    @java.lang.Override
    public com.google.protobuf.Parser<RequestUpdMap> getParserForType() {
      return PARSER;
    }

    private int bitField0_;
    // required .protocol.Map map = 1;
    public static final int MAP_FIELD_NUMBER = 1;
    private protocol.MapData.Map map_;
    /**
     * <code>required .protocol.Map map = 1;</code>
     *
     * <pre>
     *修改
     * </pre>
     */
    public boolean hasMap() {
      return ((bitField0_ & 0x00000001) == 0x00000001);
    }
    /**
     * <code>required .protocol.Map map = 1;</code>
     *
     * <pre>
     *修改
     * </pre>
     */
    public protocol.MapData.Map getMap() {
      return map_;
    }
    /**
     * <code>required .protocol.Map map = 1;</code>
     *
     * <pre>
     *修改
     * </pre>
     */
    public protocol.MapData.MapOrBuilder getMapOrBuilder() {
      return map_;
    }

    private void initFields() {
      map_ = protocol.MapData.Map.getDefaultInstance();
    }
    private byte memoizedIsInitialized = -1;
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized != -1) return isInitialized == 1;

      if (!hasMap()) {
        memoizedIsInitialized = 0;
        return false;
      }
      if (!getMap().isInitialized()) {
        memoizedIsInitialized = 0;
        return false;
      }
      memoizedIsInitialized = 1;
      return true;
    }

    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        output.writeMessage(1, map_);
      }
      getUnknownFields().writeTo(output);
    }

    private int memoizedSerializedSize = -1;
    public int getSerializedSize() {
      int size = memoizedSerializedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) == 0x00000001)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, map_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSerializedSize = size;
      return size;
    }

    private static final long serialVersionUID = 0L;
    @java.lang.Override
    protected java.lang.Object writeReplace()
        throws java.io.ObjectStreamException {
      return super.writeReplace();
    }

    public static protocol.MapData.RequestUpdMap parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }
    public static protocol.MapData.RequestUpdMap parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input);
    }
    public static protocol.MapData.RequestUpdMap parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseDelimitedFrom(input, extensionRegistry);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return PARSER.parseFrom(input);
    }
    public static protocol.MapData.RequestUpdMap parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return PARSER.parseFrom(input, extensionRegistry);
    }

    public static Builder newBuilder() { return Builder.create(); }
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder(protocol.MapData.RequestUpdMap prototype) {
      return newBuilder().mergeFrom(prototype);
    }
    public Builder toBuilder() { return newBuilder(this); }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code protocol.RequestUpdMap}
     *
     * <pre>
     *1371 
     * </pre>
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder>
       implements protocol.MapData.RequestUpdMapOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return protocol.MapData.internal_static_protocol_RequestUpdMap_descriptor;
      }

      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return protocol.MapData.internal_static_protocol_RequestUpdMap_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                protocol.MapData.RequestUpdMap.class, protocol.MapData.RequestUpdMap.Builder.class);
      }

      // Construct using protocol.MapData.RequestUpdMap.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders) {
          getMapFieldBuilder();
        }
      }
      private static Builder create() {
        return new Builder();
      }

      public Builder clear() {
        super.clear();
        if (mapBuilder_ == null) {
          map_ = protocol.MapData.Map.getDefaultInstance();
        } else {
          mapBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      public Builder clone() {
        return create().mergeFrom(buildPartial());
      }

      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return protocol.MapData.internal_static_protocol_RequestUpdMap_descriptor;
      }

      public protocol.MapData.RequestUpdMap getDefaultInstanceForType() {
        return protocol.MapData.RequestUpdMap.getDefaultInstance();
      }

      public protocol.MapData.RequestUpdMap build() {
        protocol.MapData.RequestUpdMap result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      public protocol.MapData.RequestUpdMap buildPartial() {
        protocol.MapData.RequestUpdMap result = new protocol.MapData.RequestUpdMap(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) == 0x00000001)) {
          to_bitField0_ |= 0x00000001;
        }
        if (mapBuilder_ == null) {
          result.map_ = map_;
        } else {
          result.map_ = mapBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof protocol.MapData.RequestUpdMap) {
          return mergeFrom((protocol.MapData.RequestUpdMap)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(protocol.MapData.RequestUpdMap other) {
        if (other == protocol.MapData.RequestUpdMap.getDefaultInstance()) return this;
        if (other.hasMap()) {
          mergeMap(other.getMap());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        return this;
      }

      public final boolean isInitialized() {
        if (!hasMap()) {
          
          return false;
        }
        if (!getMap().isInitialized()) {
          
          return false;
        }
        return true;
      }

      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        protocol.MapData.RequestUpdMap parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (protocol.MapData.RequestUpdMap) e.getUnfinishedMessage();
          throw e;
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      // required .protocol.Map map = 1;
      private protocol.MapData.Map map_ = protocol.MapData.Map.getDefaultInstance();
      private com.google.protobuf.SingleFieldBuilder<
          protocol.MapData.Map, protocol.MapData.Map.Builder, protocol.MapData.MapOrBuilder> mapBuilder_;
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public boolean hasMap() {
        return ((bitField0_ & 0x00000001) == 0x00000001);
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public protocol.MapData.Map getMap() {
        if (mapBuilder_ == null) {
          return map_;
        } else {
          return mapBuilder_.getMessage();
        }
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public Builder setMap(protocol.MapData.Map value) {
        if (mapBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          map_ = value;
          onChanged();
        } else {
          mapBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public Builder setMap(
          protocol.MapData.Map.Builder builderForValue) {
        if (mapBuilder_ == null) {
          map_ = builderForValue.build();
          onChanged();
        } else {
          mapBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public Builder mergeMap(protocol.MapData.Map value) {
        if (mapBuilder_ == null) {
          if (((bitField0_ & 0x00000001) == 0x00000001) &&
              map_ != protocol.MapData.Map.getDefaultInstance()) {
            map_ =
              protocol.MapData.Map.newBuilder(map_).mergeFrom(value).buildPartial();
          } else {
            map_ = value;
          }
          onChanged();
        } else {
          mapBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public Builder clearMap() {
        if (mapBuilder_ == null) {
          map_ = protocol.MapData.Map.getDefaultInstance();
          onChanged();
        } else {
          mapBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public protocol.MapData.Map.Builder getMapBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMapFieldBuilder().getBuilder();
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      public protocol.MapData.MapOrBuilder getMapOrBuilder() {
        if (mapBuilder_ != null) {
          return mapBuilder_.getMessageOrBuilder();
        } else {
          return map_;
        }
      }
      /**
       * <code>required .protocol.Map map = 1;</code>
       *
       * <pre>
       *修改
       * </pre>
       */
      private com.google.protobuf.SingleFieldBuilder<
          protocol.MapData.Map, protocol.MapData.Map.Builder, protocol.MapData.MapOrBuilder> 
          getMapFieldBuilder() {
        if (mapBuilder_ == null) {
          mapBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              protocol.MapData.Map, protocol.MapData.Map.Builder, protocol.MapData.MapOrBuilder>(
                  map_,
                  getParentForChildren(),
                  isClean());
          map_ = null;
        }
        return mapBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:protocol.RequestUpdMap)
    }

    static {
      defaultInstance = new RequestUpdMap(true);
      defaultInstance.initFields();
    }

    // @@protoc_insertion_point(class_scope:protocol.RequestUpdMap)
  }

  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_Map_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_Map_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestGetMap_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestGetMap_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_ResponseGetMap_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_ResponseGetMap_fieldAccessorTable;
  private static com.google.protobuf.Descriptors.Descriptor
    internal_static_protocol_RequestUpdMap_descriptor;
  private static
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_protocol_RequestUpdMap_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tmap.proto\022\010protocol\032\nitem.proto\"+\n\003Map" +
      "\022\020\n\010mapCount\030\001 \002(\005\022\022\n\nmapCurrent\030\002 \002(\005\"\017" +
      "\n\rRequestGetMap\",\n\016ResponseGetMap\022\032\n\003map" +
      "\030\001 \003(\0132\r.protocol.Map\"+\n\rRequestUpdMap\022\032" +
      "\n\003map\030\001 \002(\0132\r.protocol.MapB\tB\007MapData"
    };
    com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner assigner =
      new com.google.protobuf.Descriptors.FileDescriptor.InternalDescriptorAssigner() {
        public com.google.protobuf.ExtensionRegistry assignDescriptors(
            com.google.protobuf.Descriptors.FileDescriptor root) {
          descriptor = root;
          internal_static_protocol_Map_descriptor =
            getDescriptor().getMessageTypes().get(0);
          internal_static_protocol_Map_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_Map_descriptor,
              new java.lang.String[] { "MapCount", "MapCurrent", });
          internal_static_protocol_RequestGetMap_descriptor =
            getDescriptor().getMessageTypes().get(1);
          internal_static_protocol_RequestGetMap_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestGetMap_descriptor,
              new java.lang.String[] { });
          internal_static_protocol_ResponseGetMap_descriptor =
            getDescriptor().getMessageTypes().get(2);
          internal_static_protocol_ResponseGetMap_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_ResponseGetMap_descriptor,
              new java.lang.String[] { "Map", });
          internal_static_protocol_RequestUpdMap_descriptor =
            getDescriptor().getMessageTypes().get(3);
          internal_static_protocol_RequestUpdMap_fieldAccessorTable = new
            com.google.protobuf.GeneratedMessage.FieldAccessorTable(
              internal_static_protocol_RequestUpdMap_descriptor,
              new java.lang.String[] { "Map", });
          return null;
        }
      };
    com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          protocol.ItemData.getDescriptor(),
        }, assigner);
  }

  // @@protoc_insertion_point(outer_class_scope)
}
