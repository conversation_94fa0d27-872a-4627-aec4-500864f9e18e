package module.pvp;

import com.google.gson.Gson;
import com.google.protobuf.InvalidProtocolBufferException;
import entities.PVPBaseDataEntity;
import entities.PVPPetsEntity;
import entities.RoleEntity;
import manager.MySql;
import manager.ReportManager;
import protocol.PVPData;
import protocol.ProtoData;

import java.util.Comparator;
import java.util.Random;
import java.util.SortedSet;
import java.util.TreeSet;

public class PVPService {
    private static PVPService inst = null;

    private PVPService() {
    }

    public static PVPService getInstance() {
        if (inst == null) {
            inst = new PVPService();
        }
        return inst;
    }



    //   PVP宠物存储
    public byte[] RequestPVPPetOperate(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        PVPData.RequestPVPPetOperate requestPVPPetOperate = PVPData.RequestPVPPetOperate.parseFrom(bytes);
        PVPData.ResponsePVPPetOperate.Builder builder = PVPData.ResponsePVPPetOperate.newBuilder();

        builder.setType(requestPVPPetOperate.getType());
        PVPPetData pvpPetData = new PVPPetData();
        // 1 修改自己的编队  2 获取自己的编队  3 获取他人的编队
        switch (requestPVPPetOperate.getType()){
            case 1:

                String petStr1 = pvpPetData.SetInfo(requestPVPPetOperate.getPets().getPet1()).toJson();
                String petStr2 = pvpPetData.SetInfo(requestPVPPetOperate.getPets().getPet2()).toJson();
                String petStr3 = pvpPetData.SetInfo(requestPVPPetOperate.getPets().getPet3()).toJson();
                String petStr4 = pvpPetData.SetInfo(requestPVPPetOperate.getPets().getPet4()).toJson();
                String petStr5 = pvpPetData.SetInfo(requestPVPPetOperate.getPets().getPet5()).toJson();

                System.out.println(petStr1.length());
                System.out.println(petStr2.length());
                System.out.println(petStr3.length());
                System.out.println(petStr4.length());
                System.out.println(petStr5.length());

                PVPPetsEntity pvpPetsEntity = PVPDao.getInstance().GetPVPPets(uid);
                pvpPetsEntity.setPet1(petStr1);
                pvpPetsEntity.setPet2(petStr2);
                pvpPetsEntity.setPet3(petStr3);
                pvpPetsEntity.setPet4(petStr4);
                pvpPetsEntity.setPet5(petStr5);
                MySql.update(pvpPetsEntity);
//                PVPPetData pvpPetData1 = gson.fromJson(str, PVPPetData.class);
//                System.out.println(pvpPetData1.nameItem);

                break;
            case 2:
                builder.setPets(GetPVPPets(uid));
                break;
            case 3:
                builder.setPets(GetPVPPets(String.valueOf(requestPVPPetOperate.getUserUid())));
                break;
        }

        return builder.build().toByteArray();
    }

    //   PVP个人信息
    public byte[] RequestPVPBaseData(byte[] bytes, String uid) throws InvalidProtocolBufferException {
//        PVPData.RequestPVPBaseData requestPVPBaseData = PVPData.RequestPVPBaseData.parseFrom(bytes);
//        PVPBaseDataEntity pvpPetsEntity = PVPDao.getInstance().GetPVPBaseData(uid);
        PVPBaseDataEntity pvpPetsEntity = PVPRank.getInstance().GetPVPPlayerBaseData(uid);

        PVPData.ResponsePVPBaseData.Builder builder = PVPData.ResponsePVPBaseData.newBuilder();
        builder.setRank(pvpPetsEntity.getRank());
        builder.setScore(pvpPetsEntity.getScore());
        builder.setFail(pvpPetsEntity.getFail());
        builder.setVictory(pvpPetsEntity.getVictory());
        return builder.build().toByteArray();
    }

    //   PVP开始战斗
    public byte[] RequestPVPBattle(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        PVPData.ResponsePVPBattle.Builder builder = PVPData.ResponsePVPBattle.newBuilder();
        if (PVPRank.getInstance().pvpPlayerBaseDataArrayList.size() > 1 && new Random().nextInt(10) < 100){
            String roleUid = PVPRank.getInstance().GetBattlePlayerPets(uid, builder);
            builder.setPets(GetPVPPets(roleUid));
            if (roleUid.charAt(0) == '#'){
                builder.setName(roleUid.split("#")[1]);
                builder.setSex(1);
                builder.setHead(1);
            }else {
                StringBuffer sql=new  StringBuffer("from RoleEntity where uid='").append(roleUid).append("'");
                RoleEntity roleEntity =(RoleEntity)MySql.queryForOne(sql.toString());
                builder.setName(roleEntity.getName());
                builder.setSex(roleEntity.getSex());
                builder.setHead(roleEntity.getHead());
            }
        }else{
            builder.setPets(PVPRobot.getInstance().GetPVPRobot());

            builder.setName(String.valueOf(new Random().nextInt(999999)));
//            builder.setSex(1);
//            builder.setHead(2);
            builder.setSex(new Random().nextInt(10) > 5 ? 1 : 2);
            builder.setHead(3);
        }


//        String roleUid = PVPRank.getInstance().GetBattlePlayerPets(uid);
//        builder.setPets(GetPVPPets(roleUid));
        return builder.build().toByteArray();
    }

    //   PVP战斗结果 输赢
    public void RequestPVPBattleResult(byte[] bytes, String uid) throws InvalidProtocolBufferException {
        PVPData.RequestPVPBattleResult requestPVPBattleResult = PVPData.RequestPVPBattleResult.parseFrom(bytes);

        PVPRank.getInstance().PVPPlayerBattleResult(uid, requestPVPBattleResult.getVictory());

        PVPBaseDataEntity pvpPetsEntity = PVPRank.getInstance().GetPVPPlayerBaseData(uid);
        // 发送PVP基本信息
        PVPData.ResponsePVPBaseData.Builder builder = PVPData.ResponsePVPBaseData.newBuilder();
        builder.setRank(pvpPetsEntity.getRank());
        builder.setScore(pvpPetsEntity.getScore());
        builder.setFail(pvpPetsEntity.getFail());
        builder.setVictory(pvpPetsEntity.getVictory());
        ReportManager.reportInfo(uid, ProtoData.SToC.RESPONSEPVPBaseData_VALUE, builder.build().toByteArray());

//        MySql.update(pvpPetsEntity);
    }


    // 根据 roleUid 获取PVP编队
    private protocol.PVPData.PVPTeam.Builder GetPVPPets(String roleUid){
        PVPPetData pvpPetData = new PVPPetData();
        PVPPetsEntity getPVPPets = PVPDao.getInstance().GetPVPPets(roleUid);
        protocol.PVPData.PVPTeam.Builder value = PVPData.PVPTeam.newBuilder();
        if (getPVPPets.getPet1() != null && getPVPPets.getPet1().length() > 0){
            value.setPet1(pvpPetData.GetPetData(getPVPPets.getPet1()));
        }
        if (getPVPPets.getPet2() != null && getPVPPets.getPet2().length() > 0){
            value.setPet2(pvpPetData.GetPetData(getPVPPets.getPet2()));
        }
        if (getPVPPets.getPet3() != null && getPVPPets.getPet3().length() > 0){
            value.setPet3(pvpPetData.GetPetData(getPVPPets.getPet3()));
        }
        if (getPVPPets.getPet4() != null && getPVPPets.getPet4().length() > 0){
            value.setPet4(pvpPetData.GetPetData(getPVPPets.getPet4()));
        }
        if (getPVPPets.getPet5() != null && getPVPPets.getPet5().length() > 0){
            value.setPet5(pvpPetData.GetPetData(getPVPPets.getPet5()));
        }

        return value;
    }

    public byte[] RequestPVPBattleRemainTime(byte[] inBytes, String uid) {
        PVPData.ResponsePVPBattleRemainTime.Builder builder = PVPData.ResponsePVPBattleRemainTime.newBuilder();
        builder.setTimeSecond(PVPTImer.getInstance().getCurTime());
        return builder.build().toByteArray();
    }
}
