package entities;


import javax.persistence.*;
import java.sql.Time;
import java.sql.Timestamp;
import java.util.Objects;

@Entity
@Table(name = "ticketmarketrecord", schema = "", catalog = "super_star_fruit")
public class TicketmarketrecordEntity {
    private int id;
    private String uid;
    private int goodsId;
    private int type;
    private int nums;
    private int priece;
    private Timestamp time;

    @Id
    @Column(name = "id")
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Basic
    @Column(name = "uid")
    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    @Basic
    @Column(name = "goodsId")
    public int getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(int goodsId) {
        this.goodsId = goodsId;
    }

    @Basic
    @Column(name = "type")
    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    @Basic
    @Column(name = "nums")
    public int getNums() {
        return nums;
    }

    public void setNums(int nums) {
        this.nums = nums;
    }

    @Basic
    @Column(name = "priece")
    public int getPriece() {
        return priece;
    }

    public void setPriece(int priece) {
        this.priece = priece;
    }

    @Basic
    @Column(name = "time")
    public Timestamp getTime() {
        return time;
    }

    public void setTime(Timestamp time) {
        this.time = time;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TicketmarketrecordEntity that = (TicketmarketrecordEntity) o;
        return getId() == that.getId() &&
                getGoodsId() == that.getGoodsId() &&
                getType() == that.getType() &&
                getNums() == that.getNums() &&
                getPriece() == that.getPriece() &&
                Objects.equals(getUid(), that.getUid()) &&
                Objects.equals(getTime(), that.getTime());
    }

    @Override
    public int hashCode() {

        return Objects.hash(getId(), getUid(), getGoodsId(), getType(), getNums(), getPriece(), getTime());
    }
}
