package module.pay;

import module.activity.RechargeRebate.RechargeRebateService;
import module.item.ItemUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.PayData;
import protocol.ProtoData;

/**
 * Created by nara on 2019/3/21.
 */
public class PayService {
    private static Logger log = LoggerFactory.getLogger(PayService.class);
    private static PayService inst = null;
    public static PayService getInstance() {
        if (inst == null) {
            inst = new PayService();
        }
        return inst;
    }

    public byte[] requestChoosePay(byte[] bytes,String uid){
        log.info("===========requestChoosePay=========");

        PayData.RequestChoosePay requestChoosePay = null;
        PayData.ResponseChoosePay.Builder builder = PayData.ResponseChoosePay.newBuilder();
        if (uid == null) {
            /// System.out.println("uid == null");
            builder.setId(0);
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
            System.err.println("===========UID==NULL=========");
        }
        else
        {
            try {
                requestChoosePay = PayData.RequestChoosePay.parseFrom(bytes);
            }catch (Exception e){
                /// System.out.println("requestChoosePay error");

                log.error(e.getMessage(), e);
            }

            if (requestChoosePay == null){
                builder.setId(0);
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[requestChoosePay] error");
            }else {
                IPay iPay = PayDao.getInstance();
                builder = iPay.requestChoosePay(uid,requestChoosePay.getId(),requestChoosePay.getPlatform());
                //RechargeRebateService.getInstance().ChargeDiamond(uid, requestChoosePay.getId());
            }
        }
        return builder.build().toByteArray();
    }

    public byte[] requestSubmitPayBack(byte[] bytes,String uid){
        PayData.RequestSubmitPayBack requestSubmitPayBack = null;
        PayData.ResponseSubmitPayBack.Builder builder = PayData.ResponseSubmitPayBack.newBuilder();
        if (uid == null) {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } else {
            try {
                requestSubmitPayBack = PayData.RequestSubmitPayBack.parseFrom(bytes);
            }catch (Exception e){
                log.error(e.getMessage(), e);
            }
            if (requestSubmitPayBack == null){
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[requestSubmitPayBack] error");
            }else {
                IPay iPay = PayDao.getInstance();
                builder = iPay.requestSubmitPayBack(uid,requestSubmitPayBack.getTransactionID(),requestSubmitPayBack.getReceipt());
//                builder.setTransactionID(requestSubmitPayBack.getTransactionID());
//                builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }

    public byte[] requestGooglePayBack(byte[] bytes,String uid)
    {
        PayData.RequestGooglePay requestGooglePay = null;
        PayData.ResponseGooglePay.Builder builder = PayData.ResponseGooglePay.newBuilder();
        if (uid == null) 
        {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } 
        else 
        {
            try 
            {
                requestGooglePay = PayData.RequestGooglePay.parseFrom(bytes);
            }
            catch (Exception e)
            {
                log.error(e.getMessage(), e);
            }
            if (requestGooglePay == null)
            {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[requestGooglePay] error");
            }
            else 
            {
                IPay iPay = PayDao.getInstance();
                builder = iPay.requestGooglePay(uid,"com.youjgame.dream",requestGooglePay.getProductId(),requestGooglePay.getPurchaseToken(), requestGooglePay.getOrderId(), requestGooglePay.getCpOrder());
            }
        }
        return builder == null ? null : builder.build().toByteArray();
    }    

    public byte[] requestConfirmHuaWeiPurchase(byte[] bytes,String uid)
    {
        PayData.RequestConfirmHuaWeiPurchase requestConfirmPurchase = null;
        PayData.ResponseConfirmHuaWeiPurchase.Builder builder = PayData.ResponseConfirmHuaWeiPurchase.newBuilder();
        if (uid == null) 
        {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } 
        else 
        {
            try 
            {
                requestConfirmPurchase = PayData.RequestConfirmHuaWeiPurchase.parseFrom(bytes);
            }
            catch (Exception e)
            {
                log.error(e.getMessage(), e);
            }
            if (requestConfirmPurchase == null)
            {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[requestGooglePay] error");
            }
            else 
            {
                IPay iPay = PayDao.getInstance();
                builder = iPay.requestConfirmHuaWeiPurchase(uid,requestConfirmPurchase.getKey(),requestConfirmPurchase.getTransactionID(),requestConfirmPurchase.getPurchaseToken(),requestConfirmPurchase.getHuaWeiOrderID());
            }
        }
        return builder == null ? null : builder.build().toByteArray();        
    }  
    
    public byte[] requestYSDKBalance(byte[] bytes,String uid)
    {
        PayData.RequestYSDKBalance requestYSDKBalance = null;
        PayData.ResponseYSDKBalance.Builder builder = PayData.ResponseYSDKBalance.newBuilder();
        if (uid == null) 
        {
            builder.setErrorId(ProtoData.ErrorCode.USEROFFLINE_VALUE);
        } 
        else 
        {
            try 
            {
                requestYSDKBalance = PayData.RequestYSDKBalance.parseFrom(bytes);
            }
            catch (Exception e)
            {
                log.error(e.getMessage(), e);
            }
            if (requestYSDKBalance == null)
            {
                builder.setErrorId(ProtoData.ErrorCode.PROTOERROR_VALUE);
                log.error(uid+":[requestGooglePay] error");
            }
            else 
            {
                IPay iPay = PayDao.getInstance();
                builder = iPay.requestYSDKBalance(uid,requestYSDKBalance.getKey(),requestYSDKBalance.getOpenid(),requestYSDKBalance.getOpenkey(),requestYSDKBalance.getTs(),requestYSDKBalance.getPf(),requestYSDKBalance.getPfkey(),requestYSDKBalance.getProductID());
            }
        }
        builder.setBalance(0);
        builder.setGenBalance(0);
        builder.setSaveAmt(0);
        builder.setFirstSave(0);
        return builder == null ? null : builder.build().toByteArray();            
    }
}
