package module.friend;

import model.RoleDressInfo;
import model.RoleInfo;

import java.util.List;

/**
 * Created by nara on 2018/6/23.
 */
public class FriendHandler implements Runnable {
    private RoleInfo roleInfo;

    public void setRoleInfo(RoleInfo roleInfo) {
        this.roleInfo = roleInfo;
    }

    public void run() {
        getRecommendFriendList();
    }

    public void getRecommendFriendList() {
        IFriend iFriend = FriendDao.getInstance();
        String uid = this.roleInfo.getUid();
//        iFriend.getMissionRecommend(0, uid, this.roleInfo.getMissionId());
//        iFriend.getEndlessRecommend(0, uid,this.roleInfo.getEndless());
//        iFriend.getFightRecommend(0, uid);
        int clothNum = 0;
        List<RoleDressInfo> roleList = this.roleInfo.getRoleList();
        for (int i = 0; i < roleList.size(); i++) {
            clothNum += roleList.get(i).getDressList().size();
        }
//        iFriend.getClothingRecommend(0, uid ,clothNum);
    }
}
