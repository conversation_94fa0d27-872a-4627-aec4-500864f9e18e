package module.task;

import common.SuperConfig;
import entities.ActivitiesEntity;
import entities.RoleEntity;
import entities.TaskEntity;
import entities.TaskRecordEntity;
import manager.*;
import model.*;
import module.item.IItem;
import module.item.ItemDao;
import module.login.ILogin;
import module.login.LoginDao;
import module.mission.IMission;
import module.mission.MissionDao;
import org.hibernate.loader.plan.spi.Return;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import protocol.ItemData;
import protocol.ProtoData;
import protocol.TaskData;
import redis.clients.jedis.Jedis;
import utils.MyUtils;

import java.util.*;

/**
 * Created by nara on 2018/1/11.
 */
public class TaskDao implements ITask {
    private static Logger log = LoggerFactory.getLogger(TaskDao.class);
    private static TaskDao inst = null;
    public static TaskDao getInstance() {
        if (inst == null) {
            inst = new TaskDao();
        }
        return inst;
    }
    public static HashMap<String,String>  activitiesMap=new HashMap();
    //类初始化加载，避免用户请求时多次计算；
    //因为在处理用户充值礼包是否为首次时表格里物品的id不是从1开始，所以维护一个映射shopItemMap；
    static{
        Redis jedis=Redis.getInstance();
        Iterator<String>iterator=jedis.keys(SuperConfig.REDIS_EXCEL_LIMITED+"*").iterator();
        while(iterator.hasNext()){
            String key=iterator.next();
            Map<String,String> activitiesInfoMap=jedis.hgetAll(key);
            String type=activitiesInfoMap.get("type_id");
            String id=activitiesInfoMap.get("ID");
            activitiesMap.put(type,id);
        }
    }
    public List<TaskInfo> initAchievement(String uid){
        Jedis jedis = Redis.getJedis(-1);
        List<TaskInfo> taskInfoList = new LinkedList<TaskInfo>();
        List<String> list = jedis.lrange("achievementfirstconfig:1", 0, -1);
        for (int i = 0 ; i<list.size() ; i++){
            int id = Integer.parseInt(list.get(i));
            TaskInfo task = new TaskInfo();
            task.setTaskId(id);
            taskInfoList.add(task);

            TaskEntity taskEntity = new TaskEntity();
            taskEntity.setType(2);
            taskEntity.setUid(uid);
            taskEntity.setTaskid(id);
            if(id==20023){
                taskEntity.setNum(1);
            }else{
            taskEntity.setNum(0);}
            taskEntity.setStatus(0);
            if (id == SuperConfig.USEACTION_ACHIEVEMENT){
                task.setTimeStamp(TimerHandler.nowTimeStamp+"");
                taskEntity.setTimestamp(TimerHandler.nowTimeStamp+"");
            }
            MySql.insert(taskEntity);
        }
        Redis.destory(jedis);
        return taskInfoList;
    }

    public static void addNewAchievement(String uid) {
        Redis jedis = Redis.getInstance();
        ILogin iLogin = LoginDao.getInstance();
        List<TaskInfo> achievement = iLogin.getTaskInfoFromRedis(uid,2);
        int max = 0;
        for (int i = 0 ; i < achievement.size() ; i++){
            TaskInfo taskInfo = achievement.get(i);
            if (max < taskInfo.getTaskId()){
                max = taskInfo.getTaskId();
            }
        }
        List<String> list = jedis.lrange("achievementfirstconfig:1", 0, -1);
        for (int i = 0 ; i<list.size() ; i++){
            int id = Integer.parseInt(list.get(i));
            if (id > max){
                TaskEntity taskEntity = new TaskEntity();
                taskEntity.setType(2);
                taskEntity.setUid(uid);
                taskEntity.setTaskid(id);
                taskEntity.setNum(0);
                taskEntity.setStatus(0);
                if (id == SuperConfig.USEACTION_ACHIEVEMENT){
                    taskEntity.setTimestamp(TimerHandler.nowTimeStamp+"");
                }
                MySql.insert(taskEntity);
            }
        }
    }

    public  List<TaskInfo> addAchievement(String uid){
        StringBuffer sql=new StringBuffer(" from TaskEntity where type=2 and uid='").append(uid).append("'");
        List<Object> taskList = MySql.queryForList(sql.toString());
        List<TaskInfo> addtaskInfoList=new ArrayList<TaskInfo>();
        int size=taskList.size();
        List ids=new ArrayList<String>(size);
        boolean isUpdate=true;
        for(int i=0;i<taskList.size();i++){
            TaskEntity task  =(TaskEntity)taskList.get(i);
            Map<String,String> taskMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_TASK,task.getTaskid());
            ids.add(taskMap.get("stage"));
            if(task.getTaskid()>30000){
                isUpdate=false;
            }
        }
        Jedis jedis = Redis.getJedis(-1);
        List<TaskInfo> taskInfoList = new LinkedList<TaskInfo>();
        List<String> list = jedis.lrange("achievementfirstconfig:1", 0, -1);
        for (int i = 0 ; i<list.size() ; i++) {
            Map<String,String> taskMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_TASK,Integer.parseInt(list.get(i)));
            boolean needUpdate=ids.contains(taskMap.get("stage"));
            if (needUpdate) {
                 continue;
            }else {
                int id = Integer.parseInt(list.get(i));
                TaskInfo task = new TaskInfo();
                task.setTaskId(id);
                task.setTaskNowNum(0);
                task.setTimeStamp("0");
                taskInfoList.add(task);
                TaskEntity taskEntity = new TaskEntity();
                taskEntity.setType(2);
                taskEntity.setUid(uid);
                taskEntity.setTaskid(id);
                taskEntity.setNum(0);
                taskEntity.setStatus(0);
                TaskInfo taskInfo=new TaskInfo();
                taskInfo.setStatus(0);
                taskInfo.setTaskId(id);
                taskInfo.setTaskNowNum(0);
                addtaskInfoList.add(taskInfo);
                if (id == SuperConfig.USEACTION_ACHIEVEMENT) {
                    task.setTimeStamp(TimerHandler.nowTimeStamp + "");
                    taskEntity.setTimestamp(TimerHandler.nowTimeStamp + "");
                }
                jedis.hset("roletask:" + uid, id + "", MyUtils.objectToJson(task));
                MySql.insert(taskEntity);
            }

        }
        if(isUpdate){
            TaskEntity taskEntity = new TaskEntity();
            taskEntity.setType(2);
            taskEntity.setUid(uid);
            taskEntity.setTaskid(30001);
            taskEntity.setNum(0);
            taskEntity.setStatus(0);
            TaskInfo task = new TaskInfo();
            task.setTaskId(30001);
            task.setTaskNowNum(0);
            task.setTimeStamp("0");
            jedis.hset("roletask:" + uid, 30001+ "", MyUtils.objectToJson(task));
            MySql.insert(taskEntity);
            TaskEntity taskEntity1 = new TaskEntity();
            taskEntity1.setType(2);
            taskEntity1.setUid(uid);
            taskEntity1.setTaskid(30000);
            taskEntity1.setNum(1);
            taskEntity1.setStatus(0);
            MySql.insert(taskEntity1);
            TaskInfo task1 = new TaskInfo();
            task1.setTaskId(30000);
            task1.setTaskNowNum(1);
            task1.setTimeStamp("0");
            jedis.hset("roletask:" + uid, 30000+ "", MyUtils.objectToJson(task1));
        }
        Redis.destory(jedis);
        return addtaskInfoList;
    }



    public List<TaskInfo> initDailyTask(String uid){
        Redis jedis = Redis.getInstance();
        String nowStamp = TimerHandler.nowTimeStamp+"";
        List<TaskInfo> taskInfoList = new LinkedList<TaskInfo>();
        Iterator<String> iterator = jedis.keys("taskconfig*").iterator();
        while (iterator.hasNext()){
            String key = iterator.next();
            int id = Integer.parseInt(key.split(":")[1]);
            if (id < 20000){
                TaskInfo task = new TaskInfo();
                task.setTaskId(id);
                task.setTimeStamp(nowStamp);
                taskInfoList.add(task);

                TaskEntity taskEntity = new TaskEntity();
                taskEntity.setType(1);
                taskEntity.setUid(uid);
                taskEntity.setTaskid(id);
                taskEntity.setNum(0);
                taskEntity.setStatus(0);
                taskEntity.setTimestamp(nowStamp);
                MySql.insert(taskEntity);
            }
        }
        return taskInfoList;
    }

    public List<TaskInfo> addDailyTask(String uid){
        Redis jedis = Redis.getInstance();
        String nowStamp = TimerHandler.nowTimeStamp+"";
        List<TaskInfo> taskInfoList = new LinkedList<TaskInfo>();
        Iterator<String> iterator = jedis.keys("taskconfig*").iterator();
        StringBuffer sql=new StringBuffer(" from TaskEntity where type=1 and uid='").append(uid).append("'");
        List<Object> taskList = MySql.queryForList(sql.toString());
        int size=taskList.size();
        List ids=new ArrayList<Integer>(size);
        for(int i=0;i<taskList.size();i++){
            TaskEntity task  =(TaskEntity)taskList.get(i);
            ids.add(task.getTaskid());
        }
        while (iterator.hasNext()){
            String key = iterator.next();
            int id = Integer.parseInt(key.split(":")[1]);
            if (id < 20000&&!ids.contains(id)){
                TaskInfo task = new TaskInfo();
                task.setTaskId(id);
                task.setTimeStamp(nowStamp);
                taskInfoList.add(task);

                TaskEntity taskEntity = new TaskEntity();
                taskEntity.setType(1);
                taskEntity.setUid(uid);
                taskEntity.setTaskid(id);
                taskEntity.setNum(0);
                taskEntity.setStatus(0);
                taskEntity.setTimestamp(nowStamp);
                MySql.insert(taskEntity);
                TaskInfo taskInfo=new TaskInfo();
                taskInfo.setTaskNowNum(0);
                taskInfo.setTaskId(id);
                taskInfo.setStatus(0);
                taskInfoList.add(taskInfo);
                jedis.hset("roletask:" + uid, id + "",MyUtils.objectToJson(task));
            }
        }
          return  taskInfoList;
    }
    private TaskInfo getTaskInfo(String uid,int taskId){
        Redis jedis = Redis.getInstance();
        TaskInfo taskInfo = null;
        String taskStr = jedis.hget("roletask:" + uid, taskId + "");
        if (taskStr != null){
            taskInfo = (TaskInfo)MyUtils.jsonToBean(taskStr,TaskInfo.class);
        }
        if (taskInfo != null && (taskId < 20000 || taskInfo.getTaskId() == 20034 || taskInfo.getTaskId() == 20043 || taskInfo.getTaskId() == 20044)){
            boolean bo = MyUtils.isOneDay(Long.parseLong(taskInfo.getTimeStamp()),(TimerHandler.nowTimeStamp+500));
            if (bo == false){
                if (taskInfo.getTaskId() < 20000){
                    StringBuffer hql = new StringBuffer("update TaskEntity set num = 0,status = 0,timestamp = '").append((TimerHandler.nowTimeStamp+500))
                            .append("' where uid = '").append(uid).append("' and taskid = ").append(taskInfo.getTaskId());
                    MySql.updateSomes(hql.toString());
                    taskInfo.setTaskNowNum(0);
                    taskInfo.setStatus(0);
                    taskInfo.setTimeStamp((TimerHandler.nowTimeStamp+500)+"");
                    jedis.hset("roletask:"+uid,taskInfo.getTaskId()+"",MyUtils.objectToJson(taskInfo));
                }else {
                    if (taskInfo.getStatus() == 0){
                        int needNum = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,taskInfo.getTaskId()+"","numb"));
                        if (taskInfo.getTaskNowNum() < needNum){
                            StringBuffer hql = new StringBuffer("update TaskEntity set num = 0,status = 0,timestamp = '").append((TimerHandler.nowTimeStamp+500))
                                    .append("' where uid = '").append(uid).append("' and taskid = ").append(taskInfo.getTaskId());
                            MySql.updateSomes(hql.toString());
                            taskInfo.setTaskNowNum(0);
                            taskInfo.setStatus(0);
                            taskInfo.setTimeStamp((TimerHandler.nowTimeStamp+500)+"");
                            jedis.hset("roletask:"+uid,taskInfo.getTaskId()+"",MyUtils.objectToJson(taskInfo));
                        }
                    }
                }
            }
        }
        return taskInfo;
    }
     public void  updateMainAchievement(String uid,int type,long num) {
           Redis jedis=Redis.getInstance();
           String  json= jedis.hget("roletask:"+uid,"30000");
         TaskInfo taskInfo=(TaskInfo) MyUtils.jsonToBean(json,TaskInfo.class);
         if(taskInfo==null){
             return;
         }
         int taskId=30000+type;
         if(taskInfo.getTaskNowNum()==type){
             String  json1= jedis.hget("roletask:"+uid,""+taskId);
             TaskInfo taskInfo1=(TaskInfo) MyUtils.jsonToBean(json1,TaskInfo.class);
            String goalNums =jedis.hget(SuperConfig.REDIS_EXCEL_STORYTASK+":"+taskId,"num");
            if(goalNums==null){
                return;
            }else{
                if(type!=6){ // 这个任务特殊
                   if(Integer.parseInt(goalNums)<=taskInfo1.getTaskNowNum()) {
                       return;
                   }
                }
            }
             StringBuffer hql3=new StringBuffer("update TaskEntity set num=").append(num+taskInfo1.getTaskNowNum()).append("where uid='").append(uid).append("'and taskid=")
                     .append(taskId);
             MySql.updateSomes(hql3.toString());
             taskInfo1.setTaskNowNum(taskInfo1.getTaskNowNum()+num);
             jedis.hset("roletask:"+uid,""+taskId,MyUtils.objectToJson(taskInfo1));
             long nowNum=0;
             //活动
           if(type==6){
            // if(type==5){
            long dressNum=taskInfo1.getTaskNowNum();
              if((dressNum&4)>0){
                  nowNum++;
              }
                 if((dressNum&2)>0){
                     nowNum++;
                 }
                 if((dressNum&1)>0){
                     nowNum++;
                 }
             }
              else {
                 nowNum=taskInfo1.getTaskNowNum();
             }

             reportTask(uid,taskId,nowNum);
         }
     }

    //@Override
    public   List<TaskInfo>  initMainAchievement(String uid) {
        Redis jedis=Redis.getInstance();
       List<TaskInfo> list= new ArrayList<TaskInfo>();
        TaskEntity taskEntity = new TaskEntity();
        taskEntity.setType(2);
        taskEntity.setUid(uid);
        taskEntity.setTaskid(30001);
        taskEntity.setNum(1);
        taskEntity.setStatus(0);
        TaskInfo task = new TaskInfo();
        task.setTaskId(30001);
        task.setTaskNowNum(1);
        task.setTimeStamp("0");
        jedis.hset("roletask:" + uid, 30001+ "", MyUtils.objectToJson(task));
        MySql.insert(taskEntity);
        TaskEntity taskEntity1 = new TaskEntity();
        taskEntity1.setType(2);
        taskEntity1.setUid(uid);
        taskEntity1.setTaskid(30000);
        taskEntity1.setNum(1);
        taskEntity1.setStatus(0);
        MySql.insert(taskEntity1);
        TaskInfo task1 = new TaskInfo();
        task1.setTaskId(30000);
        task1.setTaskNowNum(1);
        task1.setTimeStamp("0");
        jedis.hset("roletask:" + uid, 30000+ "", MyUtils.objectToJson(task1));
        list.add(task);
        return list;
    }

    public List<ActivitiesInfo> initActivities(String uid) {
        long now=System.currentTimeMillis();
        Redis jedis = Redis.getInstance();
        List<ActivitiesInfo> activitiesInfosList = new ArrayList<ActivitiesInfo>();
        Iterator<String> iterator = jedis.keys(SuperConfig.REDIS_EXCEL_ACTIVITIES+"*").iterator();
        while (iterator.hasNext()) {
            String key = iterator.next();
            Map<String, String> activitiesMap = jedis.hgetAll(key);
            int type = Integer.parseInt(activitiesMap.get("activities_type"));
           Map<String, String> activitiesInfoMap = jedis.hgetAll(SuperConfig.REDIS_EXCEL_LIMITED+":" + TaskDao.activitiesMap.get(type+""));
            ActivitiesEntity activitiesEntity = new ActivitiesEntity();
            activitiesEntity.setUid(uid);
            activitiesEntity.setType(type);
            activitiesEntity.setStatus(0);
            activitiesEntity.setActivitiesId(Integer.parseInt(key.split(":")[1]));
            activitiesEntity.setOverdue(0);
            ActivitiesInfo activitiesInfo =new ActivitiesInfo();
            activitiesInfo.setOverdue(0);
            activitiesInfo.setStatus(0);
            activitiesInfo.setActivitiesId(Integer.parseInt(key.split(":")[1]));
            activitiesInfo.setType(type);
            if (type == 1) {
                activitiesInfo.setActivitiesNowNum(1);
                activitiesEntity.setNum(1);
            } else {
                activitiesEntity.setNum(0);
                activitiesInfo.setActivitiesNowNum(0);
            }
            int limite=Integer.parseInt(activitiesInfoMap.get("limit"));
            if(limite==0){
                activitiesInfo.setTimeStamp(0);
                activitiesEntity.setTimestamp(0);
            }else{
               long exprieTime =MyUtils.dateStringToStamp(activitiesInfoMap.get("close_time"));
          /*     if(exprieTime<System.currentTimeMillis()){
                   continue;
               }*/
               activitiesInfo.setTimeStamp(exprieTime);
                activitiesEntity.setTimestamp(exprieTime);
            }
            MySql.insert(activitiesEntity);
            String deadline=activitiesInfoMap.get("close_time");
            long closeTime=MyUtils.dateStringToStamp(deadline);
            String openLine=activitiesInfoMap.get("open_time");
            long startTime=MyUtils.dateStringToStamp(openLine);

            if(limite==0||(now>=startTime&&now<=closeTime)){
                activitiesInfosList.add(activitiesInfo);
            }

        }
       // /// System.out.println(activitiesInfosList.size()+"activitiesInfosList");
        return activitiesInfosList;
    }

    public List<Object> addActivities(String uid,List<Integer> list) {
        Redis jedis = Redis.getInstance();
        List<Object> activitiesList = new ArrayList<Object>();
        Iterator<String> iterator = jedis.keys("activitiesconfig*").iterator();
        StringBuffer sql=new  StringBuffer("from RoleEntity where uid='").append(uid).append("'");
        RoleEntity roleEntity =(RoleEntity)MySql.queryForOne(sql.toString());
        int lv=roleEntity.getLv();
        int i=0;
        while (iterator.hasNext()) {
            i++;
            String key = iterator.next();
            Map<String, String> activitiesMap = jedis.hgetAll(key);
           // /// System.out.print(activitiesMap.get("ID"));
            if(list.contains(Integer.parseInt(activitiesMap.get("ID")))){
                continue;
            }
            int type = Integer.parseInt(activitiesMap.get("activities_type"));
            Map<String, String> activitiesInfoMap = jedis.hgetAll(SuperConfig.REDIS_EXCEL_LIMITED+":" + TaskDao.activitiesMap.get(type+""));
         //   /// System.err.println(TaskDao.activitiesMap.get(type+""));
            ///// System.err.println(TaskDao.activitiesMap.get(type+"")+"type");
 /*           for(Map.Entry<String,String> entry :TaskDao.activitiesMap.entrySet()){
             //   /// System.out.println(entry.getKey()+":"+entry.getValue());
            }
          //  /// System.out.println(TaskDao.activitiesMap.get(type+"")+"~~~~"+type);*/
            ActivitiesEntity activitiesEntity = new ActivitiesEntity();
            activitiesEntity.setUid(uid);
            activitiesEntity.setType(type);
            activitiesEntity.setStatus(0);
            activitiesEntity.setActivitiesId(Integer.parseInt(key.split(":")[1]));
            activitiesEntity.setOverdue(0);
           if(type==1){
               activitiesEntity.setNum(lv);
           }else{
               activitiesEntity.setNum(0);
           }
            /// System.out.println(activitiesInfoMap+""+type);
           if(activitiesInfoMap==null||activitiesInfoMap.size()==0){
               continue;
           }
            int limite = Integer.parseInt(activitiesInfoMap.get("limit"));
            if (limite == 0) {
                activitiesEntity.setTimestamp(0);
            }else{
                long exprieTime =MyUtils.dateStringToStamp(activitiesInfoMap.get("close_time"));
                activitiesEntity.setTimestamp(exprieTime);
            }
            MySql.insert(activitiesEntity);
            activitiesList.add(activitiesEntity);
        }
     //   /// System.out.println(i+"~~"+list.size());
        return activitiesList;
    }

    public void updateActivities(String uid, int id,long num) {
        Redis jedis=Redis.getInstance();
        String activities=jedis.hget("roleactivities:"+uid,id+"");
        ActivitiesInfo activitiesInfo=(ActivitiesInfo)MyUtils.jsonToBean(activities,ActivitiesInfo.class);
        if(activitiesInfo == null || activitiesInfo.getOverdue()==1){
            return;
        }
     TaskData.ReportUpdateActivities.Builder builder=TaskData.ReportUpdateActivities.newBuilder();
        TaskData.Activities.Builder activitiesBuilder=TaskData.Activities.newBuilder();
        activitiesBuilder.setType(activitiesInfo.getType());
        Boolean isUpdate=false;
     if(id==1){
         for(int i=1;i<=6;i++){
             String activitiesInfos=jedis.hget("roleactivities:"+uid,i+"");
             ActivitiesInfo activitiesBean=(ActivitiesInfo)MyUtils.jsonToBean(activitiesInfos,ActivitiesInfo.class);
             if(activitiesBean.getStatus()==1){
              continue;
             }
             activitiesBean.setActivitiesNowNum(activitiesBean.getActivitiesNowNum()+num);
             TaskData.ActivitiesDetail.Builder activitiesBu=TaskData.ActivitiesDetail.newBuilder();
             activitiesBu.setReceive(false);
             activitiesBu.setNowNum(activitiesBean.getActivitiesNowNum());
             activitiesBu.setId(i);
             jedis.hset("roleactivities:"+uid,i+"",MyUtils.objectToJson(activitiesBean));
             StringBuffer sql  =new StringBuffer("update ActivitiesEntity set num=").append(activitiesBean.getActivitiesNowNum())
                     .append(" where uid='").append(uid).append("' and activitiesId=").append(i);
             MySql.updateSomes(sql.toString());
             isUpdate=true;
             activitiesBuilder.addActivitiesInfo(activitiesBu);
         }

     }else{
         TaskData.ActivitiesDetail.Builder activitiesBu=TaskData.ActivitiesDetail.newBuilder();
         activitiesInfo.setActivitiesNowNum(num+activitiesInfo.getActivitiesNowNum());
         activitiesBu.setReceive(false);
         activitiesBu.setNowNum(activitiesInfo.getActivitiesNowNum());
         activitiesBu.setId(id);
         activitiesBuilder.addActivitiesInfo(activitiesBu);
         jedis.hset("roleactivities:"+uid,id+"",MyUtils.objectToJson(activitiesInfo));
         StringBuffer querySql=new StringBuffer("from ActivitiesEntity where uid='").append(uid).append("' and type=").append(activitiesInfo.getType());
         List<Object> activitiesInfoList=MySql.queryForList(querySql.toString());
         for(int i=0;i<activitiesInfoList.size();i++){
             ActivitiesEntity activitiesEntity=(ActivitiesEntity)activitiesInfoList.get(i);
             if(activitiesEntity.getActivitiesId()==id){
                 continue;
             }
             TaskData.ActivitiesDetail.Builder otherActivitiesBu=TaskData.ActivitiesDetail.newBuilder();
             otherActivitiesBu.setReceive(activitiesEntity.getStatus()==0?false:true);
             otherActivitiesBu.setNowNum(activitiesEntity.getNum());
             otherActivitiesBu.setId(activitiesEntity.getActivitiesId());
             activitiesBuilder.addActivitiesInfo(otherActivitiesBu);
         }
         StringBuffer sql  =new StringBuffer("update ActivitiesEntity set num=").append(activitiesInfo.getActivitiesNowNum())
                 .append(" where uid='").append(uid).append("' and activitiesId=").append(id);
         MySql.updateSomes(sql.toString());
         isUpdate=true;


     }
        if(!isUpdate){
            return;
        }
        builder.addActivities(activitiesBuilder);
    // /// System.out.println("updateactiv~"+builder.getActivitiesCount()+"~"+id);
        ReportManager.reportInfo(uid,ProtoData.SToC.REPORTUPDATEACTIVITIES_VALUE,builder.build().toByteArray());
    }

    public Boolean updateAchievement(String uid,int type,long num){
        try {
            TaskInfo taskInfo = null;
            ILogin iLogin = LoginDao.getInstance();
            List<TaskInfo> achievement = iLogin.getTaskInfoFromRedis(uid, 2);
            for (int i = 0 ; i<achievement.size() ; i++){
                TaskInfo tmp = achievement.get(i);
                if(tmp.getTaskId()>=30000){
                    continue;
                }
               String a =Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,tmp.getTaskId()+"","stage");
                if(a==null){
                    log.info(uid+":[updateAchievement] finished >>>taskId:"+tmp.getTaskId());
                    continue;

                }
                int tmpType = Integer.parseInt(a);
                if (tmpType == type){
                    taskInfo = tmp;
                    break;
                }
            }
            if (taskInfo == null){
                log.error(uid+":[updateAchievement] error 1>>>type:"+type);
                return false;
            }
            if (taskInfo.getStatus() == 1){
                log.info(uid+":[updateAchievement] finished >>>type:"+type);
                return false;
            }
            long addNum = 0;
            switch (type){
                case 1://等级
                    addNum = num - taskInfo.getTaskNowNum();
                    break;
                case 2://主线关卡
                    int target2 = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,taskInfo.getTaskId()+"","target"));
                    if (taskInfo.getTaskNowNum() == 0 && target2 <= num){
                        addNum = 1;
                    }
                    break;
                case 21://单局最高蹦跳
                case 24://无尽单局分
                int target21 = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,taskInfo.getTaskId()+"","numb"));
                    if (taskInfo.getTaskNowNum() == 0 && num >= target21){
                        addNum = num;
                    }
                 /*      Redis jedis=Redis.getInstance();
                     String taskinfoStr=jedis.hget("role:"+uid,taskInfo.getTaskId()+"");
                     TaskInfo taskinfo=(TaskInfo)MyUtils.jsonToBean(taskinfoStr,TaskInfo.class);
                     if(taskInfo.getTaskNowNum() == 0 && num >= taskInfo.getTaskNowNum()){
                         addNum=num;
                     }*/
                    break;
                case 3://星币
                case 4://世界发言
                case 5://交友
                case 6://累计登入
                case 7://购物
                case 8://闯关
                case 9://体力剂
                case 10://合成
                case 11://使用金币
                case 12://使用星币
                case 13://开背包各自
                case 14://获取角色
                case 15://获取帽子
                case 16://获取上衣
                case 17://获取下装
                case 18://获取鞋子
                case 19://获取双持
                case 20://获取连衣裙
                case 22://赠送代表我的心次数
                case 23://无尽累积分
                case 25://闯关失败次数
                case 26://钻石兑换金币次数
                case 27://删除好友次数
                case 28://进入阿尔法线
                case 29://通关剧情模式
                case 30://通关1次水世界
                case 31://累计消除道具
                case 32://累计挑战剧情模式
                case 33://累计炼金
                    addNum = num;
                    break;
            }

            if (taskInfo != null && addNum>0){
                return updateOneTask(uid,taskInfo.getTaskId(),addNum);
            }
            return false;
        }catch (Exception e){
            log.error(uid+":[updateAchievement] error 2>>>type:"+type);
            log.error(e.getMessage(),e);
            return false;
        }
    }
    /**
     * 更新成就或日常
     */
    public Boolean updateOneTask(String uid,int taskId,long increment){
        try {
            Redis jedis = Redis.getInstance();
//            TaskInfo taskInfo = getTaskInfo(uid,taskId);
            TaskInfo taskInfo = null;
            String taskStr = jedis.hget("roletask:" + uid, taskId + "");
            if (taskStr != null){
                taskInfo = (TaskInfo)MyUtils.jsonToBean(taskStr,TaskInfo.class);
            }/*else{
                TaskInfo newTaskInfo =new TaskInfo();
                newTaskInfo.setTaskNowNum(1);
                newTaskInfo.setTaskId(taskId);
                if(taskId< 20000){
                    newTaskInfo.setTimeStamp((TimerHandler.nowTimeStamp+500)+"");
                }
                jedis.hset("roletask:" + uid, taskId + "",MyUtils.objectToJson(newTaskInfo));
            }*/
            if (taskInfo != null && (taskId < 20000 || taskInfo.getTaskId() == 20034 || taskInfo.getTaskId() == 20043 || taskInfo.getTaskId() == 20044)){
                boolean bo = MyUtils.isOneDay(Long.parseLong(taskInfo.getTimeStamp()),(TimerHandler.nowTimeStamp+500));
                if (bo == false){
                    if (taskInfo.getTaskId() < 20000){
                        taskInfo.setTaskNowNum(0);
                        taskInfo.setStatus(0);
                        taskInfo.setTimeStamp((TimerHandler.nowTimeStamp+500)+"");
                    }else {
                        if (taskInfo.getStatus() == 0){
                            int needNum = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,taskInfo.getTaskId()+"","numb"));
                            if (taskInfo.getTaskNowNum() < needNum){
                                taskInfo.setTaskNowNum(0);
                                taskInfo.setStatus(0);
                                taskInfo.setTimeStamp((TimerHandler.nowTimeStamp + 500) + "");
                            }
                        }
                    }
                }
            }
            if (taskInfo == null){
                return false;
            }
            if (taskInfo.getStatus() == 1){
                return false;
            }
//            int needNum = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,taskId+"","numb"));
//        if (taskInfo.getTaskNowNum() >= needNum){
//            return false;
//        }
            taskInfo.setTaskNowNum(taskInfo.getTaskNowNum()+increment);
//            if (taskId < 20000 && taskInfo.getTaskNowNum() > needNum){
//                taskInfo.setTaskNowNum(needNum);
//            }
            jedis.hset("roletask:" + uid, taskId + "",MyUtils.objectToJson(taskInfo));

            StringBuffer hql = new StringBuffer("update TaskEntity set num = ").append(taskInfo.getTaskNowNum()).append(",status = ").append(taskInfo.getStatus());
            if (taskId < 20000 || taskInfo.getTaskId() == 20034 || taskInfo.getTaskId() == 20043 || taskInfo.getTaskId() == 20044){
                hql.append(",timestamp = '").append(taskInfo.getTimeStamp()).append("'");
            }
            hql.append(" where uid = '").append(uid).append("' and taskid = ").append(taskInfo.getTaskId());
            MySql.updateSomes(hql.toString());

            if (taskId >= 20023 && taskId <= 20026){
                TaskHandler taskHandler = new TaskHandler();
                taskHandler.setUid(uid);
                taskHandler.setTaskId(taskId);
                taskHandler.setTaskNowNum(taskInfo.getTaskNowNum());
                Thread thread = new Thread(taskHandler);
                thread.start();
            }else {
                reportTask(uid,taskId,taskInfo.getTaskNowNum());
            }
            return true;
        }catch (Exception e){
            log.error(e.getMessage(),e);
            return false;
        }
    }

    public void reportTask(String uid,int taskId,long nowNum){
        TaskData.ReportUpdateTask.Builder builder = TaskData.ReportUpdateTask.newBuilder();
        TaskData.Task.Builder taskBu = TaskData.Task.newBuilder();
        taskBu.setId(taskId);
        taskBu.setNowNum(nowNum);
        taskBu.setStatus(0);
        builder.setTask(taskBu);
        ReportManager.reportInfo(uid, ProtoData.SToC.REPORTUPDATETASK_VALUE, builder.build().toByteArray());
    }

    public TaskData.ResponseFinishTask.Builder finishOneTask(String uid,int taskId){
        TaskData.ResponseFinishTask.Builder builder = TaskData.ResponseFinishTask.newBuilder();
        builder.setTaskId(taskId);
        builder.setErrorId(0);
        Redis jedis = Redis.getInstance();
        if(taskId>30000){
            Map<String,String> taskMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_STORYTASK,taskId);
            IItem iItem = ItemDao.getInstance();
            String[] getItemStrs = taskMap.get("reward").split("\\|");
            RewardInfo[] reward=new RewardInfo[getItemStrs.length];
            for (int i = 0 ; i < getItemStrs.length ; i++){
                int itemId = Integer.parseInt(getItemStrs[i].split(",")[0]);
                int itemNum = Integer.parseInt(getItemStrs[i].split(",")[1]);
                double total = iItem.updateItemInfo(uid, itemId, itemNum);
                RewardInfo rewardInfo  =new RewardInfo();
                rewardInfo.setItemid(itemId);
                rewardInfo.setItemnums(itemNum);
                rewardInfo.setItemtotal(new Double(total).intValue());
                reward[i]=rewardInfo;
                if (total > 0){
                    ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                    itemBu.setId(itemId);
                    itemBu.setNum(total);
                    builder.addItem(itemBu);
                }
            }
            StringBuffer hql3=new StringBuffer("update TaskEntity set num=num+1 where uid='").append(uid).append("'and taskid=")
                    .append(30000);
            MySql.updateSomes(hql3.toString());
            String  json= jedis.hget("roletask:"+uid,"30000");
            TaskInfo taskInfo=(TaskInfo) MyUtils.jsonToBean(json,TaskInfo.class);
            taskInfo.setTaskNowNum(taskInfo.getTaskNowNum()+1);
            jedis.hset("roletask:"+uid,"30000",MyUtils.objectToJson(taskInfo));
            Map<String,String> nextTask = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_STORYTASK,taskId+1);
            if( nextTask!=null&&nextTask.size()!=0) {
                int nowNum=0;
                if(taskId==30003){
                    String key="roledress:" + uid + "#1" ;
                    RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
                    List<UtilityInfo> dressList = roleDressInfo.getDressList();
                    for(UtilityInfo dress:dressList){
                        if(dress.getId()==374||dress.getId()==375||dress.getId()==376||dress.getId()==377||dress.getId()==378||dress.getId()==379){
                            nowNum++;
                        }
                    }

                }else if(taskId==30006){
                    String key="roledress:" + uid + "#1" ;
                    RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
                    List<UtilityInfo> dressList = roleDressInfo.getDressList();
                    for(UtilityInfo dress:dressList){
                        if(dress.getId()==392||dress.getId()==393||dress.getId()==394||dress.getId()==395||dress.getId()==396||dress.getId()==397){
                            nowNum++;
                        }
                    }
                }else if(taskId==30009){
                    String key="roledress:" + uid + "#1" ;
                    RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
                    List<UtilityInfo> dressList = roleDressInfo.getDressList();
                    for(UtilityInfo dress:dressList){
                        if(dress.getId()==443||dress.getId()==444||dress.getId()==445||dress.getId()==446||dress.getId()==447||dress.getId()==448){
                            nowNum++;
                        }
                    }
                }else if(taskId==30010){
                    String key="roledress:" + uid + "#1" ;
                    RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
                    List<UtilityInfo> dressList = roleDressInfo.getDressList();
                    for(UtilityInfo dress:dressList){
                        if(dress.getId()==472||dress.getId()==473||dress.getId()==474||dress.getId()==475||dress.getId()==476||dress.getId()==477){
                            nowNum++;
                        }
                    }
                }else if(taskId==30011){
                    String key="roledress:" + uid + "#1" ;
                    RoleDressInfo roleDressInfo = LoginDao.getInstance().getRoleDressFromRedis(key);
                    List<UtilityInfo> dressList = roleDressInfo.getDressList();
                    for(UtilityInfo dress:dressList){
                        if(dress.getId()==496||dress.getId()==497||dress.getId()==498||dress.getId()==499||dress.getId()==500||dress.getId()==501){
                            nowNum++;
                        }
                    }
                }
              StringBuffer hql1=new StringBuffer("delete from TaskEntity where uid='").append(uid).append("'and taskid=")
                      .append(taskId);
             MySql.updateSomes(hql1.toString());
            jedis.hdel("roletask:"+uid,taskId+"");
                 TaskEntity entity=new TaskEntity();
                 entity.setTaskid(taskId + 1);
                 entity.setNum(nowNum);
                 entity.setUid(uid);
                 entity.setStatus(0);
                 entity.setType(2);
                MySql.insert(entity);
                 TaskInfo task = new TaskInfo();
                 task.setTaskId(taskId + 1);
                 task.setTaskNowNum(nowNum);
                 task.setTimeStamp("0");
                 jedis.hset("roletask:"+uid,taskId + 1+"",MyUtils.objectToJson(task));
                 reportTask(uid,taskId + 1,nowNum);
             }
             if(nextTask==null||nextTask.size()==0){
                 String taskStr = jedis.hget("roletask:" + uid, taskId + "");
                 TaskInfo  taskInfo1 = (TaskInfo)MyUtils.jsonToBean(taskStr,TaskInfo.class);
                 taskInfo1.setStatus(1);
                 jedis.hset("roletask:" + uid, taskId + "",MyUtils.objectToJson(taskInfo1));
                 StringBuffer hql = new StringBuffer("update TaskEntity set status = 1")
                         .append(" where uid = '").append(uid).append("' and taskid = ").append(taskInfo1.getTaskId());
                 MySql.updateSomes(hql.toString());
                 TaskData.ReportUpdateTask.Builder finishbuilder = TaskData.ReportUpdateTask.newBuilder();
                 TaskData.Task.Builder taskBu = TaskData.Task.newBuilder();
                 taskBu.setId(taskInfo1.getTaskId());
                 taskBu.setNowNum(taskInfo1.getTaskNowNum());
                 taskBu.setStatus(1);
                 finishbuilder.setTask(taskBu);
                 ReportManager.reportInfo(uid, ProtoData.SToC.REPORTUPDATETASK_VALUE, finishbuilder.build().toByteArray());
             }

             TaskRecordEntity entity=new TaskRecordEntity();
            entity.setTaskid(taskId);
            entity.setUid(uid);
            StringBuffer rewardRecord =new StringBuffer("");
            for(int i=0;i<reward.length;i++){
                String rewardSub=MyUtils.objectToJson(reward[i]);
                rewardRecord.append(rewardSub);
                if(i!=reward.length-1){
                    rewardRecord.append(",");
                }
            }
            entity.setReward(rewardRecord.toString());
            MySql.insert(entity);
            return builder;
        }
        TaskInfo taskInfo = getTaskInfo(uid,taskId);
        if (taskInfo == null){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[finishOneTask] error 1");
            return builder;
        }
        if (taskInfo.getStatus() == 1){
            builder.setErrorId(ProtoData.ErrorCode.UNKNOWERROR_VALUE);
            log.error(uid+":[finishOneTask] error 2");
            return builder;
        }
        Map<String,String> taskMap = Redis.getExcelMap(SuperConfig.REDIS_EXCEL_TASK,taskId);
        int stage = Integer.parseInt(taskMap.get("stage"));
        boolean needUpdate = true;
        if (taskId == SuperConfig.DAILYTASKLAST){ //完成所有日常
            ITask task=TaskDao.getInstance();
            String[] tasks = taskMap.get("target").split(",");
            for (int i = 0 ; i < tasks.length ; i++){
                int needTask = Integer.parseInt(tasks[i]);
                TaskInfo needInfo = getTaskInfo(uid,needTask);
                if (needInfo.getStatus() == 0){
                    builder.setErrorId(ProtoData.ErrorCode.TASKNUMERROR_VALUE);
                    log.error(uid+":[finishOneTask] error 3");

                    return builder;
                }
            }
        }else {
            String needNum = taskMap.get("numb");
            int targetNum = Integer.parseInt(needNum);
            if (taskInfo.getTaskNowNum() < targetNum){
                builder.setErrorId(ProtoData.ErrorCode.TASKNUMERROR_VALUE);
                log.error(uid+":[finishOneTask] error 4 >>>taskNowNum:"+taskInfo.getTaskNowNum()+",targetNum:"+targetNum);
                return builder;
            }

            if (taskId < 20000){
                TaskInfo taskInfo2 = getTaskInfo(uid,SuperConfig.DAILYTASKLAST);
                taskInfo2.setTaskNowNum(taskInfo2.getTaskNowNum()+1);
                jedis.hset("roletask:" + uid, SuperConfig.DAILYTASKLAST + "",MyUtils.objectToJson(taskInfo2));

                StringBuffer hql = new StringBuffer("update TaskEntity set num = ").append(taskInfo2.getTaskNowNum())
                        .append(" where uid = '").append(uid).append("' and taskid = ").append(taskInfo2.getTaskId());
                MySql.updateSomes(hql.toString());
                reportTask(uid,SuperConfig.DAILYTASKLAST,taskInfo2.getTaskNowNum());
            }else {
                int nextId = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,taskId+"","next"));
                if (nextId != 0){
                    needUpdate = false;
                    jedis.hdel("roletask:" + uid, taskId + "");
                    taskInfo.setTaskId(nextId);
                    if (stage == 2){
                        IMission iMission = MissionDao.getInstance();
                        int nowMax = iMission.getMissionMax(uid);
                        int target = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,nextId+"","target"));
                        if (nowMax >= target){
                            taskInfo.setTaskNowNum(1);
                        }else {
                            taskInfo.setTaskNowNum(0);
                        }
                    }else if (stage == 21 || stage == 24){
                        int numb = Integer.parseInt(Redis.getExcelInfo(SuperConfig.REDIS_EXCEL_TASK,nextId+"","numb"));

                        if (taskInfo.getTaskNowNum() < numb){
                            taskInfo.setTaskNowNum(0);
                        }
                    }else if(stage==30){
                        taskInfo.setTaskNowNum(0);
                    }
                    jedis.hset("roletask:" + uid, nextId + "",MyUtils.objectToJson(taskInfo));
                    StringBuffer hql = new StringBuffer("update TaskEntity set taskid = ").append(nextId).append(",num = ").append(taskInfo.getTaskNowNum())
                            .append(" where uid = '").append(uid).append("' and taskid = ").append(taskId);
                    MySql.updateSomes(hql.toString());
                    reportTask(uid,nextId,taskInfo.getTaskNowNum());
                }
            }
        }
        if (needUpdate == true){
            taskInfo.setStatus(1);
            jedis.hset("roletask:" + uid, taskId + "",MyUtils.objectToJson(taskInfo));

            StringBuffer hql = new StringBuffer("update TaskEntity set status = 1")
                    .append(" where uid = '").append(uid).append("' and taskid = ").append(taskInfo.getTaskId());
            MySql.updateSomes(hql.toString());
        }

        IItem iItem = ItemDao.getInstance();
        String[] getItemStrs = taskMap.get("getitem").split("\\|");
        RewardInfo[] reward=new RewardInfo[getItemStrs.length];
        for (int i = 0 ; i < getItemStrs.length ; i++){
            int itemId = Integer.parseInt(getItemStrs[i].split(",")[0]);
            int itemNum = Integer.parseInt(getItemStrs[i].split(",")[1]);
            double total = iItem.updateItemInfo(uid, itemId, itemNum);
            RewardInfo rewardInfo  =new RewardInfo();
            rewardInfo.setItemid(itemId);
            rewardInfo.setItemnums(itemNum);
            rewardInfo.setItemtotal(new Double(total).intValue());
            reward[i]=rewardInfo;
            if (total > 0){
                ItemData.Item.Builder itemBu = ItemData.Item.newBuilder();
                itemBu.setId(itemId);
                itemBu.setNum(total);
                builder.addItem(itemBu);
            }
        }

            TaskRecordEntity entity=new TaskRecordEntity();
            entity.setTaskid(taskId);
            entity.setUid(uid);
            StringBuffer rewardRecord =new StringBuffer("");
            for(int i=0;i<reward.length;i++){
                String rewardSub=MyUtils.objectToJson(reward[i]);
                rewardRecord.append(rewardSub);
                if(i!=reward.length-1){
                    rewardRecord.append(",");
                }
            }
            entity.setReward(rewardRecord.toString());
            MySql.insert(entity);
        return builder;
    }
}
