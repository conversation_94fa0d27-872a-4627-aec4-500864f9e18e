package common;

@ExcelConfigObject(key = "starupDetermine")
public class PetStarConfig {
    @ExcelColumn(name = "id")
    private int id;
    @ExcelColumn(name = "probability")
    private int petStarProbability;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getPetStarProbability() {
        return petStarProbability;
    }

    public void setPetStarProbability(int petStarProbability) {
        this.petStarProbability = petStarProbability;
    }

    @Override
    public String toString() {
        return "PetStarConfig{" +
                "id=" + id +
                ", petStarProbability=" + petStarProbability +
                '}';
    }
}
