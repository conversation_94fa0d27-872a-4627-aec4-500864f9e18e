package module.Job;

import entities.ItemEntity;
import manager.MySql;
import manager.ReportManager;
import protocol.ItemData;
import protocol.ProtoData;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class WuTime {
    private static void physicalStrength(){
        ItemData.Item.Builder builder=ItemData.Item.newBuilder();

        Runnable runnable=new Runnable() {
            @Override
            public void run() {
                StringBuilder sql=new StringBuilder("from ItemEntity where itemId=3");
                List<Object> itemEntityList=MySql.queryForList(sql.toString());
                for (int i=0;i<itemEntityList.size();i++){
                    ItemEntity itemEntity= (ItemEntity) itemEntityList.get(i);
                    if (itemEntity.getItemnum()<70){
                        ItemData.ReportItem.Builder reportBuilder = ItemData.ReportItem.newBuilder();
                        itemEntity.setItemnum(itemEntity.getItemnum()+1);
                        MySql.update(itemEntity);
                        builder.setId(itemEntity.getItemid());
                        builder.setNum(itemEntity.getItemnum());
                        reportBuilder.addItem(builder);
                        /// System.out.println("加一=="+itemEntity.toString());
                        ReportManager.reportInfo(itemEntity.getUid(), ProtoData.SToC.REPORTITEM_VALUE, reportBuilder.build().toByteArray());
                    }
                }

            }
        };
        ScheduledExecutorService service= Executors.newSingleThreadScheduledExecutor();
        service.scheduleAtFixedRate(runnable,3,3,TimeUnit.MINUTES);
    }
}
